/**
 * 性能优化配置
 * 定义系统性能监控、优化和告警的配置参数
 */

/**
 * 系统资源阈值配置
 */
export const systemThresholds = {
  // CPU使用率阈值
  cpu: {
    warning: 70,    // 警告阈值（%）
    critical: 85,   // 严重阈值（%）
    max: 95         // 最大阈值（%）
  },

  // 内存使用率阈值
  memory: {
    warning: 75,    // 警告阈值（%）
    critical: 90,   // 严重阈值（%）
    max: 95         // 最大阈值（%）
  },

  // 磁盘使用率阈值
  disk: {
    warning: 80,    // 警告阈值（%）
    critical: 90,   // 严重阈值（%）
    max: 95         // 最大阈值（%）
  },

  // 负载平均值阈值（相对于CPU核心数）
  loadAverage: {
    warning: 0.7,   // 警告阈值（倍数）
    critical: 1.0,  // 严重阈值（倍数）
    max: 1.5        // 最大阈值（倍数）
  }
};

/**
 * 数据库性能阈值配置
 */
export const databaseThresholds = {
  // 查询响应时间阈值（毫秒）
  responseTime: {
    fast: 50,       // 快速查询
    normal: 200,    // 正常查询
    slow: 1000,     // 慢查询
    critical: 5000  // 严重慢查询
  },

  // 查询成功率阈值
  successRate: {
    excellent: 99.9,  // 优秀
    good: 99.5,       // 良好
    warning: 99.0,    // 警告
    critical: 95.0    // 严重
  },

  // 连接池阈值
  connectionPool: {
    utilizationWarning: 70,   // 连接池使用率警告阈值（%）
    utilizationCritical: 90,  // 连接池使用率严重阈值（%）
    waitingWarning: 5,        // 等待连接数警告阈值
    waitingCritical: 10       // 等待连接数严重阈值
  },

  // 慢查询率阈值
  slowQueryRate: {
    warning: 5,     // 警告阈值（%）
    critical: 10    // 严重阈值（%）
  }
};

/**
 * 缓存性能阈值配置
 */
export const cacheThresholds = {
  // 缓存命中率阈值
  hitRate: {
    excellent: 95,  // 优秀
    good: 85,       // 良好
    warning: 70,    // 警告
    critical: 50    // 严重
  },

  // 缓存响应时间阈值（毫秒）
  responseTime: {
    fast: 1,        // 快速
    normal: 5,      // 正常
    slow: 20,       // 慢
    critical: 100   // 严重
  },

  // 内存使用率阈值
  memoryUsage: {
    warning: 80,    // 警告阈值（%）
    critical: 95    // 严重阈值（%）
  }
};

/**
 * API性能阈值配置
 */
export const apiThresholds = {
  // 响应时间阈值（毫秒）
  responseTime: {
    fast: 100,      // 快速响应
    normal: 500,    // 正常响应
    slow: 2000,     // 慢响应
    critical: 10000 // 严重慢响应
  },

  // 错误率阈值
  errorRate: {
    excellent: 0.1, // 优秀
    good: 1.0,      // 良好
    warning: 5.0,   // 警告
    critical: 10.0  // 严重
  },

  // 吞吐量阈值（RPS）
  throughput: {
    low: 10,        // 低吞吐量
    normal: 50,     // 正常吞吐量
    high: 200,      // 高吞吐量
    peak: 1000      // 峰值吞吐量
  }
};

/**
 * 监控配置
 */
export const monitoringConfig = {
  // 监控间隔（毫秒）
  intervals: {
    system: 30000,      // 系统资源监控间隔
    database: 60000,    // 数据库监控间隔
    cache: 30000,       // 缓存监控间隔
    api: 10000,         // API监控间隔
    health: 60000       // 健康检查间隔
  },

  // 数据保留时间（毫秒）
  retention: {
    metrics: 7 * 24 * 60 * 60 * 1000,     // 指标数据保留7天
    logs: 30 * 24 * 60 * 60 * 1000,       // 日志数据保留30天
    alerts: 90 * 24 * 60 * 60 * 1000      // 告警数据保留90天
  },

  // 批量处理配置
  batch: {
    size: 100,          // 批量处理大小
    timeout: 5000,      // 批量处理超时（毫秒）
    maxRetries: 3       // 最大重试次数
  }
};

/**
 * 缓存配置
 */
export const cacheConfig = {
  // 响应缓存配置
  response: {
    // 默认TTL（秒）
    defaultTTL: 300,
    
    // 不同类型的TTL配置
    ttl: {
      static: 3600,      // 静态资源缓存1小时
      api: 300,          // API响应缓存5分钟
      user: 600,         // 用户数据缓存10分钟
      system: 60,        // 系统状态缓存1分钟
      health: 30         // 健康检查缓存30秒
    },

    // 缓存键前缀
    keyPrefix: {
      response: 'resp:',
      user: 'user:',
      session: 'sess:',
      system: 'sys:',
      metrics: 'metrics:'
    }
  },

  // 数据缓存配置
  data: {
    // 用户会话缓存TTL（秒）
    sessionTTL: 24 * 60 * 60,     // 24小时
    
    // JWT黑名单缓存TTL（秒）
    jwtBlacklistTTL: 15 * 60,     // 15分钟
    
    // OAuth状态缓存TTL（秒）
    oauthStateTTL: 10 * 60,       // 10分钟
    
    // 速率限制缓存TTL（秒）
    rateLimitTTL: 15 * 60         // 15分钟
  }
};

/**
 * 压缩配置
 */
export const compressionConfig = {
  // 压缩阈值（字节）
  threshold: 1024,
  
  // 压缩级别（1-9）
  level: 6,
  
  // 内存级别（1-9）
  memLevel: 8,
  
  // 压缩的MIME类型
  mimeTypes: [
    'text/plain',
    'text/html',
    'text/css',
    'text/javascript',
    'application/javascript',
    'application/json',
    'application/xml',
    'text/xml',
    'image/svg+xml'
  ]
};

/**
 * 并发控制配置
 */
export const concurrencyConfig = {
  // 最大并发请求数
  maxConcurrentRequests: 1000,
  
  // 请求队列大小
  queueSize: 5000,
  
  // 请求超时时间（毫秒）
  requestTimeout: 30000,
  
  // 连接超时时间（毫秒）
  connectionTimeout: 5000
};

/**
 * 性能预算配置
 */
export const performanceBudget = {
  // 页面加载时间预算（毫秒）
  pageLoad: {
    excellent: 1000,
    good: 2500,
    acceptable: 4000
  },

  // API响应时间预算（毫秒）
  apiResponse: {
    excellent: 100,
    good: 500,
    acceptable: 2000
  },

  // 数据库查询时间预算（毫秒）
  databaseQuery: {
    excellent: 50,
    good: 200,
    acceptable: 1000
  },

  // 缓存操作时间预算（毫秒）
  cacheOperation: {
    excellent: 1,
    good: 5,
    acceptable: 20
  }
};

/**
 * 告警配置
 */
export const alertConfig = {
  // 告警级别
  levels: {
    info: 'info',
    warning: 'warning',
    error: 'error',
    critical: 'critical'
  },

  // 告警通道
  channels: {
    log: true,          // 日志告警
    email: false,       // 邮件告警
    webhook: false,     // Webhook告警
    slack: false        // Slack告警
  },

  // 告警频率限制（秒）
  rateLimits: {
    info: 300,          // 信息级别告警5分钟内最多1次
    warning: 600,       // 警告级别告警10分钟内最多1次
    error: 300,         // 错误级别告警5分钟内最多1次
    critical: 60        // 严重级别告警1分钟内最多1次
  },

  // 告警恢复通知
  recovery: {
    enabled: true,      // 启用恢复通知
    delay: 300          // 恢复通知延迟（秒）
  }
};

/**
 * 优化建议配置
 */
export const optimizationConfig = {
  // 建议优先级权重
  priorityWeights: {
    system: 1.0,        // 系统资源权重
    database: 0.9,      // 数据库权重
    cache: 0.8,         // 缓存权重
    api: 0.7,           // API权重
    network: 0.6        // 网络权重
  },

  // 建议生成阈值
  thresholds: {
    minImpact: 5,       // 最小影响阈值（%）
    minConfidence: 70   // 最小置信度阈值（%）
  },

  // 建议类别
  categories: {
    performance: 'performance',
    scalability: 'scalability',
    reliability: 'reliability',
    security: 'security',
    cost: 'cost'
  }
};

/**
 * 获取环境特定配置
 */
export function getEnvironmentConfig() {
  const env = process.env.NODE_ENV || 'development';
  
  const envConfigs = {
    development: {
      monitoring: {
        ...monitoringConfig,
        intervals: {
          system: 60000,    // 开发环境监控间隔更长
          database: 120000,
          cache: 60000,
          api: 30000,
          health: 120000
        }
      },
      thresholds: {
        ...systemThresholds,
        cpu: { ...systemThresholds.cpu, warning: 80, critical: 90 },
        memory: { ...systemThresholds.memory, warning: 80, critical: 95 }
      }
    },
    
    production: {
      monitoring: monitoringConfig,
      thresholds: systemThresholds
    },
    
    test: {
      monitoring: {
        ...monitoringConfig,
        intervals: {
          system: 5000,     // 测试环境快速监控
          database: 5000,
          cache: 5000,
          api: 1000,
          health: 5000
        }
      },
      thresholds: {
        ...systemThresholds,
        cpu: { ...systemThresholds.cpu, warning: 90, critical: 95 },
        memory: { ...systemThresholds.memory, warning: 90, critical: 98 }
      }
    }
  };

  return envConfigs[env] || envConfigs.development;
}

/**
 * 验证配置
 */
export function validateConfig(): boolean {
  try {
    // 验证阈值配置的合理性
    const configs = [systemThresholds, databaseThresholds, cacheThresholds, apiThresholds];
    
    for (const config of configs) {
      for (const [key, thresholds] of Object.entries(config)) {
        if (typeof thresholds === 'object' && thresholds !== null) {
          const values = Object.values(thresholds) as number[];
          const sortedValues = values.sort((a, b) => a - b);
          
          // 检查阈值是否递增
          for (let i = 1; i < sortedValues.length; i++) {
            if (sortedValues[i] <= sortedValues[i - 1]) {
              console.warn(`配置警告: ${key} 的阈值配置不是递增的`);
            }
          }
        }
      }
    }

    return true;
  } catch (error) {
    console.error('配置验证失败:', error);
    return false;
  }
}
