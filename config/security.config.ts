/**
 * 安全配置
 * 定义系统安全策略、审计规则、扫描配置和合规性要求
 */

/**
 * 密码策略配置
 */
export const passwordPolicy = {
  // 最小长度
  minLength: 8,
  
  // 最大长度
  maxLength: 128,
  
  // 复杂度要求
  complexity: {
    requireUppercase: true,    // 需要大写字母
    requireLowercase: true,    // 需要小写字母
    requireNumbers: true,      // 需要数字
    requireSpecialChars: true, // 需要特殊字符
    minUniqueChars: 4         // 最少不同字符数
  },
  
  // 历史密码检查
  history: {
    enabled: true,
    count: 5  // 不能重复最近5个密码
  },
  
  // 密码过期
  expiration: {
    enabled: true,
    days: 90,  // 90天过期
    warningDays: 7  // 过期前7天开始警告
  },
  
  // 账户锁定策略
  lockout: {
    enabled: true,
    maxAttempts: 5,           // 最大失败尝试次数
    lockoutDuration: 30 * 60, // 锁定时间（秒）
    resetTime: 15 * 60        // 失败计数重置时间（秒）
  }
};

/**
 * 会话安全配置
 */
export const sessionSecurity = {
  // 会话超时
  timeout: {
    idle: 30 * 60,      // 空闲超时（秒）
    absolute: 8 * 60 * 60, // 绝对超时（秒）
    warning: 5 * 60     // 超时警告提前时间（秒）
  },
  
  // 会话固定保护
  regenerateOnLogin: true,
  
  // 并发会话限制
  concurrentSessions: {
    enabled: true,
    maxSessions: 3,  // 每用户最大并发会话数
    strategy: 'reject_new' // 'reject_new' | 'terminate_oldest'
  },
  
  // 会话Cookie配置
  cookie: {
    secure: process.env.NODE_ENV === 'production', // 生产环境强制HTTPS
    httpOnly: true,
    sameSite: 'strict',
    maxAge: 24 * 60 * 60 * 1000 // 24小时
  }
};

/**
 * JWT安全配置
 */
export const jwtSecurity = {
  // 访问令牌配置
  accessToken: {
    expiresIn: '15m',        // 15分钟过期
    algorithm: 'HS256',      // 签名算法
    issuer: 'id-provider',   // 发行者
    audience: 'api-clients'  // 受众
  },
  
  // 刷新令牌配置
  refreshToken: {
    expiresIn: '7d',         // 7天过期
    algorithm: 'HS256',
    issuer: 'id-provider',
    audience: 'api-clients'
  },
  
  // 令牌轮换
  rotation: {
    enabled: true,
    threshold: 0.5  // 剩余时间少于50%时轮换
  },
  
  // 黑名单配置
  blacklist: {
    enabled: true,
    cleanupInterval: 60 * 60 * 1000, // 1小时清理一次过期令牌
    maxSize: 10000  // 最大黑名单条目数
  }
};

/**
 * 速率限制配置
 */
export const rateLimiting = {
  // 全局限制
  global: {
    windowMs: 15 * 60 * 1000, // 15分钟窗口
    max: 1000,                // 每个IP最多1000个请求
    skipSuccessfulRequests: false
  },
  
  // 认证端点限制
  auth: {
    windowMs: 15 * 60 * 1000, // 15分钟窗口
    max: 20,                  // 每个IP最多20次认证尝试
    skipSuccessfulRequests: true
  },
  
  // API端点限制
  api: {
    windowMs: 15 * 60 * 1000, // 15分钟窗口
    max: 500,                 // 每个IP最多500个API请求
    skipSuccessfulRequests: false
  },
  
  // 管理端点限制
  admin: {
    windowMs: 15 * 60 * 1000, // 15分钟窗口
    max: 100,                 // 每个IP最多100个管理请求
    skipSuccessfulRequests: false
  }
};

/**
 * 审计配置
 */
export const auditConfig = {
  // 审计事件配置
  events: {
    // 认证事件
    authentication: {
      enabled: true,
      logSuccess: true,
      logFailure: true,
      includeDetails: true
    },
    
    // 授权事件
    authorization: {
      enabled: true,
      logSuccess: false,  // 成功的授权不记录（太多）
      logFailure: true,
      includeDetails: true
    },
    
    // 数据访问事件
    dataAccess: {
      enabled: true,
      logSuccess: false,  // 只记录敏感数据访问
      logFailure: true,
      sensitiveResources: ['user', 'admin', 'config'],
      includeDetails: true
    },
    
    // 管理操作事件
    adminActions: {
      enabled: true,
      logSuccess: true,
      logFailure: true,
      includeDetails: true
    },
    
    // 安全事件
    security: {
      enabled: true,
      logSuccess: true,
      logFailure: true,
      includeDetails: true,
      realTimeAlerts: true
    }
  },
  
  // 日志保留策略
  retention: {
    days: 365,  // 保留1年
    archiveAfterDays: 90,  // 90天后归档
    compressionEnabled: true
  },
  
  // 日志完整性
  integrity: {
    enabled: true,
    hashAlgorithm: 'sha256',
    signLogs: true
  }
};

/**
 * 威胁检测配置
 */
export const threatDetection = {
  // 暴力破解检测
  bruteForce: {
    enabled: true,
    maxAttempts: 10,          // 最大尝试次数
    timeWindow: 15 * 60,      // 时间窗口（秒）
    blockDuration: 60 * 60,   // 阻止时间（秒）
    escalationThreshold: 50   // 升级阈值
  },
  
  // 可疑活动检测
  suspiciousActivity: {
    enabled: true,
    
    // IP地址变化检测
    ipLocationChange: {
      enabled: true,
      maxDistance: 1000,  // 最大距离（公里）
      timeThreshold: 60   // 时间阈值（分钟）
    },
    
    // 用户代理变化检测
    userAgentChange: {
      enabled: true,
      similarityThreshold: 0.7  // 相似度阈值
    },
    
    // 异常时间访问检测
    unusualTimeAccess: {
      enabled: true,
      learningPeriod: 30,  // 学习期（天）
      deviationThreshold: 2 // 标准差阈值
    },
    
    // 高频操作检测
    highFrequencyActions: {
      enabled: true,
      thresholds: {
        login: { count: 20, window: 5 * 60 },      // 5分钟内20次登录
        dataAccess: { count: 100, window: 5 * 60 }, // 5分钟内100次数据访问
        apiCall: { count: 200, window: 5 * 60 }     // 5分钟内200次API调用
      }
    }
  },
  
  // 恶意载荷检测
  maliciousPayload: {
    enabled: true,
    
    // SQL注入检测
    sqlInjection: {
      enabled: true,
      patterns: [
        /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION|SCRIPT)\b)/i,
        /(\'|\"|;|--|\*|\||\^|&)/,
        /(\bOR\b|\bAND\b).*(\=|\<|\>)/i
      ]
    },
    
    // XSS检测
    xss: {
      enabled: true,
      patterns: [
        /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
        /<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi,
        /javascript:/gi,
        /on\w+\s*=/gi
      ]
    },
    
    // 路径遍历检测
    pathTraversal: {
      enabled: true,
      patterns: [
        /\.\./,
        /%2e%2e/i,
        /\0/,
        /%00/
      ]
    }
  }
};

/**
 * 漏洞扫描配置
 */
export const vulnerabilityScanning = {
  // 扫描调度
  schedule: {
    enabled: true,
    
    // 全面扫描
    fullScan: {
      cron: '0 2 * * 0',  // 每周日凌晨2点
      timeout: 60 * 60    // 1小时超时
    },
    
    // 依赖项扫描
    dependencyScan: {
      cron: '0 3 * * *',  // 每天凌晨3点
      timeout: 30 * 60    // 30分钟超时
    },
    
    // 配置扫描
    configScan: {
      cron: '0 4 * * *',  // 每天凌晨4点
      timeout: 15 * 60    // 15分钟超时
    }
  },
  
  // 扫描范围
  scope: {
    // 依赖项扫描
    dependencies: {
      enabled: true,
      includeDevDependencies: true,
      checkOutdated: true,
      severityThreshold: 'low'
    },
    
    // 代码扫描
    code: {
      enabled: true,
      directories: ['src', 'lib'],
      excludePatterns: ['node_modules', 'dist', 'build'],
      fileTypes: ['.js', '.ts', '.jsx', '.tsx']
    },
    
    // 配置扫描
    configuration: {
      enabled: true,
      checkEnvironmentVars: true,
      checkFilePermissions: true,
      checkServiceConfig: true
    },
    
    // 基础设施扫描
    infrastructure: {
      enabled: true,
      checkPorts: true,
      checkServices: true,
      checkNetworkConfig: true
    }
  },
  
  // 报告配置
  reporting: {
    enabled: true,
    formats: ['json', 'html'],
    includeRecommendations: true,
    autoNotify: true,
    notificationThreshold: 'medium'
  }
};

/**
 * 合规性配置
 */
export const complianceConfig = {
  // 支持的标准
  standards: {
    // GDPR配置
    GDPR: {
      enabled: true,
      checks: [
        'data_processing_lawfulness',
        'data_subject_rights',
        'data_protection_impact_assessment',
        'privacy_by_design',
        'data_breach_notification',
        'data_retention_policy'
      ],
      reportingRequired: true,
      auditFrequency: 'quarterly'
    },
    
    // SOX配置
    SOX: {
      enabled: false,
      checks: [
        'access_controls',
        'audit_trails',
        'change_management',
        'segregation_of_duties'
      ],
      reportingRequired: true,
      auditFrequency: 'annually'
    },
    
    // HIPAA配置
    HIPAA: {
      enabled: false,
      checks: [
        'access_controls',
        'audit_controls',
        'integrity',
        'person_or_entity_authentication',
        'transmission_security'
      ],
      reportingRequired: true,
      auditFrequency: 'annually'
    },
    
    // PCI DSS配置
    PCI_DSS: {
      enabled: false,
      checks: [
        'network_security',
        'vulnerability_management',
        'access_control',
        'network_monitoring',
        'security_policies'
      ],
      reportingRequired: true,
      auditFrequency: 'quarterly'
    }
  },
  
  // 自动合规检查
  automation: {
    enabled: true,
    schedule: '0 1 * * 1',  // 每周一凌晨1点
    generateReports: true,
    notifyOnFailure: true
  }
};

/**
 * 安全头部配置
 */
export const securityHeaders = {
  // 内容安全策略
  contentSecurityPolicy: {
    enabled: true,
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
      fontSrc: ["'self'", "https://fonts.gstatic.com"],
      imgSrc: ["'self'", "data:", "https:"],
      scriptSrc: ["'self'"],
      connectSrc: ["'self'"],
      frameSrc: ["'none'"],
      objectSrc: ["'none'"]
    },
    reportOnly: false
  },
  
  // 严格传输安全
  hsts: {
    enabled: true,
    maxAge: 31536000,  // 1年
    includeSubDomains: true,
    preload: true
  },
  
  // X-Frame-Options
  frameOptions: {
    enabled: true,
    action: 'DENY'
  },
  
  // X-Content-Type-Options
  noSniff: {
    enabled: true
  },
  
  // X-XSS-Protection
  xssProtection: {
    enabled: true,
    mode: 'block'
  },
  
  // Referrer Policy
  referrerPolicy: {
    enabled: true,
    policy: 'no-referrer'
  }
};

/**
 * 加密配置
 */
export const encryptionConfig = {
  // 数据加密
  dataEncryption: {
    algorithm: 'aes-256-gcm',
    keyRotation: {
      enabled: true,
      interval: 90 * 24 * 60 * 60 * 1000  // 90天
    }
  },
  
  // 传输加密
  transportEncryption: {
    minTlsVersion: '1.2',
    cipherSuites: [
      'ECDHE-RSA-AES128-GCM-SHA256',
      'ECDHE-RSA-AES256-GCM-SHA384',
      'ECDHE-RSA-AES128-SHA256',
      'ECDHE-RSA-AES256-SHA384'
    ]
  },
  
  // 哈希配置
  hashing: {
    algorithm: 'bcrypt',
    rounds: 12,
    pepper: process.env.PASSWORD_PEPPER || ''
  }
};

/**
 * 获取环境特定的安全配置
 */
export function getEnvironmentSecurityConfig() {
  const env = process.env.NODE_ENV || 'development';
  
  const envConfigs = {
    development: {
      // 开发环境相对宽松的配置
      passwordPolicy: {
        ...passwordPolicy,
        complexity: {
          ...passwordPolicy.complexity,
          requireSpecialChars: false
        }
      },
      sessionSecurity: {
        ...sessionSecurity,
        timeout: {
          ...sessionSecurity.timeout,
          idle: 60 * 60  // 1小时空闲超时
        }
      }
    },
    
    production: {
      // 生产环境严格配置
      passwordPolicy,
      sessionSecurity,
      securityHeaders: {
        ...securityHeaders,
        contentSecurityPolicy: {
          ...securityHeaders.contentSecurityPolicy,
          reportOnly: false
        }
      }
    },
    
    test: {
      // 测试环境配置
      passwordPolicy: {
        ...passwordPolicy,
        minLength: 6,
        complexity: {
          requireUppercase: false,
          requireLowercase: true,
          requireNumbers: false,
          requireSpecialChars: false,
          minUniqueChars: 3
        }
      }
    }
  };

  return envConfigs[env] || envConfigs.development;
}

/**
 * 验证安全配置
 */
export function validateSecurityConfig(): boolean {
  try {
    // 验证必需的环境变量
    const requiredEnvVars = [
      'JWT_SECRET',
      'SESSION_SECRET',
      'DATABASE_URL'
    ];

    for (const envVar of requiredEnvVars) {
      if (!process.env[envVar]) {
        console.error(`缺少必需的环境变量: ${envVar}`);
        return false;
      }
    }

    // 验证JWT密钥强度
    const jwtSecret = process.env.JWT_SECRET;
    if (jwtSecret && jwtSecret.length < 32) {
      console.warn('JWT_SECRET长度不足，建议至少32字符');
    }

    // 验证会话密钥强度
    const sessionSecret = process.env.SESSION_SECRET;
    if (sessionSecret && (sessionSecret.length < 32 || sessionSecret === 'default-secret')) {
      console.warn('SESSION_SECRET不安全，请使用强密钥');
    }

    return true;
  } catch (error) {
    console.error('安全配置验证失败:', error);
    return false;
  }
}
