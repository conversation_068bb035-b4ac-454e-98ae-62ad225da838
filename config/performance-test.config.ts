/**
 * 性能测试配置
 * 定义各种性能测试场景的配置参数
 */

import { TestType, TestConfig } from '../src/services/performance-test.service';

/**
 * 预定义的性能测试配置
 */
export const performanceTestConfigs: Record<string, TestConfig> = {
  // 基础负载测试
  basicLoad: {
    type: TestType.LOAD,
    name: '基础负载测试',
    description: '模拟正常业务负载的基础测试',
    duration: 5 * 60 * 1000, // 5分钟
    concurrency: 20,
    rampUpTime: 30 * 1000, // 30秒爬坡
    rampDownTime: 30 * 1000, // 30秒降压
    targetRPS: 50,
    maxErrors: 100,
    thresholds: {
      responseTime: {
        avg: 500,
        p95: 1000,
        p99: 2000
      },
      errorRate: 5,
      throughput: 40
    }
  },

  // 高负载测试
  highLoad: {
    type: TestType.LOAD,
    name: '高负载测试',
    description: '模拟高峰期业务负载',
    duration: 10 * 60 * 1000, // 10分钟
    concurrency: 50,
    rampUpTime: 60 * 1000, // 1分钟爬坡
    rampDownTime: 60 * 1000, // 1分钟降压
    targetRPS: 100,
    maxErrors: 200,
    thresholds: {
      responseTime: {
        avg: 800,
        p95: 1500,
        p99: 3000
      },
      errorRate: 8,
      throughput: 80
    }
  },

  // 压力测试
  stressTest: {
    type: TestType.STRESS,
    name: '系统压力测试',
    description: '测试系统在极限负载下的表现',
    duration: 15 * 60 * 1000, // 15分钟
    concurrency: 100,
    rampUpTime: 2 * 60 * 1000, // 2分钟爬坡
    rampDownTime: 2 * 60 * 1000, // 2分钟降压
    targetRPS: 200,
    maxErrors: 500,
    thresholds: {
      responseTime: {
        avg: 1200,
        p95: 2500,
        p99: 5000
      },
      errorRate: 15,
      throughput: 150
    }
  },

  // 峰值测试
  spikeTest: {
    type: TestType.SPIKE,
    name: '峰值冲击测试',
    description: '测试系统对突发流量的处理能力',
    duration: 8 * 60 * 1000, // 8分钟
    concurrency: 200,
    rampUpTime: 10 * 1000, // 10秒快速爬坡
    rampDownTime: 10 * 1000, // 10秒快速降压
    targetRPS: 300,
    maxErrors: 1000,
    thresholds: {
      responseTime: {
        avg: 2000,
        p95: 4000,
        p99: 8000
      },
      errorRate: 25,
      throughput: 200
    }
  },

  // 容量测试
  volumeTest: {
    type: TestType.VOLUME,
    name: '大容量数据测试',
    description: '测试系统处理大量数据的能力',
    duration: 20 * 60 * 1000, // 20分钟
    concurrency: 30,
    rampUpTime: 60 * 1000, // 1分钟爬坡
    rampDownTime: 60 * 1000, // 1分钟降压
    targetRPS: 40,
    maxErrors: 150,
    thresholds: {
      responseTime: {
        avg: 1000,
        p95: 2000,
        p99: 4000
      },
      errorRate: 10,
      throughput: 30
    }
  },

  // 耐久性测试
  enduranceTest: {
    type: TestType.ENDURANCE,
    name: '系统耐久性测试',
    description: '测试系统长时间运行的稳定性',
    duration: 60 * 60 * 1000, // 1小时
    concurrency: 25,
    rampUpTime: 5 * 60 * 1000, // 5分钟爬坡
    rampDownTime: 5 * 60 * 1000, // 5分钟降压
    targetRPS: 30,
    maxErrors: 200,
    thresholds: {
      responseTime: {
        avg: 600,
        p95: 1200,
        p99: 2500
      },
      errorRate: 5,
      throughput: 25
    }
  },

  // 基准测试
  baselineTest: {
    type: TestType.BASELINE,
    name: '性能基准测试',
    description: '建立系统性能基准线',
    duration: 3 * 60 * 1000, // 3分钟
    concurrency: 10,
    rampUpTime: 30 * 1000, // 30秒爬坡
    rampDownTime: 30 * 1000, // 30秒降压
    targetRPS: 20,
    maxErrors: 50,
    thresholds: {
      responseTime: {
        avg: 300,
        p95: 600,
        p99: 1200
      },
      errorRate: 2,
      throughput: 18
    }
  },

  // 认证系统专项测试
  authenticationTest: {
    type: TestType.LOAD,
    name: '认证系统负载测试',
    description: '专门测试认证相关接口的性能',
    duration: 8 * 60 * 1000, // 8分钟
    concurrency: 40,
    rampUpTime: 60 * 1000, // 1分钟爬坡
    rampDownTime: 60 * 1000, // 1分钟降压
    targetRPS: 80,
    maxErrors: 150,
    thresholds: {
      responseTime: {
        avg: 400,
        p95: 800,
        p99: 1500
      },
      errorRate: 6,
      throughput: 70
    }
  },

  // API接口专项测试
  apiTest: {
    type: TestType.LOAD,
    name: 'API接口负载测试',
    description: '测试各种API接口的性能表现',
    duration: 6 * 60 * 1000, // 6分钟
    concurrency: 35,
    rampUpTime: 45 * 1000, // 45秒爬坡
    rampDownTime: 45 * 1000, // 45秒降压
    targetRPS: 70,
    maxErrors: 120,
    thresholds: {
      responseTime: {
        avg: 350,
        p95: 700,
        p99: 1400
      },
      errorRate: 4,
      throughput: 60
    }
  },

  // 数据库专项测试
  databaseTest: {
    type: TestType.LOAD,
    name: '数据库负载测试',
    description: '专门测试数据库操作的性能',
    duration: 12 * 60 * 1000, // 12分钟
    concurrency: 25,
    rampUpTime: 90 * 1000, // 1.5分钟爬坡
    rampDownTime: 90 * 1000, // 1.5分钟降压
    targetRPS: 60,
    maxErrors: 100,
    thresholds: {
      responseTime: {
        avg: 200,
        p95: 400,
        p99: 800
      },
      errorRate: 3,
      throughput: 50
    }
  },

  // 缓存系统专项测试
  cacheTest: {
    type: TestType.LOAD,
    name: '缓存系统负载测试',
    description: '测试Redis缓存系统的性能',
    duration: 4 * 60 * 1000, // 4分钟
    concurrency: 60,
    rampUpTime: 30 * 1000, // 30秒爬坡
    rampDownTime: 30 * 1000, // 30秒降压
    targetRPS: 150,
    maxErrors: 80,
    thresholds: {
      responseTime: {
        avg: 50,
        p95: 100,
        p99: 200
      },
      errorRate: 2,
      throughput: 140
    }
  }
};

/**
 * 测试场景映射
 */
export const testScenarioMapping: Record<string, string> = {
  basicLoad: 'mixed',
  highLoad: 'mixed',
  stressTest: 'mixed',
  spikeTest: 'mixed',
  volumeTest: 'database',
  enduranceTest: 'mixed',
  baselineTest: 'mixed',
  authenticationTest: 'authentication',
  apiTest: 'api',
  databaseTest: 'database',
  cacheTest: 'cache'
};

/**
 * 环境特定配置
 */
export const environmentConfigs = {
  development: {
    // 开发环境配置 - 较低的负载
    scaleFactor: 0.3,
    maxConcurrency: 20,
    maxDuration: 5 * 60 * 1000 // 5分钟
  },
  
  staging: {
    // 预发布环境配置 - 中等负载
    scaleFactor: 0.7,
    maxConcurrency: 50,
    maxDuration: 15 * 60 * 1000 // 15分钟
  },
  
  production: {
    // 生产环境配置 - 完整负载
    scaleFactor: 1.0,
    maxConcurrency: 200,
    maxDuration: 60 * 60 * 1000 // 1小时
  }
};

/**
 * 根据环境调整测试配置
 */
export function adjustConfigForEnvironment(
  config: TestConfig, 
  environment: keyof typeof environmentConfigs = 'development'
): TestConfig {
  const envConfig = environmentConfigs[environment];
  
  return {
    ...config,
    duration: Math.min(config.duration * envConfig.scaleFactor, envConfig.maxDuration),
    concurrency: Math.min(Math.round(config.concurrency * envConfig.scaleFactor), envConfig.maxConcurrency),
    targetRPS: config.targetRPS ? Math.round(config.targetRPS * envConfig.scaleFactor) : undefined,
    maxErrors: config.maxErrors ? Math.round(config.maxErrors * envConfig.scaleFactor) : undefined,
    thresholds: config.thresholds ? {
      ...config.thresholds,
      responseTime: config.thresholds.responseTime ? {
        avg: config.thresholds.responseTime.avg ? config.thresholds.responseTime.avg / envConfig.scaleFactor : undefined,
        p95: config.thresholds.responseTime.p95 ? config.thresholds.responseTime.p95 / envConfig.scaleFactor : undefined,
        p99: config.thresholds.responseTime.p99 ? config.thresholds.responseTime.p99 / envConfig.scaleFactor : undefined
      } : undefined,
      throughput: config.thresholds.throughput ? config.thresholds.throughput * envConfig.scaleFactor : undefined
    } : undefined
  };
}

/**
 * 获取推荐的测试配置
 */
export function getRecommendedTestConfig(testType: 'quick' | 'standard' | 'comprehensive'): TestConfig {
  switch (testType) {
    case 'quick':
      return performanceTestConfigs.baselineTest;
    case 'standard':
      return performanceTestConfigs.basicLoad;
    case 'comprehensive':
      return performanceTestConfigs.stressTest;
    default:
      return performanceTestConfigs.basicLoad;
  }
}

/**
 * 获取所有可用的测试配置名称
 */
export function getAvailableTestConfigs(): string[] {
  return Object.keys(performanceTestConfigs);
}

/**
 * 根据名称获取测试配置
 */
export function getTestConfigByName(name: string): TestConfig | undefined {
  return performanceTestConfigs[name];
}
