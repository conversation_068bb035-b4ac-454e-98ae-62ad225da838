/**
 * 自动化安全扫描配置
 * 管理自动化安全扫描的各种配置选项
 */

/**
 * 自动化安全扫描配置接口
 */
export interface AutomatedSecurityScannerConfig {
  // 基础配置
  enabled: boolean;
  timezone: string;
  
  // 默认扫描计划
  defaultSchedules: {
    fullScan: {
      enabled: boolean;
      cronExpression: string;
      notificationThreshold: 'critical' | 'high' | 'medium' | 'low';
    };
    dependencyScan: {
      enabled: boolean;
      cronExpression: string;
      notificationThreshold: 'critical' | 'high' | 'medium' | 'low';
    };
    codeScan: {
      enabled: boolean;
      cronExpression: string;
      notificationThreshold: 'critical' | 'high' | 'medium' | 'low';
    };
    infrastructureScan: {
      enabled: boolean;
      cronExpression: string;
      notificationThreshold: 'critical' | 'high' | 'medium' | 'low';
    };
  };
  
  // 通知配置
  notifications: {
    email: {
      enabled: boolean;
      recipients: string[];
      smtpHost: string;
      smtpPort: number;
      smtpSecure: boolean;
      smtpUser: string;
      smtpPassword: string;
      fromAddress: string;
    };
    webhook: {
      enabled: boolean;
      url: string;
      token: string;
      timeout: number;
      retryAttempts: number;
    };
    slack: {
      enabled: boolean;
      webhookUrl: string;
      channel: string;
      username: string;
      iconEmoji: string;
    };
  };
  
  // 扫描结果存储配置
  storage: {
    retentionDays: number;
    maxResultsPerScan: number;
    compressionEnabled: boolean;
  };
  
  // 性能配置
  performance: {
    maxConcurrentScans: number;
    scanTimeout: number;
    resourceLimits: {
      maxMemoryUsage: number;
      maxCpuUsage: number;
    };
  };
}

/**
 * 获取自动化安全扫描配置
 */
export const automatedSecurityScannerConfig: AutomatedSecurityScannerConfig = {
  // 基础配置
  enabled: process.env.AUTOMATED_SECURITY_SCANNER_ENABLED === 'true',
  timezone: process.env.AUTOMATED_SECURITY_SCANNER_TIMEZONE || 'Asia/Shanghai',
  
  // 默认扫描计划
  defaultSchedules: {
    fullScan: {
      enabled: process.env.AUTOMATED_FULL_SCAN_ENABLED !== 'false',
      cronExpression: process.env.AUTOMATED_FULL_SCAN_CRON || '0 2 * * *', // 每天凌晨2点
      notificationThreshold: (process.env.AUTOMATED_FULL_SCAN_THRESHOLD as any) || 'medium'
    },
    dependencyScan: {
      enabled: process.env.AUTOMATED_DEPENDENCY_SCAN_ENABLED !== 'false',
      cronExpression: process.env.AUTOMATED_DEPENDENCY_SCAN_CRON || '0 */6 * * *', // 每6小时
      notificationThreshold: (process.env.AUTOMATED_DEPENDENCY_SCAN_THRESHOLD as any) || 'high'
    },
    codeScan: {
      enabled: process.env.AUTOMATED_CODE_SCAN_ENABLED !== 'false',
      cronExpression: process.env.AUTOMATED_CODE_SCAN_CRON || '0 4 * * *', // 每天凌晨4点
      notificationThreshold: (process.env.AUTOMATED_CODE_SCAN_THRESHOLD as any) || 'medium'
    },
    infrastructureScan: {
      enabled: process.env.AUTOMATED_INFRASTRUCTURE_SCAN_ENABLED !== 'false',
      cronExpression: process.env.AUTOMATED_INFRASTRUCTURE_SCAN_CRON || '0 3 * * 0', // 每周日凌晨3点
      notificationThreshold: (process.env.AUTOMATED_INFRASTRUCTURE_SCAN_THRESHOLD as any) || 'medium'
    }
  },
  
  // 通知配置
  notifications: {
    email: {
      enabled: process.env.SECURITY_SCAN_EMAIL_ENABLED === 'true',
      recipients: (process.env.SECURITY_SCAN_EMAIL_RECIPIENTS || '').split(',').filter(Boolean),
      smtpHost: process.env.SECURITY_SCAN_SMTP_HOST || 'localhost',
      smtpPort: parseInt(process.env.SECURITY_SCAN_SMTP_PORT || '587'),
      smtpSecure: process.env.SECURITY_SCAN_SMTP_SECURE === 'true',
      smtpUser: process.env.SECURITY_SCAN_SMTP_USER || '',
      smtpPassword: process.env.SECURITY_SCAN_SMTP_PASSWORD || '',
      fromAddress: process.env.SECURITY_SCAN_EMAIL_FROM || '<EMAIL>'
    },
    webhook: {
      enabled: process.env.SECURITY_SCAN_WEBHOOK_ENABLED === 'true',
      url: process.env.SECURITY_SCAN_WEBHOOK_URL || '',
      token: process.env.SECURITY_SCAN_WEBHOOK_TOKEN || '',
      timeout: parseInt(process.env.SECURITY_SCAN_WEBHOOK_TIMEOUT || '30000'),
      retryAttempts: parseInt(process.env.SECURITY_SCAN_WEBHOOK_RETRY || '3')
    },
    slack: {
      enabled: process.env.SECURITY_SCAN_SLACK_ENABLED === 'true',
      webhookUrl: process.env.SECURITY_SCAN_SLACK_WEBHOOK_URL || '',
      channel: process.env.SECURITY_SCAN_SLACK_CHANNEL || '#security',
      username: process.env.SECURITY_SCAN_SLACK_USERNAME || 'Security Scanner',
      iconEmoji: process.env.SECURITY_SCAN_SLACK_ICON || ':shield:'
    }
  },
  
  // 扫描结果存储配置
  storage: {
    retentionDays: parseInt(process.env.SECURITY_SCAN_RETENTION_DAYS || '30'),
    maxResultsPerScan: parseInt(process.env.SECURITY_SCAN_MAX_RESULTS || '1000'),
    compressionEnabled: process.env.SECURITY_SCAN_COMPRESSION_ENABLED !== 'false'
  },
  
  // 性能配置
  performance: {
    maxConcurrentScans: parseInt(process.env.SECURITY_SCAN_MAX_CONCURRENT || '2'),
    scanTimeout: parseInt(process.env.SECURITY_SCAN_TIMEOUT || '1800000'), // 30分钟
    resourceLimits: {
      maxMemoryUsage: parseInt(process.env.SECURITY_SCAN_MAX_MEMORY || '512'), // MB
      maxCpuUsage: parseInt(process.env.SECURITY_SCAN_MAX_CPU || '80') // 百分比
    }
  }
};

/**
 * 验证自动化安全扫描配置
 */
export function validateAutomatedSecurityScannerConfig(): void {
  const config = automatedSecurityScannerConfig;
  
  // 验证基础配置
  if (!config.timezone) {
    throw new Error('自动化安全扫描时区配置不能为空');
  }
  
  // 验证Cron表达式格式（简单验证）
  const cronRegex = /^(\*|[0-5]?\d) (\*|[01]?\d|2[0-3]) (\*|[0-2]?\d|3[01]) (\*|[0-1]?\d) (\*|[0-6])$/;
  
  Object.entries(config.defaultSchedules).forEach(([key, schedule]) => {
    if (schedule.enabled && !cronRegex.test(schedule.cronExpression)) {
      throw new Error(`无效的Cron表达式: ${key} - ${schedule.cronExpression}`);
    }
  });
  
  // 验证通知配置
  if (config.notifications.email.enabled) {
    if (!config.notifications.email.smtpHost) {
      throw new Error('启用邮件通知时，SMTP主机不能为空');
    }
    if (config.notifications.email.recipients.length === 0) {
      throw new Error('启用邮件通知时，收件人列表不能为空');
    }
  }
  
  if (config.notifications.webhook.enabled) {
    if (!config.notifications.webhook.url) {
      throw new Error('启用Webhook通知时，URL不能为空');
    }
    try {
      new URL(config.notifications.webhook.url);
    } catch {
      throw new Error('Webhook URL格式无效');
    }
  }
  
  if (config.notifications.slack.enabled) {
    if (!config.notifications.slack.webhookUrl) {
      throw new Error('启用Slack通知时，Webhook URL不能为空');
    }
    try {
      new URL(config.notifications.slack.webhookUrl);
    } catch {
      throw new Error('Slack Webhook URL格式无效');
    }
  }
  
  // 验证性能配置
  if (config.performance.maxConcurrentScans < 1) {
    throw new Error('最大并发扫描数必须大于0');
  }
  
  if (config.performance.scanTimeout < 60000) { // 最少1分钟
    throw new Error('扫描超时时间不能少于60秒');
  }
  
  if (config.performance.resourceLimits.maxMemoryUsage < 128) {
    throw new Error('最大内存使用量不能少于128MB');
  }
  
  if (config.performance.resourceLimits.maxCpuUsage < 10 || config.performance.resourceLimits.maxCpuUsage > 100) {
    throw new Error('最大CPU使用率必须在10-100之间');
  }
  
  // 验证存储配置
  if (config.storage.retentionDays < 1) {
    throw new Error('扫描结果保留天数必须大于0');
  }
  
  if (config.storage.maxResultsPerScan < 10) {
    throw new Error('每次扫描最大结果数不能少于10');
  }
}

/**
 * 获取扫描计划的显示名称
 */
export function getScanScheduleDisplayName(scheduleType: keyof typeof automatedSecurityScannerConfig.defaultSchedules): string {
  const names = {
    fullScan: '全面安全扫描',
    dependencyScan: '依赖项安全扫描',
    codeScan: '代码安全扫描',
    infrastructureScan: '基础设施安全扫描'
  };
  
  return names[scheduleType] || scheduleType;
}

/**
 * 获取通知阈值的显示名称
 */
export function getNotificationThresholdDisplayName(threshold: string): string {
  const names = {
    critical: '严重',
    high: '高',
    medium: '中',
    low: '低'
  };
  
  return names[threshold as keyof typeof names] || threshold;
}

/**
 * 检查是否启用了任何通知方式
 */
export function isAnyNotificationEnabled(): boolean {
  const config = automatedSecurityScannerConfig;
  return config.notifications.email.enabled || 
         config.notifications.webhook.enabled || 
         config.notifications.slack.enabled;
}

/**
 * 获取启用的扫描计划数量
 */
export function getEnabledScheduleCount(): number {
  const config = automatedSecurityScannerConfig;
  return Object.values(config.defaultSchedules).filter(schedule => schedule.enabled).length;
}

export default automatedSecurityScannerConfig;
