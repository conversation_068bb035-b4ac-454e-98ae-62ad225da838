/**
 * 组织架构权限控制系统测试
 * 测试组织架构管理、权限继承、跨组织访问等核心功能
 */

import { describe, test, expect, beforeAll, afterAll, beforeEach } from '@jest/testing-library';
import { prisma } from '@/config/database';
import { organizationService } from '@/services/organization.service';
import { organizationPermissionService } from '@/services/organization-permission.service';
import { permissionRequestService } from '@/services/permission-request.service';

describe('组织架构权限控制系统测试', () => {
  let testUser1: any;
  let testUser2: any;
  let rootOrg: any;
  let engineeringOrg: any;
  let backendTeam: any;
  let frontendTeam: any;

  beforeAll(async () => {
    // 创建测试用户
    testUser1 = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        username: 'testuser1',
        firstName: 'Test',
        lastName: 'User1',
        passwordHash: 'hashed_password'
      }
    });

    testUser2 = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        username: 'testuser2',
        firstName: 'Test',
        lastName: 'User2',
        passwordHash: 'hashed_password'
      }
    });

    // 创建测试权限
    await prisma.permission.createMany({
      data: [
        {
          name: 'read:global_data',
          displayName: '读取全局数据',
          description: '读取全局数据权限',
          category: 'data',
          scope: 'global',
          type: 'data',
          level: 'read'
        },
        {
          name: 'read:team_data',
          displayName: '读取团队数据',
          description: '读取团队数据权限',
          category: 'data',
          scope: 'application',
          type: 'data',
          level: 'read'
        },
        {
          name: 'write:code',
          displayName: '编写代码',
          description: '代码编写权限',
          category: 'development',
          scope: 'application',
          type: 'action',
          level: 'write'
        }
      ]
    });
  });

  afterAll(async () => {
    // 清理测试数据
    await prisma.organizationAccessLog.deleteMany({});
    await prisma.permissionDelegation.deleteMany({});
    await prisma.permissionRequest.deleteMany({});
    await prisma.organizationPermission.deleteMany({});
    await prisma.organizationMember.deleteMany({});
    await prisma.organization.deleteMany({});
    await prisma.permission.deleteMany({});
    await prisma.user.deleteMany({
      where: {
        email: {
          in: ['<EMAIL>', '<EMAIL>']
        }
      }
    });
  });

  describe('组织架构管理', () => {
    test('应该能够创建根组织', async () => {
      rootOrg = await organizationService.createOrganization({
        name: 'acme',
        displayName: 'ACME公司',
        description: '测试公司',
        type: 'company',
        status: 'active',
        metadata: {},
        permissionInheritance: true,
        dataIsolationLevel: 'inherit',
        settings: {}
      }, testUser1.id);

      expect(rootOrg).toBeDefined();
      expect(rootOrg.name).toBe('acme');
      expect(rootOrg.path).toBe('acme');
      expect(rootOrg.level).toBe(0);
    });

    test('应该能够创建子组织', async () => {
      engineeringOrg = await organizationService.createOrganization({
        name: 'engineering',
        displayName: '工程部',
        description: '技术研发部门',
        parentId: rootOrg.id,
        type: 'division',
        status: 'active',
        metadata: {},
        permissionInheritance: true,
        dataIsolationLevel: 'inherit',
        settings: {}
      }, testUser1.id);

      expect(engineeringOrg).toBeDefined();
      expect(engineeringOrg.name).toBe('engineering');
      expect(engineeringOrg.path).toBe('acme.engineering');
      expect(engineeringOrg.level).toBe(1);
      expect(engineeringOrg.parentId).toBe(rootOrg.id);
    });

    test('应该能够创建团队组织', async () => {
      backendTeam = await organizationService.createOrganization({
        name: 'backend',
        displayName: '后端团队',
        description: '后端开发团队',
        parentId: engineeringOrg.id,
        type: 'team',
        status: 'active',
        metadata: {},
        permissionInheritance: true,
        dataIsolationLevel: 'strict',
        settings: {}
      }, testUser1.id);

      frontendTeam = await organizationService.createOrganization({
        name: 'frontend',
        displayName: '前端团队',
        description: '前端开发团队',
        parentId: engineeringOrg.id,
        type: 'team',
        status: 'active',
        metadata: {},
        permissionInheritance: true,
        dataIsolationLevel: 'inherit',
        settings: {}
      }, testUser1.id);

      expect(backendTeam.path).toBe('acme.engineering.backend');
      expect(frontendTeam.path).toBe('acme.engineering.frontend');
      expect(backendTeam.level).toBe(2);
      expect(frontendTeam.level).toBe(2);
    });

    test('应该能够获取组织层次结构', async () => {
      const hierarchy = await organizationService.getOrganizationHierarchy(backendTeam.path);

      expect(hierarchy).toHaveLength(3);
      expect(hierarchy[0].path).toBe('acme');
      expect(hierarchy[1].path).toBe('acme.engineering');
      expect(hierarchy[2].path).toBe('acme.engineering.backend');
    });

    test('应该能够获取子组织列表', async () => {
      const children = await organizationService.getChildOrganizations(engineeringOrg.id);

      expect(children).toHaveLength(2);
      expect(children.map(c => c.name)).toContain('backend');
      expect(children.map(c => c.name)).toContain('frontend');
    });
  });

  describe('组织成员管理', () => {
    test('应该能够添加组织成员', async () => {
      const member = await organizationService.addOrganizationMember(
        backendTeam.id,
        testUser1.id,
        'admin',
        ['read:team_data', 'write:code'],
        testUser1.id,
        { isOwner: true, isPrimary: true }
      );

      expect(member).toBeDefined();
      expect(member.userId).toBe(testUser1.id);
      expect(member.organizationId).toBe(backendTeam.id);
      expect(member.role).toBe('admin');
      expect(member.isOwner).toBe(true);
    });

    test('应该能够检查用户组织关系', async () => {
      const isInOrg = await organizationService.isUserInOrganization(
        testUser1.id,
        backendTeam.id
      );

      expect(isInOrg).toBe(true);
    });

    test('应该能够获取用户组织列表', async () => {
      const userOrgs = await organizationService.getUserOrganizations(testUser1.id);

      expect(userOrgs).toHaveLength(1);
      expect(userOrgs[0].organizationId).toBe(backendTeam.id);
      expect(userOrgs[0].role).toBe('admin');
    });
  });

  describe('权限继承机制', () => {
    beforeEach(async () => {
      // 设置组织权限
      const globalDataPermission = await prisma.permission.findFirst({
        where: { name: 'read:global_data' }
      });

      const teamDataPermission = await prisma.permission.findFirst({
        where: { name: 'read:team_data' }
      });

      // 在根组织设置全局数据读取权限
      await prisma.organizationPermission.create({
        data: {
          organizationId: rootOrg.id,
          permissionId: globalDataPermission!.id,
          scope: 'descendants',
          inheritanceRule: 'inherit',
          grantedBy: testUser1.id
        }
      });

      // 在工程部设置团队数据读取权限
      await prisma.organizationPermission.create({
        data: {
          organizationId: engineeringOrg.id,
          permissionId: teamDataPermission!.id,
          scope: 'children',
          inheritanceRule: 'inherit',
          grantedBy: testUser1.id
        }
      });
    });

    test('应该能够解析用户有效权限', async () => {
      const effectivePermissions = await organizationPermissionService.resolveUserPermissions(
        testUser1.id,
        backendTeam.path
      );

      expect(effectivePermissions.permissions).toContain('read:global_data');
      expect(effectivePermissions.permissions).toContain('read:team_data');
      expect(effectivePermissions.organizationPath).toBe(backendTeam.path);
      expect(effectivePermissions.inheritedFrom).toContain('acme');
      expect(effectivePermissions.inheritedFrom).toContain('acme.engineering');
    });

    test('应该能够验证用户权限', async () => {
      const result = await organizationPermissionService.validatePermission({
        userId: testUser1.id,
        organizationId: backendTeam.id,
        action: 'read:global_data'
      });

      expect(result.granted).toBe(true);
      expect(result.organizationPath).toBe(backendTeam.path);
      expect(result.inheritanceChain).toContain('acme');
    });

    test('应该拒绝无权限的访问', async () => {
      const result = await organizationPermissionService.validatePermission({
        userId: testUser2.id,
        organizationId: backendTeam.id,
        action: 'read:global_data'
      });

      expect(result.granted).toBe(false);
      expect(result.reason).toContain('不属于任何组织');
    });
  });

  describe('跨组织权限管理', () => {
    beforeEach(async () => {
      // 将testUser2添加到前端团队
      await organizationService.addOrganizationMember(
        frontendTeam.id,
        testUser2.id,
        'member',
        ['read:team_data'],
        testUser1.id
      );
    });

    test('应该能够检查跨组织访问权限', async () => {
      // 同一父组织下的团队应该可以访问
      const hasAccess = await organizationPermissionService.checkCrossOrganizationAccess(
        testUser2.id,
        frontendTeam.path,
        backendTeam.path,
        'read:team_data'
      );

      // 由于数据隔离级别，这应该被拒绝
      expect(hasAccess).toBe(false);
    });

    test('应该能够创建权限申请', async () => {
      const request = await permissionRequestService.createPermissionRequest(
        testUser2.id,
        {
          targetOrganizationId: backendTeam.id,
          requestedPermissions: ['read:team_data'],
          requestType: 'temporary',
          reason: '需要临时访问后端团队数据进行项目协作',
          priority: 'normal',
          requestedDuration: 24
        }
      );

      expect(request).toBeDefined();
      expect(request.requesterId).toBe(testUser2.id);
      expect(request.targetOrganizationId).toBe(backendTeam.id);
      expect(request.status).toBe('pending');
      expect(request.requestedPermissions).toContain('read:team_data');
    });

    test('应该能够审批权限申请', async () => {
      // 创建申请
      const request = await permissionRequestService.createPermissionRequest(
        testUser2.id,
        {
          targetOrganizationId: backendTeam.id,
          requestedPermissions: ['read:team_data'],
          requestType: 'project',
          reason: '项目协作需要',
          priority: 'normal',
          requestedDuration: 168
        }
      );

      // 审批申请
      const approvedRequest = await permissionRequestService.approvePermissionRequest({
        requestId: request.id,
        approverId: testUser1.id,
        approvedPermissions: ['read:team_data'],
        approvedDuration: 72
      });

      expect(approvedRequest.status).toBe('approved');
      expect(approvedRequest.approvedBy).toBe(testUser1.id);
    });

    test('应该能够拒绝权限申请', async () => {
      // 创建申请
      const request = await permissionRequestService.createPermissionRequest(
        testUser2.id,
        {
          targetOrganizationId: backendTeam.id,
          requestedPermissions: ['write:code'],
          requestType: 'temporary',
          reason: '需要修改代码',
          priority: 'normal',
          requestedDuration: 24
        }
      );

      // 拒绝申请
      const rejectedRequest = await permissionRequestService.rejectPermissionRequest({
        requestId: request.id,
        rejectorId: testUser1.id,
        reason: '权限级别过高，不适合临时授予'
      });

      expect(rejectedRequest.status).toBe('rejected');
      expect(rejectedRequest.rejectedBy).toBe(testUser1.id);
      expect(rejectedRequest.rejectionReason).toContain('权限级别过高');
    });
  });

  describe('权限监控和审计', () => {
    test('应该记录权限使用日志', async () => {
      // 执行权限验证（会自动记录日志）
      await organizationPermissionService.validatePermission({
        userId: testUser1.id,
        organizationId: backendTeam.id,
        action: 'read:team_data',
        resourceType: 'document',
        resourceId: 'doc-123',
        ipAddress: '*************'
      });

      // 检查日志记录
      const logs = await prisma.organizationAccessLog.findMany({
        where: {
          userId: testUser1.id,
          targetOrganizationId: backendTeam.id
        }
      });

      expect(logs).toHaveLength(1);
      expect(logs[0].accessType).toBe('read:team_data');
      expect(logs[0].resourceType).toBe('document');
      expect(logs[0].accessGranted).toBe(true);
    });

    test('应该能够查询用户权限申请历史', async () => {
      const { requests } = await permissionRequestService.getUserPermissionRequests(
        testUser2.id,
        { status: 'pending' }
      );

      expect(requests.length).toBeGreaterThan(0);
      expect(requests[0].requesterId).toBe(testUser2.id);
    });
  });

  describe('层次权限函数测试', () => {
    test('应该正确识别祖先关系', async () => {
      const { HierarchyUtils } = await import('@/services/organization-permission.service');
      
      expect(HierarchyUtils.ancestorOf('acme', 'acme.engineering')).toBe(true);
      expect(HierarchyUtils.ancestorOf('acme', 'acme.engineering.backend')).toBe(true);
      expect(HierarchyUtils.ancestorOf('acme.engineering', 'acme.engineering.backend')).toBe(true);
      expect(HierarchyUtils.ancestorOf('acme.engineering.backend', 'acme')).toBe(false);
    });

    test('应该正确识别后代关系', async () => {
      const { HierarchyUtils } = await import('@/services/organization-permission.service');
      
      expect(HierarchyUtils.descendantOf('acme.engineering', 'acme')).toBe(true);
      expect(HierarchyUtils.descendantOf('acme.engineering.backend', 'acme')).toBe(true);
      expect(HierarchyUtils.descendantOf('acme.engineering.backend', 'acme.engineering')).toBe(true);
      expect(HierarchyUtils.descendantOf('acme', 'acme.engineering')).toBe(false);
    });

    test('应该正确识别直接父子关系', async () => {
      const { HierarchyUtils } = await import('@/services/organization-permission.service');
      
      expect(HierarchyUtils.immediateChildOf('acme.engineering', 'acme')).toBe(true);
      expect(HierarchyUtils.immediateChildOf('acme.engineering.backend', 'acme.engineering')).toBe(true);
      expect(HierarchyUtils.immediateChildOf('acme.engineering.backend', 'acme')).toBe(false);
    });

    test('应该正确识别兄弟关系', async () => {
      const { HierarchyUtils } = await import('@/services/organization-permission.service');
      
      expect(HierarchyUtils.siblingOf('acme.engineering.backend', 'acme.engineering.frontend')).toBe(true);
      expect(HierarchyUtils.siblingOf('acme.engineering', 'acme.sales')).toBe(true);
      expect(HierarchyUtils.siblingOf('acme.engineering.backend', 'acme.engineering')).toBe(false);
    });

    test('应该正确找到公共祖先', async () => {
      const { HierarchyUtils } = await import('@/services/organization-permission.service');
      
      const common1 = HierarchyUtils.commonAncestors(
        'acme.engineering.backend',
        'acme.engineering.frontend'
      );
      expect(common1).toBe('acme.engineering');

      const common2 = HierarchyUtils.commonAncestors(
        'acme.engineering.backend.auth',
        'acme.sales.north'
      );
      expect(common2).toBe('acme');
    });
  });
});
