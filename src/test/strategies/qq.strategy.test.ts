/**
 * QQ OAuth策略测试
 */

import { QQStrategy } from '@/strategies/qq.strategy';
import { config } from '@/config';

// Mock fetch
global.fetch = jest.fn();

// Mock config
jest.mock('@/config', () => ({
  config: {
    oauth: {
      qq: {
        clientId: 'test-qq-client-id',
        clientSecret: 'test-qq-client-secret',
        callbackUrl: 'http://localhost:3000/api/v1/auth/qq/callback'
      }
    }
  }
}));

// Mock logger
jest.mock('@/config/logger', () => ({
  logger: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn()
  }
}));

// Mock OAuth state service
jest.mock('@/services/oauth-state.service', () => ({
  OAuthStateService: jest.fn().mockImplementation(() => ({
    recordOAuthAttempt: jest.fn(),
    storeState: jest.fn(),
    validateAndConsumeState: jest.fn()
  }))
}));

describe('QQStrategy', () => {
  let strategy: QQStrategy;
  let mockVerify: jest.Mock;

  beforeEach(() => {
    mockVerify = jest.fn();
    strategy = new QQStrategy(mockVerify);
    jest.clearAllMocks();
  });

  describe('构造函数', () => {
    it('应该正确初始化QQ策略配置', () => {
      expect(strategy.name).toBe('qq');
      expect(strategy['config']).toEqual({
        clientId: 'test-qq-client-id',
        clientSecret: 'test-qq-client-secret',
        callbackUrl: 'http://localhost:3000/api/v1/auth/qq/callback',
        authorizationUrl: 'https://graph.qq.com/oauth2.0/authorize',
        tokenUrl: 'https://graph.qq.com/oauth2.0/token',
        userInfoUrl: 'https://graph.qq.com/user/get_user_info',
        scope: ['get_user_info']
      });
    });
  });

  describe('exchangeCodeForToken', () => {
    it('应该成功交换授权码获取访问令牌', async () => {
      const mockResponse = 'access_token=test-access-token&refresh_token=test-refresh-token&expires_in=7200';
      
      (fetch as jest.Mock).mockResolvedValueOnce({
        text: () => Promise.resolve(mockResponse)
      });

      const result = await strategy['exchangeCodeForToken']('test-code');

      expect(result).toEqual({
        access_token: 'test-access-token',
        refresh_token: 'test-refresh-token',
        expires_in: 7200
      });

      expect(fetch).toHaveBeenCalledWith(
        expect.stringContaining('https://graph.qq.com/oauth2.0/token')
      );
    });

    it('应该处理令牌交换错误', async () => {
      const mockResponse = 'error=invalid_grant&error_description=Invalid authorization code';
      
      (fetch as jest.Mock).mockResolvedValueOnce({
        text: () => Promise.resolve(mockResponse)
      });

      await expect(strategy['exchangeCodeForToken']('invalid-code'))
        .rejects.toThrow('QQ令牌交换失败');
    });

    it('应该处理网络错误', async () => {
      (fetch as jest.Mock).mockRejectedValueOnce(new Error('Network error'));

      await expect(strategy['exchangeCodeForToken']('test-code'))
        .rejects.toThrow('QQ令牌交换失败');
    });
  });

  describe('getOpenId', () => {
    it('应该成功获取QQ OpenID', async () => {
      const mockResponse = 'callback( {"client_id":"test-client-id","openid":"test-openid"} );';
      
      (fetch as jest.Mock).mockResolvedValueOnce({
        text: () => Promise.resolve(mockResponse)
      });

      const result = await strategy['getOpenId']('test-access-token');

      expect(result).toEqual({
        client_id: 'test-client-id',
        openid: 'test-openid'
      });

      expect(fetch).toHaveBeenCalledWith(
        'https://graph.qq.com/oauth2.0/me?access_token=test-access-token'
      );
    });

    it('应该处理无效的JSONP响应', async () => {
      const mockResponse = 'invalid response format';
      
      (fetch as jest.Mock).mockResolvedValueOnce({
        text: () => Promise.resolve(mockResponse)
      });

      await expect(strategy['getOpenId']('test-access-token'))
        .rejects.toThrow('获取QQ OpenID失败');
    });

    it('应该处理缺少openid的响应', async () => {
      const mockResponse = 'callback( {"client_id":"test-client-id"} );';
      
      (fetch as jest.Mock).mockResolvedValueOnce({
        text: () => Promise.resolve(mockResponse)
      });

      await expect(strategy['getOpenId']('test-access-token'))
        .rejects.toThrow('获取QQ OpenID失败');
    });
  });

  describe('getUserInfo', () => {
    it('应该成功获取QQ用户信息', async () => {
      // Mock getOpenId
      const mockOpenIdResponse = 'callback( {"client_id":"test-client-id","openid":"test-openid"} );';
      
      // Mock getUserInfo
      const mockUserInfo = {
        ret: 0,
        msg: 'success',
        nickname: '测试用户',
        figureurl_qq_2: 'https://example.com/avatar.jpg',
        gender: '男'
      };

      (fetch as jest.Mock)
        .mockResolvedValueOnce({
          text: () => Promise.resolve(mockOpenIdResponse)
        })
        .mockResolvedValueOnce({
          json: () => Promise.resolve(mockUserInfo)
        });

      const result = await strategy['getUserInfo']('test-access-token');

      expect(result).toEqual({
        id: 'test-openid',
        name: '测试用户',
        avatar: 'https://example.com/avatar.jpg'
      });
    });

    it('应该处理QQ API错误', async () => {
      const mockOpenIdResponse = 'callback( {"client_id":"test-client-id","openid":"test-openid"} );';
      const mockUserInfo = {
        ret: -1,
        msg: 'API error'
      };

      (fetch as jest.Mock)
        .mockResolvedValueOnce({
          text: () => Promise.resolve(mockOpenIdResponse)
        })
        .mockResolvedValueOnce({
          json: () => Promise.resolve(mockUserInfo)
        });

      await expect(strategy['getUserInfo']('test-access-token'))
        .rejects.toThrow('获取QQ用户信息失败');
    });

    it('应该选择最高质量的头像', async () => {
      const mockOpenIdResponse = 'callback( {"client_id":"test-client-id","openid":"test-openid"} );';
      const mockUserInfo = {
        ret: 0,
        msg: 'success',
        nickname: '测试用户',
        figureurl: 'https://example.com/avatar_small.jpg',
        figureurl_1: 'https://example.com/avatar_medium.jpg',
        figureurl_2: 'https://example.com/avatar_large.jpg',
        figureurl_qq_1: 'https://example.com/avatar_qq_medium.jpg',
        figureurl_qq_2: 'https://example.com/avatar_qq_large.jpg'
      };

      (fetch as jest.Mock)
        .mockResolvedValueOnce({
          text: () => Promise.resolve(mockOpenIdResponse)
        })
        .mockResolvedValueOnce({
          json: () => Promise.resolve(mockUserInfo)
        });

      const result = await strategy['getUserInfo']('test-access-token');

      // 应该选择最高质量的QQ头像
      expect(result.avatar).toBe('https://example.com/avatar_qq_large.jpg');
    });
  });

  describe('generateAuthorizationUrl', () => {
    it('应该生成正确的授权URL', () => {
      const state = 'test-state';
      const url = strategy['generateAuthorizationUrl'](state);

      expect(url).toContain('https://graph.qq.com/oauth2.0/authorize');
      expect(url).toContain('response_type=code');
      expect(url).toContain('client_id=test-qq-client-id');
      expect(url).toContain('redirect_uri=http%3A%2F%2Flocalhost%3A3000%2Fapi%2Fv1%2Fauth%2Fqq%2Fcallback');
      expect(url).toContain('scope=get_user_info');
      expect(url).toContain(`state=${state}`);
    });
  });

  describe('错误处理', () => {
    it('应该处理获取OpenID时的网络错误', async () => {
      (fetch as jest.Mock).mockRejectedValueOnce(new Error('Network error'));

      await expect(strategy['getOpenId']('test-access-token'))
        .rejects.toThrow('获取QQ OpenID失败');
    });

    it('应该处理获取用户信息时的网络错误', async () => {
      const mockOpenIdResponse = 'callback( {"client_id":"test-client-id","openid":"test-openid"} );';
      
      (fetch as jest.Mock)
        .mockResolvedValueOnce({
          text: () => Promise.resolve(mockOpenIdResponse)
        })
        .mockRejectedValueOnce(new Error('Network error'));

      await expect(strategy['getUserInfo']('test-access-token'))
        .rejects.toThrow('获取QQ用户信息失败');
    });
  });
});
