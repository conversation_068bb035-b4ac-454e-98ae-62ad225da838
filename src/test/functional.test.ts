/**
 * 功能验证测试
 * 测试身份提供商的核心业务逻辑功能
 */

import bcrypt from 'bcrypt';
import jwt from 'jsonwebtoken';
import { v4 as uuidv4 } from 'uuid';

describe('密码加密功能验证', () => {
  describe('bcrypt密码处理', () => {
    it('应该能够正确加密密码', async () => {
      const password = 'TestPassword123!';
      const saltRounds = 12;
      
      const hashedPassword = await bcrypt.hash(password, saltRounds);
      
      expect(hashedPassword).toBeDefined();
      expect(hashedPassword).not.toBe(password);
      expect(hashedPassword.length).toBeGreaterThan(50);
      expect(hashedPassword.startsWith('$2b$')).toBe(true);
    });

    it('应该能够验证正确的密码', async () => {
      const password = 'TestPassword123!';
      const hashedPassword = await bcrypt.hash(password, 12);
      
      const isValid = await bcrypt.compare(password, hashedPassword);
      expect(isValid).toBe(true);
    });

    it('应该拒绝错误的密码', async () => {
      const password = 'TestPassword123!';
      const wrongPassword = 'WrongPassword123!';
      const hashedPassword = await bcrypt.hash(password, 12);
      
      const isValid = await bcrypt.compare(wrongPassword, hashedPassword);
      expect(isValid).toBe(false);
    });
  });
});

describe('JWT令牌功能验证', () => {
  const JWT_SECRET = 'test-jwt-secret-key-for-testing';
  
  describe('JWT令牌生成和验证', () => {
    it('应该能够生成有效的JWT令牌', () => {
      const payload = {
        userId: 'test-user-123',
        email: '<EMAIL>',
        sessionId: 'session-123'
      };

      const token = jwt.sign(payload, JWT_SECRET, { expiresIn: '1h' });
      
      expect(token).toBeDefined();
      expect(typeof token).toBe('string');
      expect(token.split('.').length).toBe(3); // JWT有三个部分
    });

    it('应该能够验证和解码有效的JWT令牌', () => {
      const payload = {
        userId: 'test-user-123',
        email: '<EMAIL>',
        sessionId: 'session-123'
      };

      const token = jwt.sign(payload, JWT_SECRET, { expiresIn: '1h' });
      const decoded = jwt.verify(token, JWT_SECRET) as any;
      
      expect(decoded).toHaveProperty('userId');
      expect(decoded).toHaveProperty('email');
      expect(decoded).toHaveProperty('sessionId');
      expect(decoded.userId).toBe(payload.userId);
      expect(decoded.email).toBe(payload.email);
      expect(decoded.sessionId).toBe(payload.sessionId);
    });

    it('应该拒绝无效的JWT令牌', () => {
      const invalidToken = 'invalid.jwt.token';
      
      expect(() => {
        jwt.verify(invalidToken, JWT_SECRET);
      }).toThrow();
    });

    it('应该拒绝使用错误密钥签名的令牌', () => {
      const payload = { userId: 'test-user-123' };
      const token = jwt.sign(payload, 'wrong-secret', { expiresIn: '1h' });
      
      expect(() => {
        jwt.verify(token, JWT_SECRET);
      }).toThrow();
    });
  });
});

describe('数据验证功能验证', () => {
  describe('邮箱格式验证', () => {
    const validateEmail = (email: string): boolean => {
      // 基本格式检查
      if (!email || typeof email !== 'string') return false;
      if (email.length > 254) return false; // RFC 5321 限制
      if (email.includes('..')) return false; // 连续点号
      if (email.startsWith('.') || email.endsWith('.')) return false; // 开头或结尾是点号
      if (email.includes('@.') || email.includes('.@')) return false; // @前后直接是点号

      // 必须包含@符号，且只能有一个
      const atCount = (email.match(/@/g) || []).length;
      if (atCount !== 1) return false;

      const [localPart, domainPart] = email.split('@');

      // 本地部分检查
      if (!localPart || localPart.length === 0 || localPart.length > 64) return false;

      // 域名部分检查
      if (!domainPart || domainPart.length === 0 || domainPart.length > 253) return false;

      // 域名必须包含至少一个点号
      if (!domainPart.includes('.')) return false;

      // 域名不能以点号开头或结尾
      if (domainPart.startsWith('.') || domainPart.endsWith('.')) return false;

      // 更严格的邮箱验证正则表达式
      const emailRegex = /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+$/;

      return emailRegex.test(email);
    };

    it('应该接受有效的邮箱地址', () => {
      const validEmails = [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>'
      ];

      validEmails.forEach(email => {
        expect(validateEmail(email)).toBe(true);
      });
    });

    it('应该拒绝无效的邮箱地址', () => {
      const invalidEmails = [
        'invalid-email',
        '@domain.com',
        'user@',
        '<EMAIL>',
        'user@domain',
        '',
        'user@.com',
        'user@domain.',
        'user <EMAIL>'
      ];

      invalidEmails.forEach(email => {
        const result = validateEmail(email);
        if (result !== false) {
          console.log(`意外通过验证的邮箱: "${email}"`);
        }
        expect(result).toBe(false);
      });
    });
  });

  describe('密码强度验证', () => {
    const validatePasswordStrength = (password: string) => {
      const minLength = 8;
      const hasUpperCase = /[A-Z]/.test(password);
      const hasLowerCase = /[a-z]/.test(password);
      const hasNumbers = /\d/.test(password);
      const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password);
      
      const errors = [];
      
      if (password.length < minLength) {
        errors.push('密码长度至少8位');
      }
      if (!hasUpperCase) {
        errors.push('密码必须包含大写字母');
      }
      if (!hasLowerCase) {
        errors.push('密码必须包含小写字母');
      }
      if (!hasNumbers) {
        errors.push('密码必须包含数字');
      }
      if (!hasSpecialChar) {
        errors.push('密码必须包含特殊字符');
      }
      
      return {
        isValid: errors.length === 0,
        errors,
        score: [hasUpperCase, hasLowerCase, hasNumbers, hasSpecialChar, password.length >= minLength].filter(Boolean).length
      };
    };

    it('应该接受强密码', () => {
      const strongPasswords = [
        'StrongPassword123!',
        'MySecure@Pass2024',
        'Complex#Password99',
        'Test123!@#'
      ];

      strongPasswords.forEach(password => {
        const result = validatePasswordStrength(password);
        expect(result.isValid).toBe(true);
        expect(result.errors).toHaveLength(0);
        expect(result.score).toBe(5);
      });
    });

    it('应该拒绝弱密码', () => {
      const weakPasswords = [
        '123456',        // 太短，缺少字母和特殊字符
        'password',      // 缺少数字、大写字母和特殊字符
        'Password123',   // 缺少特殊字符
        'password123!',  // 缺少大写字母
        'PASSWORD123!'   // 缺少小写字母
      ];

      weakPasswords.forEach(password => {
        const result = validatePasswordStrength(password);
        expect(result.isValid).toBe(false);
        expect(result.errors.length).toBeGreaterThan(0);
        expect(result.score).toBeLessThan(5);
      });
    });
  });
});

describe('工具函数功能验证', () => {
  describe('UUID生成', () => {
    it('应该生成符合UUID v4格式的唯一标识符', () => {
      const uuid1 = uuidv4();
      const uuid2 = uuidv4();
      
      expect(uuid1).toBeDefined();
      expect(uuid2).toBeDefined();
      expect(uuid1).not.toBe(uuid2);
      
      // 验证UUID v4格式
      const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
      expect(uuid1).toMatch(uuidRegex);
      expect(uuid2).toMatch(uuidRegex);
    });

    it('应该生成大量唯一的UUID', () => {
      const uuids = new Set();
      const count = 1000;
      
      for (let i = 0; i < count; i++) {
        uuids.add(uuidv4());
      }
      
      expect(uuids.size).toBe(count); // 所有UUID都应该是唯一的
    });
  });

  describe('时间处理', () => {
    it('应该能够正确计算过期时间', () => {
      const now = new Date();
      const expiresIn30Minutes = new Date(now.getTime() + 30 * 60 * 1000);
      const expiresIn30Days = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000);
      
      expect(expiresIn30Minutes.getTime()).toBeGreaterThan(now.getTime());
      expect(expiresIn30Days.getTime()).toBeGreaterThan(expiresIn30Minutes.getTime());
      
      // 验证时间差（允许1秒的误差）
      const diffMinutes = (expiresIn30Minutes.getTime() - now.getTime()) / (1000 * 60);
      const diffDays = (expiresIn30Days.getTime() - now.getTime()) / (1000 * 60 * 60 * 24);
      
      expect(Math.abs(diffMinutes - 30)).toBeLessThan(0.1);
      expect(Math.abs(diffDays - 30)).toBeLessThan(0.1);
    });

    it('应该能够检查令牌是否过期', () => {
      const now = new Date();
      const pastTime = new Date(now.getTime() - 60 * 1000); // 1分钟前
      const futureTime = new Date(now.getTime() + 60 * 1000); // 1分钟后
      
      expect(pastTime.getTime()).toBeLessThan(now.getTime());
      expect(futureTime.getTime()).toBeGreaterThan(now.getTime());
      
      // 模拟令牌过期检查
      const isExpired = (expiresAt: Date) => expiresAt.getTime() < now.getTime();
      
      expect(isExpired(pastTime)).toBe(true);
      expect(isExpired(futureTime)).toBe(false);
    });
  });
});
