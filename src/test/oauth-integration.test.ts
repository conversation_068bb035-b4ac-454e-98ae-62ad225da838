/**
 * OAuth集成测试
 * 测试完整的OAuth登录流程
 */

import request from 'supertest';
import { app } from '../index';
import { prisma } from '../config/database';
import { generateTokenPair } from '../utils/jwt';

describe('OAuth集成测试', () => {
  let testUser: any;
  let authToken: string;

  beforeAll(async () => {
    // 创建测试用户
    testUser = await prisma.user.create({
      data: {
        id: 'oauth-integration-user',
        email: '<EMAIL>',
        nickname: 'Integration User',
        hashedPassword: 'hashed-password',
        isEmailVerified: true
      }
    });

    // 生成认证令牌
    const tokens = generateTokenPair({
      userId: testUser.id,
      email: testUser.email,
      sessionId: 'test-session'
    });
    authToken = tokens.accessToken;
  });

  afterAll(async () => {
    // 清理测试数据
    await prisma.federatedIdentity.deleteMany({ where: { userId: testUser.id } });
    await prisma.session.deleteMany({ where: { userId: testUser.id } });
    await prisma.user.delete({ where: { id: testUser.id } });
    await prisma.$disconnect();
  });

  describe('完整的OAuth流程', () => {
    it('应该能够获取支持的OAuth提供商', async () => {
      const response = await request(app)
        .get('/api/v1/auth/providers')
        .expect(200);

      expect(response.body).toHaveProperty('providers');
      expect(response.body).toHaveProperty('total');
      expect(Array.isArray(response.body.providers)).toBe(true);
    });

    it('应该能够发起OAuth登录（重定向）', async () => {
      const providers = ['google', 'github', 'wechat', 'weibo'];
      
      for (const provider of providers) {
        const response = await request(app)
          .get(`/api/v1/auth/${provider}`)
          .expect(302);

        expect(response.headers.location).toBeDefined();
      }
    });

    it('应该能够处理OAuth错误回调', async () => {
      const response = await request(app)
        .get('/api/v1/auth/google/callback?error=access_denied&error_description=User%20denied%20access')
        .expect(302);

      expect(response.headers.location).toContain('error=oauth_error');
    });
  });

  describe('联合身份管理', () => {
    let federatedIdentityId: string;

    beforeEach(async () => {
      // 创建测试联合身份
      const federatedIdentity = await prisma.federatedIdentity.create({
        data: {
          id: 'test-fed-identity',
          userId: testUser.id,
          provider: 'google',
          providerId: 'google-integration-123',
          email: testUser.email,
          name: 'Integration User',
          avatar: 'https://example.com/avatar.jpg'
        }
      });
      federatedIdentityId = federatedIdentity.id;
    });

    afterEach(async () => {
      // 清理联合身份
      await prisma.federatedIdentity.deleteMany({ 
        where: { id: federatedIdentityId } 
      });
    });

    it('应该能够获取用户的联合身份列表', async () => {
      const response = await request(app)
        .get('/api/v1/me/connections')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('connections');
      expect(response.body).toHaveProperty('stats');
      expect(response.body.connections).toHaveLength(1);
      expect(response.body.connections[0].provider).toBe('google');
      expect(response.body.stats.activeConnections).toBe(1);
    });

    it('应该能够解除联合身份关联', async () => {
      const response = await request(app)
        .delete(`/api/v1/me/connections/${federatedIdentityId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.message).toContain('联合身份关联已解除');
      expect(response.body.connectionId).toBe(federatedIdentityId);

      // 验证联合身份已被标记为非活跃
      const federatedIdentity = await prisma.federatedIdentity.findUnique({
        where: { id: federatedIdentityId }
      });
      expect(federatedIdentity?.isActive).toBe(false);
    });

    it('未认证用户不能访问联合身份管理', async () => {
      await request(app)
        .get('/api/v1/me/connections')
        .expect(401);

      await request(app)
        .delete(`/api/v1/me/connections/${federatedIdentityId}`)
        .expect(401);
    });

    it('不能解除不存在的联合身份', async () => {
      const response = await request(app)
        .delete('/api/v1/me/connections/non-existent-id')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(500);

      expect(response.body.error).toBe('disconnect_federated_identity_failed');
    });
  });

  describe('OAuth提供商解除关联', () => {
    let federatedIdentityId: string;

    beforeEach(async () => {
      // 创建测试联合身份
      const federatedIdentity = await prisma.federatedIdentity.create({
        data: {
          id: 'test-provider-disconnect',
          userId: testUser.id,
          provider: 'github',
          providerId: 'github-integration-456',
          email: testUser.email,
          name: 'Integration User'
        }
      });
      federatedIdentityId = federatedIdentity.id;
    });

    afterEach(async () => {
      // 清理联合身份
      await prisma.federatedIdentity.deleteMany({ 
        where: { id: federatedIdentityId } 
      });
    });

    it('应该能够通过提供商名称解除关联', async () => {
      const response = await request(app)
        .delete('/api/v1/auth/disconnect/github')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.message).toContain('账户关联已解除');
      expect(response.body.provider).toBe('github');
    });

    it('不能解除不存在的提供商关联', async () => {
      const response = await request(app)
        .delete('/api/v1/auth/disconnect/nonexistent')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(404);

      expect(response.body.error).toBe('connection_not_found');
    });
  });

  describe('安全性测试', () => {
    it('应该有适当的速率限制', async () => {
      // 测试OAuth端点的速率限制
      const promises = Array(25).fill(null).map(() => 
        request(app).get('/api/v1/auth/providers')
      );

      const responses = await Promise.all(promises);
      const rateLimitedResponses = responses.filter(r => r.status === 429);
      
      // 应该有一些请求被速率限制
      expect(rateLimitedResponses.length).toBeGreaterThan(0);
    });

    it('应该验证Authorization header格式', async () => {
      await request(app)
        .get('/api/v1/me/connections')
        .set('Authorization', 'InvalidFormat')
        .expect(401);

      await request(app)
        .get('/api/v1/me/connections')
        .set('Authorization', 'Bearer invalid-token')
        .expect(401);
    });
  });

  describe('错误处理', () => {
    it('应该正确处理数据库连接错误', async () => {
      // 这个测试需要模拟数据库错误，实际实现中可能需要使用mock
      // 这里只是示例结构
    });

    it('应该正确处理无效的OAuth回调参数', async () => {
      const response = await request(app)
        .get('/api/v1/auth/google/callback?code=invalid-code')
        .expect(302);

      expect(response.headers.location).toContain('error=oauth_callback_error');
    });
  });
});
