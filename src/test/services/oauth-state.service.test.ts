/**
 * OAuth状态管理服务测试
 */

import { OAuthStateService } from '@/services/oauth-state.service';
import { OAuthState } from '@/utils/oauth-security';

// Mock Redis
const mockRedis = {
  setex: jest.fn(),
  get: jest.fn(),
  del: jest.fn(),
  keys: jest.fn(),
  incr: jest.fn(),
  expire: jest.fn()
};

// Mock Redis service
jest.mock('@/services/redis.service', () => ({
  redisService: {
    getClient: () => mockRedis
  }
}));

// Mock logger
jest.mock('@/config/logger', () => ({
  logger: {
    debug: jest.fn(),
    info: jest.fn(),
    warn: jest.fn(),
    error: jest.fn()
  }
}));

// Mock OAuth security
jest.mock('@/utils/oauth-security', () => ({
  OAuthSecurity: {
    validateState: jest.fn(),
    logSecurityEvent: jest.fn()
  }
}));

describe('OAuthStateService', () => {
  let service: OAuthStateService;

  beforeEach(() => {
    service = new OAuthStateService();
    jest.clearAllMocks();
  });

  describe('storeState', () => {
    it('应该成功存储OAuth状态', async () => {
      const state: OAuthState = {
        state: 'test-state',
        codeVerifier: 'test-verifier',
        timestamp: Date.now(),
        provider: 'google'
      };

      mockRedis.setex.mockResolvedValueOnce('OK');

      await service.storeState(state);

      expect(mockRedis.setex).toHaveBeenCalledWith(
        'oauth:state:test-state',
        600,
        JSON.stringify(state)
      );
    });

    it('应该处理存储失败', async () => {
      const state: OAuthState = {
        state: 'test-state',
        codeVerifier: 'test-verifier',
        timestamp: Date.now(),
        provider: 'google'
      };

      mockRedis.setex.mockRejectedValueOnce(new Error('Redis error'));

      await expect(service.storeState(state)).rejects.toThrow('存储OAuth状态失败');
    });
  });

  describe('getState', () => {
    it('应该成功获取OAuth状态', async () => {
      const state: OAuthState = {
        state: 'test-state',
        codeVerifier: 'test-verifier',
        timestamp: Date.now(),
        provider: 'google'
      };

      mockRedis.get.mockResolvedValueOnce(JSON.stringify(state));

      const result = await service.getState('test-state');

      expect(result).toEqual(state);
      expect(mockRedis.get).toHaveBeenCalledWith('oauth:state:test-state');
    });

    it('应该处理状态不存在的情况', async () => {
      mockRedis.get.mockResolvedValueOnce(null);

      const result = await service.getState('non-existent-state');

      expect(result).toBeNull();
    });

    it('应该处理JSON解析错误', async () => {
      mockRedis.get.mockResolvedValueOnce('invalid-json');

      const result = await service.getState('test-state');

      expect(result).toBeNull();
    });

    it('应该处理Redis错误', async () => {
      mockRedis.get.mockRejectedValueOnce(new Error('Redis error'));

      const result = await service.getState('test-state');

      expect(result).toBeNull();
    });
  });

  describe('deleteState', () => {
    it('应该成功删除OAuth状态', async () => {
      mockRedis.del.mockResolvedValueOnce(1);

      await service.deleteState('test-state');

      expect(mockRedis.del).toHaveBeenCalledWith('oauth:state:test-state');
    });

    it('应该处理删除失败', async () => {
      mockRedis.del.mockRejectedValueOnce(new Error('Redis error'));

      // 不应该抛出错误，只记录日志
      await expect(service.deleteState('test-state')).resolves.not.toThrow();
    });
  });

  describe('validateAndConsumeState', () => {
    const { OAuthSecurity } = require('@/utils/oauth-security');

    it('应该验证并消费有效状态', async () => {
      const state: OAuthState = {
        state: 'test-state',
        codeVerifier: 'test-verifier',
        timestamp: Date.now(),
        provider: 'google'
      };

      mockRedis.get.mockResolvedValueOnce(JSON.stringify(state));
      mockRedis.del.mockResolvedValueOnce(1);
      OAuthSecurity.validateState.mockReturnValueOnce({ isValid: true });

      const result = await service.validateAndConsumeState('test-state');

      expect(result.isValid).toBe(true);
      expect(result.state).toEqual(state);
      expect(mockRedis.del).toHaveBeenCalledWith('oauth:state:test-state');
    });

    it('应该处理状态不存在', async () => {
      mockRedis.get.mockResolvedValueOnce(null);

      const result = await service.validateAndConsumeState('non-existent-state');

      expect(result.isValid).toBe(false);
      expect(result.reason).toBe('state_not_found');
      expect(OAuthSecurity.logSecurityEvent).toHaveBeenCalledWith(
        'state_not_found',
        { receivedState: 'non-existent-state' },
        'high'
      );
    });

    it('应该处理状态验证失败', async () => {
      const state: OAuthState = {
        state: 'test-state',
        codeVerifier: 'test-verifier',
        timestamp: Date.now(),
        provider: 'google'
      };

      mockRedis.get.mockResolvedValueOnce(JSON.stringify(state));
      mockRedis.del.mockResolvedValueOnce(1);
      OAuthSecurity.validateState.mockReturnValueOnce({ 
        isValid: false, 
        reason: 'state_expired' 
      });

      const result = await service.validateAndConsumeState('test-state');

      expect(result.isValid).toBe(false);
      expect(result.reason).toBe('state_expired');
      expect(mockRedis.del).toHaveBeenCalledWith('oauth:state:test-state');
    });
  });

  describe('cleanupExpiredStates', () => {
    it('应该清理过期状态', async () => {
      const expiredState: OAuthState = {
        state: 'expired-state',
        codeVerifier: 'verifier',
        timestamp: Date.now() - 11 * 60 * 1000, // 11分钟前
        provider: 'google'
      };

      const validState: OAuthState = {
        state: 'valid-state',
        codeVerifier: 'verifier',
        timestamp: Date.now(),
        provider: 'github'
      };

      mockRedis.keys.mockResolvedValueOnce([
        'oauth:state:expired-state',
        'oauth:state:valid-state'
      ]);

      mockRedis.get
        .mockResolvedValueOnce(JSON.stringify(expiredState))
        .mockResolvedValueOnce(JSON.stringify(validState));

      mockRedis.del.mockResolvedValueOnce(1);

      const deletedCount = await service.cleanupExpiredStates();

      expect(deletedCount).toBe(1);
      expect(mockRedis.del).toHaveBeenCalledWith('oauth:state:expired-state');
    });

    it('应该处理无效JSON数据', async () => {
      mockRedis.keys.mockResolvedValueOnce(['oauth:state:invalid-json']);
      mockRedis.get.mockResolvedValueOnce('invalid-json');
      mockRedis.del.mockResolvedValueOnce(1);

      const deletedCount = await service.cleanupExpiredStates();

      expect(deletedCount).toBe(1);
      expect(mockRedis.del).toHaveBeenCalledWith('oauth:state:invalid-json');
    });
  });

  describe('getStateStats', () => {
    it('应该返回状态统计信息', async () => {
      const googleState: OAuthState = {
        state: 'google-state',
        codeVerifier: 'verifier',
        timestamp: Date.now(),
        provider: 'google'
      };

      const githubState: OAuthState = {
        state: 'github-state',
        codeVerifier: 'verifier',
        timestamp: Date.now(),
        provider: 'github'
      };

      mockRedis.keys.mockResolvedValueOnce([
        'oauth:state:google-state',
        'oauth:state:github-state'
      ]);

      mockRedis.get
        .mockResolvedValueOnce(JSON.stringify(googleState))
        .mockResolvedValueOnce(JSON.stringify(githubState));

      const stats = await service.getStateStats();

      expect(stats).toEqual({
        totalStates: 2,
        statesByProvider: {
          google: 1,
          github: 1
        }
      });
    });

    it('应该处理Redis错误', async () => {
      mockRedis.keys.mockRejectedValueOnce(new Error('Redis error'));

      const stats = await service.getStateStats();

      expect(stats).toEqual({
        totalStates: 0,
        statesByProvider: {}
      });
    });
  });

  describe('PKCE代码验证器管理', () => {
    it('应该存储PKCE代码验证器', async () => {
      mockRedis.setex.mockResolvedValueOnce('OK');

      await service.storeCodeVerifier('test-state', 'test-verifier');

      expect(mockRedis.setex).toHaveBeenCalledWith(
        'oauth:pkce:test-state',
        600,
        'test-verifier'
      );
    });

    it('应该获取并删除PKCE代码验证器', async () => {
      mockRedis.get.mockResolvedValueOnce('test-verifier');
      mockRedis.del.mockResolvedValueOnce(1);

      const verifier = await service.getAndDeleteCodeVerifier('test-state');

      expect(verifier).toBe('test-verifier');
      expect(mockRedis.get).toHaveBeenCalledWith('oauth:pkce:test-state');
      expect(mockRedis.del).toHaveBeenCalledWith('oauth:pkce:test-state');
    });

    it('应该处理不存在的代码验证器', async () => {
      mockRedis.get.mockResolvedValueOnce(null);

      const verifier = await service.getAndDeleteCodeVerifier('non-existent-state');

      expect(verifier).toBeNull();
    });
  });

  describe('recordOAuthAttempt', () => {
    it('应该记录OAuth尝试', async () => {
      mockRedis.incr.mockResolvedValueOnce(1);
      mockRedis.expire.mockResolvedValueOnce(1);
      mockRedis.setex.mockResolvedValueOnce('OK');

      await service.recordOAuthAttempt('google', '***********', 'Mozilla/5.0');

      expect(mockRedis.incr).toHaveBeenCalledWith('oauth:attempts:google:***********');
      expect(mockRedis.expire).toHaveBeenCalledWith('oauth:attempts:google:***********', 3600);
    });

    it('应该记录过多尝试的安全事件', async () => {
      const { OAuthSecurity } = require('@/utils/oauth-security');
      
      mockRedis.incr.mockResolvedValueOnce(15); // 超过10次
      mockRedis.setex.mockResolvedValueOnce('OK');

      await service.recordOAuthAttempt('google', '***********', 'Mozilla/5.0');

      expect(OAuthSecurity.logSecurityEvent).toHaveBeenCalledWith(
        'excessive_oauth_attempts',
        {
          provider: 'google',
          ipAddress: '***********',
          attempts: 15
        },
        'high'
      );
    });
  });
});
