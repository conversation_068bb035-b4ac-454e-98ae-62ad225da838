/**
 * OAuth性能测试
 * 测试OAuth功能的性能表现
 */

import { OAuthService } from '../services/oauth.service';
import { FederatedIdentityService } from '../services/federated-identity.service';
import { prisma } from '../config/database';

describe('OAuth性能测试', () => {
  let oauthService: OAuthService;
  let federatedIdentityService: FederatedIdentityService;
  let testUsers: any[] = [];

  beforeAll(async () => {
    oauthService = new OAuthService();
    federatedIdentityService = new FederatedIdentityService();

    // 创建多个测试用户用于性能测试
    const userPromises = Array(100).fill(null).map((_, index) => 
      prisma.user.create({
        data: {
          id: `perf-user-${index}`,
          email: `perfuser${index}@example.com`,
          nickname: `Perf User ${index}`,
          hashedPassword: 'hashed-password',
          isEmailVerified: true
        }
      })
    );

    testUsers = await Promise.all(userPromises);
  });

  afterAll(async () => {
    // 清理测试数据
    await prisma.federatedIdentity.deleteMany({
      where: {
        userId: { in: testUsers.map(u => u.id) }
      }
    });
    
    await prisma.user.deleteMany({
      where: {
        id: { in: testUsers.map(u => u.id) }
      }
    });
    
    await prisma.$disconnect();
  });

  describe('OAuth登录性能', () => {
    it('应该能够快速处理多个OAuth登录请求', async () => {
      const startTime = Date.now();
      
      const loginPromises = testUsers.slice(0, 10).map((user, index) => 
        oauthService.handleOAuthLogin({
          id: `google-perf-${index}`,
          provider: 'google',
          email: user.email,
          name: user.nickname,
          avatar: 'https://example.com/avatar.jpg'
        }, '127.0.0.1', 'test-agent')
      );

      const results = await Promise.all(loginPromises);
      const endTime = Date.now();
      const duration = endTime - startTime;

      // 10个OAuth登录应该在2秒内完成
      expect(duration).toBeLessThan(2000);
      expect(results).toHaveLength(10);
      
      // 验证所有登录都成功
      results.forEach(result => {
        expect(result.user).toBeDefined();
        expect(result.tokens.accessToken).toBeDefined();
      });

      // 清理创建的联合身份
      await prisma.federatedIdentity.deleteMany({
        where: {
          providerId: { in: results.map((_, index) => `google-perf-${index}`) }
        }
      });
    });

    it('应该能够高效处理重复的OAuth登录', async () => {
      const user = testUsers[0];
      const profile = {
        id: 'google-repeat-test',
        provider: 'google',
        email: user.email,
        name: user.nickname,
        avatar: 'https://example.com/avatar.jpg'
      };

      // 首次登录
      await oauthService.handleOAuthLogin(profile, '127.0.0.1', 'test-agent');

      // 测试重复登录的性能
      const startTime = Date.now();
      const repeatPromises = Array(5).fill(null).map(() => 
        oauthService.handleOAuthLogin(profile, '127.0.0.1', 'test-agent')
      );

      const results = await Promise.all(repeatPromises);
      const endTime = Date.now();
      const duration = endTime - startTime;

      // 5次重复登录应该在1秒内完成
      expect(duration).toBeLessThan(1000);
      expect(results).toHaveLength(5);

      // 清理
      await prisma.federatedIdentity.deleteMany({
        where: { providerId: 'google-repeat-test' }
      });
    });
  });

  describe('联合身份查询性能', () => {
    beforeAll(async () => {
      // 为每个用户创建多个联合身份
      const federatedIdentityPromises: any[] = [];
      
      testUsers.slice(0, 20).forEach((user, userIndex) => {
        ['google', 'github', 'wechat', 'weibo'].forEach((provider, providerIndex) => {
          federatedIdentityPromises.push(
            prisma.federatedIdentity.create({
              data: {
                id: `perf-fed-${userIndex}-${providerIndex}`,
                userId: user.id,
                provider,
                providerId: `${provider}-perf-${userIndex}-${providerIndex}`,
                email: user.email,
                name: user.nickname
              }
            })
          );
        });
      });

      await Promise.all(federatedIdentityPromises);
    });

    it('应该能够快速获取用户联合身份列表', async () => {
      const startTime = Date.now();
      
      const queryPromises = testUsers.slice(0, 20).map(user => 
        federatedIdentityService.getUserFederatedIdentities(user.id)
      );

      const results = await Promise.all(queryPromises);
      const endTime = Date.now();
      const duration = endTime - startTime;

      // 20个用户的联合身份查询应该在500ms内完成
      expect(duration).toBeLessThan(500);
      expect(results).toHaveLength(20);
      
      // 验证每个用户都有4个联合身份
      results.forEach(identities => {
        expect(identities).toHaveLength(4);
      });
    });

    it('应该能够快速获取联合身份统计信息', async () => {
      const startTime = Date.now();
      
      const statsPromises = testUsers.slice(0, 20).map(user => 
        federatedIdentityService.getFederatedIdentityStats(user.id)
      );

      const results = await Promise.all(statsPromises);
      const endTime = Date.now();
      const duration = endTime - startTime;

      // 20个用户的统计查询应该在300ms内完成
      expect(duration).toBeLessThan(300);
      expect(results).toHaveLength(20);
      
      // 验证统计信息正确
      results.forEach(stats => {
        expect(stats.totalConnections).toBe(4);
        expect(stats.activeConnections).toBe(4);
        expect(Object.keys(stats.providerCounts)).toHaveLength(4);
      });
    });
  });

  describe('并发操作性能', () => {
    it('应该能够处理并发的联合身份解除操作', async () => {
      // 创建测试联合身份
      const user = testUsers[0];
      const federatedIdentities = await Promise.all([
        prisma.federatedIdentity.create({
          data: {
            id: 'concurrent-test-1',
            userId: user.id,
            provider: 'google',
            providerId: 'google-concurrent-1',
            email: user.email,
            name: user.nickname
          }
        }),
        prisma.federatedIdentity.create({
          data: {
            id: 'concurrent-test-2',
            userId: user.id,
            provider: 'github',
            providerId: 'github-concurrent-2',
            email: user.email,
            name: user.nickname
          }
        })
      ]);

      const startTime = Date.now();
      
      // 并发解除关联（这应该失败，因为用户只有这两个登录方式且没有密码）
      const disconnectPromises = federatedIdentities.map(fi => 
        federatedIdentityService.canDisconnectProvider(user.id, fi.id)
      );

      const results = await Promise.all(disconnectPromises);
      const endTime = Date.now();
      const duration = endTime - startTime;

      // 并发检查应该在100ms内完成
      expect(duration).toBeLessThan(100);
      expect(results).toHaveLength(2);

      // 清理
      await prisma.federatedIdentity.deleteMany({
        where: { id: { in: federatedIdentities.map(fi => fi.id) } }
      });
    });
  });

  describe('内存使用测试', () => {
    it('应该在处理大量OAuth操作时保持合理的内存使用', async () => {
      const initialMemory = process.memoryUsage();
      
      // 执行大量OAuth操作
      const operations = [];
      for (let i = 0; i < 50; i++) {
        const user = testUsers[i % testUsers.length];
        operations.push(
          federatedIdentityService.getUserFederatedIdentities(user.id)
        );
      }

      await Promise.all(operations);
      
      const finalMemory = process.memoryUsage();
      const memoryIncrease = finalMemory.heapUsed - initialMemory.heapUsed;
      
      // 内存增长应该小于50MB
      expect(memoryIncrease).toBeLessThan(50 * 1024 * 1024);
    });
  });
});
