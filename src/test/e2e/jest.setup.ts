/**
 * Jest E2E测试设置文件
 * 配置E2E测试的全局设置和钩子
 */

import { config } from 'dotenv';
import path from 'path';

// 加载测试环境变量
config({ path: path.resolve(__dirname, '../../../.env.test') });

// 设置测试超时时间
jest.setTimeout(60000);

// 全局测试钩子
beforeAll(async () => {
  console.log('🚀 开始E2E测试套件');
  
  // 确保测试环境变量正确设置
  process.env.NODE_ENV = 'test';
  process.env.E2E_TEST_MODE = 'true';
  
  // 设置默认的E2E测试配置
  if (!process.env.E2E_BASE_URL) {
    process.env.E2E_BASE_URL = 'http://localhost:3001';
  }
  
  if (!process.env.E2E_HEADLESS) {
    process.env.E2E_HEADLESS = 'true';
  }
  
  if (!process.env.E2E_TIMEOUT) {
    process.env.E2E_TIMEOUT = '30000';
  }
  
  // 创建测试结果目录
  const fs = require('fs');
  const testResultsDir = path.resolve(__dirname, '../../../test-results');
  const screenshotsDir = path.resolve(testResultsDir, 'screenshots');
  const videosDir = path.resolve(testResultsDir, 'videos');
  
  if (!fs.existsSync(testResultsDir)) {
    fs.mkdirSync(testResultsDir, { recursive: true });
  }
  
  if (!fs.existsSync(screenshotsDir)) {
    fs.mkdirSync(screenshotsDir, { recursive: true });
  }
  
  if (!fs.existsSync(videosDir)) {
    fs.mkdirSync(videosDir, { recursive: true });
  }
  
  console.log('📁 测试结果目录已创建');
});

afterAll(async () => {
  console.log('✅ E2E测试套件完成');
});

// 全局错误处理
process.on('unhandledRejection', (reason, promise) => {
  console.error('未处理的Promise拒绝:', reason);
  console.error('Promise:', promise);
});

process.on('uncaughtException', (error) => {
  console.error('未捕获的异常:', error);
});

// 扩展Jest匹配器
expect.extend({
  /**
   * 检查URL是否匹配指定模式
   */
  toMatchUrl(received: string, expected: string | RegExp) {
    const pass = typeof expected === 'string' 
      ? received.includes(expected)
      : expected.test(received);
    
    if (pass) {
      return {
        message: () => `期望 ${received} 不匹配 ${expected}`,
        pass: true,
      };
    } else {
      return {
        message: () => `期望 ${received} 匹配 ${expected}`,
        pass: false,
      };
    }
  },

  /**
   * 检查元素是否在指定时间内可见
   */
  async toBeVisibleWithin(received: any, timeout: number = 5000) {
    try {
      await received.waitFor({ state: 'visible', timeout });
      return {
        message: () => `元素在 ${timeout}ms 内变为可见`,
        pass: true,
      };
    } catch (error) {
      return {
        message: () => `元素在 ${timeout}ms 内未变为可见: ${error}`,
        pass: false,
      };
    }
  },

  /**
   * 检查页面是否在指定时间内加载完成
   */
  async toLoadWithin(received: any, timeout: number = 10000) {
    try {
      await received.waitForLoadState('networkidle', { timeout });
      return {
        message: () => `页面在 ${timeout}ms 内加载完成`,
        pass: true,
      };
    } catch (error) {
      return {
        message: () => `页面在 ${timeout}ms 内未加载完成: ${error}`,
        pass: false,
      };
    }
  },

  /**
   * 检查响应时间是否在可接受范围内
   */
  toBeFasterThan(received: number, expected: number) {
    const pass = received < expected;
    
    if (pass) {
      return {
        message: () => `期望 ${received}ms 不快于 ${expected}ms`,
        pass: true,
      };
    } else {
      return {
        message: () => `期望 ${received}ms 快于 ${expected}ms`,
        pass: false,
      };
    }
  },

  /**
   * 检查文本是否包含中文字符
   */
  toContainChinese(received: string) {
    const chineseRegex = /[\u4e00-\u9fff]/;
    const pass = chineseRegex.test(received);
    
    if (pass) {
      return {
        message: () => `期望 "${received}" 不包含中文字符`,
        pass: true,
      };
    } else {
      return {
        message: () => `期望 "${received}" 包含中文字符`,
        pass: false,
      };
    }
  }
});

// 声明自定义匹配器的类型
declare global {
  namespace jest {
    interface Matchers<R> {
      toMatchUrl(expected: string | RegExp): R;
      toBeVisibleWithin(timeout?: number): Promise<R>;
      toLoadWithin(timeout?: number): Promise<R>;
      toBeFasterThan(expected: number): R;
      toContainChinese(): R;
    }
  }
}

// 测试工具函数
export const testUtils = {
  /**
   * 等待指定时间
   */
  wait: (ms: number): Promise<void> => {
    return new Promise(resolve => setTimeout(resolve, ms));
  },

  /**
   * 生成随机字符串
   */
  randomString: (length: number = 8): string => {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  },

  /**
   * 生成随机邮箱
   */
  randomEmail: (): string => {
    return `test-${testUtils.randomString()}@example.com`;
  },

  /**
   * 格式化时间戳
   */
  formatTimestamp: (date: Date = new Date()): string => {
    return date.toISOString().replace(/[:.]/g, '-').slice(0, -5);
  },

  /**
   * 创建测试数据
   */
  createTestData: {
    user: () => ({
      email: testUtils.randomEmail(),
      username: `user-${testUtils.randomString()}`,
      password: 'Test123!@#',
      firstName: 'Test',
      lastName: 'User'
    }),

    admin: () => ({
      ...testUtils.createTestData.user(),
      role: 'admin',
      permissions: ['admin', 'security_officer', 'user_manager']
    }),

    oauthClient: () => ({
      clientId: `client-${testUtils.randomString()}`,
      clientSecret: `secret-${testUtils.randomString(32)}`,
      name: `Test Client ${testUtils.randomString(4)}`,
      redirectUris: ['http://localhost:3001/callback'],
      grantTypes: ['authorization_code', 'refresh_token'],
      responseTypes: ['code'],
      scope: 'openid profile email'
    })
  }
};

// 导出测试配置
export const testConfig = {
  baseUrl: process.env.E2E_BASE_URL || 'http://localhost:3001',
  timeout: parseInt(process.env.E2E_TIMEOUT || '30000'),
  headless: process.env.E2E_HEADLESS !== 'false',
  slowMo: parseInt(process.env.E2E_SLOW_MO || '0'),
  viewport: {
    width: parseInt(process.env.E2E_VIEWPORT_WIDTH || '1280'),
    height: parseInt(process.env.E2E_VIEWPORT_HEIGHT || '720')
  },
  screenshots: {
    enabled: process.env.E2E_SCREENSHOTS !== 'false',
    onFailure: true,
    path: path.resolve(__dirname, '../../../test-results/screenshots')
  },
  videos: {
    enabled: process.env.E2E_VIDEOS === 'true',
    path: path.resolve(__dirname, '../../../test-results/videos')
  }
};

console.log('🔧 E2E测试配置:', {
  baseUrl: testConfig.baseUrl,
  timeout: testConfig.timeout,
  headless: testConfig.headless,
  viewport: testConfig.viewport
});
