/**
 * 仪表板页面对象模型
 * 封装仪表板页面的元素和操作
 */

import { Page } from 'playwright';
import { BasePage } from '../setup';

/**
 * 仪表板页面类
 */
export class DashboardPage extends BasePage {
  // 页面元素选择器
  private readonly selectors = {
    // 主要布局
    header: '[data-testid="dashboard-header"]',
    sidebar: '[data-testid="dashboard-sidebar"]',
    mainContent: '[data-testid="dashboard-main"]',
    
    // 用户信息
    userAvatar: '[data-testid="user-avatar"]',
    userMenu: '[data-testid="user-menu"]',
    userProfile: '[data-testid="user-profile"]',
    logoutButton: '[data-testid="logout-button"]',
    
    // 导航菜单
    navMenu: '[data-testid="nav-menu"]',
    profileMenuItem: '[data-testid="nav-profile"]',
    securityMenuItem: '[data-testid="nav-security"]',
    settingsMenuItem: '[data-testid="nav-settings"]',
    adminMenuItem: '[data-testid="nav-admin"]',
    
    // 仪表板卡片
    welcomeCard: '[data-testid="welcome-card"]',
    statsCard: '[data-testid="stats-card"]',
    securityCard: '[data-testid="security-card"]',
    activityCard: '[data-testid="activity-card"]',
    
    // 统计信息
    loginCount: '[data-testid="login-count"]',
    lastLoginTime: '[data-testid="last-login-time"]',
    securityScore: '[data-testid="security-score"]',
    activeDevices: '[data-testid="active-devices"]',
    
    // 快速操作
    quickActions: '[data-testid="quick-actions"]',
    changePasswordButton: '[data-testid="change-password-button"]',
    enableMfaButton: '[data-testid="enable-mfa-button"]',
    viewProfileButton: '[data-testid="view-profile-button"]',
    
    // 最近活动
    recentActivity: '[data-testid="recent-activity"]',
    activityList: '[data-testid="activity-list"]',
    activityItem: '[data-testid="activity-item"]',
    
    // 安全状态
    securityStatus: '[data-testid="security-status"]',
    securityAlert: '[data-testid="security-alert"]',
    mfaStatus: '[data-testid="mfa-status"]',
    
    // 通知
    notifications: '[data-testid="notifications"]',
    notificationBell: '[data-testid="notification-bell"]',
    notificationCount: '[data-testid="notification-count"]',
    notificationPanel: '[data-testid="notification-panel"]',
    
    // 加载状态
    loadingSpinner: '[data-testid="loading-spinner"]',
    errorMessage: '[data-testid="error-message"]'
  };

  constructor(page: Page, baseUrl: string) {
    super(page, baseUrl);
  }

  /**
   * 导航到仪表板页面
   */
  async goto(): Promise<void> {
    await super.goto('/dashboard');
    await this.waitForPageLoad();
  }

  /**
   * 等待页面加载完成
   */
  async waitForPageLoad(): Promise<void> {
    await this.waitForElement(this.selectors.header);
    await this.waitForElement(this.selectors.mainContent);
    
    // 等待加载完成
    try {
      await this.page.waitForSelector(this.selectors.loadingSpinner, {
        state: 'hidden',
        timeout: 10000
      });
    } catch {
      // 如果没有加载指示器，继续
    }
  }

  /**
   * 检查是否成功加载仪表板
   */
  async isDashboardLoaded(): Promise<boolean> {
    return await this.isElementVisible(this.selectors.welcomeCard);
  }

  /**
   * 获取欢迎消息
   */
  async getWelcomeMessage(): Promise<string> {
    return await this.getText(this.selectors.welcomeCard);
  }

  /**
   * 获取用户显示名称
   */
  async getUserDisplayName(): Promise<string> {
    await this.click(this.selectors.userAvatar);
    await this.waitForElement(this.selectors.userProfile);
    return await this.getText(this.selectors.userProfile);
  }

  /**
   * 打开用户菜单
   */
  async openUserMenu(): Promise<void> {
    await this.click(this.selectors.userAvatar);
    await this.waitForElement(this.selectors.userMenu);
  }

  /**
   * 执行登出操作
   */
  async logout(): Promise<void> {
    await this.openUserMenu();
    await this.click(this.selectors.logoutButton);
  }

  /**
   * 导航到个人资料页面
   */
  async goToProfile(): Promise<void> {
    await this.click(this.selectors.profileMenuItem);
  }

  /**
   * 导航到安全设置页面
   */
  async goToSecurity(): Promise<void> {
    await this.click(this.selectors.securityMenuItem);
  }

  /**
   * 导航到系统设置页面
   */
  async goToSettings(): Promise<void> {
    await this.click(this.selectors.settingsMenuItem);
  }

  /**
   * 导航到管理页面（如果有权限）
   */
  async goToAdmin(): Promise<void> {
    if (await this.isElementVisible(this.selectors.adminMenuItem)) {
      await this.click(this.selectors.adminMenuItem);
    } else {
      throw new Error('用户没有管理员权限');
    }
  }

  /**
   * 检查是否有管理员权限
   */
  async hasAdminAccess(): Promise<boolean> {
    return await this.isElementVisible(this.selectors.adminMenuItem);
  }

  /**
   * 获取登录次数
   */
  async getLoginCount(): Promise<string> {
    return await this.getText(this.selectors.loginCount);
  }

  /**
   * 获取最后登录时间
   */
  async getLastLoginTime(): Promise<string> {
    return await this.getText(this.selectors.lastLoginTime);
  }

  /**
   * 获取安全评分
   */
  async getSecurityScore(): Promise<string> {
    return await this.getText(this.selectors.securityScore);
  }

  /**
   * 获取活跃设备数量
   */
  async getActiveDevicesCount(): Promise<string> {
    return await this.getText(this.selectors.activeDevices);
  }

  /**
   * 检查是否有安全警告
   */
  async hasSecurityAlert(): Promise<boolean> {
    return await this.isElementVisible(this.selectors.securityAlert);
  }

  /**
   * 获取安全警告消息
   */
  async getSecurityAlertMessage(): Promise<string> {
    if (await this.hasSecurityAlert()) {
      return await this.getText(this.selectors.securityAlert);
    }
    return '';
  }

  /**
   * 检查MFA状态
   */
  async getMfaStatus(): Promise<string> {
    return await this.getText(this.selectors.mfaStatus);
  }

  /**
   * 检查MFA是否已启用
   */
  async isMfaEnabled(): Promise<boolean> {
    const status = await this.getMfaStatus();
    return status.includes('已启用') || status.includes('enabled');
  }

  /**
   * 点击启用MFA按钮
   */
  async clickEnableMfa(): Promise<void> {
    await this.click(this.selectors.enableMfaButton);
  }

  /**
   * 点击修改密码按钮
   */
  async clickChangePassword(): Promise<void> {
    await this.click(this.selectors.changePasswordButton);
  }

  /**
   * 点击查看个人资料按钮
   */
  async clickViewProfile(): Promise<void> {
    await this.click(this.selectors.viewProfileButton);
  }

  /**
   * 获取最近活动列表
   */
  async getRecentActivities(): Promise<string[]> {
    const activities: string[] = [];
    
    if (await this.isElementVisible(this.selectors.activityList)) {
      const activityElements = await this.page.$$(this.selectors.activityItem);
      
      for (const element of activityElements) {
        const text = await element.textContent();
        if (text) {
          activities.push(text.trim());
        }
      }
    }
    
    return activities;
  }

  /**
   * 检查是否有新通知
   */
  async hasNewNotifications(): Promise<boolean> {
    return await this.isElementVisible(this.selectors.notificationCount);
  }

  /**
   * 获取通知数量
   */
  async getNotificationCount(): Promise<number> {
    if (await this.hasNewNotifications()) {
      const countText = await this.getText(this.selectors.notificationCount);
      return parseInt(countText) || 0;
    }
    return 0;
  }

  /**
   * 打开通知面板
   */
  async openNotifications(): Promise<void> {
    await this.click(this.selectors.notificationBell);
    await this.waitForElement(this.selectors.notificationPanel);
  }

  /**
   * 检查页面是否有错误
   */
  async hasError(): Promise<boolean> {
    return await this.isElementVisible(this.selectors.errorMessage);
  }

  /**
   * 获取错误消息
   */
  async getErrorMessage(): Promise<string> {
    if (await this.hasError()) {
      return await this.getText(this.selectors.errorMessage);
    }
    return '';
  }

  /**
   * 刷新仪表板数据
   */
  async refreshDashboard(): Promise<void> {
    await this.page.reload({ waitUntil: 'networkidle' });
    await this.waitForPageLoad();
  }

  /**
   * 检查所有统计卡片是否加载
   */
  async areStatsCardsLoaded(): Promise<boolean> {
    const cards = [
      this.selectors.welcomeCard,
      this.selectors.statsCard,
      this.selectors.securityCard,
      this.selectors.activityCard
    ];

    for (const card of cards) {
      if (!(await this.isElementVisible(card))) {
        return false;
      }
    }

    return true;
  }

  /**
   * 等待所有数据加载完成
   */
  async waitForAllDataLoaded(): Promise<void> {
    // 等待统计卡片加载
    await this.waitForElement(this.selectors.statsCard);
    
    // 等待安全状态加载
    await this.waitForElement(this.selectors.securityStatus);
    
    // 等待最近活动加载
    await this.waitForElement(this.selectors.recentActivity);
    
    // 等待任何加载指示器消失
    try {
      await this.page.waitForSelector(this.selectors.loadingSpinner, {
        state: 'hidden',
        timeout: 15000
      });
    } catch {
      // 如果没有加载指示器，继续
    }
  }

  /**
   * 获取页面标题
   */
  async getPageTitle(): Promise<string> {
    return await this.page.title();
  }

  /**
   * 检查侧边栏是否可见
   */
  async isSidebarVisible(): Promise<boolean> {
    return await this.isElementVisible(this.selectors.sidebar);
  }

  /**
   * 获取当前页面URL
   */
  getCurrentUrl(): string {
    return this.page.url();
  }

  /**
   * 检查是否在仪表板页面
   */
  isOnDashboard(): boolean {
    return this.getCurrentUrl().includes('/dashboard');
  }
}
