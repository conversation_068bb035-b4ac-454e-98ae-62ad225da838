/**
 * 登录页面对象模型
 * 封装登录页面的元素和操作
 */

import { Page } from 'playwright';
import { BasePage } from '../setup';

/**
 * 登录页面类
 */
export class LoginPage extends BasePage {
  // 页面元素选择器
  private readonly selectors = {
    // 登录表单
    loginForm: '[data-testid="login-form"]',
    emailInput: '[data-testid="email-input"]',
    passwordInput: '[data-testid="password-input"]',
    loginButton: '[data-testid="login-button"]',
    rememberMeCheckbox: '[data-testid="remember-me-checkbox"]',
    
    // 错误消息
    errorMessage: '[data-testid="error-message"]',
    fieldError: '[data-testid="field-error"]',
    
    // 多因素认证
    mfaSection: '[data-testid="mfa-section"]',
    mfaCodeInput: '[data-testid="mfa-code-input"]',
    mfaSubmitButton: '[data-testid="mfa-submit-button"]',
    mfaBackupCodeLink: '[data-testid="mfa-backup-code-link"]',
    
    // 第三方登录
    oauthSection: '[data-testid="oauth-section"]',
    googleLoginButton: '[data-testid="google-login-button"]',
    githubLoginButton: '[data-testid="github-login-button"]',
    wechatLoginButton: '[data-testid="wechat-login-button"]',
    
    // 其他链接
    forgotPasswordLink: '[data-testid="forgot-password-link"]',
    registerLink: '[data-testid="register-link"]',
    
    // 成功状态
    successMessage: '[data-testid="success-message"]',
    redirectingMessage: '[data-testid="redirecting-message"]'
  };

  constructor(page: Page, baseUrl: string) {
    super(page, baseUrl);
  }

  /**
   * 导航到登录页面
   */
  async goto(): Promise<void> {
    await super.goto('/login');
    await this.waitForPageLoad();
  }

  /**
   * 等待页面加载完成
   */
  async waitForPageLoad(): Promise<void> {
    await this.waitForElement(this.selectors.loginForm);
  }

  /**
   * 执行基本登录
   */
  async login(email: string, password: string, rememberMe: boolean = false): Promise<void> {
    await this.fillEmail(email);
    await this.fillPassword(password);
    
    if (rememberMe) {
      await this.checkRememberMe();
    }
    
    await this.clickLoginButton();
  }

  /**
   * 填写邮箱
   */
  async fillEmail(email: string): Promise<void> {
    await this.fill(this.selectors.emailInput, email);
  }

  /**
   * 填写密码
   */
  async fillPassword(password: string): Promise<void> {
    await this.fill(this.selectors.passwordInput, password);
  }

  /**
   * 勾选记住我
   */
  async checkRememberMe(): Promise<void> {
    await this.click(this.selectors.rememberMeCheckbox);
  }

  /**
   * 点击登录按钮
   */
  async clickLoginButton(): Promise<void> {
    await this.click(this.selectors.loginButton);
  }

  /**
   * 获取错误消息
   */
  async getErrorMessage(): Promise<string> {
    try {
      await this.waitForElement(this.selectors.errorMessage, 5000);
      return await this.getText(this.selectors.errorMessage);
    } catch {
      return '';
    }
  }

  /**
   * 获取字段错误消息
   */
  async getFieldError(): Promise<string> {
    try {
      await this.waitForElement(this.selectors.fieldError, 5000);
      return await this.getText(this.selectors.fieldError);
    } catch {
      return '';
    }
  }

  /**
   * 检查是否显示MFA界面
   */
  async isMfaRequired(): Promise<boolean> {
    return await this.isElementVisible(this.selectors.mfaSection);
  }

  /**
   * 输入MFA验证码
   */
  async enterMfaCode(code: string): Promise<void> {
    await this.waitForElement(this.selectors.mfaCodeInput);
    await this.fill(this.selectors.mfaCodeInput, code);
    await this.click(this.selectors.mfaSubmitButton);
  }

  /**
   * 点击使用备用代码
   */
  async clickBackupCodeLink(): Promise<void> {
    await this.click(this.selectors.mfaBackupCodeLink);
  }

  /**
   * 使用Google登录
   */
  async loginWithGoogle(): Promise<void> {
    await this.click(this.selectors.googleLoginButton);
  }

  /**
   * 使用GitHub登录
   */
  async loginWithGithub(): Promise<void> {
    await this.click(this.selectors.githubLoginButton);
  }

  /**
   * 使用微信登录
   */
  async loginWithWechat(): Promise<void> {
    await this.click(this.selectors.wechatLoginButton);
  }

  /**
   * 点击忘记密码链接
   */
  async clickForgotPassword(): Promise<void> {
    await this.click(this.selectors.forgotPasswordLink);
  }

  /**
   * 点击注册链接
   */
  async clickRegisterLink(): Promise<void> {
    await this.click(this.selectors.registerLink);
  }

  /**
   * 检查是否登录成功
   */
  async isLoginSuccessful(): Promise<boolean> {
    try {
      // 等待成功消息或重定向消息
      await Promise.race([
        this.waitForElement(this.selectors.successMessage, 10000),
        this.waitForElement(this.selectors.redirectingMessage, 10000),
        this.page.waitForURL('**/dashboard', { timeout: 10000 }),
        this.page.waitForURL('**/profile', { timeout: 10000 })
      ]);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * 等待重定向到指定页面
   */
  async waitForRedirect(expectedPath: string, timeout: number = 10000): Promise<boolean> {
    try {
      await this.page.waitForURL(`**${expectedPath}`, { timeout });
      return true;
    } catch {
      return false;
    }
  }

  /**
   * 获取当前页面URL
   */
  getCurrentUrl(): string {
    return this.page.url();
  }

  /**
   * 检查登录表单是否可见
   */
  async isLoginFormVisible(): Promise<boolean> {
    return await this.isElementVisible(this.selectors.loginForm);
  }

  /**
   * 检查OAuth登录选项是否可见
   */
  async isOAuthSectionVisible(): Promise<boolean> {
    return await this.isElementVisible(this.selectors.oauthSection);
  }

  /**
   * 获取页面标题
   */
  async getPageTitle(): Promise<string> {
    return await this.page.title();
  }

  /**
   * 检查输入字段是否为空
   */
  async isEmailFieldEmpty(): Promise<boolean> {
    const value = await this.page.inputValue(this.selectors.emailInput);
    return value === '';
  }

  /**
   * 检查密码字段是否为空
   */
  async isPasswordFieldEmpty(): Promise<boolean> {
    const value = await this.page.inputValue(this.selectors.passwordInput);
    return value === '';
  }

  /**
   * 检查记住我是否被选中
   */
  async isRememberMeChecked(): Promise<boolean> {
    return await this.page.isChecked(this.selectors.rememberMeCheckbox);
  }

  /**
   * 清空表单
   */
  async clearForm(): Promise<void> {
    await this.page.fill(this.selectors.emailInput, '');
    await this.page.fill(this.selectors.passwordInput, '');
    
    // 如果记住我被选中，取消选中
    if (await this.isRememberMeChecked()) {
      await this.click(this.selectors.rememberMeCheckbox);
    }
  }

  /**
   * 检查登录按钮是否被禁用
   */
  async isLoginButtonDisabled(): Promise<boolean> {
    return await this.page.isDisabled(this.selectors.loginButton);
  }

  /**
   * 模拟键盘操作登录
   */
  async loginWithKeyboard(email: string, password: string): Promise<void> {
    await this.fillEmail(email);
    await this.page.keyboard.press('Tab');
    await this.fillPassword(password);
    await this.page.keyboard.press('Enter');
  }

  /**
   * 检查是否有验证码要求
   */
  async hasCaptchaChallenge(): Promise<boolean> {
    return await this.isElementVisible('[data-testid="captcha-challenge"]');
  }

  /**
   * 等待并处理可能的验证码
   */
  async handleCaptchaIfPresent(): Promise<void> {
    if (await this.hasCaptchaChallenge()) {
      // 在测试环境中，可能需要特殊处理验证码
      // 这里可以添加测试环境的验证码绕过逻辑
      console.log('检测到验证码，在测试环境中跳过');
    }
  }

  /**
   * 获取所有可见的错误消息
   */
  async getAllErrorMessages(): Promise<string[]> {
    const errors: string[] = [];
    
    // 获取主要错误消息
    const mainError = await this.getErrorMessage();
    if (mainError) {
      errors.push(mainError);
    }
    
    // 获取字段错误消息
    const fieldError = await this.getFieldError();
    if (fieldError) {
      errors.push(fieldError);
    }
    
    return errors;
  }

  /**
   * 检查页面是否有加载指示器
   */
  async hasLoadingIndicator(): Promise<boolean> {
    return await this.isElementVisible('[data-testid="loading-indicator"]');
  }

  /**
   * 等待加载完成
   */
  async waitForLoadingComplete(): Promise<void> {
    try {
      // 等待加载指示器出现
      await this.waitForElement('[data-testid="loading-indicator"]', 2000);
      
      // 等待加载指示器消失
      await this.page.waitForSelector('[data-testid="loading-indicator"]', {
        state: 'hidden',
        timeout: 10000
      });
    } catch {
      // 如果没有加载指示器，直接继续
    }
  }
}
