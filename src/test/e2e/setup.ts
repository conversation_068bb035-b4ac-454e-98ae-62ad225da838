/**
 * E2E测试环境设置
 * 配置端到端测试的基础环境和工具
 */

import { chromium, <PERSON><PERSON><PERSON>, BrowserContext, Page } from 'playwright';
import { spawn, ChildProcess } from 'child_process';
import { logger } from '@/config/logger';
import { prisma } from '@/config/database';
import { redisService } from '@/services/redis.service';

/**
 * E2E测试配置
 */
export interface E2ETestConfig {
  baseUrl: string;
  timeout: number;
  headless: boolean;
  slowMo: number;
  viewport: {
    width: number;
    height: number;
  };
  browser: {
    name: 'chromium' | 'firefox' | 'webkit';
    args: string[];
  };
  server: {
    port: number;
    startTimeout: number;
  };
}

/**
 * 默认E2E测试配置
 */
export const defaultE2EConfig: E2ETestConfig = {
  baseUrl: process.env.E2E_BASE_URL || 'http://localhost:3001',
  timeout: parseInt(process.env.E2E_TIMEOUT || '30000'),
  headless: process.env.E2E_HEADLESS !== 'false',
  slowMo: parseInt(process.env.E2E_SLOW_MO || '0'),
  viewport: {
    width: parseInt(process.env.E2E_VIEWPORT_WIDTH || '1280'),
    height: parseInt(process.env.E2E_VIEWPORT_HEIGHT || '720')
  },
  browser: {
    name: (process.env.E2E_BROWSER as any) || 'chromium',
    args: [
      '--no-sandbox',
      '--disable-setuid-sandbox',
      '--disable-dev-shm-usage',
      '--disable-gpu'
    ]
  },
  server: {
    port: parseInt(process.env.E2E_SERVER_PORT || '3001'),
    startTimeout: parseInt(process.env.E2E_SERVER_START_TIMEOUT || '30000')
  }
};

/**
 * E2E测试环境管理器
 */
export class E2ETestEnvironment {
  private browser: Browser | null = null;
  private context: BrowserContext | null = null;
  private page: Page | null = null;
  private serverProcess: ChildProcess | null = null;
  private config: E2ETestConfig;

  constructor(config: E2ETestConfig = defaultE2EConfig) {
    this.config = config;
  }

  /**
   * 启动测试环境
   */
  async setup(): Promise<void> {
    try {
      logger.info('启动E2E测试环境');

      // 清理测试数据库
      await this.cleanDatabase();

      // 启动测试服务器
      await this.startTestServer();

      // 启动浏览器
      await this.startBrowser();

      logger.info('E2E测试环境启动完成');

    } catch (error) {
      logger.error('E2E测试环境启动失败', {
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * 清理测试环境
   */
  async teardown(): Promise<void> {
    try {
      logger.info('清理E2E测试环境');

      // 关闭浏览器
      if (this.page) {
        await this.page.close();
        this.page = null;
      }

      if (this.context) {
        await this.context.close();
        this.context = null;
      }

      if (this.browser) {
        await this.browser.close();
        this.browser = null;
      }

      // 停止测试服务器
      if (this.serverProcess) {
        this.serverProcess.kill('SIGTERM');
        this.serverProcess = null;
      }

      // 清理测试数据
      await this.cleanDatabase();

      logger.info('E2E测试环境清理完成');

    } catch (error) {
      logger.error('E2E测试环境清理失败', {
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  /**
   * 获取页面实例
   */
  getPage(): Page {
    if (!this.page) {
      throw new Error('页面未初始化，请先调用setup()');
    }
    return this.page;
  }

  /**
   * 获取浏览器上下文
   */
  getContext(): BrowserContext {
    if (!this.context) {
      throw new Error('浏览器上下文未初始化，请先调用setup()');
    }
    return this.context;
  }

  /**
   * 创建新页面
   */
  async createNewPage(): Promise<Page> {
    if (!this.context) {
      throw new Error('浏览器上下文未初始化，请先调用setup()');
    }
    return await this.context.newPage();
  }

  /**
   * 导航到指定路径
   */
  async goto(path: string): Promise<void> {
    const page = this.getPage();
    const url = `${this.config.baseUrl}${path}`;
    await page.goto(url, { waitUntil: 'networkidle' });
  }

  /**
   * 等待服务器就绪
   */
  async waitForServer(): Promise<void> {
    const startTime = Date.now();
    const timeout = this.config.server.startTimeout;

    while (Date.now() - startTime < timeout) {
      try {
        const response = await fetch(`${this.config.baseUrl}/health`);
        if (response.ok) {
          logger.info('测试服务器已就绪');
          return;
        }
      } catch (error) {
        // 服务器还未启动，继续等待
      }

      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    throw new Error(`测试服务器在${timeout}ms内未就绪`);
  }

  /**
   * 启动测试服务器
   */
  private async startTestServer(): Promise<void> {
    return new Promise((resolve, reject) => {
      logger.info('启动测试服务器', { port: this.config.server.port });

      // 设置测试环境变量
      const env = {
        ...process.env,
        NODE_ENV: 'test',
        PORT: this.config.server.port.toString(),
        DATABASE_URL: process.env.TEST_DATABASE_URL || process.env.DATABASE_URL,
        REDIS_URL: process.env.TEST_REDIS_URL || process.env.REDIS_URL,
        JWT_SECRET: 'test-jwt-secret',
        AUTOMATED_SECURITY_SCANNER_ENABLED: 'false' // 测试环境禁用自动扫描
      };

      // 启动服务器进程
      this.serverProcess = spawn('npm', ['run', 'start:test'], {
        env,
        stdio: 'pipe',
        detached: false
      });

      let serverReady = false;

      // 监听服务器输出
      this.serverProcess.stdout?.on('data', (data) => {
        const output = data.toString();
        if (output.includes('服务器启动成功') || output.includes(`端口 ${this.config.server.port}`)) {
          serverReady = true;
          resolve();
        }
      });

      this.serverProcess.stderr?.on('data', (data) => {
        logger.error('测试服务器错误输出', { error: data.toString() });
      });

      this.serverProcess.on('error', (error) => {
        logger.error('测试服务器启动失败', { error: error.message });
        reject(error);
      });

      this.serverProcess.on('exit', (code) => {
        if (!serverReady) {
          reject(new Error(`测试服务器异常退出，退出码: ${code}`));
        }
      });

      // 超时处理
      setTimeout(() => {
        if (!serverReady) {
          reject(new Error('测试服务器启动超时'));
        }
      }, this.config.server.startTimeout);
    });
  }

  /**
   * 启动浏览器
   */
  private async startBrowser(): Promise<void> {
    logger.info('启动测试浏览器', { 
      browser: this.config.browser.name,
      headless: this.config.headless 
    });

    // 启动浏览器
    this.browser = await chromium.launch({
      headless: this.config.headless,
      slowMo: this.config.slowMo,
      args: this.config.browser.args
    });

    // 创建浏览器上下文
    this.context = await this.browser.newContext({
      viewport: this.config.viewport,
      ignoreHTTPSErrors: true,
      acceptDownloads: true
    });

    // 创建页面
    this.page = await this.context.newPage();

    // 设置默认超时
    this.page.setDefaultTimeout(this.config.timeout);

    // 监听控制台消息
    this.page.on('console', (msg) => {
      if (msg.type() === 'error') {
        logger.warn('浏览器控制台错误', { message: msg.text() });
      }
    });

    // 监听页面错误
    this.page.on('pageerror', (error) => {
      logger.error('页面JavaScript错误', { error: error.message });
    });
  }

  /**
   * 清理测试数据库
   */
  private async cleanDatabase(): Promise<void> {
    try {
      logger.info('清理测试数据库');

      // 清理用户相关数据
      await prisma.session.deleteMany();
      await prisma.auditLog.deleteMany();
      await prisma.user.deleteMany();

      // 清理Redis缓存
      if (redisService.isConnected()) {
        await redisService.flushAll();
      }

      logger.info('测试数据库清理完成');

    } catch (error) {
      logger.error('清理测试数据库失败', {
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }
}

/**
 * 测试数据工厂
 */
export class TestDataFactory {
  /**
   * 创建测试用户
   */
  static async createTestUser(userData: Partial<any> = {}): Promise<any> {
    const defaultUser = {
      email: `test-${Date.now()}@example.com`,
      username: `testuser-${Date.now()}`,
      password: 'Test123!@#',
      firstName: 'Test',
      lastName: 'User',
      isActive: true,
      emailVerified: true
    };

    return await prisma.user.create({
      data: { ...defaultUser, ...userData }
    });
  }

  /**
   * 创建测试管理员
   */
  static async createTestAdmin(userData: Partial<any> = {}): Promise<any> {
    const adminData = {
      ...userData,
      role: 'admin',
      permissions: ['admin', 'security_officer', 'user_manager']
    };

    return await this.createTestUser(adminData);
  }

  /**
   * 创建OAuth客户端
   */
  static async createOAuthClient(clientData: Partial<any> = {}): Promise<any> {
    const defaultClient = {
      clientId: `test-client-${Date.now()}`,
      clientSecret: 'test-client-secret',
      name: 'Test OAuth Client',
      redirectUris: ['http://localhost:3001/callback'],
      grantTypes: ['authorization_code', 'refresh_token'],
      responseTypes: ['code'],
      scope: 'openid profile email'
    };

    return await prisma.oAuthClient.create({
      data: { ...defaultClient, ...clientData }
    });
  }
}

/**
 * 页面对象基类
 */
export abstract class BasePage {
  protected page: Page;
  protected baseUrl: string;

  constructor(page: Page, baseUrl: string) {
    this.page = page;
    this.baseUrl = baseUrl;
  }

  /**
   * 导航到页面
   */
  async goto(path: string = ''): Promise<void> {
    const url = `${this.baseUrl}${path}`;
    await this.page.goto(url, { waitUntil: 'networkidle' });
  }

  /**
   * 等待元素可见
   */
  async waitForElement(selector: string, timeout?: number): Promise<void> {
    await this.page.waitForSelector(selector, { 
      state: 'visible',
      timeout 
    });
  }

  /**
   * 点击元素
   */
  async click(selector: string): Promise<void> {
    await this.page.click(selector);
  }

  /**
   * 填写表单字段
   */
  async fill(selector: string, value: string): Promise<void> {
    await this.page.fill(selector, value);
  }

  /**
   * 获取元素文本
   */
  async getText(selector: string): Promise<string> {
    return await this.page.textContent(selector) || '';
  }

  /**
   * 检查元素是否存在
   */
  async isElementVisible(selector: string): Promise<boolean> {
    try {
      await this.page.waitForSelector(selector, { 
        state: 'visible', 
        timeout: 5000 
      });
      return true;
    } catch {
      return false;
    }
  }

  /**
   * 截图
   */
  async screenshot(name: string): Promise<void> {
    await this.page.screenshot({ 
      path: `test-results/screenshots/${name}.png`,
      fullPage: true 
    });
  }
}

// 导出全局测试环境实例
export const testEnvironment = new E2ETestEnvironment();
