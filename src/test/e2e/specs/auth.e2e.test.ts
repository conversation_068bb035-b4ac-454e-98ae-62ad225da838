/**
 * 认证流程端到端测试
 * 测试用户登录、登出和认证相关功能
 */

import { describe, beforeAll, afterAll, beforeEach, afterEach, test, expect } from '@jest/globals';
import { testEnvironment, TestDataFactory } from '../setup';
import { LoginPage } from '../pages/login.page';
import { DashboardPage } from '../pages/dashboard.page';

describe('认证流程 E2E 测试', () => {
  let loginPage: LoginPage;
  let dashboardPage: DashboardPage;
  let testUser: any;
  let testAdmin: any;

  beforeAll(async () => {
    // 启动测试环境
    await testEnvironment.setup();
    
    // 创建测试用户
    testUser = await TestDataFactory.createTestUser({
      email: '<EMAIL>',
      username: 'testuser',
      password: 'Test123!@#',
      firstName: 'Test',
      lastName: 'User'
    });

    // 创建测试管理员
    testAdmin = await TestDataFactory.createTestAdmin({
      email: '<EMAIL>',
      username: 'admin',
      password: 'Admin123!@#',
      firstName: 'Admin',
      lastName: 'User'
    });

    // 初始化页面对象
    const page = testEnvironment.getPage();
    const baseUrl = 'http://localhost:3001';
    
    loginPage = new LoginPage(page, baseUrl);
    dashboardPage = new DashboardPage(page, baseUrl);
  });

  afterAll(async () => {
    // 清理测试环境
    await testEnvironment.teardown();
  });

  beforeEach(async () => {
    // 每个测试前确保用户已登出
    try {
      await testEnvironment.goto('/logout');
    } catch {
      // 如果登出失败，直接继续
    }
  });

  afterEach(async () => {
    // 每个测试后截图（如果测试失败）
    const testName = expect.getState().currentTestName || 'unknown';
    if (expect.getState().assertionCalls === 0) {
      await loginPage.screenshot(`failed-${testName}`);
    }
  });

  describe('登录功能', () => {
    test('应该能够成功登录有效用户', async () => {
      // 导航到登录页面
      await loginPage.goto();
      
      // 验证登录页面加载
      expect(await loginPage.isLoginFormVisible()).toBe(true);
      expect(await loginPage.getPageTitle()).toContain('登录');

      // 执行登录
      await loginPage.login(testUser.email, 'Test123!@#');

      // 验证登录成功
      expect(await loginPage.isLoginSuccessful()).toBe(true);
      
      // 验证重定向到仪表板
      expect(await loginPage.waitForRedirect('/dashboard')).toBe(true);
      
      // 验证仪表板加载
      await dashboardPage.waitForPageLoad();
      expect(await dashboardPage.isDashboardLoaded()).toBe(true);
    });

    test('应该拒绝无效的登录凭据', async () => {
      await loginPage.goto();
      
      // 使用错误密码登录
      await loginPage.login(testUser.email, 'wrongpassword');
      
      // 验证显示错误消息
      const errorMessage = await loginPage.getErrorMessage();
      expect(errorMessage).toContain('凭据无效');
      
      // 验证仍在登录页面
      expect(await loginPage.isLoginFormVisible()).toBe(true);
    });

    test('应该拒绝不存在的用户', async () => {
      await loginPage.goto();
      
      // 使用不存在的邮箱登录
      await loginPage.login('<EMAIL>', 'password');
      
      // 验证显示错误消息
      const errorMessage = await loginPage.getErrorMessage();
      expect(errorMessage).toContain('用户不存在');
    });

    test('应该验证必填字段', async () => {
      await loginPage.goto();
      
      // 尝试提交空表单
      await loginPage.clickLoginButton();
      
      // 验证字段验证错误
      const fieldError = await loginPage.getFieldError();
      expect(fieldError).toContain('邮箱是必填项');
    });

    test('应该支持记住我功能', async () => {
      await loginPage.goto();
      
      // 使用记住我选项登录
      await loginPage.login(testUser.email, 'Test123!@#', true);
      
      // 验证登录成功
      expect(await loginPage.isLoginSuccessful()).toBe(true);
      
      // 验证记住我被选中
      // 注意：这里需要在登录前检查，因为登录后会重定向
    });

    test('应该支持键盘导航登录', async () => {
      await loginPage.goto();
      
      // 使用键盘操作登录
      await loginPage.loginWithKeyboard(testUser.email, 'Test123!@#');
      
      // 验证登录成功
      expect(await loginPage.isLoginSuccessful()).toBe(true);
    });
  });

  describe('管理员登录', () => {
    test('管理员应该能够访问管理功能', async () => {
      await loginPage.goto();
      
      // 管理员登录
      await loginPage.login(testAdmin.email, 'Admin123!@#');
      
      // 验证登录成功并重定向到仪表板
      expect(await loginPage.isLoginSuccessful()).toBe(true);
      await dashboardPage.waitForPageLoad();
      
      // 验证管理员权限
      expect(await dashboardPage.hasAdminAccess()).toBe(true);
    });
  });

  describe('登出功能', () => {
    test('应该能够成功登出', async () => {
      // 先登录
      await loginPage.goto();
      await loginPage.login(testUser.email, 'Test123!@#');
      await dashboardPage.waitForPageLoad();
      
      // 执行登出
      await dashboardPage.logout();
      
      // 验证重定向到登录页面
      expect(await loginPage.waitForRedirect('/login')).toBe(true);
      expect(await loginPage.isLoginFormVisible()).toBe(true);
    });
  });

  describe('会话管理', () => {
    test('应该在会话过期后重定向到登录页面', async () => {
      // 登录用户
      await loginPage.goto();
      await loginPage.login(testUser.email, 'Test123!@#');
      await dashboardPage.waitForPageLoad();
      
      // 模拟会话过期（通过清除cookies）
      await testEnvironment.getContext().clearCookies();
      
      // 尝试访问受保护的页面
      await dashboardPage.refreshDashboard();
      
      // 验证重定向到登录页面
      expect(await loginPage.waitForRedirect('/login')).toBe(true);
    });
  });

  describe('安全功能', () => {
    test('应该在多次失败登录后显示安全警告', async () => {
      await loginPage.goto();
      
      // 多次尝试错误登录
      for (let i = 0; i < 3; i++) {
        await loginPage.login(testUser.email, 'wrongpassword');
        await new Promise(resolve => setTimeout(resolve, 1000)); // 等待1秒
      }
      
      // 验证安全警告或账户锁定消息
      const errorMessage = await loginPage.getErrorMessage();
      expect(errorMessage).toMatch(/(账户已锁定|安全警告|尝试次数过多)/);
    });

    test('应该处理验证码挑战', async () => {
      await loginPage.goto();
      
      // 检查是否有验证码
      await loginPage.handleCaptchaIfPresent();
      
      // 继续正常登录流程
      await loginPage.login(testUser.email, 'Test123!@#');
      expect(await loginPage.isLoginSuccessful()).toBe(true);
    });
  });

  describe('响应式设计', () => {
    test('应该在移动设备上正常工作', async () => {
      // 设置移动设备视口
      await testEnvironment.getPage().setViewportSize({ width: 375, height: 667 });
      
      await loginPage.goto();
      
      // 验证登录表单在移动设备上可见
      expect(await loginPage.isLoginFormVisible()).toBe(true);
      
      // 执行登录
      await loginPage.login(testUser.email, 'Test123!@#');
      expect(await loginPage.isLoginSuccessful()).toBe(true);
      
      // 恢复桌面视口
      await testEnvironment.getPage().setViewportSize({ width: 1280, height: 720 });
    });
  });

  describe('第三方登录', () => {
    test('应该显示第三方登录选项', async () => {
      await loginPage.goto();
      
      // 验证OAuth登录选项可见
      expect(await loginPage.isOAuthSectionVisible()).toBe(true);
    });

    // 注意：实际的第三方登录测试需要模拟OAuth流程
    // 这里只测试UI元素的存在性
  });

  describe('错误处理', () => {
    test('应该处理网络错误', async () => {
      await loginPage.goto();
      
      // 模拟网络离线
      await testEnvironment.getContext().setOffline(true);
      
      // 尝试登录
      await loginPage.login(testUser.email, 'Test123!@#');
      
      // 验证显示网络错误消息
      const errorMessage = await loginPage.getErrorMessage();
      expect(errorMessage).toMatch(/(网络错误|连接失败|请检查网络)/);
      
      // 恢复网络连接
      await testEnvironment.getContext().setOffline(false);
    });

    test('应该处理服务器错误', async () => {
      // 这个测试需要模拟服务器错误响应
      // 可以通过拦截网络请求来实现
      await testEnvironment.getPage().route('**/api/v1/auth/login', route => {
        route.fulfill({
          status: 500,
          contentType: 'application/json',
          body: JSON.stringify({ error: '服务器内部错误' })
        });
      });

      await loginPage.goto();
      await loginPage.login(testUser.email, 'Test123!@#');
      
      // 验证显示服务器错误消息
      const errorMessage = await loginPage.getErrorMessage();
      expect(errorMessage).toContain('服务器错误');
    });
  });

  describe('性能测试', () => {
    test('登录页面应该在合理时间内加载', async () => {
      const startTime = Date.now();

      await loginPage.goto();
      await loginPage.waitForPageLoad();

      const loadTime = Date.now() - startTime;

      // 验证页面在3秒内加载完成
      expect(loadTime).toBeLessThan(3000);
    });

    test('登录操作应该在合理时间内完成', async () => {
      await loginPage.goto();

      const startTime = Date.now();

      await loginPage.login(testUser.email, 'Test123!@#');
      await loginPage.waitForRedirect('/dashboard');

      const loginTime = Date.now() - startTime;

      // 验证登录在5秒内完成
      expect(loginTime).toBeLessThan(5000);
    });
  });

  describe('多因素认证 (MFA)', () => {
    test('应该支持TOTP多因素认证', async () => {
      // 创建启用了MFA的测试用户
      const mfaUser = await TestDataFactory.createTestUser({
        email: '<EMAIL>',
        password: 'Test123!@#',
        mfaEnabled: true,
        mfaSecret: 'JBSWY3DPEHPK3PXP' // 测试用的TOTP密钥
      });

      await loginPage.goto();

      // 第一步：用户名密码登录
      await loginPage.login(mfaUser.email, 'Test123!@#');

      // 验证显示MFA界面
      expect(await loginPage.isMfaRequired()).toBe(true);

      // 第二步：输入TOTP验证码
      // 在测试环境中，可以使用固定的测试验证码
      await loginPage.enterMfaCode('123456');

      // 验证登录成功
      expect(await loginPage.isLoginSuccessful()).toBe(true);
    });

    test('应该支持备用验证码', async () => {
      const mfaUser = await TestDataFactory.createTestUser({
        email: '<EMAIL>',
        password: 'Test123!@#',
        mfaEnabled: true,
        backupCodes: ['backup123', 'backup456']
      });

      await loginPage.goto();
      await loginPage.login(mfaUser.email, 'Test123!@#');

      // 点击使用备用代码
      await loginPage.clickBackupCodeLink();

      // 输入备用代码
      await loginPage.enterMfaCode('backup123');

      // 验证登录成功
      expect(await loginPage.isLoginSuccessful()).toBe(true);
    });
  });
});
