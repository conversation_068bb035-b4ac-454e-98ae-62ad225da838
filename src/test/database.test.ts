/**
 * 数据库功能验证测试
 * 测试数据库连接和基本CRUD操作
 */

import { PrismaClient } from '@prisma/client';

// 创建测试专用的Prisma客户端
const prisma = new PrismaClient({
  datasources: {
    db: {
      url: process.env['DATABASE_URL'] || 'file:./test.db'
    }
  }
});

describe('数据库连接验证', () => {
  beforeAll(async () => {
    // 确保数据库连接正常
    try {
      await prisma.$connect();
    } catch (error) {
      console.error('数据库连接失败:', error);
      throw error;
    }
  });

  afterAll(async () => {
    // 清理测试数据并断开连接
    try {
      // 清理测试数据
      await prisma.user.deleteMany({
        where: {
          email: {
            contains: 'test-db-'
          }
        }
      });
    } catch (error) {
      console.warn('清理测试数据时出错:', error);
    } finally {
      await prisma.$disconnect();
    }
  });

  describe('基本数据库操作', () => {
    it('应该能够连接到数据库', async () => {
      // 执行一个简单的查询来验证连接
      const result = await prisma.$queryRaw`SELECT 1 as test`;
      expect(result).toBeDefined();
      expect(Array.isArray(result)).toBe(true);
    });

    it('应该能够查询数据库版本信息', async () => {
      // SQLite版本查询
      const result = await prisma.$queryRaw`SELECT sqlite_version() as version` as any[];
      
      expect(result).toBeDefined();
      expect(result.length).toBeGreaterThan(0);
      expect(result[0]).toHaveProperty('version');
      expect(typeof result[0].version).toBe('string');
    });
  });

  describe('用户表CRUD操作', () => {
    let testUserId: string;

    it('应该能够创建新用户', async () => {
      const userData = {
        id: 'test-db-user-001',
        email: '<EMAIL>',
        nickname: 'Test DB User',
        passwordHash: '$2b$12$hashedpasswordfortesting',
        emailVerified: false,
        isActive: true,
        isLocked: false
      };

      const user = await prisma.user.create({
        data: userData
      });

      testUserId = user.id;

      expect(user).toBeDefined();
      expect(user.id).toBe(userData.id);
      expect(user.email).toBe(userData.email);
      expect(user.nickname).toBe(userData.nickname);
      expect(user.emailVerified).toBe(false);
      expect(user.isActive).toBe(true);
      expect(user.isLocked).toBe(false);
      expect(user.createdAt).toBeInstanceOf(Date);
      expect(user.updatedAt).toBeInstanceOf(Date);
    });

    it('应该能够查询用户', async () => {
      const user = await prisma.user.findUnique({
        where: { id: testUserId }
      });

      expect(user).toBeDefined();
      expect(user?.id).toBe(testUserId);
      expect(user?.email).toBe('<EMAIL>');
    });

    it('应该能够更新用户信息', async () => {
      const updatedUser = await prisma.user.update({
        where: { id: testUserId },
        data: {
          nickname: 'Updated Test User',
          emailVerified: true
        }
      });

      expect(updatedUser).toBeDefined();
      expect(updatedUser.nickname).toBe('Updated Test User');
      expect(updatedUser.emailVerified).toBe(true);
      expect(updatedUser.updatedAt.getTime()).toBeGreaterThan(updatedUser.createdAt.getTime());
    });

    it('应该能够按条件查询用户', async () => {
      const users = await prisma.user.findMany({
        where: {
          email: {
            contains: 'test-db-'
          },
          isActive: true
        }
      });

      expect(users).toBeDefined();
      expect(Array.isArray(users)).toBe(true);
      expect(users.length).toBeGreaterThan(0);
      
      const testUser = users.find(u => u.id === testUserId);
      expect(testUser).toBeDefined();
    });

    it('应该能够统计用户数量', async () => {
      const count = await prisma.user.count({
        where: {
          email: {
            contains: 'test-db-'
          }
        }
      });

      expect(count).toBeDefined();
      expect(typeof count).toBe('number');
      expect(count).toBeGreaterThan(0);
    });

    it('应该能够删除用户', async () => {
      const deletedUser = await prisma.user.delete({
        where: { id: testUserId }
      });

      expect(deletedUser).toBeDefined();
      expect(deletedUser.id).toBe(testUserId);

      // 验证用户已被删除
      const user = await prisma.user.findUnique({
        where: { id: testUserId }
      });

      expect(user).toBeNull();
    });
  });

  describe('数据库事务操作', () => {
    it('应该能够执行事务操作', async () => {
      const result = await prisma.$transaction(async (tx) => {
        // 在事务中创建用户
        const user = await tx.user.create({
          data: {
            id: 'test-db-transaction-user',
            email: '<EMAIL>',
            nickname: 'Transaction Test User',
            passwordHash: '$2b$12$hashedpasswordfortesting',
            emailVerified: false,
            isActive: true,
            isLocked: false
          }
        });

        // 在同一事务中更新用户
        const updatedUser = await tx.user.update({
          where: { id: user.id },
          data: { emailVerified: true }
        });

        return { user, updatedUser };
      });

      expect(result).toBeDefined();
      expect(result.user).toBeDefined();
      expect(result.updatedUser).toBeDefined();
      expect(result.updatedUser.emailVerified).toBe(true);

      // 清理测试数据
      await prisma.user.delete({
        where: { id: 'test-db-transaction-user' }
      });
    });

    it('应该能够回滚失败的事务', async () => {
      let transactionFailed = false;

      try {
        await prisma.$transaction(async (tx) => {
          // 创建用户
          await tx.user.create({
            data: {
              id: 'test-db-rollback-user',
              email: '<EMAIL>',
              nickname: 'Rollback Test User',
              passwordHash: '$2b$12$hashedpasswordfortesting',
              emailVerified: false,
              isActive: true,
              isLocked: false
            }
          });

          // 故意抛出错误来触发回滚
          throw new Error('故意的事务错误');
        });
      } catch (error) {
        transactionFailed = true;
        expect(error).toBeInstanceOf(Error);
      }

      expect(transactionFailed).toBe(true);

      // 验证用户没有被创建（事务已回滚）
      const user = await prisma.user.findUnique({
        where: { id: 'test-db-rollback-user' }
      });

      expect(user).toBeNull();
    });
  });

  describe('数据库性能测试', () => {
    it('应该能够快速执行批量操作', async () => {
      const startTime = Date.now();
      const batchSize = 10;
      const userIds: string[] = [];

      try {
        // 批量创建用户
        for (let i = 0; i < batchSize; i++) {
          const userId = `test-db-batch-${i}`;
          userIds.push(userId);
          
          await prisma.user.create({
            data: {
              id: userId,
              email: `test-db-batch-${i}@example.com`,
              nickname: `Batch User ${i}`,
              passwordHash: '$2b$12$hashedpasswordfortesting',
              emailVerified: false,
              isActive: true,
              isLocked: false
            }
          });
        }

        const endTime = Date.now();
        const duration = endTime - startTime;

        // 批量操作应该在合理时间内完成（5秒内）
        expect(duration).toBeLessThan(5000);

        // 验证所有用户都被创建
        const count = await prisma.user.count({
          where: {
            id: {
              in: userIds
            }
          }
        });

        expect(count).toBe(batchSize);

      } finally {
        // 清理测试数据
        await prisma.user.deleteMany({
          where: {
            id: {
              in: userIds
            }
          }
        });
      }
    });
  });
});
