/**
 * OpenID Connect Provider 测试
 * 测试OIDC核心功能的实现
 */

import { oidcService } from '@/services/oidc.service';
import { oidcClientService } from '@/services/oidc-client.service';
import { prisma } from '@/config/database';
import { hashPassword } from '@/utils/password';

describe('OIDC Provider 测试', () => {
  let testUser: any;
  let testClient: any;

  beforeAll(async () => {
    // 创建测试用户
    testUser = await prisma.user.create({
      data: {
        id: 'test-user-oidc',
        email: '<EMAIL>',
        username: 'oidctest',
        passwordHash: await hashPassword('TestPassword123!'),
        nickname: 'OIDC Test User',
        firstName: 'OIDC',
        lastName: 'Test',
        emailVerified: true,
        isActive: true
      }
    });

    // 创建测试客户端
    testClient = await oidcClientService.createClient({
      name: 'Test OIDC Client',
      description: 'Test client for OIDC functionality',
      redirectUris: ['https://example.com/callback'],
      grantTypes: ['authorization_code', 'refresh_token', 'client_credentials'],
      responseTypes: ['code', 'token', 'id_token'],
      scopes: ['openid', 'profile', 'email', 'api']
    });
  });

  afterAll(async () => {
    // 清理测试数据
    await prisma.user.delete({ where: { id: testUser.id } });
    await prisma.oAuthClient.delete({ where: { id: testClient.id } });
  });

  describe('客户端验证', () => {
    it('应该能够验证有效的客户端', async () => {
      const client = await oidcService.validateClient(testClient.clientId);
      expect(client).toBeTruthy();
      expect(client?.clientId).toBe(testClient.clientId);
    });

    it('应该拒绝无效的客户端', async () => {
      const client = await oidcService.validateClient('invalid-client-id');
      expect(client).toBeNull();
    });
  });

  describe('授权码流程', () => {
    it('应该能够生成授权码', async () => {
      const code = await oidcService.generateAuthorizationCode(
        testUser.id,
        testClient.clientId,
        testClient.redirectUris[0],
        ['openid', 'profile'],
        undefined,
        undefined
      );

      expect(code).toBeTruthy();
      expect(typeof code).toBe('string');
      expect(code.length).toBeGreaterThan(10);
    });

    it('应该能够交换授权码获取令牌', async () => {
      // 生成授权码
      const code = await oidcService.generateAuthorizationCode(
        testUser.id,
        testClient.clientId,
        testClient.redirectUris[0],
        ['openid', 'profile', 'email']
      );

      // 交换令牌
      const tokens = await oidcService.exchangeCodeForTokens(
        code,
        testClient.clientId,
        testClient.clientSecret,
        testClient.redirectUris[0]
      );

      expect(tokens).toBeTruthy();
      expect(tokens.access_token).toBeTruthy();
      expect(tokens.refresh_token).toBeTruthy();
      expect(tokens.id_token).toBeTruthy();
      expect(tokens.token_type).toBe('Bearer');
      expect(tokens.expires_in).toBe(900);
    });
  });

  describe('刷新令牌流程', () => {
    it('应该能够使用刷新令牌获取新的访问令牌', async () => {
      // 首先获取初始令牌
      const code = await oidcService.generateAuthorizationCode(
        testUser.id,
        testClient.clientId,
        testClient.redirectUris[0],
        ['openid', 'profile']
      );

      const initialTokens = await oidcService.exchangeCodeForTokens(
        code,
        testClient.clientId,
        testClient.clientSecret,
        testClient.redirectUris[0]
      );

      // 使用刷新令牌获取新令牌
      const refreshedTokens = await oidcService.refreshTokens(
        initialTokens.refresh_token!,
        testClient.clientId,
        testClient.clientSecret
      );

      expect(refreshedTokens).toBeTruthy();
      expect(refreshedTokens.access_token).toBeTruthy();
      expect(refreshedTokens.refresh_token).toBeTruthy();
      expect(refreshedTokens.access_token).not.toBe(initialTokens.access_token);
    });
  });

  describe('客户端凭据流程', () => {
    it('应该能够使用客户端凭据获取访问令牌', async () => {
      const tokens = await oidcService.clientCredentialsGrant(
        testClient.clientId,
        testClient.clientSecret,
        'api'
      );

      expect(tokens).toBeTruthy();
      expect(tokens.access_token).toBeTruthy();
      expect(tokens.token_type).toBe('Bearer');
      expect(tokens.scope).toBe('api');
      expect(tokens.refresh_token).toBeUndefined(); // 客户端凭据流程不返回刷新令牌
    });

    it('应该拒绝无效的客户端凭据', async () => {
      await expect(
        oidcService.clientCredentialsGrant(
          testClient.clientId,
          'invalid-secret',
          'api'
        )
      ).rejects.toThrow('无效的客户端凭据');
    });
  });

  describe('令牌撤销', () => {
    it('应该能够撤销访问令牌', async () => {
      // 获取令牌
      const tokens = await oidcService.clientCredentialsGrant(
        testClient.clientId,
        testClient.clientSecret,
        'api'
      );

      // 撤销令牌
      await expect(
        oidcService.revokeToken(
          tokens.access_token,
          testClient.clientId,
          testClient.clientSecret
        )
      ).resolves.not.toThrow();
    });
  });

  describe('隐式流程', () => {
    it('应该能够处理token响应类型', async () => {
      const callbackUrl = await oidcService.handleImplicitFlow({
        response_type: 'token',
        client_id: testClient.clientId,
        redirect_uri: testClient.redirectUris[0],
        scope: 'openid profile',
        state: 'test-state'
      }, testUser.id);

      expect(callbackUrl).toBeTruthy();
      expect(callbackUrl).toContain('access_token=');
      expect(callbackUrl).toContain('token_type=Bearer');
      expect(callbackUrl).toContain('state=test-state');
    });

    it('应该能够处理id_token响应类型', async () => {
      const callbackUrl = await oidcService.handleImplicitFlow({
        response_type: 'id_token',
        client_id: testClient.clientId,
        redirect_uri: testClient.redirectUris[0],
        scope: 'openid profile',
        state: 'test-state'
      }, testUser.id);

      expect(callbackUrl).toBeTruthy();
      expect(callbackUrl).toContain('id_token=');
      expect(callbackUrl).toContain('state=test-state');
    });
  });

  describe('用户信息端点', () => {
    it('应该能够获取用户信息', async () => {
      // 获取访问令牌
      const tokens = await oidcService.clientCredentialsGrant(
        testClient.clientId,
        testClient.clientSecret,
        'api'
      );

      // 注意：客户端凭据流程的令牌不能用于用户信息端点
      // 这里我们需要使用授权码流程获取的令牌
      const code = await oidcService.generateAuthorizationCode(
        testUser.id,
        testClient.clientId,
        testClient.redirectUris[0],
        ['openid', 'profile', 'email']
      );

      const userTokens = await oidcService.exchangeCodeForTokens(
        code,
        testClient.clientId,
        testClient.clientSecret,
        testClient.redirectUris[0]
      );

      const userInfo = await oidcService.getUserInfo(userTokens.access_token);

      expect(userInfo).toBeTruthy();
      expect(userInfo.sub).toBe(testUser.id);
      expect(userInfo.email).toBe(testUser.email);
    });
  });
});
