/**
 * 认证系统集成测试
 * 测试认证流程的端到端功能
 */

import request from 'supertest';
import { app } from '@/index';
import { prisma } from '@/config/database';
import { redisService } from '@/services/redis.service';
import bcrypt from 'bcrypt';

describe('认证系统集成测试', () => {
  let testUser: any;
  let authToken: string;

  beforeAll(async () => {
    // 创建测试用户
    const hashedPassword = await bcrypt.hash('password123', 10);
    testUser = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        nickname: 'Integration Test User',
        hashedPassword,
        isEmailVerified: true
      }
    });
  });

  afterAll(async () => {
    // 清理测试数据
    if (testUser) {
      await prisma.session.deleteMany({
        where: { userId: testUser.id }
      });
      await prisma.user.delete({
        where: { id: testUser.id }
      });
    }
    
    // 清理Redis缓存
    await redisService.flushdb();
    
    // 断开数据库连接
    await prisma.$disconnect();
  });

  describe('用户注册流程', () => {
    test('应该成功注册新用户', async () => {
      const registerData = {
        email: '<EMAIL>',
        password: 'password123',
        nickname: 'New Integration User'
      };

      const response = await request(app)
        .post('/api/v1/auth/register')
        .send(registerData)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.data.user.email).toBe(registerData.email);
      expect(response.body.data.user.nickname).toBe(registerData.nickname);
      expect(response.body.data.accessToken).toBeDefined();
      expect(response.body.data.tokenType).toBe('Bearer');

      // 验证用户已创建到数据库
      const createdUser = await prisma.user.findUnique({
        where: { email: registerData.email }
      });
      expect(createdUser).toBeTruthy();
      expect(createdUser?.isEmailVerified).toBe(false);

      // 清理测试用户
      if (createdUser) {
        await prisma.user.delete({
          where: { id: createdUser.id }
        });
      }
    });

    test('应该拒绝重复邮箱注册', async () => {
      const registerData = {
        email: testUser.email,
        password: 'password123',
        nickname: 'Duplicate User'
      };

      const response = await request(app)
        .post('/api/v1/auth/register')
        .send(registerData)
        .expect(409);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe('EMAIL_EXISTS');
    });

    test('应该验证注册数据格式', async () => {
      const invalidData = {
        email: 'invalid-email',
        password: '123',
        nickname: ''
      };

      const response = await request(app)
        .post('/api/v1/auth/register')
        .send(invalidData)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe('VALIDATION_ERROR');
      expect(response.body.details).toBeInstanceOf(Array);
    });
  });

  describe('用户登录流程', () => {
    test('应该成功登录有效用户', async () => {
      const loginData = {
        username: testUser.email,
        password: 'password123'
      };

      const response = await request(app)
        .post('/api/v1/auth/login')
        .send(loginData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.user.email).toBe(testUser.email);
      expect(response.body.data.accessToken).toBeDefined();
      expect(response.body.data.session).toBeDefined();

      // 保存token用于后续测试
      authToken = response.body.data.accessToken;

      // 验证会话已创建
      const session = await prisma.session.findFirst({
        where: { userId: testUser.id, isActive: true }
      });
      expect(session).toBeTruthy();

      // 验证刷新令牌cookie
      const cookies = response.headers['set-cookie'];
      expect(cookies).toBeDefined();
      expect(cookies.some((cookie: string) => cookie.includes('refreshToken'))).toBe(true);
    });

    test('应该拒绝错误密码', async () => {
      const loginData = {
        username: testUser.email,
        password: 'wrongpassword'
      };

      const response = await request(app)
        .post('/api/v1/auth/login')
        .send(loginData)
        .expect(401);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe('INVALID_CREDENTIALS');
    });

    test('应该拒绝不存在的用户', async () => {
      const loginData = {
        username: '<EMAIL>',
        password: 'password123'
      };

      const response = await request(app)
        .post('/api/v1/auth/login')
        .send(loginData)
        .expect(401);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe('INVALID_CREDENTIALS');
    });
  });

  describe('受保护的路由访问', () => {
    test('应该允许有效token访问用户资料', async () => {
      const response = await request(app)
        .get('/api/v1/auth/profile')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.email).toBe(testUser.email);
      expect(response.body.data.nickname).toBe(testUser.nickname);
    });

    test('应该拒绝无token的请求', async () => {
      const response = await request(app)
        .get('/api/v1/auth/profile')
        .expect(401);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe('UNAUTHORIZED');
    });

    test('应该拒绝无效token', async () => {
      const response = await request(app)
        .get('/api/v1/auth/profile')
        .set('Authorization', 'Bearer invalid-token')
        .expect(401);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe('UNAUTHORIZED');
    });
  });

  describe('用户资料更新', () => {
    test('应该成功更新用户资料', async () => {
      const updateData = {
        nickname: 'Updated Integration User',
        avatar: 'https://example.com/new-avatar.jpg'
      };

      const response = await request(app)
        .put('/api/v1/auth/profile')
        .set('Authorization', `Bearer ${authToken}`)
        .send(updateData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.nickname).toBe(updateData.nickname);
      expect(response.body.data.avatar).toBe(updateData.avatar);

      // 验证数据库中的更新
      const updatedUser = await prisma.user.findUnique({
        where: { id: testUser.id }
      });
      expect(updatedUser?.nickname).toBe(updateData.nickname);
      expect(updatedUser?.avatar).toBe(updateData.avatar);
    });

    test('应该验证更新数据', async () => {
      const invalidData = {
        nickname: '', // 空昵称
        email: 'invalid-email' // 不应该允许更新邮箱
      };

      const response = await request(app)
        .put('/api/v1/auth/profile')
        .set('Authorization', `Bearer ${authToken}`)
        .send(invalidData)
        .expect(400);

      expect(response.body.success).toBe(false);
    });
  });

  describe('令牌刷新流程', () => {
    let refreshToken: string;

    beforeAll(async () => {
      // 获取刷新令牌
      const loginResponse = await request(app)
        .post('/api/v1/auth/login')
        .send({
          username: testUser.email,
          password: 'password123'
        });

      const cookies = loginResponse.headers['set-cookie'];
      const refreshCookie = cookies.find((cookie: string) => 
        cookie.includes('refreshToken')
      );
      
      if (refreshCookie) {
        refreshToken = refreshCookie.split('=')[1].split(';')[0];
      }
    });

    test('应该成功刷新访问令牌', async () => {
      const response = await request(app)
        .post('/api/v1/auth/refresh')
        .set('Cookie', `refreshToken=${refreshToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.accessToken).toBeDefined();
      expect(response.body.data.tokenType).toBe('Bearer');
      expect(response.body.data.expiresIn).toBeDefined();

      // 验证新的刷新令牌cookie
      const cookies = response.headers['set-cookie'];
      expect(cookies).toBeDefined();
      expect(cookies.some((cookie: string) => cookie.includes('refreshToken'))).toBe(true);
    });

    test('应该拒绝无效的刷新令牌', async () => {
      const response = await request(app)
        .post('/api/v1/auth/refresh')
        .set('Cookie', 'refreshToken=invalid-token')
        .expect(401);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe('INVALID_TOKEN');
    });

    test('应该拒绝缺失的刷新令牌', async () => {
      const response = await request(app)
        .post('/api/v1/auth/refresh')
        .expect(401);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe('UNAUTHORIZED');
    });
  });

  describe('用户登出流程', () => {
    test('应该成功登出用户', async () => {
      const response = await request(app)
        .post('/api/v1/auth/logout')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.message).toBeDefined();

      // 验证刷新令牌cookie被清除
      const cookies = response.headers['set-cookie'];
      expect(cookies).toBeDefined();
      expect(cookies.some((cookie: string) => 
        cookie.includes('refreshToken=') && cookie.includes('Max-Age=0')
      )).toBe(true);

      // 验证会话被标记为非活跃
      const sessions = await prisma.session.findMany({
        where: { userId: testUser.id, isActive: true }
      });
      expect(sessions.length).toBe(0);
    });

    test('登出后应该无法访问受保护路由', async () => {
      const response = await request(app)
        .get('/api/v1/auth/profile')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(401);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe('UNAUTHORIZED');
    });
  });

  describe('速率限制测试', () => {
    test('应该在超过限制时拒绝请求', async () => {
      const loginData = {
        username: '<EMAIL>',
        password: 'wrongpassword'
      };

      // 快速发送多个请求以触发速率限制
      const requests = Array(10).fill(null).map(() =>
        request(app)
          .post('/api/v1/auth/login')
          .send(loginData)
      );

      const responses = await Promise.all(requests);
      
      // 至少有一个请求应该被速率限制拒绝
      const rateLimitedResponses = responses.filter(res => res.status === 429);
      expect(rateLimitedResponses.length).toBeGreaterThan(0);
    });
  });

  describe('安全头部测试', () => {
    test('应该包含安全相关的HTTP头部', async () => {
      const response = await request(app)
        .get('/api/v1/auth/profile')
        .set('Authorization', `Bearer ${authToken}`);

      // 检查安全头部
      expect(response.headers['x-content-type-options']).toBe('nosniff');
      expect(response.headers['x-frame-options']).toBe('DENY');
      expect(response.headers['x-xss-protection']).toBe('1; mode=block');
      expect(response.headers['strict-transport-security']).toBeDefined();
    });
  });
});
