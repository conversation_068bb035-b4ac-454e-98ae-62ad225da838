/**
 * 核心功能验证测试
 * 测试身份提供商的核心业务功能
 */

import { hashPassword, verifyPassword, checkPasswordStrength } from '../utils/password';
import { generateTokenPair, verifyToken } from '../utils/jwt';
import { validateEmail, validatePassword } from '../utils/validation';

describe('密码处理功能测试', () => {
  describe('密码加密和验证', () => {
    it('应该能够正确加密密码', async () => {
      const password = 'TestPassword123!';
      const hashedPassword = await hashPassword(password);

      expect(hashedPassword).toBeDefined();
      expect(hashedPassword).not.toBe(password);
      expect(hashedPassword.length).toBeGreaterThan(50); // bcrypt哈希长度
    });

    it('应该能够验证正确的密码', async () => {
      const password = 'TestPassword123!';
      const hashedPassword = await hashPassword(password);

      const isValid = await verifyPassword(password, hashedPassword);
      expect(isValid).toBe(true);
    });

    it('应该拒绝错误的密码', async () => {
      const password = 'TestPassword123!';
      const wrongPassword = 'WrongPassword123!';
      const hashedPassword = await hashPassword(password);

      const isValid = await verifyPassword(wrongPassword, hashedPassword);
      expect(isValid).toBe(false);
    });
  });

  describe('密码强度检查', () => {
    it('应该接受强密码', () => {
      const strongPasswords = [
        'StrongPassword123!',
        'MySecure@Pass2024',
        'Complex#Password99'
      ];

      strongPasswords.forEach(password => {
        const result = checkPasswordStrength(password);
        expect(result.isStrong).toBe(true);
        expect(result.score).toBeGreaterThanOrEqual(80);
      });
    });

    it('应该拒绝弱密码', () => {
      const weakPasswords = [
        '123456',
        'password',
        'abc123',
        'qwerty'
      ];

      weakPasswords.forEach(password => {
        const result = checkPasswordStrength(password);
        expect(result.isStrong).toBe(false);
        expect(result.score).toBeLessThan(80);
      });
    });
  });
});
describe('JWT令牌功能测试', () => {
  describe('令牌生成和验证', () => {
    it('应该能够生成有效的JWT令牌对', () => {
      const mockUser = {
        id: 'test-user-123',
        email: '<EMAIL>',
        phone: '+1234567890',
        username: 'testuser',
        passwordHash: 'hashed-password',
        nickname: 'Test User',
        firstName: 'Test',
        lastName: 'User',
        avatar: null,
        emailVerified: true,
        phoneVerified: false,
        isActive: true,
        isLocked: false,
        lockReason: null,
        lastLoginAt: new Date(),
        lastLoginIp: '127.0.0.1',
        passwordChangedAt: new Date(),
        createdAt: new Date(),
        updatedAt: new Date()
      };

      const tokens = generateTokenPair(mockUser, 'session-123');

      expect(tokens).toHaveProperty('accessToken');
      expect(tokens).toHaveProperty('refreshToken');
      expect(tokens.accessToken).toBeDefined();
      expect(tokens.refreshToken).toBeDefined();
    });

    it('应该能够验证有效的访问令牌', () => {
      const mockUser = {
        id: 'test-user-123',
        email: '<EMAIL>',
        phone: '+1234567890',
        username: 'testuser',
        passwordHash: 'hashed-password',
        nickname: 'Test User',
        firstName: 'Test',
        lastName: 'User',
        avatar: null,
        emailVerified: true,
        phoneVerified: false,
        isActive: true,
        isLocked: false,
        lockReason: null,
        lastLoginAt: new Date(),
        lastLoginIp: '127.0.0.1',
        passwordChangedAt: new Date(),
        createdAt: new Date(),
        updatedAt: new Date()
      };

      const tokens = generateTokenPair(mockUser, 'session-123');
      const decoded = verifyToken(tokens.accessToken);

      expect(decoded).toHaveProperty('userId');
      expect(decoded).toHaveProperty('email');
      expect(decoded.userId).toBe(mockUser.id);
      expect(decoded.email).toBe(mockUser.email);
    });

    it('应该拒绝无效的令牌', () => {
      const invalidToken = 'invalid.jwt.token';

      expect(() => {
        verifyToken(invalidToken);
      }).toThrow();
    });
  });
});

describe('数据验证功能测试', () => {
  describe('邮箱验证', () => {
    it('应该接受有效的邮箱地址', () => {
      const validEmails = [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>'
      ];

      validEmails.forEach(email => {
        expect(validateEmail(email)).toBe(true);
      });
    });

    it('应该拒绝无效的邮箱地址', () => {
      const invalidEmails = [
        'invalid-email',
        '@domain.com',
        'user@',
        '<EMAIL>',
        'user@domain',
        ''
      ];

      invalidEmails.forEach(email => {
        expect(validateEmail(email)).toBe(false);
      });
    });
  });

  describe('密码验证', () => {
    it('应该接受符合要求的密码', () => {
      const validPasswords = [
        'StrongPassword123!',
        'MySecure@Pass2024',
        'Complex#Password99'
      ];

      validPasswords.forEach(password => {
        const result = validatePassword(password);
        expect(result.isValid).toBe(true);
        expect(result.errors).toHaveLength(0);
      });
    });

    it('应该拒绝不符合要求的密码', () => {
      const invalidPasswords = [
        '123456',        // 太短，缺少字母和特殊字符
        'password',      // 缺少数字和特殊字符
        'Password123',   // 缺少特殊字符
        'password123!'   // 缺少大写字母
      ];

      invalidPasswords.forEach(password => {
        const result = validatePassword(password);
        expect(result.isValid).toBe(false);
        expect(result.errors.length).toBeGreaterThan(0);
      });
    });
  });
});

describe('工具函数测试', () => {
  describe('UUID生成', () => {
    it('应该生成唯一的UUID', () => {
      const { v4: uuidv4 } = require('uuid');

      const uuid1 = uuidv4();
      const uuid2 = uuidv4();

      expect(uuid1).toBeDefined();
      expect(uuid2).toBeDefined();
      expect(uuid1).not.toBe(uuid2);
      expect(uuid1).toMatch(/^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i);
    });
  });

  describe('时间处理', () => {
    it('应该能够正确计算过期时间', () => {
      const now = new Date();
      const expiresIn30Minutes = new Date(now.getTime() + 30 * 60 * 1000);
      const expiresIn30Days = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000);

      expect(expiresIn30Minutes.getTime()).toBeGreaterThan(now.getTime());
      expect(expiresIn30Days.getTime()).toBeGreaterThan(expiresIn30Minutes.getTime());

      // 验证时间差
      const diffMinutes = (expiresIn30Minutes.getTime() - now.getTime()) / (1000 * 60);
      const diffDays = (expiresIn30Days.getTime() - now.getTime()) / (1000 * 60 * 60 * 24);

      expect(Math.round(diffMinutes)).toBe(30);
      expect(Math.round(diffDays)).toBe(30);
    });
  });
});


