/**
 * OAuth安全工具类测试
 */

import { OAuthSecurity, OAuthState } from '@/utils/oauth-security';
import crypto from 'crypto';

describe('OAuthSecurity', () => {
  describe('generateState', () => {
    it('应该生成64字符的十六进制状态字符串', () => {
      const state = OAuthSecurity.generateState();
      expect(state).toHaveLength(64);
      expect(/^[a-f0-9]+$/.test(state)).toBe(true);
    });

    it('应该生成唯一的状态字符串', () => {
      const state1 = OAuthSecurity.generateState();
      const state2 = OAuthSecurity.generateState();
      expect(state1).not.toBe(state2);
    });
  });

  describe('generateCodeVerifier', () => {
    it('应该生成base64url编码的代码验证器', () => {
      const codeVerifier = OAuthSecurity.generateCodeVerifier();
      expect(codeVerifier).toBeDefined();
      expect(codeVerifier.length).toBeGreaterThan(0);
      // base64url字符集测试
      expect(/^[A-Za-z0-9_-]+$/.test(codeVerifier)).toBe(true);
    });

    it('应该生成唯一的代码验证器', () => {
      const verifier1 = OAuthSecurity.generateCodeVerifier();
      const verifier2 = OAuthSecurity.generateCodeVerifier();
      expect(verifier1).not.toBe(verifier2);
    });
  });

  describe('generateCodeChallenge', () => {
    it('应该为给定的代码验证器生成正确的代码挑战', () => {
      const codeVerifier = 'test-code-verifier';
      const challenge = OAuthSecurity.generateCodeChallenge(codeVerifier);
      
      // 手动计算期望的挑战值
      const expectedChallenge = crypto
        .createHash('sha256')
        .update(codeVerifier)
        .digest('base64url');
      
      expect(challenge).toBe(expectedChallenge);
    });

    it('应该为相同的代码验证器生成相同的挑战', () => {
      const codeVerifier = 'consistent-verifier';
      const challenge1 = OAuthSecurity.generateCodeChallenge(codeVerifier);
      const challenge2 = OAuthSecurity.generateCodeChallenge(codeVerifier);
      expect(challenge1).toBe(challenge2);
    });
  });

  describe('createOAuthState', () => {
    it('应该创建包含所有必需字段的OAuth状态对象', () => {
      const provider = 'google';
      const redirectUrl = 'https://example.com/callback';
      const state = OAuthSecurity.createOAuthState(provider, redirectUrl);

      expect(state).toHaveProperty('state');
      expect(state).toHaveProperty('codeVerifier');
      expect(state).toHaveProperty('timestamp');
      expect(state).toHaveProperty('provider', provider);
      expect(state).toHaveProperty('redirectUrl', redirectUrl);
      expect(typeof state.timestamp).toBe('number');
    });

    it('应该创建不带重定向URL的OAuth状态对象', () => {
      const provider = 'github';
      const state = OAuthSecurity.createOAuthState(provider);

      expect(state.provider).toBe(provider);
      expect(state.redirectUrl).toBeUndefined();
    });
  });

  describe('validateState', () => {
    it('应该验证有效的状态参数', () => {
      const state: OAuthState = {
        state: 'valid-state',
        codeVerifier: 'verifier',
        timestamp: Date.now(),
        provider: 'google'
      };

      const result = OAuthSecurity.validateState('valid-state', state);
      expect(result.isValid).toBe(true);
      expect(result.reason).toBeUndefined();
    });

    it('应该拒绝不匹配的状态参数', () => {
      const state: OAuthState = {
        state: 'expected-state',
        codeVerifier: 'verifier',
        timestamp: Date.now(),
        provider: 'google'
      };

      const result = OAuthSecurity.validateState('wrong-state', state);
      expect(result.isValid).toBe(false);
      expect(result.reason).toBe('state_mismatch');
    });

    it('应该拒绝过期的状态参数', () => {
      const state: OAuthState = {
        state: 'valid-state',
        codeVerifier: 'verifier',
        timestamp: Date.now() - 11 * 60 * 1000, // 11分钟前
        provider: 'google'
      };

      const result = OAuthSecurity.validateState('valid-state', state);
      expect(result.isValid).toBe(false);
      expect(result.reason).toBe('state_expired');
    });
  });

  describe('validateCodeVerifier', () => {
    it('应该验证正确的代码验证器', () => {
      const codeVerifier = 'test-verifier';
      const codeChallenge = OAuthSecurity.generateCodeChallenge(codeVerifier);
      
      const isValid = OAuthSecurity.validateCodeVerifier(codeVerifier, codeChallenge);
      expect(isValid).toBe(true);
    });

    it('应该拒绝错误的代码验证器', () => {
      const correctVerifier = 'correct-verifier';
      const wrongVerifier = 'wrong-verifier';
      const codeChallenge = OAuthSecurity.generateCodeChallenge(correctVerifier);
      
      const isValid = OAuthSecurity.validateCodeVerifier(wrongVerifier, codeChallenge);
      expect(isValid).toBe(false);
    });
  });

  describe('generateSecureRandom', () => {
    it('应该生成指定长度的随机字符串', () => {
      const length = 16;
      const random = OAuthSecurity.generateSecureRandom(length);
      expect(random).toHaveLength(length * 2); // hex编码，每字节2个字符
    });

    it('应该生成默认长度的随机字符串', () => {
      const random = OAuthSecurity.generateSecureRandom();
      expect(random).toHaveLength(64); // 32字节 * 2
    });
  });

  describe('HMAC签名', () => {
    const testData = 'test-data';
    const testSecret = 'test-secret';

    it('应该创建HMAC签名', () => {
      const signature = OAuthSecurity.createHMACSignature(testData, testSecret);
      expect(signature).toBeDefined();
      expect(signature.length).toBeGreaterThan(0);
      expect(/^[a-f0-9]+$/.test(signature)).toBe(true);
    });

    it('应该验证正确的HMAC签名', () => {
      const signature = OAuthSecurity.createHMACSignature(testData, testSecret);
      const isValid = OAuthSecurity.verifyHMACSignature(testData, signature, testSecret);
      expect(isValid).toBe(true);
    });

    it('应该拒绝错误的HMAC签名', () => {
      const signature = OAuthSecurity.createHMACSignature(testData, testSecret);
      const isValid = OAuthSecurity.verifyHMACSignature(testData, 'wrong-signature', testSecret);
      expect(isValid).toBe(false);
    });

    it('应该拒绝使用错误密钥的签名', () => {
      const signature = OAuthSecurity.createHMACSignature(testData, testSecret);
      const isValid = OAuthSecurity.verifyHMACSignature(testData, signature, 'wrong-secret');
      expect(isValid).toBe(false);
    });
  });

  describe('validateCallbackUrl', () => {
    const allowedDomains = ['example.com', '*.trusted.com', 'localhost'];

    it('应该验证允许域名的HTTPS URL', () => {
      const url = 'https://example.com/callback';
      const isValid = OAuthSecurity.validateCallbackUrl(url, allowedDomains);
      expect(isValid).toBe(true);
    });

    it('应该验证通配符子域名', () => {
      const url = 'https://api.trusted.com/callback';
      const isValid = OAuthSecurity.validateCallbackUrl(url, allowedDomains);
      expect(isValid).toBe(true);
    });

    it('应该在开发环境允许HTTP', () => {
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = 'development';
      
      const url = 'http://localhost:3000/callback';
      const isValid = OAuthSecurity.validateCallbackUrl(url, allowedDomains);
      expect(isValid).toBe(true);
      
      process.env.NODE_ENV = originalEnv;
    });

    it('应该拒绝不在允许列表中的域名', () => {
      const url = 'https://malicious.com/callback';
      const isValid = OAuthSecurity.validateCallbackUrl(url, allowedDomains);
      expect(isValid).toBe(false);
    });

    it('应该拒绝无效的URL格式', () => {
      const url = 'not-a-valid-url';
      const isValid = OAuthSecurity.validateCallbackUrl(url, allowedDomains);
      expect(isValid).toBe(false);
    });
  });

  describe('validateProviderName', () => {
    it('应该验证支持的OAuth提供商', () => {
      const supportedProviders = ['google', 'github', 'wechat', 'weibo', 'qq', 'alipay', 'dingtalk'];
      
      supportedProviders.forEach(provider => {
        expect(OAuthSecurity.validateProviderName(provider)).toBe(true);
      });
    });

    it('应该拒绝不支持的OAuth提供商', () => {
      const unsupportedProviders = ['facebook', 'twitter', 'linkedin', 'unknown'];
      
      unsupportedProviders.forEach(provider => {
        expect(OAuthSecurity.validateProviderName(provider)).toBe(false);
      });
    });

    it('应该处理大小写不敏感的提供商名称', () => {
      expect(OAuthSecurity.validateProviderName('GOOGLE')).toBe(true);
      expect(OAuthSecurity.validateProviderName('GitHub')).toBe(true);
      expect(OAuthSecurity.validateProviderName('WeChat')).toBe(true);
    });
  });

  describe('sanitizeInput', () => {
    it('应该移除HTML特殊字符', () => {
      const input = '<script>alert("xss")</script>';
      const sanitized = OAuthSecurity.sanitizeInput(input);
      expect(sanitized).not.toContain('<');
      expect(sanitized).not.toContain('>');
    });

    it('应该移除JavaScript协议', () => {
      const input = 'javascript:alert("xss")';
      const sanitized = OAuthSecurity.sanitizeInput(input);
      expect(sanitized).not.toContain('javascript:');
    });

    it('应该移除data协议', () => {
      const input = 'data:text/html,<script>alert("xss")</script>';
      const sanitized = OAuthSecurity.sanitizeInput(input);
      expect(sanitized).not.toContain('data:');
    });

    it('应该处理空输入', () => {
      expect(OAuthSecurity.sanitizeInput('')).toBe('');
      expect(OAuthSecurity.sanitizeInput(null as any)).toBe('');
      expect(OAuthSecurity.sanitizeInput(undefined as any)).toBe('');
    });

    it('应该修剪空白字符', () => {
      const input = '  valid input  ';
      const sanitized = OAuthSecurity.sanitizeInput(input);
      expect(sanitized).toBe('valid input');
    });
  });

  describe('CSRF令牌', () => {
    it('应该生成CSRF令牌', () => {
      const token = OAuthSecurity.generateCSRFToken();
      expect(token).toBeDefined();
      expect(token).toHaveLength(64); // 32字节 * 2
      expect(/^[a-f0-9]+$/.test(token)).toBe(true);
    });

    it('应该验证正确的CSRF令牌', () => {
      const token = OAuthSecurity.generateCSRFToken();
      const isValid = OAuthSecurity.validateCSRFToken(token, token);
      expect(isValid).toBe(true);
    });

    it('应该拒绝错误的CSRF令牌', () => {
      const token1 = OAuthSecurity.generateCSRFToken();
      const token2 = OAuthSecurity.generateCSRFToken();
      const isValid = OAuthSecurity.validateCSRFToken(token1, token2);
      expect(isValid).toBe(false);
    });

    it('应该处理空令牌', () => {
      const token = OAuthSecurity.generateCSRFToken();
      expect(OAuthSecurity.validateCSRFToken('', token)).toBe(false);
      expect(OAuthSecurity.validateCSRFToken(token, '')).toBe(false);
      expect(OAuthSecurity.validateCSRFToken('', '')).toBe(false);
    });
  });
});
