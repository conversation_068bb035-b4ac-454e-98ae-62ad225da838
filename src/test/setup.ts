/**
 * 测试环境设置
 * 配置Jest测试环境和数据库
 */

import dotenv from 'dotenv';
import path from 'path';

// 加载测试环境配置
dotenv.config({ path: path.resolve(process.cwd(), '.env.test') });

// 设置测试环境变量
process.env['NODE_ENV'] = 'test';
process.env['LOG_LEVEL'] = 'error';

// 测试数据库URL（使用SQLite内存数据库）
if (!process.env['DATABASE_URL']) {
  process.env['DATABASE_URL'] = 'file:./test.db';
}

// Redis测试配置
if (!process.env['REDIS_DB']) {
  process.env['REDIS_DB'] = '2';
}
if (!process.env['REDIS_KEY_PREFIX']) {
  process.env['REDIS_KEY_PREFIX'] = 'idp:test:';
}

// 测试JWT密钥
if (!process.env['JWT_SECRET']) {
  process.env['JWT_SECRET'] = 'test-jwt-secret-key-for-testing-only';
}

if (!process.env['JWT_REFRESH_SECRET']) {
  process.env['JWT_REFRESH_SECRET'] = 'test-jwt-refresh-secret-key-for-testing-only';
}

// 全局测试超时
jest.setTimeout(30000);

// 测试前后钩子
beforeAll(async () => {
  // 测试开始前的设置
});

afterAll(async () => {
  // 测试结束后的清理
});

beforeEach(async () => {
  // 每个测试前的设置
});

afterEach(async () => {
  // 每个测试后的清理
  jest.clearAllMocks();
});

// 全局测试工具函数
declare global {
  function createMockRequest(overrides?: any): any;
  function createMockResponse(overrides?: any): any;
  function createMockNext(): any;
  function createTestUser(overrides?: any): any;
  function createTestSession(overrides?: any): any;
  function createTestRole(overrides?: any): any;
  function createTestApplication(overrides?: any): any;
}

global.createMockRequest = (overrides = {}) => ({
  body: {},
  params: {},
  query: {},
  headers: {},
  cookies: {},
  user: undefined,
  ip: '127.0.0.1',
  method: 'GET',
  path: '/test',
  get: jest.fn().mockReturnValue('test-user-agent'),
  ...overrides,
});

global.createMockResponse = (overrides = {}) => ({
  status: jest.fn().mockReturnThis(),
  json: jest.fn().mockReturnThis(),
  send: jest.fn().mockReturnThis(),
  cookie: jest.fn().mockReturnThis(),
  clearCookie: jest.fn().mockReturnThis(),
  redirect: jest.fn().mockReturnThis(),
  render: jest.fn().mockReturnThis(),
  ...overrides,
});

global.createMockNext = () => jest.fn();

// 测试数据工厂
global.createTestUser = (overrides = {}) => ({
  id: 'test-user-id',
  email: '<EMAIL>',
  nickname: 'Test User',
  hashedPassword: 'hashed-password',
  isEmailVerified: true,
  avatar: null,
  createdAt: new Date(),
  updatedAt: new Date(),
  lastLoginAt: new Date(),
  ...overrides,
});

global.createTestSession = (overrides = {}) => ({
  id: 'test-session-id',
  userId: 'test-user-id',
  token: 'test-session-token',
  isActive: true,
  expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000),
  ipAddress: '127.0.0.1',
  userAgent: 'test-user-agent',
  createdAt: new Date(),
  updatedAt: new Date(),
  ...overrides,
});

global.createTestRole = (overrides = {}) => ({
  id: 'test-role-id',
  name: 'user',
  description: 'Regular user role',
  createdAt: new Date(),
  updatedAt: new Date(),
  ...overrides,
});

global.createTestApplication = (overrides = {}) => ({
  id: 'test-app-id',
  name: 'Test Application',
  clientId: 'test-client-id',
  clientSecret: 'test-client-secret',
  redirectUris: ['http://localhost:3000/callback'],
  scopes: ['read', 'write'],
  isActive: true,
  createdAt: new Date(),
  updatedAt: new Date(),
  ...overrides,
});
