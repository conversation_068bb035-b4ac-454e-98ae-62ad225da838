/**
 * 联邦式组织架构管理系统测试
 * 测试多应用组织架构同步、映射和冲突解决功能
 */

import { describe, test, expect, beforeAll, afterAll, beforeEach } from '@jest/testing-library';
import { prisma } from '@/config/database';
import { federatedOrganizationService } from '@/services/federated-organization.service';
import { organizationService } from '@/services/organization.service';

describe('联邦式组织架构管理系统测试', () => {
  let testApp1: any;
  let testApp2: any;
  let testUser: any;
  let standardOrg1: any;
  let standardOrg2: any;

  beforeAll(async () => {
    // 创建测试用户
    testUser = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        username: 'fedtestuser',
        firstName: 'Fed',
        lastName: 'Test',
        passwordHash: 'hashed_password'
      }
    });

    // 创建测试应用
    testApp1 = await prisma.application.create({
      data: {
        name: 'test-app-1',
        displayName: '测试应用1',
        description: '联邦测试应用1',
        clientId: 'test-client-1',
        clientSecret: 'test-secret-1',
        redirectUris: ['http://localhost:3001/callback'],
        status: 'active',
        createdBy: testUser.id
      }
    });

    testApp2 = await prisma.application.create({
      data: {
        name: 'test-app-2',
        displayName: '测试应用2',
        description: '联邦测试应用2',
        clientId: 'test-client-2',
        clientSecret: 'test-secret-2',
        redirectUris: ['http://localhost:3002/callback'],
        status: 'active',
        createdBy: testUser.id
      }
    });

    // 创建标准组织架构
    standardOrg1 = await organizationService.createOrganization({
      name: 'engineering',
      displayName: '工程部',
      description: '技术研发部门',
      type: 'division',
      status: 'active',
      metadata: {},
      permissionInheritance: true,
      dataIsolationLevel: 'inherit',
      settings: {}
    }, testUser.id);

    standardOrg2 = await organizationService.createOrganization({
      name: 'backend',
      displayName: '后端团队',
      description: '后端开发团队',
      parentId: standardOrg1.id,
      type: 'team',
      status: 'active',
      metadata: {},
      permissionInheritance: true,
      dataIsolationLevel: 'inherit',
      settings: {}
    }, testUser.id);
  });

  afterAll(async () => {
    // 清理测试数据
    await prisma.federatedPermissionCache.deleteMany({});
    await prisma.federationSyncLog.deleteMany({});
    await prisma.mappingConflict.deleteMany({});
    await prisma.organizationMapping.deleteMany({});
    await prisma.applicationOrgRegistry.deleteMany({});
    await prisma.organizationMember.deleteMany({});
    await prisma.organization.deleteMany({});
    await prisma.application.deleteMany({});
    await prisma.user.deleteMany({
      where: { email: '<EMAIL>' }
    });
  });

  describe('应用组织架构注册', () => {
    test('应该能够注册应用组织架构', async () => {
      const appOrganizations = [
        {
          id: 'app1-tech-dept',
          name: 'technology',
          displayName: '技术部',
          type: 'department',
          members: [
            {
              userId: testUser.id,
              role: 'manager',
              permissions: ['read:code', 'write:code']
            }
          ]
        },
        {
          id: 'app1-backend-team',
          name: 'backend_dev',
          displayName: '后端开发',
          parentId: 'app1-tech-dept',
          type: 'team',
          members: [
            {
              userId: testUser.id,
              role: 'developer',
              permissions: ['read:code', 'write:code', 'deploy:staging']
            }
          ]
        }
      ];

      const result = await federatedOrganizationService.registerApplicationOrganization(
        testApp1.id,
        appOrganizations
      );

      expect(result.syncedCount).toBe(2);
      expect(result.newMappings).toBeGreaterThan(0);
      expect(result.conflicts).toEqual([]);
    });

    test('应该能够处理相似组织的智能映射', async () => {
      const appOrganizations = [
        {
          id: 'app2-eng-dept',
          name: 'engineering_dept',
          displayName: '工程部门',
          type: 'division',
          members: []
        },
        {
          id: 'app2-backend-group',
          name: 'backend_group',
          displayName: '后端组',
          parentId: 'app2-eng-dept',
          type: 'team',
          members: []
        }
      ];

      const result = await federatedOrganizationService.registerApplicationOrganization(
        testApp2.id,
        appOrganizations
      );

      expect(result.syncedCount).toBe(2);
      expect(result.newMappings).toBeGreaterThan(0);

      // 验证映射关系
      const mappings = await federatedOrganizationService.getOrganizationMappings(
        testApp2.id,
        'app2-eng-dept'
      );

      expect(mappings.targets).toHaveLength(1);
      expect(mappings.confidence).toBeGreaterThan(0.7);
    });
  });

  describe('组织映射管理', () => {
    beforeEach(async () => {
      // 确保有测试映射数据
      await prisma.organizationMapping.create({
        data: {
          id: 'test-mapping-1',
          sourceOrgId: 'app1-tech-dept',
          sourceApplication: testApp1.id,
          targetOrgId: standardOrg1.id,
          mappingType: 'equivalent',
          confidence: 0.95,
          createdBy: testUser.id
        }
      });
    });

    test('应该能够查询组织映射关系', async () => {
      const mappings = await federatedOrganizationService.getOrganizationMappings(
        testApp1.id,
        'app1-tech-dept'
      );

      expect(mappings.source.id).toBe('app1-tech-dept');
      expect(mappings.targets).toHaveLength(1);
      expect(mappings.confidence).toBe(0.95);
      expect(mappings.history).toHaveLength(1);
    });

    test('应该能够获取联邦状态', async () => {
      // 创建应用注册记录
      await prisma.applicationOrgRegistry.create({
        data: {
          applicationId: testApp1.id,
          orgSchemaVersion: '1.0',
          syncEndpoint: 'http://test-app-1.com/api/orgs',
          lastSyncAt: new Date(),
          syncStatus: 'active',
          totalOrganizations: 2,
          mappedOrganizations: 2,
          conflictCount: 0,
          registeredBy: testUser.id
        }
      });

      const status = await federatedOrganizationService.getFederationStatus(testApp1.id);

      expect(status.isRegistered).toBe(true);
      expect(status.syncStatus).toBe('active');
      expect(status.totalOrganizations).toBe(2);
      expect(status.mappedOrganizations).toBe(2);
      expect(status.conflictCount).toBe(0);
      expect(status.mappingAccuracy).toBe(1.0);
      expect(status.healthScore).toBeGreaterThan(80);
    });
  });

  describe('联邦权限解析', () => {
    beforeEach(async () => {
      // 设置测试数据：用户在应用组织中的成员关系
      await prisma.organizationMember.create({
        data: {
          userId: testUser.id,
          organizationId: standardOrg2.id,
          role: 'developer',
          permissions: ['read:code', 'write:code'],
          createdBy: testUser.id
        }
      });

      // 创建映射关系
      await prisma.organizationMapping.create({
        data: {
          id: 'test-mapping-2',
          sourceOrgId: 'app1-backend-team',
          sourceApplication: testApp1.id,
          targetOrgId: standardOrg2.id,
          mappingType: 'exact',
          confidence: 1.0,
          createdBy: testUser.id
        }
      });
    });

    test('应该能够解析联邦权限', async () => {
      const result = await federatedOrganizationService.resolveFederatedPermissions(
        testUser.id,
        testApp1.id
      );

      expect(result.userId).toBe(testUser.id);
      expect(result.applicationId).toBe(testApp1.id);
      expect(result.permissions).toContain('read:code');
      expect(result.permissions).toContain('write:code');
      expect(result.mappingConfidence).toBeGreaterThan(0);
      expect(result.organizationMemberships).toHaveLength(1);
    });

    test('应该能够缓存联邦权限结果', async () => {
      // 第一次调用
      const start1 = Date.now();
      const result1 = await federatedOrganizationService.resolveFederatedPermissions(
        testUser.id,
        testApp1.id
      );
      const duration1 = Date.now() - start1;

      // 第二次调用（应该从缓存获取）
      const start2 = Date.now();
      const result2 = await federatedOrganizationService.resolveFederatedPermissions(
        testUser.id,
        testApp1.id
      );
      const duration2 = Date.now() - start2;

      expect(result1.permissions).toEqual(result2.permissions);
      expect(duration2).toBeLessThan(duration1); // 缓存应该更快
    });
  });

  describe('冲突检测和解决', () => {
    test('应该能够检测映射冲突', async () => {
      // 创建冲突的映射
      const conflictingMappings = [
        {
          id: 'conflict-mapping-1',
          sourceOrgId: 'conflict-org',
          sourceApplication: testApp1.id,
          targetOrgId: standardOrg1.id,
          mappingType: 'exact',
          confidence: 0.9,
          createdBy: testUser.id
        },
        {
          id: 'conflict-mapping-2',
          sourceOrgId: 'conflict-org',
          sourceApplication: testApp1.id,
          targetOrgId: standardOrg2.id,
          mappingType: 'exact',
          confidence: 0.8,
          createdBy: testUser.id
        }
      ];

      const conflicts = await federatedOrganizationService.detectMappingConflicts(
        conflictingMappings
      );

      expect(conflicts).toHaveLength(1);
      expect(conflicts[0].type).toBe('duplicate_mapping');
      expect(conflicts[0].count).toBe(2);
    });

    test('应该能够获取映射冲突列表', async () => {
      // 创建测试冲突记录
      await prisma.mappingConflict.create({
        data: {
          applicationId: testApp1.id,
          conflictType: 'duplicate_mapping',
          sourceOrgId: 'test-conflict-org',
          conflictingMappings: [
            { sourceOrgId: 'test-conflict-org', targetOrgId: standardOrg1.id },
            { sourceOrgId: 'test-conflict-org', targetOrgId: standardOrg2.id }
          ],
          suggestedResolution: {
            strategy: 'manual_review',
            confidence: 0.5,
            reasoning: '需要人工判断正确的映射关系'
          },
          status: 'pending'
        }
      });

      const conflicts = await federatedOrganizationService.getMappingConflicts(
        testApp1.id,
        { status: 'pending' }
      );

      expect(conflicts.items).toHaveLength(1);
      expect(conflicts.items[0].conflictType).toBe('duplicate_mapping');
      expect(conflicts.items[0].status).toBe('pending');
      expect(conflicts.total).toBe(1);
    });

    test('应该能够解决映射冲突', async () => {
      // 创建待解决的冲突
      const conflict = await prisma.mappingConflict.create({
        data: {
          applicationId: testApp1.id,
          conflictType: 'duplicate_mapping',
          sourceOrgId: 'resolve-test-org',
          conflictingMappings: [
            { sourceOrgId: 'resolve-test-org', targetOrgId: standardOrg1.id }
          ],
          suggestedResolution: {
            strategy: 'use_latest',
            confidence: 0.8
          },
          status: 'pending'
        }
      });

      const result = await federatedOrganizationService.resolveConflict(
        conflict.id,
        'use_latest',
        testUser.id,
        '使用最新的映射关系'
      );

      expect(result.resolvedAt).toBeDefined();
      expect(result.newMappings).toBeGreaterThanOrEqual(0);

      // 验证冲突状态已更新
      const updatedConflict = await prisma.mappingConflict.findUnique({
        where: { id: conflict.id }
      });

      expect(updatedConflict?.status).toBe('resolved');
      expect(updatedConflict?.resolvedBy).toBe(testUser.id);
      expect(updatedConflict?.resolutionStrategy).toBe('use_latest');
    });
  });

  describe('性能和缓存测试', () => {
    test('应该能够处理大量组织映射', async () => {
      const largeOrgList = Array.from({ length: 100 }, (_, i) => ({
        id: `large-org-${i}`,
        name: `org_${i}`,
        displayName: `组织${i}`,
        type: 'department',
        members: []
      }));

      const start = Date.now();
      const result = await federatedOrganizationService.registerApplicationOrganization(
        testApp2.id,
        largeOrgList
      );
      const duration = Date.now() - start;

      expect(result.syncedCount).toBe(100);
      expect(duration).toBeLessThan(10000); // 应该在10秒内完成
    });

    test('应该能够正确处理权限缓存过期', async () => {
      // 创建一个即将过期的缓存记录
      const expiredCache = await prisma.federatedPermissionCache.create({
        data: {
          userId: testUser.id,
          applicationId: testApp1.id,
          organizationContext: 'test-context',
          permissions: ['read:test'],
          organizationMemberships: [],
          mappingConfidence: 0.9,
          resolvedAt: new Date(),
          expiresAt: new Date(Date.now() - 1000), // 已过期
          hitCount: 0
        }
      });

      // 调用权限解析，应该重新计算而不是使用过期缓存
      const result = await federatedOrganizationService.resolveFederatedPermissions(
        testUser.id,
        testApp1.id,
        'test-context'
      );

      expect(result).toBeDefined();
      // 验证缓存已更新
      const updatedCache = await prisma.federatedPermissionCache.findUnique({
        where: { id: expiredCache.id }
      });
      expect(updatedCache?.expiresAt.getTime()).toBeGreaterThan(Date.now());
    });
  });

  describe('边界情况测试', () => {
    test('应该正确处理不存在的应用', async () => {
      await expect(
        federatedOrganizationService.registerApplicationOrganization(
          'non-existent-app',
          []
        )
      ).rejects.toThrow('应用不存在');
    });

    test('应该正确处理空的组织列表', async () => {
      const result = await federatedOrganizationService.registerApplicationOrganization(
        testApp1.id,
        []
      );

      expect(result.syncedCount).toBe(0);
      expect(result.newMappings).toBe(0);
      expect(result.conflicts).toEqual([]);
    });

    test('应该正确处理无效的组织数据', async () => {
      const invalidOrgs = [
        {
          id: '',
          name: '',
          displayName: '',
          type: 'invalid-type',
          members: []
        }
      ];

      await expect(
        federatedOrganizationService.registerApplicationOrganization(
          testApp1.id,
          invalidOrgs as any
        )
      ).rejects.toThrow();
    });

    test('应该正确处理用户没有组织关系的情况', async () => {
      const newUser = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          username: 'noorguser',
          firstName: 'No',
          lastName: 'Org',
          passwordHash: 'hashed_password'
        }
      });

      const result = await federatedOrganizationService.resolveFederatedPermissions(
        newUser.id,
        testApp1.id
      );

      expect(result.permissions).toEqual([]);
      expect(result.organizationMemberships).toEqual([]);
      expect(result.mappingConfidence).toBe(0);

      // 清理
      await prisma.user.delete({ where: { id: newUser.id } });
    });
  });
});
