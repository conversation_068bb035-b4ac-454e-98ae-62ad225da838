/**
 * OAuth错误处理测试
 * 验证OAuth回调的错误处理机制
 */

import request from 'supertest';
import app from '../index';
import { logger } from '../config/logger';

// Mock logger to avoid console output during tests
jest.mock('../config/logger', () => ({
  logger: {
    error: jest.fn(),
    warn: jest.fn(),
    info: jest.fn()
  }
}));

describe('OAuth错误处理测试', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('OAuth回调错误处理', () => {
    it('应该正确处理Google OAuth认证失败', async () => {
      // 模拟Google OAuth认证失败的回调
      const response = await request(app)
        .get('/api/v1/auth/google/callback')
        .query({
          error: 'access_denied',
          error_description: 'User denied access'
        });

      // 应该重定向到错误页面
      expect(response.status).toBe(302);
      expect(response.headers.location).toContain('/auth/error');
      expect(response.headers.location).toContain('error=oauth_error');
      expect(response.headers.location).toContain('message=');
    });

    it('应该正确处理GitHub OAuth认证失败', async () => {
      const response = await request(app)
        .get('/api/v1/auth/github/callback')
        .query({
          error: 'access_denied',
          error_description: 'User denied access'
        });

      expect(response.status).toBe(302);
      expect(response.headers.location).toContain('/auth/error');
      expect(response.headers.location).toContain('error=oauth_error');
    });

    it('应该记录详细的错误信息', async () => {
      await request(app)
        .get('/api/v1/auth/google/callback')
        .query({
          error: 'invalid_request',
          error_description: 'Invalid request parameters'
        });

      // 验证是否记录了错误日志
      expect(logger.error).toHaveBeenCalledWith(
        expect.stringContaining('OAuth错误重定向'),
        expect.objectContaining({
          error: expect.any(String),
          message: expect.any(String),
          timestamp: expect.any(String)
        })
      );
    });

    it('应该处理无效的state参数', async () => {
      const response = await request(app)
        .get('/api/v1/auth/google/callback')
        .query({
          state: 'invalid',
          code: 'valid_code'
        });

      expect(response.status).toBe(302);
      expect(response.headers.location).toContain('error=invalid_state');
      expect(response.headers.location).toContain('message=');
    });

    it('应该在开发环境中包含详细错误信息', async () => {
      // 临时设置为开发环境
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = 'development';

      const response = await request(app)
        .get('/api/v1/auth/google/callback')
        .query({
          error: 'server_error',
          error_description: 'Internal server error'
        });

      expect(response.status).toBe(302);
      const location = response.headers.location;
      expect(location).toContain('details=');

      // 恢复环境变量
      process.env.NODE_ENV = originalEnv;
    });
  });

  describe('OAuth提供商列表错误处理', () => {
    it('应该正确处理获取提供商列表的错误', async () => {
      // 这个测试需要模拟配置错误的情况
      const response = await request(app)
        .get('/api/v1/auth/providers');

      // 正常情况下应该返回200，但如果有错误应该返回500
      if (response.status === 500) {
        expect(response.body).toHaveProperty('error', 'get_providers_failed');
        expect(response.body).toHaveProperty('message');
      } else {
        expect(response.status).toBe(200);
        expect(response.body).toHaveProperty('providers');
      }
    });
  });

  describe('OAuth断开连接错误处理', () => {
    it('应该正确处理断开连接时的错误', async () => {
      // 使用无效的token测试断开连接
      const response = await request(app)
        .delete('/api/v1/auth/disconnect/google')
        .set('Authorization', 'Bearer invalid_token');

      expect(response.status).toBe(401);
    });
  });

  describe('错误重定向URL构建', () => {
    it('应该构建正确的错误重定向URL', () => {
      const mockRes = {
        redirect: jest.fn()
      } as any;

      // 这里需要访问私有方法，在实际测试中可能需要重构
      // 或者通过集成测试来验证重定向行为
      expect(mockRes.redirect).toBeDefined();
    });
  });
});

describe('OAuth错误恢复机制', () => {
  it('应该提供重试机制', async () => {
    // 测试用户可以从错误页面重新尝试登录
    const response = await request(app)
      .get('/api/v1/auth/google');

    // 应该重定向到Google OAuth授权页面
    expect(response.status).toBe(302);
    expect(response.headers.location).toContain('accounts.google.com');
  });

  it('应该清理错误状态', async () => {
    // 测试错误状态不会影响后续的登录尝试
    // 这通常通过前端状态管理来实现
    expect(true).toBe(true); // 占位测试
  });
});
