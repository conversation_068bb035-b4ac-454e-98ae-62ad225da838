/**
 * Redis配置
 * 管理Redis连接和配置选项
 */

import { config } from './index';
import Redis, { RedisOptions } from 'ioredis';
import { logger } from './logger';

export interface RedisConfig {
  host: string;
  port: number;
  password?: string;
  db: number;
  keyPrefix: string;
  retryDelayOnFailover: number;
  maxRetriesPerRequest: number;
  lazyConnect: boolean;
  keepAlive: number;
  family: number;
  connectTimeout: number;
  commandTimeout: number;
}

/**
 * Redis连接配置
 */
export const redisConfig: RedisConfig = {
  host: process.env.REDIS_HOST || 'localhost',
  port: parseInt(process.env.REDIS_PORT || '6379'),
  password: process.env.REDIS_PASSWORD,
  db: parseInt(process.env.REDIS_DB || '0'),
  keyPrefix: process.env.REDIS_KEY_PREFIX || 'idp:',
  retryDelayOnFailover: 100,
  maxRetriesPerRequest: 3,
  lazyConnect: true,
  keepAlive: 30000,
  family: 4,
  connectTimeout: 10000,
  commandTimeout: 5000
};

/**
 * Redis键命名规范
 */
export const RedisKeys = {
  // 用户会话
  SESSION: (sessionId: string) => `session:${sessionId}`,
  USER_SESSIONS: (userId: string) => `user:${userId}:sessions`,
  
  // JWT令牌管理
  JWT_BLACKLIST: (jti: string) => `jwt:blacklist:${jti}`,
  REFRESH_TOKEN: (tokenId: string) => `refresh:${tokenId}`,
  
  // 速率限制
  RATE_LIMIT: (key: string) => `rate:${key}`,
  RATE_LIMIT_LOGIN: (ip: string) => `rate:login:${ip}`,
  RATE_LIMIT_REGISTER: (ip: string) => `rate:register:${ip}`,
  RATE_LIMIT_PASSWORD_RESET: (ip: string, email: string) => `rate:password:${ip}:${email}`,
  
  // OAuth状态
  OAUTH_STATE: (state: string) => `oauth:state:${state}`,
  OAUTH_CODE: (code: string) => `oauth:code:${code}`,
  
  // MFA验证码
  MFA_CODE: (userId: string, type: string) => `mfa:${type}:${userId}`,
  MFA_BACKUP_CODES: (userId: string) => `mfa:backup:${userId}`,
  
  // 用户权限缓存
  USER_PERMISSIONS: (userId: string) => `user:${userId}:permissions`,
  USER_PROFILE: (userId: string) => `user:${userId}:profile`,
  
  // 系统配置缓存
  SYSTEM_CONFIG: () => 'system:config',
  OAUTH_PROVIDERS: () => 'oauth:providers',
  
  // 安全相关
  FAILED_LOGIN_ATTEMPTS: (ip: string) => `security:failed:${ip}`,
  SUSPICIOUS_ACTIVITY: (ip: string) => `security:suspicious:${ip}`,
  
  // 邮件验证
  EMAIL_VERIFICATION: (token: string) => `email:verify:${token}`,
  PASSWORD_RESET: (token: string) => `password:reset:${token}`,
  
  // 统计数据
  STATS_DAILY_LOGINS: (date: string) => `stats:logins:${date}`,
  STATS_DAILY_REGISTRATIONS: (date: string) => `stats:registrations:${date}`,
  
  // 缓存锁
  LOCK: (resource: string) => `lock:${resource}`,

  // 设备信息
  DEVICE_INFO: (deviceId: string) => `device:${deviceId}`,

  // 会话事件
  SESSION_EVENT: (sessionId: string, event: string) => `session:${sessionId}:event:${event}`,

  // 用户黑名单索引
  USER_BLACKLIST_INDEX: (userId: string) => `blacklist:user:${userId}`,

  // 黑名单原因索引
  BLACKLIST_REASON_INDEX: (reason: string) => `blacklist:reason:${reason}`,

  // 黑名单统计
  BLACKLIST_STATS: () => `blacklist:stats`,
  BLACKLIST_DAILY_STATS: (date: string) => `blacklist:stats:${date}`,

  // 速率限制
  RATE_LIMIT_BUCKET: (key: string) => `ratelimit:bucket:${key}`,
  RATE_LIMIT_WINDOW: (key: string, window: number | string) => `ratelimit:window:${key}:${window}`,
  RATE_LIMIT_SLIDING: (key: string) => `ratelimit:sliding:${key}`
} as const;

/**
 * Redis缓存过期时间配置（秒）
 */
export const RedisTTL = {
  // 会话相关
  SESSION: 24 * 60 * 60, // 24小时
  USER_SESSIONS: 7 * 24 * 60 * 60, // 7天
  
  // JWT相关
  JWT_BLACKLIST: 15 * 60, // 15分钟（访问令牌过期时间）
  REFRESH_TOKEN: 7 * 24 * 60 * 60, // 7天
  
  // 速率限制
  RATE_LIMIT_LOGIN: 15 * 60, // 15分钟
  RATE_LIMIT_REGISTER: 60 * 60, // 1小时
  RATE_LIMIT_PASSWORD_RESET: 60 * 60, // 1小时
  RATE_LIMIT_GENERAL: 15 * 60, // 15分钟
  
  // OAuth
  OAUTH_STATE: 10 * 60, // 10分钟
  OAUTH_CODE: 5 * 60, // 5分钟
  
  // MFA
  MFA_CODE: 5 * 60, // 5分钟
  MFA_BACKUP_CODES: 30 * 24 * 60 * 60, // 30天
  
  // 用户数据缓存
  USER_PERMISSIONS: 30 * 60, // 30分钟
  USER_PROFILE: 15 * 60, // 15分钟
  
  // 系统配置
  SYSTEM_CONFIG: 60 * 60, // 1小时
  OAUTH_PROVIDERS: 60 * 60, // 1小时
  
  // 安全相关
  FAILED_LOGIN_ATTEMPTS: 60 * 60, // 1小时
  SUSPICIOUS_ACTIVITY: 24 * 60 * 60, // 24小时
  
  // 邮件验证
  EMAIL_VERIFICATION: 24 * 60 * 60, // 24小时
  PASSWORD_RESET: 60 * 60, // 1小时
  
  // 统计数据
  STATS_DAILY: 7 * 24 * 60 * 60, // 7天
  
  // 缓存锁
  LOCK: 30 // 30秒
} as const;

/**
 * Redis集群配置（如果使用集群模式）
 */
export const redisClusterConfig = {
  enableOfflineQueue: false,
  redisOptions: {
    password: process.env.REDIS_PASSWORD,
    db: parseInt(process.env.REDIS_DB || '0'),
    keyPrefix: process.env.REDIS_KEY_PREFIX || 'idp:'
  },
  clusterRetryDelayOnFailover: 100,
  clusterRetryDelayOnClusterDown: 300,
  clusterMaxRedirections: 6,
  scaleReads: 'slave' as const
};

/**
 * Redis连接健康检查配置
 */
export const redisHealthConfig = {
  checkInterval: 30000, // 30秒检查一次
  timeout: 5000, // 5秒超时
  retryAttempts: 3,
  retryDelay: 1000
};

/**
 * 开发环境Redis配置
 */
export const redisDevConfig: Partial<RedisConfig> = {
  host: 'localhost',
  port: 6379,
  db: 1, // 使用不同的数据库避免冲突
  keyPrefix: 'idp:dev:',
  commandTimeout: 10000
};

/**
 * 测试环境Redis配置
 */
export const redisTestConfig: Partial<RedisConfig> = {
  host: 'localhost',
  port: 6379,
  db: 2, // 测试专用数据库
  keyPrefix: 'idp:test:',
  commandTimeout: 5000
};

/**
 * 生产环境Redis配置
 */
export const redisProdConfig: Partial<RedisConfig> = {
  retryDelayOnFailover: 50,
  maxRetriesPerRequest: 5,
  keepAlive: 60000,
  connectTimeout: 5000,
  commandTimeout: 3000
};

/**
 * 根据环境获取Redis配置
 */
export function getRedisConfig(): RedisConfig {
  const baseConfig = { ...redisConfig };
  
  switch (config.server.nodeEnv) {
    case 'development':
      return { ...baseConfig, ...redisDevConfig };
    case 'test':
      return { ...baseConfig, ...redisTestConfig };
    case 'production':
      return { ...baseConfig, ...redisProdConfig };
    default:
      return baseConfig;
  }
}

/**
 * Redis连接选项
 */
export const redisConnectionOptions = {
  // 连接重试配置
  retryDelayOnFailover: redisConfig.retryDelayOnFailover,
  maxRetriesPerRequest: redisConfig.maxRetriesPerRequest,

  // 连接超时配置
  connectTimeout: redisConfig.connectTimeout,
  commandTimeout: redisConfig.commandTimeout,

  // 连接保活配置
  keepAlive: redisConfig.keepAlive,
  family: redisConfig.family,

  // 懒加载连接
  lazyConnect: redisConfig.lazyConnect,

  // 错误处理
  enableReadyCheck: true,
  maxLoadingTimeout: 5000
};

/**
 * Redis连接管理类
 */
class RedisManager {
  private static instance: RedisManager;
  private redis: Redis | null = null;
  private subscriber: Redis | null = null;
  private publisher: Redis | null = null;
  private config: RedisConfig;
  private isConnected = false;

  private constructor() {
    this.config = getRedisConfig();
  }

  /**
   * 获取单例实例
   */
  public static getInstance(): RedisManager {
    if (!RedisManager.instance) {
      RedisManager.instance = new RedisManager();
    }
    return RedisManager.instance;
  }

  /**
   * 初始化Redis连接
   */
  public async initialize(): Promise<void> {
    try {
      const options = this.createRedisOptions();

      // 创建主连接
      this.redis = new Redis(options);

      // 创建发布者连接
      this.publisher = new Redis(options);

      // 创建订阅者连接
      this.subscriber = new Redis(options);

      // 设置事件监听器
      this.setupEventListeners();

      // 测试连接
      await this.redis.ping();

      this.isConnected = true;
      logger.info('Redis连接初始化成功', {
        host: this.config.host,
        port: this.config.port,
        db: this.config.db
      });

    } catch (error) {
      logger.error('Redis连接初始化失败', { error });
      throw error;
    }
  }

  /**
   * 获取Redis客户端
   */
  public getClient(): Redis {
    if (!this.redis) {
      throw new Error('Redis客户端未初始化');
    }
    return this.redis;
  }

  /**
   * 获取发布者客户端
   */
  public getPublisher(): Redis {
    if (!this.publisher) {
      throw new Error('Redis发布者客户端未初始化');
    }
    return this.publisher;
  }

  /**
   * 获取订阅者客户端
   */
  public getSubscriber(): Redis {
    if (!this.subscriber) {
      throw new Error('Redis订阅者客户端未初始化');
    }
    return this.subscriber;
  }

  /**
   * 检查连接状态
   */
  public isRedisConnected(): boolean {
    return this.isConnected && this.redis?.status === 'ready';
  }

  /**
   * 关闭所有连接
   */
  public async disconnect(): Promise<void> {
    try {
      const promises: Promise<void>[] = [];

      if (this.redis) {
        promises.push(this.redis.disconnect());
      }

      if (this.publisher) {
        promises.push(this.publisher.disconnect());
      }

      if (this.subscriber) {
        promises.push(this.subscriber.disconnect());
      }

      await Promise.all(promises);

      this.isConnected = false;
      logger.info('Redis连接已关闭');

    } catch (error) {
      logger.error('关闭Redis连接失败', { error });
    }
  }

  /**
   * 健康检查
   */
  public async healthCheck(): Promise<{
    status: 'healthy' | 'unhealthy';
    latency?: number;
    error?: string;
  }> {
    try {
      if (!this.redis) {
        return {
          status: 'unhealthy',
          error: 'Redis客户端未初始化'
        };
      }

      const start = Date.now();
      await this.redis.ping();
      const latency = Date.now() - start;

      return {
        status: 'healthy',
        latency
      };

    } catch (error) {
      return {
        status: 'unhealthy',
        error: error instanceof Error ? error.message : '未知错误'
      };
    }
  }

  /**
   * 创建Redis连接选项
   */
  private createRedisOptions(): RedisOptions {
    return {
      host: this.config.host,
      port: this.config.port,
      password: this.config.password,
      db: this.config.db,
      keyPrefix: this.config.keyPrefix,
      retryDelayOnFailover: this.config.retryDelayOnFailover,
      maxRetriesPerRequest: this.config.maxRetriesPerRequest,
      lazyConnect: this.config.lazyConnect,
      keepAlive: this.config.keepAlive,
      family: this.config.family,
      connectTimeout: this.config.connectTimeout,
      commandTimeout: this.config.commandTimeout,

      // 重试策略
      retryStrategy: (times: number) => {
        const delay = Math.min(times * 50, 2000);
        logger.warn('Redis连接重试', { times, delay });
        return delay;
      },

      // 重连策略
      reconnectOnError: (err: Error) => {
        const targetError = 'READONLY';
        return err.message.includes(targetError);
      }
    };
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    if (this.redis) {
      this.redis.on('connect', () => {
        logger.info('Redis主连接已建立');
      });

      this.redis.on('ready', () => {
        logger.info('Redis主连接就绪');
        this.isConnected = true;
      });

      this.redis.on('error', (error) => {
        logger.error('Redis主连接错误', { error });
        this.isConnected = false;
      });

      this.redis.on('close', () => {
        logger.warn('Redis主连接已关闭');
        this.isConnected = false;
      });

      this.redis.on('reconnecting', () => {
        logger.info('Redis主连接重连中');
      });
    }

    if (this.publisher) {
      this.publisher.on('error', (error) => {
        logger.error('Redis发布者连接错误', { error });
      });
    }

    if (this.subscriber) {
      this.subscriber.on('error', (error) => {
        logger.error('Redis订阅者连接错误', { error });
      });
    }
  }
}

// 导出Redis管理器实例
export const redisManager = RedisManager.getInstance();

// 导出便捷方法
export const getRedisClient = () => redisManager.getClient();
export const getRedisPublisher = () => redisManager.getPublisher();
export const getRedisSubscriber = () => redisManager.getSubscriber();
