/**
 * 安全配置中心
 * 统一管理所有安全相关的配置和策略
 */

import { config } from './index';
import { logger } from './logger';

/**
 * 安全策略配置
 */
export const securityConfig = {
  // 密码策略
  password: {
    minLength: 8,
    maxLength: 128,
    requireUppercase: true,
    requireLowercase: true,
    requireNumbers: true,
    requireSpecialChars: true,
    maxAttempts: 5,
    lockoutDuration: 15 * 60 * 1000, // 15分钟
    historyCount: 5, // 记住最近5个密码
  },

  // 会话安全
  session: {
    maxAge: 24 * 60 * 60 * 1000, // 24小时
    maxConcurrentSessions: 5,
    requireSecureCookies: config.server.nodeEnv === 'production',
    sameSite: 'strict' as const,
    httpOnly: true,
  },

  // JWT安全
  jwt: {
    accessTokenExpiry: '15m',
    refreshTokenExpiry: '7d',
    issuer: 'id-provider',
    audience: 'id-provider-clients',
    algorithm: 'HS256' as const,
    clockTolerance: 30, // 30秒时钟偏差容忍
  },

  // 速率限制
  rateLimit: {
    // 登录尝试限制
    login: {
      windowMs: 15 * 60 * 1000, // 15分钟
      max: 5, // 最多5次尝试
      skipSuccessfulRequests: true,
    },
    // 注册限制
    register: {
      windowMs: 60 * 60 * 1000, // 1小时
      max: 3, // 最多3次注册
    },
    // API通用限制
    api: {
      windowMs: 15 * 60 * 1000, // 15分钟
      max: 100, // 最多100次请求
    },
    // 密码重置限制
    passwordReset: {
      windowMs: 60 * 60 * 1000, // 1小时
      max: 3, // 最多3次重置请求
    },
  },

  // CORS安全策略
  cors: {
    allowedOrigins: [
      config.server.frontendUrl,
      'http://localhost:3001',
      'http://localhost:3000',
      'https://localhost:3001',
      'https://localhost:3000',
    ],
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
    allowedHeaders: [
      'Content-Type',
      'Authorization',
      'X-Requested-With',
      'X-CSRF-Token',
      'X-API-Key',
    ],
    exposedHeaders: ['X-Total-Count', 'X-Rate-Limit-Remaining'],
    maxAge: 86400, // 24小时预检缓存
  },

  // CSP (Content Security Policy) 配置
  csp: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'", 'https://fonts.googleapis.com'],
      scriptSrc: ["'self'", "'unsafe-eval'"], // React开发需要unsafe-eval
      fontSrc: ["'self'", 'https://fonts.gstatic.com'],
      imgSrc: ["'self'", 'data:', 'https:', 'blob:'],
      connectSrc: ["'self'", 'https://api.github.com', 'https://accounts.google.com'],
      frameSrc: ["'none'"],
      objectSrc: ["'none'"],
      mediaSrc: ["'self'"],
      manifestSrc: ["'self'"],
      workerSrc: ["'self'", 'blob:'],
    },
    reportOnly: config.server.nodeEnv === 'development',
  },

  // 安全头配置
  headers: {
    // 强制HTTPS (生产环境)
    hsts: {
      maxAge: ********, // 1年
      includeSubDomains: true,
      preload: true,
    },
    // 防止点击劫持
    frameOptions: 'DENY',
    // 防止MIME类型嗅探
    noSniff: true,
    // XSS保护
    xssFilter: true,
    // 引用策略
    referrerPolicy: 'strict-origin-when-cross-origin',
    // 权限策略
    permissionsPolicy: {
      camera: [],
      microphone: [],
      geolocation: [],
      payment: [],
    },
  },

  // 加密配置
  encryption: {
    algorithm: 'aes-256-gcm',
    keyLength: 32,
    ivLength: 16,
    tagLength: 16,
    saltRounds: 12, // bcrypt盐轮数
  },

  // 审计和监控
  audit: {
    logFailedAttempts: true,
    logSuccessfulLogins: true,
    logPrivilegedOperations: true,
    retentionDays: 90,
    alertThresholds: {
      failedLogins: 10, // 10次失败登录后告警
      suspiciousActivity: 5, // 5次可疑活动后告警
    },
  },

  // IP白名单/黑名单
  ipFilter: {
    enabled: config.server.nodeEnv === 'production',
    whitelist: [], // 生产环境可配置IP白名单
    blacklist: [], // IP黑名单
    trustProxy: true, // 信任代理头
  },

  // OAuth安全
  oauth: {
    stateExpiry: 10 * 60 * 1000, // state参数10分钟过期
    nonceExpiry: 10 * 60 * 1000, // nonce参数10分钟过期
    maxRedirectUris: 10, // 最多10个重定向URI
    requirePKCE: true, // 要求PKCE
  },

  // MFA安全
  mfa: {
    totpWindow: 1, // TOTP时间窗口
    backupCodesCount: 10, // 备用码数量
    maxFailedAttempts: 3, // 最多3次MFA失败
    lockoutDuration: 30 * 60 * 1000, // 30分钟锁定
  },
};

/**
 * 验证安全配置
 */
export function validateSecurityConfig(): void {
  const errors: string[] = [];

  // 验证JWT密钥强度
  const jwtSecret = process.env.JWT_SECRET;
  if (jwtSecret && jwtSecret.length < 32) {
    errors.push('JWT_SECRET长度应至少32个字符');
  }

  // 验证刷新令牌密钥
  const refreshSecret = process.env.JWT_REFRESH_SECRET;
  if (refreshSecret && refreshSecret.length < 32) {
    errors.push('JWT_REFRESH_SECRET长度应至少32个字符');
  }

  // 生产环境安全检查
  if (config.server.nodeEnv === 'production') {
    if (!process.env.ENCRYPTION_KEY) {
      errors.push('生产环境必须设置ENCRYPTION_KEY');
    }

    if (config.server.frontendUrl.startsWith('http://')) {
      errors.push('生产环境应使用HTTPS');
    }

    if (!process.env.CSRF_SECRET) {
      errors.push('生产环境必须设置CSRF_SECRET');
    }
  }

  if (errors.length > 0) {
    logger.error('安全配置验证失败', { errors });
    throw new Error(`安全配置错误: ${errors.join(', ')}`);
  }

  logger.info('安全配置验证通过', {
    environment: config.server.nodeEnv,
    corsOrigins: securityConfig.cors.allowedOrigins.length,
    rateLimitEnabled: true,
    cspEnabled: true,
  });
}

/**
 * 获取生产环境安全建议
 */
export function getSecurityRecommendations(): string[] {
  const recommendations: string[] = [];

  if (config.server.nodeEnv !== 'production') {
    return ['当前为开发环境，生产部署时请参考安全检查清单'];
  }

  // 检查各种安全配置
  if (!process.env.ENCRYPTION_KEY) {
    recommendations.push('设置强加密密钥 (ENCRYPTION_KEY)');
  }

  if (!process.env.CSRF_SECRET) {
    recommendations.push('设置CSRF保护密钥 (CSRF_SECRET)');
  }

  if (config.server.frontendUrl.startsWith('http://')) {
    recommendations.push('启用HTTPS连接');
  }

  if (!securityConfig.ipFilter.enabled) {
    recommendations.push('考虑启用IP过滤');
  }

  return recommendations;
}
