/**
 * 数据库配置
 * 配置Prisma客户端和数据库连接
 */

import { PrismaClient } from '@prisma/client';
import { logger } from './logger';

// 创建Prisma客户端实例
export const prisma = new PrismaClient({
  log: [
    {
      emit: 'event',
      level: 'query',
    },
    {
      emit: 'event',
      level: 'error',
    },
    {
      emit: 'event',
      level: 'info',
    },
    {
      emit: 'event',
      level: 'warn',
    },
  ],
});

// 配置Prisma日志事件
prisma.$on('query', (e) => {
  logger.debug('数据库查询', {
    query: e.query,
    params: e.params,
    duration: e.duration,
    target: e.target
  });
});

prisma.$on('error', (e) => {
  logger.error('数据库错误', {
    message: e.message,
    target: e.target
  });
});

prisma.$on('info', (e) => {
  logger.info('数据库信息', {
    message: e.message,
    target: e.target
  });
});

prisma.$on('warn', (e) => {
  logger.warn('数据库警告', {
    message: e.message,
    target: e.target
  });
});

/**
 * 连接数据库
 */
export async function connectDatabase(): Promise<void> {
  try {
    await prisma.$connect();
    logger.info('数据库连接成功');
  } catch (error) {
    logger.error('数据库连接失败', { error });
    throw error;
  }
}

/**
 * 断开数据库连接
 */
export async function disconnectDatabase(): Promise<void> {
  try {
    await prisma.$disconnect();
    logger.info('数据库连接已断开');
  } catch (error) {
    logger.error('断开数据库连接失败', { error });
    throw error;
  }
}

/**
 * 检查数据库连接状态
 */
export async function checkDatabaseHealth(): Promise<boolean> {
  try {
    await prisma.$queryRaw`SELECT 1`;
    return true;
  } catch (error) {
    logger.error('数据库健康检查失败', { error });
    return false;
  }
}

// 优雅关闭处理
process.on('SIGINT', async () => {
  logger.info('收到SIGINT信号，正在关闭数据库连接...');
  await disconnectDatabase();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  logger.info('收到SIGTERM信号，正在关闭数据库连接...');
  await disconnectDatabase();
  process.exit(0);
});
