/**
 * API网关集成配置
 * 定义与各种API网关的集成配置
 */

import { config } from './index';

/**
 * 网关集成配置接口
 */
export interface GatewayConfig {
  name: string;
  type: 'kong' | 'nginx' | 'traefik' | 'envoy' | 'zuul' | 'custom';
  endpoints: {
    tokenValidation: string;
    introspection: string;
    userInfo: string;
    jwks: string;
    discovery: string;
  };
  authentication: {
    method: 'jwt' | 'oauth2' | 'api_key';
    clientId?: string;
    clientSecret?: string;
    apiKey?: string;
  };
  features: {
    supportJWT: boolean;
    supportOAuth2: boolean;
    supportOIDC: boolean;
    supportIntrospection: boolean;
    supportJWKS: boolean;
  };
}

/**
 * 预定义的网关配置
 */
export const gatewayConfigs: Record<string, GatewayConfig> = {
  // Kong API Gateway
  kong: {
    name: 'Kong API Gateway',
    type: 'kong',
    endpoints: {
      tokenValidation: '/api/v1/auth/validate-token',
      introspection: '/api/v1/auth/introspect',
      userInfo: '/api/v1/me',
      jwks: '/.well-known/jwks.json',
      discovery: '/.well-known/openid-configuration'
    },
    authentication: {
      method: 'jwt'
    },
    features: {
      supportJWT: true,
      supportOAuth2: true,
      supportOIDC: true,
      supportIntrospection: true,
      supportJWKS: true
    }
  },

  // Nginx Plus
  nginx: {
    name: 'Nginx Plus',
    type: 'nginx',
    endpoints: {
      tokenValidation: '/api/v1/auth/validate-token',
      introspection: '/api/v1/auth/introspect',
      userInfo: '/api/v1/me',
      jwks: '/.well-known/jwks.json',
      discovery: '/.well-known/openid-configuration'
    },
    authentication: {
      method: 'jwt'
    },
    features: {
      supportJWT: true,
      supportOAuth2: false,
      supportOIDC: true,
      supportIntrospection: true,
      supportJWKS: true
    }
  },

  // Traefik
  traefik: {
    name: 'Traefik',
    type: 'traefik',
    endpoints: {
      tokenValidation: '/api/v1/auth/validate-token',
      introspection: '/api/v1/auth/introspect',
      userInfo: '/api/v1/me',
      jwks: '/.well-known/jwks.json',
      discovery: '/.well-known/openid-configuration'
    },
    authentication: {
      method: 'jwt'
    },
    features: {
      supportJWT: true,
      supportOAuth2: false,
      supportOIDC: true,
      supportIntrospection: false,
      supportJWKS: true
    }
  },

  // Envoy Proxy
  envoy: {
    name: 'Envoy Proxy',
    type: 'envoy',
    endpoints: {
      tokenValidation: '/api/v1/auth/validate-token',
      introspection: '/api/v1/auth/introspect',
      userInfo: '/api/v1/me',
      jwks: '/.well-known/jwks.json',
      discovery: '/.well-known/openid-configuration'
    },
    authentication: {
      method: 'jwt'
    },
    features: {
      supportJWT: true,
      supportOAuth2: true,
      supportOIDC: true,
      supportIntrospection: true,
      supportJWKS: true
    }
  },

  // Spring Cloud Gateway / Zuul
  zuul: {
    name: 'Spring Cloud Gateway',
    type: 'zuul',
    endpoints: {
      tokenValidation: '/api/v1/auth/validate-token',
      introspection: '/api/v1/auth/introspect',
      userInfo: '/api/v1/me',
      jwks: '/.well-known/jwks.json',
      discovery: '/.well-known/openid-configuration'
    },
    authentication: {
      method: 'oauth2'
    },
    features: {
      supportJWT: true,
      supportOAuth2: true,
      supportOIDC: true,
      supportIntrospection: true,
      supportJWKS: true
    }
  }
};

/**
 * 获取网关配置
 */
export function getGatewayConfig(gatewayType: string): GatewayConfig | null {
  return gatewayConfigs[gatewayType] || null;
}

/**
 * 生成网关集成配置
 */
export function generateGatewayIntegrationConfig(gatewayType: string, baseUrl?: string) {
  const gatewayConfig = getGatewayConfig(gatewayType);
  if (!gatewayConfig) {
    throw new Error(`不支持的网关类型: ${gatewayType}`);
  }

  const idpBaseUrl = baseUrl || `http://localhost:${config.server.port}`;

  return {
    gateway: gatewayConfig.name,
    type: gatewayConfig.type,
    idp: {
      baseUrl: idpBaseUrl,
      endpoints: {
        tokenValidation: `${idpBaseUrl}${gatewayConfig.endpoints.tokenValidation}`,
        introspection: `${idpBaseUrl}${gatewayConfig.endpoints.introspection}`,
        userInfo: `${idpBaseUrl}${gatewayConfig.endpoints.userInfo}`,
        jwks: `${idpBaseUrl}${gatewayConfig.endpoints.jwks}`,
        discovery: `${idpBaseUrl}${gatewayConfig.endpoints.discovery}`
      }
    },
    authentication: gatewayConfig.authentication,
    features: gatewayConfig.features,
    examples: generateConfigExamples(gatewayType, idpBaseUrl)
  };
}

/**
 * 生成配置示例
 */
function generateConfigExamples(gatewayType: string, baseUrl: string) {
  const examples: Record<string, any> = {};

  switch (gatewayType) {
    case 'kong':
      examples.kong = {
        plugin: 'jwt',
        config: {
          uri_param_names: ['jwt'],
          cookie_names: ['jwt'],
          header_names: ['authorization'],
          claims_to_verify: ['exp'],
          key_claim_name: 'iss',
          secret_is_base64: false,
          run_on_preflight: true
        }
      };
      break;

    case 'nginx':
      examples.nginx = {
        location: {
          auth_jwt: 'on',
          auth_jwt_key_file: '/etc/nginx/jwt.key',
          auth_jwt_claim_set: '$user $jwt_claim_sub'
        }
      };
      break;

    case 'traefik':
      examples.traefik = {
        middlewares: {
          'auth-jwt': {
            forwardAuth: {
              address: `${baseUrl}/api/v1/auth/validate-token`,
              authResponseHeaders: ['X-User-Id', 'X-User-Email']
            }
          }
        }
      };
      break;

    case 'envoy':
      examples.envoy = {
        http_filters: [{
          name: 'envoy.filters.http.jwt_authn',
          typed_config: {
            providers: {
              'id-provider': {
                issuer: baseUrl,
                audiences: ['api-gateway'],
                remote_jwks: {
                  http_uri: {
                    uri: `${baseUrl}/.well-known/jwks.json`,
                    cluster: 'id-provider'
                  }
                }
              }
            }
          }
        }]
      };
      break;

    case 'zuul':
      examples.zuul = {
        security: {
          oauth2: {
            resourceserver: {
              jwt: {
                'issuer-uri': baseUrl,
                'jwk-set-uri': `${baseUrl}/.well-known/jwks.json`
              }
            }
          }
        }
      };
      break;
  }

  return examples;
}

/**
 * 网关集成健康检查
 */
export async function checkGatewayIntegration(gatewayType: string): Promise<{
  status: 'healthy' | 'unhealthy';
  checks: Record<string, boolean>;
  message?: string;
}> {
  const config = getGatewayConfig(gatewayType);
  if (!config) {
    return {
      status: 'unhealthy',
      checks: {},
      message: `不支持的网关类型: ${gatewayType}`
    };
  }

  const checks: Record<string, boolean> = {};

  // 检查各个端点是否可用
  try {
    // 这里可以添加实际的健康检查逻辑
    checks.tokenValidation = true;
    checks.introspection = config.features.supportIntrospection;
    checks.jwks = config.features.supportJWKS;
    checks.discovery = config.features.supportOIDC;

    const allHealthy = Object.values(checks).every(check => check);

    return {
      status: allHealthy ? 'healthy' : 'unhealthy',
      checks
    };

  } catch (error) {
    return {
      status: 'unhealthy',
      checks,
      message: `健康检查失败: ${(error as Error).message}`
    };
  }
}
