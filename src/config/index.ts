/**
 * 应用配置中心
 * 统一管理所有配置项
 */

import dotenv from 'dotenv';
import { logger } from './logger';

// 加载环境变量
dotenv.config();

/**
 * 验证必需的环境变量
 */
function validateRequiredEnvVars(): void {
  const required = [
    'DATABASE_URL',
    'JWT_SECRET',
    'JWT_REFRESH_SECRET'
  ];

  const missing = required.filter(key => !process.env[key]);
  
  if (missing.length > 0) {
    logger.error('缺少必需的环境变量', { missing });
    throw new Error(`缺少必需的环境变量: ${missing.join(', ')}`);
  }
}

// 验证环境变量
validateRequiredEnvVars();

/**
 * 应用配置对象
 */
export const config = {
  // 服务器配置
  server: {
    port: parseInt(process.env['PORT'] || '3000', 10),
    nodeEnv: process.env['NODE_ENV'] || 'development',
    frontendUrl: process.env['FRONTEND_URL'] || 'http://localhost:3001'
  },

  // 数据库配置
  database: {
    url: process.env['DATABASE_URL']!
  },

  // JWT配置
  jwt: {
    secret: process.env['JWT_SECRET']!,
    refreshSecret: process.env['JWT_REFRESH_SECRET']!,
    expiresIn: process.env['JWT_EXPIRES_IN'] || '15m',
    refreshExpiresIn: process.env['JWT_REFRESH_EXPIRES_IN'] || '7d'
  },

  // 邮件配置
  email: {
    smtp: {
      host: process.env['SMTP_HOST'] || 'smtp.gmail.com',
      port: parseInt(process.env['SMTP_PORT'] || '587', 10),
      secure: process.env['SMTP_SECURE'] === 'true',
      user: process.env['SMTP_USER'],
      pass: process.env['SMTP_PASS']
    },
    from: process.env['FROM_EMAIL'] || '<EMAIL>'
  },

  // 短信配置 (Twilio)
  sms: {
    accountSid: process.env['TWILIO_ACCOUNT_SID'],
    authToken: process.env['TWILIO_AUTH_TOKEN'],
    phoneNumber: process.env['TWILIO_PHONE_NUMBER']
  },

  // OAuth提供商配置
  oauth: {
    google: {
      clientId: process.env['GOOGLE_CLIENT_ID'],
      clientSecret: process.env['GOOGLE_CLIENT_SECRET'],
      callbackUrl: process.env['GOOGLE_CALLBACK_URL'] || 'http://localhost:3000/api/v1/auth/google/callback'
    },
    github: {
      clientId: process.env['GITHUB_CLIENT_ID'],
      clientSecret: process.env['GITHUB_CLIENT_SECRET'],
      callbackUrl: process.env['GITHUB_CALLBACK_URL'] || 'http://localhost:3000/api/v1/auth/github/callback'
    },
    wechat: {
      appId: process.env['WECHAT_APP_ID'],
      appSecret: process.env['WECHAT_APP_SECRET'],
      callbackUrl: process.env['WECHAT_CALLBACK_URL'] || 'http://localhost:3000/api/v1/auth/wechat/callback'
    },
    weibo: {
      clientId: process.env['WEIBO_CLIENT_ID'],
      clientSecret: process.env['WEIBO_CLIENT_SECRET'],
      callbackUrl: process.env['WEIBO_CALLBACK_URL'] || 'http://localhost:3000/api/v1/auth/weibo/callback'
    },
    // QQ互联配置
    qq: {
      clientId: process.env['QQ_CLIENT_ID'],
      clientSecret: process.env['QQ_CLIENT_SECRET'],
      callbackUrl: process.env['QQ_CALLBACK_URL'] || 'http://localhost:3000/api/v1/auth/qq/callback'
    },
    // 支付宝开放平台配置
    alipay: {
      appId: process.env['ALIPAY_APP_ID'],
      privateKey: process.env['ALIPAY_PRIVATE_KEY'],
      publicKey: process.env['ALIPAY_PUBLIC_KEY'],
      callbackUrl: process.env['ALIPAY_CALLBACK_URL'] || 'http://localhost:3000/api/v1/auth/alipay/callback',
      // 支付宝使用RSA签名，需要特殊配置
      signType: process.env['ALIPAY_SIGN_TYPE'] || 'RSA2',
      charset: process.env['ALIPAY_CHARSET'] || 'utf-8',
      gatewayUrl: process.env['ALIPAY_GATEWAY_URL'] || 'https://openapi.alipay.com/gateway.do'
    },
    // 钉钉开放平台配置
    dingtalk: {
      clientId: process.env['DINGTALK_CLIENT_ID'],
      clientSecret: process.env['DINGTALK_CLIENT_SECRET'],
      callbackUrl: process.env['DINGTALK_CALLBACK_URL'] || 'http://localhost:3000/api/v1/auth/dingtalk/callback',
      // 钉钉企业内部应用配置
      corpId: process.env['DINGTALK_CORP_ID'],
      agentId: process.env['DINGTALK_AGENT_ID']
    }
  },

  // 安全配置
  security: {
    bcryptRounds: parseInt(process.env['BCRYPT_ROUNDS'] || '12', 10),
    rateLimit: {
      windowMs: parseInt(process.env['RATE_LIMIT_WINDOW_MS'] || '900000', 10), // 15分钟
      maxRequests: parseInt(process.env['RATE_LIMIT_MAX_REQUESTS'] || '100', 10)
    }
  },

  // 零信任配置
  zeroTrust: {
    enabled: process.env['ZERO_TRUST_ENABLED'] === 'true',
    riskThresholds: {
      low: parseInt(process.env['RISK_THRESHOLD_LOW'] || '30', 10),
      medium: parseInt(process.env['RISK_THRESHOLD_MEDIUM'] || '60', 10),
      high: parseInt(process.env['RISK_THRESHOLD_HIGH'] || '80', 10)
    }
  },

  // 会话配置
  session: {
    timeoutMinutes: parseInt(process.env['SESSION_TIMEOUT_MINUTES'] || '30', 10),
    rememberMeDays: parseInt(process.env['REMEMBER_ME_DAYS'] || '30', 10)
  },

  // SAML配置
  saml: {
    certPath: process.env['SAML_CERT_PATH'] || './certs/saml.crt',
    keyPath: process.env['SAML_KEY_PATH'] || './certs/saml.key',
    issuer: process.env['SAML_ISSUER'] || 'http://localhost:3000'
  },

  // 日志配置
  logging: {
    level: process.env['LOG_LEVEL'] || 'info',
    filePath: process.env['LOG_FILE_PATH'] || './logs/app.log'
  }
};

/**
 * 检查配置是否为生产环境
 */
export const isProduction = (): boolean => {
  return config.server.nodeEnv === 'production';
};

/**
 * 检查配置是否为开发环境
 */
export const isDevelopment = (): boolean => {
  return config.server.nodeEnv === 'development';
};

/**
 * 检查配置是否为测试环境
 */
export const isTest = (): boolean => {
  return config.server.nodeEnv === 'test';
};

/**
 * 获取完整的服务器URL
 */
export const getServerUrl = (): string => {
  const protocol = isProduction() ? 'https' : 'http';
  const host = process.env['HOST'] || 'localhost';
  const port = config.server.port;

  return `${protocol}://${host}${port !== 80 && port !== 443 ? `:${port}` : ''}`;
};

// 记录配置加载完成
logger.info('应用配置加载完成', {
  nodeEnv: config.server.nodeEnv,
  port: config.server.port,
  zeroTrustEnabled: config.zeroTrust.enabled
});

export default config;
