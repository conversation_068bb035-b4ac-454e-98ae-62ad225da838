/**
 * 缓存策略配置
 * 定义统一的缓存键命名规范、过期策略和数据序列化方案
 */

export enum CacheNamespace {
  USER = 'user',
  SESSION = 'session',
  JWT = 'jwt',
  PERMISSION = 'permission',
  OAUTH = 'oauth',
  CONFIG = 'config',
  RATE_LIMIT = 'rate_limit',
  SECURITY = 'security'
}

export enum CacheTTL {
  // 短期缓存 (5分钟)
  SHORT = 5 * 60,
  
  // 中期缓存 (30分钟)
  MEDIUM = 30 * 60,
  
  // 长期缓存 (2小时)
  LONG = 2 * 60 * 60,
  
  // 会话缓存 (24小时)
  SESSION = 24 * 60 * 60,
  
  // 用户资料缓存 (1小时)
  USER_PROFILE = 60 * 60,
  
  // 权限缓存 (30分钟)
  PERMISSION = 30 * 60,
  
  // JWT黑名单 (根据令牌过期时间)
  JWT_BLACKLIST = 24 * 60 * 60,
  
  // 配置缓存 (1小时)
  CONFIG = 60 * 60,
  
  // 限流缓存 (1分钟)
  RATE_LIMIT = 60,
  
  // 安全事件缓存 (10分钟)
  SECURITY_EVENT = 10 * 60
}

/**
 * 缓存键构建器
 */
export class CacheKeyBuilder {
  private static readonly SEPARATOR = ':'
  private static readonly PREFIX = 'idp'

  /**
   * 构建缓存键
   */
  static build(namespace: CacheNamespace, ...parts: (string | number)[]): string {
    const keyParts = [this.PREFIX, namespace, ...parts.map(String)]
    return keyParts.join(this.SEPARATOR)
  }

  /**
   * 用户相关缓存键
   */
  static user = {
    profile: (userId: string) => this.build(CacheNamespace.USER, 'profile', userId),
    permissions: (userId: string) => this.build(CacheNamespace.USER, 'permissions', userId),
    roles: (userId: string) => this.build(CacheNamespace.USER, 'roles', userId),
    settings: (userId: string) => this.build(CacheNamespace.USER, 'settings', userId),
    mfaDevices: (userId: string) => this.build(CacheNamespace.USER, 'mfa_devices', userId),
    loginAttempts: (identifier: string) => this.build(CacheNamespace.USER, 'login_attempts', identifier)
  }

  /**
   * 会话相关缓存键
   */
  static session = {
    data: (sessionId: string) => this.build(CacheNamespace.SESSION, 'data', sessionId),
    user: (sessionId: string) => this.build(CacheNamespace.SESSION, 'user', sessionId),
    device: (sessionId: string) => this.build(CacheNamespace.SESSION, 'device', sessionId),
    activity: (sessionId: string) => this.build(CacheNamespace.SESSION, 'activity', sessionId),
    userSessions: (userId: string) => this.build(CacheNamespace.SESSION, 'user_sessions', userId)
  }

  /**
   * JWT相关缓存键
   */
  static jwt = {
    blacklist: (jti: string) => this.build(CacheNamespace.JWT, 'blacklist', jti),
    refreshToken: (tokenId: string) => this.build(CacheNamespace.JWT, 'refresh', tokenId),
    accessToken: (tokenId: string) => this.build(CacheNamespace.JWT, 'access', tokenId)
  }

  /**
   * 权限相关缓存键
   */
  static permission = {
    userPermissions: (userId: string) => this.build(CacheNamespace.PERMISSION, 'user', userId),
    rolePermissions: (roleId: string) => this.build(CacheNamespace.PERMISSION, 'role', roleId),
    resourcePermissions: (resourceId: string) => this.build(CacheNamespace.PERMISSION, 'resource', resourceId)
  }

  /**
   * OAuth相关缓存键
   */
  static oauth = {
    client: (clientId: string) => this.build(CacheNamespace.OAUTH, 'client', clientId),
    authCode: (code: string) => this.build(CacheNamespace.OAUTH, 'auth_code', code),
    state: (state: string) => this.build(CacheNamespace.OAUTH, 'state', state),
    pkce: (codeChallenge: string) => this.build(CacheNamespace.OAUTH, 'pkce', codeChallenge)
  }

  /**
   * 配置相关缓存键
   */
  static config = {
    system: (key: string) => this.build(CacheNamespace.CONFIG, 'system', key),
    security: (key: string) => this.build(CacheNamespace.CONFIG, 'security', key),
    email: (key: string) => this.build(CacheNamespace.CONFIG, 'email', key)
  }

  /**
   * 限流相关缓存键
   */
  static rateLimit = {
    ip: (ip: string, endpoint: string) => this.build(CacheNamespace.RATE_LIMIT, 'ip', ip, endpoint),
    user: (userId: string, endpoint: string) => this.build(CacheNamespace.RATE_LIMIT, 'user', userId, endpoint),
    global: (endpoint: string) => this.build(CacheNamespace.RATE_LIMIT, 'global', endpoint)
  }

  /**
   * 安全相关缓存键
   */
  static security = {
    failedLogin: (ip: string) => this.build(CacheNamespace.SECURITY, 'failed_login', ip),
    suspiciousActivity: (ip: string) => this.build(CacheNamespace.SECURITY, 'suspicious', ip),
    blockedIp: (ip: string) => this.build(CacheNamespace.SECURITY, 'blocked_ip', ip),
    securityEvent: (eventId: string) => this.build(CacheNamespace.SECURITY, 'event', eventId)
  }
}

/**
 * 缓存策略配置
 */
export interface CacheStrategy {
  ttl: number
  serialize: boolean
  compress: boolean
  tags?: string[]
}

/**
 * 预定义缓存策略
 */
export const CacheStrategies = {
  // 用户资料 - 中等TTL，需要序列化
  USER_PROFILE: {
    ttl: CacheTTL.USER_PROFILE,
    serialize: true,
    compress: false,
    tags: ['user']
  } as CacheStrategy,

  // 用户权限 - 短TTL，需要序列化
  USER_PERMISSIONS: {
    ttl: CacheTTL.PERMISSION,
    serialize: true,
    compress: false,
    tags: ['user', 'permission']
  } as CacheStrategy,

  // 会话数据 - 长TTL，需要序列化
  SESSION_DATA: {
    ttl: CacheTTL.SESSION,
    serialize: true,
    compress: true,
    tags: ['session']
  } as CacheStrategy,

  // JWT黑名单 - 根据令牌过期时间，不需要序列化
  JWT_BLACKLIST: {
    ttl: CacheTTL.JWT_BLACKLIST,
    serialize: false,
    compress: false,
    tags: ['jwt', 'security']
  } as CacheStrategy,

  // OAuth客户端 - 长TTL，需要序列化
  OAUTH_CLIENT: {
    ttl: CacheTTL.LONG,
    serialize: true,
    compress: false,
    tags: ['oauth']
  } as CacheStrategy,

  // 系统配置 - 长TTL，需要序列化
  SYSTEM_CONFIG: {
    ttl: CacheTTL.CONFIG,
    serialize: true,
    compress: false,
    tags: ['config']
  } as CacheStrategy,

  // 限流计数 - 短TTL，不需要序列化
  RATE_LIMIT: {
    ttl: CacheTTL.RATE_LIMIT,
    serialize: false,
    compress: false,
    tags: ['rate_limit']
  } as CacheStrategy,

  // 安全事件 - 中等TTL，需要序列化
  SECURITY_EVENT: {
    ttl: CacheTTL.SECURITY_EVENT,
    serialize: true,
    compress: false,
    tags: ['security']
  } as CacheStrategy
}

/**
 * 缓存数据序列化器
 */
export class CacheSerializer {
  /**
   * 序列化数据
   */
  static serialize(data: any): string {
    if (typeof data === 'string') {
      return data
    }
    return JSON.stringify(data)
  }

  /**
   * 反序列化数据
   */
  static deserialize<T = any>(data: string): T {
    try {
      return JSON.parse(data)
    } catch {
      return data as T
    }
  }

  /**
   * 压缩数据 (简化实现)
   */
  static compress(data: string): string {
    // 在实际项目中可以使用 zlib 或其他压缩库
    return data
  }

  /**
   * 解压数据 (简化实现)
   */
  static decompress(data: string): string {
    // 在实际项目中可以使用 zlib 或其他压缩库
    return data
  }
}

/**
 * 缓存标签管理器
 */
export class CacheTagManager {
  private static readonly TAG_PREFIX = 'tag'

  /**
   * 获取标签键
   */
  static getTagKey(tag: string): string {
    return `${this.TAG_PREFIX}:${tag}`
  }

  /**
   * 为缓存键添加标签
   */
  static async addTagsToKey(cacheKey: string, tags: string[]): Promise<void> {
    // 这里需要与Redis服务集成
    // 实现将在具体的缓存服务中完成
  }

  /**
   * 根据标签清理缓存
   */
  static async clearByTag(tag: string): Promise<number> {
    // 这里需要与Redis服务集成
    // 实现将在具体的缓存服务中完成
    return 0
  }
}

/**
 * 缓存配置验证器
 */
export class CacheConfigValidator {
  /**
   * 验证缓存键格式
   */
  static validateKey(key: string): boolean {
    // 检查键长度
    if (key.length > 250) {
      return false
    }

    // 检查键格式
    const keyPattern = /^[a-zA-Z0-9:_-]+$/
    return keyPattern.test(key)
  }

  /**
   * 验证TTL值
   */
  static validateTTL(ttl: number): boolean {
    return ttl > 0 && ttl <= 7 * 24 * 60 * 60 // 最大7天
  }

  /**
   * 验证缓存策略
   */
  static validateStrategy(strategy: CacheStrategy): boolean {
    return this.validateTTL(strategy.ttl)
  }
}

export default {
  CacheNamespace,
  CacheTTL,
  CacheKeyBuilder,
  CacheStrategies,
  CacheSerializer,
  CacheTagManager,
  CacheConfigValidator
}
