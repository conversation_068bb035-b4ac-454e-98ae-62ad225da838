/**
 * 性能优化配置
 * 
 * 功能说明：
 * 1. 数据库连接池优化
 * 2. Redis缓存策略配置
 * 3. API响应时间优化
 * 4. 内存使用优化
 * 5. 并发处理优化
 */

import { PrismaClient } from '@prisma/client';
import Redis from 'ioredis';
import compression from 'compression';
import helmet from 'helmet';
import rateLimit from 'express-rate-limit';
import slowDown from 'express-slow-down';
import { Request, Response, NextFunction } from 'express';

/**
 * 性能配置接口
 */
interface PerformanceConfig {
  database: {
    connectionPoolSize: number;
    connectionTimeout: number;
    queryTimeout: number;
    statementTimeout: number;
  };
  redis: {
    maxRetriesPerRequest: number;
    retryDelayOnFailover: number;
    maxMemoryPolicy: string;
    keyPrefix: string;
  };
  api: {
    compressionLevel: number;
    compressionThreshold: number;
    responseTimeThreshold: number;
    maxRequestSize: string;
  };
  rateLimit: {
    windowMs: number;
    maxRequests: number;
    skipSuccessfulRequests: boolean;
    skipFailedRequests: boolean;
  };
  cache: {
    defaultTTL: number;
    maxKeys: number;
    checkPeriod: number;
  };
}

/**
 * 默认性能配置
 */
export const defaultPerformanceConfig: PerformanceConfig = {
  database: {
    connectionPoolSize: parseInt(process.env.DB_POOL_SIZE || '20'),
    connectionTimeout: parseInt(process.env.DB_CONNECTION_TIMEOUT || '10000'),
    queryTimeout: parseInt(process.env.DB_QUERY_TIMEOUT || '30000'),
    statementTimeout: parseInt(process.env.DB_STATEMENT_TIMEOUT || '60000')
  },
  redis: {
    maxRetriesPerRequest: 3,
    retryDelayOnFailover: 100,
    maxMemoryPolicy: 'allkeys-lru',
    keyPrefix: process.env.REDIS_KEY_PREFIX || 'idprovider:'
  },
  api: {
    compressionLevel: 6,
    compressionThreshold: 1024,
    responseTimeThreshold: 1000,
    maxRequestSize: '10mb'
  },
  rateLimit: {
    windowMs: 15 * 60 * 1000, // 15分钟
    maxRequests: 100,
    skipSuccessfulRequests: false,
    skipFailedRequests: false
  },
  cache: {
    defaultTTL: 300, // 5分钟
    maxKeys: 10000,
    checkPeriod: 600 // 10分钟
  }
};

/**
 * 数据库性能优化配置
 */
export function createOptimizedPrismaClient(): PrismaClient {
  const config = defaultPerformanceConfig.database;
  
  return new PrismaClient({
    datasources: {
      db: {
        url: process.env.DATABASE_URL
      }
    },
    log: process.env.NODE_ENV === 'development' ? ['query', 'info', 'warn', 'error'] : ['error'],
    errorFormat: 'pretty',
    // 连接池配置
    __internal: {
      engine: {
        connectionTimeout: config.connectionTimeout,
        poolTimeout: config.queryTimeout,
        statementTimeout: config.statementTimeout
      }
    }
  });
}

/**
 * Redis性能优化配置
 */
export function createOptimizedRedisClient(): Redis {
  const config = defaultPerformanceConfig.redis;
  
  return new Redis({
    host: process.env.REDIS_HOST || 'localhost',
    port: parseInt(process.env.REDIS_PORT || '6379'),
    password: process.env.REDIS_PASSWORD,
    db: parseInt(process.env.REDIS_DB || '0'),
    keyPrefix: config.keyPrefix,
    maxRetriesPerRequest: config.maxRetriesPerRequest,
    retryDelayOnFailover: config.retryDelayOnFailover,
    lazyConnect: true,
    keepAlive: 30000,
    // 连接池配置
    family: 4,
    connectTimeout: 10000,
    commandTimeout: 5000,
    // 内存优化
    maxMemoryPolicy: config.maxMemoryPolicy as any,
    // 性能优化
    enableAutoPipelining: true,
    maxRetriesPerRequest: 3
  });
}

/**
 * 压缩中间件配置
 */
export function createCompressionMiddleware() {
  const config = defaultPerformanceConfig.api;
  
  return compression({
    level: config.compressionLevel,
    threshold: config.compressionThreshold,
    filter: (req: Request, res: Response) => {
      // 不压缩已经压缩的内容
      if (req.headers['x-no-compression']) {
        return false;
      }
      
      // 只压缩文本内容
      const contentType = res.getHeader('content-type') as string;
      if (contentType && (
        contentType.includes('text/') ||
        contentType.includes('application/json') ||
        contentType.includes('application/javascript') ||
        contentType.includes('application/xml')
      )) {
        return true;
      }
      
      return false;
    }
  });
}

/**
 * 安全头部中间件配置
 */
export function createSecurityMiddleware() {
  return helmet({
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        styleSrc: ["'self'", "'unsafe-inline'"],
        scriptSrc: ["'self'"],
        imgSrc: ["'self'", "data:", "https:"],
        connectSrc: ["'self'"],
        fontSrc: ["'self'"],
        objectSrc: ["'none'"],
        mediaSrc: ["'self'"],
        frameSrc: ["'none'"]
      }
    },
    crossOriginEmbedderPolicy: false,
    hsts: {
      maxAge: 31536000,
      includeSubDomains: true,
      preload: true
    }
  });
}

/**
 * 速率限制中间件配置
 */
export function createRateLimitMiddleware() {
  const config = defaultPerformanceConfig.rateLimit;
  
  return rateLimit({
    windowMs: config.windowMs,
    max: config.maxRequests,
    message: {
      error: 'Too many requests from this IP, please try again later.',
      code: 'RATE_LIMIT_EXCEEDED'
    },
    standardHeaders: true,
    legacyHeaders: false,
    skipSuccessfulRequests: config.skipSuccessfulRequests,
    skipFailedRequests: config.skipFailedRequests,
    // 自定义键生成器
    keyGenerator: (req: Request) => {
      return req.ip || req.connection.remoteAddress || 'unknown';
    },
    // 跳过某些路径
    skip: (req: Request) => {
      const skipPaths = ['/health', '/metrics'];
      return skipPaths.includes(req.path);
    }
  });
}

/**
 * 慢速响应中间件配置
 */
export function createSlowDownMiddleware() {
  return slowDown({
    windowMs: 15 * 60 * 1000, // 15分钟
    delayAfter: 50, // 50次请求后开始延迟
    delayMs: 500, // 每次增加500ms延迟
    maxDelayMs: 20000, // 最大延迟20秒
    skipSuccessfulRequests: true
  });
}

/**
 * 响应时间监控中间件
 */
export function createResponseTimeMiddleware() {
  const threshold = defaultPerformanceConfig.api.responseTimeThreshold;
  
  return (req: Request, res: Response, next: NextFunction) => {
    const startTime = Date.now();
    
    res.on('finish', () => {
      const responseTime = Date.now() - startTime;
      
      // 记录响应时间
      res.setHeader('X-Response-Time', `${responseTime}ms`);
      
      // 如果响应时间超过阈值，记录警告
      if (responseTime > threshold) {
        console.warn(`Slow response detected: ${req.method} ${req.path} - ${responseTime}ms`, {
          method: req.method,
          path: req.path,
          responseTime,
          userAgent: req.get('User-Agent'),
          ip: req.ip
        });
      }
    });
    
    next();
  };
}

/**
 * 内存使用监控中间件
 */
export function createMemoryMonitorMiddleware() {
  let requestCount = 0;
  const monitorInterval = 100; // 每100个请求检查一次
  
  return (req: Request, res: Response, next: NextFunction) => {
    requestCount++;
    
    if (requestCount % monitorInterval === 0) {
      const memUsage = process.memoryUsage();
      const memUsageMB = {
        rss: Math.round(memUsage.rss / 1024 / 1024),
        heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024),
        heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024),
        external: Math.round(memUsage.external / 1024 / 1024)
      };
      
      // 如果内存使用超过阈值，记录警告
      if (memUsageMB.heapUsed > 512) { // 512MB阈值
        console.warn('High memory usage detected', memUsageMB);
      }
      
      // 设置响应头
      res.setHeader('X-Memory-Usage', JSON.stringify(memUsageMB));
    }
    
    next();
  };
}

/**
 * 缓存优化配置
 */
export class CacheOptimizer {
  private redis: Redis;
  private config: PerformanceConfig['cache'];
  
  constructor(redis: Redis, config?: Partial<PerformanceConfig['cache']>) {
    this.redis = redis;
    this.config = { ...defaultPerformanceConfig.cache, ...config };
  }
  
  /**
   * 智能缓存中间件
   */
  createCacheMiddleware() {
    return async (req: Request, res: Response, next: NextFunction) => {
      // 只缓存GET请求
      if (req.method !== 'GET') {
        return next();
      }
      
      // 跳过需要认证的请求
      if (req.headers.authorization) {
        return next();
      }
      
      const cacheKey = this.generateCacheKey(req);
      
      try {
        // 尝试从缓存获取
        const cachedResponse = await this.redis.get(cacheKey);
        
        if (cachedResponse) {
          const data = JSON.parse(cachedResponse);
          res.setHeader('X-Cache', 'HIT');
          res.setHeader('X-Cache-Key', cacheKey);
          return res.json(data);
        }
        
        // 缓存未命中，继续处理请求
        res.setHeader('X-Cache', 'MISS');
        
        // 拦截响应以缓存结果
        const originalJson = res.json;
        res.json = function(data: any) {
          // 只缓存成功响应
          if (res.statusCode === 200) {
            setImmediate(async () => {
              try {
                await this.redis.setex(
                  cacheKey,
                  this.config.defaultTTL,
                  JSON.stringify(data)
                );
              } catch (error) {
                console.error('Failed to cache response:', error);
              }
            });
          }
          
          return originalJson.call(this, data);
        }.bind(this);
        
        next();
      } catch (error) {
        console.error('Cache middleware error:', error);
        next();
      }
    };
  }
  
  /**
   * 生成缓存键
   */
  private generateCacheKey(req: Request): string {
    const url = req.originalUrl || req.url;
    const query = JSON.stringify(req.query);
    const acceptLanguage = req.get('Accept-Language') || 'en';
    
    return `api:${url}:${query}:${acceptLanguage}`;
  }
  
  /**
   * 清除缓存
   */
  async clearCache(pattern?: string): Promise<number> {
    try {
      const keys = await this.redis.keys(pattern || 'api:*');
      if (keys.length > 0) {
        return await this.redis.del(...keys);
      }
      return 0;
    } catch (error) {
      console.error('Failed to clear cache:', error);
      return 0;
    }
  }
  
  /**
   * 获取缓存统计
   */
  async getCacheStats(): Promise<{
    totalKeys: number;
    memoryUsage: string;
    hitRate: number;
  }> {
    try {
      const info = await this.redis.info('memory');
      const keyspace = await this.redis.info('keyspace');
      const stats = await this.redis.info('stats');
      
      // 解析Redis信息
      const memoryUsage = this.parseRedisInfo(info, 'used_memory_human');
      const totalKeys = this.parseRedisKeyspace(keyspace);
      const hitRate = this.parseRedisStats(stats);
      
      return {
        totalKeys,
        memoryUsage,
        hitRate
      };
    } catch (error) {
      console.error('Failed to get cache stats:', error);
      return {
        totalKeys: 0,
        memoryUsage: '0B',
        hitRate: 0
      };
    }
  }
  
  private parseRedisInfo(info: string, key: string): string {
    const lines = info.split('\r\n');
    const line = lines.find(l => l.startsWith(key));
    return line ? line.split(':')[1] : '0B';
  }
  
  private parseRedisKeyspace(keyspace: string): number {
    const lines = keyspace.split('\r\n');
    const dbLine = lines.find(l => l.startsWith('db0:'));
    if (dbLine) {
      const match = dbLine.match(/keys=(\d+)/);
      return match ? parseInt(match[1]) : 0;
    }
    return 0;
  }
  
  private parseRedisStats(stats: string): number {
    const lines = stats.split('\r\n');
    const hitsLine = lines.find(l => l.startsWith('keyspace_hits:'));
    const missesLine = lines.find(l => l.startsWith('keyspace_misses:'));
    
    if (hitsLine && missesLine) {
      const hits = parseInt(hitsLine.split(':')[1]);
      const misses = parseInt(missesLine.split(':')[1]);
      const total = hits + misses;
      return total > 0 ? (hits / total) * 100 : 0;
    }
    
    return 0;
  }
}

/**
 * 数据库查询优化器
 */
export class DatabaseOptimizer {
  private prisma: PrismaClient;
  
  constructor(prisma: PrismaClient) {
    this.prisma = prisma;
  }
  
  /**
   * 批量查询优化
   */
  async batchQuery<T>(
    queries: Array<() => Promise<T>>,
    batchSize: number = 10
  ): Promise<T[]> {
    const results: T[] = [];
    
    for (let i = 0; i < queries.length; i += batchSize) {
      const batch = queries.slice(i, i + batchSize);
      const batchResults = await Promise.all(batch.map(query => query()));
      results.push(...batchResults);
    }
    
    return results;
  }
  
  /**
   * 连接池状态监控
   */
  async getConnectionPoolStats(): Promise<{
    activeConnections: number;
    idleConnections: number;
    totalConnections: number;
  }> {
    try {
      // 这里需要根据实际的Prisma版本和配置来实现
      // 目前Prisma不直接暴露连接池统计信息
      return {
        activeConnections: 0,
        idleConnections: 0,
        totalConnections: 0
      };
    } catch (error) {
      console.error('Failed to get connection pool stats:', error);
      return {
        activeConnections: 0,
        idleConnections: 0,
        totalConnections: 0
      };
    }
  }
}

/**
 * 性能监控器
 */
export class PerformanceMonitor {
  private metrics: Map<string, number[]> = new Map();
  
  /**
   * 记录指标
   */
  recordMetric(name: string, value: number): void {
    if (!this.metrics.has(name)) {
      this.metrics.set(name, []);
    }
    
    const values = this.metrics.get(name)!;
    values.push(value);
    
    // 只保留最近1000个值
    if (values.length > 1000) {
      values.shift();
    }
  }
  
  /**
   * 获取指标统计
   */
  getMetricStats(name: string): {
    count: number;
    avg: number;
    min: number;
    max: number;
    p95: number;
    p99: number;
  } | null {
    const values = this.metrics.get(name);
    if (!values || values.length === 0) {
      return null;
    }
    
    const sorted = [...values].sort((a, b) => a - b);
    const count = sorted.length;
    const sum = sorted.reduce((a, b) => a + b, 0);
    
    return {
      count,
      avg: sum / count,
      min: sorted[0],
      max: sorted[count - 1],
      p95: sorted[Math.floor(count * 0.95)],
      p99: sorted[Math.floor(count * 0.99)]
    };
  }
  
  /**
   * 获取所有指标
   */
  getAllMetrics(): Record<string, any> {
    const result: Record<string, any> = {};
    
    for (const [name] of this.metrics) {
      result[name] = this.getMetricStats(name);
    }
    
    return result;
  }
  
  /**
   * 清除指标
   */
  clearMetrics(): void {
    this.metrics.clear();
  }
}

// 导出全局实例
export const performanceMonitor = new PerformanceMonitor();
