/**
 * 数据库优化脚本
 * 应用性能分析建议的数据库索引优化
 */

import { prisma } from '../config/database';
import { logger } from '../config/logger';
import { databaseOptimizer } from '../utils/database-optimizer';

class DatabaseOptimizer {
  
  /**
   * 执行数据库优化
   */
  async optimize(): Promise<void> {
    console.log('🔧 开始数据库优化...\n');

    try {
      // 1. 应用索引优化
      await this.applyIndexOptimizations();

      // 2. 清理过期数据
      await this.cleanupExpiredData();

      // 3. 更新统计信息
      await this.updateStatistics();

      // 4. 验证优化效果
      await this.validateOptimizations();

      console.log('\n✅ 数据库优化完成！');

    } catch (error) {
      console.error('❌ 数据库优化失败:', error);
      logger.error('数据库优化失败', {
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * 应用索引优化
   */
  private async applyIndexOptimizations(): Promise<void> {
    console.log('📊 应用索引优化');
    console.log('='.repeat(50));

    const indexQueries = [
      // 用户表索引
      {
        name: 'idx_users_email',
        sql: 'CREATE UNIQUE INDEX IF NOT EXISTS idx_users_email ON users (email);',
        description: '用户邮箱唯一索引'
      },
      {
        name: 'idx_users_active_verified',
        sql: 'CREATE INDEX IF NOT EXISTS idx_users_active_verified ON users (isActive, emailVerified);',
        description: '用户活跃状态和邮箱验证状态复合索引'
      },

      // 会话表索引
      {
        name: 'idx_sessions_user_expires',
        sql: 'CREATE INDEX IF NOT EXISTS idx_sessions_user_expires ON sessions (userId, expiresAt);',
        description: '会话用户ID和过期时间复合索引'
      },
      {
        name: 'idx_sessions_token',
        sql: 'CREATE UNIQUE INDEX IF NOT EXISTS idx_sessions_token ON sessions (sessionToken);',
        description: '会话令牌唯一索引'
      },

      // 审计日志表索引
      {
        name: 'idx_audit_logs_user_created',
        sql: 'CREATE INDEX IF NOT EXISTS idx_audit_logs_user_created ON audit_logs (userId, createdAt);',
        description: '审计日志用户ID和创建时间复合索引'
      },
      {
        name: 'idx_audit_logs_action_created',
        sql: 'CREATE INDEX IF NOT EXISTS idx_audit_logs_action_created ON audit_logs (action, createdAt);',
        description: '审计日志操作类型和创建时间复合索引'
      },

      // OAuth客户端表索引
      {
        name: 'idx_oauth_clients_client_id',
        sql: 'CREATE UNIQUE INDEX IF NOT EXISTS idx_oauth_clients_client_id ON oauth_clients (clientId);',
        description: 'OAuth客户端ID唯一索引'
      },

      // 授权码表索引
      {
        name: 'idx_authorization_codes_code',
        sql: 'CREATE UNIQUE INDEX IF NOT EXISTS idx_authorization_codes_code ON authorization_codes (code);',
        description: '授权码唯一索引'
      },
      {
        name: 'idx_authorization_codes_expires_used',
        sql: 'CREATE INDEX IF NOT EXISTS idx_authorization_codes_expires_used ON authorization_codes (expiresAt, used);',
        description: '授权码过期时间和使用状态复合索引'
      }
    ];

    for (const index of indexQueries) {
      try {
        console.log(`  创建索引: ${index.name}`);
        console.log(`  描述: ${index.description}`);
        
        await prisma.$executeRawUnsafe(index.sql);
        
        console.log(`  ✅ 索引创建成功\n`);
        
        logger.info('数据库索引创建成功', {
          indexName: index.name,
          description: index.description
        });

      } catch (error) {
        console.log(`  ⚠️  索引创建跳过 (可能已存在): ${index.name}\n`);
        
        logger.warn('数据库索引创建跳过', {
          indexName: index.name,
          error: error instanceof Error ? error.message : String(error)
        });
      }
    }
  }

  /**
   * 清理过期数据
   */
  private async cleanupExpiredData(): Promise<void> {
    console.log('🧹 清理过期数据');
    console.log('='.repeat(50));

    try {
      // 清理过期会话
      const expiredSessions = await prisma.session.deleteMany({
        where: {
          expiresAt: {
            lt: new Date()
          }
        }
      });

      console.log(`  清理过期会话: ${expiredSessions.count} 条`);

      // 清理过期授权码
      const expiredCodes = await prisma.authorizationCode.deleteMany({
        where: {
          OR: [
            { expiresAt: { lt: new Date() } },
            { used: true }
          ]
        }
      });

      console.log(`  清理过期授权码: ${expiredCodes.count} 条`);

      // 清理过期密码重置令牌
      const expiredResetTokens = await prisma.passwordResetToken.deleteMany({
        where: {
          OR: [
            { expiresAt: { lt: new Date() } },
            { used: true }
          ]
        }
      });

      console.log(`  清理过期密码重置令牌: ${expiredResetTokens.count} 条`);

      // 清理过期邮箱验证令牌
      const expiredEmailTokens = await prisma.emailVerificationToken.deleteMany({
        where: {
          OR: [
            { expiresAt: { lt: new Date() } },
            { used: true }
          ]
        }
      });

      console.log(`  清理过期邮箱验证令牌: ${expiredEmailTokens.count} 条`);

      // 清理旧的审计日志（保留最近3个月）
      const threeMonthsAgo = new Date();
      threeMonthsAgo.setMonth(threeMonthsAgo.getMonth() - 3);

      const oldAuditLogs = await prisma.auditLog.deleteMany({
        where: {
          createdAt: {
            lt: threeMonthsAgo
          }
        }
      });

      console.log(`  清理旧审计日志: ${oldAuditLogs.count} 条`);

      logger.info('过期数据清理完成', {
        expiredSessions: expiredSessions.count,
        expiredCodes: expiredCodes.count,
        expiredResetTokens: expiredResetTokens.count,
        expiredEmailTokens: expiredEmailTokens.count,
        oldAuditLogs: oldAuditLogs.count
      });

    } catch (error) {
      console.error('  ❌ 清理过期数据失败:', error);
      throw error;
    }
  }

  /**
   * 更新统计信息
   */
  private async updateStatistics(): Promise<void> {
    console.log('\n📈 更新数据库统计信息');
    console.log('='.repeat(50));

    try {
      // 获取表统计信息
      const tableStats = await databaseOptimizer.getTableStats();
      
      console.log('\n📋 数据库表统计:');
      tableStats.forEach(stat => {
        if (stat.error) {
          console.log(`  ❌ ${stat.table}: ${stat.error}`);
        } else {
          console.log(`  📊 ${stat.table}: ${stat.rowCount.toLocaleString()} 行`);
        }
      });

      // 计算总记录数
      const totalRecords = tableStats
        .filter(stat => !stat.error)
        .reduce((sum, stat) => sum + Number(stat.rowCount), 0);

      console.log(`\n📊 总记录数: ${totalRecords.toLocaleString()}`);

      // 转换BigInt为数字以便序列化
      const serializedTableStats = tableStats.map(stat => ({
        ...stat,
        rowCount: Number(stat.rowCount)
      }));

      logger.info('数据库统计信息更新完成', {
        tableStats: serializedTableStats,
        totalRecords
      });

    } catch (error) {
      console.error('  ❌ 更新统计信息失败:', error);
      throw error;
    }
  }

  /**
   * 验证优化效果
   */
  private async validateOptimizations(): Promise<void> {
    console.log('\n🔍 验证优化效果');
    console.log('='.repeat(50));

    try {
      // 测试关键查询的性能
      const testQueries = [
        {
          name: '用户邮箱查询',
          query: `SELECT id, email FROM users WHERE email = '<EMAIL>' LIMIT 1`
        },
        {
          name: '活跃用户查询',
          query: `SELECT COUNT(*) as count FROM users WHERE isActive = true AND emailVerified = true`
        },
        {
          name: '用户会话查询',
          query: `SELECT COUNT(*) as count FROM sessions WHERE userId = 'test-user-id' AND expiresAt > datetime('now')`
        },
        {
          name: '最近审计日志查询',
          query: `SELECT COUNT(*) as count FROM audit_logs WHERE createdAt > datetime('now', '-1 day')`
        }
      ];

      for (const test of testQueries) {
        try {
          const startTime = Date.now();
          await prisma.$queryRawUnsafe(test.query);
          const duration = Date.now() - startTime;

          console.log(`  ✅ ${test.name}: ${duration}ms`);

          if (duration > 100) {
            console.log(`    ⚠️  查询较慢，建议进一步优化`);
          }

        } catch (error) {
          console.log(`  ❌ ${test.name}: 查询失败`);
        }
      }

      logger.info('数据库优化验证完成');

    } catch (error) {
      console.error('  ❌ 验证优化效果失败:', error);
      throw error;
    }
  }

  /**
   * 生成优化报告
   */
  async generateOptimizationReport(): Promise<void> {
    console.log('\n📋 生成优化报告');
    console.log('='.repeat(50));

    try {
      const report = {
        timestamp: new Date().toISOString(),
        optimizations: [
          '✅ 用户表邮箱唯一索引',
          '✅ 用户活跃状态复合索引',
          '✅ 会话表复合索引',
          '✅ 审计日志复合索引',
          '✅ OAuth客户端索引',
          '✅ 授权码索引'
        ],
        cleanup: [
          '✅ 清理过期会话',
          '✅ 清理过期授权码',
          '✅ 清理过期令牌',
          '✅ 清理旧审计日志'
        ],
        recommendations: [
          '定期执行数据库优化脚本',
          '监控慢查询并及时优化',
          '定期清理过期数据',
          '考虑实施数据归档策略'
        ]
      };

      console.log('\n📊 优化总结:');
      console.log(`  时间: ${report.timestamp}`);
      console.log(`  索引优化: ${report.optimizations.length} 项`);
      console.log(`  数据清理: ${report.cleanup.length} 项`);
      
      console.log('\n💡 建议:');
      report.recommendations.forEach((rec, index) => {
        console.log(`  ${index + 1}. ${rec}`);
      });

      logger.info('数据库优化报告生成完成', report);

    } catch (error) {
      console.error('  ❌ 生成优化报告失败:', error);
      throw error;
    }
  }
}

// 主函数
async function main() {
  const optimizer = new DatabaseOptimizer();
  
  try {
    await optimizer.optimize();
    await optimizer.generateOptimizationReport();
    
    console.log('\n🎉 数据库优化成功完成！');
    
  } catch (error) {
    console.error('\n❌ 数据库优化失败:', error);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main()
    .then(() => {
      console.log('\n✨ 优化脚本执行完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('优化脚本执行失败:', error);
      process.exit(1);
    });
}

export { DatabaseOptimizer };
