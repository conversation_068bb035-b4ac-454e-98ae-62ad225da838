/**
 * 创建测试OIDC客户端脚本
 * 用于开发和测试环境创建示例客户端
 */

import { oidcClientService } from '../services/oidc-client.service';
import { logger } from '../config/logger';

async function createTestClient() {
  try {
    console.log('🚀 创建测试OIDC客户端...');

    const client = await oidcClientService.createClient({
      name: 'Test Application',
      description: '用于开发和测试的示例应用程序',
      redirectUris: [
        'http://localhost:3001/callback',
        'http://localhost:3001/auth/callback',
        'https://oauth.pstmn.io/v1/callback', // Postman OAuth测试
        'https://oidcdebugger.com/debug'      // OIDC调试工具
      ],
      grantTypes: [
        'authorization_code',
        'refresh_token',
        'client_credentials'
      ],
      responseTypes: [
        'code',
        'token',
        'id_token',
        'code token',
        'code id_token'
      ],
      scopes: [
        'openid',
        'profile',
        'email',
        'offline_access'
      ],
      tokenEndpointAuthMethod: 'client_secret_basic',
      requirePkce: false,
      requireConsent: true,
      accessTokenLifetime: 3600,      // 1小时
      refreshTokenLifetime: 2592000,  // 30天
      idTokenLifetime: 3600,          // 1小时
      clientUri: 'http://localhost:3001',
      logoUri: 'http://localhost:3001/logo.png'
    });

    console.log('✅ 测试客户端创建成功！');
    console.log('');
    console.log('📋 客户端信息:');
    console.log(`   客户端ID: ${client.clientId}`);
    console.log(`   客户端密钥: ${client.clientSecret}`);
    console.log(`   名称: ${client.name}`);
    console.log(`   重定向URI: ${client.redirectUris.join(', ')}`);
    console.log(`   授权类型: ${client.grantTypes.join(', ')}`);
    console.log(`   响应类型: ${client.responseTypes.join(', ')}`);
    console.log(`   权限范围: ${client.scopes.join(', ')}`);
    console.log('');
    console.log('🔗 测试URL示例:');
    
    const baseUrl = 'http://localhost:3000';
    const authUrl = new URL(`${baseUrl}/oauth2/authorize`);
    authUrl.searchParams.set('response_type', 'code');
    authUrl.searchParams.set('client_id', client.clientId);
    authUrl.searchParams.set('redirect_uri', 'http://localhost:3001/callback');
    authUrl.searchParams.set('scope', 'openid profile email');
    authUrl.searchParams.set('state', 'test-state-123');
    
    console.log(`   授权URL: ${authUrl.toString()}`);
    console.log(`   令牌端点: ${baseUrl}/oauth2/token`);
    console.log(`   用户信息端点: ${baseUrl}/oauth2/userinfo`);
    console.log(`   JWKS端点: ${baseUrl}/.well-known/jwks.json`);
    console.log(`   发现端点: ${baseUrl}/.well-known/openid-configuration`);
    console.log('');
    console.log('📖 使用说明:');
    console.log('1. 访问授权URL进行用户登录和授权');
    console.log('2. 获取授权码后，使用客户端凭据交换访问令牌');
    console.log('3. 使用访问令牌调用用户信息端点');
    console.log('');
    console.log('🧪 Postman测试:');
    console.log('1. 在Postman中创建新的OAuth 2.0授权');
    console.log('2. 设置授权URL和令牌URL');
    console.log('3. 输入客户端ID和密钥');
    console.log('4. 设置重定向URI为 https://oauth.pstmn.io/v1/callback');
    console.log('5. 选择权限范围: openid profile email');

  } catch (error) {
    console.error('❌ 创建测试客户端失败:', error);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  createTestClient()
    .then(() => {
      console.log('');
      console.log('🎉 测试客户端创建完成！');
      process.exit(0);
    })
    .catch((error) => {
      console.error('脚本执行失败:', error);
      process.exit(1);
    });
}

export { createTestClient };
