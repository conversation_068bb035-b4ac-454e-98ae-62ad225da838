/**
 * 性能分析脚本
 * 分析系统性能，生成优化建议和报告
 */

import { databaseOptimizer } from '../utils/database-optimizer';
import { getPerformanceReport } from '../middleware/performance.middleware';
import { redisService } from '../services/redis.service';
import { prisma } from '../config/database';
import { logger } from '../config/logger';

class PerformanceAnalyzer {
  
  /**
   * 执行完整的性能分析
   */
  async runFullAnalysis(): Promise<void> {
    console.log('🔍 开始性能分析...\n');

    try {
      // 1. 数据库性能分析
      await this.analyzeDatabasePerformance();

      // 2. 缓存性能分析
      await this.analyzeCachePerformance();

      // 3. 系统资源分析
      await this.analyzeSystemResources();

      // 4. 生成索引建议
      await this.generateIndexRecommendations();

      // 5. 生成综合报告
      await this.generateComprehensiveReport();

      console.log('\n✅ 性能分析完成！');

    } catch (error) {
      console.error('❌ 性能分析失败:', error);
      logger.error('性能分析失败', {
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  /**
   * 数据库性能分析
   */
  private async analyzeDatabasePerformance(): Promise<void> {
    console.log('📊 数据库性能分析');
    console.log('='.repeat(50));

    try {
      // 获取表统计信息
      const tableStats = await databaseOptimizer.getTableStats();
      console.log('\n📋 数据库表统计:');
      tableStats.forEach(stat => {
        if (stat.error) {
          console.log(`  ❌ ${stat.table}: ${stat.error}`);
        } else {
          console.log(`  📊 ${stat.table}: ${stat.rowCount.toLocaleString()} 行`);
        }
      });

      // 获取慢查询统计
      const slowQueryStats = databaseOptimizer.getSlowQueryStats();
      console.log('\n🐌 慢查询统计:');
      console.log(`  总查询类型: ${slowQueryStats.totalUniqueQueries}`);
      console.log(`  总执行次数: ${slowQueryStats.totalExecutions}`);
      console.log(`  平均执行时间: ${slowQueryStats.averageDuration.toFixed(2)}ms`);
      
      console.log('\n📈 严重程度分布:');
      console.log(`  低: ${slowQueryStats.severityDistribution.low}`);
      console.log(`  中: ${slowQueryStats.severityDistribution.medium}`);
      console.log(`  高: ${slowQueryStats.severityDistribution.high}`);

      if (slowQueryStats.topSlowQueries.length > 0) {
        console.log('\n🔝 最慢查询 (前5个):');
        slowQueryStats.topSlowQueries.slice(0, 5).forEach((query, index) => {
          console.log(`  ${index + 1}. 平均: ${query.avgDuration.toFixed(2)}ms, 最大: ${query.maxDuration}ms`);
          console.log(`     查询: ${query.query}`);
        });
      }

    } catch (error) {
      console.error('  ❌ 数据库分析失败:', error);
    }
  }

  /**
   * 缓存性能分析
   */
  private async analyzeCachePerformance(): Promise<void> {
    console.log('\n💾 缓存性能分析');
    console.log('='.repeat(50));

    try {
      // Redis连接状态
      const isRedisConnected = redisService.isReady();
      console.log(`\n🔗 Redis连接状态: ${isRedisConnected ? '✅ 已连接' : '❌ 未连接'}`);

      if (isRedisConnected) {
        const client = redisService.getClient();
        if (client) {
          // 获取Redis信息
          const info = await client.info();
          const memoryInfo = await client.info('memory');
          const statsInfo = await client.info('stats');

          // 解析内存信息
          const memoryLines = memoryInfo.split('\n');
          const usedMemory = this.parseRedisInfoValue(memoryLines, 'used_memory_human');
          const maxMemory = this.parseRedisInfoValue(memoryLines, 'maxmemory_human');
          const fragmentation = this.parseRedisInfoValue(memoryLines, 'mem_fragmentation_ratio');

          console.log('\n📊 Redis内存使用:');
          console.log(`  已用内存: ${usedMemory || 'N/A'}`);
          console.log(`  最大内存: ${maxMemory || 'N/A'}`);
          console.log(`  碎片率: ${fragmentation || 'N/A'}`);

          // 解析统计信息
          const statsLines = statsInfo.split('\n');
          const totalConnections = this.parseRedisInfoValue(statsLines, 'total_connections_received');
          const totalCommands = this.parseRedisInfoValue(statsLines, 'total_commands_processed');
          const keyspaceHits = this.parseRedisInfoValue(statsLines, 'keyspace_hits');
          const keyspaceMisses = this.parseRedisInfoValue(statsLines, 'keyspace_misses');

          console.log('\n📈 Redis统计:');
          console.log(`  总连接数: ${totalConnections || 'N/A'}`);
          console.log(`  总命令数: ${totalCommands || 'N/A'}`);
          console.log(`  缓存命中: ${keyspaceHits || 'N/A'}`);
          console.log(`  缓存未命中: ${keyspaceMisses || 'N/A'}`);

          if (keyspaceHits && keyspaceMisses) {
            const hitRate = (parseInt(keyspaceHits) / (parseInt(keyspaceHits) + parseInt(keyspaceMisses))) * 100;
            console.log(`  命中率: ${hitRate.toFixed(2)}%`);
          }
        }
      }

    } catch (error) {
      console.error('  ❌ 缓存分析失败:', error);
    }
  }

  /**
   * 系统资源分析
   */
  private async analyzeSystemResources(): Promise<void> {
    console.log('\n🖥️  系统资源分析');
    console.log('='.repeat(50));

    try {
      const memoryUsage = process.memoryUsage();
      const cpuUsage = process.cpuUsage();
      const uptime = process.uptime();

      console.log('\n💾 内存使用:');
      console.log(`  RSS: ${(memoryUsage.rss / 1024 / 1024).toFixed(2)} MB`);
      console.log(`  堆已用: ${(memoryUsage.heapUsed / 1024 / 1024).toFixed(2)} MB`);
      console.log(`  堆总计: ${(memoryUsage.heapTotal / 1024 / 1024).toFixed(2)} MB`);
      console.log(`  外部: ${(memoryUsage.external / 1024 / 1024).toFixed(2)} MB`);

      console.log('\n⚡ CPU使用:');
      console.log(`  用户时间: ${(cpuUsage.user / 1000).toFixed(2)} ms`);
      console.log(`  系统时间: ${(cpuUsage.system / 1000).toFixed(2)} ms`);

      console.log('\n⏱️  运行时间:');
      console.log(`  运行时长: ${(uptime / 60).toFixed(2)} 分钟`);

      // 内存使用警告
      const heapUsedMB = memoryUsage.heapUsed / 1024 / 1024;
      if (heapUsedMB > 500) {
        console.log('\n⚠️  警告: 内存使用过高 (>500MB)');
      }

      // 获取系统负载（非Windows系统）
      if (process.platform !== 'win32') {
        const os = require('os');
        const loadAvg = os.loadavg();
        const cpuCount = os.cpus().length;

        console.log('\n📊 系统负载:');
        console.log(`  1分钟: ${loadAvg[0].toFixed(2)}`);
        console.log(`  5分钟: ${loadAvg[1].toFixed(2)}`);
        console.log(`  15分钟: ${loadAvg[2].toFixed(2)}`);
        console.log(`  CPU核心数: ${cpuCount}`);

        // 负载警告
        if (loadAvg[0] > cpuCount) {
          console.log('\n⚠️  警告: 系统负载过高');
        }
      }

    } catch (error) {
      console.error('  ❌ 系统资源分析失败:', error);
    }
  }

  /**
   * 生成索引建议
   */
  private async generateIndexRecommendations(): Promise<void> {
    console.log('\n🔍 索引优化建议');
    console.log('='.repeat(50));

    try {
      const suggestions = await databaseOptimizer.generateIndexSuggestions();

      if (suggestions.length === 0) {
        console.log('\n✅ 暂无索引优化建议');
        return;
      }

      console.log(`\n📋 发现 ${suggestions.length} 个索引优化建议:\n`);

      suggestions.forEach((suggestion, index) => {
        console.log(`${index + 1}. 表: ${suggestion.table}`);
        console.log(`   列: ${suggestion.columns.join(', ')}`);
        console.log(`   类型: ${suggestion.type}`);
        console.log(`   原因: ${suggestion.reason}`);
        console.log(`   预期改进: ${suggestion.estimatedImprovement}`);
        console.log('');
      });

      // 生成SQL语句（示例）
      console.log('📝 建议的SQL语句:');
      suggestions.forEach((suggestion, index) => {
        const indexName = `idx_${suggestion.table}_${suggestion.columns.join('_')}`;
        const unique = suggestion.type === 'unique' ? 'UNIQUE ' : '';
        const sql = `CREATE ${unique}INDEX ${indexName} ON ${suggestion.table} (${suggestion.columns.join(', ')});`;
        console.log(`${index + 1}. ${sql}`);
      });

    } catch (error) {
      console.error('  ❌ 索引建议生成失败:', error);
    }
  }

  /**
   * 生成综合报告
   */
  private async generateComprehensiveReport(): Promise<void> {
    console.log('\n📊 综合性能报告');
    console.log('='.repeat(50));

    try {
      const report = getPerformanceReport();

      console.log('\n📈 API性能:');
      if (report.api) {
        console.log(`  请求总数: ${report.api.count}`);
        console.log(`  平均响应时间: ${report.api.avg?.toFixed(2)}ms`);
        console.log(`  最快响应: ${report.api.min}ms`);
        console.log(`  最慢响应: ${report.api.max}ms`);
        console.log(`  95%分位数: ${report.api.p95}ms`);
        console.log(`  99%分位数: ${report.api.p99}ms`);
      } else {
        console.log('  暂无API性能数据');
      }

      console.log('\n🗄️  数据库性能:');
      if (report.database?.recentStats) {
        const dbStats = report.database.recentStats;
        console.log(`  查询总数: ${dbStats.count}`);
        console.log(`  平均查询时间: ${dbStats.avg?.toFixed(2)}ms`);
        console.log(`  最快查询: ${dbStats.min}ms`);
        console.log(`  最慢查询: ${dbStats.max}ms`);
      } else {
        console.log('  暂无数据库性能数据');
      }

      // 性能评分
      const score = this.calculatePerformanceScore(report);
      console.log('\n🏆 性能评分:');
      console.log(`  总分: ${score.total}/100`);
      console.log(`  API性能: ${score.api}/25`);
      console.log(`  数据库性能: ${score.database}/25`);
      console.log(`  缓存性能: ${score.cache}/25`);
      console.log(`  系统资源: ${score.system}/25`);

      // 优化建议
      console.log('\n💡 优化建议:');
      const recommendations = this.generateOptimizationRecommendations(report, score);
      recommendations.forEach((rec, index) => {
        console.log(`  ${index + 1}. ${rec}`);
      });

    } catch (error) {
      console.error('  ❌ 综合报告生成失败:', error);
    }
  }

  /**
   * 解析Redis INFO输出中的值
   */
  private parseRedisInfoValue(lines: string[], key: string): string | null {
    const line = lines.find(l => l.startsWith(key + ':'));
    return line ? line.split(':')[1]?.trim() : null;
  }

  /**
   * 计算性能评分
   */
  private calculatePerformanceScore(report: any): any {
    const score = {
      total: 0,
      api: 0,
      database: 0,
      cache: 0,
      system: 0
    };

    // API性能评分 (25分)
    if (report.api?.avg) {
      if (report.api.avg < 100) score.api = 25;
      else if (report.api.avg < 300) score.api = 20;
      else if (report.api.avg < 500) score.api = 15;
      else if (report.api.avg < 1000) score.api = 10;
      else score.api = 5;
    }

    // 数据库性能评分 (25分)
    if (report.database?.recentStats?.avg) {
      if (report.database.recentStats.avg < 50) score.database = 25;
      else if (report.database.recentStats.avg < 100) score.database = 20;
      else if (report.database.recentStats.avg < 200) score.database = 15;
      else if (report.database.recentStats.avg < 500) score.database = 10;
      else score.database = 5;
    }

    // 缓存性能评分 (25分) - 基于命中率
    if (report.cache?.hitRate) {
      const hitRate = parseFloat(report.cache.hitRate.replace('%', ''));
      if (hitRate > 90) score.cache = 25;
      else if (hitRate > 80) score.cache = 20;
      else if (hitRate > 70) score.cache = 15;
      else if (hitRate > 60) score.cache = 10;
      else score.cache = 5;
    }

    // 系统资源评分 (25分) - 基于内存使用
    if (report.system?.memory) {
      const heapUsed = parseFloat(report.system.memory.heapUsed.replace(' MB', ''));
      if (heapUsed < 200) score.system = 25;
      else if (heapUsed < 400) score.system = 20;
      else if (heapUsed < 600) score.system = 15;
      else if (heapUsed < 800) score.system = 10;
      else score.system = 5;
    }

    score.total = score.api + score.database + score.cache + score.system;
    return score;
  }

  /**
   * 生成优化建议
   */
  private generateOptimizationRecommendations(report: any, score: any): string[] {
    const recommendations: string[] = [];

    if (score.api < 20) {
      recommendations.push('API响应时间较慢，考虑添加缓存或优化业务逻辑');
    }

    if (score.database < 20) {
      recommendations.push('数据库查询较慢，检查索引和查询优化');
    }

    if (score.cache < 20) {
      recommendations.push('缓存命中率较低，优化缓存策略');
    }

    if (score.system < 20) {
      recommendations.push('系统资源使用较高，考虑优化内存使用');
    }

    if (score.total > 80) {
      recommendations.push('系统性能良好，继续保持');
    } else if (score.total > 60) {
      recommendations.push('系统性能一般，有优化空间');
    } else {
      recommendations.push('系统性能需要重点优化');
    }

    return recommendations;
  }
}

// 主函数
async function main() {
  const analyzer = new PerformanceAnalyzer();
  await analyzer.runFullAnalysis();
}

// 如果直接运行此脚本
if (require.main === module) {
  main()
    .then(() => {
      console.log('\n🎉 性能分析完成！');
      process.exit(0);
    })
    .catch((error) => {
      console.error('性能分析失败:', error);
      process.exit(1);
    });
}

export { PerformanceAnalyzer };
