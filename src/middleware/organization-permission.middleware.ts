/**
 * 组织权限验证中间件
 * 实现基于组织架构的API权限控制
 */

import { Request, Response, NextFunction } from 'express';
import { logger } from '@/config/logger';
import { organizationPermissionService, PermissionContext } from '@/services/organization-permission.service';
import { organizationService } from '@/services/organization.service';

/**
 * 扩展Request接口以支持组织上下文
 */
declare global {
  namespace Express {
    interface Request {
      organizationContext?: {
        organizationId: string;
        organizationPath: string;
        userRole: string;
        permissions: string[];
      };
    }
  }
}

/**
 * 组织权限验证选项
 */
export interface OrganizationPermissionOptions {
  permission: string;
  scope?: 'self' | 'children' | 'descendants';
  resourceType?: string;
  allowCrossOrganization?: boolean;
  requireOwnership?: boolean;
  fallbackToGlobal?: boolean;
}

/**
 * 组织权限验证中间件
 */
export function requireOrganizationPermission(
  permission: string,
  options: Partial<OrganizationPermissionOptions> = {}
) {
  return async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const userId = req.user?.id;
      if (!userId) {
        res.status(401).json({
          success: false,
          error: 'UNAUTHORIZED',
          message: '用户未认证'
        });
        return;
      }

      // 获取组织ID（从路径参数、查询参数或请求体中）
      const organizationId = req.params.organizationId || 
                           req.query.organizationId as string || 
                           req.body.organizationId;

      if (!organizationId && !options.fallbackToGlobal) {
        res.status(400).json({
          success: false,
          error: 'MISSING_ORGANIZATION_ID',
          message: '缺少组织ID参数'
        });
        return;
      }

      // 构建权限上下文
      const context: PermissionContext = {
        userId,
        organizationId,
        action: permission,
        resourceType: options.resourceType,
        resourceId: req.params.resourceId || req.body.resourceId,
        ipAddress: req.ip,
        userAgent: req.get('User-Agent')
      };

      // 验证权限
      const result = await organizationPermissionService.validatePermission(context);

      if (!result.granted) {
        logger.warn('组织权限验证失败', {
          userId,
          organizationId,
          permission,
          reason: result.reason,
          path: req.path,
          method: req.method
        });

        res.status(403).json({
          success: false,
          error: 'INSUFFICIENT_ORGANIZATION_PERMISSION',
          message: result.reason || '组织权限不足',
          details: {
            requiredPermission: permission,
            organizationId,
            scope: options.scope
          }
        });
        return;
      }

      // 设置组织上下文
      if (organizationId) {
        const organization = await organizationService.getOrganizationById(organizationId);
        if (organization) {
          const userOrgs = await organizationService.getUserOrganizations(userId);
          const membership = userOrgs.find(m => m.organizationId === organizationId);

          req.organizationContext = {
            organizationId,
            organizationPath: organization.path,
            userRole: membership?.role || 'unknown',
            permissions: result.inheritanceChain || []
          };
        }
      }

      logger.info('组织权限验证成功', {
        userId,
        organizationId,
        permission,
        organizationPath: result.organizationPath,
        inheritanceChain: result.inheritanceChain
      });

      next();
    } catch (error) {
      logger.error('组织权限验证中间件错误', {
        error: error instanceof Error ? error.message : String(error),
        userId: req.user?.id,
        path: req.path,
        method: req.method
      });

      res.status(500).json({
        success: false,
        error: 'PERMISSION_CHECK_FAILED',
        message: '权限验证失败'
      });
    }
  };
}

/**
 * 跨组织访问权限验证中间件
 */
export function requireCrossOrganizationPermission(
  permission: string,
  options: {
    sourceOrgParam?: string;
    targetOrgParam?: string;
    resourceType?: string;
  } = {}
) {
  return async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const userId = req.user?.id;
      if (!userId) {
        res.status(401).json({
          success: false,
          error: 'UNAUTHORIZED',
          message: '用户未认证'
        });
        return;
      }

      // 获取源组织和目标组织ID
      const sourceOrgId = req.params[options.sourceOrgParam || 'sourceOrganizationId'] ||
                         req.body[options.sourceOrgParam || 'sourceOrganizationId'];
      const targetOrgId = req.params[options.targetOrgParam || 'targetOrganizationId'] ||
                         req.body[options.targetOrgParam || 'targetOrganizationId'];

      if (!sourceOrgId || !targetOrgId) {
        res.status(400).json({
          success: false,
          error: 'MISSING_ORGANIZATION_IDS',
          message: '缺少源组织或目标组织ID'
        });
        return;
      }

      // 获取组织路径
      const [sourceOrg, targetOrg] = await Promise.all([
        organizationService.getOrganizationById(sourceOrgId),
        organizationService.getOrganizationById(targetOrgId)
      ]);

      if (!sourceOrg || !targetOrg) {
        res.status(404).json({
          success: false,
          error: 'ORGANIZATION_NOT_FOUND',
          message: '组织不存在'
        });
        return;
      }

      // 检查跨组织访问权限
      const hasAccess = await organizationPermissionService.checkCrossOrganizationAccess(
        userId,
        sourceOrg.path,
        targetOrg.path,
        permission
      );

      if (!hasAccess) {
        logger.warn('跨组织权限验证失败', {
          userId,
          sourceOrgId,
          targetOrgId,
          permission,
          sourceOrgPath: sourceOrg.path,
          targetOrgPath: targetOrg.path
        });

        res.status(403).json({
          success: false,
          error: 'INSUFFICIENT_CROSS_ORGANIZATION_PERMISSION',
          message: '跨组织访问权限不足',
          details: {
            sourceOrganization: sourceOrg.path,
            targetOrganization: targetOrg.path,
            requiredPermission: permission
          }
        });
        return;
      }

      // 设置跨组织上下文
      req.organizationContext = {
        organizationId: targetOrgId,
        organizationPath: targetOrg.path,
        userRole: 'cross_org_access',
        permissions: [`cross_org:${permission}`]
      };

      logger.info('跨组织权限验证成功', {
        userId,
        sourceOrgPath: sourceOrg.path,
        targetOrgPath: targetOrg.path,
        permission
      });

      next();
    } catch (error) {
      logger.error('跨组织权限验证中间件错误', {
        error: error instanceof Error ? error.message : String(error),
        userId: req.user?.id,
        path: req.path,
        method: req.method
      });

      res.status(500).json({
        success: false,
        error: 'CROSS_ORG_PERMISSION_CHECK_FAILED',
        message: '跨组织权限验证失败'
      });
    }
  };
}

/**
 * 组织成员身份验证中间件
 */
export function requireOrganizationMembership(
  roles: string[] = [],
  options: {
    allowOwner?: boolean;
    allowInactive?: boolean;
  } = {}
) {
  return async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const userId = req.user?.id;
      const organizationId = req.params.organizationId || 
                           req.query.organizationId as string || 
                           req.body.organizationId;

      if (!userId || !organizationId) {
        res.status(400).json({
          success: false,
          error: 'MISSING_PARAMETERS',
          message: '缺少用户ID或组织ID'
        });
        return;
      }

      // 检查用户是否为组织成员
      const userOrgs = await organizationService.getUserOrganizations(userId);
      const membership = userOrgs.find(m => m.organizationId === organizationId);

      if (!membership) {
        res.status(403).json({
          success: false,
          error: 'NOT_ORGANIZATION_MEMBER',
          message: '用户不是该组织的成员'
        });
        return;
      }

      // 检查成员状态
      if (!options.allowInactive && membership.status !== 'active') {
        res.status(403).json({
          success: false,
          error: 'INACTIVE_MEMBERSHIP',
          message: '组织成员身份已失效'
        });
        return;
      }

      // 检查角色要求
      if (roles.length > 0) {
        const hasRequiredRole = roles.includes(membership.role) ||
                               (options.allowOwner && membership.isOwner);

        if (!hasRequiredRole) {
          res.status(403).json({
            success: false,
            error: 'INSUFFICIENT_ROLE',
            message: '组织角色权限不足',
            details: {
              userRole: membership.role,
              requiredRoles: roles,
              isOwner: membership.isOwner
            }
          });
          return;
        }
      }

      // 设置成员上下文
      const organization = await organizationService.getOrganizationById(organizationId);
      if (organization) {
        req.organizationContext = {
          organizationId,
          organizationPath: organization.path,
          userRole: membership.role,
          permissions: membership.permissions
        };
      }

      logger.info('组织成员身份验证成功', {
        userId,
        organizationId,
        role: membership.role,
        isOwner: membership.isOwner
      });

      next();
    } catch (error) {
      logger.error('组织成员身份验证中间件错误', {
        error: error instanceof Error ? error.message : String(error),
        userId: req.user?.id,
        path: req.path,
        method: req.method
      });

      res.status(500).json({
        success: false,
        error: 'MEMBERSHIP_CHECK_FAILED',
        message: '成员身份验证失败'
      });
    }
  };
}

/**
 * 数据访问过滤中间件
 * 基于组织架构自动过滤数据查询结果
 */
export function applyOrganizationDataFilter() {
  return async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const userId = req.user?.id;
      if (!userId) {
        next();
        return;
      }

      // 获取用户的组织关系
      const userOrgs = await organizationService.getUserOrganizations(userId);
      
      if (userOrgs.length === 0) {
        // 用户不属于任何组织，可能需要特殊处理
        req.organizationContext = {
          organizationId: '',
          organizationPath: '',
          userRole: 'none',
          permissions: []
        };
        next();
        return;
      }

      // 构建可访问的组织路径列表
      const accessibleOrgPaths = new Set<string>();
      
      for (const membership of userOrgs) {
        const org = await organizationService.getOrganizationById(membership.organizationId);
        if (org) {
          accessibleOrgPaths.add(org.path);
          
          // 根据权限添加子组织
          if (membership.permissions.includes('read:descendants') || 
              membership.role === 'admin' || 
              membership.isOwner) {
            const descendants = await organizationService.getDescendantOrganizations(org.path);
            descendants.forEach(desc => accessibleOrgPaths.add(desc.path));
          }
        }
      }

      // 将可访问的组织路径添加到请求上下文
      req.organizationContext = {
        organizationId: userOrgs[0].organizationId, // 主要组织
        organizationPath: userOrgs[0].organization?.path || '',
        userRole: userOrgs[0].role,
        permissions: Array.from(accessibleOrgPaths)
      };

      next();
    } catch (error) {
      logger.error('数据访问过滤中间件错误', {
        error: error instanceof Error ? error.message : String(error),
        userId: req.user?.id
      });
      
      // 出错时不阻止请求，但记录错误
      next();
    }
  };
}
