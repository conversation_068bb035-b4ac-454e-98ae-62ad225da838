/**
 * 零信任中间件
 * 实现零信任架构的核心中间件，集成风险评估、设备识别和自适应认证
 */

import { Request, Response, NextFunction } from 'express';
import { logger } from '@/config/logger';
import { riskAssessmentService, RiskLevel } from '@/services/risk-assessment.service';
import { deviceFingerprintService } from '@/services/device-fingerprint.service';
import { adaptiveAuthService, AuthRequirementLevel } from '@/services/adaptive-auth.service';
import { securityAuditService, AuditEventType, AuditSeverity } from '@/services/security-audit.service';
import { metricsCollector } from '@/services/metrics-collector.service';
import { cacheService } from '@/services/cache.service';

/**
 * 零信任配置接口
 */
interface ZeroTrustConfig {
  enabled: boolean;
  strictMode: boolean;
  riskThresholds: {
    allow: number;
    challenge: number;
    deny: number;
  };
  deviceTrustThresholds: {
    trusted: number;
    suspicious: number;
    untrusted: number;
  };
  sessionLimits: {
    maxConcurrent: number;
    maxDuration: number;
    idleTimeout: number;
  };
  exemptPaths: string[];
  exemptIPs: string[];
}

/**
 * 零信任决策结果
 */
interface ZeroTrustDecision {
  action: 'allow' | 'challenge' | 'deny' | 'step_up';
  reason: string;
  riskScore: number;
  deviceTrustScore: number;
  requiredActions: string[];
  sessionRestrictions: string[];
  monitoringLevel: 'normal' | 'enhanced' | 'strict';
}

/**
 * 默认零信任配置
 */
const defaultZeroTrustConfig: ZeroTrustConfig = {
  enabled: true,
  strictMode: false,
  riskThresholds: {
    allow: 30,
    challenge: 60,
    deny: 80
  },
  deviceTrustThresholds: {
    trusted: 70,
    suspicious: 40,
    untrusted: 20
  },
  sessionLimits: {
    maxConcurrent: 3,
    maxDuration: 8 * 60 * 60, // 8小时
    idleTimeout: 30 * 60      // 30分钟
  },
  exemptPaths: [
    '/health',
    '/api/health',
    '/api/v1/auth/login',
    '/api/v1/auth/register',
    '/api/v1/auth/forgot-password'
  ],
  exemptIPs: []
};

/**
 * 零信任认证中间件
 */
export function zeroTrustAuth(config: Partial<ZeroTrustConfig> = {}) {
  const finalConfig = { ...defaultZeroTrustConfig, ...config };

  return async (req: Request, res: Response, next: NextFunction) => {
    // 检查是否启用零信任
    if (!finalConfig.enabled) {
      return next();
    }

    // 检查豁免路径
    if (finalConfig.exemptPaths.some(path => req.path.startsWith(path))) {
      return next();
    }

    // 检查豁免IP
    const clientIP = req.ip || 'unknown';
    if (finalConfig.exemptIPs.includes(clientIP)) {
      return next();
    }

    try {
      const startTime = Date.now();

      // 执行零信任评估
      const decision = await performZeroTrustAssessment(req, finalConfig);

      // 记录决策
      await logZeroTrustDecision(req, decision);

      // 根据决策执行相应操作
      const result = await executeZeroTrustDecision(req, res, decision, finalConfig);

      if (result.shouldContinue) {
        // 设置会话限制
        await applySessionRestrictions(req, decision);

        // 设置监控级别
        req.monitoringLevel = decision.monitoringLevel;

        // 记录成功的零信任检查
        metricsCollector.incrementCounter('zero_trust_checks_total', {
          action: decision.action,
          risk_level: getRiskLevelFromScore(decision.riskScore),
          device_trust: getDeviceTrustLevel(decision.deviceTrustScore)
        });

        const duration = Date.now() - startTime;
        metricsCollector.recordHistogram('zero_trust_check_duration', duration);

        next();
      }
      // 如果 shouldContinue 为 false，响应已经发送，不需要调用 next()

    } catch (error) {
      logger.error('零信任检查失败', {
        error: error instanceof Error ? error.message : String(error),
        path: req.path,
        ip: clientIP,
        userId: req.user?.id
      });

      // 在严格模式下，错误时拒绝访问
      if (finalConfig.strictMode) {
        return res.status(500).json({
          success: false,
          error: 'ZERO_TRUST_CHECK_FAILED',
          message: '零信任检查失败，访问被拒绝'
        });
      }

      // 非严格模式下，记录错误但允许继续
      next();
    }
  };
}

/**
 * 设备信任验证中间件
 */
export function deviceTrustVerification(minTrustScore: number = 50) {
  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      // 生成或识别设备指纹
      const device = await deviceFingerprintService.generateFingerprint(req, {
        // 从请求头获取客户端指纹数据
        canvasFingerprint: req.get('X-Canvas-Fingerprint'),
        webglFingerprint: req.get('X-WebGL-Fingerprint'),
        audioFingerprint: req.get('X-Audio-Fingerprint'),
        screenResolution: req.get('X-Screen-Resolution'),
        timezone: req.get('X-Timezone')
      });

      // 评估设备信任度
      const trustAssessment = await deviceFingerprintService.assessDeviceTrust(device, req.user?.id);

      // 检查信任度是否满足要求
      if (trustAssessment.trustScore < minTrustScore) {
        await securityAuditService.logAuditEvent({
          eventType: AuditEventType.PERMISSION_DENIED,
          severity: AuditSeverity.MEDIUM,
          userId: req.user?.id,
          sessionId: req.sessionID,
          ipAddress: req.ip || 'unknown',
          userAgent: req.get('User-Agent'),
          resource: req.path,
          action: 'device_trust_check',
          details: {
            deviceId: device.id,
            trustScore: trustAssessment.trustScore,
            minRequired: minTrustScore,
            requiresVerification: trustAssessment.requiresVerification
          },
          success: false,
          errorMessage: '设备信任度不足'
        });

        return res.status(403).json({
          success: false,
          error: 'DEVICE_TRUST_INSUFFICIENT',
          message: '设备信任度不足，需要验证',
          data: {
            deviceId: device.id,
            trustScore: trustAssessment.trustScore,
            requiredScore: minTrustScore,
            verificationRequired: trustAssessment.requiresVerification,
            recommendations: trustAssessment.recommendations
          }
        });
      }

      // 将设备信息添加到请求对象
      req.device = device;
      req.deviceTrust = trustAssessment;

      next();

    } catch (error) {
      logger.error('设备信任验证失败', {
        error: error instanceof Error ? error.message : String(error),
        userId: req.user?.id,
        path: req.path
      });

      // 默认拒绝访问
      res.status(500).json({
        success: false,
        error: 'DEVICE_VERIFICATION_FAILED',
        message: '设备验证失败'
      });
    }
  };
}

/**
 * 连续认证中间件
 */
export function continuousAuthentication(recheckInterval: number = 300) { // 5分钟
  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      const userId = req.user?.id;
      if (!userId) {
        return next(); // 未认证用户跳过
      }

      const sessionId = req.sessionID;
      const lastCheckKey = `continuous_auth:${sessionId}`;
      
      // 检查上次验证时间
      const lastCheck = await cacheService.get(lastCheckKey);
      const now = Date.now();
      
      if (lastCheck && (now - parseInt(lastCheck)) < recheckInterval * 1000) {
        return next(); // 还在验证有效期内
      }

      // 执行连续认证检查
      const authContext = {
        userId,
        sessionId,
        ipAddress: req.ip || 'unknown',
        userAgent: req.get('User-Agent') || 'unknown',
        deviceFingerprint: req.device?.fingerprint,
        requestedResource: req.path,
        requestedAction: req.method
      };

      const authDecision = await adaptiveAuthService.makeAuthDecision(req, authContext);

      // 根据决策结果处理
      if (authDecision.decision === 'deny') {
        await securityAuditService.logAuditEvent({
          eventType: AuditEventType.PERMISSION_DENIED,
          severity: AuditSeverity.HIGH,
          userId,
          sessionId,
          ipAddress: req.ip || 'unknown',
          userAgent: req.get('User-Agent'),
          resource: req.path,
          action: 'continuous_auth_check',
          details: {
            reason: authDecision.reason,
            riskScore: authDecision.riskScore,
            requirementLevel: authDecision.requirementLevel
          },
          success: false,
          errorMessage: '连续认证检查失败'
        });

        return res.status(403).json({
          success: false,
          error: 'CONTINUOUS_AUTH_FAILED',
          message: '连续认证检查失败，需要重新认证',
          data: {
            reason: authDecision.reason,
            riskScore: authDecision.riskScore,
            requiredActions: authDecision.requiredActions
          }
        });
      }

      if (authDecision.decision === 'challenge') {
        return res.status(401).json({
          success: false,
          error: 'STEP_UP_AUTH_REQUIRED',
          message: '需要额外认证',
          data: {
            requirementLevel: authDecision.requirementLevel,
            requiredMethods: authDecision.requiredMethods,
            optionalMethods: authDecision.optionalMethods,
            reason: authDecision.reason
          }
        });
      }

      // 更新验证时间
      await cacheService.set(lastCheckKey, now.toString(), recheckInterval);

      next();

    } catch (error) {
      logger.error('连续认证检查失败', {
        error: error instanceof Error ? error.message : String(error),
        userId: req.user?.id,
        path: req.path
      });

      // 默认允许继续，但记录错误
      next();
    }
  };
}

/**
 * 会话监控中间件
 */
export function sessionMonitoring() {
  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      const userId = req.user?.id;
      const sessionId = req.sessionID;

      if (!userId || !sessionId) {
        return next();
      }

      // 检查会话状态
      const sessionKey = `session:${sessionId}`;
      const sessionData = await cacheService.get(sessionKey);

      if (sessionData) {
        const session = JSON.parse(sessionData);
        
        // 更新会话活动时间
        session.lastActivity = new Date().toISOString();
        session.requestCount = (session.requestCount || 0) + 1;
        session.lastPath = req.path;
        session.lastIP = req.ip;

        await cacheService.set(sessionKey, JSON.stringify(session), 24 * 60 * 60); // 24小时
      } else {
        // 创建新的会话记录
        const newSession = {
          userId,
          sessionId,
          createdAt: new Date().toISOString(),
          lastActivity: new Date().toISOString(),
          requestCount: 1,
          lastPath: req.path,
          lastIP: req.ip,
          userAgent: req.get('User-Agent')
        };

        await cacheService.set(sessionKey, JSON.stringify(newSession), 24 * 60 * 60);
      }

      // 检查并发会话限制
      const userSessionsKey = `user_sessions:${userId}`;
      const userSessions = await cacheService.get(userSessionsKey);
      
      if (userSessions) {
        const sessions = JSON.parse(userSessions);
        if (sessions.length > defaultZeroTrustConfig.sessionLimits.maxConcurrent) {
          logger.warn('用户并发会话超限', {
            userId,
            sessionCount: sessions.length,
            limit: defaultZeroTrustConfig.sessionLimits.maxConcurrent
          });

          // 可以选择终止最旧的会话或拒绝当前请求
        }
      }

      next();

    } catch (error) {
      logger.error('会话监控失败', {
        error: error instanceof Error ? error.message : String(error),
        userId: req.user?.id
      });
      next();
    }
  };
}

/**
 * 执行零信任评估
 */
async function performZeroTrustAssessment(req: Request, config: ZeroTrustConfig): Promise<ZeroTrustDecision> {
  // 执行风险评估
  const riskAssessment = await riskAssessmentService.assessRisk(req, req.user?.id);

  // 获取设备信任评估
  let deviceTrustScore = 50; // 默认中等信任
  try {
    const device = await deviceFingerprintService.identifyDevice(req);
    if (device) {
      const trustAssessment = await deviceFingerprintService.assessDeviceTrust(device, req.user?.id);
      deviceTrustScore = trustAssessment.trustScore;
    }
  } catch (error) {
    logger.warn('设备信任评估失败', { error: error.message });
  }

  // 生成零信任决策
  return generateZeroTrustDecision(riskAssessment.overallRiskScore, deviceTrustScore, config);
}

/**
 * 生成零信任决策
 */
function generateZeroTrustDecision(
  riskScore: number,
  deviceTrustScore: number,
  config: ZeroTrustConfig
): ZeroTrustDecision {
  let action: 'allow' | 'challenge' | 'deny' | 'step_up' = 'allow';
  let reason = '正常访问';
  const requiredActions: string[] = [];
  const sessionRestrictions: string[] = [];
  let monitoringLevel: 'normal' | 'enhanced' | 'strict' = 'normal';

  // 基于风险评分决策
  if (riskScore >= config.riskThresholds.deny) {
    action = 'deny';
    reason = '风险评分过高';
    requiredActions.push('BLOCK_ACCESS', 'SECURITY_REVIEW');
  } else if (riskScore >= config.riskThresholds.challenge) {
    action = 'challenge';
    reason = '风险评分较高，需要额外验证';
    requiredActions.push('STEP_UP_AUTH', 'ENHANCED_MONITORING');
    monitoringLevel = 'enhanced';
  } else if (riskScore >= config.riskThresholds.allow) {
    action = 'step_up';
    reason = '风险评分中等，建议增强认证';
    requiredActions.push('PERIODIC_REAUTH');
    monitoringLevel = 'enhanced';
  }

  // 基于设备信任度调整决策
  if (deviceTrustScore < config.deviceTrustThresholds.untrusted) {
    if (action === 'allow') action = 'challenge';
    reason += ', 设备信任度过低';
    requiredActions.push('DEVICE_VERIFICATION');
    sessionRestrictions.push('LIMITED_PERMISSIONS');
  } else if (deviceTrustScore < config.deviceTrustThresholds.suspicious) {
    if (action === 'allow') action = 'step_up';
    reason += ', 设备信任度较低';
    requiredActions.push('DEVICE_MONITORING');
    sessionRestrictions.push('ENHANCED_LOGGING');
  }

  // 严格模式下的额外限制
  if (config.strictMode) {
    if (action === 'allow' && (riskScore > 20 || deviceTrustScore < 60)) {
      action = 'step_up';
      reason += ', 严格模式要求';
      monitoringLevel = 'strict';
    }
  }

  return {
    action,
    reason,
    riskScore,
    deviceTrustScore,
    requiredActions,
    sessionRestrictions,
    monitoringLevel
  };
}

/**
 * 执行零信任决策
 */
async function executeZeroTrustDecision(
  req: Request,
  res: Response,
  decision: ZeroTrustDecision,
  config: ZeroTrustConfig
): Promise<{ shouldContinue: boolean }> {
  switch (decision.action) {
    case 'deny':
      res.status(403).json({
        success: false,
        error: 'ACCESS_DENIED',
        message: '访问被拒绝',
        data: {
          reason: decision.reason,
          riskScore: decision.riskScore,
          deviceTrustScore: decision.deviceTrustScore,
          requiredActions: decision.requiredActions
        }
      });
      return { shouldContinue: false };

    case 'challenge':
      res.status(401).json({
        success: false,
        error: 'ADDITIONAL_AUTH_REQUIRED',
        message: '需要额外认证',
        data: {
          reason: decision.reason,
          riskScore: decision.riskScore,
          deviceTrustScore: decision.deviceTrustScore,
          requiredActions: decision.requiredActions
        }
      });
      return { shouldContinue: false };

    case 'step_up':
      // 设置响应头提示需要增强认证
      res.setHeader('X-Auth-Challenge', 'step-up-required');
      res.setHeader('X-Risk-Score', decision.riskScore.toString());
      return { shouldContinue: true };

    case 'allow':
    default:
      return { shouldContinue: true };
  }
}

/**
 * 应用会话限制
 */
async function applySessionRestrictions(req: Request, decision: ZeroTrustDecision): Promise<void> {
  if (decision.sessionRestrictions.length === 0) return;

  const sessionId = req.sessionID;
  const restrictionsKey = `session_restrictions:${sessionId}`;
  
  await cacheService.set(
    restrictionsKey,
    JSON.stringify(decision.sessionRestrictions),
    3600 // 1小时
  );
}

/**
 * 记录零信任决策
 */
async function logZeroTrustDecision(req: Request, decision: ZeroTrustDecision): Promise<void> {
  await securityAuditService.logAuditEvent({
    eventType: AuditEventType.PERMISSION_GRANTED,
    severity: decision.action === 'deny' ? AuditSeverity.HIGH : AuditSeverity.LOW,
    userId: req.user?.id,
    sessionId: req.sessionID,
    ipAddress: req.ip || 'unknown',
    userAgent: req.get('User-Agent'),
    resource: req.path,
    action: 'zero_trust_check',
    details: {
      decision: decision.action,
      reason: decision.reason,
      riskScore: decision.riskScore,
      deviceTrustScore: decision.deviceTrustScore,
      requiredActions: decision.requiredActions,
      sessionRestrictions: decision.sessionRestrictions,
      monitoringLevel: decision.monitoringLevel
    },
    success: decision.action !== 'deny'
  });
}

/**
 * 辅助函数
 */
function getRiskLevelFromScore(score: number): string {
  if (score >= 80) return 'critical';
  if (score >= 60) return 'high';
  if (score >= 40) return 'medium';
  if (score >= 20) return 'low';
  return 'very_low';
}

function getDeviceTrustLevel(score: number): string {
  if (score >= 70) return 'trusted';
  if (score >= 40) return 'suspicious';
  return 'untrusted';
}

// 扩展Express Request接口
declare global {
  namespace Express {
    interface Request {
      device?: any;
      deviceTrust?: any;
      monitoringLevel?: 'normal' | 'enhanced' | 'strict';
    }
  }
}
