/**
 * CDN中间件
 * 处理静态资源的CDN重写和缓存控制
 */

import { Request, Response, NextFunction } from 'express';
import { cdnService } from '@/services/cdn.service';
import { logger } from '@/config/logger';
import path from 'path';

/**
 * CDN资源重写中间件
 * 将静态资源URL重写为CDN URL
 */
export function cdnRewrite() {
  return (req: Request, res: Response, next: NextFunction): void => {
    // 只处理HTML响应
    if (!req.accepts('html')) {
      return next();
    }

    // 保存原始的send和json方法
    const originalSend = res.send;
    const originalJson = res.json;

    // 重写send方法
    res.send = function(body: any) {
      if (typeof body === 'string' && body.includes('<html')) {
        body = rewriteAssetUrls(body, req);
      }
      return originalSend.call(this, body);
    };

    // 重写json方法（对于某些API返回HTML的情况）
    res.json = function(obj: any) {
      if (typeof obj === 'object' && obj.html && typeof obj.html === 'string') {
        obj.html = rewriteAssetUrls(obj.html, req);
      }
      return originalJson.call(this, obj);
    };

    next();
  };
}

/**
 * 重写HTML中的资源URL
 */
function rewriteAssetUrls(html: string, req: Request): string {
  if (!cdnService.shouldUseCDN('')) {
    return html;
  }

  try {
    // 重写CSS链接
    html = html.replace(
      /<link[^>]+href=["']([^"']+\.css[^"']*)["'][^>]*>/gi,
      (match, url) => {
        if (shouldRewriteUrl(url)) {
          const cdnUrl = cdnService.getAssetUrl(url);
          return match.replace(url, cdnUrl);
        }
        return match;
      }
    );

    // 重写JavaScript脚本
    html = html.replace(
      /<script[^>]+src=["']([^"']+\.js[^"']*)["'][^>]*>/gi,
      (match, url) => {
        if (shouldRewriteUrl(url)) {
          const cdnUrl = cdnService.getAssetUrl(url);
          return match.replace(url, cdnUrl);
        }
        return match;
      }
    );

    // 重写图片资源
    html = html.replace(
      /<img[^>]+src=["']([^"']+\.(png|jpg|jpeg|gif|svg|webp)[^"']*)["'][^>]*>/gi,
      (match, url) => {
        if (shouldRewriteUrl(url)) {
          const cdnUrl = cdnService.getAssetUrl(url);
          return match.replace(url, cdnUrl);
        }
        return match;
      }
    );

    // 重写字体资源
    html = html.replace(
      /url\(["']?([^"'()]+\.(woff2?|ttf|eot)[^"'()]*)["']?\)/gi,
      (match, url) => {
        if (shouldRewriteUrl(url)) {
          const cdnUrl = cdnService.getAssetUrl(url);
          return match.replace(url, cdnUrl);
        }
        return match;
      }
    );

    // 重写favicon
    html = html.replace(
      /<link[^>]+rel=["'](?:icon|shortcut icon)["'][^>]+href=["']([^"']+)["'][^>]*>/gi,
      (match, url) => {
        if (shouldRewriteUrl(url)) {
          const cdnUrl = cdnService.getAssetUrl(url);
          return match.replace(url, cdnUrl);
        }
        return match;
      }
    );

    return html;
  } catch (error) {
    logger.error('重写资源URL失败', {
      error: error instanceof Error ? error.message : String(error)
    });
    return html;
  }
}

/**
 * 判断URL是否应该被重写
 */
function shouldRewriteUrl(url: string): boolean {
  // 跳过外部URL
  if (url.startsWith('http://') || url.startsWith('https://')) {
    return false;
  }

  // 跳过数据URL
  if (url.startsWith('data:')) {
    return false;
  }

  // 跳过blob URL
  if (url.startsWith('blob:')) {
    return false;
  }

  // 检查是否是支持的静态资源
  return cdnService.shouldUseCDN(url);
}

/**
 * 静态资源缓存控制中间件
 */
export function staticCacheControl() {
  return (req: Request, res: Response, next: NextFunction): void => {
    const url = req.originalUrl || req.url;
    
    // 检查是否是静态资源
    if (cdnService.shouldUseCDN(url)) {
      const cacheControl = cdnService.getCacheControl(url);
      
      // 设置缓存控制头
      res.setHeader('Cache-Control', cacheControl);
      
      // 设置ETag
      const etag = generateETag(url);
      if (etag) {
        res.setHeader('ETag', etag);
      }
      
      // 检查If-None-Match头
      const ifNoneMatch = req.headers['if-none-match'];
      if (ifNoneMatch && etag && ifNoneMatch === etag) {
        return res.status(304).end();
      }
      
      // 设置Vary头
      res.setHeader('Vary', 'Accept-Encoding');
      
      logger.debug('设置静态资源缓存控制', {
        url,
        cacheControl,
        etag
      });
    }
    
    next();
  };
}

/**
 * 生成ETag
 */
function generateETag(url: string): string | null {
  try {
    const version = cdnService.generateAssetVersion(url);
    return `"${version}"`;
  } catch (error) {
    return null;
  }
}

/**
 * CDN预加载中间件
 * 在HTML中添加资源预加载提示
 */
export function cdnPreload(assets: string[] = []) {
  return (req: Request, res: Response, next: NextFunction): void => {
    // 只处理HTML响应
    if (!req.accepts('html')) {
      return next();
    }

    const originalSend = res.send;

    res.send = function(body: any) {
      if (typeof body === 'string' && body.includes('<head>')) {
        body = addPreloadHints(body, assets);
      }
      return originalSend.call(this, body);
    };

    next();
  };
}

/**
 * 添加预加载提示
 */
function addPreloadHints(html: string, assets: string[]): string {
  if (!cdnService.shouldUseCDN('') || assets.length === 0) {
    return html;
  }

  try {
    const preloadLinks = assets
      .filter(asset => cdnService.shouldUseCDN(asset))
      .map(asset => {
        const cdnUrl = cdnService.getAssetUrl(asset);
        const ext = path.extname(asset).toLowerCase();
        
        let as = 'fetch';
        if (['.css'].includes(ext)) {
          as = 'style';
        } else if (['.js', '.mjs'].includes(ext)) {
          as = 'script';
        } else if (['.woff', '.woff2', '.ttf', '.eot'].includes(ext)) {
          as = 'font';
        } else if (['.png', '.jpg', '.jpeg', '.gif', '.svg', '.webp'].includes(ext)) {
          as = 'image';
        }
        
        const crossorigin = as === 'font' ? ' crossorigin' : '';
        return `<link rel="preload" href="${cdnUrl}" as="${as}"${crossorigin}>`;
      })
      .join('\n    ');

    if (preloadLinks) {
      html = html.replace(
        '</head>',
        `    ${preloadLinks}\n  </head>`
      );
    }

    return html;
  } catch (error) {
    logger.error('添加预加载提示失败', {
      error: error instanceof Error ? error.message : String(error)
    });
    return html;
  }
}

/**
 * CDN健康检查中间件
 */
export function cdnHealthCheck() {
  return async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const isHealthy = await cdnService.healthCheck();
      
      if (!isHealthy) {
        logger.warn('CDN健康检查失败，可能影响静态资源加载');
        
        // 可以选择降级到本地资源服务
        res.locals.cdnFallback = true;
      }
      
      next();
    } catch (error) {
      logger.error('CDN健康检查中间件错误', {
        error: error instanceof Error ? error.message : String(error)
      });
      next();
    }
  };
}

/**
 * 资源版本化中间件
 * 为静态资源添加版本号
 */
export function assetVersioning() {
  return (req: Request, res: Response, next: NextFunction): void => {
    const url = req.originalUrl || req.url;
    
    // 检查是否是静态资源且需要版本化
    if (cdnService.shouldUseCDN(url)) {
      const version = cdnService.generateAssetVersion(url);
      
      // 添加版本信息到响应头
      res.setHeader('X-Asset-Version', version);
      
      // 设置长期缓存
      const cacheControl = cdnService.getCacheControl(url);
      res.setHeader('Cache-Control', cacheControl);
      
      logger.debug('资源版本化处理', {
        url,
        version,
        cacheControl
      });
    }
    
    next();
  };
}

/**
 * CDN统计中间件
 * 收集CDN使用统计信息
 */
export function cdnStats() {
  const stats = {
    requests: 0,
    cdnHits: 0,
    localHits: 0,
    errors: 0
  };

  return (req: Request, res: Response, next: NextFunction): void => {
    const url = req.originalUrl || req.url;
    
    if (cdnService.shouldUseCDN(url)) {
      stats.requests++;
      
      // 检查是否使用了CDN
      const referer = req.headers.referer;
      if (referer && referer.includes(process.env.CDN_BASE_URL || '')) {
        stats.cdnHits++;
      } else {
        stats.localHits++;
      }
      
      // 监听响应错误
      res.on('error', () => {
        stats.errors++;
      });
      
      // 添加统计信息到响应头（开发环境）
      if (process.env.NODE_ENV === 'development') {
        res.setHeader('X-CDN-Stats', JSON.stringify(stats));
      }
    }
    
    next();
  };
}

/**
 * 获取CDN统计信息
 */
export function getCDNStats(): any {
  return cdnService.getStatistics();
}
