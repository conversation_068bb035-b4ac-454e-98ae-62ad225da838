/**
 * 缓存中间件
 * 提供HTTP响应缓存和请求缓存功能
 */

import { Request, Response, NextFunction } from 'express';
import { cacheManager } from '@/services/cache-manager.service';
import { logger } from '@/config/logger';
import crypto from 'crypto';

/**
 * 缓存选项接口
 */
export interface CacheOptions {
  ttl?: number;                        // 缓存时间（秒）
  keyGenerator?: (req: Request) => string; // 自定义键生成器
  condition?: (req: Request, res: Response) => boolean; // 缓存条件
  skipMethods?: string[];              // 跳过的HTTP方法
  skipStatusCodes?: number[];          // 跳过的状态码
  varyHeaders?: string[];              // Vary头部
  namespace?: string;                  // 缓存命名空间
  compress?: boolean;                  // 是否压缩
  tags?: string[];                     // 缓存标签
}

/**
 * 默认缓存选项
 */
const defaultCacheOptions: CacheOptions = {
  ttl: 300, // 5分钟
  skipMethods: ['POST', 'PUT', 'DELETE', 'PATCH'],
  skipStatusCodes: [400, 401, 403, 404, 500, 502, 503, 504],
  namespace: 'http',
  compress: false,
  tags: []
};

/**
 * 生成缓存键
 */
function generateCacheKey(req: Request, options: CacheOptions): string {
  if (options.keyGenerator) {
    return options.keyGenerator(req);
  }

  // 默认键生成策略
  const method = req.method;
  const url = req.originalUrl || req.url;
  const query = JSON.stringify(req.query);
  const headers = options.varyHeaders 
    ? JSON.stringify(options.varyHeaders.reduce((acc, header) => {
        acc[header] = req.headers[header];
        return acc;
      }, {} as Record<string, any>))
    : '';

  const keyData = `${method}:${url}:${query}:${headers}`;
  const hash = crypto.createHash('md5').update(keyData).digest('hex');
  
  return `${options.namespace}:${hash}`;
}

/**
 * 检查是否应该缓存
 */
function shouldCache(req: Request, res: Response, options: CacheOptions): boolean {
  // 检查HTTP方法
  if (options.skipMethods?.includes(req.method)) {
    return false;
  }

  // 检查状态码
  if (options.skipStatusCodes?.includes(res.statusCode)) {
    return false;
  }

  // 检查自定义条件
  if (options.condition && !options.condition(req, res)) {
    return false;
  }

  // 检查Cache-Control头部
  const cacheControl = res.get('Cache-Control');
  if (cacheControl && (cacheControl.includes('no-cache') || cacheControl.includes('no-store'))) {
    return false;
  }

  return true;
}

/**
 * HTTP响应缓存中间件
 */
export function httpCache(options: Partial<CacheOptions> = {}) {
  const cacheOptions = { ...defaultCacheOptions, ...options };

  return async (req: Request, res: Response, next: NextFunction) => {
    // 跳过不需要缓存的方法
    if (cacheOptions.skipMethods?.includes(req.method)) {
      return next();
    }

    const cacheKey = generateCacheKey(req, cacheOptions);

    try {
      // 尝试从缓存获取响应
      const cachedResponse = await cacheManager.get(cacheKey, {
        namespace: cacheOptions.namespace
      });

      if (cachedResponse) {
        logger.debug('缓存命中', { 
          method: req.method, 
          url: req.originalUrl,
          cacheKey 
        });

        // 设置缓存头部
        res.set('X-Cache', 'HIT');
        res.set('X-Cache-Key', cacheKey);

        // 设置Vary头部
        if (cacheOptions.varyHeaders) {
          res.vary(cacheOptions.varyHeaders);
        }

        // 返回缓存的响应
        return res.status(cachedResponse.statusCode)
          .set(cachedResponse.headers)
          .send(cachedResponse.body);
      }

      // 缓存未命中，继续处理请求
      logger.debug('缓存未命中', { 
        method: req.method, 
        url: req.originalUrl,
        cacheKey 
      });

      res.set('X-Cache', 'MISS');
      res.set('X-Cache-Key', cacheKey);

      // 拦截响应
      const originalSend = res.send;
      const originalJson = res.json;
      const originalEnd = res.end;

      let responseBody: any;
      let responseSent = false;

      // 重写send方法
      res.send = function(body: any) {
        if (!responseSent) {
          responseBody = body;
          responseSent = true;
          
          // 异步缓存响应
          setImmediate(async () => {
            if (shouldCache(req, res, cacheOptions)) {
              const responseData = {
                statusCode: res.statusCode,
                headers: res.getHeaders(),
                body: responseBody
              };

              await cacheManager.set(cacheKey, responseData, {
                ttl: cacheOptions.ttl,
                namespace: cacheOptions.namespace,
                tags: cacheOptions.tags
              });

              logger.debug('响应已缓存', { 
                method: req.method, 
                url: req.originalUrl,
                cacheKey,
                statusCode: res.statusCode
              });
            }
          });
        }

        return originalSend.call(this, body);
      };

      // 重写json方法
      res.json = function(obj: any) {
        if (!responseSent) {
          responseBody = obj;
          responseSent = true;
          
          // 异步缓存响应
          setImmediate(async () => {
            if (shouldCache(req, res, cacheOptions)) {
              const responseData = {
                statusCode: res.statusCode,
                headers: res.getHeaders(),
                body: responseBody
              };

              await cacheManager.set(cacheKey, responseData, {
                ttl: cacheOptions.ttl,
                namespace: cacheOptions.namespace,
                tags: cacheOptions.tags
              });

              logger.debug('JSON响应已缓存', { 
                method: req.method, 
                url: req.originalUrl,
                cacheKey,
                statusCode: res.statusCode
              });
            }
          });
        }

        return originalJson.call(this, obj);
      };

      // 重写end方法
      res.end = function(chunk?: any, encoding?: any) {
        if (!responseSent && chunk) {
          responseBody = chunk;
          responseSent = true;
          
          // 异步缓存响应
          setImmediate(async () => {
            if (shouldCache(req, res, cacheOptions)) {
              const responseData = {
                statusCode: res.statusCode,
                headers: res.getHeaders(),
                body: responseBody
              };

              await cacheManager.set(cacheKey, responseData, {
                ttl: cacheOptions.ttl,
                namespace: cacheOptions.namespace,
                tags: cacheOptions.tags
              });

              logger.debug('响应已缓存', { 
                method: req.method, 
                url: req.originalUrl,
                cacheKey,
                statusCode: res.statusCode
              });
            }
          });
        }

        return originalEnd.call(this, chunk, encoding);
      };

      // 设置Vary头部
      if (cacheOptions.varyHeaders) {
        res.vary(cacheOptions.varyHeaders);
      }

      next();

    } catch (error) {
      logger.error('缓存中间件错误', {
        method: req.method,
        url: req.originalUrl,
        error: error.message
      });
      
      // 出错时继续处理请求
      next();
    }
  };
}

/**
 * 缓存失效中间件
 */
export function cacheInvalidation(options: { 
  patterns?: string[];
  tags?: string[];
  namespace?: string;
} = {}) {
  return async (req: Request, res: Response, next: NextFunction) => {
    // 只在修改操作后失效缓存
    if (!['POST', 'PUT', 'DELETE', 'PATCH'].includes(req.method)) {
      return next();
    }

    // 保存原始的响应方法
    const originalSend = res.send;
    const originalJson = res.json;

    // 重写响应方法以在成功响应后失效缓存
    const invalidateCache = async () => {
      if (res.statusCode >= 200 && res.statusCode < 300) {
        try {
          // 按标签失效
          if (options.tags) {
            for (const tag of options.tags) {
              await cacheManager.deleteByTag(tag);
              logger.debug('按标签失效缓存', { tag });
            }
          }

          // 按模式失效（这里需要实现模式匹配删除）
          if (options.patterns) {
            // 简化实现，实际应该支持通配符模式
            for (const pattern of options.patterns) {
              // 这里可以实现更复杂的模式匹配逻辑
              logger.debug('按模式失效缓存', { pattern });
            }
          }

        } catch (error) {
          logger.error('缓存失效失败', { error: error.message });
        }
      }
    };

    res.send = function(body: any) {
      setImmediate(invalidateCache);
      return originalSend.call(this, body);
    };

    res.json = function(obj: any) {
      setImmediate(invalidateCache);
      return originalJson.call(this, obj);
    };

    next();
  };
}

/**
 * 条件缓存中间件
 */
export function conditionalCache(condition: (req: Request) => boolean, options: Partial<CacheOptions> = {}) {
  const cacheMiddleware = httpCache(options);

  return (req: Request, res: Response, next: NextFunction) => {
    if (condition(req)) {
      return cacheMiddleware(req, res, next);
    }
    next();
  };
}

/**
 * 用户特定缓存中间件
 */
export function userCache(options: Partial<CacheOptions> = {}) {
  const userCacheOptions = {
    ...options,
    keyGenerator: (req: Request) => {
      const userId = (req as any).user?.id || 'anonymous';
      const method = req.method;
      const url = req.originalUrl || req.url;
      const query = JSON.stringify(req.query);
      
      const keyData = `user:${userId}:${method}:${url}:${query}`;
      const hash = crypto.createHash('md5').update(keyData).digest('hex');
      
      return `user:${hash}`;
    },
    namespace: 'user'
  };

  return httpCache(userCacheOptions);
}

/**
 * API响应缓存中间件
 */
export function apiCache(ttl: number = 300) {
  return httpCache({
    ttl,
    namespace: 'api',
    skipMethods: ['POST', 'PUT', 'DELETE', 'PATCH'],
    varyHeaders: ['Authorization', 'Accept', 'Accept-Language'],
    condition: (req: Request, res: Response) => {
      // 只缓存成功的GET请求
      return req.method === 'GET' && res.statusCode === 200;
    }
  });
}

/**
 * 静态资源缓存中间件
 */
export function staticCache(ttl: number = 86400) { // 24小时
  return httpCache({
    ttl,
    namespace: 'static',
    skipMethods: ['POST', 'PUT', 'DELETE', 'PATCH'],
    varyHeaders: ['Accept-Encoding'],
    condition: (req: Request, res: Response) => {
      // 缓存静态资源
      const staticExtensions = ['.js', '.css', '.png', '.jpg', '.jpeg', '.gif', '.svg', '.ico', '.woff', '.woff2'];
      const url = req.originalUrl || req.url;
      return staticExtensions.some(ext => url.endsWith(ext)) && res.statusCode === 200;
    }
  });
}
