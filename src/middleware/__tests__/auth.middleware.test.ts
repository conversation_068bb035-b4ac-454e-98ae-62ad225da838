/**
 * 认证中间件测试
 */

import { Request, Response, NextFunction } from 'express';
import { authenticateToken, optionalAuth } from '../auth.middleware';
import { verifyAccessToken } from '@/utils/jwt';
import { cacheService } from '@/services/cache.service';
import { logger } from '@/config/logger';

// Mock依赖
jest.mock('@/utils/jwt');
jest.mock('@/services/cache.service');
jest.mock('@/config/logger');

const mockVerifyAccessToken = verifyAccessToken as jest.MockedFunction<typeof verifyAccessToken>;
const mockCacheService = cacheService as jest.Mocked<typeof cacheService>;

describe('认证中间件', () => {
  let mockRequest: Partial<Request>;
  let mockResponse: Partial<Response>;
  let mockNext: NextFunction;

  beforeEach(() => {
    jest.clearAllMocks();

    mockRequest = {
      headers: {},
      user: undefined
    };

    mockResponse = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn().mockReturnThis()
    };

    mockNext = jest.fn();
  });

  describe('authenticateToken', () => {
    test('应该成功验证有效的Bearer令牌', async () => {
      const mockUser = {
        sub: 'user-123',
        email: '<EMAIL>',
        roles: ['user']
      };

      mockRequest.headers = {
        authorization: 'Bearer valid-token'
      };

      mockVerifyAccessToken.mockReturnValue(mockUser);
      mockCacheService.isJWTBlacklisted.mockResolvedValue(false);

      await authenticateToken(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockVerifyAccessToken).toHaveBeenCalledWith('valid-token');
      expect(mockCacheService.isJWTBlacklisted).toHaveBeenCalledWith('valid-token');
      expect(mockRequest.user).toEqual({
        id: mockUser.sub,
        email: mockUser.email,
        roles: mockUser.roles
      });
      expect(mockNext).toHaveBeenCalled();
    });

    test('应该拒绝缺失的Authorization头', async () => {
      await authenticateToken(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(401);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: 'UNAUTHORIZED',
        message: '缺少认证令牌'
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    test('应该拒绝无效的Authorization头格式', async () => {
      mockRequest.headers = {
        authorization: 'Basic invalid-format'
      };

      await authenticateToken(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(401);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: 'UNAUTHORIZED',
        message: '无效的认证令牌格式'
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    test('应该拒绝无效的JWT令牌', async () => {
      mockRequest.headers = {
        authorization: 'Bearer invalid-token'
      };

      mockVerifyAccessToken.mockImplementation(() => {
        throw new Error('Invalid token');
      });

      await authenticateToken(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(401);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: 'UNAUTHORIZED',
        message: '无效的认证令牌'
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    test('应该拒绝黑名单中的令牌', async () => {
      const mockUser = {
        sub: 'user-123',
        email: '<EMAIL>'
      };

      mockRequest.headers = {
        authorization: 'Bearer blacklisted-token'
      };

      mockVerifyAccessToken.mockReturnValue(mockUser);
      mockCacheService.isJWTBlacklisted.mockResolvedValue(true);

      await authenticateToken(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(401);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: 'UNAUTHORIZED',
        message: '令牌已失效'
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    test('应该处理缓存服务错误', async () => {
      const mockUser = {
        sub: 'user-123',
        email: '<EMAIL>'
      };

      mockRequest.headers = {
        authorization: 'Bearer valid-token'
      };

      mockVerifyAccessToken.mockReturnValue(mockUser);
      mockCacheService.isJWTBlacklisted.mockRejectedValue(new Error('Cache error'));

      await authenticateToken(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(500);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: 'INTERNAL_ERROR',
        message: '认证服务暂时不可用'
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    test('应该记录认证失败的日志', async () => {
      mockRequest.headers = {
        authorization: 'Bearer invalid-token'
      };

      mockVerifyAccessToken.mockImplementation(() => {
        throw new Error('Invalid token');
      });

      await authenticateToken(mockRequest as Request, mockResponse as Response, mockNext);

      expect(logger.warn).toHaveBeenCalledWith('JWT令牌验证失败', {
        error: 'Invalid token',
        token: 'invalid-token'
      });
    });

    test('应该处理不同的JWT错误类型', async () => {
      const testCases = [
        { error: new Error('jwt expired'), expectedMessage: '认证令牌已过期' },
        { error: new Error('jwt malformed'), expectedMessage: '认证令牌格式错误' },
        { error: new Error('invalid signature'), expectedMessage: '认证令牌签名无效' },
        { error: new Error('unknown error'), expectedMessage: '无效的认证令牌' }
      ];

      for (const testCase of testCases) {
        jest.clearAllMocks();
        
        mockRequest.headers = {
          authorization: 'Bearer test-token'
        };

        mockVerifyAccessToken.mockImplementation(() => {
          throw testCase.error;
        });

        await authenticateToken(mockRequest as Request, mockResponse as Response, mockNext);

        expect(mockResponse.json).toHaveBeenCalledWith({
          success: false,
          error: 'UNAUTHORIZED',
          message: testCase.expectedMessage
        });
      }
    });
  });

  describe('optionalAuth', () => {
    test('应该在有效令牌时设置用户信息', async () => {
      const mockUser = {
        sub: 'user-123',
        email: '<EMAIL>'
      };

      mockRequest.headers = {
        authorization: 'Bearer valid-token'
      };

      mockVerifyAccessToken.mockReturnValue(mockUser);
      mockCacheService.isJWTBlacklisted.mockResolvedValue(false);

      await optionalAuth(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockRequest.user).toEqual({
        id: mockUser.sub,
        email: mockUser.email
      });
      expect(mockNext).toHaveBeenCalled();
    });

    test('应该在无令牌时继续执行', async () => {
      await optionalAuth(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockRequest.user).toBeUndefined();
      expect(mockNext).toHaveBeenCalled();
    });

    test('应该在无效令牌时继续执行', async () => {
      mockRequest.headers = {
        authorization: 'Bearer invalid-token'
      };

      mockVerifyAccessToken.mockImplementation(() => {
        throw new Error('Invalid token');
      });

      await optionalAuth(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockRequest.user).toBeUndefined();
      expect(mockNext).toHaveBeenCalled();
    });

    test('应该在黑名单令牌时继续执行', async () => {
      const mockUser = {
        sub: 'user-123',
        email: '<EMAIL>'
      };

      mockRequest.headers = {
        authorization: 'Bearer blacklisted-token'
      };

      mockVerifyAccessToken.mockReturnValue(mockUser);
      mockCacheService.isJWTBlacklisted.mockResolvedValue(true);

      await optionalAuth(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockRequest.user).toBeUndefined();
      expect(mockNext).toHaveBeenCalled();
    });

    test('应该在缓存错误时继续执行', async () => {
      const mockUser = {
        sub: 'user-123',
        email: '<EMAIL>'
      };

      mockRequest.headers = {
        authorization: 'Bearer valid-token'
      };

      mockVerifyAccessToken.mockReturnValue(mockUser);
      mockCacheService.isJWTBlacklisted.mockRejectedValue(new Error('Cache error'));

      await optionalAuth(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockRequest.user).toBeUndefined();
      expect(mockNext).toHaveBeenCalled();
    });
  });

  describe('令牌提取', () => {
    test('应该支持不同的Bearer格式', async () => {
      const testCases = [
        'Bearer valid-token',
        'bearer valid-token',
        'BEARER valid-token'
      ];

      const mockUser = {
        sub: 'user-123',
        email: '<EMAIL>'
      };

      mockVerifyAccessToken.mockReturnValue(mockUser);
      mockCacheService.isJWTBlacklisted.mockResolvedValue(false);

      for (const authHeader of testCases) {
        jest.clearAllMocks();
        
        mockRequest.headers = {
          authorization: authHeader
        };

        await authenticateToken(mockRequest as Request, mockResponse as Response, mockNext);

        expect(mockVerifyAccessToken).toHaveBeenCalledWith('valid-token');
        expect(mockNext).toHaveBeenCalled();
      }
    });

    test('应该拒绝非Bearer类型的令牌', async () => {
      const testCases = [
        'Basic dXNlcjpwYXNz',
        'Token abc123',
        'valid-token',
        'Bearer',
        ''
      ];

      for (const authHeader of testCases) {
        jest.clearAllMocks();
        
        mockRequest.headers = {
          authorization: authHeader
        };

        await authenticateToken(mockRequest as Request, mockResponse as Response, mockNext);

        expect(mockResponse.status).toHaveBeenCalledWith(401);
        expect(mockNext).not.toHaveBeenCalled();
      }
    });
  });

  describe('用户信息映射', () => {
    test('应该正确映射JWT载荷到用户对象', async () => {
      const jwtPayload = {
        sub: 'user-123',
        email: '<EMAIL>',
        roles: ['admin', 'user'],
        permissions: ['read', 'write'],
        iat: 1234567890,
        exp: 1234567890 + 3600
      };

      mockRequest.headers = {
        authorization: 'Bearer valid-token'
      };

      mockVerifyAccessToken.mockReturnValue(jwtPayload);
      mockCacheService.isJWTBlacklisted.mockResolvedValue(false);

      await authenticateToken(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockRequest.user).toEqual({
        id: jwtPayload.sub,
        email: jwtPayload.email,
        roles: jwtPayload.roles,
        permissions: jwtPayload.permissions
      });
    });

    test('应该处理缺失的可选字段', async () => {
      const jwtPayload = {
        sub: 'user-123',
        email: '<EMAIL>'
        // 缺少roles和permissions
      };

      mockRequest.headers = {
        authorization: 'Bearer valid-token'
      };

      mockVerifyAccessToken.mockReturnValue(jwtPayload);
      mockCacheService.isJWTBlacklisted.mockResolvedValue(false);

      await authenticateToken(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockRequest.user).toEqual({
        id: jwtPayload.sub,
        email: jwtPayload.email,
        roles: undefined,
        permissions: undefined
      });
    });
  });
});
