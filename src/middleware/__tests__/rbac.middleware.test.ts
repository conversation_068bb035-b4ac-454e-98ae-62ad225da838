/**
 * RBAC中间件测试
 */

import { Request, Response, NextFunction } from 'express';
import { requireRole, requirePermission, requireRoleOrPermission, Role, Permission } from '../rbac.middleware';
import { prisma } from '@/config/database';
import { logger } from '@/config/logger';

// Mock依赖
jest.mock('@/config/database', () => ({
  prisma: {
    userRole: {
      findMany: jest.fn()
    }
  }
}));

jest.mock('@/config/logger', () => ({
  logger: {
    warn: jest.fn(),
    error: jest.fn()
  }
}));

const mockPrisma = prisma as jest.Mocked<typeof prisma>;

describe('RBAC中间件', () => {
  let mockRequest: Partial<Request>;
  let mockResponse: Partial<Response>;
  let mockNext: NextFunction;

  beforeEach(() => {
    jest.clearAllMocks();

    mockRequest = {
      user: {
        id: 'user-123',
        email: '<EMAIL>'
      },
      path: '/api/test',
      method: 'GET'
    };

    mockResponse = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn().mockReturnThis()
    };

    mockNext = jest.fn();
  });

  describe('requireRole', () => {
    test('应该允许具有所需角色的用户访问', async () => {
      mockPrisma.userRole.findMany.mockResolvedValue([
        { userId: 'user-123', roleId: 'role-1', role: { name: 'admin' } }
      ] as any);

      const middleware = requireRole(['admin']);
      await middleware(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockNext).toHaveBeenCalled();
      expect(mockRequest.user?.roles).toEqual(['admin']);
    });

    test('应该允许具有多个角色中任一角色的用户访问', async () => {
      mockPrisma.userRole.findMany.mockResolvedValue([
        { userId: 'user-123', roleId: 'role-1', role: { name: 'operator' } }
      ] as any);

      const middleware = requireRole(['admin', 'operator']);
      await middleware(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockNext).toHaveBeenCalled();
      expect(mockRequest.user?.roles).toEqual(['operator']);
    });

    test('应该拒绝没有所需角色的用户', async () => {
      mockPrisma.userRole.findMany.mockResolvedValue([
        { userId: 'user-123', roleId: 'role-1', role: { name: 'user' } }
      ] as any);

      const middleware = requireRole(['admin']);
      await middleware(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(403);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: 'FORBIDDEN',
        message: '权限不足'
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    test('应该拒绝未认证的用户', async () => {
      mockRequest.user = undefined;

      const middleware = requireRole(['admin']);
      await middleware(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(401);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: 'UNAUTHORIZED',
        message: '用户未认证'
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    test('应该记录访问被拒绝的日志', async () => {
      mockPrisma.userRole.findMany.mockResolvedValue([
        { userId: 'user-123', roleId: 'role-1', role: { name: 'user' } }
      ] as any);

      const middleware = requireRole(['admin']);
      await middleware(mockRequest as Request, mockResponse as Response, mockNext);

      expect(logger.warn).toHaveBeenCalledWith('用户访问被拒绝 - 角色不足', {
        userId: 'user-123',
        userRoles: ['user'],
        requiredRoles: ['admin'],
        path: '/api/test',
        method: 'GET'
      });
    });

    test('应该处理数据库查询错误', async () => {
      mockPrisma.userRole.findMany.mockRejectedValue(new Error('Database error'));

      const middleware = requireRole(['admin']);
      await middleware(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(500);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: 'INTERNAL_ERROR',
        message: '角色检查失败'
      });
      expect(logger.error).toHaveBeenCalledWith('角色检查失败', {
        error: 'Database error',
        userId: 'user-123',
        path: '/api/test'
      });
    });
  });

  describe('requirePermission', () => {
    test('应该允许具有所需权限的用户访问', async () => {
      mockPrisma.userRole.findMany.mockResolvedValue([
        { userId: 'user-123', roleId: 'role-1', role: { name: 'admin' } }
      ] as any);

      const middleware = requirePermission([Permission.USER_READ]);
      await middleware(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockNext).toHaveBeenCalled();
      expect(mockRequest.user?.permissions).toContain(Permission.USER_READ);
    });

    test('应该拒绝没有所需权限的用户', async () => {
      mockPrisma.userRole.findMany.mockResolvedValue([
        { userId: 'user-123', roleId: 'role-1', role: { name: 'guest' } }
      ] as any);

      const middleware = requirePermission([Permission.USER_WRITE]);
      await middleware(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(403);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: 'FORBIDDEN',
        message: '权限不足'
      });
    });

    test('应该要求用户具有所有指定的权限', async () => {
      mockPrisma.userRole.findMany.mockResolvedValue([
        { userId: 'user-123', roleId: 'role-1', role: { name: 'operator' } }
      ] as any);

      const middleware = requirePermission([Permission.USER_READ, Permission.USER_DELETE]);
      await middleware(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(403);
      expect(mockNext).not.toHaveBeenCalled();
    });

    test('应该拒绝未认证的用户', async () => {
      mockRequest.user = undefined;

      const middleware = requirePermission([Permission.USER_READ]);
      await middleware(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(401);
      expect(mockNext).not.toHaveBeenCalled();
    });
  });

  describe('requireRoleOrPermission', () => {
    test('应该允许具有所需角色的用户访问', async () => {
      mockPrisma.userRole.findMany.mockResolvedValue([
        { userId: 'user-123', roleId: 'role-1', role: { name: 'admin' } }
      ] as any);

      const middleware = requireRoleOrPermission(['admin'], [Permission.USER_WRITE]);
      await middleware(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockNext).toHaveBeenCalled();
    });

    test('应该允许具有所需权限的用户访问', async () => {
      mockPrisma.userRole.findMany.mockResolvedValue([
        { userId: 'user-123', roleId: 'role-1', role: { name: 'operator' } }
      ] as any);

      const middleware = requireRoleOrPermission(['admin'], [Permission.USER_READ]);
      await middleware(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockNext).toHaveBeenCalled();
    });

    test('应该拒绝既没有角色也没有权限的用户', async () => {
      mockPrisma.userRole.findMany.mockResolvedValue([
        { userId: 'user-123', roleId: 'role-1', role: { name: 'guest' } }
      ] as any);

      const middleware = requireRoleOrPermission(['admin'], [Permission.USER_WRITE]);
      await middleware(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(403);
      expect(mockNext).not.toHaveBeenCalled();
    });

    test('应该记录详细的访问拒绝日志', async () => {
      mockPrisma.userRole.findMany.mockResolvedValue([
        { userId: 'user-123', roleId: 'role-1', role: { name: 'guest' } }
      ] as any);

      const middleware = requireRoleOrPermission(['admin'], [Permission.USER_WRITE]);
      await middleware(mockRequest as Request, mockResponse as Response, mockNext);

      expect(logger.warn).toHaveBeenCalledWith('用户访问被拒绝 - 角色和权限都不足', {
        userId: 'user-123',
        userRoles: ['guest'],
        userPermissions: [Permission.USER_READ],
        requiredRoles: ['admin'],
        requiredPermissions: [Permission.USER_WRITE],
        path: '/api/test',
        method: 'GET'
      });
    });
  });

  describe('角色权限映射', () => {
    test('super_admin应该具有所有权限', async () => {
      mockPrisma.userRole.findMany.mockResolvedValue([
        { userId: 'user-123', roleId: 'role-1', role: { name: Role.SUPER_ADMIN } }
      ] as any);

      const middleware = requirePermission([Permission.SYSTEM_CONFIG]);
      await middleware(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockNext).toHaveBeenCalled();
      expect(mockRequest.user?.permissions).toContain(Permission.SYSTEM_CONFIG);
    });

    test('admin应该具有管理权限但不包括系统配置', async () => {
      mockPrisma.userRole.findMany.mockResolvedValue([
        { userId: 'user-123', roleId: 'role-1', role: { name: Role.ADMIN } }
      ] as any);

      const middleware = requirePermission([Permission.SYSTEM_CONFIG]);
      await middleware(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(403);
      expect(mockNext).not.toHaveBeenCalled();
    });

    test('user应该只有基本读取权限', async () => {
      mockPrisma.userRole.findMany.mockResolvedValue([
        { userId: 'user-123', roleId: 'role-1', role: { name: Role.USER } }
      ] as any);

      const readMiddleware = requirePermission([Permission.USER_READ]);
      await readMiddleware(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockNext).toHaveBeenCalled();

      jest.clearAllMocks();

      const writeMiddleware = requirePermission([Permission.USER_WRITE]);
      await writeMiddleware(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(403);
    });

    test('guest应该只有最基本的权限', async () => {
      mockPrisma.userRole.findMany.mockResolvedValue([
        { userId: 'user-123', roleId: 'role-1', role: { name: Role.GUEST } }
      ] as any);

      const middleware = requirePermission([Permission.USER_READ]);
      await middleware(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockNext).toHaveBeenCalled();
    });
  });

  describe('多角色用户', () => {
    test('应该合并多个角色的权限', async () => {
      mockPrisma.userRole.findMany.mockResolvedValue([
        { userId: 'user-123', roleId: 'role-1', role: { name: Role.USER } },
        { userId: 'user-123', roleId: 'role-2', role: { name: Role.OPERATOR } }
      ] as any);

      const middleware = requirePermission([Permission.MONITOR_READ]);
      await middleware(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockNext).toHaveBeenCalled();
      expect(mockRequest.user?.roles).toEqual([Role.USER, Role.OPERATOR]);
    });

    test('应该去重权限', async () => {
      mockPrisma.userRole.findMany.mockResolvedValue([
        { userId: 'user-123', roleId: 'role-1', role: { name: Role.USER } },
        { userId: 'user-123', roleId: 'role-2', role: { name: Role.OPERATOR } }
      ] as any);

      const middleware = requirePermission([Permission.USER_READ]);
      await middleware(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockNext).toHaveBeenCalled();
      
      // USER_READ权限在两个角色中都存在，但应该只出现一次
      const permissions = mockRequest.user?.permissions || [];
      const userReadCount = permissions.filter(p => p === Permission.USER_READ).length;
      expect(userReadCount).toBe(1);
    });
  });

  describe('便捷方法', () => {
    test('requireAdmin应该要求admin或super_admin角色', async () => {
      const { requireAdmin } = require('../rbac.middleware');
      
      mockPrisma.userRole.findMany.mockResolvedValue([
        { userId: 'user-123', roleId: 'role-1', role: { name: Role.ADMIN } }
      ] as any);

      const middleware = requireAdmin();
      await middleware(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockNext).toHaveBeenCalled();
    });

    test('requireSuperAdmin应该只要求super_admin角色', async () => {
      const { requireSuperAdmin } = require('../rbac.middleware');
      
      mockPrisma.userRole.findMany.mockResolvedValue([
        { userId: 'user-123', roleId: 'role-1', role: { name: Role.ADMIN } }
      ] as any);

      const middleware = requireSuperAdmin();
      await middleware(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(403);
      expect(mockNext).not.toHaveBeenCalled();
    });

    test('requireOperator应该要求operator或更高权限', async () => {
      const { requireOperator } = require('../rbac.middleware');
      
      mockPrisma.userRole.findMany.mockResolvedValue([
        { userId: 'user-123', roleId: 'role-1', role: { name: Role.OPERATOR } }
      ] as any);

      const middleware = requireOperator();
      await middleware(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockNext).toHaveBeenCalled();
    });
  });

  describe('错误处理', () => {
    test('应该处理空的用户角色', async () => {
      mockPrisma.userRole.findMany.mockResolvedValue([]);

      const middleware = requireRole(['admin']);
      await middleware(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(403);
      expect(mockNext).not.toHaveBeenCalled();
    });

    test('应该处理数据库连接错误', async () => {
      mockPrisma.userRole.findMany.mockRejectedValue(new Error('Connection failed'));

      const middleware = requireRole(['admin']);
      await middleware(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(500);
      expect(logger.error).toHaveBeenCalledWith('角色检查失败', {
        error: 'Connection failed',
        userId: 'user-123',
        path: '/api/test'
      });
    });

    test('应该处理未知角色', async () => {
      mockPrisma.userRole.findMany.mockResolvedValue([
        { userId: 'user-123', roleId: 'role-1', role: { name: 'unknown_role' } }
      ] as any);

      const middleware = requirePermission([Permission.USER_READ]);
      await middleware(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(403);
      expect(mockNext).not.toHaveBeenCalled();
    });
  });
});
