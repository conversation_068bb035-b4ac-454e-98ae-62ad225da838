/**
 * 分析数据收集中间件
 * 
 * 功能说明：
 * 1. 自动收集API请求数据
 * 2. 记录响应时间和状态码
 * 3. 收集错误信息
 * 4. 集成用户会话跟踪
 * 5. 支持自定义事件收集
 */

import { Request, Response, NextFunction } from 'express';
import { DataCollector, EventType } from '../services/analytics/DataCollector';
import { PrismaClient } from '@prisma/client';
import Redis from 'ioredis';

/**
 * 分析中间件配置
 */
interface AnalyticsMiddlewareConfig {
  // 是否启用分析
  enabled: boolean;
  
  // 排除的路径（不收集数据）
  excludePaths: string[];
  
  // 排除的方法
  excludeMethods: string[];
  
  // 是否收集请求体（敏感数据需要过滤）
  collectRequestBody: boolean;
  
  // 是否收集响应体
  collectResponseBody: boolean;
  
  // 敏感字段列表（需要过滤）
  sensitiveFields: string[];
  
  // 采样率（0-1，1表示收集所有请求）
  samplingRate: number;
}

/**
 * 默认配置
 */
const defaultConfig: AnalyticsMiddlewareConfig = {
  enabled: true,
  excludePaths: ['/health', '/metrics', '/favicon.ico'],
  excludeMethods: ['OPTIONS'],
  collectRequestBody: false,
  collectResponseBody: false,
  sensitiveFields: ['password', 'token', 'secret', 'key', 'authorization'],
  samplingRate: 1.0
};

/**
 * 创建分析中间件
 */
export function createAnalyticsMiddleware(
  dataCollector: DataCollector,
  config: Partial<AnalyticsMiddlewareConfig> = {}
) {
  const finalConfig = { ...defaultConfig, ...config };

  return (req: Request, res: Response, next: NextFunction) => {
    // 检查是否启用
    if (!finalConfig.enabled) {
      return next();
    }

    // 检查是否需要排除
    if (shouldExclude(req, finalConfig)) {
      return next();
    }

    // 采样检查
    if (Math.random() > finalConfig.samplingRate) {
      return next();
    }

    // 记录开始时间
    const startTime = Date.now();
    
    // 收集请求数据
    const requestData = collectRequestData(req, finalConfig);

    // 监听响应完成事件
    res.on('finish', async () => {
      try {
        const endTime = Date.now();
        const responseTime = endTime - startTime;

        // 收集响应数据
        const responseData = collectResponseData(res, responseTime, finalConfig);

        // 合并请求和响应数据
        const eventData = {
          ...requestData,
          ...responseData,
          responseTime,
          timestamp: new Date(startTime)
        };

        // 确定事件类型
        const eventType = determineEventType(req, res);

        // 收集事件
        await dataCollector.collect(
          eventType,
          req,
          eventData,
          req.user?.id
        );

      } catch (error) {
        console.error('分析数据收集失败:', error);
      }
    });

    // 监听错误事件
    res.on('error', async (error) => {
      try {
        const endTime = Date.now();
        const responseTime = endTime - startTime;

        await dataCollector.collect(
          EventType.API_ERROR,
          req,
          {
            ...requestData,
            error: {
              message: error.message,
              stack: error.stack
            },
            responseTime
          },
          req.user?.id
        );

      } catch (collectError) {
        console.error('错误事件收集失败:', collectError);
      }
    });

    next();
  };
}

/**
 * 检查是否应该排除请求
 */
function shouldExclude(req: Request, config: AnalyticsMiddlewareConfig): boolean {
  // 检查路径
  if (config.excludePaths.some(path => req.path.startsWith(path))) {
    return true;
  }

  // 检查方法
  if (config.excludeMethods.includes(req.method)) {
    return true;
  }

  return false;
}

/**
 * 收集请求数据
 */
function collectRequestData(req: Request, config: AnalyticsMiddlewareConfig): Record<string, any> {
  const data: Record<string, any> = {
    method: req.method,
    path: req.path,
    url: req.url,
    query: req.query,
    headers: filterSensitiveData(req.headers, config.sensitiveFields),
    contentLength: req.get('content-length') || 0
  };

  // 收集请求体（如果启用且不包含敏感数据）
  if (config.collectRequestBody && req.body) {
    data.body = filterSensitiveData(req.body, config.sensitiveFields);
  }

  // 收集用户信息
  if (req.user) {
    data.user = {
      id: req.user.id,
      email: req.user.email,
      role: req.user.role
    };
  }

  // 收集会话信息
  if (req.session) {
    data.session = {
      id: req.session.id,
      isAuthenticated: !!req.user
    };
  }

  return data;
}

/**
 * 收集响应数据
 */
function collectResponseData(
  res: Response, 
  responseTime: number, 
  config: AnalyticsMiddlewareConfig
): Record<string, any> {
  const data: Record<string, any> = {
    statusCode: res.statusCode,
    statusMessage: res.statusMessage,
    contentLength: res.get('content-length') || 0,
    contentType: res.get('content-type')
  };

  // 收集响应头
  const responseHeaders = res.getHeaders();
  data.headers = filterSensitiveData(responseHeaders, config.sensitiveFields);

  return data;
}

/**
 * 过滤敏感数据
 */
function filterSensitiveData(data: any, sensitiveFields: string[]): any {
  if (!data || typeof data !== 'object') {
    return data;
  }

  const filtered = { ...data };

  for (const field of sensitiveFields) {
    if (field in filtered) {
      filtered[field] = '[FILTERED]';
    }
  }

  // 递归过滤嵌套对象
  for (const [key, value] of Object.entries(filtered)) {
    if (value && typeof value === 'object') {
      filtered[key] = filterSensitiveData(value, sensitiveFields);
    }
  }

  return filtered;
}

/**
 * 确定事件类型
 */
function determineEventType(req: Request, res: Response): EventType {
  // 根据路径和状态码确定事件类型
  if (res.statusCode >= 400) {
    return EventType.API_ERROR;
  }

  // 认证相关端点
  if (req.path.includes('/auth/')) {
    if (req.path.includes('/login')) {
      return res.statusCode === 200 ? EventType.AUTH_SUCCESS : EventType.AUTH_FAILURE;
    }
    if (req.path.includes('/logout')) {
      return EventType.USER_LOGOUT;
    }
    if (req.path.includes('/register')) {
      return EventType.USER_REGISTER;
    }
    if (req.path.includes('/refresh')) {
      return EventType.TOKEN_REFRESH;
    }
  }

  // MFA相关端点
  if (req.path.includes('/mfa/')) {
    return EventType.MFA_VERIFY;
  }

  // 用户相关端点
  if (req.path.includes('/user/') || req.path.includes('/profile/')) {
    return EventType.USER_PROFILE_UPDATE;
  }

  // 管理员操作
  if (req.path.includes('/admin/')) {
    return EventType.ADMIN_ACTION;
  }

  // 默认API请求
  return EventType.API_REQUEST;
}

/**
 * 性能监控中间件
 */
export function createPerformanceMiddleware(prisma: PrismaClient) {
  return async (req: Request, res: Response, next: NextFunction) => {
    const startTime = process.hrtime.bigint();
    const startMemory = process.memoryUsage();

    res.on('finish', async () => {
      try {
        const endTime = process.hrtime.bigint();
        const endMemory = process.memoryUsage();
        
        const responseTime = Number(endTime - startTime) / 1000000; // 转换为毫秒
        const memoryDelta = endMemory.heapUsed - startMemory.heapUsed;

        // 记录性能指标
        await prisma.performanceMetric.createMany({
          data: [
            {
              metricType: 'response_time',
              value: responseTime,
              unit: 'ms',
              endpoint: req.path,
              method: req.method,
              statusCode: res.statusCode
            },
            {
              metricType: 'memory_usage',
              value: memoryDelta,
              unit: 'bytes',
              endpoint: req.path,
              method: req.method,
              statusCode: res.statusCode
            }
          ]
        });

      } catch (error) {
        console.error('性能指标记录失败:', error);
      }
    });

    next();
  };
}

/**
 * 会话分析中间件
 */
export function createSessionAnalyticsMiddleware(prisma: PrismaClient) {
  return async (req: Request, res: Response, next: NextFunction) => {
    if (!req.session?.id) {
      return next();
    }

    try {
      // 更新或创建会话分析记录
      await prisma.sessionAnalytics.upsert({
        where: { sessionId: req.session.id },
        update: {
          pageViews: { increment: 1 },
          apiCalls: { increment: 1 },
          errorCount: res.statusCode >= 400 ? { increment: 1 } : undefined,
          updatedAt: new Date()
        },
        create: {
          sessionId: req.session.id,
          userId: req.user?.id,
          startTime: new Date(),
          ip: req.ip || '127.0.0.1',
          userAgent: req.get('User-Agent') || '',
          pageViews: 1,
          apiCalls: 1,
          errorCount: res.statusCode >= 400 ? 1 : 0
        }
      });

    } catch (error) {
      console.error('会话分析更新失败:', error);
    }

    next();
  };
}

export default createAnalyticsMiddleware;
