/**
 * 国际化中间件
 * 处理语言检测、设置和本地化上下文
 */

import { Request, Response, NextFunction } from 'express';
import { logger } from '@/config/logger';
import { i18nService, SupportedLanguage } from '@/services/i18n.service';
import { cacheService } from '@/services/cache.service';

/**
 * 国际化上下文接口
 */
interface I18nContext {
  language: SupportedLanguage;
  direction: 'ltr' | 'rtl';
  region: string;
  timezone: string;
  dateFormat: string;
  timeFormat: string;
  numberFormat: {
    decimal: string;
    thousands: string;
    currency: string;
  };
}

/**
 * 语言检测配置
 */
interface LanguageDetectionConfig {
  sources: ('header' | 'cookie' | 'query' | 'session' | 'user')[];
  cookieName: string;
  queryParam: string;
  sessionKey: string;
  cacheUserPreference: boolean;
  fallbackLanguage: SupportedLanguage;
}

/**
 * 默认语言检测配置
 */
const defaultDetectionConfig: LanguageDetectionConfig = {
  sources: ['query', 'cookie', 'session', 'user', 'header'],
  cookieName: 'i18n_lang',
  queryParam: 'lang',
  sessionKey: 'language',
  cacheUserPreference: true,
  fallbackLanguage: SupportedLanguage.EN_US
};

/**
 * 国际化中间件
 */
export function i18nMiddleware(config: Partial<LanguageDetectionConfig> = {}) {
  const finalConfig = { ...defaultDetectionConfig, ...config };

  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      // 检测用户语言偏好
      const detectedLanguage = await detectUserLanguage(req, finalConfig);

      // 获取语言信息和本地化配置
      const languageInfo = i18nService.getLanguageInfo(detectedLanguage);
      const localizationConfig = i18nService.getLocalizationConfig(detectedLanguage);

      // 创建国际化上下文
      const i18nContext: I18nContext = {
        language: detectedLanguage,
        direction: languageInfo?.direction || 'ltr',
        region: languageInfo?.region || 'Unknown',
        timezone: localizationConfig.timezone,
        dateFormat: localizationConfig.dateFormat,
        timeFormat: localizationConfig.timeFormat,
        numberFormat: localizationConfig.numberFormat
      };

      // 将上下文添加到请求对象
      req.i18n = {
        language: detectedLanguage,
        context: i18nContext,
        t: (key: string, variables?: Record<string, any>) => {
          return i18nService.translate(key, {
            language: detectedLanguage,
            variables
          });
        },
        formatDate: (date: Date, format?: string) => {
          return i18nService.formatDate(date, detectedLanguage, format);
        },
        formatTime: (date: Date, format?: string) => {
          return i18nService.formatTime(date, detectedLanguage, format);
        },
        formatNumber: (number: number, options?: Intl.NumberFormatOptions) => {
          return i18nService.formatNumber(number, detectedLanguage, options);
        },
        formatCurrency: (amount: number, currency: string) => {
          return i18nService.formatCurrency(amount, currency, detectedLanguage);
        },
        getRelativeTime: (date: Date) => {
          return i18nService.getRelativeTime(date, detectedLanguage);
        }
      };

      // 设置响应头
      res.setHeader('Content-Language', detectedLanguage);
      res.setHeader('X-Language-Direction', i18nContext.direction);

      // 如果语言发生变化，更新Cookie
      const currentCookieLanguage = req.cookies[finalConfig.cookieName];
      if (currentCookieLanguage !== detectedLanguage) {
        res.cookie(finalConfig.cookieName, detectedLanguage, {
          maxAge: 365 * 24 * 60 * 60 * 1000, // 1年
          httpOnly: false, // 允许客户端访问
          secure: process.env.NODE_ENV === 'production',
          sameSite: 'lax'
        });
      }

      // 缓存用户语言偏好
      if (finalConfig.cacheUserPreference && req.user?.id) {
        const cacheKey = `user_language:${req.user.id}`;
        await cacheService.set(cacheKey, detectedLanguage, 30 * 24 * 60 * 60); // 30天
      }

      logger.debug('国际化上下文设置完成', {
        userId: req.user?.id,
        detectedLanguage,
        direction: i18nContext.direction,
        region: i18nContext.region
      });

      next();

    } catch (error) {
      logger.error('国际化中间件处理失败', {
        error: error instanceof Error ? error.message : String(error),
        userId: req.user?.id
      });

      // 设置默认语言上下文
      req.i18n = createDefaultI18nContext(finalConfig.fallbackLanguage);
      next();
    }
  };
}

/**
 * 语言切换中间件
 */
export function languageSwitchMiddleware() {
  return async (req: Request, res: Response, next: NextFunction) => {
    // 检查是否有语言切换请求
    const newLanguage = req.query.lang as string || req.body.language as string;
    
    if (newLanguage && Object.values(SupportedLanguage).includes(newLanguage as SupportedLanguage)) {
      const language = newLanguage as SupportedLanguage;
      
      // 更新会话
      if (req.session) {
        req.session.language = language;
      }

      // 更新用户偏好（如果已登录）
      if (req.user?.id) {
        try {
          // 这里可以更新数据库中的用户语言偏好
          // await updateUserLanguagePreference(req.user.id, language);
          
          // 更新缓存
          const cacheKey = `user_language:${req.user.id}`;
          await cacheService.set(cacheKey, language, 30 * 24 * 60 * 60); // 30天
          
          logger.info('用户语言偏好已更新', {
            userId: req.user.id,
            newLanguage: language
          });
        } catch (error) {
          logger.error('更新用户语言偏好失败', {
            error: error instanceof Error ? error.message : String(error),
            userId: req.user.id,
            language
          });
        }
      }

      // 设置Cookie
      res.cookie('i18n_lang', language, {
        maxAge: 365 * 24 * 60 * 60 * 1000, // 1年
        httpOnly: false,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax'
      });

      // 如果是API请求，返回成功响应
      if (req.path.startsWith('/api/')) {
        return res.json({
          success: true,
          message: 'Language preference updated',
          language
        });
      }

      // 如果是页面请求，重定向到当前页面
      const redirectUrl = req.get('Referer') || '/';
      return res.redirect(redirectUrl);
    }

    next();
  };
}

/**
 * RTL支持中间件
 */
export function rtlSupportMiddleware() {
  return (req: Request, res: Response, next: NextFunction) => {
    const language = req.i18n?.language || SupportedLanguage.EN_US;
    const languageInfo = i18nService.getLanguageInfo(language);
    
    if (languageInfo?.direction === 'rtl') {
      // 为RTL语言添加特殊处理
      res.locals.isRTL = true;
      res.locals.direction = 'rtl';
      res.setHeader('X-Text-Direction', 'rtl');
    } else {
      res.locals.isRTL = false;
      res.locals.direction = 'ltr';
      res.setHeader('X-Text-Direction', 'ltr');
    }

    next();
  };
}

/**
 * 检测用户语言偏好
 */
async function detectUserLanguage(req: Request, config: LanguageDetectionConfig): Promise<SupportedLanguage> {
  for (const source of config.sources) {
    let detectedLanguage: SupportedLanguage | null = null;

    switch (source) {
      case 'query':
        // 从查询参数检测
        const queryLang = req.query[config.queryParam] as string;
        if (queryLang && Object.values(SupportedLanguage).includes(queryLang as SupportedLanguage)) {
          detectedLanguage = queryLang as SupportedLanguage;
        }
        break;

      case 'cookie':
        // 从Cookie检测
        if (req.cookies && typeof req.cookies === 'object') {
          const cookieLang = req.cookies[config.cookieName];
          if (cookieLang && Object.values(SupportedLanguage).includes(cookieLang as SupportedLanguage)) {
            detectedLanguage = cookieLang as SupportedLanguage;
          }
        } else {
          logger.debug('Cookie对象不可用，跳过Cookie语言检测', {
            cookiesType: typeof req.cookies,
            cookiesValue: req.cookies
          });
        }
        break;

      case 'session':
        // 从会话检测
        const sessionLang = req.session?.[config.sessionKey];
        if (sessionLang && Object.values(SupportedLanguage).includes(sessionLang as SupportedLanguage)) {
          detectedLanguage = sessionLang as SupportedLanguage;
        }
        break;

      case 'user':
        // 从用户偏好检测
        if (req.user?.id) {
          try {
            const cacheKey = `user_language:${req.user.id}`;
            const cachedLang = await cacheService.get(cacheKey);
            if (cachedLang && Object.values(SupportedLanguage).includes(cachedLang as SupportedLanguage)) {
              detectedLanguage = cachedLang as SupportedLanguage;
            }
          } catch (error) {
            logger.debug('获取用户语言偏好失败', { error: error.message });
          }
        }
        break;

      case 'header':
        // 从Accept-Language头部检测
        const acceptLanguage = req.get('Accept-Language');
        const userAgent = req.get('User-Agent');
        detectedLanguage = i18nService.detectUserLanguage(acceptLanguage, userAgent);
        break;
    }

    if (detectedLanguage) {
      // 验证语言是否启用
      const languageInfo = i18nService.getLanguageInfo(detectedLanguage);
      if (languageInfo?.enabled) {
        return detectedLanguage;
      }
    }
  }

  return config.fallbackLanguage;
}

/**
 * 创建默认国际化上下文
 */
function createDefaultI18nContext(language: SupportedLanguage): any {
  const languageInfo = i18nService.getLanguageInfo(language);
  const localizationConfig = i18nService.getLocalizationConfig(language);

  const context: I18nContext = {
    language,
    direction: languageInfo?.direction || 'ltr',
    region: languageInfo?.region || 'Unknown',
    timezone: localizationConfig.timezone,
    dateFormat: localizationConfig.dateFormat,
    timeFormat: localizationConfig.timeFormat,
    numberFormat: localizationConfig.numberFormat
  };

  return {
    language,
    context,
    t: (key: string, variables?: Record<string, any>) => {
      return i18nService.translate(key, { language, variables });
    },
    formatDate: (date: Date, format?: string) => {
      return i18nService.formatDate(date, language, format);
    },
    formatTime: (date: Date, format?: string) => {
      return i18nService.formatTime(date, language, format);
    },
    formatNumber: (number: number, options?: Intl.NumberFormatOptions) => {
      return i18nService.formatNumber(number, language, options);
    },
    formatCurrency: (amount: number, currency: string) => {
      return i18nService.formatCurrency(amount, currency, language);
    },
    getRelativeTime: (date: Date) => {
      return i18nService.getRelativeTime(date, language);
    }
  };
}

// 扩展Express Request接口
declare global {
  namespace Express {
    interface Request {
      i18n?: {
        language: SupportedLanguage;
        context: I18nContext;
        t: (key: string, variables?: Record<string, any>) => string;
        formatDate: (date: Date, format?: string) => string;
        formatTime: (date: Date, format?: string) => string;
        formatNumber: (number: number, options?: Intl.NumberFormatOptions) => string;
        formatCurrency: (amount: number, currency: string) => string;
        getRelativeTime: (date: Date) => string;
      };
    }
  }
}
