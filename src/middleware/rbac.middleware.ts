/**
 * 基于角色的访问控制 (RBAC) 中间件
 * 提供细粒度的权限控制和角色管理
 */

import { Request, Response, NextFunction } from 'express';
import { logger } from '@/config/logger';
import { prisma } from '@/config/database';

/**
 * 扩展Request接口以包含用户信息
 */
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        email: string;
        roles?: string[];
        permissions?: string[];
      };
    }
  }
}

/**
 * 角色枚举
 */
export enum Role {
  SUPER_ADMIN = 'super_admin',
  ADMIN = 'admin',
  OPERATOR = 'operator',
  USER = 'user',
  GUEST = 'guest'
}

/**
 * 权限枚举
 */
export enum Permission {
  // 用户管理权限
  USER_READ = 'user:read',
  USER_WRITE = 'user:write',
  USER_DELETE = 'user:delete',
  
  // 应用管理权限
  APP_READ = 'app:read',
  APP_WRITE = 'app:write',
  APP_DELETE = 'app:delete',
  
  // 系统管理权限
  SYSTEM_READ = 'system:read',
  SYSTEM_WRITE = 'system:write',
  SYSTEM_CONFIG = 'system:config',
  
  // 监控权限
  MONITOR_READ = 'monitor:read',
  MONITOR_WRITE = 'monitor:write',
  
  // 审计权限
  AUDIT_READ = 'audit:read',
  AUDIT_WRITE = 'audit:write'
}

/**
 * 角色权限映射
 */
const ROLE_PERMISSIONS: Record<Role, Permission[]> = {
  [Role.SUPER_ADMIN]: Object.values(Permission),
  [Role.ADMIN]: [
    Permission.USER_READ,
    Permission.USER_WRITE,
    Permission.USER_DELETE,
    Permission.APP_READ,
    Permission.APP_WRITE,
    Permission.APP_DELETE,
    Permission.SYSTEM_READ,
    Permission.SYSTEM_WRITE,
    Permission.MONITOR_READ,
    Permission.MONITOR_WRITE,
    Permission.AUDIT_READ
  ],
  [Role.OPERATOR]: [
    Permission.USER_READ,
    Permission.USER_WRITE,
    Permission.APP_READ,
    Permission.APP_WRITE,
    Permission.SYSTEM_READ,
    Permission.MONITOR_READ,
    Permission.AUDIT_READ
  ],
  [Role.USER]: [
    Permission.USER_READ,
    Permission.APP_READ,
    Permission.MONITOR_READ
  ],
  [Role.GUEST]: [
    Permission.USER_READ
  ]
};

/**
 * 检查用户是否具有指定角色
 */
export function requireRole(allowedRoles: string[]) {
  return async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      // 检查用户是否已认证
      if (!req.user) {
        res.status(401).json({
          success: false,
          error: 'UNAUTHORIZED',
          message: '用户未认证'
        });
        return;
      }

      // 获取用户角色
      const userRoles = await getUserRoles(req.user.id);
      
      // 检查是否有匹配的角色
      const hasRequiredRole = allowedRoles.some(role => 
        userRoles.includes(role)
      );

      if (!hasRequiredRole) {
        logger.warn('用户访问被拒绝 - 角色不足', {
          userId: req.user.id,
          userRoles,
          requiredRoles: allowedRoles,
          path: req.path,
          method: req.method
        });

        res.status(403).json({
          success: false,
          error: 'FORBIDDEN',
          message: '权限不足'
        });
        return;
      }

      // 将用户角色添加到请求对象
      req.user.roles = userRoles;
      next();

    } catch (error) {
      logger.error('角色检查失败', {
        error: error instanceof Error ? error.message : String(error),
        userId: req.user?.id,
        path: req.path
      });

      res.status(500).json({
        success: false,
        error: 'INTERNAL_ERROR',
        message: '角色检查失败'
      });
    }
  };
}

/**
 * 检查用户是否具有指定权限
 */
export function requirePermission(requiredPermissions: Permission[]) {
  return async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      // 检查用户是否已认证
      if (!req.user) {
        res.status(401).json({
          success: false,
          error: 'UNAUTHORIZED',
          message: '用户未认证'
        });
        return;
      }

      // 获取用户权限
      const userPermissions = await getUserPermissions(req.user.id);
      
      // 检查是否有所有必需的权限
      const hasAllPermissions = requiredPermissions.every(permission => 
        userPermissions.includes(permission)
      );

      if (!hasAllPermissions) {
        logger.warn('用户访问被拒绝 - 权限不足', {
          userId: req.user.id,
          userPermissions,
          requiredPermissions,
          path: req.path,
          method: req.method
        });

        res.status(403).json({
          success: false,
          error: 'FORBIDDEN',
          message: '权限不足'
        });
        return;
      }

      // 将用户权限添加到请求对象
      req.user.permissions = userPermissions;
      next();

    } catch (error) {
      logger.error('权限检查失败', {
        error: error instanceof Error ? error.message : String(error),
        userId: req.user?.id,
        path: req.path
      });

      res.status(500).json({
        success: false,
        error: 'INTERNAL_ERROR',
        message: '权限检查失败'
      });
    }
  };
}

/**
 * 检查用户是否具有指定角色或权限（任一满足即可）
 */
export function requireRoleOrPermission(
  allowedRoles: string[], 
  allowedPermissions: Permission[]
) {
  return async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      // 检查用户是否已认证
      if (!req.user) {
        res.status(401).json({
          success: false,
          error: 'UNAUTHORIZED',
          message: '用户未认证'
        });
        return;
      }

      // 获取用户角色和权限
      const [userRoles, userPermissions] = await Promise.all([
        getUserRoles(req.user.id),
        getUserPermissions(req.user.id)
      ]);

      // 检查角色
      const hasRequiredRole = allowedRoles.some(role => 
        userRoles.includes(role)
      );

      // 检查权限
      const hasRequiredPermission = allowedPermissions.some(permission => 
        userPermissions.includes(permission)
      );

      if (!hasRequiredRole && !hasRequiredPermission) {
        logger.warn('用户访问被拒绝 - 角色和权限都不足', {
          userId: req.user.id,
          userRoles,
          userPermissions,
          requiredRoles: allowedRoles,
          requiredPermissions: allowedPermissions,
          path: req.path,
          method: req.method
        });

        res.status(403).json({
          success: false,
          error: 'FORBIDDEN',
          message: '权限不足'
        });
        return;
      }

      // 将用户角色和权限添加到请求对象
      req.user.roles = userRoles;
      req.user.permissions = userPermissions;
      next();

    } catch (error) {
      logger.error('角色或权限检查失败', {
        error: error instanceof Error ? error.message : String(error),
        userId: req.user?.id,
        path: req.path
      });

      res.status(500).json({
        success: false,
        error: 'INTERNAL_ERROR',
        message: '角色或权限检查失败'
      });
    }
  };
}

/**
 * 获取用户角色
 */
async function getUserRoles(userId: string): Promise<string[]> {
  try {
    const userRoles = await prisma.userRole.findMany({
      where: { userId },
      include: { role: true }
    });

    return userRoles.map(ur => ur.role.name);
  } catch (error) {
    logger.error('获取用户角色失败', {
      error: error instanceof Error ? error.message : String(error),
      userId
    });
    return [];
  }
}

/**
 * 获取用户权限
 */
async function getUserPermissions(userId: string): Promise<Permission[]> {
  try {
    const userRoles = await getUserRoles(userId);
    const permissions = new Set<Permission>();

    // 根据角色获取权限
    userRoles.forEach(roleName => {
      const role = roleName as Role;
      if (ROLE_PERMISSIONS[role]) {
        ROLE_PERMISSIONS[role].forEach(permission => {
          permissions.add(permission);
        });
      }
    });

    return Array.from(permissions);
  } catch (error) {
    logger.error('获取用户权限失败', {
      error: error instanceof Error ? error.message : String(error),
      userId
    });
    return [];
  }
}

/**
 * 检查用户是否为管理员
 */
export function requireAdmin() {
  return requireRole([Role.ADMIN, Role.SUPER_ADMIN]);
}

/**
 * 检查用户是否为超级管理员
 */
export function requireSuperAdmin() {
  return requireRole([Role.SUPER_ADMIN]);
}

/**
 * 检查用户是否为操作员或更高权限
 */
export function requireOperator() {
  return requireRole([Role.OPERATOR, Role.ADMIN, Role.SUPER_ADMIN]);
}
