/**
 * 日志收集中间件
 * 自动收集HTTP请求日志和应用日志
 */

import { Request, Response, NextFunction } from 'express';
import { logAggregator, LogLevel } from '@/services/log-aggregator.service';
import { metricsCollector } from '@/services/metrics-collector.service';
import { logger } from '@/config/logger';

/**
 * 扩展Request接口以包含日志相关信息
 */
declare global {
  namespace Express {
    interface Request {
      requestId?: string;
      startTime?: number;
    }
  }
}

/**
 * 生成请求ID
 */
function generateRequestId(): string {
  return `req_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`;
}

/**
 * 获取客户端IP地址
 */
function getClientIP(req: Request): string {
  return (
    req.headers['x-forwarded-for'] as string ||
    req.headers['x-real-ip'] as string ||
    req.connection.remoteAddress ||
    req.socket.remoteAddress ||
    'unknown'
  );
}

/**
 * 确定日志级别
 */
function determineLogLevel(statusCode: number): LogLevel {
  if (statusCode >= 500) return LogLevel.ERROR;
  if (statusCode >= 400) return LogLevel.WARN;
  if (statusCode >= 300) return LogLevel.INFO;
  return LogLevel.INFO;
}

/**
 * HTTP请求日志中间件
 */
export const httpLogMiddleware = (req: Request, res: Response, next: NextFunction): void => {
  // 生成请求ID和记录开始时间
  req.requestId = generateRequestId();
  req.startTime = Date.now();

  const clientIP = getClientIP(req);
  const userAgent = req.headers['user-agent'] || 'unknown';

  // 记录请求开始日志
  logAggregator.addLog({
    level: LogLevel.INFO,
    message: `HTTP请求开始: ${req.method} ${req.originalUrl}`,
    service: 'http-server',
    module: 'request-handler',
    userId: (req as any).user?.id,
    sessionId: (req as any).sessionId,
    requestId: req.requestId,
    ipAddress: clientIP,
    userAgent,
    metadata: {
      method: req.method,
      url: req.originalUrl,
      query: req.query,
      headers: {
        'content-type': req.headers['content-type'],
        'authorization': req.headers.authorization ? '[REDACTED]' : undefined,
        'user-agent': userAgent
      }
    },
    tags: ['http', 'request', 'start']
  });

  // 记录请求指标
  metricsCollector.incrementCounter('http_requests_total', 1, {
    method: req.method,
    endpoint: req.route?.path || req.originalUrl
  });

  // 监听响应完成事件
  res.on('finish', () => {
    const duration = Date.now() - (req.startTime || Date.now());
    const logLevel = determineLogLevel(res.statusCode);

    // 记录请求完成日志
    logAggregator.addLog({
      level: logLevel,
      message: `HTTP请求完成: ${req.method} ${req.originalUrl} - ${res.statusCode} (${duration}ms)`,
      service: 'http-server',
      module: 'request-handler',
      userId: (req as any).user?.id,
      sessionId: (req as any).sessionId,
      requestId: req.requestId,
      ipAddress: clientIP,
      userAgent,
      metadata: {
        method: req.method,
        url: req.originalUrl,
        statusCode: res.statusCode,
        duration,
        responseSize: res.get('content-length') || 0
      },
      tags: ['http', 'request', 'complete']
    });

    // 记录响应时间指标
    metricsCollector.recordHistogram('http_request_duration_ms', duration, {
      method: req.method,
      status_code: res.statusCode.toString(),
      endpoint: req.route?.path || req.originalUrl
    });

    // 记录状态码指标
    metricsCollector.incrementCounter('http_responses_total', 1, {
      method: req.method,
      status_code: res.statusCode.toString(),
      endpoint: req.route?.path || req.originalUrl
    });

    // 如果是错误响应，记录错误指标
    if (res.statusCode >= 400) {
      metricsCollector.incrementCounter('http_errors_total', 1, {
        method: req.method,
        status_code: res.statusCode.toString(),
        endpoint: req.route?.path || req.originalUrl
      });
    }
  });

  next();
};

/**
 * 错误日志中间件
 */
export const errorLogMiddleware = (
  error: Error,
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  const clientIP = getClientIP(req);
  const userAgent = req.headers['user-agent'] || 'unknown';

  // 记录错误日志
  logAggregator.addLog({
    level: LogLevel.ERROR,
    message: `HTTP请求错误: ${error.message}`,
    service: 'http-server',
    module: 'error-handler',
    userId: (req as any).user?.id,
    sessionId: (req as any).sessionId,
    requestId: req.requestId,
    ipAddress: clientIP,
    userAgent,
    metadata: {
      method: req.method,
      url: req.originalUrl,
      errorName: error.name,
      errorMessage: error.message,
      stack: error.stack
    },
    stack: error.stack,
    tags: ['http', 'error', 'exception']
  });

  // 记录错误指标
  metricsCollector.incrementCounter('application_errors_total', 1, {
    error_type: error.name,
    endpoint: req.route?.path || req.originalUrl
  });

  next(error);
};

/**
 * 自定义日志记录器
 */
export class ApplicationLogger {
  private static instance: ApplicationLogger;

  private constructor() {}

  static getInstance(): ApplicationLogger {
    if (!ApplicationLogger.instance) {
      ApplicationLogger.instance = new ApplicationLogger();
    }
    return ApplicationLogger.instance;
  }

  /**
   * 记录信息日志
   */
  info(message: string, metadata?: Record<string, any>, options?: {
    service?: string;
    module?: string;
    userId?: string;
    sessionId?: string;
    tags?: string[];
  }): void {
    logAggregator.addLog({
      level: LogLevel.INFO,
      message,
      service: options?.service || 'application',
      module: options?.module,
      userId: options?.userId,
      sessionId: options?.sessionId,
      metadata,
      tags: options?.tags || ['application']
    });

    // 同时使用原有的logger
    logger.info(message, metadata);
  }

  /**
   * 记录警告日志
   */
  warn(message: string, metadata?: Record<string, any>, options?: {
    service?: string;
    module?: string;
    userId?: string;
    sessionId?: string;
    tags?: string[];
  }): void {
    logAggregator.addLog({
      level: LogLevel.WARN,
      message,
      service: options?.service || 'application',
      module: options?.module,
      userId: options?.userId,
      sessionId: options?.sessionId,
      metadata,
      tags: options?.tags || ['application', 'warning']
    });

    logger.warn(message, metadata);
  }

  /**
   * 记录错误日志
   */
  error(message: string, error?: Error, metadata?: Record<string, any>, options?: {
    service?: string;
    module?: string;
    userId?: string;
    sessionId?: string;
    tags?: string[];
  }): void {
    logAggregator.addLog({
      level: LogLevel.ERROR,
      message,
      service: options?.service || 'application',
      module: options?.module,
      userId: options?.userId,
      sessionId: options?.sessionId,
      metadata: {
        ...metadata,
        errorName: error?.name,
        errorMessage: error?.message
      },
      stack: error?.stack,
      tags: options?.tags || ['application', 'error']
    });

    logger.error(message, { ...metadata, error: error?.message });
  }

  /**
   * 记录调试日志
   */
  debug(message: string, metadata?: Record<string, any>, options?: {
    service?: string;
    module?: string;
    userId?: string;
    sessionId?: string;
    tags?: string[];
  }): void {
    logAggregator.addLog({
      level: LogLevel.DEBUG,
      message,
      service: options?.service || 'application',
      module: options?.module,
      userId: options?.userId,
      sessionId: options?.sessionId,
      metadata,
      tags: options?.tags || ['application', 'debug']
    });

    logger.debug(message, metadata);
  }

  /**
   * 记录业务事件日志
   */
  logBusinessEvent(event: string, details: Record<string, any>, options?: {
    userId?: string;
    sessionId?: string;
    severity?: 'low' | 'medium' | 'high';
  }): void {
    const level = options?.severity === 'high' ? LogLevel.WARN : LogLevel.INFO;

    logAggregator.addLog({
      level,
      message: `业务事件: ${event}`,
      service: 'business-logic',
      module: 'event-tracker',
      userId: options?.userId,
      sessionId: options?.sessionId,
      metadata: {
        event,
        ...details,
        severity: options?.severity || 'medium'
      },
      tags: ['business', 'event', options?.severity || 'medium']
    });

    // 记录业务事件指标
    metricsCollector.incrementCounter('business_events_total', 1, {
      event,
      severity: options?.severity || 'medium'
    });
  }

  /**
   * 记录安全事件日志
   */
  logSecurityEvent(event: string, details: Record<string, any>, options?: {
    userId?: string;
    sessionId?: string;
    ipAddress?: string;
    severity?: 'low' | 'medium' | 'high' | 'critical';
  }): void {
    const level = options?.severity === 'critical' || options?.severity === 'high' 
      ? LogLevel.ERROR 
      : LogLevel.WARN;

    logAggregator.addLog({
      level,
      message: `安全事件: ${event}`,
      service: 'security',
      module: 'security-monitor',
      userId: options?.userId,
      sessionId: options?.sessionId,
      ipAddress: options?.ipAddress,
      metadata: {
        event,
        ...details,
        severity: options?.severity || 'medium'
      },
      tags: ['security', 'event', options?.severity || 'medium']
    });

    // 记录安全事件指标
    metricsCollector.incrementCounter('security_events_total', 1, {
      event,
      severity: options?.severity || 'medium'
    });
  }
}

// 导出单例实例
export const appLogger = ApplicationLogger.getInstance();
