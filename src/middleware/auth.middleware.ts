/**
 * 认证中间件
 * 处理JWT令牌验证和用户认证
 */

import { Request, Response, NextFunction } from 'express';
import { verifyAccessToken, JWTPayload } from '@/utils/jwt';
import { prisma } from '@/config/database';
import { logger, logSecurityEvent } from '@/config/logger';
import { cacheService } from '@/services/cache.service';

/**
 * 扩展Request接口，添加用户信息
 */
export interface AuthenticatedRequest extends Request {
  user: {
    userId: string;
    email: string;
    sessionId?: string | undefined;
    roles?: string[];
  } | undefined;
}

/**
 * JWT认证中间件
 * 验证访问令牌并设置用户信息
 */
export const authenticateToken = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

    if (!token) {
      res.status(401).json({
        error: 'missing_token',
        message: '缺少访问令牌'
      });
      return;
    }

    // 验证JWT令牌
    const payload: JWTPayload = verifyAccessToken(token);

    // 检查JWT令牌是否在黑名单中
    if (payload.jti) {
      const isBlacklisted = await cacheService.isJWTBlacklisted(payload.jti);
      if (isBlacklisted) {
        logSecurityEvent('jwt_blacklisted', {
          jti: payload.jti,
          userId: payload.userId
        }, req.ip);

        res.status(401).json({
          error: 'token_revoked',
          message: '令牌已被撤销'
        });
        return;
      }
    }

    // 检查用户是否存在且活跃
    const user = await prisma.user.findUnique({
      where: { 
        id: payload.userId,
        isActive: true,
        isLocked: false
      },
      include: {
        userRoles: {
          include: {
            role: true
          }
        }
      }
    });

    if (!user) {
      logSecurityEvent('token_validation_user_not_found', { userId: payload.userId }, payload.userId, req.ip);
      res.status(401).json({
        error: 'invalid_user',
        message: '用户不存在或已被禁用'
      });
      return;
    }

    // 如果有会话ID，检查会话是否有效
    if (payload.sessionId) {
      const session = await prisma.session.findUnique({
        where: { 
          id: payload.sessionId,
          isActive: true,
          expiresAt: { gt: new Date() }
        }
      });

      if (!session) {
        logSecurityEvent('token_validation_session_invalid', { sessionId: payload.sessionId }, user.id, req.ip);
        res.status(401).json({
          error: 'invalid_session',
          message: '会话已过期或无效'
        });
        return;
      }

      // 更新会话最后访问时间
      await prisma.session.update({
        where: { id: session.id },
        data: { lastAccessedAt: new Date() }
      });
    }

    // 设置用户信息到请求对象
    const userInfo: any = {
      userId: user.id,
      email: user.email,
      roles: user.userRoles.map(ur => ur.role.name)
    };

    if (payload.sessionId) {
      userInfo.sessionId = payload.sessionId;
    }

    req.user = userInfo;

    next();

  } catch (error) {
    logger.warn('令牌验证失败', { error: (error as Error).message, ip: req.ip });
    
    res.status(401).json({
      error: 'invalid_token',
      message: '无效的访问令牌'
    });
  }
};

/**
 * 可选认证中间件
 * 如果有令牌则验证，没有令牌则继续
 */
export const optionalAuthentication = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1];

    if (!token) {
      // 没有令牌，继续处理
      next();
      return;
    }

    // 有令牌，尝试验证
    await authenticateToken(req, res, next);

  } catch (error) {
    // 验证失败，但不阻止请求继续
    logger.debug('可选认证失败', { error: (error as Error).message });
    next();
  }
};

/**
 * 角色授权中间件
 * 检查用户是否具有指定角色
 */
export const requireRole = (requiredRoles: string | string[]) => {
  return (req: AuthenticatedRequest, res: Response, next: NextFunction): void => {
    if (!req.user) {
      res.status(401).json({
        error: 'unauthorized',
        message: '未授权访问'
      });
      return;
    }

    const userRoles = req.user.roles || [];
    const roles = Array.isArray(requiredRoles) ? requiredRoles : [requiredRoles];

    const hasRequiredRole = roles.some(role => userRoles.includes(role));

    if (!hasRequiredRole) {
      logSecurityEvent('authorization_failed', { 
        requiredRoles: roles, 
        userRoles 
      }, req.user.userId, req.ip);

      res.status(403).json({
        error: 'insufficient_permissions',
        message: '权限不足'
      });
      return;
    }

    next();
  };
};

/**
 * 权限检查中间件
 * 检查用户是否具有指定权限
 */
export const requirePermission = (requiredPermissions: string | string[]) => {
  return async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    if (!req.user) {
      res.status(401).json({
        error: 'unauthorized',
        message: '未授权访问'
      });
      return;
    }

    try {
      // 获取用户的所有权限
      const user = await prisma.user.findUnique({
        where: { id: req.user.userId },
        include: {
          userRoles: {
            include: {
              role: true
            }
          }
        }
      });

      if (!user) {
        res.status(401).json({
          error: 'user_not_found',
          message: '用户不存在'
        });
        return;
      }

      // 收集所有权限
      const userPermissions: string[] = [];
      user.userRoles.forEach(userRole => {
        if (Array.isArray(userRole.role.permissions)) {
          userPermissions.push(...(userRole.role.permissions as string[]));
        }
      });

      const permissions = Array.isArray(requiredPermissions) ? requiredPermissions : [requiredPermissions];
      const hasRequiredPermission = permissions.some(permission => userPermissions.includes(permission));

      if (!hasRequiredPermission) {
        logSecurityEvent('permission_denied', { 
          requiredPermissions: permissions, 
          userPermissions 
        }, req.user.userId, req.ip);

        res.status(403).json({
          error: 'insufficient_permissions',
          message: '权限不足'
        });
        return;
      }

      next();

    } catch (error) {
      logger.error('权限检查失败', { error, userId: req.user.userId });
      
      res.status(500).json({
        error: 'permission_check_failed',
        message: '权限检查失败'
      });
    }
  };
};

/**
 * MFA验证中间件
 * 检查是否需要MFA验证
 */
export const requireMfaVerification = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  if (!req.user || !req.user.sessionId) {
    res.status(401).json({
      error: 'unauthorized',
      message: '未授权访问'
    });
    return;
  }

  try {
    // 检查会话的MFA验证状态
    const session = await prisma.session.findUnique({
      where: { id: req.user.sessionId }
    });

    if (!session) {
      res.status(401).json({
        error: 'invalid_session',
        message: '会话无效'
      });
      return;
    }

    if (!session.mfaVerified) {
      res.status(403).json({
        error: 'mfa_required',
        message: '需要多因素认证'
      });
      return;
    }

    next();

  } catch (error) {
    logger.error('MFA验证检查失败', { error, userId: req.user.userId });
    
    res.status(500).json({
      error: 'mfa_check_failed',
      message: 'MFA验证检查失败'
    });
  }
};

/**
 * 资源所有者检查中间件
 * 检查用户是否为资源的所有者
 */
export const requireResourceOwner = (resourceIdParam: string = 'id') => {
  return (req: AuthenticatedRequest, res: Response, next: NextFunction): void => {
    if (!req.user) {
      res.status(401).json({
        error: 'unauthorized',
        message: '未授权访问'
      });
      return;
    }

    const resourceId = req.params[resourceIdParam];
    const userId = req.user.userId;

    // 如果资源ID就是用户ID，或者用户是管理员，则允许访问
    if (resourceId === userId || req.user.roles?.includes('admin')) {
      next();
      return;
    }

    logSecurityEvent('resource_access_denied', { 
      resourceId, 
      userId 
    }, userId, req.ip);

    res.status(403).json({
      error: 'access_denied',
      message: '访问被拒绝'
    });
  };
};

/**
 * 速率限制中间件
 * 基于用户ID的速率限制
 */
export const rateLimitByUser = (maxRequests: number, windowMs: number) => {
  return async (req: any, res: Response, next: NextFunction): Promise<void> => {
    if (!req.user) {
      next();
      return;
    }

    const userId = req.user.userId;
    const rateLimitKey = `rate:user:${userId}`;

    try {
      // 使用Redis进行速率限制检查
      const result = await cacheService.checkRateLimit(rateLimitKey, maxRequests, windowMs);

      // 设置响应头
      res.set({
        'X-RateLimit-Limit': maxRequests.toString(),
        'X-RateLimit-Remaining': result.remaining.toString(),
        'X-RateLimit-Reset': new Date(result.resetTime).toISOString()
      });

      if (!result.allowed) {
        const retryAfter = Math.ceil((result.resetTime - Date.now()) / 1000);

        logSecurityEvent('user_rate_limit_exceeded', {
          userId,
          maxRequests,
          windowMs
        }, req.ip);

        res.status(429).json({
          error: 'rate_limit_exceeded',
          message: '请求过于频繁，请稍后再试',
          retryAfter
        });
        return;
      }

      next();
    } catch (error) {
      logger.error('用户速率限制检查失败', {
        userId,
        error: error instanceof Error ? error.message : String(error)
      });

      // 出错时允许请求继续
      next();
    }
  };
};


