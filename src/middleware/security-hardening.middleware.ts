/**
 * 安全加固中间件
 * 提供全面的安全防护措施，包括请求验证、攻击防护、安全头部设置等
 */

import { Request, Response, NextFunction } from 'express';
import helmet from 'helmet';
import rateLimit from 'express-rate-limit';
import slowDown from 'express-slow-down';
import { logger } from '@/config/logger';
import { securityAuditService, AuditEventType } from '@/services/security-audit.service';
import { metricsCollector } from '@/services/metrics-collector.service';
import { cacheService } from '@/services/cache.service';
import crypto from 'crypto';

/**
 * 安全配置接口
 */
interface SecurityConfig {
  rateLimit: {
    windowMs: number;
    max: number;
    skipSuccessfulRequests?: boolean;
  };
  slowDown: {
    windowMs: number;
    delayAfter: number;
    delayMs: number;
  };
  bruteForceProtection: {
    maxAttempts: number;
    windowMs: number;
    blockDurationMs: number;
  };
  requestValidation: {
    maxBodySize: string;
    maxHeaderSize: number;
    maxUrlLength: number;
  };
}

/**
 * 默认安全配置
 */
const defaultSecurityConfig: SecurityConfig = {
  rateLimit: {
    windowMs: 15 * 60 * 1000, // 15分钟
    max: 100, // 每个IP最多100个请求
    skipSuccessfulRequests: false
  },
  slowDown: {
    windowMs: 15 * 60 * 1000, // 15分钟
    delayAfter: 50, // 50个请求后开始延迟
    delayMs: 500 // 每个请求延迟500ms
  },
  bruteForceProtection: {
    maxAttempts: 5,
    windowMs: 15 * 60 * 1000, // 15分钟
    blockDurationMs: 60 * 60 * 1000 // 1小时
  },
  requestValidation: {
    maxBodySize: '10mb',
    maxHeaderSize: 8192,
    maxUrlLength: 2048
  }
};

/**
 * 安全头部中间件
 */
export function securityHeaders() {
  return helmet({
    // 内容安全策略
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
        fontSrc: ["'self'", "https://fonts.gstatic.com"],
        imgSrc: ["'self'", "data:", "https:"],
        scriptSrc: ["'self'"],
        connectSrc: ["'self'"],
        frameSrc: ["'none'"],
        objectSrc: ["'none'"],
        mediaSrc: ["'self'"],
        manifestSrc: ["'self'"]
      }
    },
    
    // 跨域嵌入保护
    crossOriginEmbedderPolicy: { policy: "require-corp" },
    
    // 跨域资源策略
    crossOriginResourcePolicy: { policy: "cross-origin" },
    
    // DNS预取控制
    dnsPrefetchControl: { allow: false },
    
    // 下载选项
    ieNoOpen: true,
    
    // MIME类型嗅探保护
    noSniff: true,
    
    // 来源策略
    originAgentCluster: true,
    
    // 权限策略
    permittedCrossDomainPolicies: false,
    
    // 引用者策略
    referrerPolicy: { policy: "no-referrer" },
    
    // 严格传输安全
    hsts: {
      maxAge: 31536000, // 1年
      includeSubDomains: true,
      preload: true
    },
    
    // X-Frame-Options
    frameguard: { action: 'deny' },
    
    // X-XSS-Protection
    xssFilter: true
  });
}

/**
 * 速率限制中间件
 */
export function createRateLimit(config: Partial<SecurityConfig['rateLimit']> = {}) {
  const finalConfig = { ...defaultSecurityConfig.rateLimit, ...config };
  
  return rateLimit({
    windowMs: finalConfig.windowMs,
    max: finalConfig.max,
    skipSuccessfulRequests: finalConfig.skipSuccessfulRequests,
    
    // 自定义消息
    message: {
      success: false,
      error: 'RATE_LIMIT_EXCEEDED',
      message: '请求过于频繁，请稍后重试',
      retryAfter: Math.ceil(finalConfig.windowMs / 1000)
    },
    
    // 自定义头部
    standardHeaders: true,
    legacyHeaders: false,
    
    // 跳过条件
    skip: (req: Request) => {
      // 跳过健康检查端点
      return req.path === '/health' || req.path === '/api/health';
    },
    
    // 键生成器
    keyGenerator: (req: Request) => {
      // 优先使用真实IP
      return req.ip || req.connection.remoteAddress || 'unknown';
    },
    
    // 处理器
    handler: async (req: Request, res: Response) => {
      const ip = req.ip || 'unknown';
      
      // 记录速率限制事件
      await securityAuditService.logAuditEvent({
        eventType: AuditEventType.RATE_LIMIT_EXCEEDED,
        severity: 'medium',
        ipAddress: ip,
        userAgent: req.get('User-Agent'),
        resource: req.path,
        action: 'rate_limit_exceeded',
        details: {
          method: req.method,
          url: req.url,
          headers: req.headers
        },
        success: false,
        errorMessage: '速率限制超出'
      });
      
      // 记录指标
      metricsCollector.incrementCounter('security_rate_limit_exceeded_total', {
        ip_address: ip,
        path: req.path
      });
      
      logger.warn('速率限制触发', {
        ip,
        path: req.path,
        method: req.method,
        userAgent: req.get('User-Agent')
      });
      
      res.status(429).json({
        success: false,
        error: 'RATE_LIMIT_EXCEEDED',
        message: '请求过于频繁，请稍后重试',
        retryAfter: Math.ceil(finalConfig.windowMs / 1000)
      });
    }
  });
}

/**
 * 请求减速中间件
 */
export function createSlowDown(config: Partial<SecurityConfig['slowDown']> = {}) {
  const finalConfig = { ...defaultSecurityConfig.slowDown, ...config };
  
  return slowDown({
    windowMs: finalConfig.windowMs,
    delayAfter: finalConfig.delayAfter,
    delayMs: finalConfig.delayMs,
    
    // 最大延迟
    maxDelayMs: 20000, // 最多延迟20秒
    
    // 跳过条件
    skip: (req: Request) => {
      return req.path === '/health' || req.path === '/api/health';
    },
    
    // 键生成器
    keyGenerator: (req: Request) => {
      return req.ip || req.connection.remoteAddress || 'unknown';
    },
    
    // 延迟处理器
    onLimitReached: async (req: Request, res: Response, options: any) => {
      const ip = req.ip || 'unknown';
      
      logger.warn('请求减速触发', {
        ip,
        path: req.path,
        method: req.method,
        delay: options.delay
      });
      
      metricsCollector.incrementCounter('security_slow_down_triggered_total', {
        ip_address: ip,
        path: req.path
      });
    }
  });
}

/**
 * 暴力破解保护中间件
 */
export function bruteForceProtection(config: Partial<SecurityConfig['bruteForceProtection']> = {}) {
  const finalConfig = { ...defaultSecurityConfig.bruteForceProtection, ...config };
  
  return async (req: Request, res: Response, next: NextFunction) => {
    const ip = req.ip || 'unknown';
    const key = `brute_force:${ip}`;
    
    try {
      // 检查是否被阻止
      const blocked = await cacheService.get(`${key}:blocked`);
      if (blocked) {
        await securityAuditService.logAuditEvent({
          eventType: AuditEventType.BRUTE_FORCE_ATTEMPT,
          severity: 'critical',
          ipAddress: ip,
          userAgent: req.get('User-Agent'),
          resource: req.path,
          action: 'blocked_request',
          details: {
            reason: 'IP blocked due to brute force attempts',
            method: req.method,
            url: req.url
          },
          success: false,
          errorMessage: 'IP地址因暴力破解被阻止'
        });
        
        return res.status(429).json({
          success: false,
          error: 'IP_BLOCKED',
          message: 'IP地址因多次失败尝试被临时阻止',
          retryAfter: Math.ceil(finalConfig.blockDurationMs / 1000)
        });
      }
      
      // 继续处理请求
      next();
      
    } catch (error) {
      logger.error('暴力破解保护检查失败', {
        error: error instanceof Error ? error.message : String(error),
        ip
      });
      next();
    }
  };
}

/**
 * 请求验证中间件
 */
export function requestValidation(config: Partial<SecurityConfig['requestValidation']> = {}) {
  const finalConfig = { ...defaultSecurityConfig.requestValidation, ...config };
  
  return async (req: Request, res: Response, next: NextFunction) => {
    const ip = req.ip || 'unknown';
    const violations: string[] = [];
    
    try {
      // 检查URL长度
      if (req.url.length > finalConfig.maxUrlLength) {
        violations.push(`URL长度超出限制: ${req.url.length} > ${finalConfig.maxUrlLength}`);
      }
      
      // 检查头部大小
      const headerSize = JSON.stringify(req.headers).length;
      if (headerSize > finalConfig.maxHeaderSize) {
        violations.push(`请求头大小超出限制: ${headerSize} > ${finalConfig.maxHeaderSize}`);
      }
      
      // 检查可疑的头部
      const suspiciousHeaders = [
        'x-forwarded-for',
        'x-real-ip',
        'x-cluster-client-ip'
      ];
      
      for (const header of suspiciousHeaders) {
        const value = req.get(header);
        if (value && this.containsSuspiciousContent(value)) {
          violations.push(`可疑的头部内容: ${header}`);
        }
      }
      
      // 检查用户代理
      const userAgent = req.get('User-Agent');
      if (userAgent && this.isSuspiciousUserAgent(userAgent)) {
        violations.push('可疑的用户代理');
      }
      
      // 检查请求方法
      const allowedMethods = ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS', 'HEAD'];
      if (!allowedMethods.includes(req.method)) {
        violations.push(`不允许的HTTP方法: ${req.method}`);
      }
      
      // 如果有违规，记录并拒绝请求
      if (violations.length > 0) {
        await securityAuditService.logSecurityViolation(
          req,
          'request_validation_failed',
          '请求验证失败',
          { violations }
        );
        
        metricsCollector.incrementCounter('security_request_validation_failed_total', {
          ip_address: ip,
          violation_count: violations.length.toString()
        });
        
        return res.status(400).json({
          success: false,
          error: 'REQUEST_VALIDATION_FAILED',
          message: '请求格式不符合安全要求'
        });
      }
      
      next();
      
    } catch (error) {
      logger.error('请求验证失败', {
        error: error instanceof Error ? error.message : String(error),
        ip
      });
      next();
    }
  };
}

/**
 * SQL注入防护中间件
 */
export function sqlInjectionProtection() {
  return async (req: Request, res: Response, next: NextFunction) => {
    const ip = req.ip || 'unknown';
    
    try {
      // SQL注入模式
      const sqlInjectionPatterns = [
        /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION|SCRIPT)\b)/i,
        /(\'|\"|;|--|\*|\||\^|&)/,
        /(\bOR\b|\bAND\b).*(\=|\<|\>)/i,
        /(UNION.*SELECT|SELECT.*FROM|INSERT.*INTO|UPDATE.*SET|DELETE.*FROM)/i
      ];
      
      // 检查查询参数
      const queryString = JSON.stringify(req.query);
      const bodyString = JSON.stringify(req.body);
      const paramsString = JSON.stringify(req.params);
      
      const allInput = `${queryString} ${bodyString} ${paramsString}`;
      
      for (const pattern of sqlInjectionPatterns) {
        if (pattern.test(allInput)) {
          await securityAuditService.logSecurityViolation(
            req,
            'sql_injection_attempt',
            'SQL注入攻击尝试',
            {
              pattern: pattern.toString(),
              input: allInput.substring(0, 500) // 限制日志长度
            }
          );
          
          metricsCollector.incrementCounter('security_sql_injection_attempts_total', {
            ip_address: ip
          });
          
          return res.status(400).json({
            success: false,
            error: 'MALICIOUS_INPUT_DETECTED',
            message: '检测到恶意输入'
          });
        }
      }
      
      next();
      
    } catch (error) {
      logger.error('SQL注入防护检查失败', {
        error: error instanceof Error ? error.message : String(error),
        ip
      });
      next();
    }
  };
}

/**
 * XSS防护中间件
 */
export function xssProtection() {
  return async (req: Request, res: Response, next: NextFunction) => {
    const ip = req.ip || 'unknown';
    
    try {
      // XSS攻击模式
      const xssPatterns = [
        /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
        /<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi,
        /javascript:/gi,
        /on\w+\s*=/gi,
        /<img[^>]+src[^>]*>/gi,
        /<object\b[^<]*(?:(?!<\/object>)<[^<]*)*<\/object>/gi
      ];
      
      // 检查所有输入
      const allInput = JSON.stringify({
        query: req.query,
        body: req.body,
        params: req.params
      });
      
      for (const pattern of xssPatterns) {
        if (pattern.test(allInput)) {
          await securityAuditService.logSecurityViolation(
            req,
            'xss_attempt',
            'XSS攻击尝试',
            {
              pattern: pattern.toString(),
              input: allInput.substring(0, 500)
            }
          );
          
          metricsCollector.incrementCounter('security_xss_attempts_total', {
            ip_address: ip
          });
          
          return res.status(400).json({
            success: false,
            error: 'MALICIOUS_INPUT_DETECTED',
            message: '检测到恶意输入'
          });
        }
      }
      
      next();
      
    } catch (error) {
      logger.error('XSS防护检查失败', {
        error: error instanceof Error ? error.message : String(error),
        ip
      });
      next();
    }
  };
}

/**
 * CSRF保护中间件
 */
export function csrfProtection() {
  return async (req: Request, res: Response, next: NextFunction) => {
    // 跳过GET、HEAD、OPTIONS请求
    if (['GET', 'HEAD', 'OPTIONS'].includes(req.method)) {
      return next();
    }
    
    const ip = req.ip || 'unknown';
    
    try {
      // 检查CSRF令牌
      const token = req.get('X-CSRF-Token') || req.body._csrf || req.query._csrf;
      const sessionToken = req.session?.csrfToken;
      
      if (!token || !sessionToken || token !== sessionToken) {
        await securityAuditService.logSecurityViolation(
          req,
          'csrf_token_mismatch',
          'CSRF令牌不匹配',
          {
            hasToken: !!token,
            hasSessionToken: !!sessionToken,
            tokensMatch: token === sessionToken
          }
        );
        
        metricsCollector.incrementCounter('security_csrf_violations_total', {
          ip_address: ip
        });
        
        return res.status(403).json({
          success: false,
          error: 'CSRF_TOKEN_INVALID',
          message: 'CSRF令牌无效'
        });
      }
      
      next();
      
    } catch (error) {
      logger.error('CSRF保护检查失败', {
        error: error instanceof Error ? error.message : String(error),
        ip
      });
      next();
    }
  };
}

/**
 * 请求大小限制中间件
 */
export function requestSizeLimit(maxSize: number = 10 * 1024 * 1024) { // 默认10MB
  return async (req: Request, res: Response, next: NextFunction) => {
    const contentLength = parseInt(req.get('Content-Length') || '0');
    const ip = req.ip || 'unknown';
    
    if (contentLength > maxSize) {
      await securityAuditService.logSecurityViolation(
        req,
        'request_size_exceeded',
        '请求大小超出限制',
        {
          contentLength,
          maxSize,
          ratio: contentLength / maxSize
        }
      );
      
      metricsCollector.incrementCounter('security_request_size_exceeded_total', {
        ip_address: ip
      });
      
      return res.status(413).json({
        success: false,
        error: 'PAYLOAD_TOO_LARGE',
        message: '请求体过大',
        maxSize
      });
    }
    
    next();
  };
}

/**
 * IP白名单中间件
 */
export function ipWhitelist(allowedIPs: string[]) {
  return async (req: Request, res: Response, next: NextFunction) => {
    const ip = req.ip || 'unknown';
    
    if (!allowedIPs.includes(ip)) {
      await securityAuditService.logSecurityViolation(
        req,
        'ip_not_whitelisted',
        'IP地址不在白名单中',
        { ip, allowedIPs: allowedIPs.length }
      );
      
      metricsCollector.incrementCounter('security_ip_whitelist_violations_total', {
        ip_address: ip
      });
      
      return res.status(403).json({
        success: false,
        error: 'IP_NOT_ALLOWED',
        message: 'IP地址不被允许访问'
      });
    }
    
    next();
  };
}

/**
 * 检查是否包含可疑内容
 */
function containsSuspiciousContent(content: string): boolean {
  const suspiciousPatterns = [
    /\.\./,  // 路径遍历
    /\0/,    // 空字节
    /%00/,   // URL编码的空字节
    /%2e%2e/, // URL编码的..
    /\${/,   // 模板注入
    /{{/     // 模板注入
  ];
  
  return suspiciousPatterns.some(pattern => pattern.test(content));
}

/**
 * 检查是否为可疑的用户代理
 */
function isSuspiciousUserAgent(userAgent: string): boolean {
  const suspiciousPatterns = [
    /bot/i,
    /crawler/i,
    /spider/i,
    /scraper/i,
    /curl/i,
    /wget/i,
    /python/i,
    /java/i,
    /perl/i,
    /php/i
  ];
  
  // 检查是否为空或过短
  if (!userAgent || userAgent.length < 10) {
    return true;
  }
  
  // 检查是否匹配可疑模式
  return suspiciousPatterns.some(pattern => pattern.test(userAgent));
}

// 扩展Express Request接口
declare global {
  namespace Express {
    interface Request {
      csrfToken?: string;
    }
    
    interface Session {
      csrfToken?: string;
    }
  }
}
