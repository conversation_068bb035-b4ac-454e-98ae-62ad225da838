/**
 * 微博OAuth策略
 * 实现微博开放平台的OAuth 2.0认证
 */

import { BaseOAuthStrategy, OAuthConfig, OAuthUserInfo } from './base-oauth.strategy';
import { config } from '@/config';
import { logger } from '@/config/logger';

/**
 * 微博用户信息接口
 */
interface WeiboUserInfo {
  id: number;
  idstr: string;
  screen_name: string;
  name: string;
  province: string;
  city: string;
  location: string;
  description: string;
  url: string;
  profile_image_url: string;
  profile_url: string;
  domain: string;
  weihao: string;
  gender: string;
  followers_count: number;
  friends_count: number;
  statuses_count: number;
  favourites_count: number;
  created_at: string;
  following: boolean;
  allow_all_act_msg: boolean;
  geo_enabled: boolean;
  verified: boolean;
  verified_type: number;
  remark: string;
  email?: string;
}

/**
 * 微博OAuth策略类
 */
export class WeiboStrategy extends BaseOAuthStrategy {
  constructor(verify: Function) {
    const weiboConfig: OAuthConfig = {
      clientId: config.oauth.weibo.clientId!,
      clientSecret: config.oauth.weibo.clientSecret!,
      callbackUrl: config.oauth.weibo.callbackUrl,
      authorizationUrl: 'https://api.weibo.com/oauth2/authorize',
      tokenUrl: 'https://api.weibo.com/oauth2/access_token',
      userInfoUrl: 'https://api.weibo.com/2/users/show.json',
      scope: ['email']
    };

    super('weibo', weiboConfig, verify);
  }

  /**
   * 重写获取用户信息方法，适配微博API
   */
  protected override async getUserInfo(accessToken: string): Promise<OAuthUserInfo> {
    try {
      // 微博需要先获取用户UID
      const uid = await this.getUserUid(accessToken);
      
      if (!uid) {
        throw new Error('无法获取微博用户UID');
      }

      // 使用UID和access_token获取用户信息
      const userInfoUrl = `${this.config.userInfoUrl}?access_token=${accessToken}&uid=${uid}`;
      
      const response = await fetch(userInfoUrl);
      const weiboUser = await response.json() as WeiboUserInfo;

      if ((weiboUser as any).error) {
        throw new Error(`微博API错误: ${(weiboUser as any).error_description}`);
      }

      // 转换为标准格式
      const userInfo: OAuthUserInfo = {
        id: weiboUser.idstr || weiboUser.id.toString(),
        email: weiboUser.email,
        name: weiboUser.screen_name || weiboUser.name,
        avatar: weiboUser.profile_image_url
      };

      logger.info('微博用户信息获取成功', { 
        uid: weiboUser.id,
        screen_name: weiboUser.screen_name 
      });

      return userInfo;

    } catch (error) {
      logger.error('获取微博用户信息失败', { error });
      throw new Error('获取微博用户信息失败');
    }
  }

  /**
   * 获取用户UID
   */
  private async getUserUid(accessToken: string): Promise<string | null> {
    try {
      const uidUrl = `https://api.weibo.com/2/account/get_uid.json?access_token=${accessToken}`;
      
      const response = await fetch(uidUrl);
      const uidData = await response.json() as any;

      if (uidData.error) {
        throw new Error(`获取微博UID失败: ${uidData.error_description}`);
      }

      return uidData.uid;

    } catch (error) {
      logger.error('获取微博UID失败', { error });
      return null;
    }
  }

  /**
   * 重写令牌交换方法，适配微博API
   */
  protected override async exchangeCodeForToken(code: string): Promise<any> {
    try {
      const tokenData = new URLSearchParams({
        client_id: this.config.clientId,
        client_secret: this.config.clientSecret,
        grant_type: 'authorization_code',
        redirect_uri: this.config.callbackUrl,
        code: code
      });

      const response = await fetch(this.config.tokenUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'Accept': 'application/json'
        },
        body: tokenData
      });

      const result = await response.json() as any;

      if (result.error) {
        throw new Error(`微博令牌交换失败: ${result.error_description}`);
      }

      return {
        access_token: result.access_token,
        refresh_token: result.refresh_token,
        expires_in: result.expires_in,
        uid: result.uid
      };

    } catch (error) {
      logger.error('微博令牌交换失败', { error });
      throw new Error('微博令牌交换失败');
    }
  }
}
