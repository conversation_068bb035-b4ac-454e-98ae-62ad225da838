/**
 * 支付宝OAuth策略
 * 实现支付宝开放平台的OAuth 2.0认证
 */

import { BaseOAuthStrategy, OAuthConfig, OAuthUserInfo } from './base-oauth.strategy';
import { config } from '@/config';
import { logger } from '@/config/logger';
import crypto from 'crypto';

/**
 * 支付宝用户信息接口
 */
interface AlipayUserInfo {
  user_id: string;
  nick_name?: string;
  avatar?: string;
  province?: string;
  city?: string;
  gender?: string;
  is_student_certified?: string;
  user_type?: string;
  user_status?: string;
  is_certified?: string;
  is_certify_grade_a?: string;
}

/**
 * 支付宝API响应接口
 */
interface AlipayResponse {
  alipay_user_info_share_response?: {
    code: string;
    msg: string;
    sub_code?: string;
    sub_msg?: string;
    user_id?: string;
    nick_name?: string;
    avatar?: string;
    province?: string;
    city?: string;
    gender?: string;
    is_student_certified?: string;
    user_type?: string;
    user_status?: string;
    is_certified?: string;
    is_certify_grade_a?: string;
  };
  error_response?: {
    code: string;
    msg: string;
    sub_code?: string;
    sub_msg?: string;
  };
  sign?: string;
}

/**
 * 支付宝OAuth策略类
 */
export class AlipayStrategy extends BaseOAuthStrategy {
  constructor(verify: Function) {
    const alipayConfig: OAuthConfig = {
      clientId: config.oauth.alipay.appId!,
      clientSecret: '', // 支付宝使用RSA签名，不使用client_secret
      callbackUrl: config.oauth.alipay.callbackUrl,
      authorizationUrl: 'https://openauth.alipay.com/oauth2/publicAppAuthorize.htm',
      tokenUrl: config.oauth.alipay.gatewayUrl,
      userInfoUrl: config.oauth.alipay.gatewayUrl,
      scope: ['auth_user']
    };

    super('alipay', alipayConfig, verify);
  }

  /**
   * 重写获取用户信息方法，适配支付宝API
   */
  protected override async getUserInfo(accessToken: string): Promise<OAuthUserInfo> {
    try {
      // 构建支付宝API请求参数
      const bizContent = {};
      const params = {
        app_id: this.config.clientId,
        method: 'alipay.user.info.share',
        charset: config.oauth.alipay.charset,
        sign_type: config.oauth.alipay.signType,
        timestamp: new Date().toISOString().replace('T', ' ').substring(0, 19),
        version: '1.0',
        auth_token: accessToken,
        biz_content: JSON.stringify(bizContent)
      };

      // 生成签名
      const sign = this.generateSign(params);
      params['sign'] = sign;

      // 发送请求
      const response = await fetch(this.config.userInfoUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        },
        body: new URLSearchParams(params)
      });

      const alipayResponse = await response.json() as AlipayResponse;

      // 检查响应
      if (alipayResponse.error_response) {
        throw new Error(`支付宝API错误: ${alipayResponse.error_response.sub_msg || alipayResponse.error_response.msg}`);
      }

      const userResponse = alipayResponse.alipay_user_info_share_response;
      if (!userResponse || userResponse.code !== '10000') {
        throw new Error(`支付宝用户信息获取失败: ${userResponse?.sub_msg || userResponse?.msg || '未知错误'}`);
      }

      // 转换为标准格式
      const userInfo: OAuthUserInfo = {
        id: userResponse.user_id!,
        name: userResponse.nick_name,
        avatar: userResponse.avatar
      };

      logger.info('支付宝用户信息获取成功', { 
        userId: userResponse.user_id,
        nickName: userResponse.nick_name 
      });

      return userInfo;

    } catch (error) {
      logger.error('获取支付宝用户信息失败', { error });
      throw new Error('获取支付宝用户信息失败');
    }
  }

  /**
   * 重写令牌交换方法，适配支付宝API
   */
  protected override async exchangeCodeForToken(code: string): Promise<any> {
    try {
      // 构建支付宝令牌交换请求参数
      const bizContent = {
        grant_type: 'authorization_code',
        code: code
      };

      const params = {
        app_id: this.config.clientId,
        method: 'alipay.system.oauth.token',
        charset: config.oauth.alipay.charset,
        sign_type: config.oauth.alipay.signType,
        timestamp: new Date().toISOString().replace('T', ' ').substring(0, 19),
        version: '1.0',
        biz_content: JSON.stringify(bizContent)
      };

      // 生成签名
      const sign = this.generateSign(params);
      params['sign'] = sign;

      // 发送请求
      const response = await fetch(this.config.tokenUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        },
        body: new URLSearchParams(params)
      });

      const tokenResponse = await response.json() as any;

      // 检查响应
      if (tokenResponse.error_response) {
        throw new Error(`支付宝令牌交换失败: ${tokenResponse.error_response.sub_msg || tokenResponse.error_response.msg}`);
      }

      const oauthResponse = tokenResponse.alipay_system_oauth_token_response;
      if (!oauthResponse || oauthResponse.code !== '10000') {
        throw new Error(`支付宝令牌交换失败: ${oauthResponse?.sub_msg || oauthResponse?.msg || '未知错误'}`);
      }

      return {
        access_token: oauthResponse.access_token,
        refresh_token: oauthResponse.refresh_token,
        expires_in: oauthResponse.expires_in,
        user_id: oauthResponse.user_id
      };

    } catch (error) {
      logger.error('支付宝令牌交换失败', { error });
      throw new Error('支付宝令牌交换失败');
    }
  }

  /**
   * 生成支付宝RSA签名
   */
  private generateSign(params: Record<string, any>): string {
    try {
      // 排序参数
      const sortedKeys = Object.keys(params).sort();
      const signString = sortedKeys
        .filter(key => key !== 'sign' && params[key] !== '' && params[key] !== null && params[key] !== undefined)
        .map(key => `${key}=${params[key]}`)
        .join('&');

      // 使用RSA私钥签名
      const privateKey = config.oauth.alipay.privateKey;
      if (!privateKey) {
        throw new Error('支付宝私钥未配置');
      }

      const sign = crypto
        .createSign('RSA-SHA256')
        .update(signString, 'utf8')
        .sign(privateKey, 'base64');

      return sign;

    } catch (error) {
      logger.error('生成支付宝签名失败', { error });
      throw new Error('生成支付宝签名失败');
    }
  }

  /**
   * 重写授权URL生成方法，适配支付宝
   */
  protected override generateAuthorizationUrl(state: string): string {
    const params = new URLSearchParams({
      app_id: this.config.clientId,
      scope: this.config.scope?.join(',') || '',
      redirect_uri: this.config.callbackUrl,
      state
    });

    return `${this.config.authorizationUrl}?${params.toString()}`;
  }
}
