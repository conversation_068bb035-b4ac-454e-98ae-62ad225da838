/**
 * QQ互联OAuth策略
 * 实现QQ互联开放平台的OAuth 2.0认证
 */

import { BaseOAuthStrategy, OAuthConfig, OAuthUserInfo } from './base-oauth.strategy';
import { config } from '@/config';
import { logger } from '@/config/logger';

/**
 * QQ用户信息接口
 */
interface QQUserInfo {
  ret: number;
  msg: string;
  nickname?: string;
  figureurl?: string;
  figureurl_1?: string;
  figureurl_2?: string;
  figureurl_qq_1?: string;
  figureurl_qq_2?: string;
  gender?: string;
  is_yellow_vip?: string;
  vip?: string;
  yellow_vip_level?: string;
  level?: string;
  is_yellow_year_vip?: string;
}

/**
 * QQ OpenID响应接口
 */
interface QQOpenIdResponse {
  client_id: string;
  openid: string;
}

/**
 * QQ互联OAuth策略类
 */
export class QQStrategy extends BaseOAuthStrategy {
  constructor(verify: Function) {
    const qqConfig: OAuthConfig = {
      clientId: config.oauth.qq.clientId!,
      clientSecret: config.oauth.qq.clientSecret!,
      callbackUrl: config.oauth.qq.callbackUrl,
      authorizationUrl: 'https://graph.qq.com/oauth2.0/authorize',
      tokenUrl: 'https://graph.qq.com/oauth2.0/token',
      userInfoUrl: 'https://graph.qq.com/user/get_user_info',
      scope: ['get_user_info']
    };

    super('qq', qqConfig, verify);
  }

  /**
   * 重写获取用户信息方法，适配QQ API
   */
  protected override async getUserInfo(accessToken: string): Promise<OAuthUserInfo> {
    try {
      // QQ需要先获取OpenID
      const openIdInfo = await this.getOpenId(accessToken);
      
      if (!openIdInfo.openid) {
        throw new Error('无法获取QQ用户OpenID');
      }

      // 使用OpenID和access_token获取用户信息
      const userInfoUrl = `${this.config.userInfoUrl}?access_token=${accessToken}&oauth_consumer_key=${this.config.clientId}&openid=${openIdInfo.openid}`;
      
      const response = await fetch(userInfoUrl);
      const qqUser = await response.json() as QQUserInfo;

      if (qqUser.ret !== 0) {
        throw new Error(`QQ API错误: ${qqUser.msg}`);
      }

      // 转换为标准格式
      const userInfo: OAuthUserInfo = {
        id: openIdInfo.openid,
        name: qqUser.nickname,
        // 选择最高质量的头像
        avatar: qqUser.figureurl_qq_2 || qqUser.figureurl_qq_1 || qqUser.figureurl_2 || qqUser.figureurl_1 || qqUser.figureurl
      };

      logger.info('QQ用户信息获取成功', { 
        openid: openIdInfo.openid,
        nickname: qqUser.nickname 
      });

      return userInfo;

    } catch (error) {
      logger.error('获取QQ用户信息失败', { error });
      throw new Error('获取QQ用户信息失败');
    }
  }

  /**
   * 获取QQ OpenID
   */
  private async getOpenId(accessToken: string): Promise<QQOpenIdResponse> {
    try {
      const openIdUrl = `https://graph.qq.com/oauth2.0/me?access_token=${accessToken}`;
      
      const response = await fetch(openIdUrl);
      const responseText = await response.text();

      // QQ返回的是JSONP格式，需要解析
      const jsonpMatch = responseText.match(/callback\(\s*({.*})\s*\)/);
      if (!jsonpMatch) {
        throw new Error('无法解析QQ OpenID响应');
      }

      const openIdData = JSON.parse(jsonpMatch[1]) as QQOpenIdResponse;
      
      if (!openIdData.openid) {
        throw new Error('QQ OpenID响应中缺少openid');
      }

      return openIdData;

    } catch (error) {
      logger.error('获取QQ OpenID失败', { error });
      throw new Error('获取QQ OpenID失败');
    }
  }

  /**
   * 重写令牌交换方法，适配QQ API
   */
  protected override async exchangeCodeForToken(code: string): Promise<any> {
    try {
      const tokenUrl = `${this.config.tokenUrl}?grant_type=authorization_code&client_id=${this.config.clientId}&client_secret=${this.config.clientSecret}&code=${code}&redirect_uri=${encodeURIComponent(this.config.callbackUrl)}`;
      
      const response = await fetch(tokenUrl);
      const responseText = await response.text();

      // QQ返回的是URL编码格式，需要解析
      const params = new URLSearchParams(responseText);
      const accessToken = params.get('access_token');
      const refreshToken = params.get('refresh_token');
      const expiresIn = params.get('expires_in');

      if (!accessToken) {
        // 检查是否有错误信息
        const error = params.get('error');
        const errorDescription = params.get('error_description');
        throw new Error(`QQ令牌交换失败: ${errorDescription || error || '未知错误'}`);
      }

      return {
        access_token: accessToken,
        refresh_token: refreshToken,
        expires_in: expiresIn ? parseInt(expiresIn) : null
      };

    } catch (error) {
      logger.error('QQ令牌交换失败', { error });
      throw new Error('QQ令牌交换失败');
    }
  }

  /**
   * 重写授权URL生成方法，添加QQ特定参数
   */
  protected override generateAuthorizationUrl(state: string): string {
    const params = new URLSearchParams({
      response_type: 'code',
      client_id: this.config.clientId,
      redirect_uri: this.config.callbackUrl,
      scope: this.config.scope?.join(',') || '',
      state
    });

    return `${this.config.authorizationUrl}?${params.toString()}`;
  }
}
