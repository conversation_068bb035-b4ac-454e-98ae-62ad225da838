/**
 * 钉钉OAuth策略
 * 实现钉钉开放平台的OAuth 2.0认证
 */

import { BaseOAuthStrategy, OAuthConfig, OAuthUserInfo } from './base-oauth.strategy';
import { config } from '@/config';
import { logger } from '@/config/logger';

/**
 * 钉钉用户信息接口
 */
interface DingTalkUserInfo {
  nick: string;
  unionid: string;
  openid: string;
  main_org_auth_high_level?: boolean;
}

/**
 * 钉钉用户详细信息接口
 */
interface DingTalkUserDetail {
  userid: string;
  name: string;
  avatar?: string;
  mobile?: string;
  email?: string;
  org_email?: string;
  department?: number[];
  position?: string;
  job_number?: string;
  hide_mobile?: boolean;
  senior?: boolean;
  boss?: boolean;
  leader?: boolean;
  role?: string;
  telephone?: string;
  work_place?: string;
  remark?: string;
  exclusive_account?: boolean;
  exclusive_account_type?: string;
  login_id?: string;
  exclusive_account_corp_id?: string;
  exclusive_account_corp_name?: string;
}

/**
 * 钉钉OAuth策略类
 */
export class DingTalkStrategy extends BaseOAuthStrategy {
  constructor(verify: Function) {
    const dingtalkConfig: OAuthConfig = {
      clientId: config.oauth.dingtalk.clientId!,
      clientSecret: config.oauth.dingtalk.clientSecret!,
      callbackUrl: config.oauth.dingtalk.callbackUrl,
      authorizationUrl: 'https://login.dingtalk.com/oauth2/auth',
      tokenUrl: 'https://api.dingtalk.com/v1.0/oauth2/userAccessToken',
      userInfoUrl: 'https://api.dingtalk.com/v1.0/contact/users/me',
      scope: ['openid', 'corpid']
    };

    super('dingtalk', dingtalkConfig, verify);
  }

  /**
   * 重写获取用户信息方法，适配钉钉API
   */
  protected override async getUserInfo(accessToken: string): Promise<OAuthUserInfo> {
    try {
      // 获取用户基本信息
      const userInfoResponse = await fetch('https://api.dingtalk.com/v1.0/contact/users/me', {
        method: 'GET',
        headers: {
          'x-acs-dingtalk-access-token': accessToken,
          'Content-Type': 'application/json'
        }
      });

      if (!userInfoResponse.ok) {
        throw new Error(`钉钉用户信息请求失败: ${userInfoResponse.status}`);
      }

      const userInfo = await userInfoResponse.json() as DingTalkUserDetail;

      // 转换为标准格式
      const standardUserInfo: OAuthUserInfo = {
        id: userInfo.userid,
        name: userInfo.name,
        avatar: userInfo.avatar,
        email: userInfo.email || userInfo.org_email
      };

      logger.info('钉钉用户信息获取成功', { 
        userId: userInfo.userid,
        name: userInfo.name 
      });

      return standardUserInfo;

    } catch (error) {
      logger.error('获取钉钉用户信息失败', { error });
      throw new Error('获取钉钉用户信息失败');
    }
  }

  /**
   * 重写令牌交换方法，适配钉钉API
   */
  protected override async exchangeCodeForToken(code: string): Promise<any> {
    try {
      // 钉钉令牌交换请求
      const tokenResponse = await fetch(this.config.tokenUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          clientId: this.config.clientId,
          clientSecret: this.config.clientSecret,
          code: code,
          grantType: 'authorization_code'
        })
      });

      if (!tokenResponse.ok) {
        throw new Error(`钉钉令牌交换请求失败: ${tokenResponse.status}`);
      }

      const tokenData = await tokenResponse.json() as any;

      // 检查响应
      if (!tokenData.accessToken) {
        throw new Error(`钉钉令牌交换失败: ${tokenData.message || '未知错误'}`);
      }

      return {
        access_token: tokenData.accessToken,
        refresh_token: tokenData.refreshToken,
        expires_in: tokenData.expireIn,
        corp_id: tokenData.corpId
      };

    } catch (error) {
      logger.error('钉钉令牌交换失败', { error });
      throw new Error('钉钉令牌交换失败');
    }
  }

  /**
   * 重写授权URL生成方法，适配钉钉
   */
  protected override generateAuthorizationUrl(state: string): string {
    const params = new URLSearchParams({
      response_type: 'code',
      client_id: this.config.clientId,
      redirect_uri: this.config.callbackUrl,
      scope: this.config.scope?.join(' ') || '',
      state,
      prompt: 'consent'
    });

    return `${this.config.authorizationUrl}?${params.toString()}`;
  }

  /**
   * 获取企业访问令牌（用于企业内部应用）
   */
  private async getCorpAccessToken(): Promise<string> {
    try {
      const corpId = config.oauth.dingtalk.corpId;
      const agentId = config.oauth.dingtalk.agentId;

      if (!corpId || !agentId) {
        throw new Error('钉钉企业配置不完整');
      }

      const response = await fetch('https://api.dingtalk.com/v1.0/oauth2/corpAccessToken', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          corpId: corpId,
          corpSecret: this.config.clientSecret
        })
      });

      if (!response.ok) {
        throw new Error(`获取企业访问令牌失败: ${response.status}`);
      }

      const tokenData = await response.json() as any;
      
      if (!tokenData.accessToken) {
        throw new Error(`获取企业访问令牌失败: ${tokenData.message || '未知错误'}`);
      }

      return tokenData.accessToken;

    } catch (error) {
      logger.error('获取钉钉企业访问令牌失败', { error });
      throw new Error('获取钉钉企业访问令牌失败');
    }
  }

  /**
   * 通过免登码获取用户信息（企业内部应用）
   */
  async getUserInfoByCode(authCode: string): Promise<OAuthUserInfo> {
    try {
      const corpAccessToken = await this.getCorpAccessToken();

      // 通过免登码获取用户信息
      const response = await fetch('https://api.dingtalk.com/v1.0/contact/users/me', {
        method: 'GET',
        headers: {
          'x-acs-dingtalk-access-token': corpAccessToken,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`钉钉用户信息请求失败: ${response.status}`);
      }

      const userInfo = await response.json() as DingTalkUserDetail;

      return {
        id: userInfo.userid,
        name: userInfo.name,
        avatar: userInfo.avatar,
        email: userInfo.email || userInfo.org_email
      };

    } catch (error) {
      logger.error('通过免登码获取钉钉用户信息失败', { error });
      throw new Error('通过免登码获取钉钉用户信息失败');
    }
  }
}
