/**
 * 基础OAuth策略
 * 为自定义OAuth提供商提供通用的OAuth 2.0实现
 */

import { Strategy } from 'passport-strategy';
import { Request } from 'express';
import { logger } from '@/config/logger';
import { OAuthSecurity } from '@/utils/oauth-security';
import { OAuthStateService } from '@/services/oauth-state.service';

/**
 * OAuth配置接口
 */
export interface OAuthConfig {
  clientId: string;
  clientSecret: string;
  callbackUrl: string;
  authorizationUrl: string;
  tokenUrl: string;
  userInfoUrl: string;
  scope?: string[];
}

/**
 * OAuth用户信息接口
 */
export interface OAuthUserInfo {
  id: string;
  email?: string;
  name?: string;
  avatar?: string;
  [key: string]: any;
}

/**
 * 基础OAuth策略类
 */
export class BaseOAuthStrategy extends Strategy {
  name: string;
  protected config: OAuthConfig;
  protected stateService: OAuthStateService;

  constructor(name: string, config: OAuthConfig, verify: Function) {
    super();
    this.name = name;
    this.config = config;
    this._verify = verify;
    this.stateService = new OAuthStateService();
  }

  /**
   * 认证方法
   */
  override authenticate(req: Request, options?: any): void {
    // 记录OAuth尝试
    const ipAddress = req.ip || req.connection.remoteAddress || 'unknown';
    const userAgent = req.get('User-Agent') || 'unknown';
    this.stateService.recordOAuthAttempt(this.name, ipAddress, userAgent);

    if (req.query.error) {
      // OAuth错误处理
      const error = new Error(`OAuth认证失败: ${req.query.error_description || req.query.error}`);
      OAuthSecurity.logSecurityEvent('oauth_error_received', {
        provider: this.name,
        error: req.query.error,
        errorDescription: req.query.error_description,
        ipAddress,
        userAgent
      }, 'medium');
      return this.fail(error.message, 400);
    }

    if (req.query.code) {
      // 处理回调，交换授权码获取访问令牌
      this.handleCallback(req);
    } else {
      // 重定向到授权页面
      this.redirectToAuthorizationUrl(req);
    }
  }

  /**
   * 重定向到授权URL
   */
  private async redirectToAuthorizationUrl(req: Request): Promise<void> {
    try {
      // 创建安全的OAuth状态
      const oauthState = OAuthSecurity.createOAuthState(this.name);

      // 存储状态到Redis
      await this.stateService.storeState(oauthState);

      // 生成授权URL
      const authUrl = this.generateAuthorizationUrl(oauthState.state);

      logger.info('重定向到OAuth授权页面', {
        provider: this.name,
        state: oauthState.state,
        url: authUrl
      });

      this.redirect(authUrl);
    } catch (error) {
      logger.error('重定向到授权URL失败', { error, provider: this.name });
      this.fail('OAuth认证初始化失败', 500);
    }
  }

  /**
   * 生成授权URL（可被子类重写）
   */
  protected generateAuthorizationUrl(state: string): string {
    const params = new URLSearchParams({
      client_id: this.config.clientId,
      redirect_uri: this.config.callbackUrl,
      response_type: 'code',
      state,
      ...(this.config.scope && { scope: this.config.scope.join(' ') })
    });

    return `${this.config.authorizationUrl}?${params.toString()}`;
  }

  /**
   * 处理OAuth回调
   */
  private async handleCallback(req: Request): Promise<void> {
    try {
      const { code, state } = req.query;

      if (!code) {
        OAuthSecurity.logSecurityEvent('missing_authorization_code', {
          provider: this.name,
          query: req.query
        }, 'high');
        return this.fail('缺少授权码', 400);
      }

      if (!state) {
        OAuthSecurity.logSecurityEvent('missing_state_parameter', {
          provider: this.name,
          query: req.query
        }, 'high');
        return this.fail('缺少状态参数', 400);
      }

      // 验证状态参数
      const stateValidation = await this.stateService.validateAndConsumeState(state as string);

      if (!stateValidation.isValid) {
        OAuthSecurity.logSecurityEvent('state_validation_failed', {
          provider: this.name,
          reason: stateValidation.reason,
          receivedState: state
        }, 'high');
        return this.fail(`状态验证失败: ${stateValidation.reason}`, 400);
      }

      // 交换访问令牌
      const tokenResponse = await this.exchangeCodeForToken(code as string);

      if (!tokenResponse.access_token) {
        OAuthSecurity.logSecurityEvent('token_exchange_failed', {
          provider: this.name,
          hasCode: !!code
        }, 'medium');
        return this.fail('获取访问令牌失败', 400);
      }

      // 获取用户信息
      const userInfo = await this.getUserInfo(tokenResponse.access_token);

      logger.info('OAuth认证成功', {
        provider: this.name,
        userId: userInfo.id,
        state: state
      });

      // 调用验证回调
      this._verify(
        tokenResponse.access_token,
        tokenResponse.refresh_token,
        userInfo,
        (err: any, user: any) => {
          if (err) {
            logger.error('OAuth用户验证失败', { error: err, provider: this.name });
            return this.error(err);
          }
          if (!user) {
            logger.warn('OAuth用户验证被拒绝', { provider: this.name });
            return this.fail('用户验证失败', 400);
          }
          return this.success(user);
        }
      );

    } catch (error) {
      logger.error(`${this.name} OAuth回调处理失败`, { error });
      OAuthSecurity.logSecurityEvent('oauth_callback_error', {
        provider: this.name,
        error: error.message
      }, 'high');
      this.error(error);
    }
  }

  /**
   * 交换授权码获取访问令牌
   */
  protected async exchangeCodeForToken(code: string): Promise<any> {
    try {
      const tokenData = new URLSearchParams({
        client_id: this.config.clientId,
        client_secret: this.config.clientSecret,
        code,
        redirect_uri: this.config.callbackUrl,
        grant_type: 'authorization_code'
      });

      const response = await fetch(this.config.tokenUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'Accept': 'application/json'
        },
        body: tokenData
      });

      const result = await response.json();
      return result;
    } catch (error) {
      logger.error(`${this.name} 令牌交换失败`, { error });
      throw new Error('令牌交换失败');
    }
  }

  /**
   * 获取用户信息
   */
  protected async getUserInfo(accessToken: string): Promise<OAuthUserInfo> {
    try {
      const response = await fetch(this.config.userInfoUrl, {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Accept': 'application/json'
        }
      });

      const result = await response.json() as any;

      // 确保返回的对象符合OAuthUserInfo接口
      return {
        id: result.id || result.sub || result.user_id || 'unknown',
        email: result.email || '',
        name: result.name || result.display_name || result.nickname || '',
        avatar: result.avatar || result.picture || result.avatar_url || '',
        provider: this.name,
        accessToken,
        refreshToken: undefined
      };
    } catch (error) {
      logger.error(`${this.name} 获取用户信息失败`, { error });
      throw new Error('获取用户信息失败');
    }
  }

  /**
   * 生成状态参数
   */
  private generateState(): string {
    return Math.random().toString(36).substring(2, 15) + 
           Math.random().toString(36).substring(2, 15);
  }

  /**
   * 验证回调函数
   */
  private _verify: Function;
}
