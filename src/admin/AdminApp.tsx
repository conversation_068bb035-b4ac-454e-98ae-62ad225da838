/**
 * React Admin 管理应用主组件
 * 提供完整的管理员Web界面，包括用户管理、应用管理、权限管理等功能
 */

import React from 'react';
import {
  Admin,
  Resource,
  CustomRoutes,
  Layout,
  AppBar,
  Menu,
  Title,
  defaultTheme,
  defaultDarkTheme
} from 'react-admin';
import { Route } from 'react-router-dom';
import { Card, CardContent, Typography, Box, Grid } from '@mui/material';
import {
  People as UsersIcon,
  Apps as ApplicationsIcon,
  Security as PermissionsIcon,
  Assignment as RequestsIcon,
  History as AuditIcon,
  Dashboard as DashboardIcon,
  Settings as SettingsIcon
} from '@mui/icons-material';

import { adminDataProviderService } from '@/services/admin-data-provider.service';
import { AdminAuthProvider } from './providers/AdminAuthProvider';

// 资源组件导入
import {
  UserList,
  UserEdit,
  UserCreate,
  UserShow
} from './resources/users';

import {
  ApplicationList,
  ApplicationEdit,
  ApplicationCreate,
  ApplicationShow
} from './resources/applications';

import {
  PermissionList,
  PermissionEdit,
  PermissionCreate,
  PermissionShow
} from './resources/permissions';

import {
  PermissionRequestList,
  PermissionRequestEdit,
  PermissionRequestShow
} from './resources/permission-requests';

import {
  AuditLogList,
  AuditLogShow
} from './resources/audit-logs';

import { SystemSettings } from './pages/SystemSettings';

/**
 * 管理员仪表板组件
 */
const Dashboard = () => (
  <Box sx={{ p: 3 }}>
    <Title title="管理员控制台" />
    
    <Typography variant="h4" gutterBottom>
      欢迎使用身份提供商管理控制台
    </Typography>
    
    <Typography variant="body1" color="text.secondary" paragraph>
      这里是您的管理中心，您可以管理用户、应用程序、权限和系统配置。
    </Typography>

    <Grid container spacing={3} sx={{ mt: 2 }}>
      {/* 用户统计卡片 */}
      <Grid item xs={12} sm={6} md={3}>
        <Card>
          <CardContent>
            <Box display="flex" alignItems="center">
              <UsersIcon color="primary" sx={{ mr: 2, fontSize: 40 }} />
              <Box>
                <Typography variant="h6">用户管理</Typography>
                <Typography variant="body2" color="text.secondary">
                  管理系统用户和用户资料
                </Typography>
              </Box>
            </Box>
          </CardContent>
        </Card>
      </Grid>

      {/* 应用统计卡片 */}
      <Grid item xs={12} sm={6} md={3}>
        <Card>
          <CardContent>
            <Box display="flex" alignItems="center">
              <ApplicationsIcon color="primary" sx={{ mr: 2, fontSize: 40 }} />
              <Box>
                <Typography variant="h6">应用管理</Typography>
                <Typography variant="body2" color="text.secondary">
                  管理客户端应用和配置
                </Typography>
              </Box>
            </Box>
          </CardContent>
        </Card>
      </Grid>

      {/* 权限统计卡片 */}
      <Grid item xs={12} sm={6} md={3}>
        <Card>
          <CardContent>
            <Box display="flex" alignItems="center">
              <PermissionsIcon color="primary" sx={{ mr: 2, fontSize: 40 }} />
              <Box>
                <Typography variant="h6">权限管理</Typography>
                <Typography variant="body2" color="text.secondary">
                  管理权限和访问控制
                </Typography>
              </Box>
            </Box>
          </CardContent>
        </Card>
      </Grid>

      {/* 审计统计卡片 */}
      <Grid item xs={12} sm={6} md={3}>
        <Card>
          <CardContent>
            <Box display="flex" alignItems="center">
              <AuditIcon color="primary" sx={{ mr: 2, fontSize: 40 }} />
              <Box>
                <Typography variant="h6">审计日志</Typography>
                <Typography variant="body2" color="text.secondary">
                  查看系统操作记录
                </Typography>
              </Box>
            </Box>
          </CardContent>
        </Card>
      </Grid>
    </Grid>

    {/* 快速操作区域 */}
    <Box sx={{ mt: 4 }}>
      <Typography variant="h5" gutterBottom>
        快速操作
      </Typography>
      
      <Grid container spacing={2}>
        <Grid item xs={12} sm={6} md={4}>
          <Card sx={{ cursor: 'pointer', '&:hover': { elevation: 4 } }}>
            <CardContent>
              <Typography variant="h6">创建新用户</Typography>
              <Typography variant="body2" color="text.secondary">
                快速添加新的系统用户
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={4}>
          <Card sx={{ cursor: 'pointer', '&:hover': { elevation: 4 } }}>
            <CardContent>
              <Typography variant="h6">注册新应用</Typography>
              <Typography variant="body2" color="text.secondary">
                注册新的客户端应用
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={4}>
          <Card sx={{ cursor: 'pointer', '&:hover': { elevation: 4 } }}>
            <CardContent>
              <Typography variant="h6">查看系统状态</Typography>
              <Typography variant="body2" color="text.secondary">
                检查系统健康状况
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  </Box>
);

/**
 * 自定义应用栏
 */
const CustomAppBar = () => (
  <AppBar>
    <Typography variant="h6" color="inherit" sx={{ flex: 1 }}>
      身份提供商管理控制台
    </Typography>
  </AppBar>
);

/**
 * 自定义菜单
 */
const CustomMenu = () => (
  <Menu>
    <Menu.DashboardItem />
    <Menu.ResourceItem name="users" />
    <Menu.ResourceItem name="applications" />
    <Menu.ResourceItem name="permissions" />
    <Menu.ResourceItem name="permission-requests" />
    <Menu.ResourceItem name="audit-logs" />
    <Menu.Item to="/settings" primaryText="系统设置" leftIcon={<SettingsIcon />} />
  </Menu>
);

/**
 * 自定义布局
 */
const CustomLayout = ({ children }: { children: React.ReactNode }) => (
  <Layout appBar={CustomAppBar} menu={CustomMenu}>
    {children}
  </Layout>
);

/**
 * 主题配置
 */
const lightTheme = {
  ...defaultTheme,
  palette: {
    ...defaultTheme.palette,
    primary: {
      main: '#1976d2',
    },
    secondary: {
      main: '#dc004e',
    },
  },
};

const darkTheme = {
  ...defaultDarkTheme,
  palette: {
    ...defaultDarkTheme.palette,
    primary: {
      main: '#90caf9',
    },
    secondary: {
      main: '#f48fb1',
    },
  },
};

/**
 * React Admin 主应用组件
 */
export const AdminApp: React.FC = () => {
  return (
    <Admin
      dataProvider={adminDataProviderService}
      authProvider={AdminAuthProvider}
      dashboard={Dashboard}
      layout={CustomLayout}
      theme={lightTheme}
      darkTheme={darkTheme}
      title="身份提供商管理控制台"
    >
      {/* 用户资源 */}
      <Resource
        name="users"
        list={UserList}
        edit={UserEdit}
        create={UserCreate}
        show={UserShow}
        icon={UsersIcon}
        options={{ label: '用户管理' }}
      />

      {/* 应用资源 */}
      <Resource
        name="applications"
        list={ApplicationList}
        edit={ApplicationEdit}
        create={ApplicationCreate}
        show={ApplicationShow}
        icon={ApplicationsIcon}
        options={{ label: '应用管理' }}
      />

      {/* 权限资源 */}
      <Resource
        name="permissions"
        list={PermissionList}
        edit={PermissionEdit}
        create={PermissionCreate}
        show={PermissionShow}
        icon={PermissionsIcon}
        options={{ label: '权限管理' }}
      />

      {/* 权限申请资源 */}
      <Resource
        name="permission-requests"
        list={PermissionRequestList}
        edit={PermissionRequestEdit}
        show={PermissionRequestShow}
        icon={RequestsIcon}
        options={{ label: '权限申请' }}
      />

      {/* 审计日志资源 */}
      <Resource
        name="audit-logs"
        list={AuditLogList}
        show={AuditLogShow}
        icon={AuditIcon}
        options={{ label: '审计日志' }}
      />

      {/* 自定义路由 */}
      <CustomRoutes>
        <Route path="/settings" element={<SystemSettings />} />
      </CustomRoutes>
    </Admin>
  );
};

export default AdminApp;
