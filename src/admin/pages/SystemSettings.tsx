/**
 * 系统设置页面
 * 提供动态系统配置管理界面，支持在线修改系统参数
 */

import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  Typography,
  Box,
  Grid,
  TextField,
  Switch,
  FormControlLabel,
  Button,
  Tabs,
  Tab,
  Alert,
  Snackbar,
  Divider,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions
} from '@mui/material';
import {
  Save as SaveIcon,
  Refresh as RefreshIcon,
  Security as SecurityIcon,
  Storage as StorageIcon,
  Email as EmailIcon,
  Notifications as NotificationsIcon,
  Settings as SettingsIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Add as AddIcon
} from '@mui/icons-material';
import { Title } from 'react-admin';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`settings-tabpanel-${index}`}
      aria-labelledby={`settings-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

export const SystemSettings: React.FC = () => {
  const [tabValue, setTabValue] = useState(0);
  const [settings, setSettings] = useState<any>({});
  const [loading, setLoading] = useState(false);
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' as any });
  const [editDialog, setEditDialog] = useState({ open: false, key: '', value: '', title: '' });

  // 模拟加载系统设置
  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    setLoading(true);
    try {
      // 这里应该调用实际的API
      const mockSettings = {
        general: {
          siteName: '身份提供商',
          siteDescription: '企业级身份认证和权限管理平台',
          adminEmail: '<EMAIL>',
          supportEmail: '<EMAIL>',
          maintenanceMode: false,
          debugMode: false,
          logLevel: 'info'
        },
        security: {
          sessionTimeout: 3600,
          maxLoginAttempts: 5,
          lockoutDuration: 900,
          passwordMinLength: 8,
          passwordRequireSpecialChars: true,
          passwordRequireNumbers: true,
          passwordRequireUppercase: true,
          mfaRequired: false,
          mfaGracePeriod: 7,
          jwtExpirationTime: 900,
          refreshTokenExpirationTime: 604800
        },
        database: {
          connectionPoolSize: 10,
          queryTimeout: 30000,
          enableQueryLogging: false,
          backupEnabled: true,
          backupInterval: 24,
          retentionDays: 30
        },
        email: {
          smtpHost: 'smtp.example.com',
          smtpPort: 587,
          smtpSecure: true,
          smtpUser: '<EMAIL>',
          fromName: '身份提供商',
          fromEmail: '<EMAIL>',
          enableEmailVerification: true,
          emailTemplateLanguage: 'zh-CN'
        },
        notifications: {
          enablePushNotifications: true,
          enableEmailNotifications: true,
          enableSmsNotifications: false,
          notificationRetentionDays: 90,
          criticalAlertsEmail: '<EMAIL>',
          webhookUrl: '',
          slackWebhookUrl: ''
        }
      };
      setSettings(mockSettings);
    } catch (error) {
      setSnackbar({ open: true, message: '加载设置失败', severity: 'error' });
    } finally {
      setLoading(false);
    }
  };

  const saveSettings = async () => {
    setLoading(true);
    try {
      // 这里应该调用实际的API保存设置
      console.log('保存设置:', settings);
      setSnackbar({ open: true, message: '设置保存成功', severity: 'success' });
    } catch (error) {
      setSnackbar({ open: true, message: '保存设置失败', severity: 'error' });
    } finally {
      setLoading(false);
    }
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handleSettingChange = (category: string, key: string, value: any) => {
    setSettings((prev: any) => ({
      ...prev,
      [category]: {
        ...prev[category],
        [key]: value
      }
    }));
  };

  const handleEditSetting = (category: string, key: string, title: string) => {
    const value = settings[category]?.[key] || '';
    setEditDialog({
      open: true,
      key: `${category}.${key}`,
      value: String(value),
      title
    });
  };

  const handleSaveEdit = () => {
    const [category, key] = editDialog.key.split('.');
    handleSettingChange(category, key, editDialog.value);
    setEditDialog({ open: false, key: '', value: '', title: '' });
  };

  return (
    <Box>
      <Title title="系统设置" />
      
      <Typography variant="h4" gutterBottom>
        系统设置
      </Typography>
      
      <Typography variant="body1" color="text.secondary" paragraph>
        管理系统的各项配置参数，修改后需要保存才能生效。
      </Typography>

      <Card>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={tabValue} onChange={handleTabChange}>
            <Tab icon={<SettingsIcon />} label="常规设置" />
            <Tab icon={<SecurityIcon />} label="安全设置" />
            <Tab icon={<StorageIcon />} label="数据库设置" />
            <Tab icon={<EmailIcon />} label="邮件设置" />
            <Tab icon={<NotificationsIcon />} label="通知设置" />
          </Tabs>
        </Box>

        {/* 常规设置 */}
        <TabPanel value={tabValue} index={0}>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="站点名称"
                value={settings.general?.siteName || ''}
                onChange={(e) => handleSettingChange('general', 'siteName', e.target.value)}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="管理员邮箱"
                value={settings.general?.adminEmail || ''}
                onChange={(e) => handleSettingChange('general', 'adminEmail', e.target.value)}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                multiline
                rows={3}
                label="站点描述"
                value={settings.general?.siteDescription || ''}
                onChange={(e) => handleSettingChange('general', 'siteDescription', e.target.value)}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControlLabel
                control={
                  <Switch
                    checked={settings.general?.maintenanceMode || false}
                    onChange={(e) => handleSettingChange('general', 'maintenanceMode', e.target.checked)}
                  />
                }
                label="维护模式"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControlLabel
                control={
                  <Switch
                    checked={settings.general?.debugMode || false}
                    onChange={(e) => handleSettingChange('general', 'debugMode', e.target.checked)}
                  />
                }
                label="调试模式"
              />
            </Grid>
          </Grid>
        </TabPanel>

        {/* 安全设置 */}
        <TabPanel value={tabValue} index={1}>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                type="number"
                label="会话超时时间（秒）"
                value={settings.security?.sessionTimeout || ''}
                onChange={(e) => handleSettingChange('security', 'sessionTimeout', parseInt(e.target.value))}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                type="number"
                label="最大登录尝试次数"
                value={settings.security?.maxLoginAttempts || ''}
                onChange={(e) => handleSettingChange('security', 'maxLoginAttempts', parseInt(e.target.value))}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                type="number"
                label="账户锁定时间（秒）"
                value={settings.security?.lockoutDuration || ''}
                onChange={(e) => handleSettingChange('security', 'lockoutDuration', parseInt(e.target.value))}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                type="number"
                label="密码最小长度"
                value={settings.security?.passwordMinLength || ''}
                onChange={(e) => handleSettingChange('security', 'passwordMinLength', parseInt(e.target.value))}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControlLabel
                control={
                  <Switch
                    checked={settings.security?.mfaRequired || false}
                    onChange={(e) => handleSettingChange('security', 'mfaRequired', e.target.checked)}
                  />
                }
                label="强制多因素认证"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControlLabel
                control={
                  <Switch
                    checked={settings.security?.passwordRequireSpecialChars || false}
                    onChange={(e) => handleSettingChange('security', 'passwordRequireSpecialChars', e.target.checked)}
                  />
                }
                label="密码需要特殊字符"
              />
            </Grid>
          </Grid>
        </TabPanel>

        {/* 数据库设置 */}
        <TabPanel value={tabValue} index={2}>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                type="number"
                label="连接池大小"
                value={settings.database?.connectionPoolSize || ''}
                onChange={(e) => handleSettingChange('database', 'connectionPoolSize', parseInt(e.target.value))}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                type="number"
                label="查询超时时间（毫秒）"
                value={settings.database?.queryTimeout || ''}
                onChange={(e) => handleSettingChange('database', 'queryTimeout', parseInt(e.target.value))}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControlLabel
                control={
                  <Switch
                    checked={settings.database?.enableQueryLogging || false}
                    onChange={(e) => handleSettingChange('database', 'enableQueryLogging', e.target.checked)}
                  />
                }
                label="启用查询日志"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControlLabel
                control={
                  <Switch
                    checked={settings.database?.backupEnabled || false}
                    onChange={(e) => handleSettingChange('database', 'backupEnabled', e.target.checked)}
                  />
                }
                label="启用自动备份"
              />
            </Grid>
          </Grid>
        </TabPanel>

        {/* 邮件设置 */}
        <TabPanel value={tabValue} index={3}>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="SMTP主机"
                value={settings.email?.smtpHost || ''}
                onChange={(e) => handleSettingChange('email', 'smtpHost', e.target.value)}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                type="number"
                label="SMTP端口"
                value={settings.email?.smtpPort || ''}
                onChange={(e) => handleSettingChange('email', 'smtpPort', parseInt(e.target.value))}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="SMTP用户名"
                value={settings.email?.smtpUser || ''}
                onChange={(e) => handleSettingChange('email', 'smtpUser', e.target.value)}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="发件人邮箱"
                value={settings.email?.fromEmail || ''}
                onChange={(e) => handleSettingChange('email', 'fromEmail', e.target.value)}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControlLabel
                control={
                  <Switch
                    checked={settings.email?.smtpSecure || false}
                    onChange={(e) => handleSettingChange('email', 'smtpSecure', e.target.checked)}
                  />
                }
                label="启用SSL/TLS"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControlLabel
                control={
                  <Switch
                    checked={settings.email?.enableEmailVerification || false}
                    onChange={(e) => handleSettingChange('email', 'enableEmailVerification', e.target.checked)}
                  />
                }
                label="启用邮箱验证"
              />
            </Grid>
          </Grid>
        </TabPanel>

        {/* 通知设置 */}
        <TabPanel value={tabValue} index={4}>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <FormControlLabel
                control={
                  <Switch
                    checked={settings.notifications?.enablePushNotifications || false}
                    onChange={(e) => handleSettingChange('notifications', 'enablePushNotifications', e.target.checked)}
                  />
                }
                label="启用推送通知"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControlLabel
                control={
                  <Switch
                    checked={settings.notifications?.enableEmailNotifications || false}
                    onChange={(e) => handleSettingChange('notifications', 'enableEmailNotifications', e.target.checked)}
                  />
                }
                label="启用邮件通知"
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="关键告警邮箱"
                value={settings.notifications?.criticalAlertsEmail || ''}
                onChange={(e) => handleSettingChange('notifications', 'criticalAlertsEmail', e.target.value)}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Webhook URL"
                value={settings.notifications?.webhookUrl || ''}
                onChange={(e) => handleSettingChange('notifications', 'webhookUrl', e.target.value)}
              />
            </Grid>
          </Grid>
        </TabPanel>

        <Divider />
        
        <CardContent>
          <Box display="flex" justifyContent="space-between" alignItems="center">
            <Box>
              <Typography variant="body2" color="text.secondary">
                最后更新：2024年1月15日 14:30
              </Typography>
            </Box>
            <Box display="flex" gap={2}>
              <Button
                variant="outlined"
                startIcon={<RefreshIcon />}
                onClick={loadSettings}
                disabled={loading}
              >
                重新加载
              </Button>
              <Button
                variant="contained"
                startIcon={<SaveIcon />}
                onClick={saveSettings}
                disabled={loading}
              >
                保存设置
              </Button>
            </Box>
          </Box>
        </CardContent>
      </Card>

      {/* 编辑对话框 */}
      <Dialog open={editDialog.open} onClose={() => setEditDialog({ ...editDialog, open: false })} maxWidth="sm" fullWidth>
        <DialogTitle>{editDialog.title}</DialogTitle>
        <DialogContent>
          <TextField
            fullWidth
            multiline
            rows={4}
            value={editDialog.value}
            onChange={(e) => setEditDialog({ ...editDialog, value: e.target.value })}
            margin="normal"
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setEditDialog({ ...editDialog, open: false })}>取消</Button>
          <Button onClick={handleSaveEdit} variant="contained">保存</Button>
        </DialogActions>
      </Dialog>

      {/* 通知栏 */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
      >
        <Alert severity={snackbar.severity} onClose={() => setSnackbar({ ...snackbar, open: false })}>
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};
