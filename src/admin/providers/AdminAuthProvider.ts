/**
 * React Admin 认证提供者
 * 处理管理员登录、登出和权限验证
 */

import { AuthProvider } from 'react-admin';
import { getServerUrl } from '@/config';

/**
 * 管理员认证提供者实现
 */
export const AdminAuthProvider: AuthProvider = {
  /**
   * 登录方法
   */
  login: async ({ username, password }) => {
    try {
      const response = await fetch(`${getServerUrl()}/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          username,
          password,
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || '登录失败');
      }

      const data = await response.json();
      
      // 检查用户是否有管理员权限
      if (!data.user.isAdmin && !data.user.permissions?.includes('admin.access')) {
        throw new Error('您没有管理员权限');
      }

      // 存储认证信息
      localStorage.setItem('admin_token', data.accessToken);
      localStorage.setItem('admin_user', JSON.stringify(data.user));
      localStorage.setItem('admin_refresh_token', data.refreshToken);

      return Promise.resolve();
    } catch (error) {
      return Promise.reject(error);
    }
  },

  /**
   * 登出方法
   */
  logout: async () => {
    try {
      const token = localStorage.getItem('admin_token');
      
      if (token) {
        // 调用服务器登出接口
        await fetch(`${getServerUrl()}/auth/logout`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        });
      }

      // 清除本地存储
      localStorage.removeItem('admin_token');
      localStorage.removeItem('admin_user');
      localStorage.removeItem('admin_refresh_token');

      return Promise.resolve();
    } catch (error) {
      // 即使服务器登出失败，也要清除本地存储
      localStorage.removeItem('admin_token');
      localStorage.removeItem('admin_user');
      localStorage.removeItem('admin_refresh_token');
      
      return Promise.resolve();
    }
  },

  /**
   * 检查认证状态
   */
  checkAuth: async () => {
    try {
      const token = localStorage.getItem('admin_token');
      
      if (!token) {
        return Promise.reject(new Error('未登录'));
      }

      // 验证令牌有效性
      const response = await fetch(`${getServerUrl()}/auth/verify`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        // 令牌无效，尝试刷新
        const refreshToken = localStorage.getItem('admin_refresh_token');
        if (refreshToken) {
          const refreshResponse = await fetch(`${getServerUrl()}/auth/refresh`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              refreshToken,
            }),
          });

          if (refreshResponse.ok) {
            const refreshData = await refreshResponse.json();
            localStorage.setItem('admin_token', refreshData.accessToken);
            localStorage.setItem('admin_refresh_token', refreshData.refreshToken);
            return Promise.resolve();
          }
        }

        // 刷新失败，清除存储
        localStorage.removeItem('admin_token');
        localStorage.removeItem('admin_user');
        localStorage.removeItem('admin_refresh_token');
        
        return Promise.reject(new Error('认证已过期'));
      }

      const data = await response.json();
      
      // 检查管理员权限
      if (!data.user.isAdmin && !data.user.permissions?.includes('admin.access')) {
        return Promise.reject(new Error('权限不足'));
      }

      return Promise.resolve();
    } catch (error) {
      return Promise.reject(error);
    }
  },

  /**
   * 检查错误类型
   */
  checkError: async (error: any) => {
    const status = error.status;
    
    if (status === 401 || status === 403) {
      // 认证或权限错误，清除存储并重定向到登录
      localStorage.removeItem('admin_token');
      localStorage.removeItem('admin_user');
      localStorage.removeItem('admin_refresh_token');
      
      return Promise.reject();
    }
    
    return Promise.resolve();
  },

  /**
   * 获取权限
   */
  getPermissions: async () => {
    try {
      const userStr = localStorage.getItem('admin_user');
      
      if (!userStr) {
        return Promise.reject(new Error('用户信息不存在'));
      }

      const user = JSON.parse(userStr);
      
      // 返回用户权限列表
      return Promise.resolve(user.permissions || []);
    } catch (error) {
      return Promise.reject(error);
    }
  },

  /**
   * 获取用户身份信息
   */
  getIdentity: async () => {
    try {
      const userStr = localStorage.getItem('admin_user');
      
      if (!userStr) {
        return Promise.reject(new Error('用户信息不存在'));
      }

      const user = JSON.parse(userStr);
      
      return Promise.resolve({
        id: user.id,
        fullName: user.profile?.displayName || user.username,
        avatar: user.profile?.avatar,
        email: user.email,
      });
    } catch (error) {
      return Promise.reject(error);
    }
  },
};

/**
 * HTTP 客户端配置
 * 为所有请求添加认证头
 */
export const httpClient = (url: string, options: any = {}) => {
  const token = localStorage.getItem('admin_token');
  
  if (token) {
    options.headers = {
      ...options.headers,
      'Authorization': `Bearer ${token}`,
    };
  }

  return fetch(url, options);
};
