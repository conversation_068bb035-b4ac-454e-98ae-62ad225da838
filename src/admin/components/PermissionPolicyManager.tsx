/**
 * 权限策略管理组件
 * 提供权限策略的创建、编辑和管理功能
 */

import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  Typography,
  Box,
  Grid,
  TextField,
  Button,
  Switch,
  FormControlLabel,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Paper,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  Snackbar,
  Chip,
  IconButton,
  Tooltip,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  Divider,
  FormControl,
  InputLabel,
  Select,
  MenuItem
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  ExpandMore as ExpandMoreIcon,
  Security as SecurityIcon,
  Policy as PolicyIcon,
  Rule as RuleIcon,
  Visibility as ViewIcon,
  Save as SaveIcon,
  Cancel as CancelIcon
} from '@mui/icons-material';

interface PermissionPolicy {
  id: string;
  name: string;
  description: string;
  version: string;
  isActive: boolean;
  rules: PermissionRule[];
  priority: number;
  createdAt: string;
  updatedAt: string;
}

interface PermissionRule {
  id: string;
  name: string;
  description: string;
  subjects: string[];
  resources: ResourceSelector[];
  actions: string[];
  effect: 'allow' | 'deny';
  conditions?: PermissionCondition[];
  priority: number;
}

interface ResourceSelector {
  type: string;
  pattern: string;
  attributes?: Record<string, any>;
}

interface PermissionCondition {
  type: 'time' | 'location' | 'attribute' | 'context';
  operator: string;
  field: string;
  value: any;
  description?: string;
}

export const PermissionPolicyManager: React.FC = () => {
  const [policies, setPolicies] = useState<PermissionPolicy[]>([]);
  const [loading, setLoading] = useState(true);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [totalCount, setTotalCount] = useState(0);
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' as any });
  
  // 对话框状态
  const [policyDialog, setPolicyDialog] = useState({ open: false, policy: null as PermissionPolicy | null, mode: 'create' as 'create' | 'edit' | 'view' });
  const [deleteDialog, setDeleteDialog] = useState({ open: false, policyId: '' });

  // 表单状态
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    version: '1.0.0',
    isActive: true,
    priority: 100,
    rules: [] as PermissionRule[]
  });

  // 加载权限策略列表
  const loadPolicies = async () => {
    try {
      setLoading(true);
      
      // 这里应该调用实际的API
      // const response = await fetch('/fine-grained-permission/policies');
      // const data = await response.json();
      
      // 模拟数据
      const mockPolicies: PermissionPolicy[] = [
        {
          id: '1',
          name: '管理员完全访问策略',
          description: '为管理员提供所有资源的完全访问权限',
          version: '1.0.0',
          isActive: true,
          priority: 1000,
          rules: [
            {
              id: 'rule1',
              name: '管理员全权限',
              description: '管理员对所有资源的所有操作权限',
              subjects: ['admin_role'],
              resources: [{ type: 'all', pattern: '*' }],
              actions: ['create', 'read', 'update', 'delete', 'execute'],
              effect: 'allow',
              priority: 1000
            }
          ],
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        },
        {
          id: '2',
          name: '普通用户只读策略',
          description: '为普通用户提供基本的只读权限',
          version: '1.0.0',
          isActive: true,
          priority: 100,
          rules: [
            {
              id: 'rule2',
              name: '用户只读权限',
              description: '普通用户的只读权限',
              subjects: ['user_role'],
              resources: [{ type: 'user', pattern: 'self' }],
              actions: ['read'],
              effect: 'allow',
              priority: 100
            }
          ],
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }
      ];

      setPolicies(mockPolicies);
      setTotalCount(mockPolicies.length);

    } catch (error) {
      setSnackbar({ open: true, message: '加载权限策略失败', severity: 'error' });
    } finally {
      setLoading(false);
    }
  };

  // 保存权限策略
  const savePolicy = async () => {
    try {
      if (policyDialog.mode === 'create') {
        // 创建新策略
        const newPolicy: PermissionPolicy = {
          id: Date.now().toString(),
          ...formData,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };
        
        setPolicies(prev => [...prev, newPolicy]);
        setSnackbar({ open: true, message: '权限策略创建成功', severity: 'success' });
      } else if (policyDialog.mode === 'edit') {
        // 更新策略
        setPolicies(prev => prev.map(p => 
          p.id === policyDialog.policy?.id 
            ? { ...p, ...formData, updatedAt: new Date().toISOString() }
            : p
        ));
        setSnackbar({ open: true, message: '权限策略更新成功', severity: 'success' });
      }

      setPolicyDialog({ open: false, policy: null, mode: 'create' });
      resetForm();

    } catch (error) {
      setSnackbar({ open: true, message: '保存权限策略失败', severity: 'error' });
    }
  };

  // 删除权限策略
  const deletePolicy = async () => {
    try {
      setPolicies(prev => prev.filter(p => p.id !== deleteDialog.policyId));
      setSnackbar({ open: true, message: '权限策略删除成功', severity: 'success' });
      setDeleteDialog({ open: false, policyId: '' });

    } catch (error) {
      setSnackbar({ open: true, message: '删除权限策略失败', severity: 'error' });
    }
  };

  // 重置表单
  const resetForm = () => {
    setFormData({
      name: '',
      description: '',
      version: '1.0.0',
      isActive: true,
      priority: 100,
      rules: []
    });
  };

  // 打开策略对话框
  const openPolicyDialog = (mode: 'create' | 'edit' | 'view', policy?: PermissionPolicy) => {
    if (policy) {
      setFormData({
        name: policy.name,
        description: policy.description,
        version: policy.version,
        isActive: policy.isActive,
        priority: policy.priority,
        rules: policy.rules
      });
    } else {
      resetForm();
    }
    
    setPolicyDialog({ open: true, policy: policy || null, mode });
  };

  // 获取效果颜色
  const getEffectColor = (effect: string) => {
    return effect === 'allow' ? 'success' : 'error';
  };

  // 初始加载
  useEffect(() => {
    loadPolicies();
  }, [page, rowsPerPage]);

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4">
          权限策略管理
        </Typography>
        
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => openPolicyDialog('create')}
        >
          创建策略
        </Button>
      </Box>

      <Typography variant="body1" color="text.secondary" paragraph>
        管理细粒度权限策略，定义用户和角色对资源的访问规则。
      </Typography>

      {/* 策略列表 */}
      <Card>
        <CardContent>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>策略名称</TableCell>
                  <TableCell>版本</TableCell>
                  <TableCell>优先级</TableCell>
                  <TableCell>状态</TableCell>
                  <TableCell>规则数量</TableCell>
                  <TableCell>更新时间</TableCell>
                  <TableCell>操作</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {policies.map((policy) => (
                  <TableRow key={policy.id}>
                    <TableCell>
                      <Box>
                        <Typography variant="subtitle2" fontWeight="medium">
                          {policy.name}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {policy.description}
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Chip label={policy.version} size="small" />
                    </TableCell>
                    <TableCell>{policy.priority}</TableCell>
                    <TableCell>
                      <Chip 
                        label={policy.isActive ? '启用' : '禁用'}
                        color={policy.isActive ? 'success' : 'default'}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>{policy.rules.length}</TableCell>
                    <TableCell>
                      {new Date(policy.updatedAt).toLocaleString('zh-CN')}
                    </TableCell>
                    <TableCell>
                      <Box display="flex" gap={1}>
                        <Tooltip title="查看">
                          <IconButton 
                            size="small"
                            onClick={() => openPolicyDialog('view', policy)}
                          >
                            <ViewIcon />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="编辑">
                          <IconButton 
                            size="small"
                            onClick={() => openPolicyDialog('edit', policy)}
                          >
                            <EditIcon />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="删除">
                          <IconButton 
                            size="small"
                            color="error"
                            onClick={() => setDeleteDialog({ open: true, policyId: policy.id })}
                          >
                            <DeleteIcon />
                          </IconButton>
                        </Tooltip>
                      </Box>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
          
          <TablePagination
            component="div"
            count={totalCount}
            page={page}
            onPageChange={(e, newPage) => setPage(newPage)}
            rowsPerPage={rowsPerPage}
            onRowsPerPageChange={(e) => {
              setRowsPerPage(parseInt(e.target.value, 10));
              setPage(0);
            }}
            labelRowsPerPage="每页行数:"
            labelDisplayedRows={({ from, to, count }) => `${from}-${to} 共 ${count} 条`}
          />
        </CardContent>
      </Card>

      {/* 策略编辑对话框 */}
      <Dialog 
        open={policyDialog.open} 
        onClose={() => setPolicyDialog({ open: false, policy: null, mode: 'create' })}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          {policyDialog.mode === 'create' ? '创建权限策略' : 
           policyDialog.mode === 'edit' ? '编辑权限策略' : '查看权限策略'}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="策略名称"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                disabled={policyDialog.mode === 'view'}
              />
            </Grid>
            
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="版本"
                value={formData.version}
                onChange={(e) => setFormData({ ...formData, version: e.target.value })}
                disabled={policyDialog.mode === 'view'}
              />
            </Grid>
            
            <Grid item xs={12}>
              <TextField
                fullWidth
                multiline
                rows={3}
                label="策略描述"
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                disabled={policyDialog.mode === 'view'}
              />
            </Grid>
            
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                type="number"
                label="优先级"
                value={formData.priority}
                onChange={(e) => setFormData({ ...formData, priority: parseInt(e.target.value) })}
                disabled={policyDialog.mode === 'view'}
                helperText="数值越大优先级越高"
              />
            </Grid>
            
            <Grid item xs={12} md={6}>
              <FormControlLabel
                control={
                  <Switch
                    checked={formData.isActive}
                    onChange={(e) => setFormData({ ...formData, isActive: e.target.checked })}
                    disabled={policyDialog.mode === 'view'}
                  />
                }
                label="启用策略"
              />
            </Grid>
            
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>
                权限规则
              </Typography>
              
              {formData.rules.map((rule, index) => (
                <Accordion key={rule.id} sx={{ mb: 1 }}>
                  <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                    <Box display="flex" alignItems="center" gap={2}>
                      <RuleIcon />
                      <Typography variant="subtitle1">{rule.name}</Typography>
                      <Chip 
                        label={rule.effect}
                        color={getEffectColor(rule.effect)}
                        size="small"
                      />
                    </Box>
                  </AccordionSummary>
                  <AccordionDetails>
                    <Grid container spacing={2}>
                      <Grid item xs={12}>
                        <Typography variant="body2" color="text.secondary">
                          {rule.description}
                        </Typography>
                      </Grid>
                      <Grid item xs={12} md={4}>
                        <Typography variant="subtitle2">主体</Typography>
                        <List dense>
                          {rule.subjects.map((subject, idx) => (
                            <ListItem key={idx}>
                              <ListItemText primary={subject} />
                            </ListItem>
                          ))}
                        </List>
                      </Grid>
                      <Grid item xs={12} md={4}>
                        <Typography variant="subtitle2">操作</Typography>
                        <Box display="flex" flexWrap="wrap" gap={0.5}>
                          {rule.actions.map((action, idx) => (
                            <Chip key={idx} label={action} size="small" variant="outlined" />
                          ))}
                        </Box>
                      </Grid>
                      <Grid item xs={12} md={4}>
                        <Typography variant="subtitle2">资源</Typography>
                        <List dense>
                          {rule.resources.map((resource, idx) => (
                            <ListItem key={idx}>
                              <ListItemText 
                                primary={`${resource.type}: ${resource.pattern}`}
                              />
                            </ListItem>
                          ))}
                        </List>
                      </Grid>
                    </Grid>
                  </AccordionDetails>
                </Accordion>
              ))}
              
              {formData.rules.length === 0 && (
                <Alert severity="info">
                  暂无权限规则，请添加规则来定义权限策略。
                </Alert>
              )}
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setPolicyDialog({ open: false, policy: null, mode: 'create' })}>
            取消
          </Button>
          {policyDialog.mode !== 'view' && (
            <Button onClick={savePolicy} variant="contained">
              保存
            </Button>
          )}
        </DialogActions>
      </Dialog>

      {/* 删除确认对话框 */}
      <Dialog open={deleteDialog.open} onClose={() => setDeleteDialog({ open: false, policyId: '' })}>
        <DialogTitle>确认删除</DialogTitle>
        <DialogContent>
          <Typography>
            确定要删除这个权限策略吗？此操作不可撤销。
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialog({ open: false, policyId: '' })}>取消</Button>
          <Button onClick={deletePolicy} color="error" variant="contained">删除</Button>
        </DialogActions>
      </Dialog>

      {/* 通知栏 */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
      >
        <Alert severity={snackbar.severity} onClose={() => setSnackbar({ ...snackbar, open: false })}>
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};
