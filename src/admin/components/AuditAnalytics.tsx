/**
 * 审计日志分析组件
 * 提供高级审计日志查询、分析和可视化功能
 */

import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  Typography,
  Box,
  Grid,
  TextField,
  Button,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Paper,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  Snackbar,
  Tabs,
  Tab,
  LinearProgress,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  Search as SearchIcon,
  Download as DownloadIcon,
  FilterList as FilterIcon,
  Timeline as TimelineIcon,
  Warning as WarningIcon,
  Security as SecurityIcon,
  Refresh as RefreshIcon,
  DateRange as DateRangeIcon
} from '@mui/icons-material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { zhCN } from '@mui/x-date-pickers/locales';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`audit-tabpanel-${index}`}
      aria-labelledby={`audit-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

export const AuditAnalytics: React.FC = () => {
  const [tabValue, setTabValue] = useState(0);
  const [loading, setLoading] = useState(false);
  const [auditLogs, setAuditLogs] = useState<any[]>([]);
  const [statistics, setStatistics] = useState<any>({});
  const [anomalies, setAnomalies] = useState<any[]>([]);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(25);
  const [totalCount, setTotalCount] = useState(0);
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' as any });
  const [exportDialog, setExportDialog] = useState(false);

  // 查询参数
  const [filters, setFilters] = useState({
    searchText: '',
    action: '',
    resource: '',
    level: '',
    userId: '',
    startDate: null as Date | null,
    endDate: null as Date | null,
    riskScoreMin: '',
    riskScoreMax: '',
    hasAnomalies: false
  });

  // 加载审计日志
  const loadAuditLogs = async () => {
    setLoading(true);
    try {
      const params = new URLSearchParams();
      params.append('page', (page + 1).toString());
      params.append('pageSize', rowsPerPage.toString());
      
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== '' && value !== null && value !== false) {
          if (key === 'startDate' || key === 'endDate') {
            params.append(key, (value as Date).toISOString());
          } else {
            params.append(key, value.toString());
          }
        }
      });

      const response = await fetch(`/audit/logs?${params.toString()}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('admin_token')}`
        }
      });

      if (!response.ok) {
        throw new Error('获取审计日志失败');
      }

      const data = await response.json();
      setAuditLogs(data.data);
      setTotalCount(data.pagination.total);

    } catch (error) {
      setSnackbar({ open: true, message: '加载审计日志失败', severity: 'error' });
    } finally {
      setLoading(false);
    }
  };

  // 加载统计信息
  const loadStatistics = async () => {
    try {
      const params = new URLSearchParams();
      if (filters.startDate) {
        params.append('startDate', filters.startDate.toISOString());
      }
      if (filters.endDate) {
        params.append('endDate', filters.endDate.toISOString());
      }

      const response = await fetch(`/audit/statistics?${params.toString()}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('admin_token')}`
        }
      });

      if (!response.ok) {
        throw new Error('获取统计信息失败');
      }

      const data = await response.json();
      setStatistics(data.statistics);

    } catch (error) {
      setSnackbar({ open: true, message: '加载统计信息失败', severity: 'error' });
    }
  };

  // 加载异常检测
  const loadAnomalies = async () => {
    try {
      const params = new URLSearchParams();
      if (filters.startDate) {
        params.append('startDate', filters.startDate.toISOString());
      }
      if (filters.endDate) {
        params.append('endDate', filters.endDate.toISOString());
      }

      const response = await fetch(`/audit/anomalies?${params.toString()}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('admin_token')}`
        }
      });

      if (!response.ok) {
        throw new Error('获取异常检测失败');
      }

      const data = await response.json();
      setAnomalies(data.anomalies);

    } catch (error) {
      setSnackbar({ open: true, message: '加载异常检测失败', severity: 'error' });
    }
  };

  // 导出审计日志
  const exportAuditLogs = async (format: string) => {
    try {
      const params = new URLSearchParams();
      params.append('format', format);
      
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== '' && value !== null && value !== false) {
          if (key === 'startDate' || key === 'endDate') {
            params.append(key, (value as Date).toISOString());
          } else {
            params.append(key, value.toString());
          }
        }
      });

      const response = await fetch(`/audit/export?${params.toString()}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('admin_token')}`
        }
      });

      if (!response.ok) {
        throw new Error('导出失败');
      }

      // 下载文件
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `audit-logs-${new Date().toISOString().slice(0, 10)}.${format}`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      setSnackbar({ open: true, message: '导出成功', severity: 'success' });
      setExportDialog(false);

    } catch (error) {
      setSnackbar({ open: true, message: '导出失败', severity: 'error' });
    }
  };

  // 初始加载
  useEffect(() => {
    loadAuditLogs();
    loadStatistics();
    loadAnomalies();
  }, [page, rowsPerPage]);

  // 处理搜索
  const handleSearch = () => {
    setPage(0);
    loadAuditLogs();
    loadStatistics();
    loadAnomalies();
  };

  // 处理重置
  const handleReset = () => {
    setFilters({
      searchText: '',
      action: '',
      resource: '',
      level: '',
      userId: '',
      startDate: null,
      endDate: null,
      riskScoreMin: '',
      riskScoreMax: '',
      hasAnomalies: false
    });
    setPage(0);
  };

  // 获取级别颜色
  const getLevelColor = (level: string) => {
    switch (level) {
      case 'error':
      case 'critical':
        return 'error';
      case 'warning':
        return 'warning';
      case 'info':
        return 'info';
      default:
        return 'default';
    }
  };

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns} localeText={zhCN.components.MuiLocalizationProvider.defaultProps.localeText}>
      <Box>
        <Typography variant="h4" gutterBottom>
          审计日志分析
        </Typography>
        
        <Typography variant="body1" color="text.secondary" paragraph>
          查询和分析系统审计日志，监控安全事件和异常行为。
        </Typography>

        {/* 过滤器 */}
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              查询条件
            </Typography>
            
            <Grid container spacing={2}>
              <Grid item xs={12} md={4}>
                <TextField
                  fullWidth
                  label="搜索文本"
                  value={filters.searchText}
                  onChange={(e) => setFilters({ ...filters, searchText: e.target.value })}
                  placeholder="搜索描述、用户代理等"
                  InputProps={{
                    startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />
                  }}
                />
              </Grid>
              
              <Grid item xs={12} md={2}>
                <FormControl fullWidth>
                  <InputLabel>操作类型</InputLabel>
                  <Select
                    value={filters.action}
                    onChange={(e) => setFilters({ ...filters, action: e.target.value })}
                  >
                    <MenuItem value="">全部</MenuItem>
                    <MenuItem value="login">登录</MenuItem>
                    <MenuItem value="logout">登出</MenuItem>
                    <MenuItem value="create">创建</MenuItem>
                    <MenuItem value="update">更新</MenuItem>
                    <MenuItem value="delete">删除</MenuItem>
                    <MenuItem value="permission_grant">权限授予</MenuItem>
                    <MenuItem value="permission_revoke">权限撤销</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              
              <Grid item xs={12} md={2}>
                <FormControl fullWidth>
                  <InputLabel>资源类型</InputLabel>
                  <Select
                    value={filters.resource}
                    onChange={(e) => setFilters({ ...filters, resource: e.target.value })}
                  >
                    <MenuItem value="">全部</MenuItem>
                    <MenuItem value="user">用户</MenuItem>
                    <MenuItem value="application">应用</MenuItem>
                    <MenuItem value="permission">权限</MenuItem>
                    <MenuItem value="system">系统</MenuItem>
                    <MenuItem value="auth">认证</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              
              <Grid item xs={12} md={2}>
                <FormControl fullWidth>
                  <InputLabel>日志级别</InputLabel>
                  <Select
                    value={filters.level}
                    onChange={(e) => setFilters({ ...filters, level: e.target.value })}
                  >
                    <MenuItem value="">全部</MenuItem>
                    <MenuItem value="info">信息</MenuItem>
                    <MenuItem value="warning">警告</MenuItem>
                    <MenuItem value="error">错误</MenuItem>
                    <MenuItem value="critical">严重</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              
              <Grid item xs={12} md={2}>
                <Button
                  fullWidth
                  variant="contained"
                  onClick={handleSearch}
                  disabled={loading}
                  startIcon={<SearchIcon />}
                >
                  查询
                </Button>
              </Grid>
              
              <Grid item xs={12} md={3}>
                <DatePicker
                  label="开始时间"
                  value={filters.startDate}
                  onChange={(date) => setFilters({ ...filters, startDate: date })}
                  slotProps={{ textField: { fullWidth: true } }}
                />
              </Grid>
              
              <Grid item xs={12} md={3}>
                <DatePicker
                  label="结束时间"
                  value={filters.endDate}
                  onChange={(date) => setFilters({ ...filters, endDate: date })}
                  slotProps={{ textField: { fullWidth: true } }}
                />
              </Grid>
              
              <Grid item xs={12} md={3}>
                <Button
                  fullWidth
                  variant="outlined"
                  onClick={handleReset}
                  startIcon={<RefreshIcon />}
                >
                  重置
                </Button>
              </Grid>
              
              <Grid item xs={12} md={3}>
                <Button
                  fullWidth
                  variant="outlined"
                  onClick={() => setExportDialog(true)}
                  startIcon={<DownloadIcon />}
                >
                  导出
                </Button>
              </Grid>
            </Grid>
          </CardContent>
        </Card>

        {/* 标签页 */}
        <Card>
          <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
            <Tabs value={tabValue} onChange={(e, v) => setTabValue(v)}>
              <Tab icon={<SecurityIcon />} label="审计日志" />
              <Tab icon={<TimelineIcon />} label="统计分析" />
              <Tab icon={<WarningIcon />} label="异常检测" />
            </Tabs>
          </Box>

          {loading && <LinearProgress />}

          {/* 审计日志列表 */}
          <TabPanel value={tabValue} index={0}>
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>时间</TableCell>
                    <TableCell>用户</TableCell>
                    <TableCell>操作</TableCell>
                    <TableCell>资源</TableCell>
                    <TableCell>级别</TableCell>
                    <TableCell>IP地址</TableCell>
                    <TableCell>描述</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {auditLogs.map((log) => (
                    <TableRow key={log.id}>
                      <TableCell>
                        {new Date(log.createdAt).toLocaleString('zh-CN')}
                      </TableCell>
                      <TableCell>
                        {log.user?.username || '系统'}
                      </TableCell>
                      <TableCell>
                        <Chip label={log.action} size="small" />
                      </TableCell>
                      <TableCell>{log.resource || '-'}</TableCell>
                      <TableCell>
                        <Chip 
                          label={log.level || 'info'} 
                          size="small" 
                          color={getLevelColor(log.level)}
                        />
                      </TableCell>
                      <TableCell>{log.ipAddress || '-'}</TableCell>
                      <TableCell>
                        <Tooltip title={log.description || ''}>
                          <Typography variant="body2" noWrap sx={{ maxWidth: 200 }}>
                            {log.description || '-'}
                          </Typography>
                        </Tooltip>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
            
            <TablePagination
              component="div"
              count={totalCount}
              page={page}
              onPageChange={(e, newPage) => setPage(newPage)}
              rowsPerPage={rowsPerPage}
              onRowsPerPageChange={(e) => {
                setRowsPerPage(parseInt(e.target.value, 10));
                setPage(0);
              }}
              labelRowsPerPage="每页行数:"
              labelDisplayedRows={({ from, to, count }) => `${from}-${to} 共 ${count} 条`}
            />
          </TabPanel>

          {/* 统计分析 */}
          <TabPanel value={tabValue} index={1}>
            <Grid container spacing={3}>
              <Grid item xs={12} md={3}>
                <Card>
                  <CardContent>
                    <Typography variant="h6">总日志数</Typography>
                    <Typography variant="h4" color="primary">
                      {statistics.overview?.totalLogs || 0}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
              
              <Grid item xs={12} md={3}>
                <Card>
                  <CardContent>
                    <Typography variant="h6">活跃用户</Typography>
                    <Typography variant="h4" color="primary">
                      {statistics.overview?.uniqueUsers || 0}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
              
              <Grid item xs={12} md={3}>
                <Card>
                  <CardContent>
                    <Typography variant="h6">操作类型</Typography>
                    <Typography variant="h4" color="primary">
                      {statistics.overview?.uniqueActions || 0}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
              
              <Grid item xs={12} md={3}>
                <Card>
                  <CardContent>
                    <Typography variant="h6">风险事件</Typography>
                    <Typography variant="h4" color="error">
                      {statistics.overview?.riskEvents || 0}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          </TabPanel>

          {/* 异常检测 */}
          <TabPanel value={tabValue} index={2}>
            {anomalies.length === 0 ? (
              <Alert severity="success">
                未检测到异常行为
              </Alert>
            ) : (
              <Grid container spacing={2}>
                {anomalies.map((anomaly, index) => (
                  <Grid item xs={12} key={index}>
                    <Alert severity="warning">
                      <Typography variant="subtitle1">
                        {anomaly.title}
                      </Typography>
                      <Typography variant="body2">
                        {anomaly.description}
                      </Typography>
                    </Alert>
                  </Grid>
                ))}
              </Grid>
            )}
          </TabPanel>
        </Card>

        {/* 导出对话框 */}
        <Dialog open={exportDialog} onClose={() => setExportDialog(false)}>
          <DialogTitle>导出审计日志</DialogTitle>
          <DialogContent>
            <Typography variant="body2" sx={{ mb: 2 }}>
              选择导出格式：
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={4}>
                <Button
                  fullWidth
                  variant="outlined"
                  onClick={() => exportAuditLogs('csv')}
                >
                  CSV
                </Button>
              </Grid>
              <Grid item xs={4}>
                <Button
                  fullWidth
                  variant="outlined"
                  onClick={() => exportAuditLogs('xlsx')}
                >
                  Excel
                </Button>
              </Grid>
              <Grid item xs={4}>
                <Button
                  fullWidth
                  variant="outlined"
                  onClick={() => exportAuditLogs('json')}
                >
                  JSON
                </Button>
              </Grid>
            </Grid>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setExportDialog(false)}>取消</Button>
          </DialogActions>
        </Dialog>

        {/* 通知栏 */}
        <Snackbar
          open={snackbar.open}
          autoHideDuration={6000}
          onClose={() => setSnackbar({ ...snackbar, open: false })}
        >
          <Alert severity={snackbar.severity} onClose={() => setSnackbar({ ...snackbar, open: false })}>
            {snackbar.message}
          </Alert>
        </Snackbar>
      </Box>
    </LocalizationProvider>
  );
};
