/**
 * 实时监控仪表板组件
 * 提供系统实时状态监控和告警管理界面
 */

import React, { useState, useEffect, useRef } from 'react';
import {
  Card,
  CardContent,
  Typography,
  Box,
  Grid,
  LinearProgress,
  Chip,
  Alert,
  AlertTitle,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  IconButton,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Snackbar,
  Switch,
  FormControlLabel,
  Tooltip,
  CircularProgress
} from '@mui/material';
import {
  Memory as MemoryIcon,
  Speed as CpuIcon,
  Storage as DatabaseIcon,
  People as UsersIcon,
  Security as SecurityIcon,
  Warning as WarningIcon,
  Error as ErrorIcon,
  Info as InfoIcon,
  CheckCircle as CheckIcon,
  Refresh as RefreshIcon,
  PlayArrow as StartIcon,
  Stop as StopIcon,
  Notifications as NotificationIcon
} from '@mui/icons-material';

interface MonitorMetrics {
  timestamp: string;
  system: {
    uptime: number;
    memory: {
      heapUsed: number;
      heapTotal: number;
      external: number;
      arrayBuffers: number;
    };
    cpu: number;
    loadAverage: number[];
  };
  database: {
    connections: number;
    queryTime: number;
    errorRate: number;
  };
  authentication: {
    activeUsers: number;
    loginRate: number;
    failureRate: number;
  };
  permissions: {
    requestsPerMinute: number;
    approvalRate: number;
    pendingRequests: number;
  };
  security: {
    riskEvents: number;
    anomalies: number;
    blockedIPs: number;
  };
}

interface Alert {
  id: string;
  level: 'info' | 'warning' | 'error' | 'critical';
  title: string;
  message: string;
  source: string;
  timestamp: string;
  acknowledged: boolean;
}

export const RealTimeMonitor: React.FC = () => {
  const [metrics, setMetrics] = useState<MonitorMetrics | null>(null);
  const [alerts, setAlerts] = useState<Alert[]>([]);
  const [isConnected, setIsConnected] = useState(false);
  const [isMonitoringEnabled, setIsMonitoringEnabled] = useState(false);
  const [loading, setLoading] = useState(true);
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' as any });
  const [alertDialog, setAlertDialog] = useState({ open: false, alert: null as Alert | null });
  
  const wsRef = useRef<WebSocket | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // 初始化WebSocket连接
  const connectWebSocket = () => {
    try {
      const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
      const wsUrl = `${protocol}//${window.location.host}/ws/monitor`;
      
      wsRef.current = new WebSocket(wsUrl);

      wsRef.current.onopen = () => {
        setIsConnected(true);
        setSnackbar({ open: true, message: '实时监控已连接', severity: 'success' });
        
        // 清除重连定时器
        if (reconnectTimeoutRef.current) {
          clearTimeout(reconnectTimeoutRef.current);
          reconnectTimeoutRef.current = null;
        }
      };

      wsRef.current.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          
          switch (data.type) {
            case 'metrics':
              setMetrics(data.data);
              break;
            case 'alert':
              setAlerts(prev => [data.data, ...prev.slice(0, 49)]); // 保持最新50个告警
              break;
            case 'alertAcknowledged':
              setAlerts(prev => prev.map(alert => 
                alert.id === data.data.id 
                  ? { ...alert, acknowledged: true }
                  : alert
              ));
              break;
            case 'alerts':
              setAlerts(data.data);
              break;
          }
        } catch (error) {
          console.error('解析WebSocket消息失败:', error);
        }
      };

      wsRef.current.onclose = () => {
        setIsConnected(false);
        
        // 5秒后尝试重连
        reconnectTimeoutRef.current = setTimeout(() => {
          connectWebSocket();
        }, 5000);
      };

      wsRef.current.onerror = (error) => {
        console.error('WebSocket错误:', error);
        setIsConnected(false);
      };

    } catch (error) {
      console.error('创建WebSocket连接失败:', error);
      setIsConnected(false);
    }
  };

  // 加载初始数据
  const loadInitialData = async () => {
    try {
      setLoading(true);

      // 获取监控状态
      const statusResponse = await fetch('/monitor/status', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('admin_token')}`
        }
      });

      if (statusResponse.ok) {
        const statusData = await statusResponse.json();
        setIsMonitoringEnabled(statusData.status.isRunning);
      }

      // 获取当前指标
      const metricsResponse = await fetch('/monitor/metrics/current', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('admin_token')}`
        }
      });

      if (metricsResponse.ok) {
        const metricsData = await metricsResponse.json();
        setMetrics(metricsData.metrics);
      }

      // 获取活跃告警
      const alertsResponse = await fetch('/monitor/alerts?active=true', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('admin_token')}`
        }
      });

      if (alertsResponse.ok) {
        const alertsData = await alertsResponse.json();
        setAlerts(alertsData.alerts);
      }

    } catch (error) {
      setSnackbar({ open: true, message: '加载监控数据失败', severity: 'error' });
    } finally {
      setLoading(false);
    }
  };

  // 切换监控服务
  const toggleMonitoring = async () => {
    try {
      const endpoint = isMonitoringEnabled ? '/monitor/stop' : '/monitor/start';
      
      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('admin_token')}`
        }
      });

      if (response.ok) {
        setIsMonitoringEnabled(!isMonitoringEnabled);
        setSnackbar({ 
          open: true, 
          message: `监控服务已${isMonitoringEnabled ? '停止' : '启动'}`, 
          severity: 'success' 
        });
      } else {
        throw new Error('操作失败');
      }

    } catch (error) {
      setSnackbar({ open: true, message: '操作失败', severity: 'error' });
    }
  };

  // 确认告警
  const acknowledgeAlert = async (alertId: string) => {
    try {
      const response = await fetch(`/monitor/alerts/${alertId}/acknowledge`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('admin_token')}`
        }
      });

      if (response.ok) {
        setAlerts(prev => prev.map(alert => 
          alert.id === alertId 
            ? { ...alert, acknowledged: true }
            : alert
        ));
        setSnackbar({ open: true, message: '告警已确认', severity: 'success' });
      } else {
        throw new Error('确认失败');
      }

    } catch (error) {
      setSnackbar({ open: true, message: '确认告警失败', severity: 'error' });
    }
  };

  // 获取告警图标
  const getAlertIcon = (level: string) => {
    switch (level) {
      case 'critical':
        return <ErrorIcon color="error" />;
      case 'error':
        return <ErrorIcon color="error" />;
      case 'warning':
        return <WarningIcon color="warning" />;
      case 'info':
      default:
        return <InfoIcon color="info" />;
    }
  };

  // 获取告警颜色
  const getAlertSeverity = (level: string): 'error' | 'warning' | 'info' | 'success' => {
    switch (level) {
      case 'critical':
      case 'error':
        return 'error';
      case 'warning':
        return 'warning';
      case 'info':
      default:
        return 'info';
    }
  };

  // 格式化内存使用率
  const getMemoryUsage = () => {
    if (!metrics) return 0;
    return (metrics.system.memory.heapUsed / metrics.system.memory.heapTotal) * 100;
  };

  // 格式化运行时间
  const formatUptime = (seconds: number) => {
    const days = Math.floor(seconds / 86400);
    const hours = Math.floor((seconds % 86400) / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    
    if (days > 0) {
      return `${days}天 ${hours}小时 ${minutes}分钟`;
    } else if (hours > 0) {
      return `${hours}小时 ${minutes}分钟`;
    } else {
      return `${minutes}分钟`;
    }
  };

  // 组件挂载时初始化
  useEffect(() => {
    loadInitialData();
    connectWebSocket();

    return () => {
      if (wsRef.current) {
        wsRef.current.close();
      }
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
      }
    };
  }, []);

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" height={400}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4">
          实时监控
        </Typography>
        
        <Box display="flex" alignItems="center" gap={2}>
          <Chip 
            icon={isConnected ? <CheckIcon /> : <ErrorIcon />}
            label={isConnected ? '已连接' : '未连接'}
            color={isConnected ? 'success' : 'error'}
            size="small"
          />
          
          <FormControlLabel
            control={
              <Switch
                checked={isMonitoringEnabled}
                onChange={toggleMonitoring}
                color="primary"
              />
            }
            label="启用监控"
          />
          
          <Button
            variant="outlined"
            startIcon={<RefreshIcon />}
            onClick={loadInitialData}
          >
            刷新
          </Button>
        </Box>
      </Box>

      {/* 系统指标卡片 */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" mb={2}>
                <MemoryIcon color="primary" sx={{ mr: 1 }} />
                <Typography variant="h6">内存使用</Typography>
              </Box>
              <Typography variant="h4" color="primary">
                {getMemoryUsage().toFixed(1)}%
              </Typography>
              <LinearProgress 
                variant="determinate" 
                value={getMemoryUsage()} 
                sx={{ mt: 1 }}
                color={getMemoryUsage() > 80 ? 'error' : 'primary'}
              />
              {metrics && (
                <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                  {(metrics.system.memory.heapUsed / 1024 / 1024).toFixed(0)}MB / {(metrics.system.memory.heapTotal / 1024 / 1024).toFixed(0)}MB
                </Typography>
              )}
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" mb={2}>
                <CpuIcon color="primary" sx={{ mr: 1 }} />
                <Typography variant="h6">CPU使用</Typography>
              </Box>
              <Typography variant="h4" color="primary">
                {metrics?.system.cpu.toFixed(1) || 0}%
              </Typography>
              <LinearProgress 
                variant="determinate" 
                value={metrics?.system.cpu || 0} 
                sx={{ mt: 1 }}
                color={(metrics?.system.cpu || 0) > 70 ? 'error' : 'primary'}
              />
              <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                运行时间: {metrics ? formatUptime(metrics.system.uptime) : '-'}
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" mb={2}>
                <UsersIcon color="primary" sx={{ mr: 1 }} />
                <Typography variant="h6">活跃用户</Typography>
              </Box>
              <Typography variant="h4" color="primary">
                {metrics?.authentication.activeUsers || 0}
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                登录失败率: {metrics?.authentication.failureRate.toFixed(1) || 0}%
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" mb={2}>
                <SecurityIcon color="primary" sx={{ mr: 1 }} />
                <Typography variant="h6">安全事件</Typography>
              </Box>
              <Typography variant="h4" color="primary">
                {metrics?.security.riskEvents || 0}
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                异常: {metrics?.security.anomalies || 0}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* 告警列表 */}
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            活跃告警 ({alerts.filter(a => !a.acknowledged).length})
          </Typography>
          
          {alerts.filter(a => !a.acknowledged).length === 0 ? (
            <Alert severity="success">
              <AlertTitle>系统正常</AlertTitle>
              当前没有活跃的告警
            </Alert>
          ) : (
            <List>
              {alerts.filter(a => !a.acknowledged).slice(0, 10).map((alert) => (
                <ListItem key={alert.id}>
                  <ListItemIcon>
                    {getAlertIcon(alert.level)}
                  </ListItemIcon>
                  <ListItemText
                    primary={alert.title}
                    secondary={
                      <Box>
                        <Typography variant="body2" color="text.secondary">
                          {alert.message}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {new Date(alert.timestamp).toLocaleString('zh-CN')} - {alert.source}
                        </Typography>
                      </Box>
                    }
                  />
                  <Button
                    size="small"
                    onClick={() => acknowledgeAlert(alert.id)}
                  >
                    确认
                  </Button>
                </ListItem>
              ))}
            </List>
          )}
        </CardContent>
      </Card>

      {/* 通知栏 */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
      >
        <Alert severity={snackbar.severity} onClose={() => setSnackbar({ ...snackbar, open: false })}>
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};
