/**
 * 管理员界面样式
 * 为React Admin应用提供自定义样式
 */

/* 全局样式重置 */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 管理员应用根容器 */
#admin-root {
  width: 100%;
  height: 100vh;
  overflow: hidden;
}

/* React Admin 自定义样式 */
.RaLayout-root {
  display: flex;
  flex-direction: column;
  height: 100vh;
}

.RaLayout-appFrame {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.RaLayout-contentWithSidebar {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.RaLayout-content {
  flex: 1;
  overflow: auto;
  padding: 16px;
  background-color: #f5f5f5;
}

/* 侧边栏样式 */
.RaSidebar-root {
  width: 240px;
  background-color: #1976d2;
  color: white;
  overflow-y: auto;
}

.RaSidebar-fixed {
  position: fixed;
  height: 100vh;
  z-index: 1200;
}

/* 应用栏样式 */
.RaAppBar-root {
  background-color: #1976d2 !important;
  color: white !important;
}

.RaAppBar-title {
  flex: 1;
  font-size: 1.25rem;
  font-weight: 500;
}

/* 菜单样式 */
.RaMenu-root {
  padding: 8px 0;
}

.RaMenuItemLink-root {
  color: rgba(255, 255, 255, 0.87);
  padding: 8px 16px;
  display: flex;
  align-items: center;
  text-decoration: none;
  transition: background-color 0.3s;
}

.RaMenuItemLink-root:hover {
  background-color: rgba(255, 255, 255, 0.08);
}

.RaMenuItemLink-active {
  background-color: rgba(255, 255, 255, 0.12);
  border-right: 3px solid #90caf9;
}

.RaMenuItemLink-icon {
  margin-right: 16px;
  min-width: 24px;
}

/* 仪表板样式 */
.dashboard-container {
  max-width: 1200px;
  margin: 0 auto;
}

.dashboard-welcome {
  margin-bottom: 32px;
}

.dashboard-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
  margin-bottom: 32px;
}

.dashboard-stat-card {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.3s;
}

.dashboard-stat-card:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.dashboard-stat-icon {
  width: 48px;
  height: 48px;
  margin-bottom: 16px;
  color: #1976d2;
}

.dashboard-stat-title {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 8px;
  color: #333;
}

.dashboard-stat-value {
  font-size: 2rem;
  font-weight: 700;
  color: #1976d2;
  margin-bottom: 4px;
}

.dashboard-stat-change {
  font-size: 0.875rem;
  color: #666;
}

.dashboard-stat-change.positive {
  color: #4caf50;
}

.dashboard-stat-change.negative {
  color: #f44336;
}

/* 数据表格样式 */
.RaDatagrid-root {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.RaDatagrid-table {
  width: 100%;
  border-collapse: collapse;
}

.RaDatagrid-headerRow {
  background-color: #f5f5f5;
}

.RaDatagrid-headerCell {
  padding: 16px;
  font-weight: 600;
  color: #333;
  border-bottom: 1px solid #e0e0e0;
}

.RaDatagrid-row {
  transition: background-color 0.2s;
}

.RaDatagrid-row:hover {
  background-color: #f9f9f9;
}

.RaDatagrid-rowCell {
  padding: 16px;
  border-bottom: 1px solid #e0e0e0;
  vertical-align: middle;
}

/* 表单样式 */
.RaSimpleForm-root {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.RaTabbedForm-root {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.RaTabbedForm-tabs {
  background-color: #f5f5f5;
  border-bottom: 1px solid #e0e0e0;
}

.RaTabbedForm-content {
  padding: 24px;
}

/* 按钮样式 */
.RaButton-root {
  border-radius: 6px;
  text-transform: none;
  font-weight: 500;
  padding: 8px 16px;
}

.RaButton-primary {
  background-color: #1976d2;
  color: white;
}

.RaButton-primary:hover {
  background-color: #1565c0;
}

.RaButton-secondary {
  background-color: transparent;
  color: #1976d2;
  border: 1px solid #1976d2;
}

.RaButton-secondary:hover {
  background-color: rgba(25, 118, 210, 0.04);
}

/* 卡片样式 */
.RaCard-root {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.RaCard-header {
  padding: 16px 24px;
  background-color: #f5f5f5;
  border-bottom: 1px solid #e0e0e0;
}

.RaCard-content {
  padding: 24px;
}

/* 工具栏样式 */
.RaToolbar-root {
  background: white;
  border-top: 1px solid #e0e0e0;
  padding: 16px 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 分页样式 */
.RaPagination-root {
  background: white;
  padding: 16px;
  border-top: 1px solid #e0e0e0;
  display: flex;
  justify-content: center;
}

/* 过滤器样式 */
.RaFilter-root {
  background: white;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .RaSidebar-root {
    width: 100%;
    position: fixed;
    top: 0;
    left: -100%;
    transition: left 0.3s;
    z-index: 1300;
  }

  .RaSidebar-open {
    left: 0;
  }

  .RaLayout-content {
    padding: 8px;
  }

  .dashboard-stats-grid {
    grid-template-columns: 1fr;
  }

  .RaDatagrid-table {
    font-size: 0.875rem;
  }

  .RaDatagrid-headerCell,
  .RaDatagrid-rowCell {
    padding: 8px;
  }
}

/* 加载状态样式 */
.RaLoading-root {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.RaLoading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #1976d2;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 错误状态样式 */
.RaError-root {
  background: #ffebee;
  color: #c62828;
  padding: 16px;
  border-radius: 4px;
  margin: 16px 0;
}

/* 成功状态样式 */
.RaSuccess-root {
  background: #e8f5e8;
  color: #2e7d32;
  padding: 16px;
  border-radius: 4px;
  margin: 16px 0;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
