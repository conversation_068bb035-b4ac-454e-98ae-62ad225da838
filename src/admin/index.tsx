/**
 * 管理员Web界面入口文件
 * 渲染React Admin应用到DOM
 */

import React from 'react';
import { createRoot } from 'react-dom/client';
import { AdminApp } from './AdminApp';
import './styles/admin.css';

// 获取根元素
const container = document.getElementById('admin-root');

if (!container) {
  throw new Error('找不到管理员应用的根元素 #admin-root');
}

// 创建React根并渲染应用
const root = createRoot(container);

root.render(
  <React.StrictMode>
    <AdminApp />
  </React.StrictMode>
);

// 热模块替换支持（开发环境）
if (module.hot) {
  module.hot.accept('./AdminApp', () => {
    const NextAdminApp = require('./AdminApp').AdminApp;
    root.render(
      <React.StrictMode>
        <NextAdminApp />
      </React.StrictMode>
    );
  });
}
