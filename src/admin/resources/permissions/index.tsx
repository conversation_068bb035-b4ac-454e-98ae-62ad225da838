/**
 * 权限管理资源组件
 * 提供权限的列表、编辑、创建和详情查看功能
 */

import React from 'react';
import {
  List,
  Datagrid,
  TextField,
  BooleanField,
  DateField,
  EditButton,
  ShowButton,
  DeleteButton,
  Create,
  Edit,
  Show,
  SimpleForm,
  SimpleShowLayout,
  TextInput,
  BooleanInput,
  required,
  Toolbar,
  SaveButton,
  DeleteButton as FormDeleteButton,
  useRecordContext,
  TopToolbar,
  CreateButton,
  ExportButton,
  FilterButton,
  SearchInput,
  SelectInput,
  ArrayInput,
  SimpleFormIterator,
  ChipField,
  ArrayField,
  SingleFieldList,
  Tab,
  TabbedForm,
  TabbedShowLayout
} from 'react-admin';
import {
  Card,
  CardContent,
  Typography,
  Box,
  Chip,
  Avatar
} from '@mui/material';
import {
  Security as SecurityIcon,
  VpnKey as KeyIcon,
  Group as GroupIcon,
  AdminPanelSettings as AdminIcon
} from '@mui/icons-material';

/**
 * 权限列表过滤器
 */
const permissionFilters = [
  <SearchInput source="q" placeholder="搜索权限名称或描述" alwaysOn />,
  <SelectInput
    source="category"
    choices={[
      { id: 'user', name: '用户管理' },
      { id: 'application', name: '应用管理' },
      { id: 'permission', name: '权限管理' },
      { id: 'system', name: '系统管理' },
      { id: 'audit', name: '审计管理' },
    ]}
    placeholder="权限类别"
  />,
  <SelectInput
    source="scope"
    choices={[
      { id: 'global', name: '全局' },
      { id: 'application', name: '应用' },
      { id: 'resource', name: '资源' },
    ]}
    placeholder="权限范围"
  />,
  <SelectInput
    source="type"
    choices={[
      { id: 'action', name: '操作' },
      { id: 'data', name: '数据' },
      { id: 'feature', name: '功能' },
      { id: 'admin', name: '管理' },
    ]}
    placeholder="权限类型"
  />,
  <SelectInput
    source="level"
    choices={[
      { id: 'read', name: '读取' },
      { id: 'write', name: '写入' },
      { id: 'admin', name: '管理' },
      { id: 'owner', name: '拥有者' },
    ]}
    placeholder="权限级别"
  />,
  <SelectInput
    source="isActive"
    choices={[
      { id: true, name: '活跃' },
      { id: false, name: '禁用' },
    ]}
    placeholder="权限状态"
  />,
];

/**
 * 权限列表操作栏
 */
const PermissionListActions = () => (
  <TopToolbar>
    <FilterButton />
    <CreateButton />
    <ExportButton />
  </TopToolbar>
);

/**
 * 权限图标组件
 */
const PermissionIcon = () => {
  const record = useRecordContext();
  if (!record) return null;

  const getIcon = () => {
    switch (record.type) {
      case 'admin':
        return <AdminIcon />;
      case 'data':
        return <KeyIcon />;
      case 'feature':
        return <GroupIcon />;
      default:
        return <SecurityIcon />;
    }
  };

  return (
    <Avatar sx={{ width: 40, height: 40, bgcolor: 'primary.main' }}>
      {getIcon()}
    </Avatar>
  );
};

/**
 * 权限状态芯片组件
 */
const PermissionStatusChip = () => {
  const record = useRecordContext();
  if (!record) return null;

  return (
    <Chip
      label={record.isActive ? '活跃' : '禁用'}
      color={record.isActive ? 'success' : 'error'}
      size="small"
    />
  );
};

/**
 * 权限级别芯片组件
 */
const PermissionLevelChip = () => {
  const record = useRecordContext();
  if (!record) return null;

  const getColor = () => {
    switch (record.level) {
      case 'read':
        return 'info';
      case 'write':
        return 'warning';
      case 'admin':
        return 'error';
      case 'owner':
        return 'secondary';
      default:
        return 'default';
    }
  };

  return (
    <Chip
      label={record.level}
      color={getColor()}
      size="small"
      variant="outlined"
    />
  );
};

/**
 * 权限标签组件
 */
const PermissionTags = () => {
  const record = useRecordContext();
  if (!record || !record.tags || record.tags.length === 0) return null;

  return (
    <Box display="flex" gap={0.5} flexWrap="wrap">
      {record.tags.slice(0, 3).map((tag: string, index: number) => (
        <Chip
          key={index}
          label={tag}
          size="small"
          variant="outlined"
        />
      ))}
      {record.tags.length > 3 && (
        <Chip
          label={`+${record.tags.length - 3}`}
          size="small"
          variant="outlined"
        />
      )}
    </Box>
  );
};

/**
 * 权限列表组件
 */
export const PermissionList = () => (
  <List
    filters={permissionFilters}
    actions={<PermissionListActions />}
    sort={{ field: 'createdAt', order: 'DESC' }}
    perPage={25}
  >
    <Datagrid rowClick="show">
      <PermissionIcon />
      <TextField source="name" label="权限名称" />
      <TextField source="displayName" label="显示名称" />
      <TextField source="category" label="类别" />
      <TextField source="scope" label="范围" />
      <TextField source="type" label="类型" />
      <PermissionLevelChip />
      <PermissionTags />
      <PermissionStatusChip />
      <DateField source="createdAt" label="创建时间" showTime />
      <EditButton />
      <ShowButton />
      <DeleteButton />
    </Datagrid>
  </List>
);

/**
 * 权限创建表单工具栏
 */
const PermissionCreateToolbar = () => (
  <Toolbar>
    <SaveButton />
  </Toolbar>
);

/**
 * 权限创建组件
 */
export const PermissionCreate = () => (
  <Create>
    <TabbedForm toolbar={<PermissionCreateToolbar />}>
      <Tab label="基本信息">
        <TextInput
          source="name"
          label="权限名称"
          validate={[required()]}
          fullWidth
          helperText="权限的唯一标识符，如：user.read"
        />
        <TextInput
          source="displayName"
          label="显示名称"
          validate={[required()]}
          fullWidth
          helperText="用户友好的权限名称"
        />
        <TextInput
          source="description"
          label="权限描述"
          validate={[required()]}
          multiline
          fullWidth
          helperText="详细描述此权限的作用"
        />
        <SelectInput
          source="category"
          label="权限类别"
          choices={[
            { id: 'user', name: '用户管理' },
            { id: 'application', name: '应用管理' },
            { id: 'permission', name: '权限管理' },
            { id: 'system', name: '系统管理' },
            { id: 'audit', name: '审计管理' },
          ]}
          validate={[required()]}
          fullWidth
        />
        <SelectInput
          source="scope"
          label="权限范围"
          choices={[
            { id: 'global', name: '全局' },
            { id: 'application', name: '应用' },
            { id: 'resource', name: '资源' },
          ]}
          validate={[required()]}
          fullWidth
        />
        <SelectInput
          source="type"
          label="权限类型"
          choices={[
            { id: 'action', name: '操作' },
            { id: 'data', name: '数据' },
            { id: 'feature', name: '功能' },
            { id: 'admin', name: '管理' },
          ]}
          validate={[required()]}
          fullWidth
        />
        <SelectInput
          source="level"
          label="权限级别"
          choices={[
            { id: 'read', name: '读取' },
            { id: 'write', name: '写入' },
            { id: 'admin', name: '管理' },
            { id: 'owner', name: '拥有者' },
          ]}
          validate={[required()]}
          fullWidth
        />
        <BooleanInput source="isActive" label="启用权限" defaultValue={true} />
      </Tab>
      
      <Tab label="依赖关系">
        <ArrayInput source="dependencies" label="依赖权限">
          <SimpleFormIterator>
            <TextInput
              label="权限ID"
              fullWidth
              helperText="此权限依赖的其他权限ID"
            />
          </SimpleFormIterator>
        </ArrayInput>
        
        <ArrayInput source="conflicts" label="冲突权限">
          <SimpleFormIterator>
            <TextInput
              label="权限ID"
              fullWidth
              helperText="与此权限冲突的权限ID"
            />
          </SimpleFormIterator>
        </ArrayInput>
        
        <ArrayInput source="prerequisites" label="前置条件">
          <SimpleFormIterator>
            <TextInput
              label="权限ID"
              fullWidth
              helperText="获得此权限前必须先拥有的权限ID"
            />
          </SimpleFormIterator>
        </ArrayInput>
        
        <ArrayInput source="implications" label="隐含权限">
          <SimpleFormIterator>
            <TextInput
              label="权限ID"
              fullWidth
              helperText="获得此权限时自动授予的权限ID"
            />
          </SimpleFormIterator>
        </ArrayInput>
      </Tab>
      
      <Tab label="标签和元数据">
        <ArrayInput source="tags" label="权限标签">
          <SimpleFormIterator>
            <TextInput
              label="标签"
              fullWidth
              helperText="用于分类和搜索的标签"
            />
          </SimpleFormIterator>
        </ArrayInput>
        
        <TextInput
          source="metadata.businessOwner"
          label="业务负责人"
          fullWidth
        />
        <TextInput
          source="metadata.technicalOwner"
          label="技术负责人"
          fullWidth
        />
        <TextInput
          source="metadata.riskLevel"
          label="风险级别"
          fullWidth
        />
        <TextInput
          source="metadata.complianceRequirement"
          label="合规要求"
          multiline
          fullWidth
        />
      </Tab>
    </TabbedForm>
  </Create>
);

/**
 * 权限编辑表单工具栏
 */
const PermissionEditToolbar = () => (
  <Toolbar>
    <SaveButton />
    <FormDeleteButton />
  </Toolbar>
);

/**
 * 权限编辑组件
 */
export const PermissionEdit = () => (
  <Edit>
    <TabbedForm toolbar={<PermissionEditToolbar />}>
      <Tab label="基本信息">
        <TextInput
          source="name"
          label="权限名称"
          validate={[required()]}
          fullWidth
        />
        <TextInput
          source="displayName"
          label="显示名称"
          validate={[required()]}
          fullWidth
        />
        <TextInput
          source="description"
          label="权限描述"
          validate={[required()]}
          multiline
          fullWidth
        />
        <SelectInput
          source="category"
          label="权限类别"
          choices={[
            { id: 'user', name: '用户管理' },
            { id: 'application', name: '应用管理' },
            { id: 'permission', name: '权限管理' },
            { id: 'system', name: '系统管理' },
            { id: 'audit', name: '审计管理' },
          ]}
          validate={[required()]}
          fullWidth
        />
        <SelectInput
          source="scope"
          label="权限范围"
          choices={[
            { id: 'global', name: '全局' },
            { id: 'application', name: '应用' },
            { id: 'resource', name: '资源' },
          ]}
          validate={[required()]}
          fullWidth
        />
        <SelectInput
          source="type"
          label="权限类型"
          choices={[
            { id: 'action', name: '操作' },
            { id: 'data', name: '数据' },
            { id: 'feature', name: '功能' },
            { id: 'admin', name: '管理' },
          ]}
          validate={[required()]}
          fullWidth
        />
        <SelectInput
          source="level"
          label="权限级别"
          choices={[
            { id: 'read', name: '读取' },
            { id: 'write', name: '写入' },
            { id: 'admin', name: '管理' },
            { id: 'owner', name: '拥有者' },
          ]}
          validate={[required()]}
          fullWidth
        />
        <BooleanInput source="isActive" label="启用权限" />
        <DateField source="createdAt" label="创建时间" showTime />
        <DateField source="updatedAt" label="更新时间" showTime />
      </Tab>
      
      <Tab label="依赖关系">
        <ArrayInput source="dependencies" label="依赖权限">
          <SimpleFormIterator>
            <TextInput label="权限ID" fullWidth />
          </SimpleFormIterator>
        </ArrayInput>
        
        <ArrayInput source="conflicts" label="冲突权限">
          <SimpleFormIterator>
            <TextInput label="权限ID" fullWidth />
          </SimpleFormIterator>
        </ArrayInput>
        
        <ArrayInput source="prerequisites" label="前置条件">
          <SimpleFormIterator>
            <TextInput label="权限ID" fullWidth />
          </SimpleFormIterator>
        </ArrayInput>
        
        <ArrayInput source="implications" label="隐含权限">
          <SimpleFormIterator>
            <TextInput label="权限ID" fullWidth />
          </SimpleFormIterator>
        </ArrayInput>
      </Tab>
      
      <Tab label="标签和元数据">
        <ArrayInput source="tags" label="权限标签">
          <SimpleFormIterator>
            <TextInput label="标签" fullWidth />
          </SimpleFormIterator>
        </ArrayInput>
        
        <TextInput
          source="metadata.businessOwner"
          label="业务负责人"
          fullWidth
        />
        <TextInput
          source="metadata.technicalOwner"
          label="技术负责人"
          fullWidth
        />
        <TextInput
          source="metadata.riskLevel"
          label="风险级别"
          fullWidth
        />
        <TextInput
          source="metadata.complianceRequirement"
          label="合规要求"
          multiline
          fullWidth
        />
      </Tab>
    </TabbedForm>
  </Edit>
);

/**
 * 权限详情组件
 */
export const PermissionShow = () => (
  <Show>
    <TabbedShowLayout>
      <Tab label="基本信息">
        <Box display="flex" alignItems="center" mb={2}>
          <PermissionIcon />
          <Box ml={2}>
            <Typography variant="h6">
              <TextField source="displayName" />
            </Typography>
            <Typography variant="body2" color="text.secondary">
              <TextField source="name" />
            </Typography>
          </Box>
        </Box>

        <TextField source="description" label="权限描述" />
        <TextField source="category" label="权限类别" />
        <TextField source="scope" label="权限范围" />
        <TextField source="type" label="权限类型" />
        <PermissionLevelChip />
        <PermissionStatusChip />
        <DateField source="createdAt" label="创建时间" showTime />
        <DateField source="updatedAt" label="更新时间" showTime />
      </Tab>

      <Tab label="依赖关系">
        <ArrayField source="dependencies" label="依赖权限">
          <SingleFieldList>
            <ChipField />
          </SingleFieldList>
        </ArrayField>

        <ArrayField source="conflicts" label="冲突权限">
          <SingleFieldList>
            <ChipField />
          </SingleFieldList>
        </ArrayField>

        <ArrayField source="prerequisites" label="前置条件">
          <SingleFieldList>
            <ChipField />
          </SingleFieldList>
        </ArrayField>

        <ArrayField source="implications" label="隐含权限">
          <SingleFieldList>
            <ChipField />
          </SingleFieldList>
        </ArrayField>
      </Tab>

      <Tab label="标签和元数据">
        <ArrayField source="tags" label="权限标签">
          <SingleFieldList>
            <ChipField />
          </SingleFieldList>
        </ArrayField>

        <TextField source="metadata.businessOwner" label="业务负责人" />
        <TextField source="metadata.technicalOwner" label="技术负责人" />
        <TextField source="metadata.riskLevel" label="风险级别" />
        <TextField source="metadata.complianceRequirement" label="合规要求" />
      </Tab>
    </TabbedShowLayout>
  </Show>
);
