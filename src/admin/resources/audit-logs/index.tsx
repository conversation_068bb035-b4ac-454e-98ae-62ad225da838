/**
 * 审计日志资源组件
 * 提供审计日志的列表和详情查看功能
 */

import React from 'react';
import {
  List,
  Datagrid,
  TextField,
  DateField,
  ShowButton,
  Show,
  SimpleShowLayout,
  useRecordContext,
  TopToolbar,
  ExportButton,
  FilterButton,
  SearchInput,
  SelectInput,
  DateInput,
  ReferenceField,
  Tab,
  TabbedShowLayout,
  JsonField
} from 'react-admin';
import {
  Card,
  CardContent,
  Typography,
  Box,
  Chip,
  Avatar
} from '@mui/material';
import {
  History as HistoryIcon,
  Security as SecurityIcon,
  Warning as WarningIcon,
  Error as ErrorIcon,
  Info as InfoIcon,
  Person as PersonIcon,
  Computer as ComputerIcon
} from '@mui/icons-material';

/**
 * 审计日志列表过滤器
 */
const auditLogFilters = [
  <SearchInput source="q" placeholder="搜索操作或资源" alwaysOn />,
  <SelectInput
    source="action"
    choices={[
      { id: 'login', name: '登录' },
      { id: 'logout', name: '登出' },
      { id: 'create', name: '创建' },
      { id: 'update', name: '更新' },
      { id: 'delete', name: '删除' },
      { id: 'permission_grant', name: '权限授予' },
      { id: 'permission_revoke', name: '权限撤销' },
      { id: 'config_change', name: '配置变更' },
    ]}
    placeholder="操作类型"
  />,
  <SelectInput
    source="resource"
    choices={[
      { id: 'user', name: '用户' },
      { id: 'application', name: '应用' },
      { id: 'permission', name: '权限' },
      { id: 'system', name: '系统' },
      { id: 'auth', name: '认证' },
    ]}
    placeholder="资源类型"
  />,
  <SelectInput
    source="level"
    choices={[
      { id: 'info', name: '信息' },
      { id: 'warning', name: '警告' },
      { id: 'error', name: '错误' },
      { id: 'critical', name: '严重' },
    ]}
    placeholder="日志级别"
  />,
  <DateInput source="createdAt_gte" label="开始时间" />,
  <DateInput source="createdAt_lte" label="结束时间" />,
];

/**
 * 审计日志列表操作栏
 */
const AuditLogListActions = () => (
  <TopToolbar>
    <FilterButton />
    <ExportButton />
  </TopToolbar>
);

/**
 * 日志级别图标组件
 */
const LogLevelIcon = () => {
  const record = useRecordContext();
  if (!record) return null;

  const getIcon = () => {
    switch (record.level) {
      case 'info':
        return <InfoIcon color="info" />;
      case 'warning':
        return <WarningIcon color="warning" />;
      case 'error':
        return <ErrorIcon color="error" />;
      case 'critical':
        return <ErrorIcon color="error" />;
      default:
        return <HistoryIcon />;
    }
  };

  return (
    <Avatar sx={{ width: 32, height: 32 }}>
      {getIcon()}
    </Avatar>
  );
};

/**
 * 日志级别芯片组件
 */
const LogLevelChip = () => {
  const record = useRecordContext();
  if (!record) return null;

  const getColor = () => {
    switch (record.level) {
      case 'info':
        return 'info';
      case 'warning':
        return 'warning';
      case 'error':
        return 'error';
      case 'critical':
        return 'secondary';
      default:
        return 'default';
    }
  };

  const getLabel = () => {
    switch (record.level) {
      case 'info':
        return '信息';
      case 'warning':
        return '警告';
      case 'error':
        return '错误';
      case 'critical':
        return '严重';
      default:
        return record.level;
    }
  };

  return (
    <Chip
      label={getLabel()}
      color={getColor()}
      size="small"
    />
  );
};

/**
 * 操作类型芯片组件
 */
const ActionChip = () => {
  const record = useRecordContext();
  if (!record) return null;

  const getLabel = () => {
    switch (record.action) {
      case 'login':
        return '登录';
      case 'logout':
        return '登出';
      case 'create':
        return '创建';
      case 'update':
        return '更新';
      case 'delete':
        return '删除';
      case 'permission_grant':
        return '权限授予';
      case 'permission_revoke':
        return '权限撤销';
      case 'config_change':
        return '配置变更';
      default:
        return record.action;
    }
  };

  return (
    <Chip
      label={getLabel()}
      variant="outlined"
      size="small"
    />
  );
};

/**
 * 用户信息组件
 */
const UserInfo = () => {
  const record = useRecordContext();
  if (!record) return null;

  if (!record.user) {
    return (
      <Box display="flex" alignItems="center">
        <Avatar sx={{ width: 32, height: 32, mr: 1 }}>
          <ComputerIcon />
        </Avatar>
        <Typography variant="body2" color="text.secondary">
          系统操作
        </Typography>
      </Box>
    );
  }

  return (
    <Box display="flex" alignItems="center">
      <Avatar sx={{ width: 32, height: 32, mr: 1 }}>
        <PersonIcon />
      </Avatar>
      <Box>
        <Typography variant="body2" fontWeight="medium">
          {record.user.username}
        </Typography>
        <Typography variant="caption" color="text.secondary">
          {record.user.email}
        </Typography>
      </Box>
    </Box>
  );
};

/**
 * IP地址和位置信息组件
 */
const LocationInfo = () => {
  const record = useRecordContext();
  if (!record) return null;

  return (
    <Box>
      <Typography variant="body2">
        {record.ipAddress}
      </Typography>
      {record.location && (
        <Typography variant="caption" color="text.secondary">
          {record.location}
        </Typography>
      )}
    </Box>
  );
};

/**
 * 审计日志列表组件
 */
export const AuditLogList = () => (
  <List
    filters={auditLogFilters}
    actions={<AuditLogListActions />}
    sort={{ field: 'createdAt', order: 'DESC' }}
    perPage={50}
  >
    <Datagrid rowClick="show">
      <LogLevelIcon />
      <ActionChip />
      <TextField source="resource" label="资源" />
      <UserInfo />
      <LocationInfo />
      <TextField source="userAgent" label="用户代理" />
      <LogLevelChip />
      <DateField source="createdAt" label="时间" showTime />
      <ShowButton />
    </Datagrid>
  </List>
);

/**
 * 审计日志详情组件
 */
export const AuditLogShow = () => (
  <Show>
    <TabbedShowLayout>
      <Tab label="基本信息">
        <Box display="flex" alignItems="center" mb={2}>
          <LogLevelIcon />
          <Box ml={2}>
            <Typography variant="h6">
              <ActionChip />
            </Typography>
            <Typography variant="body2" color="text.secondary">
              <TextField source="resource" />
            </Typography>
          </Box>
        </Box>
        
        <TextField source="description" label="操作描述" />
        <LogLevelChip />
        <DateField source="createdAt" label="操作时间" showTime />
        
        <Box mt={2}>
          <Typography variant="h6" gutterBottom>
            操作者信息
          </Typography>
          <UserInfo />
        </Box>
      </Tab>
      
      <Tab label="技术详情">
        <TextField source="ipAddress" label="IP地址" />
        <TextField source="location" label="地理位置" />
        <TextField source="userAgent" label="用户代理" />
        <TextField source="sessionId" label="会话ID" />
        <TextField source="requestId" label="请求ID" />
        <TextField source="method" label="HTTP方法" />
        <TextField source="url" label="请求URL" />
        <TextField source="statusCode" label="状态码" />
        <TextField source="responseTime" label="响应时间(ms)" />
      </Tab>
      
      <Tab label="详细数据">
        <Typography variant="h6" gutterBottom>
          请求数据
        </Typography>
        <JsonField source="requestData" />
        
        <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>
          响应数据
        </Typography>
        <JsonField source="responseData" />
        
        <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>
          元数据
        </Typography>
        <JsonField source="metadata" />
      </Tab>
      
      <Tab label="安全信息">
        <TextField source="riskScore" label="风险评分" />
        <TextField source="threatLevel" label="威胁级别" />
        <TextField source="complianceFlags" label="合规标记" />
        
        <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>
          安全上下文
        </Typography>
        <TextField source="securityContext.authMethod" label="认证方法" />
        <TextField source="securityContext.mfaUsed" label="是否使用MFA" />
        <TextField source="securityContext.deviceTrusted" label="设备可信状态" />
        <TextField source="securityContext.anomalyScore" label="异常评分" />
        
        <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>
          相关事件
        </Typography>
        <TextField source="relatedEvents" label="关联事件ID" />
        <TextField source="correlationId" label="关联ID" />
      </Tab>
    </TabbedShowLayout>
  </Show>
);
