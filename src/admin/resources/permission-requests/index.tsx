/**
 * 权限申请管理资源组件
 * 提供权限申请的列表、编辑和详情查看功能
 */

import React from 'react';
import {
  List,
  Datagrid,
  TextField,
  DateField,
  EditButton,
  ShowButton,
  Edit,
  Show,
  SimpleForm,
  SimpleShowLayout,
  TextInput,
  SelectInput,
  useRecordContext,
  TopToolbar,
  ExportButton,
  FilterButton,
  SearchInput,
  ArrayField,
  SingleFieldList,
  ChipField,
  Tab,
  TabbedForm,
  TabbedShowLayout,
  ReferenceField,
  Toolbar,
  SaveButton,
  Button,
  useUpdate,
  useNotify,
  useRefresh
} from 'react-admin';
import {
  Card,
  CardContent,
  Typography,
  Box,
  Chip,
  Avatar,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField as MuiTextField
} from '@mui/material';
import {
  Assignment as RequestIcon,
  CheckCircle as ApproveIcon,
  Cancel as RejectIcon,
  Schedule as PendingIcon,
  Person as PersonIcon
} from '@mui/icons-material';

/**
 * 权限申请列表过滤器
 */
const permissionRequestFilters = [
  <SearchInput source="q" placeholder="搜索申请人或应用" alwaysOn />,
  <SelectInput
    source="status"
    choices={[
      { id: 'pending', name: '待审批' },
      { id: 'approved', name: '已批准' },
      { id: 'rejected', name: '已拒绝' },
    ]}
    placeholder="申请状态"
  />,
  <SelectInput
    source="urgency"
    choices={[
      { id: 'low', name: '低' },
      { id: 'medium', name: '中' },
      { id: 'high', name: '高' },
      { id: 'critical', name: '紧急' },
    ]}
    placeholder="紧急程度"
  />,
];

/**
 * 权限申请列表操作栏
 */
const PermissionRequestListActions = () => (
  <TopToolbar>
    <FilterButton />
    <ExportButton />
  </TopToolbar>
);

/**
 * 申请状态芯片组件
 */
const RequestStatusChip = () => {
  const record = useRecordContext();
  if (!record) return null;

  const getColor = () => {
    switch (record.status) {
      case 'pending':
        return 'warning';
      case 'approved':
        return 'success';
      case 'rejected':
        return 'error';
      default:
        return 'default';
    }
  };

  const getIcon = () => {
    switch (record.status) {
      case 'pending':
        return <PendingIcon />;
      case 'approved':
        return <ApproveIcon />;
      case 'rejected':
        return <RejectIcon />;
      default:
        return <RequestIcon />;
    }
  };

  const getLabel = () => {
    switch (record.status) {
      case 'pending':
        return '待审批';
      case 'approved':
        return '已批准';
      case 'rejected':
        return '已拒绝';
      default:
        return record.status;
    }
  };

  return (
    <Chip
      icon={getIcon()}
      label={getLabel()}
      color={getColor()}
      size="small"
    />
  );
};

/**
 * 紧急程度芯片组件
 */
const UrgencyChip = () => {
  const record = useRecordContext();
  if (!record) return null;

  const getColor = () => {
    switch (record.urgency) {
      case 'low':
        return 'info';
      case 'medium':
        return 'warning';
      case 'high':
        return 'error';
      case 'critical':
        return 'secondary';
      default:
        return 'default';
    }
  };

  const getLabel = () => {
    switch (record.urgency) {
      case 'low':
        return '低';
      case 'medium':
        return '中';
      case 'high':
        return '高';
      case 'critical':
        return '紧急';
      default:
        return record.urgency;
    }
  };

  return (
    <Chip
      label={getLabel()}
      color={getColor()}
      size="small"
      variant="outlined"
    />
  );
};

/**
 * 申请人信息组件
 */
const RequesterInfo = () => {
  const record = useRecordContext();
  if (!record || !record.user) return null;

  return (
    <Box display="flex" alignItems="center">
      <Avatar sx={{ width: 32, height: 32, mr: 1 }}>
        <PersonIcon />
      </Avatar>
      <Box>
        <Typography variant="body2" fontWeight="medium">
          {record.user.username}
        </Typography>
        <Typography variant="caption" color="text.secondary">
          {record.user.email}
        </Typography>
      </Box>
    </Box>
  );
};

/**
 * 审批操作按钮组件
 */
const ApprovalActions = () => {
  const record = useRecordContext();
  const [update] = useUpdate();
  const notify = useNotify();
  const refresh = useRefresh();
  const [dialogOpen, setDialogOpen] = React.useState(false);
  const [decision, setDecision] = React.useState<'approve' | 'reject'>('approve');
  const [comments, setComments] = React.useState('');

  if (!record || record.status !== 'pending') return null;

  const handleApproval = async () => {
    try {
      await update(
        'permission-requests',
        {
          id: record.id,
          data: { status: decision, comments },
          previousData: record
        }
      );
      
      notify(`权限申请已${decision === 'approve' ? '批准' : '拒绝'}`, { type: 'success' });
      setDialogOpen(false);
      setComments('');
      refresh();
    } catch (error) {
      notify('操作失败', { type: 'error' });
    }
  };

  return (
    <>
      <Box display="flex" gap={1}>
        <Button
          label="批准"
          onClick={() => {
            setDecision('approve');
            setDialogOpen(true);
          }}
          variant="contained"
          color="success"
          size="small"
        />
        <Button
          label="拒绝"
          onClick={() => {
            setDecision('reject');
            setDialogOpen(true);
          }}
          variant="outlined"
          color="error"
          size="small"
        />
      </Box>

      <Dialog open={dialogOpen} onClose={() => setDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>
          {decision === 'approve' ? '批准权限申请' : '拒绝权限申请'}
        </DialogTitle>
        <DialogContent>
          <MuiTextField
            label="审批意见"
            multiline
            rows={4}
            fullWidth
            value={comments}
            onChange={(e) => setComments(e.target.value)}
            margin="normal"
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDialogOpen(false)}>取消</Button>
          <Button onClick={handleApproval} variant="contained">
            确认{decision === 'approve' ? '批准' : '拒绝'}
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

/**
 * 权限申请列表组件
 */
export const PermissionRequestList = () => (
  <List
    filters={permissionRequestFilters}
    actions={<PermissionRequestListActions />}
    sort={{ field: 'createdAt', order: 'DESC' }}
    perPage={25}
  >
    <Datagrid rowClick="show">
      <RequesterInfo />
      <ReferenceField source="targetApplicationId" reference="applications" label="目标应用">
        <TextField source="name" />
      </ReferenceField>
      <ArrayField source="requestedPermissions" label="申请权限">
        <SingleFieldList>
          <ChipField size="small" />
        </SingleFieldList>
      </ArrayField>
      <UrgencyChip />
      <RequestStatusChip />
      <DateField source="createdAt" label="申请时间" showTime />
      <ApprovalActions />
      <EditButton />
      <ShowButton />
    </Datagrid>
  </List>
);

/**
 * 权限申请编辑组件
 */
export const PermissionRequestEdit = () => (
  <Edit>
    <TabbedForm>
      <Tab label="基本信息">
        <ReferenceField source="userId" reference="users" label="申请人">
          <TextField source="username" />
        </ReferenceField>
        <ReferenceField source="targetApplicationId" reference="applications" label="目标应用">
          <TextField source="name" />
        </ReferenceField>
        <SelectInput
          source="status"
          label="申请状态"
          choices={[
            { id: 'pending', name: '待审批' },
            { id: 'approved', name: '已批准' },
            { id: 'rejected', name: '已拒绝' },
          ]}
        />
        <SelectInput
          source="urgency"
          label="紧急程度"
          choices={[
            { id: 'low', name: '低' },
            { id: 'medium', name: '中' },
            { id: 'high', name: '高' },
            { id: 'critical', name: '紧急' },
          ]}
        />
        <TextInput source="justification" label="申请理由" multiline fullWidth />
        <DateField source="createdAt" label="申请时间" showTime />
        <DateField source="processedAt" label="处理时间" showTime />
      </Tab>
      
      <Tab label="权限详情">
        <ArrayField source="requestedPermissions" label="申请的权限">
          <SingleFieldList>
            <ChipField />
          </SingleFieldList>
        </ArrayField>
        
        <ArrayField source="resolvedPermissions" label="解析后的权限">
          <SingleFieldList>
            <ChipField />
          </SingleFieldList>
        </ArrayField>
        
        <ArrayField source="approvers" label="审批者">
          <SingleFieldList>
            <ChipField />
          </SingleFieldList>
        </ArrayField>
      </Tab>
    </TabbedForm>
  </Edit>
);

/**
 * 权限申请详情组件
 */
export const PermissionRequestShow = () => (
  <Show>
    <TabbedShowLayout>
      <Tab label="基本信息">
        <Box mb={2}>
          <RequesterInfo />
        </Box>
        
        <ReferenceField source="sourceApplicationId" reference="applications" label="源应用">
          <TextField source="name" />
        </ReferenceField>
        <ReferenceField source="targetApplicationId" reference="applications" label="目标应用">
          <TextField source="name" />
        </ReferenceField>
        <TextField source="justification" label="申请理由" />
        <UrgencyChip />
        <RequestStatusChip />
        <TextField source="estimatedProcessingTime" label="预计处理时间(小时)" />
        <DateField source="createdAt" label="申请时间" showTime />
        <DateField source="processedAt" label="处理时间" showTime />
      </Tab>
      
      <Tab label="权限详情">
        <ArrayField source="requestedPermissions" label="申请的权限">
          <SingleFieldList>
            <ChipField />
          </SingleFieldList>
        </ArrayField>
        
        <ArrayField source="resolvedPermissions" label="解析后的权限">
          <SingleFieldList>
            <ChipField />
          </SingleFieldList>
        </ArrayField>
        
        <ArrayField source="approvers" label="审批者">
          <SingleFieldList>
            <ChipField />
          </SingleFieldList>
        </ArrayField>
      </Tab>
      
      <Tab label="审批记录">
        {/* 这里可以添加审批历史记录 */}
        <Typography variant="h6" gutterBottom>
          审批历史
        </Typography>
        <Typography variant="body2" color="text.secondary">
          审批历史记录将在此显示
        </Typography>
      </Tab>
    </TabbedShowLayout>
  </Show>
);
