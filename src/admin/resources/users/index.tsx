/**
 * 用户管理资源组件
 * 提供用户的列表、编辑、创建和详情查看功能
 */

import React from 'react';
import {
  List,
  Datagrid,
  TextField,
  EmailField,
  BooleanField,
  DateField,
  EditButton,
  ShowButton,
  DeleteButton,
  Create,
  Edit,
  Show,
  SimpleForm,
  SimpleShowLayout,
  TextInput,
  BooleanInput,
  DateInput,
  required,
  email,
  Toolbar,
  SaveButton,
  DeleteButton as FormDeleteButton,
  useRecordContext,
  TopToolbar,
  CreateButton,
  ExportButton,
  FilterButton,
  SearchInput,
  SelectInput,
  ReferenceInput,
  ChipField,
  ArrayField,
  SingleFieldList,
  Tab,
  TabbedForm,
  TabbedShowLayout,
  ReferenceManyField,
  NumberField
} from 'react-admin';
import {
  Card,
  CardContent,
  Typography,
  Box,
  Chip,
  Avatar
} from '@mui/material';
import {
  Person as PersonIcon,
  Email as EmailIcon,
  Phone as PhoneIcon,
  LocationOn as LocationIcon
} from '@mui/icons-material';

/**
 * 用户列表过滤器
 */
const userFilters = [
  <SearchInput source="q" placeholder="搜索用户名或邮箱" alwaysOn />,
  <SelectInput
    source="isActive"
    choices={[
      { id: true, name: '活跃' },
      { id: false, name: '禁用' },
    ]}
    placeholder="用户状态"
  />,
  <SelectInput
    source="emailVerified"
    choices={[
      { id: true, name: '已验证' },
      { id: false, name: '未验证' },
    ]}
    placeholder="邮箱验证状态"
  />,
];

/**
 * 用户列表操作栏
 */
const UserListActions = () => (
  <TopToolbar>
    <FilterButton />
    <CreateButton />
    <ExportButton />
  </TopToolbar>
);

/**
 * 用户头像组件
 */
const UserAvatar = () => {
  const record = useRecordContext();
  if (!record) return null;

  return (
    <Avatar
      src={record.profile?.avatar}
      sx={{ width: 40, height: 40 }}
    >
      {record.username?.charAt(0).toUpperCase()}
    </Avatar>
  );
};

/**
 * 用户状态芯片组件
 */
const UserStatusChip = () => {
  const record = useRecordContext();
  if (!record) return null;

  return (
    <Chip
      label={record.isActive ? '活跃' : '禁用'}
      color={record.isActive ? 'success' : 'error'}
      size="small"
    />
  );
};

/**
 * 用户列表组件
 */
export const UserList = () => (
  <List
    filters={userFilters}
    actions={<UserListActions />}
    sort={{ field: 'createdAt', order: 'DESC' }}
    perPage={25}
  >
    <Datagrid rowClick="show">
      <UserAvatar />
      <TextField source="username" label="用户名" />
      <EmailField source="email" label="邮箱" />
      <TextField source="profile.displayName" label="显示名称" />
      <UserStatusChip />
      <BooleanField source="emailVerified" label="邮箱已验证" />
      <DateField source="lastLoginAt" label="最后登录" showTime />
      <DateField source="createdAt" label="创建时间" showTime />
      <EditButton />
      <ShowButton />
      <DeleteButton />
    </Datagrid>
  </List>
);

/**
 * 用户创建表单工具栏
 */
const UserCreateToolbar = () => (
  <Toolbar>
    <SaveButton />
  </Toolbar>
);

/**
 * 用户创建组件
 */
export const UserCreate = () => (
  <Create>
    <TabbedForm toolbar={<UserCreateToolbar />}>
      <Tab label="基本信息">
        <TextInput
          source="username"
          label="用户名"
          validate={[required()]}
          fullWidth
        />
        <TextInput
          source="email"
          label="邮箱"
          validate={[required(), email()]}
          fullWidth
        />
        <TextInput
          source="password"
          label="密码"
          type="password"
          validate={[required()]}
          fullWidth
        />
        <BooleanInput source="isActive" label="启用用户" defaultValue={true} />
        <BooleanInput source="emailVerified" label="邮箱已验证" defaultValue={false} />
      </Tab>
      
      <Tab label="个人资料">
        <TextInput source="profile.displayName" label="显示名称" fullWidth />
        <TextInput source="profile.firstName" label="名" fullWidth />
        <TextInput source="profile.lastName" label="姓" fullWidth />
        <TextInput source="profile.phoneNumber" label="电话号码" fullWidth />
        <TextInput source="profile.avatar" label="头像URL" fullWidth />
        <TextInput source="profile.website" label="网站" fullWidth />
        <SelectInput
          source="profile.gender"
          label="性别"
          choices={[
            { id: 'male', name: '男' },
            { id: 'female', name: '女' },
            { id: 'other', name: '其他' },
          ]}
          fullWidth
        />
        <DateInput source="profile.birthDate" label="出生日期" />
        <TextInput source="profile.timezone" label="时区" fullWidth />
        <TextInput source="profile.locale" label="语言" fullWidth />
      </Tab>
      
      <Tab label="地址信息">
        <TextInput source="profile.address" label="地址" multiline fullWidth />
        <TextInput source="profile.streetAddress" label="街道地址" fullWidth />
        <TextInput source="profile.city" label="城市" fullWidth />
        <TextInput source="profile.state" label="省/州" fullWidth />
        <TextInput source="profile.postalCode" label="邮政编码" fullWidth />
        <TextInput source="profile.country" label="国家" fullWidth />
      </Tab>
    </TabbedForm>
  </Create>
);

/**
 * 用户编辑表单工具栏
 */
const UserEditToolbar = () => (
  <Toolbar>
    <SaveButton />
    <FormDeleteButton />
  </Toolbar>
);

/**
 * 用户编辑组件
 */
export const UserEdit = () => (
  <Edit>
    <TabbedForm toolbar={<UserEditToolbar />}>
      <Tab label="基本信息">
        <TextInput
          source="username"
          label="用户名"
          validate={[required()]}
          fullWidth
        />
        <TextInput
          source="email"
          label="邮箱"
          validate={[required(), email()]}
          fullWidth
        />
        <BooleanInput source="isActive" label="启用用户" />
        <BooleanInput source="emailVerified" label="邮箱已验证" />
        <DateField source="lastLoginAt" label="最后登录" showTime />
        <DateField source="createdAt" label="创建时间" showTime />
        <DateField source="updatedAt" label="更新时间" showTime />
      </Tab>
      
      <Tab label="个人资料">
        <TextInput source="profile.displayName" label="显示名称" fullWidth />
        <TextInput source="profile.firstName" label="名" fullWidth />
        <TextInput source="profile.lastName" label="姓" fullWidth />
        <TextInput source="profile.phoneNumber" label="电话号码" fullWidth />
        <TextInput source="profile.avatar" label="头像URL" fullWidth />
        <TextInput source="profile.website" label="网站" fullWidth />
        <SelectInput
          source="profile.gender"
          label="性别"
          choices={[
            { id: 'male', name: '男' },
            { id: 'female', name: '女' },
            { id: 'other', name: '其他' },
          ]}
          fullWidth
        />
        <DateInput source="profile.birthDate" label="出生日期" />
        <TextInput source="profile.timezone" label="时区" fullWidth />
        <TextInput source="profile.locale" label="语言" fullWidth />
      </Tab>
      
      <Tab label="地址信息">
        <TextInput source="profile.address" label="地址" multiline fullWidth />
        <TextInput source="profile.streetAddress" label="街道地址" fullWidth />
        <TextInput source="profile.city" label="城市" fullWidth />
        <TextInput source="profile.state" label="省/州" fullWidth />
        <TextInput source="profile.postalCode" label="邮政编码" fullWidth />
        <TextInput source="profile.country" label="国家" fullWidth />
      </Tab>
      
      <Tab label="权限管理">
        <ReferenceManyField
          reference="user-permissions"
          target="userId"
          label="用户权限"
        >
          <Datagrid>
            <TextField source="permission.name" label="权限名称" />
            <TextField source="permission.displayName" label="显示名称" />
            <TextField source="application.name" label="应用" />
            <DateField source="grantedAt" label="授予时间" showTime />
            <DateField source="expiresAt" label="过期时间" showTime />
            <BooleanField source="isActive" label="状态" />
          </Datagrid>
        </ReferenceManyField>
      </Tab>
    </TabbedForm>
  </Edit>
);

/**
 * 用户详情组件
 */
export const UserShow = () => (
  <Show>
    <TabbedShowLayout>
      <Tab label="基本信息">
        <Box display="flex" alignItems="center" mb={2}>
          <UserAvatar />
          <Box ml={2}>
            <Typography variant="h6">
              <TextField source="profile.displayName" />
            </Typography>
            <Typography variant="body2" color="text.secondary">
              @<TextField source="username" />
            </Typography>
          </Box>
        </Box>
        
        <EmailField source="email" label="邮箱" />
        <UserStatusChip />
        <BooleanField source="emailVerified" label="邮箱已验证" />
        <DateField source="lastLoginAt" label="最后登录" showTime />
        <DateField source="createdAt" label="创建时间" showTime />
        <DateField source="updatedAt" label="更新时间" showTime />
      </Tab>
      
      <Tab label="个人资料">
        <TextField source="profile.firstName" label="名" />
        <TextField source="profile.lastName" label="姓" />
        <TextField source="profile.phoneNumber" label="电话号码" />
        <TextField source="profile.website" label="网站" />
        <TextField source="profile.gender" label="性别" />
        <DateField source="profile.birthDate" label="出生日期" />
        <TextField source="profile.timezone" label="时区" />
        <TextField source="profile.locale" label="语言" />
      </Tab>
      
      <Tab label="地址信息">
        <TextField source="profile.address" label="地址" />
        <TextField source="profile.streetAddress" label="街道地址" />
        <TextField source="profile.city" label="城市" />
        <TextField source="profile.state" label="省/州" />
        <TextField source="profile.postalCode" label="邮政编码" />
        <TextField source="profile.country" label="国家" />
      </Tab>
      
      <Tab label="权限信息">
        <ReferenceManyField
          reference="user-permissions"
          target="userId"
          label="用户权限"
        >
          <Datagrid>
            <TextField source="permission.name" label="权限名称" />
            <TextField source="permission.displayName" label="显示名称" />
            <TextField source="application.name" label="应用" />
            <DateField source="grantedAt" label="授予时间" showTime />
            <DateField source="expiresAt" label="过期时间" showTime />
            <BooleanField source="isActive" label="状态" />
          </Datagrid>
        </ReferenceManyField>
      </Tab>
      
      <Tab label="活动记录">
        <ReferenceManyField
          reference="audit-logs"
          target="userId"
          label="用户活动"
          sort={{ field: 'createdAt', order: 'DESC' }}
        >
          <Datagrid>
            <TextField source="action" label="操作" />
            <TextField source="resource" label="资源" />
            <TextField source="ipAddress" label="IP地址" />
            <TextField source="userAgent" label="用户代理" />
            <DateField source="createdAt" label="时间" showTime />
          </Datagrid>
        </ReferenceManyField>
      </Tab>
    </TabbedShowLayout>
  </Show>
);
