/**
 * 应用管理资源组件
 * 提供应用的列表、编辑、创建和详情查看功能
 */

import React from 'react';
import {
  List,
  Datagrid,
  TextField,
  BooleanField,
  DateField,
  EditButton,
  ShowButton,
  DeleteButton,
  Create,
  Edit,
  Show,
  SimpleForm,
  SimpleShowLayout,
  TextInput,
  BooleanInput,
  required,
  Toolbar,
  SaveButton,
  DeleteButton as FormDeleteButton,
  useRecordContext,
  TopToolbar,
  CreateButton,
  ExportButton,
  FilterButton,
  SearchInput,
  SelectInput,
  ArrayInput,
  SimpleFormIterator,
  ChipField,
  ArrayField,
  SingleFieldList,
  Tab,
  TabbedForm,
  TabbedShowLayout,
  NumberField,
  UrlField
} from 'react-admin';
import {
  Card,
  CardContent,
  Typography,
  Box,
  Chip,
  Avatar
} from '@mui/material';
import {
  Apps as AppsIcon,
  Security as SecurityIcon,
  Link as LinkIcon,
  Code as CodeIcon
} from '@mui/icons-material';

/**
 * 应用列表过滤器
 */
const applicationFilters = [
  <SearchInput source="q" placeholder="搜索应用名称或描述" alwaysOn />,
  <SelectInput
    source="isActive"
    choices={[
      { id: true, name: '活跃' },
      { id: false, name: '禁用' },
    ]}
    placeholder="应用状态"
  />,
  <SelectInput
    source="applicationType"
    choices={[
      { id: 'web', name: 'Web应用' },
      { id: 'spa', name: '单页应用' },
      { id: 'native', name: '原生应用' },
      { id: 'machine', name: '机器对机器' },
    ]}
    placeholder="应用类型"
  />,
];

/**
 * 应用列表操作栏
 */
const ApplicationListActions = () => (
  <TopToolbar>
    <FilterButton />
    <CreateButton />
    <ExportButton />
  </TopToolbar>
);

/**
 * 应用图标组件
 */
const ApplicationIcon = () => {
  const record = useRecordContext();
  if (!record) return null;

  return (
    <Avatar
      src={record.logoUrl}
      sx={{ width: 40, height: 40 }}
    >
      <AppsIcon />
    </Avatar>
  );
};

/**
 * 应用状态芯片组件
 */
const ApplicationStatusChip = () => {
  const record = useRecordContext();
  if (!record) return null;

  return (
    <Chip
      label={record.isActive ? '活跃' : '禁用'}
      color={record.isActive ? 'success' : 'error'}
      size="small"
    />
  );
};

/**
 * 支持的协议芯片组件
 */
const ProtocolChips = () => {
  const record = useRecordContext();
  if (!record || !record.supportedProtocols) return null;

  return (
    <Box display="flex" gap={0.5}>
      {record.supportedProtocols.map((protocol: string) => (
        <Chip
          key={protocol}
          label={protocol.toUpperCase()}
          size="small"
          variant="outlined"
        />
      ))}
    </Box>
  );
};

/**
 * 应用列表组件
 */
export const ApplicationList = () => (
  <List
    filters={applicationFilters}
    actions={<ApplicationListActions />}
    sort={{ field: 'createdAt', order: 'DESC' }}
    perPage={25}
  >
    <Datagrid rowClick="show">
      <ApplicationIcon />
      <TextField source="name" label="应用名称" />
      <TextField source="clientId" label="客户端ID" />
      <TextField source="applicationType" label="应用类型" />
      <ProtocolChips />
      <ApplicationStatusChip />
      <DateField source="createdAt" label="创建时间" showTime />
      <EditButton />
      <ShowButton />
      <DeleteButton />
    </Datagrid>
  </List>
);

/**
 * 应用创建表单工具栏
 */
const ApplicationCreateToolbar = () => (
  <Toolbar>
    <SaveButton />
  </Toolbar>
);

/**
 * 应用创建组件
 */
export const ApplicationCreate = () => (
  <Create>
    <TabbedForm toolbar={<ApplicationCreateToolbar />}>
      <Tab label="基本信息">
        <TextInput
          source="name"
          label="应用名称"
          validate={[required()]}
          fullWidth
        />
        <TextInput
          source="description"
          label="应用描述"
          multiline
          fullWidth
        />
        <TextInput
          source="clientId"
          label="客户端ID"
          validate={[required()]}
          fullWidth
        />
        <TextInput
          source="clientSecret"
          label="客户端密钥"
          type="password"
          fullWidth
        />
        <SelectInput
          source="applicationType"
          label="应用类型"
          choices={[
            { id: 'web', name: 'Web应用' },
            { id: 'spa', name: '单页应用' },
            { id: 'native', name: '原生应用' },
            { id: 'machine', name: '机器对机器' },
          ]}
          validate={[required()]}
          fullWidth
        />
        <TextInput source="logoUrl" label="应用图标URL" fullWidth />
        <TextInput source="homepageUrl" label="主页URL" fullWidth />
        <BooleanInput source="isActive" label="启用应用" defaultValue={true} />
      </Tab>
      
      <Tab label="协议配置">
        <ArrayInput source="supportedProtocols" label="支持的协议">
          <SimpleFormIterator>
            <SelectInput
              choices={[
                { id: 'oidc', name: 'OpenID Connect' },
                { id: 'oauth2', name: 'OAuth 2.0' },
                { id: 'saml', name: 'SAML 2.0' },
              ]}
              fullWidth
            />
          </SimpleFormIterator>
        </ArrayInput>
        
        <ArrayInput source="redirectUris" label="重定向URI">
          <SimpleFormIterator>
            <TextInput fullWidth />
          </SimpleFormIterator>
        </ArrayInput>
        
        <ArrayInput source="postLogoutRedirectUris" label="登出后重定向URI">
          <SimpleFormIterator>
            <TextInput fullWidth />
          </SimpleFormIterator>
        </ArrayInput>
        
        <ArrayInput source="scopes" label="权限范围">
          <SimpleFormIterator>
            <TextInput fullWidth />
          </SimpleFormIterator>
        </ArrayInput>
      </Tab>
      
      <Tab label="OAuth/OIDC设置">
        <ArrayInput source="grantTypes" label="授权类型">
          <SimpleFormIterator>
            <SelectInput
              choices={[
                { id: 'authorization_code', name: '授权码' },
                { id: 'implicit', name: '隐式' },
                { id: 'refresh_token', name: '刷新令牌' },
                { id: 'client_credentials', name: '客户端凭据' },
              ]}
              fullWidth
            />
          </SimpleFormIterator>
        </ArrayInput>
        
        <ArrayInput source="responseTypes" label="响应类型">
          <SimpleFormIterator>
            <SelectInput
              choices={[
                { id: 'code', name: 'code' },
                { id: 'token', name: 'token' },
                { id: 'id_token', name: 'id_token' },
              ]}
              fullWidth
            />
          </SimpleFormIterator>
        </ArrayInput>
        
        <SelectInput
          source="tokenEndpointAuthMethod"
          label="令牌端点认证方法"
          choices={[
            { id: 'client_secret_basic', name: 'Basic认证' },
            { id: 'client_secret_post', name: 'POST参数' },
            { id: 'client_secret_jwt', name: 'JWT密钥' },
            { id: 'private_key_jwt', name: '私钥JWT' },
            { id: 'none', name: '无认证' },
          ]}
          fullWidth
        />
        
        <SelectInput
          source="subjectType"
          label="主题类型"
          choices={[
            { id: 'public', name: '公开' },
            { id: 'pairwise', name: '成对' },
          ]}
          fullWidth
        />
        
        <BooleanInput source="requireAuthTime" label="需要认证时间" />
        <BooleanInput source="requirePkce" label="需要PKCE" />
      </Tab>
      
      <Tab label="SAML设置">
        <TextInput source="samlConfig.acsUrl" label="断言消费服务URL" fullWidth />
        <TextInput source="samlConfig.sloUrl" label="单点登出URL" fullWidth />
        <TextInput source="samlConfig.certificate" label="证书" multiline fullWidth />
        <BooleanInput source="samlConfig.wantAssertionsSigned" label="需要断言签名" />
        <BooleanInput source="samlConfig.wantNameId" label="需要名称ID" />
        <BooleanInput source="samlConfig.signMetadata" label="签名元数据" />
        
        <ArrayInput source="samlConfig.requiredAttributes" label="必需属性">
          <SimpleFormIterator>
            <TextInput fullWidth />
          </SimpleFormIterator>
        </ArrayInput>
        
        <ArrayInput source="samlConfig.optionalAttributes" label="可选属性">
          <SimpleFormIterator>
            <TextInput fullWidth />
          </SimpleFormIterator>
        </ArrayInput>
      </Tab>
    </TabbedForm>
  </Create>
);

/**
 * 应用编辑表单工具栏
 */
const ApplicationEditToolbar = () => (
  <Toolbar>
    <SaveButton />
    <FormDeleteButton />
  </Toolbar>
);

/**
 * 应用编辑组件
 */
export const ApplicationEdit = () => (
  <Edit>
    <TabbedForm toolbar={<ApplicationEditToolbar />}>
      <Tab label="基本信息">
        <TextInput
          source="name"
          label="应用名称"
          validate={[required()]}
          fullWidth
        />
        <TextInput
          source="description"
          label="应用描述"
          multiline
          fullWidth
        />
        <TextInput
          source="clientId"
          label="客户端ID"
          validate={[required()]}
          fullWidth
        />
        <SelectInput
          source="applicationType"
          label="应用类型"
          choices={[
            { id: 'web', name: 'Web应用' },
            { id: 'spa', name: '单页应用' },
            { id: 'native', name: '原生应用' },
            { id: 'machine', name: '机器对机器' },
          ]}
          validate={[required()]}
          fullWidth
        />
        <TextInput source="logoUrl" label="应用图标URL" fullWidth />
        <TextInput source="homepageUrl" label="主页URL" fullWidth />
        <BooleanInput source="isActive" label="启用应用" />
        <DateField source="createdAt" label="创建时间" showTime />
        <DateField source="updatedAt" label="更新时间" showTime />
      </Tab>
      
      <Tab label="协议配置">
        <ArrayInput source="supportedProtocols" label="支持的协议">
          <SimpleFormIterator>
            <SelectInput
              choices={[
                { id: 'oidc', name: 'OpenID Connect' },
                { id: 'oauth2', name: 'OAuth 2.0' },
                { id: 'saml', name: 'SAML 2.0' },
              ]}
              fullWidth
            />
          </SimpleFormIterator>
        </ArrayInput>
        
        <ArrayInput source="redirectUris" label="重定向URI">
          <SimpleFormIterator>
            <TextInput fullWidth />
          </SimpleFormIterator>
        </ArrayInput>
        
        <ArrayInput source="postLogoutRedirectUris" label="登出后重定向URI">
          <SimpleFormIterator>
            <TextInput fullWidth />
          </SimpleFormIterator>
        </ArrayInput>
        
        <ArrayInput source="scopes" label="权限范围">
          <SimpleFormIterator>
            <TextInput fullWidth />
          </SimpleFormIterator>
        </ArrayInput>
      </Tab>
      
      <Tab label="OAuth/OIDC设置">
        <ArrayInput source="grantTypes" label="授权类型">
          <SimpleFormIterator>
            <SelectInput
              choices={[
                { id: 'authorization_code', name: '授权码' },
                { id: 'implicit', name: '隐式' },
                { id: 'refresh_token', name: '刷新令牌' },
                { id: 'client_credentials', name: '客户端凭据' },
              ]}
              fullWidth
            />
          </SimpleFormIterator>
        </ArrayInput>
        
        <ArrayInput source="responseTypes" label="响应类型">
          <SimpleFormIterator>
            <SelectInput
              choices={[
                { id: 'code', name: 'code' },
                { id: 'token', name: 'token' },
                { id: 'id_token', name: 'id_token' },
              ]}
              fullWidth
            />
          </SimpleFormIterator>
        </ArrayInput>
        
        <SelectInput
          source="tokenEndpointAuthMethod"
          label="令牌端点认证方法"
          choices={[
            { id: 'client_secret_basic', name: 'Basic认证' },
            { id: 'client_secret_post', name: 'POST参数' },
            { id: 'client_secret_jwt', name: 'JWT密钥' },
            { id: 'private_key_jwt', name: '私钥JWT' },
            { id: 'none', name: '无认证' },
          ]}
          fullWidth
        />
        
        <SelectInput
          source="subjectType"
          label="主题类型"
          choices={[
            { id: 'public', name: '公开' },
            { id: 'pairwise', name: '成对' },
          ]}
          fullWidth
        />
        
        <BooleanInput source="requireAuthTime" label="需要认证时间" />
        <BooleanInput source="requirePkce" label="需要PKCE" />
      </Tab>
      
      <Tab label="SAML设置">
        <TextInput source="samlConfig.acsUrl" label="断言消费服务URL" fullWidth />
        <TextInput source="samlConfig.sloUrl" label="单点登出URL" fullWidth />
        <TextInput source="samlConfig.certificate" label="证书" multiline fullWidth />
        <BooleanInput source="samlConfig.wantAssertionsSigned" label="需要断言签名" />
        <BooleanInput source="samlConfig.wantNameId" label="需要名称ID" />
        <BooleanInput source="samlConfig.signMetadata" label="签名元数据" />
        
        <ArrayInput source="samlConfig.requiredAttributes" label="必需属性">
          <SimpleFormIterator>
            <TextInput fullWidth />
          </SimpleFormIterator>
        </ArrayInput>
        
        <ArrayInput source="samlConfig.optionalAttributes" label="可选属性">
          <SimpleFormIterator>
            <TextInput fullWidth />
          </SimpleFormIterator>
        </ArrayInput>
      </Tab>
    </TabbedForm>
  </Edit>
);

/**
 * 应用详情组件
 */
export const ApplicationShow = () => (
  <Show>
    <TabbedShowLayout>
      <Tab label="基本信息">
        <Box display="flex" alignItems="center" mb={2}>
          <ApplicationIcon />
          <Box ml={2}>
            <Typography variant="h6">
              <TextField source="name" />
            </Typography>
            <Typography variant="body2" color="text.secondary">
              <TextField source="description" />
            </Typography>
          </Box>
        </Box>
        
        <TextField source="clientId" label="客户端ID" />
        <TextField source="applicationType" label="应用类型" />
        <UrlField source="homepageUrl" label="主页URL" />
        <ApplicationStatusChip />
        <DateField source="createdAt" label="创建时间" showTime />
        <DateField source="updatedAt" label="更新时间" showTime />
      </Tab>
      
      <Tab label="协议配置">
        <ArrayField source="supportedProtocols" label="支持的协议">
          <SingleFieldList>
            <ChipField />
          </SingleFieldList>
        </ArrayField>
        
        <ArrayField source="redirectUris" label="重定向URI">
          <SingleFieldList>
            <UrlField />
          </SingleFieldList>
        </ArrayField>
        
        <ArrayField source="scopes" label="权限范围">
          <SingleFieldList>
            <ChipField />
          </SingleFieldList>
        </ArrayField>
      </Tab>
      
      <Tab label="OAuth/OIDC设置">
        <ArrayField source="grantTypes" label="授权类型">
          <SingleFieldList>
            <ChipField />
          </SingleFieldList>
        </ArrayField>
        
        <ArrayField source="responseTypes" label="响应类型">
          <SingleFieldList>
            <ChipField />
          </SingleFieldList>
        </ArrayField>
        
        <TextField source="tokenEndpointAuthMethod" label="令牌端点认证方法" />
        <TextField source="subjectType" label="主题类型" />
        <BooleanField source="requireAuthTime" label="需要认证时间" />
        <BooleanField source="requirePkce" label="需要PKCE" />
      </Tab>
      
      <Tab label="SAML设置">
        <TextField source="samlConfig.acsUrl" label="断言消费服务URL" />
        <TextField source="samlConfig.sloUrl" label="单点登出URL" />
        <BooleanField source="samlConfig.wantAssertionsSigned" label="需要断言签名" />
        <BooleanField source="samlConfig.wantNameId" label="需要名称ID" />
        <BooleanField source="samlConfig.signMetadata" label="签名元数据" />
        
        <ArrayField source="samlConfig.requiredAttributes" label="必需属性">
          <SingleFieldList>
            <ChipField />
          </SingleFieldList>
        </ArrayField>
        
        <ArrayField source="samlConfig.optionalAttributes" label="可选属性">
          <SingleFieldList>
            <ChipField />
          </SingleFieldList>
        </ArrayField>
      </Tab>
    </TabbedShowLayout>
  </Show>
);
