/**
 * JWT工具函数测试
 */

import {
  generateAccessToken,
  generateRefreshToken,
  verifyAccessToken,
  verifyRefreshToken,
  generateTokenPair,
  extractTokenFromHeader,
  getTokenExpiration,
  isTokenExpired
} from '../jwt';
import { logger } from '@/config/logger';

// Mock logger
jest.mock('@/config/logger', () => ({
  logger: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn()
  }
}));

// Mock环境变量
const originalEnv = process.env;

describe('JWT工具函数', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    process.env = {
      ...originalEnv,
      JWT_SECRET: 'test-secret-key',
      JWT_REFRESH_SECRET: 'test-refresh-secret-key',
      JWT_EXPIRES_IN: '15m',
      JWT_REFRESH_EXPIRES_IN: '7d'
    };
  });

  afterAll(() => {
    process.env = originalEnv;
  });

  describe('访问令牌生成和验证', () => {
    test('应该能够生成访问令牌', () => {
      const payload = {
        sub: 'user-123',
        email: '<EMAIL>',
        roles: ['user']
      };

      const token = generateAccessToken(payload);

      expect(typeof token).toBe('string');
      expect(token.split('.')).toHaveLength(3); // JWT格式检查
    });

    test('应该能够验证有效的访问令牌', () => {
      const payload = {
        sub: 'user-123',
        email: '<EMAIL>',
        roles: ['user']
      };

      const token = generateAccessToken(payload);
      const decoded = verifyAccessToken(token);

      expect(decoded.sub).toBe(payload.sub);
      expect(decoded.email).toBe(payload.email);
      expect(decoded.roles).toEqual(payload.roles);
      expect(decoded.iat).toBeDefined();
      expect(decoded.exp).toBeDefined();
    });

    test('应该拒绝无效的访问令牌', () => {
      const invalidToken = 'invalid.token.here';

      expect(() => verifyAccessToken(invalidToken)).toThrow();
    });

    test('应该拒绝过期的访问令牌', () => {
      // 设置很短的过期时间
      process.env.JWT_EXPIRES_IN = '1ms';
      
      const payload = {
        sub: 'user-123',
        email: '<EMAIL>'
      };

      const token = generateAccessToken(payload);
      
      // 等待令牌过期
      return new Promise((resolve) => {
        setTimeout(() => {
          expect(() => verifyAccessToken(token)).toThrow();
          resolve(undefined);
        }, 10);
      });
    });
  });

  describe('刷新令牌生成和验证', () => {
    test('应该能够生成刷新令牌', () => {
      const payload = {
        sub: 'user-123',
        tokenType: 'refresh'
      };

      const token = generateRefreshToken(payload);

      expect(typeof token).toBe('string');
      expect(token.split('.')).toHaveLength(3);
    });

    test('应该能够验证有效的刷新令牌', () => {
      const payload = {
        sub: 'user-123',
        tokenType: 'refresh'
      };

      const token = generateRefreshToken(payload);
      const decoded = verifyRefreshToken(token);

      expect(decoded.sub).toBe(payload.sub);
      expect(decoded.tokenType).toBe(payload.tokenType);
    });

    test('应该拒绝无效的刷新令牌', () => {
      const invalidToken = 'invalid.refresh.token';

      expect(() => verifyRefreshToken(invalidToken)).toThrow();
    });
  });

  describe('令牌对生成', () => {
    test('应该能够生成令牌对', () => {
      const payload = {
        sub: 'user-123',
        email: '<EMAIL>',
        roles: ['user']
      };

      const tokenPair = generateTokenPair(payload);

      expect(tokenPair).toHaveProperty('accessToken');
      expect(tokenPair).toHaveProperty('refreshToken');
      expect(tokenPair).toHaveProperty('expiresIn');
      expect(tokenPair).toHaveProperty('tokenType', 'Bearer');

      // 验证生成的令牌
      const accessDecoded = verifyAccessToken(tokenPair.accessToken);
      const refreshDecoded = verifyRefreshToken(tokenPair.refreshToken);

      expect(accessDecoded.sub).toBe(payload.sub);
      expect(refreshDecoded.sub).toBe(payload.sub);
    });

    test('应该包含正确的过期时间', () => {
      const payload = { sub: 'user-123' };
      const tokenPair = generateTokenPair(payload);

      expect(tokenPair.expiresIn).toBe(15 * 60); // 15分钟转换为秒
    });
  });

  describe('令牌提取', () => {
    test('应该能够从Authorization头提取Bearer令牌', () => {
      const token = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.test.token';
      const authHeader = `Bearer ${token}`;

      const extracted = extractTokenFromHeader(authHeader);

      expect(extracted).toBe(token);
    });

    test('应该处理不正确的Authorization头格式', () => {
      const invalidHeaders = [
        'Basic dXNlcjpwYXNz',
        'Bearer',
        'Token abc123',
        '',
        undefined
      ];

      invalidHeaders.forEach(header => {
        const extracted = extractTokenFromHeader(header);
        expect(extracted).toBeNull();
      });
    });

    test('应该处理Bearer前缀大小写不敏感', () => {
      const token = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.test.token';
      const authHeaders = [
        `bearer ${token}`,
        `BEARER ${token}`,
        `Bearer ${token}`,
        `BeArEr ${token}`
      ];

      authHeaders.forEach(header => {
        const extracted = extractTokenFromHeader(header);
        expect(extracted).toBe(token);
      });
    });
  });

  describe('令牌过期检查', () => {
    test('应该能够获取令牌过期时间', () => {
      const payload = { sub: 'user-123' };
      const token = generateAccessToken(payload);

      const expiration = getTokenExpiration(token);

      expect(expiration).toBeInstanceOf(Date);
      expect(expiration.getTime()).toBeGreaterThan(Date.now());
    });

    test('应该能够检查令牌是否过期', () => {
      const payload = { sub: 'user-123' };
      const token = generateAccessToken(payload);

      const isExpired = isTokenExpired(token);

      expect(isExpired).toBe(false);
    });

    test('应该正确识别过期的令牌', () => {
      // 创建一个已过期的令牌（手动设置过期时间）
      const jwt = require('jsonwebtoken');
      const expiredToken = jwt.sign(
        { sub: 'user-123' },
        process.env.JWT_SECRET,
        { expiresIn: '-1h' } // 1小时前过期
      );

      const isExpired = isTokenExpired(expiredToken);

      expect(isExpired).toBe(true);
    });

    test('应该处理无效令牌的过期检查', () => {
      const invalidToken = 'invalid.token.here';

      expect(() => getTokenExpiration(invalidToken)).toThrow();
      expect(() => isTokenExpired(invalidToken)).toThrow();
    });
  });

  describe('令牌载荷验证', () => {
    test('应该包含标准JWT声明', () => {
      const payload = {
        sub: 'user-123',
        email: '<EMAIL>'
      };

      const token = generateAccessToken(payload);
      const decoded = verifyAccessToken(token);

      expect(decoded.sub).toBe(payload.sub);
      expect(decoded.email).toBe(payload.email);
      expect(decoded.iat).toBeDefined(); // issued at
      expect(decoded.exp).toBeDefined(); // expires at
      expect(decoded.iss).toBeDefined(); // issuer
    });

    test('应该支持自定义载荷字段', () => {
      const payload = {
        sub: 'user-123',
        email: '<EMAIL>',
        roles: ['admin', 'user'],
        permissions: ['read', 'write'],
        customField: 'custom-value'
      };

      const token = generateAccessToken(payload);
      const decoded = verifyAccessToken(token);

      expect(decoded.roles).toEqual(payload.roles);
      expect(decoded.permissions).toEqual(payload.permissions);
      expect(decoded.customField).toBe(payload.customField);
    });
  });

  describe('错误处理', () => {
    test('应该处理缺失的JWT密钥', () => {
      delete process.env.JWT_SECRET;

      expect(() => {
        generateAccessToken({ sub: 'user-123' });
      }).toThrow();
    });

    test('应该处理缺失的刷新令牌密钥', () => {
      delete process.env.JWT_REFRESH_SECRET;

      expect(() => {
        generateRefreshToken({ sub: 'user-123' });
      }).toThrow();
    });

    test('应该记录令牌验证错误', () => {
      const invalidToken = 'invalid.token.here';

      expect(() => verifyAccessToken(invalidToken)).toThrow();
      expect(logger.error).toHaveBeenCalledWith('JWT验证失败', {
        error: expect.any(String)
      });
    });
  });

  describe('令牌安全性', () => {
    test('不同的载荷应该生成不同的令牌', () => {
      const payload1 = { sub: 'user-123' };
      const payload2 = { sub: 'user-456' };

      const token1 = generateAccessToken(payload1);
      const token2 = generateAccessToken(payload2);

      expect(token1).not.toBe(token2);
    });

    test('相同载荷在不同时间生成的令牌应该不同', () => {
      const payload = { sub: 'user-123' };

      const token1 = generateAccessToken(payload);
      
      // 等待一毫秒确保时间戳不同
      return new Promise((resolve) => {
        setTimeout(() => {
          const token2 = generateAccessToken(payload);
          expect(token1).not.toBe(token2);
          resolve(undefined);
        }, 1);
      });
    });

    test('应该使用不同的密钥签名访问令牌和刷新令牌', () => {
      const payload = { sub: 'user-123' };

      const accessToken = generateAccessToken(payload);
      const refreshToken = generateRefreshToken(payload);

      // 尝试用错误的密钥验证应该失败
      expect(() => verifyRefreshToken(accessToken)).toThrow();
      expect(() => verifyAccessToken(refreshToken)).toThrow();
    });
  });
});
