/**
 * OpenID Connect 验证工具函数
 * 验证OAuth 2.0和OpenID Connect请求参数
 */

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
}

/**
 * 验证授权请求参数
 */
export function validateAuthorizationRequest(params: {
  response_type?: string;
  client_id?: string;
  redirect_uri?: string;
  scope?: string;
}): ValidationResult {
  const errors: string[] = [];

  // 验证response_type
  if (!params.response_type) {
    errors.push('缺少response_type参数');
  } else {
    const validResponseTypes = [
      'code',
      'token',
      'id_token',
      'code token',
      'code id_token',
      'token id_token',
      'code token id_token'
    ];
    
    if (!validResponseTypes.includes(params.response_type)) {
      errors.push(`无效的response_type: ${params.response_type}`);
    }
  }

  // 验证client_id
  if (!params.client_id) {
    errors.push('缺少client_id参数');
  } else if (typeof params.client_id !== 'string' || params.client_id.trim().length === 0) {
    errors.push('client_id必须是非空字符串');
  }

  // 验证redirect_uri
  if (!params.redirect_uri) {
    errors.push('缺少redirect_uri参数');
  } else {
    try {
      const url = new URL(params.redirect_uri);
      
      // 检查协议
      if (!['http:', 'https:'].includes(url.protocol)) {
        errors.push('redirect_uri必须使用http或https协议');
      }
      
      // 检查是否包含fragment
      if (url.hash) {
        errors.push('redirect_uri不能包含fragment');
      }
      
    } catch (error) {
      errors.push('redirect_uri格式无效');
    }
  }

  // 验证scope
  if (!params.scope) {
    errors.push('缺少scope参数');
  } else {
    const scopes = params.scope.split(' ');
    const validScopes = [
      'openid',
      'profile',
      'email',
      'address',
      'phone',
      'offline_access'
    ];
    
    const invalidScopes = scopes.filter(scope => !validScopes.includes(scope));
    if (invalidScopes.length > 0) {
      errors.push(`无效的scope: ${invalidScopes.join(', ')}`);
    }
    
    // 如果是OpenID Connect请求，必须包含openid scope
    if (params.response_type?.includes('id_token') && !scopes.includes('openid')) {
      errors.push('OpenID Connect请求必须包含openid scope');
    }
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * 验证令牌请求参数
 */
export function validateTokenRequest(params: {
  grant_type?: string;
  code?: string;
  redirect_uri?: string;
  client_id?: string;
  refresh_token?: string;
}): ValidationResult {
  const errors: string[] = [];

  // 验证grant_type
  if (!params.grant_type) {
    errors.push('缺少grant_type参数');
  } else {
    const validGrantTypes = [
      'authorization_code',
      'refresh_token',
      'client_credentials',
      'password'
    ];
    
    if (!validGrantTypes.includes(params.grant_type)) {
      errors.push(`无效的grant_type: ${params.grant_type}`);
    }
  }

  // 验证client_id
  if (!params.client_id) {
    errors.push('缺少client_id参数');
  }

  // 根据grant_type验证特定参数
  switch (params.grant_type) {
    case 'authorization_code':
      if (!params.code) {
        errors.push('authorization_code流程缺少code参数');
      }
      if (!params.redirect_uri) {
        errors.push('authorization_code流程缺少redirect_uri参数');
      }
      break;

    case 'refresh_token':
      if (!params.refresh_token) {
        errors.push('refresh_token流程缺少refresh_token参数');
      }
      break;

    case 'client_credentials':
      // 客户端凭据流程不需要额外参数
      break;

    case 'password':
      // 密码流程需要username和password，但这里不验证敏感信息
      break;
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * 验证PKCE参数
 */
export function validatePKCE(params: {
  code_challenge?: string;
  code_challenge_method?: string;
  code_verifier?: string;
}): ValidationResult {
  const errors: string[] = [];

  if (params.code_challenge) {
    // 验证code_challenge_method
    if (!params.code_challenge_method) {
      errors.push('使用PKCE时必须提供code_challenge_method');
    } else if (!['plain', 'S256'].includes(params.code_challenge_method)) {
      errors.push('code_challenge_method必须是plain或S256');
    }

    // 验证code_challenge格式
    if (params.code_challenge_method === 'S256') {
      // S256方法的challenge应该是base64url编码的
      const base64urlPattern = /^[A-Za-z0-9_-]+$/;
      if (!base64urlPattern.test(params.code_challenge)) {
        errors.push('S256方法的code_challenge必须是base64url编码');
      }
      
      if (params.code_challenge.length !== 43) {
        errors.push('S256方法的code_challenge长度必须是43字符');
      }
    }
  }

  if (params.code_verifier) {
    // 验证code_verifier格式
    const verifierPattern = /^[A-Za-z0-9._~-]+$/;
    if (!verifierPattern.test(params.code_verifier)) {
      errors.push('code_verifier包含无效字符');
    }
    
    if (params.code_verifier.length < 43 || params.code_verifier.length > 128) {
      errors.push('code_verifier长度必须在43-128字符之间');
    }
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * 验证JWT声明
 */
export function validateJWTClaims(claims: any): ValidationResult {
  const errors: string[] = [];

  // 验证必需的声明
  const requiredClaims = ['iss', 'sub', 'aud', 'exp', 'iat'];
  
  for (const claim of requiredClaims) {
    if (!claims[claim]) {
      errors.push(`缺少必需的声明: ${claim}`);
    }
  }

  // 验证时间声明
  const now = Math.floor(Date.now() / 1000);
  
  if (claims.exp && claims.exp <= now) {
    errors.push('令牌已过期');
  }
  
  if (claims.nbf && claims.nbf > now) {
    errors.push('令牌尚未生效');
  }
  
  if (claims.iat && claims.iat > now + 60) {
    errors.push('令牌发行时间无效');
  }

  // 验证发行者
  if (claims.iss && typeof claims.iss !== 'string') {
    errors.push('发行者(iss)必须是字符串');
  }

  // 验证主题
  if (claims.sub && typeof claims.sub !== 'string') {
    errors.push('主题(sub)必须是字符串');
  }

  // 验证受众
  if (claims.aud) {
    if (typeof claims.aud !== 'string' && !Array.isArray(claims.aud)) {
      errors.push('受众(aud)必须是字符串或字符串数组');
    }
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * 验证权限范围
 */
export function validateScopes(requestedScopes: string[], allowedScopes: string[]): ValidationResult {
  const errors: string[] = [];

  if (!Array.isArray(requestedScopes)) {
    errors.push('请求的权限范围必须是数组');
    return { isValid: false, errors };
  }

  if (!Array.isArray(allowedScopes)) {
    errors.push('允许的权限范围必须是数组');
    return { isValid: false, errors };
  }

  // 检查是否有无效的权限范围
  const invalidScopes = requestedScopes.filter(scope => !allowedScopes.includes(scope));
  
  if (invalidScopes.length > 0) {
    errors.push(`无效的权限范围: ${invalidScopes.join(', ')}`);
  }

  // 检查权限范围格式
  const scopePattern = /^[a-zA-Z0-9_.-]+$/;
  const malformedScopes = requestedScopes.filter(scope => !scopePattern.test(scope));
  
  if (malformedScopes.length > 0) {
    errors.push(`权限范围格式无效: ${malformedScopes.join(', ')}`);
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * 验证客户端信息
 */
export function validateClientInfo(client: {
  clientId?: string;
  clientSecret?: string;
  redirectUris?: string[];
  grantTypes?: string[];
  responseTypes?: string[];
}): ValidationResult {
  const errors: string[] = [];

  // 验证客户端ID
  if (!client.clientId) {
    errors.push('缺少客户端ID');
  } else if (typeof client.clientId !== 'string' || client.clientId.length < 1) {
    errors.push('客户端ID必须是非空字符串');
  }

  // 验证重定向URI
  if (!client.redirectUris || !Array.isArray(client.redirectUris)) {
    errors.push('重定向URI必须是数组');
  } else {
    for (const uri of client.redirectUris) {
      try {
        const url = new URL(uri);
        if (!['http:', 'https:'].includes(url.protocol)) {
          errors.push(`重定向URI必须使用http或https协议: ${uri}`);
        }
      } catch (error) {
        errors.push(`无效的重定向URI格式: ${uri}`);
      }
    }
  }

  // 验证授权类型
  if (client.grantTypes && Array.isArray(client.grantTypes)) {
    const validGrantTypes = ['authorization_code', 'refresh_token', 'client_credentials', 'password'];
    const invalidGrantTypes = client.grantTypes.filter(type => !validGrantTypes.includes(type));
    
    if (invalidGrantTypes.length > 0) {
      errors.push(`无效的授权类型: ${invalidGrantTypes.join(', ')}`);
    }
  }

  // 验证响应类型
  if (client.responseTypes && Array.isArray(client.responseTypes)) {
    const validResponseTypes = [
      'code',
      'token',
      'id_token',
      'code token',
      'code id_token',
      'token id_token',
      'code token id_token'
    ];
    const invalidResponseTypes = client.responseTypes.filter(type => !validResponseTypes.includes(type));

    if (invalidResponseTypes.length > 0) {
      errors.push(`无效的响应类型: ${invalidResponseTypes.join(', ')}`);
    }
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * 验证重定向URI匹配
 */
export function validateRedirectUriMatch(
  requestedUri: string,
  registeredUris: string[]
): ValidationResult {
  const errors: string[] = [];

  if (!registeredUris.includes(requestedUri)) {
    errors.push('重定向URI与注册的URI不匹配');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}
