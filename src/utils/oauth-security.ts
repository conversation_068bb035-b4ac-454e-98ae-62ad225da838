/**
 * OAuth安全工具类
 * 提供OAuth 2.0认证过程中的安全功能
 */

import crypto from 'crypto';
import { logger } from '@/config/logger';

/**
 * OAuth状态信息接口
 */
export interface OAuthState {
  state: string;
  codeVerifier?: string;
  timestamp: number;
  provider: string;
  redirectUrl?: string;
}

/**
 * OAuth安全工具类
 */
export class OAuthSecurity {
  private static readonly STATE_EXPIRY_TIME = 10 * 60 * 1000; // 10分钟
  private static readonly CODE_VERIFIER_LENGTH = 128;
  private static readonly STATE_LENGTH = 32;

  /**
   * 生成安全的随机状态参数
   */
  static generateState(): string {
    return crypto.randomBytes(this.STATE_LENGTH).toString('hex');
  }

  /**
   * 生成PKCE代码验证器
   */
  static generateCodeVerifier(): string {
    return crypto.randomBytes(this.CODE_VERIFIER_LENGTH).toString('base64url');
  }

  /**
   * 生成PKCE代码挑战
   */
  static generateCodeChallenge(codeVerifier: string): string {
    return crypto
      .createHash('sha256')
      .update(codeVerifier)
      .digest('base64url');
  }

  /**
   * 创建OAuth状态对象
   */
  static createOAuthState(provider: string, redirectUrl?: string): OAuthState {
    return {
      state: this.generateState(),
      codeVerifier: this.generateCodeVerifier(),
      timestamp: Date.now(),
      provider,
      redirectUrl
    };
  }

  /**
   * 验证OAuth状态参数
   */
  static validateState(
    receivedState: string,
    storedState: OAuthState
  ): { isValid: boolean; reason?: string } {
    // 检查状态参数是否匹配
    if (receivedState !== storedState.state) {
      logger.warn('OAuth状态参数不匹配', {
        received: receivedState,
        expected: storedState.state,
        provider: storedState.provider
      });
      return { isValid: false, reason: 'state_mismatch' };
    }

    // 检查状态是否过期
    const now = Date.now();
    if (now - storedState.timestamp > this.STATE_EXPIRY_TIME) {
      logger.warn('OAuth状态参数已过期', {
        timestamp: storedState.timestamp,
        now,
        provider: storedState.provider
      });
      return { isValid: false, reason: 'state_expired' };
    }

    return { isValid: true };
  }

  /**
   * 验证PKCE代码验证器
   */
  static validateCodeVerifier(
    codeVerifier: string,
    codeChallenge: string
  ): boolean {
    const computedChallenge = this.generateCodeChallenge(codeVerifier);
    return computedChallenge === codeChallenge;
  }

  /**
   * 生成安全的随机字符串
   */
  static generateSecureRandom(length: number = 32): string {
    return crypto.randomBytes(length).toString('hex');
  }

  /**
   * 创建HMAC签名
   */
  static createHMACSignature(data: string, secret: string): string {
    return crypto
      .createHmac('sha256', secret)
      .update(data)
      .digest('hex');
  }

  /**
   * 验证HMAC签名
   */
  static verifyHMACSignature(
    data: string,
    signature: string,
    secret: string
  ): boolean {
    try {
      const expectedSignature = this.createHMACSignature(data, secret);

      // 确保两个签名长度相同
      if (signature.length !== expectedSignature.length) {
        return false;
      }

      return crypto.timingSafeEqual(
        Buffer.from(signature, 'hex'),
        Buffer.from(expectedSignature, 'hex')
      );
    } catch (error) {
      logger.error('HMAC签名验证失败', { error });
      return false;
    }
  }

  /**
   * 加密敏感数据
   */
  static encryptSensitiveData(data: string, key: string): string {
    try {
      const algorithm = 'aes-256-gcm';
      const iv = crypto.randomBytes(16);
      const cipher = crypto.createCipher(algorithm, key);
      
      let encrypted = cipher.update(data, 'utf8', 'hex');
      encrypted += cipher.final('hex');
      
      const authTag = cipher.getAuthTag();
      
      return `${iv.toString('hex')}:${authTag.toString('hex')}:${encrypted}`;
    } catch (error) {
      logger.error('加密敏感数据失败', { error });
      throw new Error('数据加密失败');
    }
  }

  /**
   * 解密敏感数据
   */
  static decryptSensitiveData(encryptedData: string, key: string): string {
    try {
      const algorithm = 'aes-256-gcm';
      const parts = encryptedData.split(':');
      
      if (parts.length !== 3) {
        throw new Error('无效的加密数据格式');
      }
      
      const iv = Buffer.from(parts[0], 'hex');
      const authTag = Buffer.from(parts[1], 'hex');
      const encrypted = parts[2];
      
      const decipher = crypto.createDecipher(algorithm, key);
      decipher.setAuthTag(authTag);
      
      let decrypted = decipher.update(encrypted, 'hex', 'utf8');
      decrypted += decipher.final('utf8');
      
      return decrypted;
    } catch (error) {
      logger.error('解密敏感数据失败', { error });
      throw new Error('数据解密失败');
    }
  }

  /**
   * 验证回调URL是否安全
   */
  static validateCallbackUrl(url: string, allowedDomains: string[]): boolean {
    try {
      const parsedUrl = new URL(url);
      
      // 检查协议是否为HTTPS（生产环境）
      if (process.env.NODE_ENV === 'production' && parsedUrl.protocol !== 'https:') {
        logger.warn('生产环境下回调URL必须使用HTTPS', { url });
        return false;
      }
      
      // 检查域名是否在允许列表中
      const hostname = parsedUrl.hostname;
      const isAllowed = allowedDomains.some(domain => {
        // 支持通配符子域名
        if (domain.startsWith('*.')) {
          const baseDomain = domain.substring(2);
          return hostname.endsWith(baseDomain);
        }
        return hostname === domain;
      });
      
      if (!isAllowed) {
        logger.warn('回调URL域名不在允许列表中', { url, hostname, allowedDomains });
        return false;
      }
      
      return true;
    } catch (error) {
      logger.error('验证回调URL失败', { error, url });
      return false;
    }
  }

  /**
   * 生成防CSRF令牌
   */
  static generateCSRFToken(): string {
    return this.generateSecureRandom(32);
  }

  /**
   * 验证防CSRF令牌
   */
  static validateCSRFToken(token: string, expectedToken: string): boolean {
    if (!token || !expectedToken) {
      return false;
    }
    
    return crypto.timingSafeEqual(
      Buffer.from(token, 'hex'),
      Buffer.from(expectedToken, 'hex')
    );
  }

  /**
   * 清理和验证用户输入
   */
  static sanitizeInput(input: string): string {
    if (!input || typeof input !== 'string') {
      return '';
    }
    
    // 移除潜在的恶意字符
    return input
      .replace(/[<>\"'&]/g, '') // 移除HTML特殊字符
      .replace(/javascript:/gi, '') // 移除JavaScript协议
      .replace(/data:/gi, '') // 移除data协议
      .trim();
  }

  /**
   * 验证OAuth提供商名称
   */
  static validateProviderName(provider: string): boolean {
    const allowedProviders = [
      'google', 'github', 'wechat', 'weibo', 
      'qq', 'alipay', 'dingtalk'
    ];
    
    return allowedProviders.includes(provider.toLowerCase());
  }

  /**
   * 记录安全事件
   */
  static logSecurityEvent(
    event: string,
    details: Record<string, any>,
    severity: 'low' | 'medium' | 'high' = 'medium'
  ): void {
    logger.warn(`OAuth安全事件: ${event}`, {
      event,
      severity,
      timestamp: new Date().toISOString(),
      ...details
    });
  }
}
