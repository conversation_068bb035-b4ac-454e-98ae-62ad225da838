/**
 * 数据库查询分析工具
 * 提供查询性能分析、优化建议和慢查询检测功能
 */

import { PrismaClient } from '@prisma/client'
import { logger } from '@/config/logger'

export interface QueryAnalysis {
  query: string
  executionTime: number
  planningTime: number
  totalTime: number
  rowsReturned: number
  bufferHits: number
  bufferReads: number
  isSlowQuery: boolean
  optimizationSuggestions: string[]
}

export interface IndexSuggestion {
  table: string
  columns: string[]
  reason: string
  estimatedImprovement: string
  priority: 'high' | 'medium' | 'low'
}

export interface QueryPattern {
  pattern: string
  frequency: number
  avgExecutionTime: number
  tables: string[]
  suggestedIndexes: IndexSuggestion[]
}

/**
 * 查询分析器类
 */
export class QueryAnalyzer {
  private prisma: PrismaClient
  private slowQueryThreshold: number = 1000 // 毫秒
  private queryHistory: QueryAnalysis[] = []
  private maxHistorySize: number = 1000

  constructor(prisma: PrismaClient) {
    this.prisma = prisma
  }

  /**
   * 分析查询性能
   */
  async analyzeQuery(sql: string, params?: any[]): Promise<QueryAnalysis> {
    try {
      const startTime = Date.now()
      
      // 执行EXPLAIN ANALYZE
      const explainResult = await this.prisma.$queryRawUnsafe(
        `EXPLAIN (ANALYZE, BUFFERS, FORMAT JSON) ${sql}`,
        ...(params || [])
      ) as any[]

      const endTime = Date.now()
      const totalTime = endTime - startTime

      const plan = explainResult[0]['QUERY PLAN'][0]
      const executionTime = plan['Execution Time'] || 0
      const planningTime = plan['Planning Time'] || 0

      const analysis: QueryAnalysis = {
        query: sql,
        executionTime,
        planningTime,
        totalTime,
        rowsReturned: plan['Actual Rows'] || 0,
        bufferHits: this.extractBufferHits(plan),
        bufferReads: this.extractBufferReads(plan),
        isSlowQuery: totalTime > this.slowQueryThreshold,
        optimizationSuggestions: this.generateOptimizationSuggestions(plan, sql)
      }

      // 添加到历史记录
      this.addToHistory(analysis)

      // 记录慢查询
      if (analysis.isSlowQuery) {
        logger.warn('慢查询检测', {
          query: sql,
          executionTime: analysis.executionTime,
          totalTime: analysis.totalTime,
          suggestions: analysis.optimizationSuggestions
        })
      }

      return analysis

    } catch (error) {
      logger.error('查询分析失败', {
        query: sql,
        error: error instanceof Error ? error.message : String(error)
      })
      throw error
    }
  }

  /**
   * 检测缺失的索引
   */
  async detectMissingIndexes(): Promise<IndexSuggestion[]> {
    try {
      const suggestions: IndexSuggestion[] = []

      // 分析pg_stat_user_tables获取表统计信息
      const tableStats = await this.prisma.$queryRaw`
        SELECT 
          schemaname,
          tablename,
          seq_scan,
          seq_tup_read,
          idx_scan,
          idx_tup_fetch,
          n_tup_ins,
          n_tup_upd,
          n_tup_del
        FROM pg_stat_user_tables
        WHERE schemaname = 'public'
        ORDER BY seq_scan DESC
      ` as any[]

      // 分析高顺序扫描的表
      for (const table of tableStats) {
        if (table.seq_scan > 1000 && table.seq_tup_read > 10000) {
          suggestions.push({
            table: table.tablename,
            columns: await this.suggestColumnsForIndex(table.tablename),
            reason: `表 ${table.tablename} 有 ${table.seq_scan} 次顺序扫描，读取了 ${table.seq_tup_read} 行`,
            estimatedImprovement: '可能提升 50-80% 的查询性能',
            priority: 'high'
          })
        }
      }

      // 分析慢查询模式
      const slowQueryPatterns = this.analyzeSlowQueryPatterns()
      for (const pattern of slowQueryPatterns) {
        suggestions.push(...pattern.suggestedIndexes)
      }

      return suggestions

    } catch (error) {
      logger.error('检测缺失索引失败', {
        error: error instanceof Error ? error.message : String(error)
      })
      return []
    }
  }

  /**
   * 分析查询模式
   */
  analyzeSlowQueryPatterns(): QueryPattern[] {
    const patterns: Map<string, QueryPattern> = new Map()

    // 分析查询历史中的慢查询
    const slowQueries = this.queryHistory.filter(q => q.isSlowQuery)

    for (const query of slowQueries) {
      const pattern = this.extractQueryPattern(query.query)
      
      if (patterns.has(pattern)) {
        const existing = patterns.get(pattern)!
        existing.frequency++
        existing.avgExecutionTime = (existing.avgExecutionTime + query.executionTime) / 2
      } else {
        patterns.set(pattern, {
          pattern,
          frequency: 1,
          avgExecutionTime: query.executionTime,
          tables: this.extractTablesFromQuery(query.query),
          suggestedIndexes: this.suggestIndexesForPattern(pattern, query.query)
        })
      }
    }

    return Array.from(patterns.values())
      .filter(p => p.frequency > 1) // 只返回出现多次的模式
      .sort((a, b) => b.frequency - a.frequency)
  }

  /**
   * 获取查询统计信息
   */
  async getQueryStats(): Promise<{
    totalQueries: number
    slowQueries: number
    avgExecutionTime: number
    topSlowQueries: QueryAnalysis[]
  }> {
    const totalQueries = this.queryHistory.length
    const slowQueries = this.queryHistory.filter(q => q.isSlowQuery).length
    const avgExecutionTime = this.queryHistory.length > 0
      ? this.queryHistory.reduce((sum, q) => sum + q.executionTime, 0) / this.queryHistory.length
      : 0

    const topSlowQueries = this.queryHistory
      .filter(q => q.isSlowQuery)
      .sort((a, b) => b.executionTime - a.executionTime)
      .slice(0, 10)

    return {
      totalQueries,
      slowQueries,
      avgExecutionTime,
      topSlowQueries
    }
  }

  /**
   * 生成优化报告
   */
  async generateOptimizationReport(): Promise<{
    summary: any
    missingIndexes: IndexSuggestion[]
    queryPatterns: QueryPattern[]
    recommendations: string[]
  }> {
    const [stats, missingIndexes, queryPatterns] = await Promise.all([
      this.getQueryStats(),
      this.detectMissingIndexes(),
      Promise.resolve(this.analyzeSlowQueryPatterns())
    ])

    const recommendations = this.generateRecommendations(stats, missingIndexes, queryPatterns)

    return {
      summary: stats,
      missingIndexes,
      queryPatterns,
      recommendations
    }
  }

  // ==================== 私有方法 ====================

  /**
   * 提取缓冲区命中数
   */
  private extractBufferHits(plan: any): number {
    if (plan['Shared Hit Blocks']) {
      return plan['Shared Hit Blocks']
    }
    return 0
  }

  /**
   * 提取缓冲区读取数
   */
  private extractBufferReads(plan: any): number {
    if (plan['Shared Read Blocks']) {
      return plan['Shared Read Blocks']
    }
    return 0
  }

  /**
   * 生成优化建议
   */
  private generateOptimizationSuggestions(plan: any, sql: string): string[] {
    const suggestions: string[] = []

    // 检查是否有顺序扫描
    if (this.hasSequentialScan(plan)) {
      suggestions.push('考虑添加索引以避免全表扫描')
    }

    // 检查是否有排序操作
    if (this.hasSortOperation(plan)) {
      suggestions.push('考虑添加索引以避免排序操作')
    }

    // 检查是否有嵌套循环连接
    if (this.hasNestedLoopJoin(plan)) {
      suggestions.push('考虑优化连接条件或添加索引')
    }

    // 检查缓冲区命中率
    const hitRatio = this.calculateBufferHitRatio(plan)
    if (hitRatio < 0.9) {
      suggestions.push('缓冲区命中率较低，考虑增加shared_buffers配置')
    }

    return suggestions
  }

  /**
   * 检查是否有顺序扫描
   */
  private hasSequentialScan(plan: any): boolean {
    if (plan['Node Type'] === 'Seq Scan') {
      return true
    }
    if (plan['Plans']) {
      return plan['Plans'].some((subPlan: any) => this.hasSequentialScan(subPlan))
    }
    return false
  }

  /**
   * 检查是否有排序操作
   */
  private hasSortOperation(plan: any): boolean {
    if (plan['Node Type'] === 'Sort') {
      return true
    }
    if (plan['Plans']) {
      return plan['Plans'].some((subPlan: any) => this.hasSortOperation(subPlan))
    }
    return false
  }

  /**
   * 检查是否有嵌套循环连接
   */
  private hasNestedLoopJoin(plan: any): boolean {
    if (plan['Node Type'] === 'Nested Loop') {
      return true
    }
    if (plan['Plans']) {
      return plan['Plans'].some((subPlan: any) => this.hasNestedLoopJoin(subPlan))
    }
    return false
  }

  /**
   * 计算缓冲区命中率
   */
  private calculateBufferHitRatio(plan: any): number {
    const hits = this.extractBufferHits(plan)
    const reads = this.extractBufferReads(plan)
    const total = hits + reads
    return total > 0 ? hits / total : 1
  }

  /**
   * 为表建议索引列
   */
  private async suggestColumnsForIndex(tableName: string): Promise<string[]> {
    // 简化实现，实际应该分析查询模式
    const commonIndexColumns: Record<string, string[]> = {
      users: ['email', 'is_active', 'last_login_at'],
      sessions: ['user_id', 'is_active', 'expires_at'],
      oauth_clients: ['client_id', 'is_active'],
      audit_logs: ['user_id', 'action', 'created_at'],
      authorization_codes: ['client_id', 'user_id', 'expires_at']
    }

    return commonIndexColumns[tableName] || ['id']
  }

  /**
   * 提取查询模式
   */
  private extractQueryPattern(sql: string): string {
    // 简化查询模式提取，移除具体值
    return sql
      .replace(/\$\d+/g, '?')
      .replace(/'\w+'/g, '?')
      .replace(/\d+/g, '?')
      .replace(/\s+/g, ' ')
      .trim()
  }

  /**
   * 从查询中提取表名
   */
  private extractTablesFromQuery(sql: string): string[] {
    const tableRegex = /FROM\s+(\w+)|JOIN\s+(\w+)|UPDATE\s+(\w+)|INSERT\s+INTO\s+(\w+)/gi
    const matches = sql.match(tableRegex) || []
    return matches.map(match => match.split(/\s+/).pop()!).filter(Boolean)
  }

  /**
   * 为查询模式建议索引
   */
  private suggestIndexesForPattern(pattern: string, sql: string): IndexSuggestion[] {
    const suggestions: IndexSuggestion[] = []
    const tables = this.extractTablesFromQuery(sql)

    // 简化实现
    for (const table of tables) {
      if (pattern.includes('WHERE') && pattern.includes(table)) {
        suggestions.push({
          table,
          columns: ['id'], // 简化，实际应该分析WHERE条件
          reason: `查询模式 ${pattern} 频繁访问表 ${table}`,
          estimatedImprovement: '可能提升 30-50% 的查询性能',
          priority: 'medium'
        })
      }
    }

    return suggestions
  }

  /**
   * 生成优化建议
   */
  private generateRecommendations(
    stats: any,
    missingIndexes: IndexSuggestion[],
    queryPatterns: QueryPattern[]
  ): string[] {
    const recommendations: string[] = []

    if (stats.slowQueries > stats.totalQueries * 0.1) {
      recommendations.push('慢查询比例较高，建议优化查询或添加索引')
    }

    if (missingIndexes.length > 0) {
      recommendations.push(`发现 ${missingIndexes.length} 个可能缺失的索引`)
    }

    if (queryPatterns.length > 0) {
      recommendations.push(`发现 ${queryPatterns.length} 个重复的慢查询模式`)
    }

    if (stats.avgExecutionTime > 500) {
      recommendations.push('平均查询时间较长，建议优化数据库配置')
    }

    return recommendations
  }

  /**
   * 添加到历史记录
   */
  private addToHistory(analysis: QueryAnalysis): void {
    this.queryHistory.push(analysis)
    
    // 保持历史记录大小限制
    if (this.queryHistory.length > this.maxHistorySize) {
      this.queryHistory.shift()
    }
  }
}

// 导出查询分析器实例
export const queryAnalyzer = new QueryAnalyzer(new PrismaClient())
export default queryAnalyzer
