/**
 * 数据库查询优化工具
 * 提供查询优化、索引建议和性能分析功能
 */

import { prisma } from '@/config/database';
import { logger } from '@/config/logger';

export interface QueryAnalysis {
  query: string;
  duration: number;
  suggestions: string[];
  severity: 'low' | 'medium' | 'high';
  optimizedQuery?: string;
}

export interface IndexSuggestion {
  table: string;
  columns: string[];
  type: 'single' | 'composite' | 'unique';
  reason: string;
  estimatedImprovement: string;
}

export class DatabaseOptimizer {
  private slowQueries: Map<string, QueryAnalysis[]> = new Map();
  private readonly slowQueryThreshold = 100; // 100ms

  /**
   * 分析慢查询
   */
  analyzeSlowQuery(query: string, duration: number): QueryAnalysis {
    const analysis: QueryAnalysis = {
      query,
      duration,
      suggestions: [],
      severity: this.determineSeverity(duration)
    };

    // 分析查询模式并提供建议
    analysis.suggestions = this.generateOptimizationSuggestions(query, duration);
    
    // 尝试生成优化后的查询
    analysis.optimizedQuery = this.generateOptimizedQuery(query);

    // 记录慢查询
    this.recordSlowQuery(analysis);

    return analysis;
  }

  /**
   * 确定查询严重程度
   */
  private determineSeverity(duration: number): 'low' | 'medium' | 'high' {
    if (duration > 1000) return 'high';
    if (duration > 500) return 'medium';
    return 'low';
  }

  /**
   * 生成优化建议
   */
  private generateOptimizationSuggestions(query: string, duration: number): string[] {
    const suggestions: string[] = [];
    const lowerQuery = query.toLowerCase();

    // 检查是否缺少WHERE子句
    if (lowerQuery.includes('select') && !lowerQuery.includes('where') && !lowerQuery.includes('limit')) {
      suggestions.push('考虑添加WHERE子句来限制查询范围');
    }

    // 检查是否使用了SELECT *
    if (lowerQuery.includes('select *')) {
      suggestions.push('避免使用SELECT *，只选择需要的列');
    }

    // 检查是否缺少LIMIT
    if (lowerQuery.includes('select') && !lowerQuery.includes('limit') && duration > 200) {
      suggestions.push('考虑添加LIMIT子句来限制返回的行数');
    }

    // 检查JOIN操作
    if (lowerQuery.includes('join') && duration > 300) {
      suggestions.push('检查JOIN条件是否有适当的索引');
      suggestions.push('考虑使用EXPLAIN分析JOIN的执行计划');
    }

    // 检查子查询
    if (lowerQuery.includes('select') && lowerQuery.match(/\([^)]*select/g)) {
      suggestions.push('考虑将子查询重写为JOIN操作以提高性能');
    }

    // 检查ORDER BY
    if (lowerQuery.includes('order by') && duration > 200) {
      suggestions.push('为ORDER BY字段创建索引以提高排序性能');
    }

    // 检查GROUP BY
    if (lowerQuery.includes('group by') && duration > 200) {
      suggestions.push('为GROUP BY字段创建索引以提高分组性能');
    }

    // 检查DISTINCT
    if (lowerQuery.includes('distinct') && duration > 150) {
      suggestions.push('考虑使用GROUP BY替代DISTINCT，或为相关字段创建索引');
    }

    // 检查LIKE操作
    if (lowerQuery.includes('like') && duration > 100) {
      suggestions.push('LIKE操作可能较慢，考虑使用全文搜索或优化LIKE模式');
    }

    // 检查IN操作
    if (lowerQuery.includes(' in (') && duration > 150) {
      suggestions.push('大量IN操作可能较慢，考虑使用JOIN或临时表');
    }

    // 检查ORDER BY
    if (lowerQuery.includes('order by') && duration > 200) {
      suggestions.push('确保ORDER BY的列有索引');
    }

    // 检查GROUP BY
    if (lowerQuery.includes('group by') && duration > 300) {
      suggestions.push('确保GROUP BY的列有索引');
      suggestions.push('考虑使用聚合索引');
    }

    // 检查子查询
    if (lowerQuery.includes('select') && lowerQuery.split('select').length > 2) {
      suggestions.push('考虑将子查询重写为JOIN操作');
    }

    // 检查LIKE操作
    if (lowerQuery.includes('like') && duration > 150) {
      suggestions.push('避免在LIKE模式开头使用通配符');
      suggestions.push('考虑使用全文搜索索引');
    }

    return suggestions;
  }

  /**
   * 生成优化后的查询（简单示例）
   */
  private generateOptimizedQuery(query: string): string | undefined {
    let optimized = query;
    const lowerQuery = query.toLowerCase();

    // 替换SELECT *
    if (lowerQuery.includes('select *')) {
      // 这里只是示例，实际应该根据具体表结构来优化
      optimized = optimized.replace(/SELECT \*/gi, 'SELECT id, name, email');
    }

    // 添加LIMIT（如果没有）
    if (lowerQuery.includes('select') && !lowerQuery.includes('limit') && !lowerQuery.includes('count')) {
      optimized += ' LIMIT 100';
    }

    return optimized !== query ? optimized : undefined;
  }

  /**
   * 记录慢查询
   */
  private recordSlowQuery(analysis: QueryAnalysis): void {
    const queryHash = this.hashQuery(analysis.query);
    
    if (!this.slowQueries.has(queryHash)) {
      this.slowQueries.set(queryHash, []);
    }

    const queries = this.slowQueries.get(queryHash)!;
    queries.push(analysis);

    // 保持最近的10个查询记录
    if (queries.length > 10) {
      queries.shift();
    }

    logger.warn('慢查询记录', {
      queryHash,
      duration: analysis.duration,
      severity: analysis.severity,
      suggestions: analysis.suggestions
    });
  }

  /**
   * 生成查询哈希
   */
  private hashQuery(query: string): string {
    // 简单的查询标准化和哈希
    const normalized = query
      .toLowerCase()
      .replace(/\s+/g, ' ')
      .replace(/\d+/g, '?') // 替换数字为占位符
      .replace(/'[^']*'/g, '?') // 替换字符串为占位符
      .trim();

    return Buffer.from(normalized).toString('base64').substring(0, 16);
  }

  /**
   * 获取慢查询统计
   */
  getSlowQueryStats(): any {
    const stats = {
      totalUniqueQueries: this.slowQueries.size,
      totalExecutions: 0,
      averageDuration: 0,
      severityDistribution: { low: 0, medium: 0, high: 0 },
      topSlowQueries: [] as any[]
    };

    let totalDuration = 0;
    const queryStats: Array<{ hash: string; count: number; avgDuration: number; maxDuration: number; query: string }> = [];

    for (const [hash, queries] of this.slowQueries) {
      stats.totalExecutions += queries.length;
      
      const durations = queries.map(q => q.duration);
      const avgDuration = durations.reduce((sum, d) => sum + d, 0) / durations.length;
      const maxDuration = Math.max(...durations);
      
      totalDuration += durations.reduce((sum, d) => sum + d, 0);

      // 统计严重程度分布
      queries.forEach(q => {
        stats.severityDistribution[q.severity]++;
      });

      queryStats.push({
        hash,
        count: queries.length,
        avgDuration,
        maxDuration,
        query: queries[0].query.substring(0, 100) + '...'
      });
    }

    stats.averageDuration = stats.totalExecutions > 0 ? totalDuration / stats.totalExecutions : 0;
    
    // 按平均执行时间排序，取前10个
    stats.topSlowQueries = queryStats
      .sort((a, b) => b.avgDuration - a.avgDuration)
      .slice(0, 10);

    return stats;
  }

  /**
   * 生成索引建议
   */
  async generateIndexSuggestions(): Promise<IndexSuggestion[]> {
    const suggestions: IndexSuggestion[] = [];

    try {
      // 分析用户表的查询模式
      suggestions.push({
        table: 'users',
        columns: ['email'],
        type: 'unique',
        reason: '邮箱字段经常用于登录查询，且应该唯一',
        estimatedImprovement: '登录查询性能提升90%'
      });

      suggestions.push({
        table: 'users',
        columns: ['isActive', 'emailVerified'],
        type: 'composite',
        reason: '经常同时按活跃状态和邮箱验证状态查询用户',
        estimatedImprovement: '用户列表查询性能提升70%'
      });

      // 分析会话表的查询模式
      suggestions.push({
        table: 'sessions',
        columns: ['userId', 'expiresAt'],
        type: 'composite',
        reason: '经常按用户ID查询未过期的会话',
        estimatedImprovement: '会话验证性能提升80%'
      });

      suggestions.push({
        table: 'sessions',
        columns: ['sessionToken'],
        type: 'unique',
        reason: '会话令牌用于快速查找会话',
        estimatedImprovement: '会话查找性能提升95%'
      });

      // 分析审计日志表的查询模式
      suggestions.push({
        table: 'audit_logs',
        columns: ['userId', 'createdAt'],
        type: 'composite',
        reason: '经常按用户ID和时间范围查询审计日志',
        estimatedImprovement: '审计查询性能提升60%'
      });

      suggestions.push({
        table: 'audit_logs',
        columns: ['action', 'createdAt'],
        type: 'composite',
        reason: '经常按操作类型和时间范围查询日志',
        estimatedImprovement: '操作日志查询性能提升65%'
      });

      // 分析OAuth客户端表
      suggestions.push({
        table: 'oauth_clients',
        columns: ['clientId'],
        type: 'unique',
        reason: '客户端ID用于OAuth认证，需要快速查找',
        estimatedImprovement: 'OAuth认证性能提升90%'
      });

      // 分析授权码表
      suggestions.push({
        table: 'authorization_codes',
        columns: ['code'],
        type: 'unique',
        reason: '授权码需要快速验证和查找',
        estimatedImprovement: '授权码验证性能提升95%'
      });

      suggestions.push({
        table: 'authorization_codes',
        columns: ['expiresAt', 'used'],
        type: 'composite',
        reason: '经常查询未过期且未使用的授权码',
        estimatedImprovement: '授权码清理性能提升70%'
      });

    } catch (error) {
      logger.error('生成索引建议失败', {
        error: error instanceof Error ? error.message : String(error)
      });
    }

    return suggestions;
  }

  /**
   * 执行查询性能测试
   */
  async performanceTest(testQueries: string[]): Promise<any> {
    const results = [];

    for (const query of testQueries) {
      try {
        const startTime = Date.now();
        
        // 执行查询（这里需要根据实际查询类型调整）
        await prisma.$queryRawUnsafe(query);
        
        const duration = Date.now() - startTime;
        const analysis = this.analyzeSlowQuery(query, duration);

        results.push({
          query: query.substring(0, 100) + '...',
          duration,
          analysis
        });

      } catch (error) {
        results.push({
          query: query.substring(0, 100) + '...',
          error: error instanceof Error ? error.message : String(error)
        });
      }
    }

    return {
      timestamp: new Date().toISOString(),
      totalQueries: testQueries.length,
      results
    };
  }

  /**
   * 清理慢查询记录
   */
  clearSlowQueries(): void {
    this.slowQueries.clear();
    logger.info('慢查询记录已清理');
  }

  /**
   * 获取数据库表统计信息
   */
  async getTableStats(): Promise<any> {
    try {
      // 这里需要根据具体数据库类型实现
      // SQLite的示例查询
      const tables = await prisma.$queryRaw`
        SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'
      ` as any[];

      const stats = [];

      for (const table of tables) {
        try {
          const count = await prisma.$queryRawUnsafe(`SELECT COUNT(*) as count FROM ${table.name}`);
          stats.push({
            table: table.name,
            rowCount: (count as any)[0].count
          });
        } catch (error) {
          stats.push({
            table: table.name,
            error: 'Unable to get row count'
          });
        }
      }

      return stats;

    } catch (error) {
      logger.error('获取表统计信息失败', {
        error: error instanceof Error ? error.message : String(error)
      });
      return [];
    }
  }
}

// 创建单例实例
export const databaseOptimizer = new DatabaseOptimizer();
