/**
 * 测试覆盖率分析工具
 * 分析代码覆盖率，识别未测试的代码路径，生成测试建议
 */

import fs from 'fs'
import path from 'path'
import { logger } from '@/config/logger'

export interface CoverageReport {
  file: string
  lines: {
    total: number
    covered: number
    uncovered: number
    percentage: number
  }
  functions: {
    total: number
    covered: number
    uncovered: number
    percentage: number
  }
  branches: {
    total: number
    covered: number
    uncovered: number
    percentage: number
  }
  statements: {
    total: number
    covered: number
    uncovered: number
    percentage: number
  }
}

export interface CoverageSummary {
  totalFiles: number
  overallCoverage: {
    lines: number
    functions: number
    branches: number
    statements: number
  }
  filesCoverage: CoverageReport[]
  uncoveredFiles: string[]
  lowCoverageFiles: Array<{
    file: string
    coverage: number
    priority: 'high' | 'medium' | 'low'
  }>
  testSuggestions: TestSuggestion[]
}

export interface TestSuggestion {
  file: string
  type: 'unit' | 'integration' | 'e2e'
  priority: 'high' | 'medium' | 'low'
  reason: string
  suggestedTests: string[]
  uncoveredLines: number[]
  uncoveredFunctions: string[]
}

/**
 * 测试覆盖率分析器类
 */
export class TestCoverageAnalyzer {
  private coverageThreshold = {
    high: 90,
    medium: 70,
    low: 50
  }

  /**
   * 分析覆盖率报告
   */
  async analyzeCoverage(coverageFilePath: string = 'coverage/coverage-final.json'): Promise<CoverageSummary> {
    try {
      if (!fs.existsSync(coverageFilePath)) {
        throw new Error(`覆盖率文件不存在: ${coverageFilePath}`)
      }

      const coverageData = JSON.parse(fs.readFileSync(coverageFilePath, 'utf8'))
      
      const filesCoverage: CoverageReport[] = []
      const uncoveredFiles: string[] = []
      const lowCoverageFiles: Array<{ file: string; coverage: number; priority: 'high' | 'medium' | 'low' }> = []

      let totalLines = 0
      let coveredLines = 0
      let totalFunctions = 0
      let coveredFunctions = 0
      let totalBranches = 0
      let coveredBranches = 0
      let totalStatements = 0
      let coveredStatements = 0

      // 分析每个文件的覆盖率
      for (const [filePath, fileData] of Object.entries(coverageData)) {
        const data = fileData as any
        
        const report: CoverageReport = {
          file: filePath,
          lines: {
            total: Object.keys(data.l || {}).length,
            covered: Object.values(data.l || {}).filter((hits: any) => hits > 0).length,
            uncovered: Object.values(data.l || {}).filter((hits: any) => hits === 0).length,
            percentage: 0
          },
          functions: {
            total: Object.keys(data.f || {}).length,
            covered: Object.values(data.f || {}).filter((hits: any) => hits > 0).length,
            uncovered: Object.values(data.f || {}).filter((hits: any) => hits === 0).length,
            percentage: 0
          },
          branches: {
            total: Object.keys(data.b || {}).length,
            covered: Object.values(data.b || {}).filter((branch: any) => 
              Array.isArray(branch) && branch.some(hits => hits > 0)
            ).length,
            uncovered: Object.values(data.b || {}).filter((branch: any) => 
              Array.isArray(branch) && branch.every(hits => hits === 0)
            ).length,
            percentage: 0
          },
          statements: {
            total: Object.keys(data.s || {}).length,
            covered: Object.values(data.s || {}).filter((hits: any) => hits > 0).length,
            uncovered: Object.values(data.s || {}).filter((hits: any) => hits === 0).length,
            percentage: 0
          }
        }

        // 计算百分比
        report.lines.percentage = report.lines.total > 0 
          ? (report.lines.covered / report.lines.total) * 100 
          : 100
        report.functions.percentage = report.functions.total > 0 
          ? (report.functions.covered / report.functions.total) * 100 
          : 100
        report.branches.percentage = report.branches.total > 0 
          ? (report.branches.covered / report.branches.total) * 100 
          : 100
        report.statements.percentage = report.statements.total > 0 
          ? (report.statements.covered / report.statements.total) * 100 
          : 100

        filesCoverage.push(report)

        // 累计总数
        totalLines += report.lines.total
        coveredLines += report.lines.covered
        totalFunctions += report.functions.total
        coveredFunctions += report.functions.covered
        totalBranches += report.branches.total
        coveredBranches += report.branches.covered
        totalStatements += report.statements.total
        coveredStatements += report.statements.covered

        // 识别低覆盖率文件
        const avgCoverage = (
          report.lines.percentage + 
          report.functions.percentage + 
          report.branches.percentage + 
          report.statements.percentage
        ) / 4

        if (avgCoverage === 0) {
          uncoveredFiles.push(filePath)
        } else if (avgCoverage < this.coverageThreshold.medium) {
          lowCoverageFiles.push({
            file: filePath,
            coverage: avgCoverage,
            priority: avgCoverage < this.coverageThreshold.low ? 'high' : 'medium'
          })
        }
      }

      // 生成测试建议
      const testSuggestions = await this.generateTestSuggestions(filesCoverage, coverageData)

      const summary: CoverageSummary = {
        totalFiles: filesCoverage.length,
        overallCoverage: {
          lines: totalLines > 0 ? (coveredLines / totalLines) * 100 : 100,
          functions: totalFunctions > 0 ? (coveredFunctions / totalFunctions) * 100 : 100,
          branches: totalBranches > 0 ? (coveredBranches / totalBranches) * 100 : 100,
          statements: totalStatements > 0 ? (coveredStatements / totalStatements) * 100 : 100
        },
        filesCoverage: filesCoverage.sort((a, b) => {
          const avgA = (a.lines.percentage + a.functions.percentage + a.branches.percentage + a.statements.percentage) / 4
          const avgB = (b.lines.percentage + b.functions.percentage + b.branches.percentage + b.statements.percentage) / 4
          return avgA - avgB
        }),
        uncoveredFiles,
        lowCoverageFiles: lowCoverageFiles.sort((a, b) => a.coverage - b.coverage),
        testSuggestions
      }

      logger.info('覆盖率分析完成', {
        service: 'test-coverage-analyzer',
        totalFiles: summary.totalFiles,
        overallCoverage: summary.overallCoverage,
        uncoveredFiles: summary.uncoveredFiles.length,
        lowCoverageFiles: summary.lowCoverageFiles.length
      })

      return summary

    } catch (error) {
      logger.error('覆盖率分析失败', {
        service: 'test-coverage-analyzer',
        error: error instanceof Error ? error.message : String(error)
      })
      throw error
    }
  }

  /**
   * 生成测试建议
   */
  private async generateTestSuggestions(
    filesCoverage: CoverageReport[], 
    coverageData: any
  ): Promise<TestSuggestion[]> {
    const suggestions: TestSuggestion[] = []

    for (const report of filesCoverage) {
      const fileData = coverageData[report.file]
      const avgCoverage = (
        report.lines.percentage + 
        report.functions.percentage + 
        report.branches.percentage + 
        report.statements.percentage
      ) / 4

      if (avgCoverage < this.coverageThreshold.medium) {
        const suggestion = await this.generateFileSuggestion(report, fileData)
        if (suggestion) {
          suggestions.push(suggestion)
        }
      }
    }

    return suggestions.sort((a, b) => {
      const priorityOrder = { high: 3, medium: 2, low: 1 }
      return priorityOrder[b.priority] - priorityOrder[a.priority]
    })
  }

  /**
   * 为单个文件生成测试建议
   */
  private async generateFileSuggestion(
    report: CoverageReport, 
    fileData: any
  ): Promise<TestSuggestion | null> {
    try {
      const filePath = report.file
      const fileContent = fs.existsSync(filePath) ? fs.readFileSync(filePath, 'utf8') : ''
      
      // 分析文件类型和内容
      const fileType = this.analyzeFileType(filePath, fileContent)
      const uncoveredLines = this.getUncoveredLines(fileData)
      const uncoveredFunctions = this.getUncoveredFunctions(fileData, fileContent)
      
      const avgCoverage = (
        report.lines.percentage + 
        report.functions.percentage + 
        report.branches.percentage + 
        report.statements.percentage
      ) / 4

      const priority = avgCoverage < this.coverageThreshold.low ? 'high' : 
                      avgCoverage < this.coverageThreshold.medium ? 'medium' : 'low'

      const suggestedTests = this.generateTestCases(filePath, fileContent, uncoveredFunctions)
      const reason = this.generateReason(report, fileType)

      return {
        file: filePath,
        type: fileType,
        priority,
        reason,
        suggestedTests,
        uncoveredLines,
        uncoveredFunctions
      }

    } catch (error) {
      logger.error('生成文件测试建议失败', {
        service: 'test-coverage-analyzer',
        file: report.file,
        error: error instanceof Error ? error.message : String(error)
      })
      return null
    }
  }

  /**
   * 分析文件类型
   */
  private analyzeFileType(filePath: string, content: string): 'unit' | 'integration' | 'e2e' {
    // 根据文件路径和内容判断测试类型
    if (filePath.includes('/controllers/') || filePath.includes('/routes/')) {
      return 'integration'
    }
    
    if (filePath.includes('/services/') || filePath.includes('/utils/')) {
      return 'unit'
    }
    
    if (content.includes('express') || content.includes('app.') || content.includes('server')) {
      return 'integration'
    }
    
    return 'unit'
  }

  /**
   * 获取未覆盖的行号
   */
  private getUncoveredLines(fileData: any): number[] {
    const uncoveredLines: number[] = []
    
    if (fileData.l) {
      Object.entries(fileData.l).forEach(([lineNum, hits]) => {
        if (hits === 0) {
          uncoveredLines.push(parseInt(lineNum))
        }
      })
    }
    
    return uncoveredLines.sort((a, b) => a - b)
  }

  /**
   * 获取未覆盖的函数
   */
  private getUncoveredFunctions(fileData: any, fileContent: string): string[] {
    const uncoveredFunctions: string[] = []
    
    if (fileData.f && fileData.fnMap) {
      Object.entries(fileData.f).forEach(([funcId, hits]) => {
        if (hits === 0) {
          const funcInfo = fileData.fnMap[funcId]
          if (funcInfo && funcInfo.name) {
            uncoveredFunctions.push(funcInfo.name)
          }
        }
      })
    }
    
    return uncoveredFunctions
  }

  /**
   * 生成测试用例建议
   */
  private generateTestCases(filePath: string, content: string, uncoveredFunctions: string[]): string[] {
    const suggestions: string[] = []
    
    // 基于未覆盖的函数生成测试建议
    uncoveredFunctions.forEach(funcName => {
      if (funcName && funcName !== '(anonymous)') {
        suggestions.push(`测试 ${funcName} 函数的正常流程`)
        suggestions.push(`测试 ${funcName} 函数的错误处理`)
        suggestions.push(`测试 ${funcName} 函数的边界条件`)
      }
    })
    
    // 基于文件类型生成通用建议
    if (filePath.includes('/controllers/')) {
      suggestions.push('测试HTTP请求处理')
      suggestions.push('测试参数验证')
      suggestions.push('测试错误响应')
    } else if (filePath.includes('/services/')) {
      suggestions.push('测试业务逻辑')
      suggestions.push('测试数据处理')
      suggestions.push('测试异常情况')
    } else if (filePath.includes('/utils/')) {
      suggestions.push('测试工具函数的各种输入')
      suggestions.push('测试边界值和异常值')
    }
    
    return [...new Set(suggestions)] // 去重
  }

  /**
   * 生成建议原因
   */
  private generateReason(report: CoverageReport, fileType: string): string {
    const issues: string[] = []
    
    if (report.lines.percentage < this.coverageThreshold.medium) {
      issues.push(`行覆盖率仅${report.lines.percentage.toFixed(1)}%`)
    }
    
    if (report.functions.percentage < this.coverageThreshold.medium) {
      issues.push(`函数覆盖率仅${report.functions.percentage.toFixed(1)}%`)
    }
    
    if (report.branches.percentage < this.coverageThreshold.medium) {
      issues.push(`分支覆盖率仅${report.branches.percentage.toFixed(1)}%`)
    }
    
    const typeDescription = {
      unit: '单元测试',
      integration: '集成测试',
      e2e: '端到端测试'
    }
    
    return `${issues.join('，')}，建议添加${typeDescription[fileType]}以提高覆盖率`
  }

  /**
   * 生成覆盖率报告
   */
  generateReport(summary: CoverageSummary): string {
    const lines: string[] = []
    
    lines.push('# 测试覆盖率分析报告')
    lines.push('')
    lines.push('## 总体覆盖率')
    lines.push(`- 文件总数: ${summary.totalFiles}`)
    lines.push(`- 行覆盖率: ${summary.overallCoverage.lines.toFixed(2)}%`)
    lines.push(`- 函数覆盖率: ${summary.overallCoverage.functions.toFixed(2)}%`)
    lines.push(`- 分支覆盖率: ${summary.overallCoverage.branches.toFixed(2)}%`)
    lines.push(`- 语句覆盖率: ${summary.overallCoverage.statements.toFixed(2)}%`)
    lines.push('')
    
    if (summary.uncoveredFiles.length > 0) {
      lines.push('## 未覆盖文件')
      summary.uncoveredFiles.forEach(file => {
        lines.push(`- ${file}`)
      })
      lines.push('')
    }
    
    if (summary.lowCoverageFiles.length > 0) {
      lines.push('## 低覆盖率文件')
      summary.lowCoverageFiles.forEach(item => {
        lines.push(`- ${item.file} (${item.coverage.toFixed(1)}%) - ${item.priority}优先级`)
      })
      lines.push('')
    }
    
    if (summary.testSuggestions.length > 0) {
      lines.push('## 测试建议')
      summary.testSuggestions.forEach((suggestion, index) => {
        lines.push(`### ${index + 1}. ${suggestion.file}`)
        lines.push(`**优先级**: ${suggestion.priority}`)
        lines.push(`**类型**: ${suggestion.type}`)
        lines.push(`**原因**: ${suggestion.reason}`)
        lines.push('**建议测试**:')
        suggestion.suggestedTests.forEach(test => {
          lines.push(`- ${test}`)
        })
        lines.push('')
      })
    }
    
    return lines.join('\n')
  }
}

// 导出分析器实例
export const testCoverageAnalyzer = new TestCoverageAnalyzer()
export default testCoverageAnalyzer
