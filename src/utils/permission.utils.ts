/**
 * 权限工具类
 *
 * 提供权限验证、依赖关系检查、约束条件验证等工具函数
 *
 * <AUTHOR> Provider Team
 * @version 1.0.0
 * @since 2025-08-27
 */

import {
  PermissionMetadata,
  PermissionContext,
  PermissionValidationResult,
  PermissionDependency,
  PermissionConstraints,
  PermissionOperation,
  ResourceType
} from '../types/permission.types';

/**
 * 权限验证工具类
 */
export class PermissionValidator {
  /**
   * 验证权限是否满足约束条件
   */
  static validateConstraints(
    constraints: PermissionConstraints,
    context: PermissionContext
  ): { valid: boolean; failedConstraints: string[] } {
    const failedConstraints: string[] = [];

    // 验证时间限制
    if (constraints.timeRestrictions) {
      const timeValid = this.validateTimeRestrictions(
        constraints.timeRestrictions,
        context.environment.timestamp
      );
      if (!timeValid) {
        failedConstraints.push('时间限制不满足');
      }
    }

    // 验证IP限制
    if (constraints.ipRestrictions && context.environment.ipAddress) {
      const ipValid = this.validateIPRestrictions(
        constraints.ipRestrictions,
        context.environment.ipAddress
      );
      if (!ipValid) {
        failedConstraints.push('IP地址限制不满足');
      }
    }

    // 验证设备限制
    if (constraints.deviceRestrictions) {
      const deviceValid = this.validateDeviceRestrictions(
        constraints.deviceRestrictions,
        context.environment
      );
      if (!deviceValid) {
        failedConstraints.push('设备限制不满足');
      }
    }

    // 验证地理位置限制
    if (constraints.geoRestrictions && context.environment.location) {
      const geoValid = this.validateGeoRestrictions(
        constraints.geoRestrictions,
        context.environment.location
      );
      if (!geoValid) {
        failedConstraints.push('地理位置限制不满足');
      }
    }

    return {
      valid: failedConstraints.length === 0,
      failedConstraints
    };
  }

  /**
   * 验证时间限制
   */
  private static validateTimeRestrictions(
    timeRestrictions: NonNullable<PermissionConstraints['timeRestrictions']>,
    timestamp: string
  ): boolean {
    const now = new Date(timestamp);

    // 检查开始时间
    if (timeRestrictions.startTime) {
      const startTime = new Date(timeRestrictions.startTime);
      if (now < startTime) return false;
    }

    // 检查结束时间
    if (timeRestrictions.endTime) {
      const endTime = new Date(timeRestrictions.endTime);
      if (now > endTime) return false;
    }

    // 检查允许的小时
    if (timeRestrictions.allowedHours) {
      const currentHour = now.getHours();
      if (!timeRestrictions.allowedHours.includes(currentHour)) return false;
    }

    // 检查允许的星期
    if (timeRestrictions.allowedDays) {
      const currentDay = now.getDay();
      if (!timeRestrictions.allowedDays.includes(currentDay)) return false;
    }

    return true;
  }

  /**
   * 验证IP限制
   */
  private static validateIPRestrictions(
    ipRestrictions: NonNullable<PermissionConstraints['ipRestrictions']>,
    ipAddress: string
  ): boolean {
    // 检查禁止的IP
    if (ipRestrictions.blockedIPs?.includes(ipAddress)) {
      return false;
    }

    // 检查允许的IP
    if (ipRestrictions.allowedIPs && ipRestrictions.allowedIPs.length > 0) {
      if (!ipRestrictions.allowedIPs.includes(ipAddress)) {
        return false;
      }
    }

    // 检查CIDR网段（简化实现）
    if (ipRestrictions.allowedCIDRs && ipRestrictions.allowedCIDRs.length > 0) {
      // 这里应该实现CIDR匹配逻辑
      // 为简化，暂时返回true
      return true;
    }

    return true;
  }

  /**
   * 验证设备限制
   */
  private static validateDeviceRestrictions(
    deviceRestrictions: NonNullable<PermissionConstraints['deviceRestrictions']>,
    environment: PermissionContext['environment']
  ): boolean {
    // 检查设备类型
    if (deviceRestrictions.allowedDeviceTypes && environment.deviceType) {
      if (!deviceRestrictions.allowedDeviceTypes.includes(environment.deviceType)) {
        return false;
      }
    }

    // 这里可以添加更多设备验证逻辑
    return true;
  }

  /**
   * 验证地理位置限制
   */
  private static validateGeoRestrictions(
    geoRestrictions: NonNullable<PermissionConstraints['geoRestrictions']>,
    location: NonNullable<PermissionContext['environment']['location']>
  ): boolean {
    // 检查禁止的国家
    if (geoRestrictions.blockedCountries && location.country) {
      if (geoRestrictions.blockedCountries.includes(location.country)) {
        return false;
      }
    }

    // 检查允许的国家
    if (geoRestrictions.allowedCountries && location.country) {
      if (!geoRestrictions.allowedCountries.includes(location.country)) {
        return false;
      }
    }

    return true;
  }

  /**
   * 检查权限依赖关系
   */
  static checkDependencies(
    dependencies: PermissionDependency[],
    userPermissions: string[]
  ): { satisfied: boolean; missingDependencies: string[] } {
    const missingDependencies: string[] = [];

    for (const dependency of dependencies) {
      switch (dependency.type) {
        case 'requires':
          if (!userPermissions.includes(dependency.permissionId)) {
            missingDependencies.push(dependency.permissionId);
          }
          break;

        case 'conflicts':
          if (userPermissions.includes(dependency.permissionId)) {
            missingDependencies.push(`冲突权限: ${dependency.permissionId}`);
          }
          break;

        case 'excludes':
          if (userPermissions.includes(dependency.permissionId)) {
            missingDependencies.push(`互斥权限: ${dependency.permissionId}`);
          }
          break;
      }
    }

    return {
      satisfied: missingDependencies.length === 0,
      missingDependencies
    };
  }
}