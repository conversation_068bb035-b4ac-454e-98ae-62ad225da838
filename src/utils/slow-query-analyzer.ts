/**
 * 慢查询分析工具
 * 使用PostgreSQL查询分析工具识别慢查询，优化SQL语句和数据库结构
 */

import { PrismaClient } from '@prisma/client'
import { logger } from '@/config/logger'

export interface SlowQueryRecord {
  query: string
  duration: number
  timestamp: Date
  database: string
  user: string
  parameters?: any[]
  executionPlan?: any
  suggestions?: string[]
}

export interface QueryAnalysis {
  query: string
  avgDuration: number
  maxDuration: number
  minDuration: number
  executionCount: number
  totalDuration: number
  lastExecuted: Date
  performance: 'excellent' | 'good' | 'poor' | 'critical'
  issues: string[]
  suggestions: string[]
}

export interface DatabasePerformanceReport {
  summary: {
    totalQueries: number
    slowQueries: number
    avgQueryTime: number
    slowestQuery: SlowQueryRecord | null
  }
  topSlowQueries: QueryAnalysis[]
  recommendations: string[]
  indexSuggestions: string[]
}

/**
 * 慢查询分析器类
 */
export class SlowQueryAnalyzer {
  private prisma: PrismaClient
  private slowQueryThreshold: number = 1000 // 1秒
  private queryHistory: SlowQueryRecord[] = []
  private maxHistorySize: number = 10000

  constructor(prisma: PrismaClient) {
    this.prisma = prisma
  }

  /**
   * 启用PostgreSQL慢查询日志
   */
  async enableSlowQueryLogging(): Promise<void> {
    try {
      // 设置慢查询阈值（毫秒）
      await this.prisma.$executeRaw`SET log_min_duration_statement = ${this.slowQueryThreshold}`
      
      // 启用查询日志
      await this.prisma.$executeRaw`SET log_statement = 'all'`
      
      // 启用查询计划日志
      await this.prisma.$executeRaw`SET log_checkpoints = on`
      
      logger.info('慢查询日志已启用', {
        service: 'slow-query-analyzer',
        threshold: this.slowQueryThreshold
      })

    } catch (error) {
      logger.error('启用慢查询日志失败', {
        service: 'slow-query-analyzer',
        error: error instanceof Error ? error.message : String(error)
      })
      throw error
    }
  }

  /**
   * 记录慢查询
   */
  recordSlowQuery(record: SlowQueryRecord): void {
    try {
      this.queryHistory.push(record)
      
      // 保持历史记录大小限制
      if (this.queryHistory.length > this.maxHistorySize) {
        this.queryHistory.shift()
      }
      
      logger.warn('慢查询记录', {
        service: 'slow-query-analyzer',
        query: record.query.substring(0, 100) + '...',
        duration: record.duration,
        timestamp: record.timestamp
      })

    } catch (error) {
      logger.error('记录慢查询失败', {
        service: 'slow-query-analyzer',
        error: error instanceof Error ? error.message : String(error)
      })
    }
  }

  /**
   * 分析查询性能
   */
  async analyzeQuery(query: string): Promise<QueryAnalysis> {
    try {
      // 获取查询执行计划
      const executionPlan = await this.getQueryExecutionPlan(query)
      
      // 从历史记录中获取该查询的统计信息
      const queryRecords = this.queryHistory.filter(record => 
        this.normalizeQuery(record.query) === this.normalizeQuery(query)
      )
      
      if (queryRecords.length === 0) {
        return {
          query,
          avgDuration: 0,
          maxDuration: 0,
          minDuration: 0,
          executionCount: 0,
          totalDuration: 0,
          lastExecuted: new Date(),
          performance: 'excellent',
          issues: [],
          suggestions: []
        }
      }
      
      // 计算统计信息
      const durations = queryRecords.map(r => r.duration)
      const avgDuration = durations.reduce((sum, d) => sum + d, 0) / durations.length
      const maxDuration = Math.max(...durations)
      const minDuration = Math.min(...durations)
      const totalDuration = durations.reduce((sum, d) => sum + d, 0)
      const lastExecuted = new Date(Math.max(...queryRecords.map(r => r.timestamp.getTime())))
      
      // 评估性能等级
      const performance = this.evaluatePerformance(avgDuration)
      
      // 分析问题和建议
      const { issues, suggestions } = this.analyzeQueryIssues(query, executionPlan, avgDuration)
      
      return {
        query,
        avgDuration,
        maxDuration,
        minDuration,
        executionCount: queryRecords.length,
        totalDuration,
        lastExecuted,
        performance,
        issues,
        suggestions
      }

    } catch (error) {
      logger.error('查询分析失败', {
        service: 'slow-query-analyzer',
        query: query.substring(0, 100),
        error: error instanceof Error ? error.message : String(error)
      })
      
      throw error
    }
  }

  /**
   * 获取查询执行计划
   */
  private async getQueryExecutionPlan(query: string): Promise<any> {
    try {
      // 使用EXPLAIN ANALYZE获取详细的执行计划
      const explainQuery = `EXPLAIN (ANALYZE, BUFFERS, FORMAT JSON) ${query}`
      const result = await this.prisma.$queryRawUnsafe(explainQuery)
      
      return result

    } catch (error) {
      logger.error('获取查询执行计划失败', {
        service: 'slow-query-analyzer',
        error: error instanceof Error ? error.message : String(error)
      })
      return null
    }
  }

  /**
   * 标准化查询字符串（用于比较）
   */
  private normalizeQuery(query: string): string {
    return query
      .replace(/\s+/g, ' ') // 标准化空白字符
      .replace(/\$\d+/g, '?') // 替换参数占位符
      .replace(/\d+/g, 'N') // 替换数字字面量
      .replace(/'[^']*'/g, "'STRING'") // 替换字符串字面量
      .trim()
      .toLowerCase()
  }

  /**
   * 评估查询性能等级
   */
  private evaluatePerformance(avgDuration: number): QueryAnalysis['performance'] {
    if (avgDuration < 100) return 'excellent'
    if (avgDuration < 500) return 'good'
    if (avgDuration < 2000) return 'poor'
    return 'critical'
  }

  /**
   * 分析查询问题和建议
   */
  private analyzeQueryIssues(
    query: string, 
    executionPlan: any, 
    avgDuration: number
  ): { issues: string[]; suggestions: string[] } {
    const issues: string[] = []
    const suggestions: string[] = []
    
    // 分析查询语句
    const queryLower = query.toLowerCase()
    
    // 检查是否缺少WHERE子句
    if (queryLower.includes('select') && !queryLower.includes('where') && !queryLower.includes('limit')) {
      issues.push('查询缺少WHERE子句，可能导致全表扫描')
      suggestions.push('添加适当的WHERE条件限制查询范围')
    }
    
    // 检查是否使用了SELECT *
    if (queryLower.includes('select *')) {
      issues.push('使用SELECT *可能影响性能')
      suggestions.push('只选择需要的列，避免使用SELECT *')
    }
    
    // 检查是否有ORDER BY但没有LIMIT
    if (queryLower.includes('order by') && !queryLower.includes('limit')) {
      issues.push('ORDER BY查询没有LIMIT可能影响性能')
      suggestions.push('考虑添加LIMIT子句限制结果集大小')
    }
    
    // 检查是否有多个JOIN
    const joinCount = (queryLower.match(/join/g) || []).length
    if (joinCount > 3) {
      issues.push(`查询包含${joinCount}个JOIN，可能影响性能`)
      suggestions.push('考虑优化JOIN顺序或使用子查询')
    }
    
    // 检查是否有子查询
    if (queryLower.includes('select') && queryLower.match(/\(/g)?.length > 1) {
      issues.push('查询包含子查询，可能影响性能')
      suggestions.push('考虑使用JOIN替代子查询或优化子查询')
    }
    
    // 基于执行时间的建议
    if (avgDuration > 1000) {
      issues.push(`平均执行时间${avgDuration}ms过长`)
      suggestions.push('考虑添加索引或优化查询逻辑')
    }
    
    // 分析执行计划（如果可用）
    if (executionPlan) {
      this.analyzeExecutionPlan(executionPlan, issues, suggestions)
    }
    
    return { issues, suggestions }
  }

  /**
   * 分析执行计划
   */
  private analyzeExecutionPlan(
    executionPlan: any, 
    issues: string[], 
    suggestions: string[]
  ): void {
    try {
      // 这里可以分析PostgreSQL的执行计划
      // 检查是否有Seq Scan、高成本操作等
      
      const planStr = JSON.stringify(executionPlan).toLowerCase()
      
      if (planStr.includes('seq scan')) {
        issues.push('查询使用了顺序扫描')
        suggestions.push('考虑添加索引以避免顺序扫描')
      }
      
      if (planStr.includes('nested loop')) {
        issues.push('查询使用了嵌套循环连接')
        suggestions.push('考虑优化JOIN条件或添加索引')
      }

    } catch (error) {
      logger.error('分析执行计划失败', {
        service: 'slow-query-analyzer',
        error: error instanceof Error ? error.message : String(error)
      })
    }
  }

  /**
   * 获取最慢的查询
   */
  getTopSlowQueries(limit: number = 10): QueryAnalysis[] {
    try {
      // 按查询分组并计算统计信息
      const queryGroups = new Map<string, SlowQueryRecord[]>()
      
      for (const record of this.queryHistory) {
        const normalizedQuery = this.normalizeQuery(record.query)
        if (!queryGroups.has(normalizedQuery)) {
          queryGroups.set(normalizedQuery, [])
        }
        queryGroups.get(normalizedQuery)!.push(record)
      }
      
      // 计算每个查询的分析结果
      const analyses: QueryAnalysis[] = []
      
      for (const [normalizedQuery, records] of queryGroups) {
        const durations = records.map(r => r.duration)
        const avgDuration = durations.reduce((sum, d) => sum + d, 0) / durations.length
        const maxDuration = Math.max(...durations)
        const minDuration = Math.min(...durations)
        const totalDuration = durations.reduce((sum, d) => sum + d, 0)
        const lastExecuted = new Date(Math.max(...records.map(r => r.timestamp.getTime())))
        
        const performance = this.evaluatePerformance(avgDuration)
        const { issues, suggestions } = this.analyzeQueryIssues(records[0].query, null, avgDuration)
        
        analyses.push({
          query: records[0].query,
          avgDuration,
          maxDuration,
          minDuration,
          executionCount: records.length,
          totalDuration,
          lastExecuted,
          performance,
          issues,
          suggestions
        })
      }
      
      // 按平均执行时间排序
      return analyses
        .sort((a, b) => b.avgDuration - a.avgDuration)
        .slice(0, limit)

    } catch (error) {
      logger.error('获取最慢查询失败', {
        service: 'slow-query-analyzer',
        error: error instanceof Error ? error.message : String(error)
      })
      return []
    }
  }

  /**
   * 生成数据库性能报告
   */
  generatePerformanceReport(): DatabasePerformanceReport {
    try {
      const totalQueries = this.queryHistory.length
      const slowQueries = this.queryHistory.filter(q => q.duration > this.slowQueryThreshold).length
      const avgQueryTime = totalQueries > 0 
        ? this.queryHistory.reduce((sum, q) => sum + q.duration, 0) / totalQueries 
        : 0
      
      const slowestQuery = this.queryHistory.length > 0
        ? this.queryHistory.reduce((slowest, current) => 
            current.duration > slowest.duration ? current : slowest
          )
        : null
      
      const topSlowQueries = this.getTopSlowQueries(10)
      
      // 生成通用建议
      const recommendations = this.generateRecommendations(topSlowQueries)
      
      // 生成索引建议
      const indexSuggestions = this.generateIndexSuggestions(topSlowQueries)
      
      return {
        summary: {
          totalQueries,
          slowQueries,
          avgQueryTime,
          slowestQuery
        },
        topSlowQueries,
        recommendations,
        indexSuggestions
      }

    } catch (error) {
      logger.error('生成性能报告失败', {
        service: 'slow-query-analyzer',
        error: error instanceof Error ? error.message : String(error)
      })
      
      throw error
    }
  }

  /**
   * 生成优化建议
   */
  private generateRecommendations(topSlowQueries: QueryAnalysis[]): string[] {
    const recommendations: string[] = []
    
    if (topSlowQueries.length > 0) {
      recommendations.push('定期监控和优化慢查询')
      
      const criticalQueries = topSlowQueries.filter(q => q.performance === 'critical')
      if (criticalQueries.length > 0) {
        recommendations.push(`发现${criticalQueries.length}个严重性能问题的查询，需要立即优化`)
      }
      
      const poorQueries = topSlowQueries.filter(q => q.performance === 'poor')
      if (poorQueries.length > 0) {
        recommendations.push(`发现${poorQueries.length}个性能较差的查询，建议优化`)
      }
    }
    
    recommendations.push('定期更新表统计信息以优化查询计划')
    recommendations.push('考虑使用连接池优化数据库连接')
    recommendations.push('监控数据库资源使用情况')
    
    return recommendations
  }

  /**
   * 生成索引建议
   */
  private generateIndexSuggestions(topSlowQueries: QueryAnalysis[]): string[] {
    const suggestions: string[] = []
    
    for (const query of topSlowQueries) {
      if (query.issues.some(issue => issue.includes('顺序扫描') || issue.includes('全表扫描'))) {
        suggestions.push(`为查询 "${query.query.substring(0, 50)}..." 考虑添加索引`)
      }
    }
    
    return [...new Set(suggestions)] // 去重
  }

  /**
   * 清理历史记录
   */
  clearHistory(): void {
    this.queryHistory = []
    logger.info('慢查询历史记录已清理', {
      service: 'slow-query-analyzer'
    })
  }

  /**
   * 设置慢查询阈值
   */
  setSlowQueryThreshold(threshold: number): void {
    this.slowQueryThreshold = threshold
    logger.info('慢查询阈值已更新', {
      service: 'slow-query-analyzer',
      threshold
    })
  }
}

// 导出分析器实例
export const slowQueryAnalyzer = new SlowQueryAnalyzer(new PrismaClient())
export default slowQueryAnalyzer
