/**
 * SAML 工具类
 * 提供SAML协议相关的工具函数
 */

import crypto from 'crypto';
import { v4 as uuidv4 } from 'uuid';
import * as forge from 'node-forge';
import * as xml2js from 'xml2js';
import { create } from 'xmlbuilder2';
import { logger } from '@/config/logger';

/**
 * 生成SAML ID
 */
export function generateSAMLId(): string {
  return '_' + uuidv4().replace(/-/g, '');
}

/**
 * 生成ISO 8601格式的时间戳
 */
export function generateTimestamp(offsetSeconds: number = 0): string {
  const date = new Date();
  date.setSeconds(date.getSeconds() + offsetSeconds);
  return date.toISOString();
}

/**
 * 验证时间戳是否在有效范围内
 */
export function isTimestampValid(
  timestamp: string, 
  clockSkewSeconds: number = 300
): boolean {
  try {
    const time = new Date(timestamp).getTime();
    const now = Date.now();
    const skew = clockSkewSeconds * 1000;
    
    return time >= (now - skew) && time <= (now + skew);
  } catch (error) {
    return false;
  }
}

/**
 * Base64编码URL安全
 */
export function base64UrlEncode(str: string): string {
  return Buffer.from(str, 'utf8')
    .toString('base64')
    .replace(/\+/g, '-')
    .replace(/\//g, '_')
    .replace(/=/g, '');
}

/**
 * Base64解码URL安全
 */
export function base64UrlDecode(str: string): string {
  // 添加填充
  const padding = '='.repeat((4 - str.length % 4) % 4);
  const base64 = str.replace(/-/g, '+').replace(/_/g, '/') + padding;
  
  return Buffer.from(base64, 'base64').toString('utf8');
}

/**
 * 压缩字符串（用于HTTP-Redirect绑定）
 */
export function deflateString(str: string): Buffer {
  return require('zlib').deflateRawSync(Buffer.from(str, 'utf8'));
}

/**
 * 解压缩字符串
 */
export function inflateString(buffer: Buffer): string {
  return require('zlib').inflateRawSync(buffer).toString('utf8');
}

/**
 * 创建XML签名
 */
export function signXML(
  xmlString: string, 
  privateKey: string, 
  certificate: string,
  algorithm: string = 'http://www.w3.org/2001/04/xmldsig-more#rsa-sha256'
): string {
  try {
    // 解析XML
    const parser = new xml2js.Parser({ explicitArray: false });
    const builder = new xml2js.Builder({ rootName: 'root' });
    
    // 这里应该使用专门的XML签名库，如xmldsig
    // 为了简化，我们先返回原始XML
    logger.warn('XML签名功能需要完整实现');
    return xmlString;
    
  } catch (error) {
    logger.error('XML签名失败', { error });
    throw new Error('XML签名失败');
  }
}

/**
 * 验证XML签名
 */
export function verifyXMLSignature(
  xmlString: string, 
  certificate: string
): boolean {
  try {
    // 这里应该使用专门的XML签名验证库
    logger.warn('XML签名验证功能需要完整实现');
    return true;
    
  } catch (error) {
    logger.error('XML签名验证失败', { error });
    return false;
  }
}

/**
 * 加密XML元素
 */
export function encryptXMLElement(
  xmlElement: string, 
  certificate: string,
  algorithm: string = 'http://www.w3.org/2001/04/xmlenc#aes256-cbc'
): string {
  try {
    // 解析证书
    const cert = forge.pki.certificateFromPem(certificate);
    const publicKey = cert.publicKey;
    
    // 生成对称密钥
    const symmetricKey = forge.random.getBytesSync(32); // 256位密钥
    
    // 使用对称密钥加密XML
    const cipher = forge.cipher.createCipher('AES-CBC', symmetricKey);
    const iv = forge.random.getBytesSync(16);
    cipher.start({ iv });
    cipher.update(forge.util.createBuffer(xmlElement, 'utf8'));
    cipher.finish();
    
    const encryptedData = cipher.output.getBytes();
    
    // 使用公钥加密对称密钥
    const encryptedKey = (publicKey as any).encrypt(symmetricKey, 'RSA-OAEP');
    
    // 构建加密的XML结构
    const encryptedXml = create({ version: '1.0', encoding: 'UTF-8' })
      .ele('xenc:EncryptedData', {
        'xmlns:xenc': 'http://www.w3.org/2001/04/xmlenc#',
        'Type': 'http://www.w3.org/2001/04/xmlenc#Element'
      })
        .ele('xenc:EncryptionMethod', { 'Algorithm': algorithm }).up()
        .ele('ds:KeyInfo', { 'xmlns:ds': 'http://www.w3.org/2000/09/xmldsig#' })
          .ele('xenc:EncryptedKey')
            .ele('xenc:EncryptionMethod', { 'Algorithm': 'http://www.w3.org/2001/04/xmlenc#rsa-oaep-mgf1p' }).up()
            .ele('xenc:CipherData')
              .ele('xenc:CipherValue', forge.util.encode64(encryptedKey)).up()
            .up()
          .up()
        .up()
        .ele('xenc:CipherData')
          .ele('xenc:CipherValue', forge.util.encode64(iv + encryptedData)).up()
        .up()
      .up();
    
    return encryptedXml.end({ prettyPrint: true });
    
  } catch (error) {
    logger.error('XML加密失败', { error });
    throw new Error('XML加密失败');
  }
}

/**
 * 解密XML元素
 */
export function decryptXMLElement(
  encryptedXml: string, 
  privateKey: string
): string {
  try {
    // 解析私钥
    const key = forge.pki.privateKeyFromPem(privateKey);
    
    // 解析加密的XML
    const parser = new xml2js.Parser({ explicitArray: false });
    
    return new Promise<string>((resolve, reject) => {
      parser.parseString(encryptedXml, (err, result) => {
        if (err) {
          reject(new Error('解析加密XML失败'));
          return;
        }
        
        try {
          const encryptedData = result['xenc:EncryptedData'];
          const encryptedKey = encryptedData['ds:KeyInfo']['xenc:EncryptedKey']['xenc:CipherData']['xenc:CipherValue'];
          const cipherValue = encryptedData['xenc:CipherData']['xenc:CipherValue'];
          
          // 解密对称密钥
          const encryptedKeyBytes = forge.util.decode64(encryptedKey);
          const symmetricKey = key.decrypt(encryptedKeyBytes, 'RSA-OAEP');
          
          // 解密数据
          const cipherBytes = forge.util.decode64(cipherValue);
          const iv = cipherBytes.slice(0, 16);
          const encryptedContent = cipherBytes.slice(16);
          
          const decipher = forge.cipher.createDecipher('AES-CBC', symmetricKey);
          decipher.start({ iv });
          decipher.update(forge.util.createBuffer(encryptedContent));
          decipher.finish();
          
          const decryptedXml = decipher.output.toString();
          resolve(decryptedXml);
          
        } catch (decryptError) {
          reject(new Error('解密失败'));
        }
      });
    }) as any;
    
  } catch (error) {
    logger.error('XML解密失败', { error });
    throw new Error('XML解密失败');
  }
}

/**
 * 生成X.509证书（用于测试）
 */
export function generateTestCertificate(): { certificate: string; privateKey: string } {
  try {
    // 生成密钥对
    const keys = forge.pki.rsa.generateKeyPair(2048);
    
    // 创建证书
    const cert = forge.pki.createCertificate();
    cert.publicKey = keys.publicKey;
    cert.serialNumber = '01';
    cert.validity.notBefore = new Date();
    cert.validity.notAfter = new Date();
    cert.validity.notAfter.setFullYear(cert.validity.notBefore.getFullYear() + 1);
    
    const attrs = [{
      name: 'commonName',
      value: 'SAML IdP Test Certificate'
    }, {
      name: 'countryName',
      value: 'US'
    }, {
      shortName: 'ST',
      value: 'Test'
    }, {
      name: 'localityName',
      value: 'Test'
    }, {
      name: 'organizationName',
      value: 'Test Organization'
    }, {
      shortName: 'OU',
      value: 'Test Unit'
    }];
    
    cert.setSubject(attrs);
    cert.setIssuer(attrs);
    
    // 自签名
    cert.sign(keys.privateKey);
    
    // 转换为PEM格式
    const certificate = forge.pki.certificateToPem(cert);
    const privateKey = forge.pki.privateKeyToPem(keys.privateKey);
    
    return { certificate, privateKey };
    
  } catch (error) {
    logger.error('生成测试证书失败', { error });
    throw new Error('生成测试证书失败');
  }
}

/**
 * 验证证书
 */
export function validateCertificate(certificatePem: string): boolean {
  try {
    const cert = forge.pki.certificateFromPem(certificatePem);
    const now = new Date();
    
    // 检查证书有效期
    if (now < cert.validity.notBefore || now > cert.validity.notAfter) {
      return false;
    }
    
    return true;
    
  } catch (error) {
    logger.error('证书验证失败', { error });
    return false;
  }
}

/**
 * 从证书中提取公钥
 */
export function extractPublicKeyFromCertificate(certificatePem: string): string {
  try {
    const cert = forge.pki.certificateFromPem(certificatePem);
    return forge.pki.publicKeyToPem(cert.publicKey);
  } catch (error) {
    logger.error('从证书提取公钥失败', { error });
    throw new Error('从证书提取公钥失败');
  }
}

/**
 * 格式化证书（移除头尾和换行）
 */
export function formatCertificateForXML(certificatePem: string): string {
  return certificatePem
    .replace(/-----BEGIN CERTIFICATE-----/, '')
    .replace(/-----END CERTIFICATE-----/, '')
    .replace(/\n/g, '')
    .replace(/\r/g, '');
}
