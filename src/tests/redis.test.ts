/**
 * Redis服务测试
 */

import { redisService, RedisKeys, RedisTTL } from '../services/redis.service';
import { cacheService } from '../services/cache.service';

describe('Redis服务测试', () => {
  beforeAll(async () => {
    // 使用测试数据库
    process.env.REDIS_DB = '2';
    process.env.REDIS_KEY_PREFIX = 'idp:test:';
    
    await redisService.initialize();
  });

  afterAll(async () => {
    // 清理测试数据
    await redisService.deletePattern('idp:test:*');
    await redisService.close();
  });

  beforeEach(async () => {
    // 每个测试前清理
    await redisService.deletePattern('idp:test:*');
  });

  describe('基础Redis操作', () => {
    test('应该能够设置和获取缓存', async () => {
      const key = 'test:basic';
      const value = { message: 'Hello Redis' };

      await redisService.set(key, value, 60);
      const result = await redisService.get(key);

      expect(result).toEqual(value);
    });

    test('应该能够删除缓存', async () => {
      const key = 'test:delete';
      const value = 'test value';

      await redisService.set(key, value);
      await redisService.del(key);
      const result = await redisService.get(key);

      expect(result).toBeNull();
    });

    test('应该能够检查键是否存在', async () => {
      const key = 'test:exists';
      const value = 'test value';

      expect(await redisService.exists(key)).toBe(false);
      
      await redisService.set(key, value);
      expect(await redisService.exists(key)).toBe(true);
    });

    test('应该能够设置过期时间', async () => {
      const key = 'test:expire';
      const value = 'test value';

      await redisService.set(key, value);
      await redisService.expire(key, 1);
      
      const ttl = await redisService.ttl(key);
      expect(ttl).toBeGreaterThan(0);
      expect(ttl).toBeLessThanOrEqual(1);
    });

    test('应该能够原子递增', async () => {
      const key = 'test:incr';

      const result1 = await redisService.incr(key);
      const result2 = await redisService.incr(key);
      const result3 = await redisService.incrby(key, 5);

      expect(result1).toBe(1);
      expect(result2).toBe(2);
      expect(result3).toBe(7);
    });
  });

  describe('会话缓存测试', () => {
    test('应该能够缓存和获取用户会话', async () => {
      const sessionId = 'test-session-123';
      const sessionData = {
        userId: 'user-123',
        email: '<EMAIL>',
        roles: ['user'],
        permissions: ['read:profile'],
        lastAccessedAt: new Date(),
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000)
      };

      await cacheService.setSession(sessionId, sessionData);
      const result = await cacheService.getSession(sessionId);

      expect(result).toMatchObject({
        userId: sessionData.userId,
        email: sessionData.email,
        roles: sessionData.roles,
        permissions: sessionData.permissions
      });
    });

    test('应该能够删除用户会话', async () => {
      const sessionId = 'test-session-delete';
      const sessionData = {
        userId: 'user-delete',
        email: '<EMAIL>',
        roles: ['user'],
        permissions: ['read:profile'],
        lastAccessedAt: new Date(),
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000)
      };

      await cacheService.setSession(sessionId, sessionData);
      await cacheService.deleteSession(sessionId);
      const result = await cacheService.getSession(sessionId);

      expect(result).toBeNull();
    });

    test('应该能够管理用户会话列表', async () => {
      const userId = 'user-sessions-test';
      const sessionIds = ['session-1', 'session-2', 'session-3'];

      // 添加会话
      for (const sessionId of sessionIds) {
        await cacheService.addUserSession(userId, sessionId);
      }

      // 获取会话列表
      const userSessions = await cacheService.getUserSessions(userId);
      expect(userSessions).toHaveLength(3);
      expect(userSessions).toEqual(expect.arrayContaining(sessionIds));

      // 移除一个会话
      await cacheService.removeUserSession(userId, sessionIds[0]);
      const updatedSessions = await cacheService.getUserSessions(userId);
      expect(updatedSessions).toHaveLength(2);
      expect(updatedSessions).not.toContain(sessionIds[0]);

      // 删除所有会话
      await cacheService.deleteAllUserSessions(userId);
      const finalSessions = await cacheService.getUserSessions(userId);
      expect(finalSessions).toHaveLength(0);
    });
  });

  describe('JWT令牌管理测试', () => {
    test('应该能够管理JWT黑名单', async () => {
      const jti = 'test-jwt-123';
      const exp = Math.floor(Date.now() / 1000) + 3600; // 1小时后过期

      // 检查初始状态
      expect(await cacheService.isJWTBlacklisted(jti)).toBe(false);

      // 加入黑名单
      await cacheService.blacklistJWT(jti, exp);
      expect(await cacheService.isJWTBlacklisted(jti)).toBe(true);
    });

    test('应该能够管理刷新令牌', async () => {
      const tokenId = 'refresh-token-123';
      const tokenData = {
        userId: 'user-123',
        sessionId: 'session-123',
        issuedAt: new Date(),
        expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)
      };

      await cacheService.setRefreshToken(tokenId, tokenData);
      const result = await cacheService.getRefreshToken(tokenId);

      expect(result).toMatchObject({
        userId: tokenData.userId,
        sessionId: tokenData.sessionId
      });

      await cacheService.deleteRefreshToken(tokenId);
      const deletedResult = await cacheService.getRefreshToken(tokenId);
      expect(deletedResult).toBeNull();
    });
  });

  describe('速率限制测试', () => {
    test('应该能够正确实施速率限制', async () => {
      const key = 'test-rate-limit';
      const limit = 3;
      const windowMs = 60000; // 1分钟

      // 前3次请求应该被允许
      for (let i = 0; i < limit; i++) {
        const result = await cacheService.checkRateLimit(key, limit, windowMs);
        expect(result.allowed).toBe(true);
        expect(result.remaining).toBe(limit - i - 1);
      }

      // 第4次请求应该被拒绝
      const result = await cacheService.checkRateLimit(key, limit, windowMs);
      expect(result.allowed).toBe(false);
      expect(result.remaining).toBe(0);
    });
  });

  describe('用户数据缓存测试', () => {
    test('应该能够缓存用户资料', async () => {
      const userId = 'user-profile-test';
      const profile = {
        id: userId,
        email: '<EMAIL>',
        nickname: 'Test User',
        firstName: 'Test',
        lastName: 'User',
        isEmailVerified: true,
        mfaEnabled: false,
        roles: ['user'],
        createdAt: new Date(),
        lastLoginAt: new Date()
      };

      await cacheService.setUserProfile(userId, profile);
      const result = await cacheService.getUserProfile(userId);

      expect(result).toMatchObject({
        id: profile.id,
        email: profile.email,
        nickname: profile.nickname,
        isEmailVerified: profile.isEmailVerified,
        mfaEnabled: profile.mfaEnabled,
        roles: profile.roles
      });

      await cacheService.deleteUserProfile(userId);
      const deletedResult = await cacheService.getUserProfile(userId);
      expect(deletedResult).toBeNull();
    });

    test('应该能够缓存用户权限', async () => {
      const userId = 'user-permissions-test';
      const permissions = ['read:profile', 'write:profile', 'read:sessions'];

      await cacheService.setUserPermissions(userId, permissions);
      const result = await cacheService.getUserPermissions(userId);

      expect(result).toEqual(permissions);
    });
  });

  describe('OAuth状态管理测试', () => {
    test('应该能够管理OAuth状态', async () => {
      const state = 'oauth-state-123';
      const data = {
        provider: 'google',
        redirectUrl: 'http://localhost:3001/auth/callback',
        timestamp: Date.now()
      };

      await cacheService.setOAuthState(state, data);
      const result = await cacheService.getOAuthState(state);

      expect(result).toMatchObject(data);

      await cacheService.deleteOAuthState(state);
      const deletedResult = await cacheService.getOAuthState(state);
      expect(deletedResult).toBeNull();
    });
  });

  describe('MFA验证码管理测试', () => {
    test('应该能够管理MFA验证码', async () => {
      const userId = 'user-mfa-test';
      const type = 'email';
      const code = '123456';

      await cacheService.setMFACode(userId, type, code);
      const result = await cacheService.getMFACode(userId, type);

      expect(result).toBe(code);

      await cacheService.deleteMFACode(userId, type);
      const deletedResult = await cacheService.getMFACode(userId, type);
      expect(deletedResult).toBeNull();
    });
  });

  describe('Redis连接健康检查', () => {
    test('应该能够检查Redis连接状态', () => {
      expect(redisService.isReady()).toBe(true);
    });

    test('应该能够获取Redis信息', async () => {
      const info = await redisService.getInfo();
      expect(info).toBeDefined();
      expect(typeof info).toBe('object');
    });
  });

  describe('错误处理测试', () => {
    test('Redis不可用时应该优雅降级', async () => {
      // 模拟Redis不可用的情况
      await redisService.close();

      // 缓存操作应该不会抛出错误
      await expect(cacheService.cache('test-key', 'test-value')).resolves.not.toThrow();
      await expect(cacheService.getCached('test-key')).resolves.toBeNull();

      // 重新连接Redis
      await redisService.initialize();
    });
  });
});
