/**
 * 认证控制器测试
 */

import request from 'supertest';
import express from 'express';
import { AuthController } from '../controllers/auth.controller';
import { AuthService } from '../services/auth.service';
import { prisma } from '../config/database';

// 模拟依赖
jest.mock('../services/auth.service');
jest.mock('../config/database');
jest.mock('../config/logger');
jest.mock('../services/cache.service');

describe('AuthController', () => {
  let app: express.Application;
  let authController: AuthController;
  let mockAuthService: jest.Mocked<AuthService>;

  beforeAll(() => {
    // 创建Express应用
    app = express();
    app.use(express.json());
    
    // 创建模拟的AuthService
    mockAuthService = new AuthService() as jest.Mocked<AuthService>;
    authController = new AuthController();
    
    // 设置路由
    app.post('/register', authController.register);
    app.post('/login', authController.login);
    app.post('/logout', authController.logout);
    app.post('/refresh-token', authController.refreshToken);
    app.post('/forgot-password', authController.forgotPassword);
    app.post('/reset-password', authController.resetPassword);
    app.post('/change-password', authController.changePassword);
    app.get('/verify-email', authController.verifyEmail);
    app.post('/validate-token', authController.validateToken);
    app.post('/introspect', authController.introspectToken);
  });

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('POST /register', () => {
    const validRegisterData = {
      email: '<EMAIL>',
      password: 'Password123!',
      nickname: 'Test User',
      firstName: 'Test',
      lastName: 'User'
    };

    test('应该成功注册新用户', async () => {
      const mockUser = {
        id: 'user-123',
        email: '<EMAIL>',
        nickname: 'Test User',
        isEmailVerified: false
      };

      mockAuthService.register = jest.fn().mockResolvedValue(mockUser);

      const response = await request(app)
        .post('/register')
        .send(validRegisterData)
        .expect(201);

      expect(response.body).toMatchObject({
        userId: mockUser.id,
        status: 'pending_verification'
      });
    });

    test('应该拒绝无效的邮箱格式', async () => {
      const invalidData = {
        ...validRegisterData,
        email: 'invalid-email'
      };

      const response = await request(app)
        .post('/register')
        .send(invalidData)
        .expect(400);

      expect(response.body.error).toBe('validation_failed');
    });

    test('应该拒绝弱密码', async () => {
      const weakPasswordData = {
        ...validRegisterData,
        password: '123'
      };

      const response = await request(app)
        .post('/register')
        .send(weakPasswordData)
        .expect(400);

      expect(response.body.error).toBe('validation_failed');
    });

    test('应该处理邮箱已存在的情况', async () => {
      mockAuthService.register = jest.fn().mockRejectedValue(
        new Error('邮箱已存在')
      );

      const response = await request(app)
        .post('/register')
        .send(validRegisterData)
        .expect(400);

      expect(response.body.error).toBe('registration_failed');
    });
  });

  describe('POST /login', () => {
    const validLoginData = {
      username: '<EMAIL>',
      password: 'Password123!'
    };

    test('应该成功登录用户', async () => {
      const mockLoginResult = {
        user: {
          id: 'user-123',
          email: '<EMAIL>',
          nickname: 'Test User',
          isEmailVerified: true
        },
        tokens: {
          accessToken: 'access-token',
          refreshToken: 'refresh-token',
          expiresIn: 900
        },
        sessionId: 'session-123'
      };

      mockAuthService.login = jest.fn().mockResolvedValue(mockLoginResult);

      const response = await request(app)
        .post('/login')
        .send(validLoginData)
        .expect(200);

      expect(response.body).toMatchObject({
        user: mockLoginResult.user,
        tokens: mockLoginResult.tokens,
        sessionId: mockLoginResult.sessionId
      });
    });

    test('应该拒绝无效凭据', async () => {
      mockAuthService.login = jest.fn().mockRejectedValue(
        new Error('无效的用户名或密码')
      );

      const response = await request(app)
        .post('/login')
        .send(validLoginData)
        .expect(401);

      expect(response.body.error).toBe('login_failed');
    });

    test('应该拒绝缺少必填字段', async () => {
      const incompleteData = {
        username: '<EMAIL>'
        // 缺少password
      };

      const response = await request(app)
        .post('/login')
        .send(incompleteData)
        .expect(400);

      expect(response.body.error).toBe('validation_failed');
    });
  });

  describe('POST /logout', () => {
    test('应该成功登出用户', async () => {
      const logoutData = {
        refreshToken: 'refresh-token'
      };

      mockAuthService.logout = jest.fn().mockResolvedValue(undefined);

      const response = await request(app)
        .post('/logout')
        .send(logoutData)
        .expect(204);

      expect(mockAuthService.logout).toHaveBeenCalledWith(
        logoutData.refreshToken,
        expect.any(String)
      );
    });

    test('应该拒绝缺少刷新令牌的请求', async () => {
      const response = await request(app)
        .post('/logout')
        .send({})
        .expect(400);

      expect(response.body.error).toBe('missing_refresh_token');
    });
  });

  describe('POST /refresh-token', () => {
    test('应该成功刷新访问令牌', async () => {
      const refreshData = {
        refreshToken: 'valid-refresh-token'
      };

      const mockRefreshResult = {
        accessToken: 'new-access-token',
        expiresIn: 900
      };

      mockAuthService.refreshToken = jest.fn().mockResolvedValue(mockRefreshResult);

      const response = await request(app)
        .post('/refresh-token')
        .send(refreshData)
        .expect(200);

      expect(response.body).toMatchObject(mockRefreshResult);
    });

    test('应该拒绝无效的刷新令牌', async () => {
      const refreshData = {
        refreshToken: 'invalid-refresh-token'
      };

      mockAuthService.refreshToken = jest.fn().mockRejectedValue(
        new Error('无效的刷新令牌')
      );

      const response = await request(app)
        .post('/refresh-token')
        .send(refreshData)
        .expect(401);

      expect(response.body.error).toBe('token_refresh_failed');
    });
  });

  describe('POST /forgot-password', () => {
    test('应该成功发送密码重置邮件', async () => {
      const forgotPasswordData = {
        email: '<EMAIL>'
      };

      mockAuthService.forgotPassword = jest.fn().mockResolvedValue(undefined);

      const response = await request(app)
        .post('/forgot-password')
        .send(forgotPasswordData)
        .expect(202);

      expect(response.body.message).toBe('密码重置邮件已发送');
    });

    test('应该拒绝无效的邮箱格式', async () => {
      const invalidData = {
        email: 'invalid-email'
      };

      const response = await request(app)
        .post('/forgot-password')
        .send(invalidData)
        .expect(400);

      expect(response.body.error).toBe('validation_failed');
    });
  });

  describe('POST /reset-password', () => {
    test('应该成功重置密码', async () => {
      const resetData = {
        token: 'valid-reset-token',
        newPassword: 'NewPassword123!'
      };

      mockAuthService.resetPassword = jest.fn().mockResolvedValue(undefined);

      const response = await request(app)
        .post('/reset-password')
        .send(resetData)
        .expect(204);

      expect(mockAuthService.resetPassword).toHaveBeenCalledWith(
        resetData.token,
        resetData.newPassword
      );
    });

    test('应该拒绝无效的重置令牌', async () => {
      const resetData = {
        token: 'invalid-reset-token',
        newPassword: 'NewPassword123!'
      };

      mockAuthService.resetPassword = jest.fn().mockRejectedValue(
        new Error('无效的重置令牌')
      );

      const response = await request(app)
        .post('/reset-password')
        .send(resetData)
        .expect(400);

      expect(response.body.error).toBe('password_reset_failed');
    });
  });

  describe('POST /validate-token', () => {
    test('应该成功验证有效令牌', async () => {
      const tokenData = {
        token: 'valid-access-token'
      };

      const mockValidationResult = {
        valid: true,
        user: {
          id: 'user-123',
          email: '<EMAIL>',
          roles: ['user'],
          permissions: ['read:profile']
        },
        exp: Math.floor(Date.now() / 1000) + 900
      };

      mockAuthService.validateToken = jest.fn().mockResolvedValue(mockValidationResult);

      const response = await request(app)
        .post('/validate-token')
        .send(tokenData)
        .expect(200);

      expect(response.body).toMatchObject(mockValidationResult);
    });

    test('应该拒绝无效令牌', async () => {
      const tokenData = {
        token: 'invalid-token'
      };

      mockAuthService.validateToken = jest.fn().mockRejectedValue(
        new Error('无效的令牌')
      );

      const response = await request(app)
        .post('/validate-token')
        .send(tokenData)
        .expect(401);

      expect(response.body.error).toBe('token_validation_failed');
    });
  });

  describe('POST /introspect', () => {
    test('应该成功内省令牌', async () => {
      const introspectData = {
        token: 'valid-token',
        token_type_hint: 'access_token'
      };

      const mockIntrospectResult = {
        active: true,
        scope: 'read write',
        client_id: 'client-123',
        username: '<EMAIL>',
        exp: Math.floor(Date.now() / 1000) + 900,
        iat: Math.floor(Date.now() / 1000),
        sub: 'user-123'
      };

      mockAuthService.introspectToken = jest.fn().mockResolvedValue(mockIntrospectResult);

      const response = await request(app)
        .post('/introspect')
        .send(introspectData)
        .expect(200);

      expect(response.body).toMatchObject(mockIntrospectResult);
    });

    test('应该返回非活跃状态对于无效令牌', async () => {
      const introspectData = {
        token: 'invalid-token'
      };

      const mockIntrospectResult = {
        active: false
      };

      mockAuthService.introspectToken = jest.fn().mockResolvedValue(mockIntrospectResult);

      const response = await request(app)
        .post('/introspect')
        .send(introspectData)
        .expect(200);

      expect(response.body.active).toBe(false);
    });
  });

  describe('GET /verify-email', () => {
    test('应该成功验证邮箱', async () => {
      const token = 'valid-verification-token';

      mockAuthService.verifyEmail = jest.fn().mockResolvedValue(undefined);

      const response = await request(app)
        .get(`/verify-email?token=${token}`)
        .expect(200);

      expect(response.body.message).toBe('邮箱验证成功');
    });

    test('应该拒绝无效的验证令牌', async () => {
      const token = 'invalid-verification-token';

      mockAuthService.verifyEmail = jest.fn().mockRejectedValue(
        new Error('无效的验证令牌')
      );

      const response = await request(app)
        .get(`/verify-email?token=${token}`)
        .expect(400);

      expect(response.body.error).toBe('email_verification_failed');
    });

    test('应该拒绝缺少令牌的请求', async () => {
      const response = await request(app)
        .get('/verify-email')
        .expect(400);

      expect(response.body.error).toBe('missing_token');
    });
  });
});
