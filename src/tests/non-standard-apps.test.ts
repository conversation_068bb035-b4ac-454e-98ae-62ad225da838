/**
 * 非标准应用支持功能测试
 */

import { describe, it, expect, beforeAll, afterAll, beforeEach, afterEach } from '@jest/globals';
import { adapterRegistry } from '@/services/adapter-registry.service';
import { pluginManager } from '@/services/plugin-manager.service';
import { protocolAdapterService } from '@/services/protocol-adapter.service';
import { LegacySystemAdapter } from '@/adapters/legacy-system.adapter';
import { WebhookAdapter } from '@/adapters/webhook.adapter';
import { ApiGatewayAdapter } from '@/adapters/api-gateway.adapter';
import { ProtocolConfig } from '@/types/protocol-adapter';

describe('非标准应用支持功能测试', () => {
  let testInstanceIds: string[] = [];

  beforeAll(async () => {
    // 初始化服务
    await protocolAdapterService.initialize();
  });

  afterAll(async () => {
    // 清理测试实例
    for (const instanceId of testInstanceIds) {
      await adapterRegistry.stopAdapterInstance(instanceId);
    }
  });

  beforeEach(() => {
    testInstanceIds = [];
  });

  describe('适配器注册表测试', () => {
    it('应该能够获取已注册的适配器', () => {
      const adapters = adapterRegistry.getRegisteredAdapters();
      expect(adapters).toBeDefined();
      expect(Array.isArray(adapters)).toBe(true);
      expect(adapters.length).toBeGreaterThan(0);

      // 检查内置适配器是否已注册
      const adapterNames = adapters.map(a => a.name);
      expect(adapterNames).toContain('custom-oauth');
      expect(adapterNames).toContain('legacy-system');
      expect(adapterNames).toContain('webhook');
      expect(adapterNames).toContain('api-gateway');
    });

    it('应该能够获取适配器元数据', () => {
      const metadata = adapterRegistry.getAdapterMetadata('custom-oauth');
      expect(metadata).toBeDefined();
      expect(metadata?.name).toBe('custom-oauth');
      expect(metadata?.version).toBeDefined();
      expect(metadata?.supportedMethods).toBeDefined();
      expect(Array.isArray(metadata?.supportedMethods)).toBe(true);
    });

    it('应该能够检查适配器是否已注册', () => {
      expect(adapterRegistry.isAdapterRegistered('custom-oauth')).toBe(true);
      expect(adapterRegistry.isAdapterRegistered('non-existent-adapter')).toBe(false);
    });
  });

  describe('遗留系统适配器测试', () => {
    it('应该能够创建遗留系统适配器实例', async () => {
      const config: ProtocolConfig = {
        name: 'legacy-system',
        version: '1.0.0',
        endpoints: {
          authentication: '/auth/legacy',
          userinfo: '/auth/userinfo'
        },
        authMethod: 'basic_auth',
        customSettings: {
          enableBasicAuth: true
        }
      };

      const instanceId = await adapterRegistry.createAdapterInstance('legacy-system', config);
      testInstanceIds.push(instanceId);

      expect(instanceId).toBeDefined();
      expect(typeof instanceId).toBe('string');

      const instance = adapterRegistry.getAdapterInstance(instanceId);
      expect(instance).toBeDefined();
      expect(instance?.name).toBe('legacy-system');
      expect(instance?.isActive).toBe(true);
    });

    it('应该能够处理基础认证', async () => {
      const config: ProtocolConfig = {
        name: 'legacy-system',
        version: '1.0.0',
        endpoints: {
          authentication: '/auth/legacy'
        },
        authMethod: 'basic_auth'
      };

      const instanceId = await adapterRegistry.createAdapterInstance('legacy-system', config);
      testInstanceIds.push(instanceId);

      const instance = adapterRegistry.getAdapterInstance(instanceId);
      expect(instance).toBeDefined();

      const authRequest = {
        applicationId: 'test-app',
        clientId: 'test-client',
        credentials: {
          username: 'testuser',
          password: 'testpass'
        }
      };

      const response = await instance!.adapter.handleAuthRequest(authRequest as any);
      expect(response).toBeDefined();
      expect(response.success).toBeDefined();
    });
  });

  describe('Webhook适配器测试', () => {
    it('应该能够创建Webhook适配器实例', async () => {
      const config: ProtocolConfig = {
        name: 'webhook',
        version: '1.0.0',
        endpoints: {
          webhook: '/webhook/auth'
        },
        webhooks: {
          authUrl: 'https://example.com/webhook/auth',
          secretKey: 'test-secret-key'
        },
        signatureConfig: {
          algorithm: 'sha256',
          headerName: 'X-Webhook-Signature'
        },
        authConfig: {
          method: 'POST',
          responseField: 'authenticated'
        }
      };

      const instanceId = await adapterRegistry.createAdapterInstance('webhook', config);
      testInstanceIds.push(instanceId);

      expect(instanceId).toBeDefined();
      
      const instance = adapterRegistry.getAdapterInstance(instanceId);
      expect(instance).toBeDefined();
      expect(instance?.name).toBe('webhook');
    });

    it('应该能够生成和验证Webhook签名', async () => {
      const config: ProtocolConfig = {
        name: 'webhook',
        version: '1.0.0',
        endpoints: {
          webhook: '/webhook/auth'
        },
        webhooks: {
          authUrl: 'https://example.com/webhook/auth',
          secretKey: 'test-secret-key'
        },
        signatureConfig: {
          algorithm: 'sha256',
          headerName: 'X-Webhook-Signature'
        },
        authConfig: {
          method: 'POST',
          responseField: 'authenticated'
        }
      };

      const instanceId = await adapterRegistry.createAdapterInstance('webhook', config);
      testInstanceIds.push(instanceId);

      const instance = adapterRegistry.getAdapterInstance(instanceId);
      const adapter = instance!.adapter as WebhookAdapter;

      const payload = JSON.stringify({ test: 'data' });
      const signature = (adapter as any).generateSignature(payload);
      
      expect(signature).toBeDefined();
      expect(typeof signature).toBe('string');

      const isValid = adapter.verifySignature(payload, signature);
      expect(isValid).toBe(true);
    });
  });

  describe('API网关适配器测试', () => {
    it('应该能够创建API网关适配器实例', async () => {
      const config: ProtocolConfig = {
        name: 'api-gateway',
        version: '1.0.0',
        endpoints: {
          gateway: '/gateway'
        },
        gateway: {
          type: 'kong',
          adminUrl: 'http://localhost:8001'
        },
        authentication: {
          strategy: 'jwt_validation',
          jwtConfig: {
            algorithm: 'HS256',
            issuer: 'test-issuer',
            audience: 'test-audience'
          }
        },
        routing: {
          upstreamUrl: 'https://api.example.com'
        }
      };

      const instanceId = await adapterRegistry.createAdapterInstance('api-gateway', config);
      testInstanceIds.push(instanceId);

      expect(instanceId).toBeDefined();
      
      const instance = adapterRegistry.getAdapterInstance(instanceId);
      expect(instance).toBeDefined();
      expect(instance?.name).toBe('api-gateway');
    });
  });

  describe('适配器实例管理测试', () => {
    it('应该能够获取所有适配器实例', () => {
      const instances = adapterRegistry.getAllAdapterInstances();
      expect(Array.isArray(instances)).toBe(true);
    });

    it('应该能够按名称获取适配器实例', async () => {
      const config: ProtocolConfig = {
        name: 'custom-oauth',
        version: '2.0.0',
        endpoints: {
          authorization: '/oauth/authorize',
          token: '/oauth/token'
        }
      };

      const instanceId = await adapterRegistry.createAdapterInstance('custom-oauth', config);
      testInstanceIds.push(instanceId);

      const instances = adapterRegistry.getAdapterInstancesByName('custom-oauth');
      expect(instances.length).toBeGreaterThan(0);
      
      const createdInstance = instances.find(i => i.id === instanceId);
      expect(createdInstance).toBeDefined();
    });

    it('应该能够停止和重启适配器实例', async () => {
      const config: ProtocolConfig = {
        name: 'custom-oauth',
        version: '2.0.0',
        endpoints: {
          authorization: '/oauth/authorize',
          token: '/oauth/token'
        }
      };

      const instanceId = await adapterRegistry.createAdapterInstance('custom-oauth', config);
      
      // 停止实例
      const stopResult = await adapterRegistry.stopAdapterInstance(instanceId);
      expect(stopResult).toBe(true);

      // 重启实例
      const restartResult = await adapterRegistry.restartAdapterInstance(instanceId);
      expect(restartResult).toBe(true);

      testInstanceIds.push(instanceId);
    });

    it('应该能够更新适配器实例配置', async () => {
      const config: ProtocolConfig = {
        name: 'custom-oauth',
        version: '2.0.0',
        endpoints: {
          authorization: '/oauth/authorize',
          token: '/oauth/token'
        }
      };

      const instanceId = await adapterRegistry.createAdapterInstance('custom-oauth', config);
      testInstanceIds.push(instanceId);

      const newConfig: ProtocolConfig = {
        ...config,
        endpoints: {
          ...config.endpoints,
          userinfo: '/oauth/userinfo'
        }
      };

      const updateResult = await adapterRegistry.updateAdapterInstanceConfig(instanceId, newConfig);
      expect(updateResult).toBe(true);

      const instance = adapterRegistry.getAdapterInstance(instanceId);
      expect(instance?.config.endpoints.userinfo).toBe('/oauth/userinfo');
    });

    it('应该能够记录适配器使用情况', async () => {
      const config: ProtocolConfig = {
        name: 'custom-oauth',
        version: '2.0.0',
        endpoints: {
          authorization: '/oauth/authorize',
          token: '/oauth/token'
        }
      };

      const instanceId = await adapterRegistry.createAdapterInstance('custom-oauth', config);
      testInstanceIds.push(instanceId);

      const instanceBefore = adapterRegistry.getAdapterInstance(instanceId);
      const usageCountBefore = instanceBefore?.usageCount || 0;

      adapterRegistry.recordAdapterUsage(instanceId);

      const instanceAfter = adapterRegistry.getAdapterInstance(instanceId);
      const usageCountAfter = instanceAfter?.usageCount || 0;

      expect(usageCountAfter).toBe(usageCountBefore + 1);
      expect(instanceAfter?.lastUsed).toBeDefined();
    });
  });

  describe('适配器使用统计测试', () => {
    it('应该能够获取适配器使用统计', async () => {
      const stats = adapterRegistry.getAdapterUsageStats();
      expect(stats).toBeDefined();
      expect(typeof stats).toBe('object');
    });
  });

  describe('错误处理测试', () => {
    it('应该在创建不存在的适配器实例时抛出错误', async () => {
      const config: ProtocolConfig = {
        name: 'non-existent',
        version: '1.0.0',
        endpoints: {}
      };

      await expect(
        adapterRegistry.createAdapterInstance('non-existent-adapter', config)
      ).rejects.toThrow();
    });

    it('应该在获取不存在的适配器实例时返回undefined', () => {
      const instance = adapterRegistry.getAdapterInstance('non-existent-instance');
      expect(instance).toBeUndefined();
    });

    it('应该在停止不存在的适配器实例时返回false', async () => {
      const result = await adapterRegistry.stopAdapterInstance('non-existent-instance');
      expect(result).toBe(false);
    });
  });
});
