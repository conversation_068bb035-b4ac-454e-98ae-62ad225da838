/**
 * JWT工具函数测试
 */

import jwt from 'jsonwebtoken';
import {
  generateAccessToken,
  generateRefreshToken,
  verifyAccessToken,
  verifyRefreshToken,
  JWTPayload
} from '../utils/jwt';

// 模拟配置
jest.mock('../config', () => ({
  config: {
    jwt: {
      secret: 'test-jwt-secret',
      refreshSecret: 'test-refresh-secret',
      expiresIn: '15m',
      refreshExpiresIn: '7d'
    }
  }
}));

describe('JWT工具函数', () => {
  const mockUser = {
    id: 'user-123',
    email: '<EMAIL>',
    roles: ['user'],
    permissions: ['read:profile']
  };

  const mockSession = {
    id: 'session-123',
    userId: 'user-123'
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('generateAccessToken', () => {
    test('应该生成有效的访问令牌', () => {
      const token = generateAccessToken(mockUser, mockSession);

      expect(typeof token).toBe('string');
      expect(token.split('.')).toHaveLength(3); // JWT格式检查

      // 验证令牌内容
      const decoded = jwt.verify(token, 'test-jwt-secret') as any;
      expect(decoded.userId).toBe(mockUser.id);
      expect(decoded.email).toBe(mockUser.email);
      expect(decoded.sessionId).toBe(mockSession.id);
      expect(decoded.roles).toEqual(mockUser.roles);
      expect(decoded.permissions).toEqual(mockUser.permissions);
      expect(decoded.type).toBe('access');
    });

    test('应该包含正确的过期时间', () => {
      const token = generateAccessToken(mockUser, mockSession);
      const decoded = jwt.verify(token, 'test-jwt-secret') as any;

      expect(decoded.exp).toBeDefined();
      expect(decoded.iat).toBeDefined();
      expect(decoded.exp - decoded.iat).toBe(15 * 60); // 15分钟
    });

    test('应该包含唯一的JTI', () => {
      const token1 = generateAccessToken(mockUser, mockSession);
      const token2 = generateAccessToken(mockUser, mockSession);

      const decoded1 = jwt.verify(token1, 'test-jwt-secret') as any;
      const decoded2 = jwt.verify(token2, 'test-jwt-secret') as any;

      expect(decoded1.jti).toBeDefined();
      expect(decoded2.jti).toBeDefined();
      expect(decoded1.jti).not.toBe(decoded2.jti);
    });
  });

  describe('generateRefreshToken', () => {
    test('应该生成有效的刷新令牌', () => {
      const token = generateRefreshToken(mockUser, mockSession);

      expect(typeof token).toBe('string');
      expect(token.split('.')).toHaveLength(3); // JWT格式检查

      // 验证令牌内容
      const decoded = jwt.verify(token, 'test-refresh-secret') as any;
      expect(decoded.userId).toBe(mockUser.id);
      expect(decoded.sessionId).toBe(mockSession.id);
      expect(decoded.type).toBe('refresh');
    });

    test('应该包含正确的过期时间', () => {
      const token = generateRefreshToken(mockUser, mockSession);
      const decoded = jwt.verify(token, 'test-refresh-secret') as any;

      expect(decoded.exp).toBeDefined();
      expect(decoded.iat).toBeDefined();
      expect(decoded.exp - decoded.iat).toBe(7 * 24 * 60 * 60); // 7天
    });

    test('刷新令牌不应包含敏感信息', () => {
      const token = generateRefreshToken(mockUser, mockSession);
      const decoded = jwt.verify(token, 'test-refresh-secret') as any;

      expect(decoded.email).toBeUndefined();
      expect(decoded.roles).toBeUndefined();
      expect(decoded.permissions).toBeUndefined();
    });
  });

  describe('verifyAccessToken', () => {
    test('应该成功验证有效的访问令牌', () => {
      const token = generateAccessToken(mockUser, mockSession);
      const payload = verifyAccessToken(token);

      expect(payload.userId).toBe(mockUser.id);
      expect(payload.email).toBe(mockUser.email);
      expect(payload.sessionId).toBe(mockSession.id);
      expect(payload.roles).toEqual(mockUser.roles);
      expect(payload.permissions).toEqual(mockUser.permissions);
      expect(payload.type).toBe('access');
    });

    test('应该拒绝无效的令牌', () => {
      const invalidToken = 'invalid.token.here';

      expect(() => verifyAccessToken(invalidToken)).toThrow();
    });

    test('应该拒绝过期的令牌', () => {
      // 创建一个已过期的令牌
      const expiredToken = jwt.sign(
        {
          userId: mockUser.id,
          email: mockUser.email,
          sessionId: mockSession.id,
          type: 'access',
          exp: Math.floor(Date.now() / 1000) - 3600 // 1小时前过期
        },
        'test-jwt-secret'
      );

      expect(() => verifyAccessToken(expiredToken)).toThrow();
    });

    test('应该拒绝错误类型的令牌', () => {
      // 使用刷新令牌密钥生成的令牌
      const wrongTypeToken = jwt.sign(
        {
          userId: mockUser.id,
          sessionId: mockSession.id,
          type: 'refresh'
        },
        'test-refresh-secret',
        { expiresIn: '15m' }
      );

      expect(() => verifyAccessToken(wrongTypeToken)).toThrow();
    });

    test('应该拒绝使用错误密钥签名的令牌', () => {
      const wrongSecretToken = jwt.sign(
        {
          userId: mockUser.id,
          email: mockUser.email,
          sessionId: mockSession.id,
          type: 'access'
        },
        'wrong-secret',
        { expiresIn: '15m' }
      );

      expect(() => verifyAccessToken(wrongSecretToken)).toThrow();
    });
  });

  describe('verifyRefreshToken', () => {
    test('应该成功验证有效的刷新令牌', () => {
      const token = generateRefreshToken(mockUser, mockSession);
      const payload = verifyRefreshToken(token);

      expect(payload.userId).toBe(mockUser.id);
      expect(payload.sessionId).toBe(mockSession.id);
      expect(payload.type).toBe('refresh');
    });

    test('应该拒绝无效的令牌', () => {
      const invalidToken = 'invalid.token.here';

      expect(() => verifyRefreshToken(invalidToken)).toThrow();
    });

    test('应该拒绝过期的令牌', () => {
      // 创建一个已过期的令牌
      const expiredToken = jwt.sign(
        {
          userId: mockUser.id,
          sessionId: mockSession.id,
          type: 'refresh',
          exp: Math.floor(Date.now() / 1000) - 3600 // 1小时前过期
        },
        'test-refresh-secret'
      );

      expect(() => verifyRefreshToken(expiredToken)).toThrow();
    });

    test('应该拒绝错误类型的令牌', () => {
      // 使用访问令牌密钥生成的令牌
      const wrongTypeToken = jwt.sign(
        {
          userId: mockUser.id,
          email: mockUser.email,
          sessionId: mockSession.id,
          type: 'access'
        },
        'test-jwt-secret',
        { expiresIn: '7d' }
      );

      expect(() => verifyRefreshToken(wrongTypeToken)).toThrow();
    });

    test('应该拒绝使用错误密钥签名的令牌', () => {
      const wrongSecretToken = jwt.sign(
        {
          userId: mockUser.id,
          sessionId: mockSession.id,
          type: 'refresh'
        },
        'wrong-secret',
        { expiresIn: '7d' }
      );

      expect(() => verifyRefreshToken(wrongSecretToken)).toThrow();
    });
  });

  describe('令牌安全性', () => {
    test('访问令牌和刷新令牌应该使用不同的密钥', () => {
      const accessToken = generateAccessToken(mockUser, mockSession);
      const refreshToken = generateRefreshToken(mockUser, mockSession);

      // 访问令牌不应该能用刷新令牌密钥验证
      expect(() => jwt.verify(accessToken, 'test-refresh-secret')).toThrow();
      
      // 刷新令牌不应该能用访问令牌密钥验证
      expect(() => jwt.verify(refreshToken, 'test-jwt-secret')).toThrow();
    });

    test('令牌应该包含必要的标准声明', () => {
      const accessToken = generateAccessToken(mockUser, mockSession);
      const decoded = jwt.verify(accessToken, 'test-jwt-secret') as any;

      expect(decoded.iss).toBeDefined(); // 发行者
      expect(decoded.aud).toBeDefined(); // 受众
      expect(decoded.iat).toBeDefined(); // 发行时间
      expect(decoded.exp).toBeDefined(); // 过期时间
      expect(decoded.jti).toBeDefined(); // JWT ID
    });

    test('相同输入应该生成不同的令牌（由于JTI）', () => {
      const token1 = generateAccessToken(mockUser, mockSession);
      const token2 = generateAccessToken(mockUser, mockSession);

      expect(token1).not.toBe(token2);

      const decoded1 = jwt.verify(token1, 'test-jwt-secret') as any;
      const decoded2 = jwt.verify(token2, 'test-jwt-secret') as any;

      expect(decoded1.jti).not.toBe(decoded2.jti);
    });
  });

  describe('错误处理', () => {
    test('应该正确处理格式错误的令牌', () => {
      const malformedTokens = [
        '',
        'not.a.jwt',
        'header.payload', // 缺少签名
        'too.many.parts.here.invalid'
      ];

      malformedTokens.forEach(token => {
        expect(() => verifyAccessToken(token)).toThrow();
        expect(() => verifyRefreshToken(token)).toThrow();
      });
    });

    test('应该正确处理篡改的令牌', () => {
      const validToken = generateAccessToken(mockUser, mockSession);
      const parts = validToken.split('.');
      
      // 篡改payload
      const tamperedPayload = Buffer.from('{"userId":"hacker"}').toString('base64');
      const tamperedToken = `${parts[0]}.${tamperedPayload}.${parts[2]}`;

      expect(() => verifyAccessToken(tamperedToken)).toThrow();
    });
  });
});
