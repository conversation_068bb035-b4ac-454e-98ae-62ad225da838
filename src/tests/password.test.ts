/**
 * 密码工具函数测试
 */

import {
  hashPassword,
  comparePassword,
  validatePasswordStrength,
  generateSecurePassword
} from '../utils/password';

describe('密码工具函数', () => {
  describe('hashPassword', () => {
    test('应该成功哈希密码', async () => {
      const password = 'TestPassword123!';
      const hash = await hashPassword(password);

      expect(typeof hash).toBe('string');
      expect(hash).not.toBe(password);
      expect(hash.length).toBeGreaterThan(50); // bcrypt哈希长度检查
      expect(hash.startsWith('$2b$')).toBe(true); // bcrypt格式检查
    });

    test('相同密码应该生成不同的哈希（由于盐）', async () => {
      const password = 'TestPassword123!';
      const hash1 = await hashPassword(password);
      const hash2 = await hashPassword(password);

      expect(hash1).not.toBe(hash2);
    });

    test('应该处理空密码', async () => {
      const password = '';
      const hash = await hashPassword(password);

      expect(typeof hash).toBe('string');
      expect(hash).not.toBe('');
    });

    test('应该处理特殊字符密码', async () => {
      const password = '!@#$%^&*()_+-=[]{}|;:,.<>?';
      const hash = await hashPassword(password);

      expect(typeof hash).toBe('string');
      expect(hash).not.toBe(password);
    });

    test('应该处理Unicode字符密码', async () => {
      const password = '密码测试123！';
      const hash = await hashPassword(password);

      expect(typeof hash).toBe('string');
      expect(hash).not.toBe(password);
    });
  });

  describe('comparePassword', () => {
    test('应该正确验证匹配的密码', async () => {
      const password = 'TestPassword123!';
      const hash = await hashPassword(password);
      const isMatch = await comparePassword(password, hash);

      expect(isMatch).toBe(true);
    });

    test('应该正确拒绝不匹配的密码', async () => {
      const password = 'TestPassword123!';
      const wrongPassword = 'WrongPassword123!';
      const hash = await hashPassword(password);
      const isMatch = await comparePassword(wrongPassword, hash);

      expect(isMatch).toBe(false);
    });

    test('应该处理空密码比较', async () => {
      const password = '';
      const hash = await hashPassword(password);
      const isMatch = await comparePassword('', hash);

      expect(isMatch).toBe(true);
    });

    test('应该拒绝空密码与非空哈希的比较', async () => {
      const password = 'TestPassword123!';
      const hash = await hashPassword(password);
      const isMatch = await comparePassword('', hash);

      expect(isMatch).toBe(false);
    });

    test('应该处理无效的哈希', async () => {
      const password = 'TestPassword123!';
      const invalidHash = 'invalid-hash';

      await expect(comparePassword(password, invalidHash)).rejects.toThrow();
    });

    test('应该处理特殊字符密码比较', async () => {
      const password = '!@#$%^&*()_+-=[]{}|;:,.<>?';
      const hash = await hashPassword(password);
      const isMatch = await comparePassword(password, hash);

      expect(isMatch).toBe(true);
    });

    test('应该处理Unicode字符密码比较', async () => {
      const password = '密码测试123！';
      const hash = await hashPassword(password);
      const isMatch = await comparePassword(password, hash);

      expect(isMatch).toBe(true);
    });
  });

  describe('validatePasswordStrength', () => {
    test('应该接受强密码', () => {
      const strongPasswords = [
        'StrongPassword123!',
        'MySecure@Pass2023',
        'Complex#Password$456',
        'Aa1!bcdefgh',
        'P@ssw0rd123456'
      ];

      strongPasswords.forEach(password => {
        const result = validatePasswordStrength(password);
        expect(result.isValid).toBe(true);
        expect(result.errors).toHaveLength(0);
      });
    });

    test('应该拒绝过短的密码', () => {
      const shortPasswords = [
        'Aa1!',
        'Short1!',
        '1234567'
      ];

      shortPasswords.forEach(password => {
        const result = validatePasswordStrength(password);
        expect(result.isValid).toBe(false);
        expect(result.errors).toContain('密码长度至少8位');
      });
    });

    test('应该拒绝缺少大写字母的密码', () => {
      const passwords = [
        'lowercase123!',
        'nouppercasehere1!',
        'alllowercase123!'
      ];

      passwords.forEach(password => {
        const result = validatePasswordStrength(password);
        expect(result.isValid).toBe(false);
        expect(result.errors).toContain('密码必须包含至少一个大写字母');
      });
    });

    test('应该拒绝缺少小写字母的密码', () => {
      const passwords = [
        'UPPERCASE123!',
        'NOLOWERCASEHERE1!',
        'ALLUPPERCASE123!'
      ];

      passwords.forEach(password => {
        const result = validatePasswordStrength(password);
        expect(result.isValid).toBe(false);
        expect(result.errors).toContain('密码必须包含至少一个小写字母');
      });
    });

    test('应该拒绝缺少数字的密码', () => {
      const passwords = [
        'NoNumbersHere!',
        'OnlyLetters@',
        'PasswordWithoutDigits!'
      ];

      passwords.forEach(password => {
        const result = validatePasswordStrength(password);
        expect(result.isValid).toBe(false);
        expect(result.errors).toContain('密码必须包含至少一个数字');
      });
    });

    test('应该拒绝缺少特殊字符的密码', () => {
      const passwords = [
        'NoSpecialChars123',
        'OnlyAlphanumeric456',
        'PasswordWithoutSymbols789'
      ];

      passwords.forEach(password => {
        const result = validatePasswordStrength(password);
        expect(result.isValid).toBe(false);
        expect(result.errors).toContain('密码必须包含至少一个特殊字符');
      });
    });

    test('应该拒绝常见的弱密码', () => {
      const weakPasswords = [
        'Password123!',
        'Admin123!',
        'User123!',
        'Test123!',
        '123456789!',
        'Qwerty123!'
      ];

      weakPasswords.forEach(password => {
        const result = validatePasswordStrength(password);
        expect(result.isValid).toBe(false);
        expect(result.errors).toContain('密码过于常见，请使用更复杂的密码');
      });
    });

    test('应该拒绝包含重复字符的密码', () => {
      const passwords = [
        'Aaaa1111!!!!',
        'Password111!',
        'Test1234!!!!'
      ];

      passwords.forEach(password => {
        const result = validatePasswordStrength(password);
        expect(result.isValid).toBe(false);
        expect(result.errors).toContain('密码不能包含过多重复字符');
      });
    });

    test('应该拒绝包含连续字符的密码', () => {
      const passwords = [
        'Abcd1234!',
        'Password123!',
        'Test4567!'
      ];

      passwords.forEach(password => {
        const result = validatePasswordStrength(password);
        expect(result.isValid).toBe(false);
        expect(result.errors).toContain('密码不能包含连续的字符序列');
      });
    });

    test('应该返回密码强度评分', () => {
      const testCases = [
        { password: 'weak', expectedScore: 0 },
        { password: 'Weak1!', expectedScore: 1 },
        { password: 'Medium123!', expectedScore: 2 },
        { password: 'StrongPassword123!', expectedScore: 3 },
        { password: 'VeryStrong@Password#2023$', expectedScore: 4 }
      ];

      testCases.forEach(({ password, expectedScore }) => {
        const result = validatePasswordStrength(password);
        expect(result.score).toBe(expectedScore);
      });
    });

    test('应该提供改进建议', () => {
      const result = validatePasswordStrength('weak');
      
      expect(result.suggestions).toContain('增加密码长度');
      expect(result.suggestions).toContain('添加大写字母');
      expect(result.suggestions).toContain('添加数字');
      expect(result.suggestions).toContain('添加特殊字符');
    });
  });

  describe('generateSecurePassword', () => {
    test('应该生成指定长度的密码', () => {
      const lengths = [8, 12, 16, 20, 32];

      lengths.forEach(length => {
        const password = generateSecurePassword(length);
        expect(password.length).toBe(length);
      });
    });

    test('应该生成包含所有字符类型的密码', () => {
      const password = generateSecurePassword(16);
      
      expect(/[a-z]/.test(password)).toBe(true); // 小写字母
      expect(/[A-Z]/.test(password)).toBe(true); // 大写字母
      expect(/[0-9]/.test(password)).toBe(true); // 数字
      expect(/[!@#$%^&*()_+\-=\[\]{}|;:,.<>?]/.test(password)).toBe(true); // 特殊字符
    });

    test('应该生成不同的密码', () => {
      const password1 = generateSecurePassword(16);
      const password2 = generateSecurePassword(16);

      expect(password1).not.toBe(password2);
    });

    test('应该生成通过强度验证的密码', () => {
      const password = generateSecurePassword(12);
      const validation = validatePasswordStrength(password);

      expect(validation.isValid).toBe(true);
      expect(validation.score).toBeGreaterThanOrEqual(3);
    });

    test('应该处理最小长度要求', () => {
      const password = generateSecurePassword(8);
      expect(password.length).toBe(8);
      
      const validation = validatePasswordStrength(password);
      expect(validation.isValid).toBe(true);
    });

    test('应该处理大长度要求', () => {
      const password = generateSecurePassword(64);
      expect(password.length).toBe(64);
      
      const validation = validatePasswordStrength(password);
      expect(validation.isValid).toBe(true);
    });

    test('生成的密码应该不包含易混淆字符', () => {
      const password = generateSecurePassword(20);
      
      // 检查是否不包含易混淆的字符
      expect(password).not.toMatch(/[0O1lI]/);
    });
  });

  describe('性能测试', () => {
    test('密码哈希应该在合理时间内完成', async () => {
      const password = 'TestPassword123!';
      const startTime = Date.now();
      
      await hashPassword(password);
      
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      // 哈希应该在1秒内完成（在测试环境中使用较低的成本因子）
      expect(duration).toBeLessThan(1000);
    });

    test('密码比较应该在合理时间内完成', async () => {
      const password = 'TestPassword123!';
      const hash = await hashPassword(password);
      
      const startTime = Date.now();
      await comparePassword(password, hash);
      const endTime = Date.now();
      
      const duration = endTime - startTime;
      
      // 比较应该在500ms内完成
      expect(duration).toBeLessThan(500);
    });
  });
});
