/**
 * 认证服务测试
 */

import { AuthService } from '../services/auth.service';
import { prisma } from '../config/database';
import { hashPassword, comparePassword } from '../utils/password';
import { generateAccessToken, generateRefreshToken } from '../utils/jwt';
import { cacheService } from '../services/cache.service';

// 模拟依赖
jest.mock('../config/database');
jest.mock('../utils/password');
jest.mock('../utils/jwt');
jest.mock('../services/cache.service');
jest.mock('../config/logger');

describe('AuthService', () => {
  let authService: AuthService;
  let mockPrisma: any;
  let mockCacheService: any;

  beforeEach(() => {
    authService = new AuthService();
    mockPrisma = prisma as any;
    mockCacheService = cacheService as any;
    
    // 重置所有模拟
    jest.clearAllMocks();
  });

  describe('register', () => {
    const registerData = {
      email: '<EMAIL>',
      password: 'Password123!',
      nickname: 'Test User',
      firstName: 'Test',
      lastName: 'User'
    };

    test('应该成功注册新用户', async () => {
      const hashedPassword = 'hashed-password';
      const mockUser = {
        id: 'user-123',
        email: '<EMAIL>',
        nickname: 'Test User',
        firstName: 'Test',
        lastName: 'User',
        isEmailVerified: false,
        createdAt: new Date()
      };

      // 模拟依赖
      (hashPassword as jest.Mock).mockResolvedValue(hashedPassword);
      mockPrisma.user.findUnique.mockResolvedValue(null); // 用户不存在
      mockPrisma.user.create.mockResolvedValue(mockUser);

      const result = await authService.register(registerData, '127.0.0.1');

      expect(hashPassword).toHaveBeenCalledWith(registerData.password);
      expect(mockPrisma.user.create).toHaveBeenCalledWith({
        data: {
          email: registerData.email,
          passwordHash: hashedPassword,
          nickname: registerData.nickname,
          firstName: registerData.firstName,
          lastName: registerData.lastName,
          isEmailVerified: false
        }
      });
      expect(result).toMatchObject({
        id: mockUser.id,
        email: mockUser.email,
        nickname: mockUser.nickname
      });
    });

    test('应该拒绝已存在的邮箱', async () => {
      const existingUser = { id: 'existing-user', email: '<EMAIL>' };
      
      mockPrisma.user.findUnique.mockResolvedValue(existingUser);

      await expect(authService.register(registerData, '127.0.0.1'))
        .rejects.toThrow('邮箱已存在');

      expect(mockPrisma.user.create).not.toHaveBeenCalled();
    });

    test('应该处理数据库错误', async () => {
      (hashPassword as jest.Mock).mockResolvedValue('hashed-password');
      mockPrisma.user.findUnique.mockResolvedValue(null);
      mockPrisma.user.create.mockRejectedValue(new Error('数据库错误'));

      await expect(authService.register(registerData, '127.0.0.1'))
        .rejects.toThrow('数据库错误');
    });
  });

  describe('login', () => {
    const loginData = {
      username: '<EMAIL>',
      password: 'Password123!',
      rememberMe: false
    };

    test('应该成功登录用户', async () => {
      const mockUser = {
        id: 'user-123',
        email: '<EMAIL>',
        passwordHash: 'hashed-password',
        nickname: 'Test User',
        isEmailVerified: true,
        isActive: true,
        lastLoginAt: null
      };

      const mockTokens = {
        accessToken: 'access-token',
        refreshToken: 'refresh-token',
        expiresIn: 900
      };

      const mockSession = {
        id: 'session-123',
        userId: 'user-123',
        token: 'session-token'
      };

      // 模拟依赖
      mockPrisma.user.findFirst.mockResolvedValue(mockUser);
      (comparePassword as jest.Mock).mockResolvedValue(true);
      (generateAccessToken as jest.Mock).mockReturnValue(mockTokens.accessToken);
      (generateRefreshToken as jest.Mock).mockReturnValue(mockTokens.refreshToken);
      mockPrisma.session.create.mockResolvedValue(mockSession);
      mockPrisma.user.update.mockResolvedValue(mockUser);

      const result = await authService.login({
        ...loginData,
        ipAddress: '127.0.0.1',
        userAgent: 'test-user-agent'
      });

      expect(comparePassword).toHaveBeenCalledWith(loginData.password, mockUser.passwordHash);
      expect(mockPrisma.session.create).toHaveBeenCalled();
      expect(mockPrisma.user.update).toHaveBeenCalledWith({
        where: { id: mockUser.id },
        data: { lastLoginAt: expect.any(Date) }
      });
      
      expect(result).toMatchObject({
        user: {
          id: mockUser.id,
          email: mockUser.email,
          nickname: mockUser.nickname
        },
        tokens: {
          accessToken: mockTokens.accessToken,
          refreshToken: mockTokens.refreshToken
        },
        sessionId: mockSession.id
      });
    });

    test('应该拒绝不存在的用户', async () => {
      mockPrisma.user.findFirst.mockResolvedValue(null);

      await expect(authService.login({
        ...loginData,
        ipAddress: '127.0.0.1',
        userAgent: 'test-user-agent'
      }))
        .rejects.toThrow('无效的用户名或密码');

      expect(comparePassword).not.toHaveBeenCalled();
    });

    test('应该拒绝错误的密码', async () => {
      const mockUser = {
        id: 'user-123',
        email: '<EMAIL>',
        passwordHash: 'hashed-password',
        isEmailVerified: true,
        isActive: true
      };

      mockPrisma.user.findFirst.mockResolvedValue(mockUser);
      (comparePassword as jest.Mock).mockResolvedValue(false);

      await expect(authService.login({
        ...loginData,
        ipAddress: '127.0.0.1',
        userAgent: 'test-user-agent'
      }))
        .rejects.toThrow('无效的用户名或密码');

      expect(mockPrisma.session.create).not.toHaveBeenCalled();
    });

    test('应该拒绝未验证邮箱的用户', async () => {
      const mockUser = {
        id: 'user-123',
        email: '<EMAIL>',
        passwordHash: 'hashed-password',
        isEmailVerified: false,
        isActive: true
      };

      mockPrisma.user.findFirst.mockResolvedValue(mockUser);
      (comparePassword as jest.Mock).mockResolvedValue(true);

      await expect(authService.login({
        ...loginData,
        ipAddress: '127.0.0.1',
        userAgent: 'test-user-agent'
      }))
        .rejects.toThrow('请先验证邮箱');
    });

    test('应该拒绝被禁用的用户', async () => {
      const mockUser = {
        id: 'user-123',
        email: '<EMAIL>',
        passwordHash: 'hashed-password',
        isEmailVerified: true,
        isActive: false
      };

      mockPrisma.user.findFirst.mockResolvedValue(mockUser);
      (comparePassword as jest.Mock).mockResolvedValue(true);

      await expect(authService.login({
        ...loginData,
        ipAddress: '127.0.0.1',
        userAgent: 'test-user-agent'
      }))
        .rejects.toThrow('账户已被禁用');
    });
  });

  describe('logout', () => {
    test('应该成功登出用户', async () => {
      const refreshToken = 'valid-refresh-token';
      const mockSession = {
        id: 'session-123',
        userId: 'user-123',
        token: 'session-token'
      };

      mockPrisma.session.findFirst.mockResolvedValue(mockSession);
      mockPrisma.session.delete.mockResolvedValue(mockSession);
      mockCacheService.deleteSession.mockResolvedValue(undefined);

      await authService.logout(refreshToken, '127.0.0.1');

      expect(mockPrisma.session.delete).toHaveBeenCalledWith({
        where: { id: mockSession.id }
      });
      expect(mockCacheService.deleteSession).toHaveBeenCalledWith(mockSession.id);
    });

    test('应该处理无效的刷新令牌', async () => {
      const refreshToken = 'invalid-refresh-token';

      mockPrisma.session.findFirst.mockResolvedValue(null);

      await expect(authService.logout(refreshToken, '127.0.0.1'))
        .rejects.toThrow('无效的刷新令牌');

      expect(mockPrisma.session.delete).not.toHaveBeenCalled();
    });
  });

  describe('refreshToken', () => {
    test('应该成功刷新访问令牌', async () => {
      const refreshToken = 'valid-refresh-token';
      const mockSession = {
        id: 'session-123',
        userId: 'user-123',
        token: 'session-token',
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24小时后过期
        user: {
          id: 'user-123',
          email: '<EMAIL>',
          isActive: true
        }
      };

      const newAccessToken = 'new-access-token';

      mockPrisma.session.findFirst.mockResolvedValue(mockSession);
      (generateAccessToken as jest.Mock).mockReturnValue(newAccessToken);

      const result = await authService.refreshToken(refreshToken);

      expect(result).toMatchObject({
        accessToken: newAccessToken,
        expiresIn: 900
      });
    });

    test('应该拒绝过期的刷新令牌', async () => {
      const refreshToken = 'expired-refresh-token';
      const mockSession = {
        id: 'session-123',
        userId: 'user-123',
        expiresAt: new Date(Date.now() - 1000), // 已过期
        user: {
          id: 'user-123',
          isActive: true
        }
      };

      mockPrisma.session.findFirst.mockResolvedValue(mockSession);

      await expect(authService.refreshToken(refreshToken))
        .rejects.toThrow('刷新令牌已过期');
    });

    test('应该拒绝被禁用用户的令牌', async () => {
      const refreshToken = 'valid-refresh-token';
      const mockSession = {
        id: 'session-123',
        userId: 'user-123',
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000),
        user: {
          id: 'user-123',
          isActive: false
        }
      };

      mockPrisma.session.findFirst.mockResolvedValue(mockSession);

      await expect(authService.refreshToken(refreshToken))
        .rejects.toThrow('用户账户已被禁用');
    });
  });

  describe('forgotPassword', () => {
    test('应该成功发送密码重置邮件', async () => {
      const email = '<EMAIL>';
      const mockUser = {
        id: 'user-123',
        email: '<EMAIL>',
        isActive: true
      };

      mockPrisma.user.findUnique.mockResolvedValue(mockUser);
      mockPrisma.passwordResetToken.create.mockResolvedValue({
        id: 'token-123',
        token: 'reset-token'
      });

      await authService.forgotPassword(email);

      expect(mockPrisma.passwordResetToken.create).toHaveBeenCalled();
    });

    test('应该处理不存在的邮箱', async () => {
      const email = '<EMAIL>';

      mockPrisma.user.findUnique.mockResolvedValue(null);

      // 应该静默处理，不暴露用户是否存在
      await expect(authService.forgotPassword(email)).resolves.not.toThrow();
      expect(mockPrisma.passwordResetToken.create).not.toHaveBeenCalled();
    });
  });

  describe('resetPassword', () => {
    test('应该成功重置密码', async () => {
      const token = 'valid-reset-token';
      const newPassword = 'NewPassword123!';
      const hashedPassword = 'new-hashed-password';

      const mockResetToken = {
        id: 'token-123',
        token: 'valid-reset-token',
        userId: 'user-123',
        expiresAt: new Date(Date.now() + 60 * 60 * 1000), // 1小时后过期
        used: false,
        user: {
          id: 'user-123',
          isActive: true
        }
      };

      mockPrisma.passwordResetToken.findFirst.mockResolvedValue(mockResetToken);
      (hashPassword as jest.Mock).mockResolvedValue(hashedPassword);
      mockPrisma.user.update.mockResolvedValue({});
      mockPrisma.passwordResetToken.update.mockResolvedValue({});

      await authService.resetPassword(token, newPassword);

      expect(hashPassword).toHaveBeenCalledWith(newPassword);
      expect(mockPrisma.user.update).toHaveBeenCalledWith({
        where: { id: mockResetToken.userId },
        data: { passwordHash: hashedPassword }
      });
      expect(mockPrisma.passwordResetToken.update).toHaveBeenCalledWith({
        where: { id: mockResetToken.id },
        data: { used: true }
      });
    });

    test('应该拒绝无效的重置令牌', async () => {
      const token = 'invalid-reset-token';
      const newPassword = 'NewPassword123!';

      mockPrisma.passwordResetToken.findFirst.mockResolvedValue(null);

      await expect(authService.resetPassword(token, newPassword))
        .rejects.toThrow('无效的重置令牌');

      expect(hashPassword).not.toHaveBeenCalled();
    });

    test('应该拒绝过期的重置令牌', async () => {
      const token = 'expired-reset-token';
      const newPassword = 'NewPassword123!';

      const mockResetToken = {
        id: 'token-123',
        expiresAt: new Date(Date.now() - 1000), // 已过期
        used: false,
        user: { isActive: true }
      };

      mockPrisma.passwordResetToken.findFirst.mockResolvedValue(mockResetToken);

      await expect(authService.resetPassword(token, newPassword))
        .rejects.toThrow('重置令牌已过期');
    });

    test('应该拒绝已使用的重置令牌', async () => {
      const token = 'used-reset-token';
      const newPassword = 'NewPassword123!';

      const mockResetToken = {
        id: 'token-123',
        expiresAt: new Date(Date.now() + 60 * 60 * 1000),
        used: true,
        user: { isActive: true }
      };

      mockPrisma.passwordResetToken.findFirst.mockResolvedValue(mockResetToken);

      await expect(authService.resetPassword(token, newPassword))
        .rejects.toThrow('重置令牌已被使用');
    });
  });
});
