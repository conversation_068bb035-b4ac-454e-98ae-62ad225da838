/**
 * 基础协议适配器
 * 为所有协议适配器提供通用功能
 */

import { Request, Response } from 'express';
import { 
  IProtocolAdapter, 
  ProtocolConfig, 
  AuthenticationRequest, 
  AuthenticationResponse,
  ProtocolAdapterError
} from '@/types/protocol-adapter';
import { logger } from '@/config/logger';
import { generateTokenPair } from '@/utils/jwt';
import { prisma } from '@/config/database';

/**
 * 基础协议适配器抽象类
 */
export abstract class BaseProtocolAdapter implements IProtocolAdapter {
  protected config: ProtocolConfig;
  protected initialized: boolean = false;

  abstract readonly name: string;
  abstract readonly version: string;
  abstract readonly supportedMethods: string[];

  /**
   * 初始化适配器
   */
  async initialize(config: ProtocolConfig): Promise<void> {
    try {
      // 验证配置
      const isValid = await this.validateConfig(config);
      if (!isValid) {
        throw new ProtocolAdapterError(
          'INVALID_CONFIG',
          `协议 ${this.name} 的配置无效`
        );
      }

      this.config = config;
      this.initialized = true;

      // 执行子类特定的初始化
      await this.onInitialize();

      logger.info('协议适配器初始化成功', {
        name: this.name,
        version: this.version
      });
    } catch (error) {
      logger.error('协议适配器初始化失败', {
        name: this.name,
        error
      });
      throw error;
    }
  }

  /**
   * 子类特定的初始化逻辑
   */
  protected async onInitialize(): Promise<void> {
    // 子类可以重写此方法
  }

  /**
   * 检查是否已初始化
   */
  protected ensureInitialized(): void {
    if (!this.initialized) {
      throw new ProtocolAdapterError(
        'NOT_INITIALIZED',
        `协议适配器 ${this.name} 未初始化`
      );
    }
  }

  /**
   * 处理认证请求
   */
  async handleAuthRequest(request: AuthenticationRequest): Promise<AuthenticationResponse> {
    this.ensureInitialized();

    try {
      logger.info('处理认证请求', {
        protocol: this.name,
        applicationId: request.applicationId,
        clientId: request.clientId
      });

      // 验证应用
      const application = await this.validateApplication(request.clientId);
      if (!application) {
        return this.createErrorResponse('INVALID_CLIENT', '无效的客户端ID');
      }

      // 验证重定向URI
      if (request.redirectUri && !this.validateRedirectUri(application, request.redirectUri)) {
        return this.createErrorResponse('INVALID_REDIRECT_URI', '无效的重定向URI');
      }

      // 执行协议特定的认证逻辑
      return await this.performAuthentication(request, application);

    } catch (error) {
      logger.error('处理认证请求失败', {
        protocol: this.name,
        error
      });

      return this.createErrorResponse(
        'AUTHENTICATION_ERROR',
        error.message || '认证处理失败'
      );
    }
  }

  /**
   * 处理令牌请求
   */
  async handleTokenRequest(request: Request): Promise<AuthenticationResponse> {
    this.ensureInitialized();

    try {
      logger.info('处理令牌请求', {
        protocol: this.name,
        method: request.method,
        path: request.path
      });

      return await this.performTokenExchange(request);

    } catch (error) {
      logger.error('处理令牌请求失败', {
        protocol: this.name,
        error
      });

      return this.createErrorResponse(
        'TOKEN_ERROR',
        error.message || '令牌处理失败'
      );
    }
  }

  /**
   * 处理用户信息请求
   */
  async handleUserInfoRequest(request: Request): Promise<AuthenticationResponse> {
    this.ensureInitialized();

    try {
      logger.info('处理用户信息请求', {
        protocol: this.name
      });

      return await this.performUserInfoRetrieval(request);

    } catch (error) {
      logger.error('处理用户信息请求失败', {
        protocol: this.name,
        error
      });

      return this.createErrorResponse(
        'USERINFO_ERROR',
        error.message || '用户信息获取失败'
      );
    }
  }

  /**
   * 处理登出请求
   */
  async handleLogoutRequest(request: Request): Promise<AuthenticationResponse> {
    this.ensureInitialized();

    try {
      logger.info('处理登出请求', {
        protocol: this.name
      });

      return await this.performLogout(request);

    } catch (error) {
      logger.error('处理登出请求失败', {
        protocol: this.name,
        error
      });

      return this.createErrorResponse(
        'LOGOUT_ERROR',
        error.message || '登出处理失败'
      );
    }
  }

  /**
   * 生成元数据
   */
  async generateMetadata(): Promise<Record<string, any>> {
    this.ensureInitialized();

    const baseMetadata = {
      protocol: this.name,
      version: this.version,
      issuer: this.config.customSettings?.issuer || 'http://localhost:3000',
      endpoints: this.config.endpoints,
      supported_methods: this.supportedMethods,
      generated_at: new Date().toISOString()
    };

    // 子类可以扩展元数据
    const customMetadata = await this.generateCustomMetadata();
    
    return {
      ...baseMetadata,
      ...customMetadata
    };
  }

  /**
   * 验证配置
   */
  async validateConfig(config: ProtocolConfig): Promise<boolean> {
    try {
      // 基础验证
      if (!config.name || !config.version) {
        return false;
      }

      if (!config.endpoints || Object.keys(config.endpoints).length === 0) {
        return false;
      }

      // 子类特定验证
      return await this.validateCustomConfig(config);

    } catch (error) {
      logger.error('配置验证失败', { error });
      return false;
    }
  }

  // 抽象方法 - 子类必须实现

  /**
   * 执行协议特定的认证逻辑
   */
  protected abstract performAuthentication(
    request: AuthenticationRequest, 
    application: any
  ): Promise<AuthenticationResponse>;

  /**
   * 执行令牌交换
   */
  protected abstract performTokenExchange(request: Request): Promise<AuthenticationResponse>;

  /**
   * 执行用户信息获取
   */
  protected abstract performUserInfoRetrieval(request: Request): Promise<AuthenticationResponse>;

  /**
   * 执行登出
   */
  protected abstract performLogout(request: Request): Promise<AuthenticationResponse>;

  /**
   * 生成自定义元数据
   */
  protected abstract generateCustomMetadata(): Promise<Record<string, any>>;

  /**
   * 验证自定义配置
   */
  protected abstract validateCustomConfig(config: ProtocolConfig): Promise<boolean>;

  // 辅助方法

  /**
   * 验证应用
   */
  protected async validateApplication(clientId: string): Promise<any> {
    try {
      const application = await prisma.application.findUnique({
        where: { 
          clientId,
          isActive: true
        }
      });

      return application;
    } catch (error) {
      logger.error('验证应用失败', { clientId, error });
      return null;
    }
  }

  /**
   * 验证重定向URI
   */
  protected validateRedirectUri(application: any, redirectUri: string): boolean {
    if (!application.redirectUris || application.redirectUris.length === 0) {
      return false;
    }

    return application.redirectUris.includes(redirectUri);
  }

  /**
   * 创建错误响应
   */
  protected createErrorResponse(code: string, message: string): AuthenticationResponse {
    return {
      success: false,
      error: {
        code,
        message
      }
    };
  }

  /**
   * 创建成功响应
   */
  protected createSuccessResponse(
    tokens?: any, 
    user?: any, 
    customData?: Record<string, any>
  ): AuthenticationResponse {
    return {
      success: true,
      tokens,
      user,
      customData
    };
  }

  /**
   * 生成标准JWT令牌
   */
  protected async generateStandardTokens(
    user: any, 
    sessionId: string,
    customClaims?: Record<string, any>
  ): Promise<any> {
    // 使用完整的用户对象生成令牌
    return generateTokenPair(user, sessionId);
  }

  /**
   * 从请求中提取Bearer令牌
   */
  protected extractBearerToken(request: Request): string | null {
    const authHeader = request.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return null;
    }

    return authHeader.substring(7);
  }

  /**
   * 记录审计日志
   */
  protected async logAuditEvent(
    action: string,
    userId?: string,
    applicationId?: string,
    details?: Record<string, any>
  ): Promise<void> {
    try {
      await prisma.auditLog.create({
        data: {
          userId,
          applicationId,
          action,
          resource: 'protocol_adapter',
          details: {
            protocol: this.name,
            ...details
          },
          success: true
        }
      });
    } catch (error) {
      logger.error('记录审计日志失败', { error });
    }
  }
}
