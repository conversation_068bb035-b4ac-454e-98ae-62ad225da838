/**
 * 自定义OAuth适配器
 * 支持非标准OAuth实现和自定义认证流程
 */

import { Request } from 'express';
import { BaseProtocolAdapter } from './base-protocol.adapter';
import { 
  ProtocolConfig, 
  AuthenticationRequest, 
  AuthenticationResponse,
  ProtocolAdapterError
} from '@/types/protocol-adapter';
import { logger } from '@/config/logger';
import { prisma } from '@/config/database';
import crypto from 'crypto';
import jwt from 'jsonwebtoken';
import { config } from '@/config';

/**
 * 自定义OAuth适配器
 * 支持各种非标准OAuth实现
 */
export class CustomOAuthAdapter extends BaseProtocolAdapter {
  readonly name = 'custom-oauth';
  readonly version = '2.0';
  readonly supportedMethods = [
    'authorization_code',
    'client_credentials',
    'password',
    'refresh_token',
    'custom_grant'
  ];

  private authorizationCodes: Map<string, any> = new Map();
  private accessTokens: Map<string, any> = new Map();

  /**
   * 执行协议特定的认证逻辑
   */
  protected async performAuthentication(
    request: AuthenticationRequest,
    application: any
  ): Promise<AuthenticationResponse> {
    try {
      const { scope, state, redirectUri, customParams } = request;

      // 检查用户是否已认证
      if (!request.userId) {
        // 生成授权URL，重定向到登录页面
        const authUrl = this.buildAuthorizationUrl(request, application);
        return {
          success: true,
          redirectUrl: authUrl
        };
      }

      // 用户已认证，生成授权码
      const authCode = this.generateAuthorizationCode();
      const codeData = {
        code: authCode,
        clientId: request.clientId,
        userId: request.userId,
        scope: scope || [],
        redirectUri,
        expiresAt: new Date(Date.now() + 10 * 60 * 1000), // 10分钟过期
        customParams
      };

      this.authorizationCodes.set(authCode, codeData);

      // 构建回调URL
      const callbackUrl = this.buildCallbackUrl(redirectUri!, authCode, state);

      await this.logAuditEvent(
        'authorization_granted',
        request.userId,
        request.applicationId,
        { scope, redirectUri }
      );

      return {
        success: true,
        redirectUrl: callbackUrl
      };

    } catch (error) {
      logger.error('自定义OAuth认证失败', { error });
      throw error;
    }
  }

  /**
   * 执行令牌交换
   */
  protected async performTokenExchange(request: Request): Promise<AuthenticationResponse> {
    try {
      const { grant_type, code, client_id, client_secret, redirect_uri, refresh_token } = request.body;

      // 验证客户端
      const application = await this.validateApplication(client_id);
      if (!application || application.clientSecret !== client_secret) {
        return this.createErrorResponse('invalid_client', '无效的客户端凭据');
      }

      switch (grant_type) {
        case 'authorization_code':
          return await this.handleAuthorizationCodeGrant(code, client_id, redirect_uri);
        
        case 'refresh_token':
          return await this.handleRefreshTokenGrant(refresh_token, client_id);
        
        case 'client_credentials':
          return await this.handleClientCredentialsGrant(client_id, request.body.scope);
        
        case 'password':
          return await this.handlePasswordGrant(
            request.body.username,
            request.body.password,
            client_id,
            request.body.scope
          );
        
        default:
          // 支持自定义授权类型
          if (grant_type.startsWith('custom:')) {
            return await this.handleCustomGrant(grant_type, request.body, application);
          }
          
          return this.createErrorResponse('unsupported_grant_type', '不支持的授权类型');
      }

    } catch (error) {
      logger.error('令牌交换失败', { error });
      return this.createErrorResponse('server_error', '服务器内部错误');
    }
  }

  /**
   * 处理授权码授权
   */
  private async handleAuthorizationCodeGrant(
    code: string,
    clientId: string,
    redirectUri: string
  ): Promise<AuthenticationResponse> {
    const codeData = this.authorizationCodes.get(code);
    
    if (!codeData) {
      return this.createErrorResponse('invalid_grant', '无效的授权码');
    }

    if (codeData.expiresAt < new Date()) {
      this.authorizationCodes.delete(code);
      return this.createErrorResponse('invalid_grant', '授权码已过期');
    }

    if (codeData.clientId !== clientId) {
      return this.createErrorResponse('invalid_grant', '客户端ID不匹配');
    }

    if (codeData.redirectUri !== redirectUri) {
      return this.createErrorResponse('invalid_grant', '重定向URI不匹配');
    }

    // 删除已使用的授权码
    this.authorizationCodes.delete(code);

    // 生成访问令牌
    const tokens = await this.generateTokens(codeData.userId, clientId, codeData.scope);

    // 获取用户信息
    const user = await this.getUserInfo(codeData.userId);

    await this.logAuditEvent(
      'token_issued',
      codeData.userId,
      undefined,
      { clientId, grantType: 'authorization_code' }
    );

    return {
      success: true,
      tokens,
      user
    };
  }

  /**
   * 处理刷新令牌授权
   */
  private async handleRefreshTokenGrant(
    refreshToken: string,
    clientId: string
  ): Promise<AuthenticationResponse> {
    try {
      // 验证刷新令牌
      const decoded = jwt.verify(refreshToken, config.jwt.refreshSecret) as any;
      
      if (decoded.clientId !== clientId) {
        return this.createErrorResponse('invalid_grant', '客户端ID不匹配');
      }

      // 生成新的访问令牌
      const tokens = await this.generateTokens(decoded.userId, clientId, decoded.scope);

      await this.logAuditEvent(
        'token_refreshed',
        decoded.userId,
        undefined,
        { clientId }
      );

      return {
        success: true,
        tokens
      };

    } catch (error) {
      return this.createErrorResponse('invalid_grant', '无效的刷新令牌');
    }
  }

  /**
   * 处理客户端凭据授权
   */
  private async handleClientCredentialsGrant(
    clientId: string,
    scope?: string[]
  ): Promise<AuthenticationResponse> {
    // 生成应用级别的访问令牌
    const tokens = await this.generateClientTokens(clientId, scope);

    await this.logAuditEvent(
      'client_token_issued',
      undefined,
      undefined,
      { clientId, grantType: 'client_credentials' }
    );

    return {
      success: true,
      tokens
    };
  }

  /**
   * 处理密码授权
   */
  private async handlePasswordGrant(
    username: string,
    password: string,
    clientId: string,
    scope?: string[]
  ): Promise<AuthenticationResponse> {
    // 验证用户凭据
    const user = await this.validateUserCredentials(username, password);
    if (!user) {
      return this.createErrorResponse('invalid_grant', '无效的用户凭据');
    }

    // 生成访问令牌
    const tokens = await this.generateTokens(user.id, clientId, scope);
    const userInfo = await this.getUserInfo(user.id);

    await this.logAuditEvent(
      'password_grant_issued',
      user.id,
      undefined,
      { clientId, grantType: 'password' }
    );

    return {
      success: true,
      tokens,
      user: userInfo
    };
  }

  /**
   * 处理自定义授权类型
   */
  private async handleCustomGrant(
    grantType: string,
    params: any,
    application: any
  ): Promise<AuthenticationResponse> {
    // 这里可以实现各种自定义授权逻辑
    const customType = grantType.replace('custom:', '');

    switch (customType) {
      case 'api_key':
        return await this.handleApiKeyGrant(params, application);
      
      case 'device_code':
        return await this.handleDeviceCodeGrant(params, application);
      
      case 'token_exchange':
        return await this.handleTokenExchangeGrant(params, application);
      
      default:
        return this.createErrorResponse('unsupported_grant_type', `不支持的自定义授权类型: ${customType}`);
    }
  }

  /**
   * 处理API密钥授权
   */
  private async handleApiKeyGrant(params: any, application: any): Promise<AuthenticationResponse> {
    const { api_key, user_id } = params;

    // 验证API密钥
    const isValid = await this.validateApiKey(api_key, application.id);
    if (!isValid) {
      return this.createErrorResponse('invalid_grant', '无效的API密钥');
    }

    // 生成访问令牌
    const tokens = await this.generateTokens(user_id, application.clientId, ['api']);

    return {
      success: true,
      tokens
    };
  }

  /**
   * 执行用户信息获取
   */
  protected async performUserInfoRetrieval(request: Request): Promise<AuthenticationResponse> {
    try {
      const accessToken = this.extractBearerToken(request);
      if (!accessToken) {
        return this.createErrorResponse('invalid_token', '缺少访问令牌');
      }

      // 验证访问令牌
      const tokenData = await this.validateAccessToken(accessToken);
      if (!tokenData) {
        return this.createErrorResponse('invalid_token', '无效的访问令牌');
      }

      // 获取用户信息
      const user = await this.getUserInfo(tokenData.userId);
      if (!user) {
        return this.createErrorResponse('invalid_token', '用户不存在');
      }

      return {
        success: true,
        user
      };

    } catch (error) {
      logger.error('获取用户信息失败', { error });
      return this.createErrorResponse('server_error', '服务器内部错误');
    }
  }

  /**
   * 执行登出
   */
  protected async performLogout(request: Request): Promise<AuthenticationResponse> {
    try {
      const accessToken = this.extractBearerToken(request);
      if (accessToken) {
        // 撤销访问令牌
        await this.revokeAccessToken(accessToken);
      }

      return {
        success: true
      };

    } catch (error) {
      logger.error('登出失败', { error });
      return this.createErrorResponse('server_error', '登出失败');
    }
  }

  /**
   * 生成自定义元数据
   */
  protected async generateCustomMetadata(): Promise<Record<string, any>> {
    return {
      grant_types_supported: this.supportedMethods,
      response_types_supported: ['code', 'token'],
      scopes_supported: ['openid', 'profile', 'email', 'api'],
      token_endpoint_auth_methods_supported: ['client_secret_basic', 'client_secret_post'],
      custom_grants_supported: ['custom:api_key', 'custom:device_code', 'custom:token_exchange']
    };
  }

  /**
   * 验证自定义配置
   */
  protected async validateCustomConfig(config: ProtocolConfig): Promise<boolean> {
    // 验证必需的端点
    const requiredEndpoints = ['authorization', 'token'];
    for (const endpoint of requiredEndpoints) {
      if (!config.endpoints[endpoint]) {
        return false;
      }
    }

    return true;
  }

  // 辅助方法

  private generateAuthorizationCode(): string {
    return crypto.randomBytes(32).toString('hex');
  }

  private buildAuthorizationUrl(request: AuthenticationRequest, application: any): string {
    const params = new URLSearchParams({
      response_type: 'code',
      client_id: request.clientId,
      redirect_uri: request.redirectUri || '',
      scope: (request.scope || []).join(' '),
      state: request.state || ''
    });

    return `/auth/login?${params.toString()}`;
  }

  private buildCallbackUrl(redirectUri: string, code: string, state?: string): string {
    const url = new URL(redirectUri);
    url.searchParams.set('code', code);
    if (state) {
      url.searchParams.set('state', state);
    }
    return url.toString();
  }

  private async generateTokens(userId: string, clientId: string, scope?: string[]): Promise<any> {
    const payload = {
      userId,
      clientId,
      scope: scope || [],
      type: 'access_token'
    };

    const accessToken = jwt.sign(payload, config.jwt.secret, { expiresIn: '1h' });
    const refreshToken = jwt.sign(
      { ...payload, type: 'refresh_token' },
      config.jwt.refreshSecret,
      { expiresIn: '30d' }
    );

    return {
      access_token: accessToken,
      refresh_token: refreshToken,
      token_type: 'Bearer',
      expires_in: 3600,
      scope: (scope || []).join(' ')
    };
  }

  private async generateClientTokens(clientId: string, scope?: string[]): Promise<any> {
    const payload = {
      clientId,
      scope: scope || [],
      type: 'client_credentials'
    };

    const accessToken = jwt.sign(payload, config.jwt.secret, { expiresIn: '1h' });

    return {
      access_token: accessToken,
      token_type: 'Bearer',
      expires_in: 3600,
      scope: (scope || []).join(' ')
    };
  }

  private async validateAccessToken(token: string): Promise<any> {
    try {
      const decoded = jwt.verify(token, config.jwt.secret);
      return decoded;
    } catch (error) {
      return null;
    }
  }

  private async getUserInfo(userId: string): Promise<any> {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        email: true,
        nickname: true,
        firstName: true,
        lastName: true,
        avatar: true,
        emailVerified: true
      }
    });

    return user;
  }

  private async validateUserCredentials(username: string, password: string): Promise<any> {
    // 这里应该实现实际的用户验证逻辑
    // 为了示例，这里返回null
    return null;
  }

  private async validateApiKey(apiKey: string, applicationId: string): Promise<boolean> {
    // 这里应该实现API密钥验证逻辑
    return false;
  }

  private async revokeAccessToken(token: string): Promise<void> {
    // 这里应该实现令牌撤销逻辑
    // 可以将令牌加入黑名单或从存储中删除
  }
}
