/**
 * Webhook协议适配器
 * 支持基于Webhook的认证和事件通知
 */

import { Request, Response } from 'express';
import { BaseProtocolAdapter } from './base-protocol.adapter';
import { 
  AuthenticationRequest, 
  AuthenticationResponse, 
  ProtocolConfig,
  ProtocolAdapterError 
} from '@/types/protocol-adapter';
import { logger } from '@/config/logger';
import crypto from 'crypto';
import axios from 'axios';

/**
 * Webhook事件类型
 */
export enum WebhookEventType {
  AUTH_REQUEST = 'auth.request',
  AUTH_SUCCESS = 'auth.success',
  AUTH_FAILURE = 'auth.failure',
  TOKEN_ISSUED = 'token.issued',
  TOKEN_REFRESHED = 'token.refreshed',
  TOKEN_REVOKED = 'token.revoked',
  USER_LOGIN = 'user.login',
  USER_LOGOUT = 'user.logout',
  SESSION_CREATED = 'session.created',
  SESSION_EXPIRED = 'session.expired'
}

/**
 * Webhook配置接口
 */
export interface WebhookConfig extends ProtocolConfig {
  webhooks: {
    authUrl: string;           // 认证Webhook URL
    eventUrl?: string;         // 事件通知URL
    secretKey: string;         // 签名密钥
    timeout?: number;          // 超时时间（毫秒）
    retryAttempts?: number;    // 重试次数
    retryDelay?: number;       // 重试延迟（毫秒）
  };
  signatureConfig: {
    algorithm: 'sha256' | 'sha1' | 'md5';
    headerName: string;        // 签名头部名称
    prefix?: string;           // 签名前缀
  };
  authConfig: {
    method: 'POST' | 'GET';
    headers?: Record<string, string>;
    responseField: string;     // 响应中的认证结果字段
    userField?: string;        // 响应中的用户信息字段
    tokenField?: string;       // 响应中的令牌字段
  };
  eventConfig?: {
    enabled: boolean;
    events: WebhookEventType[];
    batchSize?: number;        // 批量发送大小
    batchDelay?: number;       // 批量发送延迟
  };
}

/**
 * Webhook事件数据
 */
export interface WebhookEvent {
  id: string;
  type: WebhookEventType;
  timestamp: string;
  data: Record<string, any>;
  metadata?: Record<string, any>;
}

/**
 * Webhook协议适配器实现
 */
export class WebhookAdapter extends BaseProtocolAdapter {
  readonly name = 'webhook';
  readonly version = '1.0.0';
  readonly supportedMethods = ['webhook_auth', 'webhook_event'];

  private webhookConfig: WebhookConfig;
  private eventQueue: WebhookEvent[] = [];
  private batchTimer?: NodeJS.Timeout;

  /**
   * 初始化Webhook配置
   */
  protected async onInitialize(): Promise<void> {
    this.webhookConfig = this.config as WebhookConfig;
    
    // 验证Webhook配置
    await this.validateWebhookConfig();
    
    // 启动事件处理
    if (this.webhookConfig.eventConfig?.enabled) {
      this.startEventProcessor();
    }
    
    logger.info('Webhook适配器初始化完成', {
      authUrl: this.webhookConfig.webhooks.authUrl,
      eventUrl: this.webhookConfig.webhooks.eventUrl,
      eventsEnabled: this.webhookConfig.eventConfig?.enabled
    });
  }

  /**
   * 验证Webhook配置
   */
  private async validateWebhookConfig(): Promise<void> {
    const { webhooks, signatureConfig, authConfig } = this.webhookConfig;

    if (!webhooks.authUrl) {
      throw new ProtocolAdapterError('INVALID_CONFIG', 'Webhook认证URL不能为空');
    }

    if (!webhooks.secretKey) {
      throw new ProtocolAdapterError('INVALID_CONFIG', 'Webhook密钥不能为空');
    }

    if (!signatureConfig.algorithm || !signatureConfig.headerName) {
      throw new ProtocolAdapterError('INVALID_CONFIG', '签名配置不完整');
    }

    if (!authConfig.responseField) {
      throw new ProtocolAdapterError('INVALID_CONFIG', '认证响应字段不能为空');
    }

    // 测试Webhook连接
    try {
      await this.testWebhookConnection();
    } catch (error) {
      logger.warn('Webhook连接测试失败', { error: error.message });
    }
  }

  /**
   * 测试Webhook连接
   */
  private async testWebhookConnection(): Promise<void> {
    const testPayload = {
      type: 'connection_test',
      timestamp: new Date().toISOString(),
      data: { test: true }
    };

    await this.sendWebhookRequest(
      this.webhookConfig.webhooks.authUrl,
      testPayload,
      { timeout: 5000 }
    );
  }

  /**
   * 执行协议特定的认证逻辑
   */
  protected async performAuthentication(
    request: AuthenticationRequest,
    application: any
  ): Promise<AuthenticationResponse> {
    try {
      // 构建认证请求负载
      const authPayload = {
        type: 'auth_request',
        timestamp: new Date().toISOString(),
        data: {
          applicationId: request.applicationId,
          clientId: request.clientId,
          username: request.credentials?.username,
          password: request.credentials?.password,
          redirectUri: request.redirectUri,
          state: request.state,
          scope: request.scope,
          metadata: request.metadata
        }
      };

      // 发送认证Webhook请求
      const response = await this.sendWebhookRequest(
        this.webhookConfig.webhooks.authUrl,
        authPayload
      );

      // 解析认证响应
      const authResult = this.parseAuthResponse(response);

      if (authResult.success) {
        // 发送认证成功事件
        await this.emitEvent(WebhookEventType.AUTH_SUCCESS, {
          applicationId: request.applicationId,
          clientId: request.clientId,
          username: request.credentials?.username,
          timestamp: new Date().toISOString()
        });

        return {
          success: true,
          data: {
            access_token: authResult.token,
            token_type: 'Bearer',
            expires_in: authResult.expiresIn || 3600,
            user: authResult.user
          }
        };
      } else {
        // 发送认证失败事件
        await this.emitEvent(WebhookEventType.AUTH_FAILURE, {
          applicationId: request.applicationId,
          clientId: request.clientId,
          username: request.credentials?.username,
          error: authResult.error,
          timestamp: new Date().toISOString()
        });

        return {
          success: false,
          error: authResult.error || 'WEBHOOK_AUTH_FAILED',
          message: authResult.message || 'Webhook认证失败'
        };
      }

    } catch (error) {
      logger.error('Webhook认证失败', { error: error.message });
      
      await this.emitEvent(WebhookEventType.AUTH_FAILURE, {
        applicationId: request.applicationId,
        clientId: request.clientId,
        error: 'WEBHOOK_ERROR',
        message: error.message,
        timestamp: new Date().toISOString()
      });

      return {
        success: false,
        error: 'WEBHOOK_ERROR',
        message: error.message
      };
    }
  }

  /**
   * 发送Webhook请求
   */
  private async sendWebhookRequest(
    url: string,
    payload: any,
    options: { timeout?: number; retries?: number } = {}
  ): Promise<any> {
    const { timeout = this.webhookConfig.webhooks.timeout || 10000 } = options;
    const maxRetries = options.retries ?? this.webhookConfig.webhooks.retryAttempts ?? 3;
    const retryDelay = this.webhookConfig.webhooks.retryDelay || 1000;

    const requestBody = JSON.stringify(payload);
    const signature = this.generateSignature(requestBody);

    const headers = {
      'Content-Type': 'application/json',
      [this.webhookConfig.signatureConfig.headerName]: signature,
      ...this.webhookConfig.authConfig.headers
    };

    let lastError: Error;

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        const response = await axios({
          method: this.webhookConfig.authConfig.method,
          url,
          data: requestBody,
          headers,
          timeout,
          validateStatus: (status) => status < 500 // 重试5xx错误
        });

        if (response.status >= 400) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        return response.data;

      } catch (error) {
        lastError = error as Error;
        
        if (attempt < maxRetries) {
          logger.warn(`Webhook请求失败，${retryDelay}ms后重试`, {
            attempt: attempt + 1,
            maxRetries,
            error: error.message
          });
          
          await new Promise(resolve => setTimeout(resolve, retryDelay * (attempt + 1)));
        }
      }
    }

    throw lastError!;
  }

  /**
   * 生成Webhook签名
   */
  private generateSignature(payload: string): string {
    const { algorithm, prefix = '' } = this.webhookConfig.signatureConfig;
    const secretKey = this.webhookConfig.webhooks.secretKey;

    const hmac = crypto.createHmac(algorithm, secretKey);
    hmac.update(payload);
    const signature = hmac.digest('hex');

    return prefix + signature;
  }

  /**
   * 验证Webhook签名
   */
  public verifySignature(payload: string, signature: string): boolean {
    const expectedSignature = this.generateSignature(payload);
    return crypto.timingSafeEqual(
      Buffer.from(signature),
      Buffer.from(expectedSignature)
    );
  }

  /**
   * 解析认证响应
   */
  private parseAuthResponse(response: any): any {
    const { responseField, userField, tokenField } = this.webhookConfig.authConfig;

    const success = response[responseField];
    const user = userField ? response[userField] : null;
    const token = tokenField ? response[tokenField] : null;

    return {
      success: Boolean(success),
      user,
      token,
      error: response.error,
      message: response.message,
      expiresIn: response.expires_in
    };
  }

  /**
   * 发送事件
   */
  public async emitEvent(type: WebhookEventType, data: Record<string, any>): Promise<void> {
    if (!this.webhookConfig.eventConfig?.enabled) {
      return;
    }

    if (!this.webhookConfig.eventConfig.events.includes(type)) {
      return;
    }

    const event: WebhookEvent = {
      id: crypto.randomUUID(),
      type,
      timestamp: new Date().toISOString(),
      data,
      metadata: {
        source: 'webhook-adapter',
        version: this.version
      }
    };

    this.eventQueue.push(event);

    // 如果队列达到批量大小，立即发送
    const batchSize = this.webhookConfig.eventConfig.batchSize || 10;
    if (this.eventQueue.length >= batchSize) {
      await this.flushEventQueue();
    }
  }

  /**
   * 启动事件处理器
   */
  private startEventProcessor(): void {
    const batchDelay = this.webhookConfig.eventConfig?.batchDelay || 5000;

    this.batchTimer = setInterval(async () => {
      if (this.eventQueue.length > 0) {
        await this.flushEventQueue();
      }
    }, batchDelay);
  }

  /**
   * 刷新事件队列
   */
  private async flushEventQueue(): Promise<void> {
    if (this.eventQueue.length === 0) {
      return;
    }

    const events = this.eventQueue.splice(0);
    const eventUrl = this.webhookConfig.webhooks.eventUrl;

    if (!eventUrl) {
      logger.warn('事件URL未配置，跳过事件发送');
      return;
    }

    try {
      await this.sendWebhookRequest(eventUrl, { events });
      logger.info('事件批量发送成功', { count: events.length });
    } catch (error) {
      logger.error('事件发送失败', { 
        count: events.length, 
        error: error.message 
      });
      
      // 重新加入队列（可选）
      // this.eventQueue.unshift(...events);
    }
  }

  /**
   * 处理自定义端点
   */
  async handleCustomEndpoint(endpoint: string, request: Request, response: Response): Promise<void> {
    if (endpoint === 'webhook') {
      await this.handleIncomingWebhook(request, response);
    } else {
      response.status(404).json({ error: 'Endpoint not found' });
    }
  }

  /**
   * 处理传入的Webhook
   */
  private async handleIncomingWebhook(request: Request, response: Response): Promise<void> {
    try {
      const signature = request.headers[this.webhookConfig.signatureConfig.headerName] as string;
      const payload = JSON.stringify(request.body);

      // 验证签名
      if (!this.verifySignature(payload, signature)) {
        response.status(401).json({ error: 'Invalid signature' });
        return;
      }

      // 处理Webhook事件
      await this.processIncomingWebhook(request.body);

      response.status(200).json({ success: true });

    } catch (error) {
      logger.error('处理传入Webhook失败', { error: error.message });
      response.status(500).json({ error: 'Internal server error' });
    }
  }

  /**
   * 处理传入的Webhook事件
   */
  private async processIncomingWebhook(data: any): Promise<void> {
    logger.info('收到Webhook事件', { type: data.type, timestamp: data.timestamp });
    
    // 这里可以根据事件类型执行相应的处理逻辑
    switch (data.type) {
      case 'user_updated':
        // 处理用户更新事件
        break;
      case 'permission_changed':
        // 处理权限变更事件
        break;
      default:
        logger.warn('未知的Webhook事件类型', { type: data.type });
    }
  }

  // 抽象方法实现

  protected async performTokenExchange(request: Request): Promise<AuthenticationResponse> {
    return { success: true, data: { message: 'Token exchange not supported' } };
  }

  protected async performUserInfoRetrieval(request: Request): Promise<AuthenticationResponse> {
    return { success: true, data: { message: 'User info retrieval not supported' } };
  }

  protected async performLogout(request: Request): Promise<AuthenticationResponse> {
    await this.emitEvent(WebhookEventType.USER_LOGOUT, {
      timestamp: new Date().toISOString()
    });
    
    return { success: true, data: { message: 'Logout successful' } };
  }

  protected async validateCustomConfig(config: ProtocolConfig): Promise<boolean> {
    const webhookConfig = config as WebhookConfig;
    return !!(webhookConfig.webhooks?.authUrl && webhookConfig.webhooks?.secretKey);
  }

  async generateMetadata(): Promise<Record<string, any>> {
    return {
      name: this.name,
      version: this.version,
      supportedMethods: this.supportedMethods,
      endpoints: this.config.endpoints,
      webhookEndpoint: '/webhook',
      supportedEvents: Object.values(WebhookEventType)
    };
  }

  /**
   * 清理资源
   */
  async destroy(): Promise<void> {
    if (this.batchTimer) {
      clearInterval(this.batchTimer);
    }
    
    // 发送剩余事件
    await this.flushEventQueue();
  }
}
