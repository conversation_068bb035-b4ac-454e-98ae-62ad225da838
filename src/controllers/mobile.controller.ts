/**
 * 移动端控制器
 * 提供移动设备管理、认证、<PERSON><PERSON>生成等API接口
 */

import { Request, Response } from 'express';
import { logger } from '@/config/logger';
import { mobileAuthService, MobileDeviceType, BiometricType, MobileAuthMethod } from '@/services/mobile-auth.service';
import { mobileSDKService, SDKPlatform } from '@/services/mobile-sdk.service';
import { securityAuditService, AuditEventType, AuditSeverity } from '@/services/security-audit.service';

/**
 * 移动端控制器
 */
export class MobileController {

  /**
   * 注册移动设备
   * POST /api/v1/mobile/devices/register
   */
  registerDevice = async (req: Request, res: Response): Promise<void> => {
    try {
      const {
        deviceType,
        deviceId,
        deviceName,
        osVersion,
        appVersion,
        manufacturer,
        model,
        screenResolution,
        isJailbroken,
        isRooted,
        hasSecureHardware,
        supportedBiometrics,
        pushToken,
        publicKey,
        attestationData,
        metadata
      } = req.body;

      const userId = req.user?.id;
      if (!userId) {
        return res.status(401).json({
          success: false,
          error: 'UNAUTHORIZED',
          message: '需要用户认证'
        });
      }

      const device = await mobileAuthService.registerDevice({
        userId,
        deviceType,
        deviceId,
        deviceName,
        osVersion,
        appVersion,
        manufacturer,
        model,
        screenResolution,
        isJailbroken,
        isRooted,
        hasSecureHardware,
        supportedBiometrics,
        pushToken,
        publicKey,
        attestationData,
        metadata
      });

      res.status(201).json({
        success: true,
        message: '设备注册成功',
        data: {
          deviceId: device.deviceId,
          trustScore: device.trustScore,
          supportedBiometrics: device.supportedBiometrics,
          registeredAt: device.registeredAt
        }
      });

    } catch (error) {
      logger.error('设备注册失败', {
        error: error instanceof Error ? error.message : String(error),
        userId: req.user?.id
      });

      res.status(500).json({
        success: false,
        error: 'INTERNAL_ERROR',
        message: '设备注册失败'
      });
    }
  };

  /**
   * 注册生物识别
   * POST /api/v1/mobile/biometric/register
   */
  registerBiometric = async (req: Request, res: Response): Promise<void> => {
    try {
      const { deviceId, biometricType, templateData, quality } = req.body;
      const userId = req.user?.id;

      if (!userId) {
        return res.status(401).json({
          success: false,
          error: 'UNAUTHORIZED',
          message: '需要用户认证'
        });
      }

      if (!deviceId || !biometricType || !templateData) {
        return res.status(400).json({
          success: false,
          error: 'MISSING_PARAMETERS',
          message: '缺少必需参数'
        });
      }

      const template = await mobileAuthService.registerBiometric(
        userId,
        deviceId,
        biometricType as BiometricType,
        templateData,
        quality
      );

      res.status(201).json({
        success: true,
        message: '生物识别注册成功',
        data: {
          templateId: template.id,
          biometricType: template.biometricType,
          quality: template.quality,
          createdAt: template.createdAt
        }
      });

    } catch (error) {
      logger.error('生物识别注册失败', {
        error: error instanceof Error ? error.message : String(error),
        userId: req.user?.id
      });

      res.status(500).json({
        success: false,
        error: 'INTERNAL_ERROR',
        message: '生物识别注册失败'
      });
    }
  };

  /**
   * 创建移动认证会话
   * POST /api/v1/mobile/auth/session
   */
  createAuthSession = async (req: Request, res: Response): Promise<void> => {
    try {
      const { deviceId, authMethod, ttl, maxAttempts, metadata } = req.body;
      const userId = req.user?.id;

      if (!userId) {
        return res.status(401).json({
          success: false,
          error: 'UNAUTHORIZED',
          message: '需要用户认证'
        });
      }

      if (!deviceId || !authMethod) {
        return res.status(400).json({
          success: false,
          error: 'MISSING_PARAMETERS',
          message: '缺少必需参数'
        });
      }

      const session = await mobileAuthService.createAuthSession(
        userId,
        deviceId,
        authMethod as MobileAuthMethod,
        { ttl, maxAttempts, metadata }
      );

      res.status(201).json({
        success: true,
        message: '认证会话创建成功',
        data: {
          sessionId: session.id,
          challenge: session.challenge,
          authMethod: session.authMethod,
          expiresAt: session.challengeExpiry,
          maxAttempts: session.maxAttempts
        }
      });

    } catch (error) {
      logger.error('认证会话创建失败', {
        error: error instanceof Error ? error.message : String(error),
        userId: req.user?.id
      });

      res.status(500).json({
        success: false,
        error: 'INTERNAL_ERROR',
        message: '认证会话创建失败'
      });
    }
  };

  /**
   * 验证移动认证
   * POST /api/v1/mobile/auth/verify
   */
  verifyMobileAuth = async (req: Request, res: Response): Promise<void> => {
    try {
      const { sessionId, response, additionalData } = req.body;

      if (!sessionId || !response) {
        return res.status(400).json({
          success: false,
          error: 'MISSING_PARAMETERS',
          message: '缺少必需参数'
        });
      }

      const result = await mobileAuthService.verifyMobileAuth(sessionId, response, additionalData);

      if (result.success) {
        res.json({
          success: true,
          message: '认证验证成功',
          data: {
            sessionId: result.session.id,
            isSuccessful: result.session.isSuccessful,
            completedAt: result.session.completedAt,
            attempts: result.session.attempts
          }
        });
      } else {
        res.status(401).json({
          success: false,
          error: 'AUTH_VERIFICATION_FAILED',
          message: '认证验证失败',
          data: {
            sessionId: result.session.id,
            attempts: result.session.attempts,
            maxAttempts: result.session.maxAttempts
          }
        });
      }

    } catch (error) {
      logger.error('认证验证失败', {
        error: error instanceof Error ? error.message : String(error),
        sessionId: req.body.sessionId
      });

      res.status(500).json({
        success: false,
        error: 'INTERNAL_ERROR',
        message: '认证验证失败'
      });
    }
  };

  /**
   * 发送推送通知
   * POST /api/v1/mobile/push/send
   */
  sendPushNotification = async (req: Request, res: Response): Promise<void> => {
    try {
      const { deviceId, title, body, data, priority, ttl } = req.body;
      const userId = req.user?.id;

      if (!userId) {
        return res.status(401).json({
          success: false,
          error: 'UNAUTHORIZED',
          message: '需要用户认证'
        });
      }

      if (!deviceId) {
        return res.status(400).json({
          success: false,
          error: 'MISSING_PARAMETERS',
          message: '缺少设备ID'
        });
      }

      const notification = await mobileAuthService.sendPushNotification(userId, deviceId, {
        title,
        body,
        data,
        priority,
        ttl
      });

      res.json({
        success: true,
        message: '推送通知发送成功',
        data: {
          notificationId: notification.id,
          status: notification.status,
          sentAt: notification.sentAt
        }
      });

    } catch (error) {
      logger.error('推送通知发送失败', {
        error: error instanceof Error ? error.message : String(error),
        userId: req.user?.id
      });

      res.status(500).json({
        success: false,
        error: 'INTERNAL_ERROR',
        message: '推送通知发送失败'
      });
    }
  };

  /**
   * 获取用户设备列表
   * GET /api/v1/mobile/devices
   */
  getUserDevices = async (req: Request, res: Response): Promise<void> => {
    try {
      const userId = req.user?.id;

      if (!userId) {
        return res.status(401).json({
          success: false,
          error: 'UNAUTHORIZED',
          message: '需要用户认证'
        });
      }

      const devices = await mobileAuthService.getUserDevices(userId);

      res.json({
        success: true,
        data: {
          devices: devices.map(device => ({
            deviceId: device.deviceId,
            deviceName: device.deviceName,
            deviceType: device.deviceType,
            manufacturer: device.manufacturer,
            model: device.model,
            osVersion: device.osVersion,
            appVersion: device.appVersion,
            trustScore: device.trustScore,
            supportedBiometrics: device.supportedBiometrics,
            isActive: device.isActive,
            lastSeen: device.lastSeen,
            registeredAt: device.registeredAt
          })),
          totalDevices: devices.length,
          activeDevices: devices.filter(d => d.isActive).length
        }
      });

    } catch (error) {
      logger.error('获取用户设备列表失败', {
        error: error instanceof Error ? error.message : String(error),
        userId: req.user?.id
      });

      res.status(500).json({
        success: false,
        error: 'INTERNAL_ERROR',
        message: '获取设备列表失败'
      });
    }
  };

  /**
   * 停用设备
   * POST /api/v1/mobile/devices/:deviceId/deactivate
   */
  deactivateDevice = async (req: Request, res: Response): Promise<void> => {
    try {
      const { deviceId } = req.params;
      const userId = req.user?.id;

      if (!userId) {
        return res.status(401).json({
          success: false,
          error: 'UNAUTHORIZED',
          message: '需要用户认证'
        });
      }

      await mobileAuthService.deactivateDevice(userId, deviceId);

      res.json({
        success: true,
        message: '设备已停用'
      });

    } catch (error) {
      logger.error('停用设备失败', {
        error: error instanceof Error ? error.message : String(error),
        userId: req.user?.id,
        deviceId: req.params.deviceId
      });

      res.status(500).json({
        success: false,
        error: 'INTERNAL_ERROR',
        message: '停用设备失败'
      });
    }
  };

  /**
   * 生成SDK
   * POST /api/v1/mobile/sdk/generate
   */
  generateSDK = async (req: Request, res: Response): Promise<void> => {
    try {
      const {
        platform,
        applicationId,
        clientId,
        clientSecret,
        serverUrl,
        redirectUri,
        scopes,
        features,
        customization,
        security
      } = req.body;

      const userId = req.user?.id;

      if (!userId) {
        return res.status(401).json({
          success: false,
          error: 'UNAUTHORIZED',
          message: '需要用户认证'
        });
      }

      if (!platform || !applicationId || !clientId) {
        return res.status(400).json({
          success: false,
          error: 'MISSING_PARAMETERS',
          message: '缺少必需参数'
        });
      }

      const sdkConfig = {
        platform: platform as SDKPlatform,
        version: '1.0.0',
        applicationId,
        clientId,
        clientSecret,
        serverUrl: serverUrl || process.env.SERVER_URL || 'https://auth.example.com',
        redirectUri,
        scopes: scopes || ['openid', 'profile', 'email'],
        features: features || [],
        customization: customization || {},
        security: security || {},
        metadata: {
          userId,
          ipAddress: req.ip,
          userAgent: req.get('User-Agent')
        }
      };

      const sdkPackage = await mobileSDKService.generateSDK(sdkConfig);

      res.status(201).json({
        success: true,
        message: 'SDK生成成功',
        data: {
          packageId: sdkPackage.id,
          platform: sdkPackage.platform,
          version: sdkPackage.version,
          fileName: sdkPackage.fileName,
          fileSize: sdkPackage.fileSize,
          downloadUrl: sdkPackage.downloadUrl,
          checksum: sdkPackage.checksum,
          expiresAt: sdkPackage.expiresAt
        }
      });

    } catch (error) {
      logger.error('SDK生成失败', {
        error: error instanceof Error ? error.message : String(error),
        userId: req.user?.id
      });

      res.status(500).json({
        success: false,
        error: 'INTERNAL_ERROR',
        message: 'SDK生成失败'
      });
    }
  };

  /**
   * 获取SDK配置模板
   * GET /api/v1/mobile/sdk/template/:platform
   */
  getSDKTemplate = async (req: Request, res: Response): Promise<void> => {
    try {
      const { platform } = req.params;

      if (!Object.values(SDKPlatform).includes(platform as SDKPlatform)) {
        return res.status(400).json({
          success: false,
          error: 'INVALID_PLATFORM',
          message: '不支持的平台'
        });
      }

      const template = mobileSDKService.getSDKConfigTemplate(platform as SDKPlatform);

      res.json({
        success: true,
        data: template
      });

    } catch (error) {
      logger.error('获取SDK模板失败', {
        error: error instanceof Error ? error.message : String(error),
        platform: req.params.platform
      });

      res.status(500).json({
        success: false,
        error: 'INTERNAL_ERROR',
        message: '获取SDK模板失败'
      });
    }
  };

  /**
   * 获取SDK包列表
   * GET /api/v1/mobile/sdk/packages
   */
  getSDKPackages = async (req: Request, res: Response): Promise<void> => {
    try {
      const { applicationId } = req.query;
      const userId = req.user?.id;

      if (!userId) {
        return res.status(401).json({
          success: false,
          error: 'UNAUTHORIZED',
          message: '需要用户认证'
        });
      }

      if (!applicationId) {
        return res.status(400).json({
          success: false,
          error: 'MISSING_PARAMETERS',
          message: '缺少应用ID'
        });
      }

      const packages = await mobileSDKService.getSDKPackages(applicationId as string);

      res.json({
        success: true,
        data: {
          packages: packages.map(pkg => ({
            id: pkg.id,
            platform: pkg.platform,
            version: pkg.version,
            fileName: pkg.fileName,
            fileSize: pkg.fileSize,
            downloadUrl: pkg.downloadUrl,
            checksum: pkg.checksum,
            createdAt: pkg.createdAt,
            expiresAt: pkg.expiresAt,
            downloadCount: pkg.downloadCount,
            isActive: pkg.isActive
          })),
          totalPackages: packages.length
        }
      });

    } catch (error) {
      logger.error('获取SDK包列表失败', {
        error: error instanceof Error ? error.message : String(error),
        userId: req.user?.id
      });

      res.status(500).json({
        success: false,
        error: 'INTERNAL_ERROR',
        message: '获取SDK包列表失败'
      });
    }
  };

  /**
   * 下载SDK包
   * GET /api/v1/mobile/sdk/download/:packageId
   */
  downloadSDKPackage = async (req: Request, res: Response): Promise<void> => {
    try {
      const { packageId } = req.params;
      const userId = req.user?.id;

      if (!userId) {
        return res.status(401).json({
          success: false,
          error: 'UNAUTHORIZED',
          message: '需要用户认证'
        });
      }

      const result = await mobileSDKService.downloadSDKPackage(packageId, userId);

      // 这里应该返回实际的文件流
      // 简化实现：返回下载信息
      res.json({
        success: true,
        message: 'SDK包下载准备就绪',
        data: {
          downloadUrl: result.url,
          fileName: result.package.fileName,
          fileSize: result.package.fileSize,
          checksum: result.package.checksum
        }
      });

    } catch (error) {
      logger.error('SDK包下载失败', {
        error: error instanceof Error ? error.message : String(error),
        packageId: req.params.packageId,
        userId: req.user?.id
      });

      res.status(500).json({
        success: false,
        error: 'INTERNAL_ERROR',
        message: 'SDK包下载失败'
      });
    }
  };

  /**
   * 获取集成指南
   * GET /api/v1/mobile/sdk/guide/:platform
   */
  getIntegrationGuide = async (req: Request, res: Response): Promise<void> => {
    try {
      const { platform } = req.params;
      const { language = 'en' } = req.query;

      if (!Object.values(SDKPlatform).includes(platform as SDKPlatform)) {
        return res.status(400).json({
          success: false,
          error: 'INVALID_PLATFORM',
          message: '不支持的平台'
        });
      }

      const guide = mobileSDKService.getIntegrationGuide(
        platform as SDKPlatform,
        language as string
      );

      res.json({
        success: true,
        data: {
          platform,
          language,
          guide
        }
      });

    } catch (error) {
      logger.error('获取集成指南失败', {
        error: error instanceof Error ? error.message : String(error),
        platform: req.params.platform
      });

      res.status(500).json({
        success: false,
        error: 'INTERNAL_ERROR',
        message: '获取集成指南失败'
      });
    }
  };
}

// 创建控制器实例
export const mobileController = new MobileController();
