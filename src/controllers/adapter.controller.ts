/**
 * 协议适配器管理控制器
 * 提供适配器的管理和配置API
 */

import { Request, Response } from 'express';
import { adapterRegistry } from '@/services/adapter-registry.service';
import { logger } from '@/config/logger';
import { ProtocolConfig } from '@/types/protocol-adapter';
import { validateRequest } from '@/utils/validation';
import { z } from 'zod';

/**
 * 创建适配器实例的请求模式
 */
const createInstanceSchema = z.object({
  adapterName: z.string().min(1, '适配器名称不能为空'),
  config: z.object({}).passthrough(),
  instanceId: z.string().optional()
});

/**
 * 更新适配器配置的请求模式
 */
const updateConfigSchema = z.object({
  config: z.object({}).passthrough()
});

/**
 * 协议适配器管理控制器
 */
export class AdapterController {
  /**
   * 获取所有已注册的适配器
   * GET /api/v1/adapters
   */
  getRegisteredAdapters = async (req: Request, res: Response): Promise<void> => {
    try {
      const adapters = adapterRegistry.getRegisteredAdapters();
      
      res.json({
        success: true,
        data: {
          adapters,
          count: adapters.length
        }
      });

    } catch (error) {
      logger.error('获取已注册适配器失败', { error: error.message });
      res.status(500).json({
        success: false,
        error: 'INTERNAL_ERROR',
        message: '获取适配器列表失败'
      });
    }
  };

  /**
   * 获取适配器元数据
   * GET /api/v1/adapters/:name
   */
  getAdapterMetadata = async (req: Request, res: Response): Promise<void> => {
    try {
      const { name } = req.params;
      const metadata = adapterRegistry.getAdapterMetadata(name);

      if (!metadata) {
        res.status(404).json({
          success: false,
          error: 'ADAPTER_NOT_FOUND',
          message: '适配器未找到'
        });
        return;
      }

      res.json({
        success: true,
        data: metadata
      });

    } catch (error) {
      logger.error('获取适配器元数据失败', { error: error.message });
      res.status(500).json({
        success: false,
        error: 'INTERNAL_ERROR',
        message: '获取适配器元数据失败'
      });
    }
  };

  /**
   * 获取所有适配器实例
   * GET /api/v1/adapters/instances
   */
  getAdapterInstances = async (req: Request, res: Response): Promise<void> => {
    try {
      const { adapterName, status } = req.query;
      let instances = adapterRegistry.getAllAdapterInstances();

      // 按适配器名称筛选
      if (adapterName) {
        instances = instances.filter(instance => instance.name === adapterName);
      }

      // 按状态筛选
      if (status) {
        const isActive = status === 'active';
        instances = instances.filter(instance => instance.isActive === isActive);
      }

      // 转换为安全的响应格式（移除敏感信息）
      const safeInstances = instances.map(instance => ({
        id: instance.id,
        name: instance.name,
        version: instance.metadata.version,
        isActive: instance.isActive,
        createdAt: instance.createdAt,
        lastUsed: instance.lastUsed,
        usageCount: instance.usageCount,
        supportedMethods: instance.metadata.supportedMethods,
        category: instance.metadata.category,
        status: instance.metadata.status
      }));

      res.json({
        success: true,
        data: {
          instances: safeInstances,
          count: safeInstances.length
        }
      });

    } catch (error) {
      logger.error('获取适配器实例失败', { error: error.message });
      res.status(500).json({
        success: false,
        error: 'INTERNAL_ERROR',
        message: '获取适配器实例失败'
      });
    }
  };

  /**
   * 创建适配器实例
   * POST /api/v1/adapters/instances
   */
  createAdapterInstance = async (req: Request, res: Response): Promise<void> => {
    try {
      const validation = validateRequest(createInstanceSchema, req.body);
      if (!validation.success) {
        res.status(400).json({
          success: false,
          error: 'VALIDATION_ERROR',
          message: '请求参数验证失败',
          details: validation.errors
        });
        return;
      }

      const { adapterName, config, instanceId } = validation.data;

      // 检查适配器是否已注册
      if (!adapterRegistry.isAdapterRegistered(adapterName)) {
        res.status(404).json({
          success: false,
          error: 'ADAPTER_NOT_FOUND',
          message: '适配器未找到'
        });
        return;
      }

      // 创建适配器实例
      const createdInstanceId = await adapterRegistry.createAdapterInstance(
        adapterName,
        config as ProtocolConfig,
        instanceId
      );

      res.status(201).json({
        success: true,
        data: {
          instanceId: createdInstanceId,
          message: '适配器实例创建成功'
        }
      });

    } catch (error) {
      logger.error('创建适配器实例失败', { error: error.message });
      
      if (error.message.includes('ADAPTER_NOT_FOUND')) {
        res.status(404).json({
          success: false,
          error: 'ADAPTER_NOT_FOUND',
          message: error.message
        });
      } else if (error.message.includes('INSTANCE_CREATION_FAILED')) {
        res.status(400).json({
          success: false,
          error: 'INSTANCE_CREATION_FAILED',
          message: error.message
        });
      } else {
        res.status(500).json({
          success: false,
          error: 'INTERNAL_ERROR',
          message: '创建适配器实例失败'
        });
      }
    }
  };

  /**
   * 获取特定适配器实例
   * GET /api/v1/adapters/instances/:instanceId
   */
  getAdapterInstance = async (req: Request, res: Response): Promise<void> => {
    try {
      const { instanceId } = req.params;
      const instance = adapterRegistry.getAdapterInstance(instanceId);

      if (!instance) {
        res.status(404).json({
          success: false,
          error: 'INSTANCE_NOT_FOUND',
          message: '适配器实例未找到'
        });
        return;
      }

      // 返回安全的实例信息
      const safeInstance = {
        id: instance.id,
        name: instance.name,
        version: instance.metadata.version,
        description: instance.metadata.description,
        isActive: instance.isActive,
        createdAt: instance.createdAt,
        lastUsed: instance.lastUsed,
        usageCount: instance.usageCount,
        supportedMethods: instance.metadata.supportedMethods,
        category: instance.metadata.category,
        status: instance.metadata.status,
        config: instance.config // 注意：可能包含敏感信息，生产环境需要过滤
      };

      res.json({
        success: true,
        data: safeInstance
      });

    } catch (error) {
      logger.error('获取适配器实例失败', { error: error.message });
      res.status(500).json({
        success: false,
        error: 'INTERNAL_ERROR',
        message: '获取适配器实例失败'
      });
    }
  };

  /**
   * 更新适配器实例配置
   * PUT /api/v1/adapters/instances/:instanceId/config
   */
  updateAdapterInstanceConfig = async (req: Request, res: Response): Promise<void> => {
    try {
      const { instanceId } = req.params;
      const validation = validateRequest(updateConfigSchema, req.body);
      
      if (!validation.success) {
        res.status(400).json({
          success: false,
          error: 'VALIDATION_ERROR',
          message: '请求参数验证失败',
          details: validation.errors
        });
        return;
      }

      const { config } = validation.data;

      const success = await adapterRegistry.updateAdapterInstanceConfig(
        instanceId,
        config as ProtocolConfig
      );

      if (!success) {
        res.status(404).json({
          success: false,
          error: 'INSTANCE_NOT_FOUND',
          message: '适配器实例未找到'
        });
        return;
      }

      res.json({
        success: true,
        data: {
          message: '适配器实例配置更新成功'
        }
      });

    } catch (error) {
      logger.error('更新适配器实例配置失败', { error: error.message });
      res.status(500).json({
        success: false,
        error: 'INTERNAL_ERROR',
        message: '更新适配器实例配置失败'
      });
    }
  };

  /**
   * 停止适配器实例
   * POST /api/v1/adapters/instances/:instanceId/stop
   */
  stopAdapterInstance = async (req: Request, res: Response): Promise<void> => {
    try {
      const { instanceId } = req.params;
      const success = await adapterRegistry.stopAdapterInstance(instanceId);

      if (!success) {
        res.status(404).json({
          success: false,
          error: 'INSTANCE_NOT_FOUND',
          message: '适配器实例未找到'
        });
        return;
      }

      res.json({
        success: true,
        data: {
          message: '适配器实例停止成功'
        }
      });

    } catch (error) {
      logger.error('停止适配器实例失败', { error: error.message });
      res.status(500).json({
        success: false,
        error: 'INTERNAL_ERROR',
        message: '停止适配器实例失败'
      });
    }
  };

  /**
   * 重启适配器实例
   * POST /api/v1/adapters/instances/:instanceId/restart
   */
  restartAdapterInstance = async (req: Request, res: Response): Promise<void> => {
    try {
      const { instanceId } = req.params;
      const success = await adapterRegistry.restartAdapterInstance(instanceId);

      if (!success) {
        res.status(404).json({
          success: false,
          error: 'INSTANCE_NOT_FOUND',
          message: '适配器实例未找到'
        });
        return;
      }

      res.json({
        success: true,
        data: {
          message: '适配器实例重启成功'
        }
      });

    } catch (error) {
      logger.error('重启适配器实例失败', { error: error.message });
      res.status(500).json({
        success: false,
        error: 'INTERNAL_ERROR',
        message: '重启适配器实例失败'
      });
    }
  };

  /**
   * 获取适配器使用统计
   * GET /api/v1/adapters/stats
   */
  getAdapterStats = async (req: Request, res: Response): Promise<void> => {
    try {
      const stats = adapterRegistry.getAdapterUsageStats();

      res.json({
        success: true,
        data: {
          stats,
          timestamp: new Date().toISOString()
        }
      });

    } catch (error) {
      logger.error('获取适配器统计失败', { error: error.message });
      res.status(500).json({
        success: false,
        error: 'INTERNAL_ERROR',
        message: '获取适配器统计失败'
      });
    }
  };

  /**
   * 加载外部适配器
   * POST /api/v1/adapters/load
   */
  loadExternalAdapter = async (req: Request, res: Response): Promise<void> => {
    try {
      const { adapterPath } = req.body;

      if (!adapterPath) {
        res.status(400).json({
          success: false,
          error: 'MISSING_PARAMETER',
          message: '缺少适配器路径'
        });
        return;
      }

      const adapterName = await adapterRegistry.loadExternalAdapter(adapterPath);

      res.json({
        success: true,
        data: {
          adapterName,
          message: '外部适配器加载成功'
        }
      });

    } catch (error) {
      logger.error('加载外部适配器失败', { error: error.message });
      res.status(400).json({
        success: false,
        error: 'EXTERNAL_ADAPTER_LOAD_FAILED',
        message: error.message
      });
    }
  };

  /**
   * 清理非活跃实例
   * POST /api/v1/adapters/cleanup
   */
  cleanupInactiveInstances = async (req: Request, res: Response): Promise<void> => {
    try {
      await adapterRegistry.cleanupInactiveInstances();

      res.json({
        success: true,
        data: {
          message: '非活跃实例清理完成'
        }
      });

    } catch (error) {
      logger.error('清理非活跃实例失败', { error: error.message });
      res.status(500).json({
        success: false,
        error: 'INTERNAL_ERROR',
        message: '清理非活跃实例失败'
      });
    }
  };
}

// 创建控制器实例
export const adapterController = new AdapterController();
