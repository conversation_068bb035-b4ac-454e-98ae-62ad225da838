/**
 * 管理员控制器
 * 提供系统管理、用户管理、OAuth客户端管理等管理员功能
 */

import { Request, Response } from 'express';
import { prisma } from '@/config/database';
import { logger, logAuditEvent } from '@/config/logger';
import { getPerformanceReport } from '@/middleware/performance.middleware';
import { oidcClientService } from '@/services/oidc-client.service';
import { hashPassword } from '@/utils/password';
import { redisService } from '@/services/redis.service';

export class AdminController {

  /**
   * 获取系统统计信息
   * GET /api/v1/admin/stats
   */
  getSystemStats = async (req: Request, res: Response): Promise<void> => {
    try {
      const [
        totalUsers,
        activeUsers,
        totalSessions,
        oauthClients,
        recentAuditLogs
      ] = await Promise.all([
        prisma.user.count(),
        prisma.user.count({ where: { isActive: true } }),
        prisma.session.count({ where: { expiresAt: { gt: new Date() } } }),
        prisma.oAuthClient.count(),
        prisma.auditLog.count({
          where: {
            createdAt: {
              gte: new Date(Date.now() - 24 * 60 * 60 * 1000) // 24小时内
            }
          }
        })
      ]);

      // 获取系统健康状态
      const isRedisHealthy = redisService.isReady();
      const memoryUsage = process.memoryUsage();

      const stats = {
        totalUsers,
        activeUsers,
        totalSessions,
        oauthClients,
        apiRequests24h: recentAuditLogs,
        errorRate: 0, // TODO: 计算错误率
        systemHealth: {
          database: true, // TODO: 实际检查数据库健康状态
          redis: isRedisHealthy,
          memory: Math.round((memoryUsage.heapUsed / memoryUsage.heapTotal) * 100),
          cpu: 0 // TODO: 获取CPU使用率
        }
      };

      res.status(200).json(stats);

    } catch (error) {
      logger.error('获取系统统计失败', {
        error: error instanceof Error ? error.message : String(error)
      });

      res.status(500).json({
        error: 'stats_fetch_failed',
        message: '获取系统统计失败'
      });
    }
  };

  /**
   * 获取系统健康状态
   * GET /api/v1/admin/health
   */
  getSystemHealth = async (req: Request, res: Response): Promise<void> => {
    try {
      const performanceReport = getPerformanceReport();
      
      res.status(200).json({
        timestamp: new Date().toISOString(),
        status: 'healthy',
        performance: performanceReport
      });

    } catch (error) {
      logger.error('获取系统健康状态失败', {
        error: error instanceof Error ? error.message : String(error)
      });

      res.status(500).json({
        error: 'health_check_failed',
        message: '获取系统健康状态失败'
      });
    }
  };

  /**
   * 获取用户列表
   * GET /api/v1/admin/users
   */
  getUsers = async (req: Request, res: Response): Promise<void> => {
    try {
      const {
        page = '1',
        limit = '20',
        search,
        isActive,
        role
      } = req.query as Record<string, string>;

      const pageNum = parseInt(page);
      const limitNum = parseInt(limit);
      const offset = (pageNum - 1) * limitNum;

      const where: any = {};

      if (search) {
        where.OR = [
          { email: { contains: search, mode: 'insensitive' } },
          { nickname: { contains: search, mode: 'insensitive' } },
          { firstName: { contains: search, mode: 'insensitive' } },
          { lastName: { contains: search, mode: 'insensitive' } }
        ];
      }

      if (isActive !== undefined) {
        where.isActive = isActive === 'true';
      }

      if (role) {
        where.userRoles = {
          some: {
            role: {
              name: role
            }
          }
        };
      }

      const [users, total] = await Promise.all([
        prisma.user.findMany({
          where,
          skip: offset,
          take: limitNum,
          include: {
            userRoles: {
              include: {
                role: true
              }
            },
            sessions: {
              where: {
                expiresAt: { gt: new Date() }
              }
            }
          },
          orderBy: { createdAt: 'desc' }
        }),
        prisma.user.count({ where })
      ]);

      const formattedUsers = users.map(user => ({
        id: user.id,
        email: user.email,
        nickname: user.nickname,
        firstName: user.firstName,
        lastName: user.lastName,
        isActive: user.isActive,
        isEmailVerified: user.emailVerified,
        mfaEnabled: user.mfaEnabled,
        lastLoginAt: user.lastLoginAt,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
        roles: user.userRoles.map(ur => ur.role.name),
        sessions: user.sessions.length,
        loginCount: 0 // TODO: 实现登录次数统计
      }));

      res.status(200).json({
        users: formattedUsers,
        total,
        page: pageNum,
        limit: limitNum,
        totalPages: Math.ceil(total / limitNum)
      });

    } catch (error) {
      logger.error('获取用户列表失败', {
        error: error instanceof Error ? error.message : String(error)
      });

      res.status(500).json({
        error: 'users_fetch_failed',
        message: '获取用户列表失败'
      });
    }
  };

  /**
   * 获取单个用户详情
   * GET /api/v1/admin/users/:id
   */
  getUser = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;

      const user = await prisma.user.findUnique({
        where: { id },
        include: {
          userRoles: {
            include: {
              role: true
            }
          },
          sessions: {
            where: {
              expiresAt: { gt: new Date() }
            }
          },
          mfaDevices: true,
          auditLogs: {
            take: 10,
            orderBy: { createdAt: 'desc' }
          }
        }
      });

      if (!user) {
        res.status(404).json({
          error: 'user_not_found',
          message: '用户不存在'
        });
        return;
      }

      const formattedUser = {
        id: user.id,
        email: user.email,
        nickname: user.nickname,
        firstName: user.firstName,
        lastName: user.lastName,
        isActive: user.isActive,
        isEmailVerified: user.emailVerified,
        mfaEnabled: user.mfaEnabled,
        lastLoginAt: user.lastLoginAt,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
        roles: user.userRoles.map(ur => ur.role.name),
        sessions: user.sessions.length,
        mfaDevices: user.mfaDevices.length,
        recentActivities: user.auditLogs
      };

      res.status(200).json(formattedUser);

    } catch (error) {
      logger.error('获取用户详情失败', {
        error: error instanceof Error ? error.message : String(error),
        userId: req.params.id
      });

      res.status(500).json({
        error: 'user_fetch_failed',
        message: '获取用户详情失败'
      });
    }
  };

  /**
   * 创建用户
   * POST /api/v1/admin/users
   */
  createUser = async (req: Request, res: Response): Promise<void> => {
    try {
      const {
        email,
        password = 'TempPassword123!',
        nickname,
        firstName,
        lastName,
        roles = ['user'],
        isActive = true
      } = req.body;

      // 检查邮箱是否已存在
      const existingUser = await prisma.user.findUnique({
        where: { email }
      });

      if (existingUser) {
        res.status(400).json({
          error: 'email_exists',
          message: '邮箱已存在'
        });
        return;
      }

      // 哈希密码
      const passwordHash = await hashPassword(password);

      // 创建用户
      const user = await prisma.user.create({
        data: {
          email,
          passwordHash,
          nickname,
          firstName,
          lastName,
          isActive,
          emailVerified: false
        }
      });

      // 分配角色
      for (const roleName of roles) {
        const role = await prisma.role.findUnique({
          where: { name: roleName }
        });

        if (role) {
          await prisma.userRole.create({
            data: {
              userId: user.id,
              roleId: role.id
            }
          });
        }
      }

      // 记录审计日志
      logAuditEvent(
        'user_created',
        'admin',
        (req as any).user?.userId,
        {
          targetUserId: user.id,
          email: user.email,
          roles
        }
      );

      res.status(201).json({
        id: user.id,
        email: user.email,
        nickname: user.nickname,
        firstName: user.firstName,
        lastName: user.lastName,
        isActive: user.isActive,
        roles,
        temporaryPassword: password
      });

    } catch (error) {
      logger.error('创建用户失败', {
        error: error instanceof Error ? error.message : String(error),
        email: req.body.email
      });

      res.status(500).json({
        error: 'user_creation_failed',
        message: '创建用户失败'
      });
    }
  };

  /**
   * 更新用户
   * PUT /api/v1/admin/users/:id
   */
  updateUser = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      const {
        nickname,
        firstName,
        lastName,
        isActive,
        roles
      } = req.body;

      // 检查用户是否存在
      const existingUser = await prisma.user.findUnique({
        where: { id }
      });

      if (!existingUser) {
        res.status(404).json({
          error: 'user_not_found',
          message: '用户不存在'
        });
        return;
      }

      // 更新用户基本信息
      const updatedUser = await prisma.user.update({
        where: { id },
        data: {
          nickname,
          firstName,
          lastName,
          isActive
        }
      });

      // 更新角色
      if (roles && Array.isArray(roles)) {
        // 删除现有角色
        await prisma.userRole.deleteMany({
          where: { userId: id }
        });

        // 添加新角色
        for (const roleName of roles) {
          const role = await prisma.role.findUnique({
            where: { name: roleName }
          });

          if (role) {
            await prisma.userRole.create({
              data: {
                userId: id,
                roleId: role.id
              }
            });
          }
        }
      }

      // 记录审计日志
      logAuditEvent(
        'user_updated',
        'admin',
        (req as any).user?.userId,
        {
          targetUserId: id,
          changes: req.body
        }
      );

      res.status(200).json({
        id: updatedUser.id,
        email: updatedUser.email,
        nickname: updatedUser.nickname,
        firstName: updatedUser.firstName,
        lastName: updatedUser.lastName,
        isActive: updatedUser.isActive,
        roles: roles || []
      });

    } catch (error) {
      logger.error('更新用户失败', {
        error: error instanceof Error ? error.message : String(error),
        userId: req.params.id
      });

      res.status(500).json({
        error: 'user_update_failed',
        message: '更新用户失败'
      });
    }
  };

  /**
   * 删除用户
   * DELETE /api/v1/admin/users/:id
   */
  deleteUser = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;

      // 检查用户是否存在
      const existingUser = await prisma.user.findUnique({
        where: { id }
      });

      if (!existingUser) {
        res.status(404).json({
          error: 'user_not_found',
          message: '用户不存在'
        });
        return;
      }

      // 删除用户（级联删除相关数据）
      await prisma.user.delete({
        where: { id }
      });

      // 记录审计日志
      logAuditEvent(
        'user_deleted',
        'admin',
        (req as any).user?.userId,
        {
          targetUserId: id,
          email: existingUser.email
        }
      );

      res.status(204).send();

    } catch (error) {
      logger.error('删除用户失败', {
        error: error instanceof Error ? error.message : String(error),
        userId: req.params.id
      });

      res.status(500).json({
        error: 'user_deletion_failed',
        message: '删除用户失败'
      });
    }
  };

  /**
   * 获取OAuth客户端列表
   * GET /api/v1/admin/oauth-clients
   */
  getOAuthClients = async (req: Request, res: Response): Promise<void> => {
    try {
      const {
        page = '1',
        limit = '20',
        search,
        isActive
      } = req.query as Record<string, string>;

      const pageNum = parseInt(page);
      const limitNum = parseInt(limit);
      const offset = (pageNum - 1) * limitNum;

      const where: any = {};

      if (search) {
        where.OR = [
          { name: { contains: search, mode: 'insensitive' } },
          { clientId: { contains: search, mode: 'insensitive' } },
          { description: { contains: search, mode: 'insensitive' } }
        ];
      }

      if (isActive !== undefined) {
        where.isActive = isActive === 'true';
      }

      const [clients, total] = await Promise.all([
        prisma.oAuthClient.findMany({
          where,
          skip: offset,
          take: limitNum,
          orderBy: { createdAt: 'desc' }
        }),
        prisma.oAuthClient.count({ where })
      ]);

      const formattedClients = clients.map(client => ({
        id: client.id,
        clientId: client.clientId,
        name: client.name,
        description: client.description,
        redirectUris: client.redirectUris as string[],
        grantTypes: client.grantTypes as string[],
        responseTypes: client.responseTypes as string[],
        scopes: client.scopes as string[],
        isActive: client.isActive,
        requirePkce: client.requirePkce,
        requireConsent: client.requireConsent,
        accessTokenLifetime: client.accessTokenLifetime,
        refreshTokenLifetime: client.refreshTokenLifetime,
        createdAt: client.createdAt,
        updatedAt: client.updatedAt,
        usageStats: {
          totalAuthorizations: 0, // TODO: 实现使用统计
          activeTokens: 0,
          lastUsed: null
        }
      }));

      res.status(200).json({
        clients: formattedClients,
        total,
        page: pageNum,
        limit: limitNum,
        totalPages: Math.ceil(total / limitNum)
      });

    } catch (error) {
      logger.error('获取OAuth客户端列表失败', {
        error: error instanceof Error ? error.message : String(error)
      });

      res.status(500).json({
        error: 'oauth_clients_fetch_failed',
        message: '获取OAuth客户端列表失败'
      });
    }
  };

  /**
   * 创建OAuth客户端
   * POST /api/v1/admin/oauth-clients
   */
  createOAuthClient = async (req: Request, res: Response): Promise<void> => {
    try {
      const client = await oidcClientService.createClient(req.body);

      // 记录审计日志
      logAuditEvent(
        'oauth_client_created',
        'admin',
        (req as any).user?.userId,
        {
          clientId: client.clientId,
          name: client.name
        }
      );

      res.status(201).json(client);

    } catch (error) {
      logger.error('创建OAuth客户端失败', {
        error: error instanceof Error ? error.message : String(error)
      });

      res.status(500).json({
        error: 'oauth_client_creation_failed',
        message: '创建OAuth客户端失败'
      });
    }
  };

  /**
   * 更新OAuth客户端
   * PUT /api/v1/admin/oauth-clients/:id
   */
  updateOAuthClient = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      
      // 通过clientId查找客户端
      const existingClient = await prisma.oAuthClient.findFirst({
        where: { clientId: id }
      });

      if (!existingClient) {
        res.status(404).json({
          error: 'oauth_client_not_found',
          message: 'OAuth客户端不存在'
        });
        return;
      }

      const updatedClient = await oidcClientService.updateClient(id, req.body);

      // 记录审计日志
      logAuditEvent(
        'oauth_client_updated',
        'admin',
        (req as any).user?.userId,
        {
          clientId: id,
          changes: req.body
        }
      );

      res.status(200).json(updatedClient);

    } catch (error) {
      logger.error('更新OAuth客户端失败', {
        error: error instanceof Error ? error.message : String(error),
        clientId: req.params.id
      });

      res.status(500).json({
        error: 'oauth_client_update_failed',
        message: '更新OAuth客户端失败'
      });
    }
  };

  /**
   * 删除OAuth客户端
   * DELETE /api/v1/admin/oauth-clients/:id
   */
  deleteOAuthClient = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;

      await oidcClientService.deleteClient(id);

      // 记录审计日志
      logAuditEvent(
        'oauth_client_deleted',
        'admin',
        (req as any).user?.userId,
        {
          clientId: id
        }
      );

      res.status(204).send();

    } catch (error) {
      logger.error('删除OAuth客户端失败', {
        error: error instanceof Error ? error.message : String(error),
        clientId: req.params.id
      });

      res.status(500).json({
        error: 'oauth_client_deletion_failed',
        message: '删除OAuth客户端失败'
      });
    }
  };

  /**
   * 重新生成客户端密钥
   * POST /api/v1/admin/oauth-clients/:id/regenerate-secret
   */
  regenerateClientSecret = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;

      const newSecret = await oidcClientService.regenerateClientSecret(id);

      // 记录审计日志
      logAuditEvent(
        'oauth_client_secret_regenerated',
        'admin',
        (req as any).user?.userId,
        {
          clientId: id
        }
      );

      res.status(200).json({
        clientSecret: newSecret
      });

    } catch (error) {
      logger.error('重新生成客户端密钥失败', {
        error: error instanceof Error ? error.message : String(error),
        clientId: req.params.id
      });

      res.status(500).json({
        error: 'client_secret_regeneration_failed',
        message: '重新生成客户端密钥失败'
      });
    }
  };

  /**
   * 获取审计日志
   * GET /api/v1/admin/audit-logs
   */
  getAuditLogs = async (req: Request, res: Response): Promise<void> => {
    try {
      const {
        page = '1',
        limit = '20',
        action,
        userId,
        startDate,
        endDate
      } = req.query as Record<string, string>;

      const pageNum = parseInt(page);
      const limitNum = parseInt(limit);
      const offset = (pageNum - 1) * limitNum;

      const where: any = {};

      if (action) {
        where.action = action;
      }

      if (userId) {
        where.userId = userId;
      }

      if (startDate || endDate) {
        where.createdAt = {};
        if (startDate) {
          where.createdAt.gte = new Date(startDate);
        }
        if (endDate) {
          where.createdAt.lte = new Date(endDate);
        }
      }

      const [logs, total] = await Promise.all([
        prisma.auditLog.findMany({
          where,
          skip: offset,
          take: limitNum,
          include: {
            user: {
              select: {
                email: true,
                nickname: true
              }
            }
          },
          orderBy: { createdAt: 'desc' }
        }),
        prisma.auditLog.count({ where })
      ]);

      const formattedLogs = logs.map(log => ({
        id: log.id,
        action: log.action,
        resource: log.resource,
        resourceId: log.resourceId,
        userId: log.userId,
        userEmail: log.user?.email,
        ipAddress: log.ipAddress,
        userAgent: log.userAgent,
        details: log.details,
        createdAt: log.createdAt
      }));

      res.status(200).json({
        logs: formattedLogs,
        total,
        page: pageNum,
        limit: limitNum,
        totalPages: Math.ceil(total / limitNum)
      });

    } catch (error) {
      logger.error('获取审计日志失败', {
        error: error instanceof Error ? error.message : String(error)
      });

      res.status(500).json({
        error: 'audit_logs_fetch_failed',
        message: '获取审计日志失败'
      });
    }
  };

  /**
   * 清理缓存
   * POST /api/v1/admin/maintenance/clear-cache
   */
  clearCache = async (req: Request, res: Response): Promise<void> => {
    try {
      const { type } = req.body;

      if (type === 'redis' || !type) {
        // 清理Redis缓存
        const client = redisService.getClient();
        if (client) {
          await client.flushdb();
        }
      }

      // 记录审计日志
      logAuditEvent(
        'cache_cleared',
        'admin',
        (req as any).user?.userId,
        { type: type || 'all' }
      );

      res.status(200).json({
        message: '缓存清理成功'
      });

    } catch (error) {
      logger.error('清理缓存失败', {
        error: error instanceof Error ? error.message : String(error)
      });

      res.status(500).json({
        error: 'cache_clear_failed',
        message: '清理缓存失败'
      });
    }
  };

  /**
   * 清理过期令牌
   * POST /api/v1/admin/maintenance/cleanup-tokens
   */
  cleanupExpiredTokens = async (req: Request, res: Response): Promise<void> => {
    try {
      const deletedCount = await prisma.authorizationCode.deleteMany({
        where: {
          OR: [
            { expiresAt: { lt: new Date() } },
            { used: true }
          ]
        }
      });

      // 记录审计日志
      logAuditEvent(
        'expired_tokens_cleaned',
        'admin',
        (req as any).user?.userId,
        { deletedCount: deletedCount.count }
      );

      res.status(200).json({
        deletedCount: deletedCount.count
      });

    } catch (error) {
      logger.error('清理过期令牌失败', {
        error: error instanceof Error ? error.message : String(error)
      });

      res.status(500).json({
        error: 'token_cleanup_failed',
        message: '清理过期令牌失败'
      });
    }
  };

  /**
   * 清理过期会话
   * POST /api/v1/admin/maintenance/cleanup-sessions
   */
  cleanupExpiredSessions = async (req: Request, res: Response): Promise<void> => {
    try {
      const deletedCount = await prisma.session.deleteMany({
        where: {
          expiresAt: { lt: new Date() }
        }
      });

      // 记录审计日志
      logAuditEvent(
        'expired_sessions_cleaned',
        'admin',
        (req as any).user?.userId,
        { deletedCount: deletedCount.count }
      );

      res.status(200).json({
        deletedCount: deletedCount.count
      });

    } catch (error) {
      logger.error('清理过期会话失败', {
        error: error instanceof Error ? error.message : String(error)
      });

      res.status(500).json({
        error: 'session_cleanup_failed',
        message: '清理过期会话失败'
      });
    }
  };
}
