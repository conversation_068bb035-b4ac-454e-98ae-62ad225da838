/**
 * 性能测试控制器
 * 提供性能测试、基准测试和负载测试的API接口
 */

import { Request, Response } from 'express';
import { performanceTestService, TestType, TestConfig } from '@/services/performance-test.service';
import { benchmarkService } from '@/services/benchmark.service';
import { loadTestScenariosService } from '@/services/load-test-scenarios.service';
import { automatedPerformanceTestingService } from '@/services/automated-performance-testing.service';
import { logger } from '@/config/logger';
import { validateRequest } from '@/utils/validation';
import { z } from 'zod';

/**
 * 性能测试配置验证模式
 */
const performanceTestConfigSchema = z.object({
  type: z.enum(['load', 'stress', 'spike', 'volume', 'endurance', 'baseline']),
  name: z.string().min(1).max(100),
  description: z.string().max(500).optional(),
  duration: z.number().positive().max(3600000), // 最大1小时
  concurrency: z.number().positive().max(1000),
  rampUpTime: z.number().positive().optional(),
  rampDownTime: z.number().positive().optional(),
  targetRPS: z.number().positive().optional(),
  maxErrors: z.number().positive().optional(),
  scenarioType: z.string().optional(),
  thresholds: z.object({
    responseTime: z.object({
      avg: z.number().positive().optional(),
      p95: z.number().positive().optional(),
      p99: z.number().positive().optional()
    }).optional(),
    errorRate: z.number().min(0).max(100).optional(),
    throughput: z.number().positive().optional()
  }).optional()
});

/**
 * 基准测试配置验证模式
 */
const benchmarkConfigSchema = z.object({
  categories: z.array(z.enum(['database', 'cache', 'api', 'auth'])).optional(),
  saveAsBaseline: z.boolean().optional(),
  compareWithBaseline: z.string().optional()
});

/**
 * 性能测试控制器
 */
export class PerformanceTestController {

  /**
   * 运行性能测试
   * POST /api/v1/performance/test
   */
  runPerformanceTest = async (req: Request, res: Response): Promise<void> => {
    try {
      const validation = validateRequest(performanceTestConfigSchema, req.body);
      if (!validation.success) {
        res.status(400).json({
          success: false,
          error: 'VALIDATION_ERROR',
          message: '请求参数验证失败',
          details: validation.errors
        });
        return;
      }

      const { scenarioType = 'mixed', ...configData } = validation.data;

      // 创建测试配置
      const config: TestConfig = {
        type: configData.type as TestType,
        name: configData.name,
        description: configData.description || '',
        duration: configData.duration,
        concurrency: configData.concurrency,
        rampUpTime: configData.rampUpTime,
        rampDownTime: configData.rampDownTime,
        targetRPS: configData.targetRPS,
        maxErrors: configData.maxErrors,
        thresholds: configData.thresholds
      };

      // 获取测试场景
      const scenarios = loadTestScenariosService.getScenariosByType(scenarioType);

      logger.info('开始性能测试', {
        testName: config.name,
        type: config.type,
        scenarioType,
        concurrency: config.concurrency,
        duration: config.duration
      });

      // 运行测试
      const result = await performanceTestService.runTest(config, scenarios);

      res.json({
        success: true,
        data: {
          testId: result.testId,
          config: result.config,
          summary: {
            duration: result.duration,
            totalRequests: result.totalRequests,
            successfulRequests: result.successfulRequests,
            failedRequests: result.failedRequests,
            errorRate: result.errorRate,
            avgResponseTime: result.responseTime.avg,
            throughput: result.throughput.rps
          },
          thresholds: result.thresholdResults,
          detailedResults: result
        }
      });

    } catch (error) {
      logger.error('性能测试失败', { error: error.message });
      res.status(500).json({
        success: false,
        error: 'INTERNAL_ERROR',
        message: '性能测试执行失败',
        details: error.message
      });
    }
  };

  /**
   * 获取性能测试结果
   * GET /api/v1/performance/test/:testId
   */
  getTestResult = async (req: Request, res: Response): Promise<void> => {
    try {
      const { testId } = req.params;
      const result = performanceTestService.getTestResult(testId);

      if (!result) {
        res.status(404).json({
          success: false,
          error: 'NOT_FOUND',
          message: '测试结果未找到'
        });
        return;
      }

      res.json({
        success: true,
        data: result
      });

    } catch (error) {
      logger.error('获取测试结果失败', { error: error.message });
      res.status(500).json({
        success: false,
        error: 'INTERNAL_ERROR',
        message: '获取测试结果失败'
      });
    }
  };

  /**
   * 获取所有测试结果
   * GET /api/v1/performance/tests
   */
  getAllTestResults = async (req: Request, res: Response): Promise<void> => {
    try {
      const results = performanceTestService.getAllTestResults();
      const limit = parseInt(req.query.limit as string) || 50;
      const offset = parseInt(req.query.offset as string) || 0;

      const paginatedResults = results
        .sort((a, b) => b.startTime.getTime() - a.startTime.getTime())
        .slice(offset, offset + limit);

      res.json({
        success: true,
        data: {
          tests: paginatedResults.map(result => ({
            testId: result.testId,
            name: result.config.name,
            type: result.config.type,
            startTime: result.startTime,
            duration: result.duration,
            totalRequests: result.totalRequests,
            errorRate: result.errorRate,
            avgResponseTime: result.responseTime.avg,
            throughput: result.throughput.rps,
            thresholdsPassed: result.thresholdResults.passed
          })),
          total: results.length,
          limit,
          offset
        }
      });

    } catch (error) {
      logger.error('获取测试结果列表失败', { error: error.message });
      res.status(500).json({
        success: false,
        error: 'INTERNAL_ERROR',
        message: '获取测试结果列表失败'
      });
    }
  };

  /**
   * 获取活跃测试
   * GET /api/v1/performance/tests/active
   */
  getActiveTests = async (req: Request, res: Response): Promise<void> => {
    try {
      const activeTests = performanceTestService.getActiveTests();

      res.json({
        success: true,
        data: {
          activeTests: activeTests.map(test => ({
            testId: test.testId,
            name: test.config.name,
            type: test.config.type,
            startTime: test.startTime,
            duration: test.config.duration,
            concurrency: test.config.concurrency,
            elapsedTime: Date.now() - test.startTime.getTime()
          })),
          count: activeTests.length
        }
      });

    } catch (error) {
      logger.error('获取活跃测试失败', { error: error.message });
      res.status(500).json({
        success: false,
        error: 'INTERNAL_ERROR',
        message: '获取活跃测试失败'
      });
    }
  };

  /**
   * 中止测试
   * POST /api/v1/performance/test/:testId/abort
   */
  abortTest = async (req: Request, res: Response): Promise<void> => {
    try {
      const { testId } = req.params;
      performanceTestService.abortTest(testId);

      res.json({
        success: true,
        data: {
          message: '测试已中止',
          testId
        }
      });

    } catch (error) {
      logger.error('中止测试失败', { error: error.message });
      res.status(500).json({
        success: false,
        error: 'INTERNAL_ERROR',
        message: '中止测试失败'
      });
    }
  };

  /**
   * 运行基准测试
   * POST /api/v1/performance/benchmark
   */
  runBenchmark = async (req: Request, res: Response): Promise<void> => {
    try {
      const validation = validateRequest(benchmarkConfigSchema, req.body);
      if (!validation.success) {
        res.status(400).json({
          success: false,
          error: 'VALIDATION_ERROR',
          message: '请求参数验证失败',
          details: validation.errors
        });
        return;
      }

      const { categories, saveAsBaseline, compareWithBaseline } = validation.data;

      logger.info('开始基准测试', {
        categories: categories || 'all',
        saveAsBaseline,
        compareWithBaseline
      });

      let results;

      if (categories && categories.length > 0) {
        // 运行指定类别的基准测试
        results = [];
        for (const category of categories) {
          let result;
          switch (category) {
            case 'database':
              result = await benchmarkService.runDatabaseBenchmark();
              break;
            case 'cache':
              result = await benchmarkService.runRedisBenchmark();
              break;
            case 'api':
              result = await benchmarkService.runAPIBenchmark();
              break;
            case 'auth':
              result = await benchmarkService.runAuthBenchmark();
              break;
          }
          if (result) {
            results.push(result);
          }
        }
      } else {
        // 运行完整基准测试套件
        results = await benchmarkService.runFullBenchmarkSuite();
      }

      // 处理基准保存和比较
      if (saveAsBaseline) {
        results.forEach(result => {
          benchmarkService.saveBaseline(result.name, result);
        });
      }

      if (compareWithBaseline) {
        results = results.map(result => 
          benchmarkService.compareWithBaseline(compareWithBaseline, result)
        );
      }

      res.json({
        success: true,
        data: {
          results,
          summary: {
            totalTests: results.length,
            categories: results.map(r => r.category),
            timestamp: new Date().toISOString(),
            environment: results[0]?.environment
          }
        }
      });

    } catch (error) {
      logger.error('基准测试失败', { error: error.message });
      res.status(500).json({
        success: false,
        error: 'INTERNAL_ERROR',
        message: '基准测试执行失败',
        details: error.message
      });
    }
  };

  /**
   * 获取基准测试历史
   * GET /api/v1/performance/benchmarks
   */
  getBenchmarkHistory = async (req: Request, res: Response): Promise<void> => {
    try {
      const baselines = benchmarkService.getAllBaselines();
      const results = benchmarkService.getAllResults();

      res.json({
        success: true,
        data: {
          baselines: baselines.map(baseline => ({
            name: baseline.name,
            category: baseline.category,
            timestamp: baseline.timestamp,
            environment: baseline.environment,
            summary: this.summarizeBenchmarkResult(baseline)
          })),
          results: results.map(result => ({
            name: result.name,
            category: result.category,
            timestamp: result.timestamp,
            environment: result.environment,
            summary: this.summarizeBenchmarkResult(result),
            comparison: result.comparison
          }))
        }
      });

    } catch (error) {
      logger.error('获取基准测试历史失败', { error: error.message });
      res.status(500).json({
        success: false,
        error: 'INTERNAL_ERROR',
        message: '获取基准测试历史失败'
      });
    }
  };

  /**
   * 获取可用测试场景
   * GET /api/v1/performance/scenarios
   */
  getAvailableScenarios = async (req: Request, res: Response): Promise<void> => {
    try {
      const scenarioTypes = loadTestScenariosService.getAllScenarioTypes();
      const scenarios: any = {};

      for (const [type, getScenarios] of Object.entries(scenarioTypes)) {
        const typeScenarios = getScenarios();
        scenarios[type] = typeScenarios.map(scenario => ({
          name: scenario.name,
          weight: scenario.weight,
          description: `${scenario.name} scenario with weight ${scenario.weight}`
        }));
      }

      res.json({
        success: true,
        data: {
          scenarioTypes: Object.keys(scenarioTypes),
          scenarios,
          totalScenarios: Object.values(scenarios).reduce((sum, arr) => sum + arr.length, 0)
        }
      });

    } catch (error) {
      logger.error('获取可用场景失败', { error: error.message });
      res.status(500).json({
        success: false,
        error: 'INTERNAL_ERROR',
        message: '获取可用场景失败'
      });
    }
  };

  /**
   * 获取性能测试报告
   * GET /api/v1/performance/report
   */
  getPerformanceReport = async (req: Request, res: Response): Promise<void> => {
    try {
      const testResults = performanceTestService.getAllTestResults();
      const benchmarkResults = benchmarkService.getAllResults();

      // 生成综合报告
      const report = {
        summary: {
          totalTests: testResults.length,
          totalBenchmarks: benchmarkResults.length,
          lastTestDate: testResults.length > 0 
            ? Math.max(...testResults.map(r => r.startTime.getTime()))
            : null,
          lastBenchmarkDate: benchmarkResults.length > 0
            ? Math.max(...benchmarkResults.map(r => r.timestamp.getTime()))
            : null
        },
        performanceTests: {
          recent: testResults
            .sort((a, b) => b.startTime.getTime() - a.startTime.getTime())
            .slice(0, 10)
            .map(result => ({
              testId: result.testId,
              name: result.config.name,
              type: result.config.type,
              startTime: result.startTime,
              duration: result.duration,
              totalRequests: result.totalRequests,
              errorRate: result.errorRate,
              avgResponseTime: result.responseTime.avg,
              throughput: result.throughput.rps,
              thresholdsPassed: result.thresholdResults.passed
            })),
          statistics: this.calculateTestStatistics(testResults)
        },
        benchmarks: {
          recent: benchmarkResults
            .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
            .slice(0, 5)
            .map(result => ({
              name: result.name,
              category: result.category,
              timestamp: result.timestamp,
              summary: this.summarizeBenchmarkResult(result)
            })),
          trends: this.calculateBenchmarkTrends(benchmarkResults)
        }
      };

      res.json({
        success: true,
        data: report
      });

    } catch (error) {
      logger.error('获取性能报告失败', { error: error.message });
      res.status(500).json({
        success: false,
        error: 'INTERNAL_ERROR',
        message: '获取性能报告失败'
      });
    }
  };

  /**
   * 总结基准测试结果
   */
  private summarizeBenchmarkResult(result: any): any {
    const testNames = Object.keys(result.results);
    const avgOpsPerSecond = testNames.reduce((sum, name) => 
      sum + result.results[name].opsPerSecond, 0) / testNames.length;
    const avgLatency = testNames.reduce((sum, name) => 
      sum + result.results[name].avgLatency, 0) / testNames.length;

    return {
      testCount: testNames.length,
      avgOpsPerSecond: Math.round(avgOpsPerSecond),
      avgLatency: Math.round(avgLatency * 100) / 100,
      totalThroughput: testNames.reduce((sum, name) => 
        sum + result.results[name].throughput, 0)
    };
  }

  /**
   * 计算测试统计
   */
  private calculateTestStatistics(testResults: any[]): any {
    if (testResults.length === 0) {
      return {
        avgResponseTime: 0,
        avgThroughput: 0,
        avgErrorRate: 0,
        successRate: 0
      };
    }

    const avgResponseTime = testResults.reduce((sum, result) => 
      sum + result.responseTime.avg, 0) / testResults.length;
    const avgThroughput = testResults.reduce((sum, result) => 
      sum + result.throughput.rps, 0) / testResults.length;
    const avgErrorRate = testResults.reduce((sum, result) => 
      sum + result.errorRate, 0) / testResults.length;
    const successRate = testResults.filter(result => 
      result.thresholdResults.passed).length / testResults.length * 100;

    return {
      avgResponseTime: Math.round(avgResponseTime * 100) / 100,
      avgThroughput: Math.round(avgThroughput * 100) / 100,
      avgErrorRate: Math.round(avgErrorRate * 100) / 100,
      successRate: Math.round(successRate * 100) / 100
    };
  }

  /**
   * 计算基准测试趋势
   */
  private calculateBenchmarkTrends(benchmarkResults: any[]): any {
    const trends: any = {};
    
    benchmarkResults.forEach(result => {
      if (!trends[result.category]) {
        trends[result.category] = [];
      }
      trends[result.category].push({
        timestamp: result.timestamp,
        summary: this.summarizeBenchmarkResult(result)
      });
    });

    // 按时间排序
    Object.keys(trends).forEach(category => {
      trends[category].sort((a: any, b: any) => 
        new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
      );
    });

    return trends;
  }

  /**
   * 获取自动化性能测试计划列表
   * GET /api/v1/performance/automated/schedules
   */
  getAutomatedTestSchedules = async (req: Request, res: Response): Promise<void> => {
    try {
      const schedules = automatedPerformanceTestingService.getSchedules();

      res.json({
        success: true,
        data: {
          schedules: schedules.map(schedule => ({
            id: schedule.id,
            name: schedule.name,
            cronExpression: schedule.cronExpression,
            enabled: schedule.enabled,
            testType: schedule.testType,
            lastRun: schedule.lastRun,
            nextRun: schedule.nextRun,
            thresholds: schedule.thresholds
          })),
          totalCount: schedules.length,
          activeCount: schedules.filter(s => s.enabled).length
        }
      });

    } catch (error) {
      logger.error('获取自动化测试计划失败', {
        error: error instanceof Error ? error.message : String(error)
      });

      res.status(500).json({
        success: false,
        error: 'AUTOMATED_SCHEDULES_ERROR',
        message: '获取自动化测试计划失败'
      });
    }
  };

  /**
   * 获取测试历史和回归分析
   * GET /api/v1/performance/automated/history/:scheduleId
   */
  getAutomatedTestHistory = async (req: Request, res: Response): Promise<void> => {
    try {
      const { scheduleId } = req.params;
      const { limit = 50 } = req.query;

      const history = automatedPerformanceTestingService.getTestHistory(scheduleId);
      const baseline = automatedPerformanceTestingService.getBaseline(scheduleId);

      // 限制返回的历史记录数量
      const limitedHistory = history.slice(-parseInt(limit as string));

      // 计算趋势分析
      const trends = this.calculatePerformanceTrends(limitedHistory);

      res.json({
        success: true,
        data: {
          scheduleId,
          baseline,
          history: limitedHistory,
          trends,
          summary: {
            totalTests: history.length,
            recentTests: limitedHistory.length,
            hasBaseline: !!baseline
          }
        }
      });

    } catch (error) {
      logger.error('获取自动化测试历史失败', {
        scheduleId: req.params.scheduleId,
        error: error instanceof Error ? error.message : String(error)
      });

      res.status(500).json({
        success: false,
        error: 'AUTOMATED_HISTORY_ERROR',
        message: '获取自动化测试历史失败'
      });
    }
  };

  /**
   * 手动触发自动化性能测试
   * POST /api/v1/performance/automated/trigger/:scheduleId
   */
  triggerAutomatedTest = async (req: Request, res: Response): Promise<void> => {
    try {
      const { scheduleId } = req.params;

      // 异步执行测试
      automatedPerformanceTestingService.executeTest(scheduleId)
        .then(result => {
          logger.info('手动触发的自动化测试完成', {
            scheduleId,
            testId: result.testId
          });
        })
        .catch(error => {
          logger.error('手动触发的自动化测试失败', {
            scheduleId,
            error: error instanceof Error ? error.message : String(error)
          });
        });

      res.json({
        success: true,
        message: '自动化性能测试已触发',
        data: {
          scheduleId,
          status: 'triggered',
          timestamp: new Date()
        }
      });

    } catch (error) {
      logger.error('触发自动化测试失败', {
        scheduleId: req.params.scheduleId,
        error: error instanceof Error ? error.message : String(error)
      });

      res.status(500).json({
        success: false,
        error: 'TRIGGER_TEST_ERROR',
        message: '触发自动化测试失败'
      });
    }
  };

  /**
   * 获取性能回归报告
   * GET /api/v1/performance/automated/regression/:scheduleId
   */
  getRegressionReport = async (req: Request, res: Response): Promise<void> => {
    try {
      const { scheduleId } = req.params;
      const { days = 7 } = req.query;

      const history = automatedPerformanceTestingService.getTestHistory(scheduleId);
      const baseline = automatedPerformanceTestingService.getBaseline(scheduleId);

      if (!baseline) {
        return res.status(404).json({
          success: false,
          error: 'NO_BASELINE',
          message: '没有找到基准数据'
        });
      }

      // 过滤指定天数内的测试结果
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - parseInt(days as string));

      const recentHistory = history.filter(h =>
        new Date(h.timestamp) >= cutoffDate
      );

      // 生成回归报告
      const regressionReport = this.generateRegressionReport(recentHistory, baseline);

      res.json({
        success: true,
        data: {
          scheduleId,
          reportPeriod: `${days} days`,
          baseline: {
            timestamp: baseline.timestamp,
            summary: baseline.summary
          },
          regression: regressionReport,
          summary: {
            totalTests: recentHistory.length,
            regressionsDetected: regressionReport.totalRegressions,
            improvementsDetected: regressionReport.totalImprovements
          }
        }
      });

    } catch (error) {
      logger.error('获取回归报告失败', {
        scheduleId: req.params.scheduleId,
        error: error instanceof Error ? error.message : String(error)
      });

      res.status(500).json({
        success: false,
        error: 'REGRESSION_REPORT_ERROR',
        message: '获取回归报告失败'
      });
    }
  };

  /**
   * 计算性能趋势
   */
  private calculatePerformanceTrends(history: any[]): any {
    if (history.length < 2) {
      return { message: '数据不足，无法计算趋势' };
    }

    const trends = {
      responseTime: [],
      throughput: [],
      errorRate: []
    };

    history.forEach(h => {
      const timestamp = h.timestamp;
      const result = h.result;

      if (result.responseTime) {
        trends.responseTime.push({
          timestamp,
          avg: result.responseTime.avg,
          p95: result.responseTime.p95,
          p99: result.responseTime.p99
        });
      }

      if (result.throughput) {
        trends.throughput.push({
          timestamp,
          rps: result.throughput.rps
        });
      }

      if (result.errorRate !== undefined) {
        trends.errorRate.push({
          timestamp,
          rate: result.errorRate
        });
      }
    });

    return trends;
  }

  /**
   * 生成回归报告
   */
  private generateRegressionReport(history: any[], baseline: any): any {
    const regressions = [];
    const improvements = [];

    history.forEach(h => {
      const result = h.result;

      // 检查响应时间回归
      if (result.responseTime && baseline.responseTime) {
        const avgDegradation = this.calculateDegradation(
          result.responseTime.avg,
          baseline.responseTime.avg
        );

        if (avgDegradation > 20) {
          regressions.push({
            timestamp: h.timestamp,
            metric: 'avg_response_time',
            degradationPercent: avgDegradation,
            currentValue: result.responseTime.avg,
            baselineValue: baseline.responseTime.avg
          });
        } else if (avgDegradation < -10) {
          improvements.push({
            timestamp: h.timestamp,
            metric: 'avg_response_time',
            improvementPercent: Math.abs(avgDegradation),
            currentValue: result.responseTime.avg,
            baselineValue: baseline.responseTime.avg
          });
        }
      }

      // 检查吞吐量回归
      if (result.throughput && baseline.throughput) {
        const throughputDegradation = this.calculateDegradation(
          result.throughput.rps,
          baseline.throughput.rps
        );

        if (throughputDegradation > 15) {
          regressions.push({
            timestamp: h.timestamp,
            metric: 'throughput',
            degradationPercent: throughputDegradation,
            currentValue: result.throughput.rps,
            baselineValue: baseline.throughput.rps
          });
        }
      }
    });

    return {
      totalRegressions: regressions.length,
      totalImprovements: improvements.length,
      regressions,
      improvements
    };
  }

  /**
   * 计算性能退化百分比
   */
  private calculateDegradation(current: number, baseline: number): number {
    if (baseline === 0) return 0;
    return ((current - baseline) / baseline) * 100;
  }
}

// 创建控制器实例
export const performanceTestController = new PerformanceTestController();
