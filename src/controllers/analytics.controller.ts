/**
 * 分析控制器
 * 提供用户行为分析、安全事件分析、报告生成等API接口
 */

import { Request, Response } from 'express';
import { logger } from '@/config/logger';
import { userBehaviorAnalyticsService, UserBehaviorEventType } from '@/services/user-behavior-analytics.service';
import { securityEventAnalyticsService } from '@/services/security-event-analytics.service';
import { automatedReportingService, ReportType, ReportFrequency, ReportFormat } from '@/services/automated-reporting.service';
import { securityAuditService, AuditEventType, AuditSeverity } from '@/services/security-audit.service';

/**
 * 分析控制器
 */
export class AnalyticsController {

  /**
   * 记录用户行为事件
   * POST /api/v1/analytics/behavior/events
   */
  recordBehaviorEvent = async (req: Request, res: Response): Promise<void> => {
    try {
      const {
        eventType,
        ipAddress,
        userAgent,
        location,
        device,
        context
      } = req.body;

      const userId = req.user?.id;
      if (!userId) {
        return res.status(401).json({
          success: false,
          error: 'UNAUTHORIZED',
          message: '需要用户认证'
        });
      }

      if (!eventType) {
        return res.status(400).json({
          success: false,
          error: 'MISSING_PARAMETERS',
          message: '缺少事件类型'
        });
      }

      await userBehaviorAnalyticsService.recordBehaviorEvent({
        userId,
        sessionId: req.sessionID,
        eventType: eventType as UserBehaviorEventType,
        timestamp: new Date(),
        ipAddress: ipAddress || req.ip || 'unknown',
        userAgent: userAgent || req.get('User-Agent') || 'unknown',
        location,
        device,
        context: context || {}
      });

      res.json({
        success: true,
        message: '用户行为事件已记录'
      });

    } catch (error) {
      logger.error('记录用户行为事件失败', {
        error: error instanceof Error ? error.message : String(error),
        userId: req.user?.id
      });

      res.status(500).json({
        success: false,
        error: 'INTERNAL_ERROR',
        message: '记录用户行为事件失败'
      });
    }
  };

  /**
   * 获取用户行为分析
   * GET /api/v1/analytics/behavior/analysis/:userId
   */
  getUserBehaviorAnalysis = async (req: Request, res: Response): Promise<void> => {
    try {
      const { userId } = req.params;
      const { startDate, endDate, period = '30d' } = req.query;

      // 权限检查：只能查看自己的分析或管理员权限
      if (userId !== req.user?.id && !req.user?.roles?.includes('admin')) {
        return res.status(403).json({
          success: false,
          error: 'FORBIDDEN',
          message: '权限不足'
        });
      }

      // 计算时间范围
      let timeRange: { start: Date; end: Date };
      if (startDate && endDate) {
        timeRange = {
          start: new Date(startDate as string),
          end: new Date(endDate as string)
        };
      } else {
        const end = new Date();
        const start = new Date();
        switch (period) {
          case '7d':
            start.setDate(end.getDate() - 7);
            break;
          case '30d':
            start.setDate(end.getDate() - 30);
            break;
          case '90d':
            start.setDate(end.getDate() - 90);
            break;
          default:
            start.setDate(end.getDate() - 30);
        }
        timeRange = { start, end };
      }

      const analysis = await userBehaviorAnalyticsService.analyzeUserBehavior(userId, timeRange);

      res.json({
        success: true,
        data: analysis
      });

    } catch (error) {
      logger.error('获取用户行为分析失败', {
        error: error instanceof Error ? error.message : String(error),
        userId: req.params.userId
      });

      res.status(500).json({
        success: false,
        error: 'INTERNAL_ERROR',
        message: '获取用户行为分析失败'
      });
    }
  };

  /**
   * 获取用户行为趋势
   * GET /api/v1/analytics/behavior/trends/:userId
   */
  getUserBehaviorTrends = async (req: Request, res: Response): Promise<void> => {
    try {
      const { userId } = req.params;
      const { period = 'week' } = req.query;

      // 权限检查
      if (userId !== req.user?.id && !req.user?.roles?.includes('admin')) {
        return res.status(403).json({
          success: false,
          error: 'FORBIDDEN',
          message: '权限不足'
        });
      }

      const trends = await userBehaviorAnalyticsService.getUserBehaviorTrends(
        userId,
        period as 'day' | 'week' | 'month'
      );

      res.json({
        success: true,
        data: trends
      });

    } catch (error) {
      logger.error('获取用户行为趋势失败', {
        error: error instanceof Error ? error.message : String(error),
        userId: req.params.userId
      });

      res.status(500).json({
        success: false,
        error: 'INTERNAL_ERROR',
        message: '获取用户行为趋势失败'
      });
    }
  };

  /**
   * 获取用户行为洞察
   * GET /api/v1/analytics/behavior/insights/:userId
   */
  getUserBehaviorInsights = async (req: Request, res: Response): Promise<void> => {
    try {
      const { userId } = req.params;

      // 权限检查
      if (userId !== req.user?.id && !req.user?.roles?.includes('admin')) {
        return res.status(403).json({
          success: false,
          error: 'FORBIDDEN',
          message: '权限不足'
        });
      }

      const insights = await userBehaviorAnalyticsService.getUserBehaviorInsights(userId);

      res.json({
        success: true,
        data: insights
      });

    } catch (error) {
      logger.error('获取用户行为洞察失败', {
        error: error instanceof Error ? error.message : String(error),
        userId: req.params.userId
      });

      res.status(500).json({
        success: false,
        error: 'INTERNAL_ERROR',
        message: '获取用户行为洞察失败'
      });
    }
  };

  /**
   * 获取安全态势
   * GET /api/v1/analytics/security/posture
   */
  getSecurityPosture = async (req: Request, res: Response): Promise<void> => {
    try {
      const posture = await securityEventAnalyticsService.getSecurityPosture();

      res.json({
        success: true,
        data: posture
      });

    } catch (error) {
      logger.error('获取安全态势失败', {
        error: error instanceof Error ? error.message : String(error)
      });

      res.status(500).json({
        success: false,
        error: 'INTERNAL_ERROR',
        message: '获取安全态势失败'
      });
    }
  };

  /**
   * 获取威胁分析报告
   * GET /api/v1/analytics/security/threats
   */
  getThreatAnalysis = async (req: Request, res: Response): Promise<void> => {
    try {
      const { startDate, endDate, period = '7d' } = req.query;

      // 计算时间范围
      let timeRange: { start: Date; end: Date };
      if (startDate && endDate) {
        timeRange = {
          start: new Date(startDate as string),
          end: new Date(endDate as string)
        };
      } else {
        const end = new Date();
        const start = new Date();
        switch (period) {
          case '1d':
            start.setDate(end.getDate() - 1);
            break;
          case '7d':
            start.setDate(end.getDate() - 7);
            break;
          case '30d':
            start.setDate(end.getDate() - 30);
            break;
          default:
            start.setDate(end.getDate() - 7);
        }
        timeRange = { start, end };
      }

      const threatReport = await securityEventAnalyticsService.getThreatAnalysisReport(timeRange);

      res.json({
        success: true,
        data: threatReport
      });

    } catch (error) {
      logger.error('获取威胁分析报告失败', {
        error: error instanceof Error ? error.message : String(error)
      });

      res.status(500).json({
        success: false,
        error: 'INTERNAL_ERROR',
        message: '获取威胁分析报告失败'
      });
    }
  };

  /**
   * 创建报告配置
   * POST /api/v1/analytics/reports/configs
   */
  createReportConfig = async (req: Request, res: Response): Promise<void> => {
    try {
      const {
        name,
        type,
        frequency,
        format,
        recipients,
        parameters,
        template
      } = req.body;

      if (!name || !type) {
        return res.status(400).json({
          success: false,
          error: 'MISSING_PARAMETERS',
          message: '缺少必需参数'
        });
      }

      const config = await automatedReportingService.createReportConfig({
        name,
        type: type as ReportType,
        frequency: frequency as ReportFrequency || ReportFrequency.WEEKLY,
        format: format || [ReportFormat.PDF],
        recipients: recipients || [],
        parameters: parameters || {},
        template
      });

      res.status(201).json({
        success: true,
        message: '报告配置已创建',
        data: config
      });

    } catch (error) {
      logger.error('创建报告配置失败', {
        error: error instanceof Error ? error.message : String(error)
      });

      res.status(500).json({
        success: false,
        error: 'INTERNAL_ERROR',
        message: '创建报告配置失败'
      });
    }
  };

  /**
   * 获取报告配置列表
   * GET /api/v1/analytics/reports/configs
   */
  getReportConfigs = async (req: Request, res: Response): Promise<void> => {
    try {
      const configs = automatedReportingService.getReportConfigs();

      res.json({
        success: true,
        data: {
          configs,
          totalConfigs: configs.length
        }
      });

    } catch (error) {
      logger.error('获取报告配置列表失败', {
        error: error instanceof Error ? error.message : String(error)
      });

      res.status(500).json({
        success: false,
        error: 'INTERNAL_ERROR',
        message: '获取报告配置列表失败'
      });
    }
  };

  /**
   * 生成报告
   * POST /api/v1/analytics/reports/generate
   */
  generateReport = async (req: Request, res: Response): Promise<void> => {
    try {
      const { configId, parameters } = req.body;

      if (!configId) {
        return res.status(400).json({
          success: false,
          error: 'MISSING_PARAMETERS',
          message: '缺少报告配置ID'
        });
      }

      const reports = await automatedReportingService.generateReport(configId, parameters);

      res.json({
        success: true,
        message: '报告生成成功',
        data: {
          reports,
          totalReports: reports.length
        }
      });

    } catch (error) {
      logger.error('生成报告失败', {
        error: error instanceof Error ? error.message : String(error),
        configId: req.body.configId
      });

      res.status(500).json({
        success: false,
        error: 'INTERNAL_ERROR',
        message: '生成报告失败'
      });
    }
  };

  /**
   * 获取报告列表
   * GET /api/v1/analytics/reports
   */
  getReports = async (req: Request, res: Response): Promise<void> => {
    try {
      const { type, startDate, endDate, configId } = req.query;

      const filters: any = {};
      if (type) filters.type = type as ReportType;
      if (startDate) filters.startDate = new Date(startDate as string);
      if (endDate) filters.endDate = new Date(endDate as string);
      if (configId) filters.configId = configId as string;

      const reports = await automatedReportingService.getReports(filters);

      res.json({
        success: true,
        data: {
          reports,
          totalReports: reports.length
        }
      });

    } catch (error) {
      logger.error('获取报告列表失败', {
        error: error instanceof Error ? error.message : String(error)
      });

      res.status(500).json({
        success: false,
        error: 'INTERNAL_ERROR',
        message: '获取报告列表失败'
      });
    }
  };

  /**
   * 下载报告
   * GET /api/v1/analytics/reports/:reportId/download
   */
  downloadReport = async (req: Request, res: Response): Promise<void> => {
    try {
      const { reportId } = req.params;

      const { filePath, fileName, mimeType } = await automatedReportingService.downloadReport(reportId);

      res.setHeader('Content-Type', mimeType);
      res.setHeader('Content-Disposition', `attachment; filename="${fileName}"`);
      
      // 这里应该发送实际文件
      // 简化实现：返回下载信息
      res.json({
        success: true,
        message: '报告下载准备就绪',
        data: {
          fileName,
          mimeType,
          downloadUrl: `/files/reports/${fileName}`
        }
      });

    } catch (error) {
      logger.error('下载报告失败', {
        error: error instanceof Error ? error.message : String(error),
        reportId: req.params.reportId
      });

      res.status(500).json({
        success: false,
        error: 'INTERNAL_ERROR',
        message: '下载报告失败'
      });
    }
  };

  /**
   * 更新威胁情报
   * POST /api/v1/analytics/security/threat-intelligence
   */
  updateThreatIntelligence = async (req: Request, res: Response): Promise<void> => {
    try {
      const {
        type,
        value,
        threatType,
        severity,
        source,
        confidence,
        tags,
        description
      } = req.body;

      if (!type || !value || !threatType) {
        return res.status(400).json({
          success: false,
          error: 'MISSING_PARAMETERS',
          message: '缺少必需参数'
        });
      }

      await securityEventAnalyticsService.updateThreatIntelligence({
        type,
        value,
        threatType,
        severity,
        source,
        confidence,
        tags,
        description
      });

      res.json({
        success: true,
        message: '威胁情报已更新'
      });

    } catch (error) {
      logger.error('更新威胁情报失败', {
        error: error instanceof Error ? error.message : String(error)
      });

      res.status(500).json({
        success: false,
        error: 'INTERNAL_ERROR',
        message: '更新威胁情报失败'
      });
    }
  };
}

// 创建控制器实例
export const analyticsController = new AnalyticsController();
