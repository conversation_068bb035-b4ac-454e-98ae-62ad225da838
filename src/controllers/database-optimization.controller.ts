/**
 * 数据库优化控制器
 * 提供数据库查询优化和性能监控的API接口
 */

import { Request, Response } from 'express';
import { queryAnalyzer } from '@/services/query-analyzer.service';
import { connectionPool } from '@/services/connection-pool.service';
import { prismaOptimizer } from '@/services/prisma-optimizer.service';
import { indexManager } from '@/services/index-manager.service';
import { logger } from '@/config/logger';
import { validateRequest } from '@/utils/validation';
import { z } from 'zod';

/**
 * 查询分析请求模式
 */
const queryAnalysisSchema = z.object({
  timeRange: z.object({
    start: z.string().datetime().optional(),
    end: z.string().datetime().optional()
  }).optional(),
  limit: z.number().positive().max(1000).optional()
});

/**
 * 索引操作请求模式
 */
const indexOperationSchema = z.object({
  operation: z.enum(['create', 'drop', 'analyze']),
  indexName: z.string().optional(),
  tableName: z.string().optional(),
  columns: z.array(z.string()).optional(),
  sql: z.string().optional()
});

/**
 * 数据库优化控制器
 */
export class DatabaseOptimizationController {

  /**
   * 获取查询性能统计
   * GET /api/v1/database/query-stats
   */
  getQueryStats = async (req: Request, res: Response): Promise<void> => {
    try {
      const stats = queryAnalyzer.getPerformanceStats();

      res.json({
        success: true,
        data: {
          ...stats,
          timestamp: new Date().toISOString()
        }
      });

    } catch (error) {
      logger.error('获取查询统计失败', { error: error.message });
      res.status(500).json({
        success: false,
        error: 'INTERNAL_ERROR',
        message: '获取查询统计失败'
      });
    }
  };

  /**
   * 获取慢查询列表
   * GET /api/v1/database/slow-queries
   */
  getSlowQueries = async (req: Request, res: Response): Promise<void> => {
    try {
      const validation = validateRequest(queryAnalysisSchema, req.query);
      if (!validation.success) {
        res.status(400).json({
          success: false,
          error: 'VALIDATION_ERROR',
          message: '请求参数验证失败',
          details: validation.errors
        });
        return;
      }

      const { limit = 50 } = validation.data;
      const slowQueries = queryAnalyzer.getSlowQueries(limit);

      res.json({
        success: true,
        data: {
          slowQueries,
          count: slowQueries.length,
          timestamp: new Date().toISOString()
        }
      });

    } catch (error) {
      logger.error('获取慢查询失败', { error: error.message });
      res.status(500).json({
        success: false,
        error: 'INTERNAL_ERROR',
        message: '获取慢查询失败'
      });
    }
  };

  /**
   * 获取查询历史
   * GET /api/v1/database/query-history
   */
  getQueryHistory = async (req: Request, res: Response): Promise<void> => {
    try {
      const validation = validateRequest(queryAnalysisSchema, req.query);
      if (!validation.success) {
        res.status(400).json({
          success: false,
          error: 'VALIDATION_ERROR',
          message: '请求参数验证失败',
          details: validation.errors
        });
        return;
      }

      const { limit = 100 } = validation.data;
      const queryHistory = queryAnalyzer.getQueryHistory(limit);

      res.json({
        success: true,
        data: {
          queries: queryHistory,
          count: queryHistory.length,
          timestamp: new Date().toISOString()
        }
      });

    } catch (error) {
      logger.error('获取查询历史失败', { error: error.message });
      res.status(500).json({
        success: false,
        error: 'INTERNAL_ERROR',
        message: '获取查询历史失败'
      });
    }
  };

  /**
   * 获取连接池状态
   * GET /api/v1/database/connection-pool
   */
  getConnectionPoolStatus = async (req: Request, res: Response): Promise<void> => {
    try {
      const stats = connectionPool.getStats();
      const currentHealth = connectionPool.getCurrentHealth();
      const healthHistory = connectionPool.getHealthHistory(10);

      res.json({
        success: true,
        data: {
          stats,
          currentHealth,
          healthHistory,
          timestamp: new Date().toISOString()
        }
      });

    } catch (error) {
      logger.error('获取连接池状态失败', { error: error.message });
      res.status(500).json({
        success: false,
        error: 'INTERNAL_ERROR',
        message: '获取连接池状态失败'
      });
    }
  };

  /**
   * 获取索引分析结果
   * GET /api/v1/database/index-analysis
   */
  getIndexAnalysis = async (req: Request, res: Response): Promise<void> => {
    try {
      const analysis = await indexManager.analyzeIndexUsage();

      res.json({
        success: true,
        data: {
          ...analysis,
          timestamp: new Date().toISOString()
        }
      });

    } catch (error) {
      logger.error('获取索引分析失败', { error: error.message });
      res.status(500).json({
        success: false,
        error: 'INTERNAL_ERROR',
        message: '获取索引分析失败'
      });
    }
  };

  /**
   * 获取所有索引信息
   * GET /api/v1/database/indexes
   */
  getAllIndexes = async (req: Request, res: Response): Promise<void> => {
    try {
      const indexes = await indexManager.getAllIndexes();

      res.json({
        success: true,
        data: {
          indexes,
          count: indexes.length,
          timestamp: new Date().toISOString()
        }
      });

    } catch (error) {
      logger.error('获取索引信息失败', { error: error.message });
      res.status(500).json({
        success: false,
        error: 'INTERNAL_ERROR',
        message: '获取索引信息失败'
      });
    }
  };

  /**
   * 执行索引操作
   * POST /api/v1/database/index-operation
   */
  executeIndexOperation = async (req: Request, res: Response): Promise<void> => {
    try {
      const validation = validateRequest(indexOperationSchema, req.body);
      if (!validation.success) {
        res.status(400).json({
          success: false,
          error: 'VALIDATION_ERROR',
          message: '请求参数验证失败',
          details: validation.errors
        });
        return;
      }

      const { operation, sql } = validation.data;

      if (operation === 'analyze') {
        const analysis = await indexManager.analyzeIndexUsage();
        res.json({
          success: true,
          data: {
            operation: 'analyze',
            result: analysis
          }
        });
        return;
      }

      if (!sql) {
        res.status(400).json({
          success: false,
          error: 'MISSING_SQL',
          message: '缺少SQL语句'
        });
        return;
      }

      // 执行索引操作（需要管理员权限）
      const recommendation = {
        type: operation as any,
        priority: 'medium' as const,
        reason: '手动执行',
        impact: '用户请求',
        sql
      };

      const success = await indexManager.executeRecommendation(recommendation);

      res.json({
        success,
        data: {
          operation,
          sql,
          executed: success,
          message: success ? '索引操作执行成功' : '索引操作执行失败'
        }
      });

    } catch (error) {
      logger.error('执行索引操作失败', { error: error.message });
      res.status(500).json({
        success: false,
        error: 'INTERNAL_ERROR',
        message: '执行索引操作失败'
      });
    }
  };

  /**
   * 获取数据库统计信息
   * GET /api/v1/database/stats
   */
  getDatabaseStats = async (req: Request, res: Response): Promise<void> => {
    try {
      const stats = await prismaOptimizer.getDatabaseStats();

      res.json({
        success: true,
        data: {
          ...stats,
          timestamp: new Date().toISOString()
        }
      });

    } catch (error) {
      logger.error('获取数据库统计失败', { error: error.message });
      res.status(500).json({
        success: false,
        error: 'INTERNAL_ERROR',
        message: '获取数据库统计失败'
      });
    }
  };

  /**
   * 执行数据清理
   * POST /api/v1/database/cleanup
   */
  performDataCleanup = async (req: Request, res: Response): Promise<void> => {
    try {
      const result = await prismaOptimizer.cleanupExpiredData();

      res.json({
        success: true,
        data: {
          ...result,
          message: '数据清理完成',
          timestamp: new Date().toISOString()
        }
      });

    } catch (error) {
      logger.error('数据清理失败', { error: error.message });
      res.status(500).json({
        success: false,
        error: 'INTERNAL_ERROR',
        message: '数据清理失败'
      });
    }
  };

  /**
   * 获取优化建议
   * GET /api/v1/database/optimization-suggestions
   */
  getOptimizationSuggestions = async (req: Request, res: Response): Promise<void> => {
    try {
      // 综合各种分析结果生成优化建议
      const [queryStats, indexAnalysis, connectionStats] = await Promise.all([
        queryAnalyzer.getPerformanceStats(),
        indexManager.analyzeIndexUsage(),
        connectionPool.getStats()
      ]);

      const suggestions = this.generateOptimizationSuggestions(
        queryStats,
        indexAnalysis,
        connectionStats
      );

      res.json({
        success: true,
        data: {
          suggestions,
          summary: {
            totalSuggestions: suggestions.length,
            highPriority: suggestions.filter(s => s.priority === 'high').length,
            mediumPriority: suggestions.filter(s => s.priority === 'medium').length,
            lowPriority: suggestions.filter(s => s.priority === 'low').length
          },
          timestamp: new Date().toISOString()
        }
      });

    } catch (error) {
      logger.error('获取优化建议失败', { error: error.message });
      res.status(500).json({
        success: false,
        error: 'INTERNAL_ERROR',
        message: '获取优化建议失败'
      });
    }
  };

  /**
   * 重置查询统计
   * POST /api/v1/database/reset-stats
   */
  resetQueryStats = async (req: Request, res: Response): Promise<void> => {
    try {
      queryAnalyzer.resetStats();
      connectionPool.resetStats();

      res.json({
        success: true,
        data: {
          message: '查询统计已重置',
          timestamp: new Date().toISOString()
        }
      });

    } catch (error) {
      logger.error('重置查询统计失败', { error: error.message });
      res.status(500).json({
        success: false,
        error: 'INTERNAL_ERROR',
        message: '重置查询统计失败'
      });
    }
  };

  /**
   * 生成综合优化建议
   */
  private generateOptimizationSuggestions(
    queryStats: any,
    indexAnalysis: any,
    connectionStats: any
  ): Array<{
    category: string;
    priority: 'high' | 'medium' | 'low';
    title: string;
    description: string;
    impact: string;
    action: string;
  }> {
    const suggestions = [];

    // 查询性能建议
    if (queryStats.slowQueries > queryStats.totalQueries * 0.1) {
      suggestions.push({
        category: 'query_performance',
        priority: 'high' as const,
        title: '慢查询比例过高',
        description: `慢查询占总查询的${((queryStats.slowQueries / queryStats.totalQueries) * 100).toFixed(1)}%`,
        impact: '严重影响系统性能',
        action: '优化慢查询SQL语句，添加必要索引'
      });
    }

    // N+1查询建议
    if (queryStats.nPlusOneProblems.length > 0) {
      suggestions.push({
        category: 'n_plus_one',
        priority: 'high' as const,
        title: 'N+1查询问题',
        description: `检测到${queryStats.nPlusOneProblems.length}个N+1查询模式`,
        impact: '大量重复查询影响性能',
        action: '使用Prisma的include或预加载功能'
      });
    }

    // 索引建议
    if (indexAnalysis.unusedIndexes.length > 0) {
      suggestions.push({
        category: 'index_optimization',
        priority: 'medium' as const,
        title: '未使用的索引',
        description: `发现${indexAnalysis.unusedIndexes.length}个未使用的索引`,
        impact: `可节省${Math.round(indexAnalysis.potentialSavings / 1024 / 1024)}MB存储空间`,
        action: '删除未使用的索引以减少维护开销'
      });
    }

    // 连接池建议
    if (connectionStats.activeConnections > connectionStats.totalConnections * 0.8) {
      suggestions.push({
        category: 'connection_pool',
        priority: 'medium' as const,
        title: '连接池使用率过高',
        description: `连接池使用率达到${((connectionStats.activeConnections / connectionStats.totalConnections) * 100).toFixed(1)}%`,
        impact: '可能导致连接等待和性能下降',
        action: '考虑增加连接池大小或优化查询'
      });
    }

    return suggestions;
  }
}

// 创建控制器实例
export const databaseOptimizationController = new DatabaseOptimizationController();
