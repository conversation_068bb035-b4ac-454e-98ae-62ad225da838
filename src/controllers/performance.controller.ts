/**
 * 性能优化控制器
 * 提供性能监控、优化建议和系统健康检查的API接口
 */

import { Request, Response } from 'express';
import { databaseOptimizationService } from '@/services/database-optimization.service';
import { systemMonitorService } from '@/services/system-monitor.service';
import { metricsCollector } from '@/services/metrics-collector.service';
import { cacheService } from '@/services/cache.service';
import { redisService } from '@/services/redis.service';
import { getPerformanceReport } from '@/middleware/performance.middleware';
import { logger } from '@/config/logger';

/**
 * 性能优化控制器
 */
export class PerformanceController {

  /**
   * 获取系统性能概览
   * GET /api/v1/performance/overview
   */
  getPerformanceOverview = async (req: Request, res: Response): Promise<void> => {
    try {
      const [systemHealth, dbHealth, cacheHealth, performanceReport] = await Promise.all([
        systemMonitorService.getSystemHealth(),
        databaseOptimizationService.getDatabaseHealth(),
        redisService.getHealthStatus(),
        Promise.resolve(getPerformanceReport())
      ]);

      const overview = {
        timestamp: new Date().toISOString(),
        status: this.calculateOverallStatus([
          systemHealth.status,
          dbHealth.status,
          cacheHealth.status
        ]),
        system: {
          status: systemHealth.status,
          cpu: systemHealth.resources.cpu.usage,
          memory: systemHealth.resources.memory.usage,
          disk: systemHealth.resources.disk.usage,
          uptime: systemHealth.resources.process.uptime
        },
        database: {
          status: dbHealth.status,
          responseTime: dbHealth.responseTime || 0,
          connectionPool: dbHealth.connectionPool || {},
          queryStats: dbHealth.recentQueries || {}
        },
        cache: {
          status: cacheHealth.status,
          connected: cacheHealth.connected,
          version: cacheHealth.version || 'unknown'
        },
        performance: performanceReport,
        issues: [
          ...systemHealth.issues,
          ...(dbHealth.status === 'unhealthy' ? [`数据库: ${dbHealth.error}`] : []),
          ...(cacheHealth.status === 'unhealthy' ? [`缓存: ${cacheHealth.error}`] : [])
        ]
      };

      res.json({
        success: true,
        data: overview
      });

    } catch (error) {
      logger.error('获取性能概览失败', {
        error: error instanceof Error ? error.message : String(error)
      });

      res.status(500).json({
        success: false,
        error: 'INTERNAL_ERROR',
        message: '获取性能概览失败'
      });
    }
  };

  /**
   * 获取系统资源使用情况
   * GET /api/v1/performance/system
   */
  getSystemResources = async (req: Request, res: Response): Promise<void> => {
    try {
      const resources = await systemMonitorService.getSystemResources();
      const monitoringStatus = systemMonitorService.getMonitoringStatus();

      res.json({
        success: true,
        data: {
          resources,
          monitoring: monitoringStatus,
          timestamp: new Date().toISOString()
        }
      });

    } catch (error) {
      logger.error('获取系统资源失败', {
        error: error instanceof Error ? error.message : String(error)
      });

      res.status(500).json({
        success: false,
        error: 'INTERNAL_ERROR',
        message: '获取系统资源失败'
      });
    }
  };

  /**
   * 获取数据库性能统计
   * GET /api/v1/performance/database
   */
  getDatabasePerformance = async (req: Request, res: Response): Promise<void> => {
    try {
      const timeRange = this.parseTimeRange(req.query);
      const queryStats = databaseOptimizationService.getQueryStats(timeRange);
      const connectionStats = await databaseOptimizationService.getConnectionPoolStats();
      const health = await databaseOptimizationService.getDatabaseHealth();

      res.json({
        success: true,
        data: {
          queryStats,
          connectionPool: connectionStats,
          health,
          timeRange,
          timestamp: new Date().toISOString()
        }
      });

    } catch (error) {
      logger.error('获取数据库性能失败', {
        error: error instanceof Error ? error.message : String(error)
      });

      res.status(500).json({
        success: false,
        error: 'INTERNAL_ERROR',
        message: '获取数据库性能失败'
      });
    }
  };

  /**
   * 获取缓存性能统计
   * GET /api/v1/performance/cache
   */
  getCachePerformance = async (req: Request, res: Response): Promise<void> => {
    try {
      const [cacheStats, redisHealth] = await Promise.all([
        cacheService.getStats(),
        redisService.getHealthStatus()
      ]);

      res.json({
        success: true,
        data: {
          stats: cacheStats,
          health: redisHealth,
          timestamp: new Date().toISOString()
        }
      });

    } catch (error) {
      logger.error('获取缓存性能失败', {
        error: error instanceof Error ? error.message : String(error)
      });

      res.status(500).json({
        success: false,
        error: 'INTERNAL_ERROR',
        message: '获取缓存性能失败'
      });
    }
  };

  /**
   * 获取性能指标
   * GET /api/v1/performance/metrics
   */
  getPerformanceMetrics = async (req: Request, res: Response): Promise<void> => {
    try {
      const format = req.query.format as string || 'json';
      
      if (format === 'prometheus') {
        // 返回Prometheus格式的指标
        const metrics = await metricsCollector.register.metrics();
        res.set('Content-Type', metricsCollector.register.contentType);
        res.send(metrics);
      } else {
        // 返回JSON格式的指标
        const metrics = await metricsCollector.getMetrics();
        res.json({
          success: true,
          data: {
            metrics,
            timestamp: new Date().toISOString()
          }
        });
      }

    } catch (error) {
      logger.error('获取性能指标失败', {
        error: error instanceof Error ? error.message : String(error)
      });

      res.status(500).json({
        success: false,
        error: 'INTERNAL_ERROR',
        message: '获取性能指标失败'
      });
    }
  };

  /**
   * 获取性能优化建议
   * GET /api/v1/performance/recommendations
   */
  getOptimizationRecommendations = async (req: Request, res: Response): Promise<void> => {
    try {
      const [systemHealth, dbStats, cacheStats] = await Promise.all([
        systemMonitorService.getSystemHealth(),
        databaseOptimizationService.getQueryStats(),
        cacheService.getStats()
      ]);

      const recommendations = this.generateRecommendations(systemHealth, dbStats, cacheStats);

      res.json({
        success: true,
        data: {
          recommendations,
          priority: this.categorizePriority(recommendations),
          timestamp: new Date().toISOString()
        }
      });

    } catch (error) {
      logger.error('获取优化建议失败', {
        error: error instanceof Error ? error.message : String(error)
      });

      res.status(500).json({
        success: false,
        error: 'INTERNAL_ERROR',
        message: '获取优化建议失败'
      });
    }
  };

  /**
   * 清理过期数据
   * POST /api/v1/performance/cleanup
   */
  cleanupExpiredData = async (req: Request, res: Response): Promise<void> => {
    try {
      const result = await databaseOptimizationService.cleanupExpiredData();

      logger.info('手动清理过期数据', {
        result,
        operator: req.user?.id
      });

      res.json({
        success: true,
        data: {
          message: '过期数据清理完成',
          result,
          timestamp: new Date().toISOString()
        }
      });

    } catch (error) {
      logger.error('清理过期数据失败', {
        error: error instanceof Error ? error.message : String(error),
        operator: req.user?.id
      });

      res.status(500).json({
        success: false,
        error: 'INTERNAL_ERROR',
        message: '清理过期数据失败'
      });
    }
  };

  /**
   * 启动系统监控
   * POST /api/v1/performance/monitoring/start
   */
  startMonitoring = async (req: Request, res: Response): Promise<void> => {
    try {
      const interval = parseInt(req.body.interval) || 30000;
      systemMonitorService.startMonitoring(interval);

      logger.info('启动系统监控', {
        interval,
        operator: req.user?.id
      });

      res.json({
        success: true,
        data: {
          message: '系统监控已启动',
          interval,
          timestamp: new Date().toISOString()
        }
      });

    } catch (error) {
      logger.error('启动系统监控失败', {
        error: error instanceof Error ? error.message : String(error)
      });

      res.status(500).json({
        success: false,
        error: 'INTERNAL_ERROR',
        message: '启动系统监控失败'
      });
    }
  };

  /**
   * 停止系统监控
   * POST /api/v1/performance/monitoring/stop
   */
  stopMonitoring = async (req: Request, res: Response): Promise<void> => {
    try {
      systemMonitorService.stopMonitoring();

      logger.info('停止系统监控', {
        operator: req.user?.id
      });

      res.json({
        success: true,
        data: {
          message: '系统监控已停止',
          timestamp: new Date().toISOString()
        }
      });

    } catch (error) {
      logger.error('停止系统监控失败', {
        error: error instanceof Error ? error.message : String(error)
      });

      res.status(500).json({
        success: false,
        error: 'INTERNAL_ERROR',
        message: '停止系统监控失败'
      });
    }
  };

  /**
   * 解析时间范围参数
   */
  private parseTimeRange(query: any): { start: Date; end: Date } | undefined {
    const { start, end } = query;
    
    if (start && end) {
      return {
        start: new Date(start),
        end: new Date(end)
      };
    }

    return undefined;
  }

  /**
   * 计算总体状态
   */
  private calculateOverallStatus(statuses: string[]): string {
    if (statuses.includes('critical') || statuses.includes('unhealthy')) {
      return 'critical';
    }
    if (statuses.includes('warning')) {
      return 'warning';
    }
    return 'healthy';
  }

  /**
   * 生成优化建议
   */
  private generateRecommendations(systemHealth: any, dbStats: any, cacheStats: any): any[] {
    const recommendations = [];

    // 系统资源建议
    if (systemHealth.resources.cpu.usage > 80) {
      recommendations.push({
        type: 'system',
        priority: 'high',
        title: 'CPU使用率过高',
        description: `当前CPU使用率为${systemHealth.resources.cpu.usage.toFixed(1)}%，建议优化代码或增加服务器资源`,
        actions: [
          '检查是否有CPU密集型操作',
          '考虑使用缓存减少计算',
          '优化算法复杂度',
          '考虑水平扩展'
        ]
      });
    }

    if (systemHealth.resources.memory.usage > 85) {
      recommendations.push({
        type: 'system',
        priority: 'high',
        title: '内存使用率过高',
        description: `当前内存使用率为${systemHealth.resources.memory.usage.toFixed(1)}%，建议优化内存使用`,
        actions: [
          '检查内存泄漏',
          '优化数据结构',
          '增加内存容量',
          '实施内存缓存策略'
        ]
      });
    }

    // 数据库建议
    if (dbStats.slowQueryRate > 10) {
      recommendations.push({
        type: 'database',
        priority: 'medium',
        title: '慢查询率过高',
        description: `慢查询率为${dbStats.slowQueryRate.toFixed(1)}%，建议优化数据库查询`,
        actions: [
          '添加适当的索引',
          '优化查询语句',
          '考虑查询缓存',
          '分析查询执行计划'
        ]
      });
    }

    if (dbStats.successRate < 95) {
      recommendations.push({
        type: 'database',
        priority: 'high',
        title: '数据库错误率过高',
        description: `数据库成功率为${dbStats.successRate.toFixed(1)}%，需要检查数据库连接和查询`,
        actions: [
          '检查数据库连接池配置',
          '分析错误日志',
          '优化查询超时设置',
          '检查数据库服务器状态'
        ]
      });
    }

    // 缓存建议
    const hitRate = cacheStats.hits / (cacheStats.hits + cacheStats.misses) * 100;
    if (hitRate < 80) {
      recommendations.push({
        type: 'cache',
        priority: 'medium',
        title: '缓存命中率偏低',
        description: `缓存命中率为${hitRate.toFixed(1)}%，建议优化缓存策略`,
        actions: [
          '调整缓存过期时间',
          '增加缓存容量',
          '优化缓存键设计',
          '实施预热策略'
        ]
      });
    }

    return recommendations;
  }

  /**
   * 分类优先级
   */
  private categorizePriority(recommendations: any[]): any {
    return {
      high: recommendations.filter(r => r.priority === 'high').length,
      medium: recommendations.filter(r => r.priority === 'medium').length,
      low: recommendations.filter(r => r.priority === 'low').length,
      total: recommendations.length
    };
  }
}

// 创建控制器实例
export const performanceController = new PerformanceController();
