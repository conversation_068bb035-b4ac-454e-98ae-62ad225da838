/**
 * SAML 2.0 控制器
 * 处理SAML协议的HTTP请求
 */

import { Request, Response } from 'express';
import { samlService } from '@/services/saml.service';
import { samlIdPService } from '@/services/saml-idp.service';
import { logger } from '@/config/logger';
import { deflateString, base64UrlEncode } from '@/utils/saml';

export class SAMLController {
  /**
   * SAML SSO端点 - 处理认证请求
   * GET/POST /saml/sso
   */
  sso = async (req: Request, res: Response): Promise<void> => {
    try {
      const { SAMLRequest, RelayState } = req.method === 'GET' ? req.query : req.body;
      
      if (!SAMLRequest) {
        res.status(400).json({
          error: 'missing_parameter',
          error_description: '缺少SAMLRequest参数'
        });
        return;
      }

      const binding = req.method === 'GET' ? 'HTTP-Redirect' : 'HTTP-POST';

      // 处理SAML认证请求
      const requestInfo = await samlIdPService.handleAuthenticationRequest(
        SAMLRequest as string,
        RelayState as string,
        binding
      );

      // 检查用户是否已登录
      const user = (req as any).user;
      if (!user) {
        // 重定向到登录页面，携带SAML请求信息
        const loginUrl = `/login?saml_request=${encodeURIComponent(requestInfo.requestId)}`;
        if (RelayState) {
          loginUrl += `&relay_state=${encodeURIComponent(RelayState as string)}`;
        }
        res.redirect(loginUrl);
        return;
      }

      // 用户已登录，直接生成SAML响应
      await this.generateAndSendResponse(res, requestInfo.requestId, user.userId);

    } catch (error) {
      logger.error('SAML SSO处理失败', {
        error: error instanceof Error ? error.message : String(error),
        method: req.method,
        userAgent: req.get('User-Agent')
      });

      res.status(500).json({
        error: 'server_error',
        error_description: 'SAML认证处理失败'
      });
    }
  };

  /**
   * SAML认证完成端点 - 用户登录后调用
   * POST /saml/authenticate
   */
  authenticate = async (req: Request, res: Response): Promise<void> => {
    try {
      const { requestId, userId, attributes } = req.body;

      if (!requestId || !userId) {
        res.status(400).json({
          error: 'missing_parameter',
          error_description: '缺少必需参数'
        });
        return;
      }

      await this.generateAndSendResponse(res, requestId, userId, attributes);

    } catch (error) {
      logger.error('SAML认证完成处理失败', {
        error: error instanceof Error ? error.message : String(error)
      });

      res.status(500).json({
        error: 'server_error',
        error_description: 'SAML认证完成处理失败'
      });
    }
  };

  /**
   * SAML SLO端点 - 处理登出请求
   * GET/POST /saml/slo
   */
  slo = async (req: Request, res: Response): Promise<void> => {
    try {
      const { SAMLRequest, RelayState } = req.method === 'GET' ? req.query : req.body;
      
      if (!SAMLRequest) {
        res.status(400).json({
          error: 'missing_parameter',
          error_description: '缺少SAMLRequest参数'
        });
        return;
      }

      const binding = req.method === 'GET' ? 'HTTP-Redirect' : 'HTTP-POST';

      // 处理SAML登出请求
      const logoutInfo = await samlIdPService.handleLogoutRequest(
        SAMLRequest as string,
        RelayState as string,
        binding
      );

      // 发送登出响应
      await this.sendLogoutResponse(res, logoutInfo, binding);

    } catch (error) {
      logger.error('SAML SLO处理失败', {
        error: error instanceof Error ? error.message : String(error),
        method: req.method
      });

      res.status(500).json({
        error: 'server_error',
        error_description: 'SAML登出处理失败'
      });
    }
  };

  /**
   * SAML元数据端点
   * GET /saml/metadata
   */
  metadata = async (req: Request, res: Response): Promise<void> => {
    try {
      // 使用新的 SAML IdP 服务生成元数据
      const metadata = samlIdPService.generateMetadata();

      res.set({
        'Content-Type': 'application/samlmetadata+xml',
        'Cache-Control': 'public, max-age=3600' // 缓存1小时
      });

      res.send(metadata);

      logger.info('SAML元数据请求处理成功', {
        userAgent: req.get('User-Agent'),
        ip: req.ip
      });

    } catch (error) {
      logger.error('SAML元数据生成失败', {
        error: error instanceof Error ? error.message : String(error)
      });

      res.status(500).json({
        error: 'server_error',
        error_description: '元数据生成失败'
      });
    }
  };

  /**
   * 生成并发送SAML响应
   */
  private async generateAndSendResponse(
    res: Response,
    requestId: string,
    userId: string,
    attributes?: Record<string, any>
  ): Promise<void> {
    try {
      // 生成SAML响应
      const responseInfo = await samlIdPService.generateAuthenticationResponse(
        requestId,
        userId,
        attributes
      );

      // 构建自动提交的HTML表单
      const html = this.buildAutoSubmitForm(
        responseInfo.samlResponse,
        responseInfo.relayState
      );

      res.set('Content-Type', 'text/html');
      res.send(html);

      logger.info('SAML响应发送成功', {
        requestId,
        userId
      });

    } catch (error) {
      logger.error('SAML响应生成失败', {
        error: error instanceof Error ? error.message : String(error),
        requestId,
        userId
      });

      throw error;
    }
  }

  /**
   * 发送登出响应
   */
  private async sendLogoutResponse(
    res: Response,
    logoutInfo: { logoutResponse: string; relayState?: string },
    binding: 'HTTP-POST' | 'HTTP-Redirect'
  ): Promise<void> {
    try {
      if (binding === 'HTTP-Redirect') {
        // HTTP-Redirect绑定：构建重定向URL
        const params = new URLSearchParams();
        params.set('SAMLResponse', logoutInfo.logoutResponse);
        
        if (logoutInfo.relayState) {
          params.set('RelayState', logoutInfo.relayState);
        }

        // 这里应该重定向到SP的SLO端点，但我们需要从请求中获取
        // 为了简化，返回成功页面
        res.send(`
          <html>
            <head><title>登出成功</title></head>
            <body>
              <h1>登出成功</h1>
              <p>您已成功登出。</p>
              <script>
                // 如果有RelayState，可以重定向到指定页面
                ${logoutInfo.relayState ? `window.location.href = '${logoutInfo.relayState}';` : ''}
              </script>
            </body>
          </html>
        `);
      } else {
        // HTTP-POST绑定：构建自动提交表单
        const html = this.buildLogoutResponseForm(
          logoutInfo.logoutResponse,
          logoutInfo.relayState
        );

        res.set('Content-Type', 'text/html');
        res.send(html);
      }

      logger.info('SAML登出响应发送成功', {
        binding
      });

    } catch (error) {
      logger.error('SAML登出响应发送失败', {
        error: error instanceof Error ? error.message : String(error)
      });

      throw error;
    }
  }

  /**
   * 构建自动提交的HTML表单（用于SSO响应）
   */
  private buildAutoSubmitForm(samlResponse: string, relayState?: string): string {
    return `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <title>SAML认证</title>
          <style>
            body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
            .loading { color: #666; }
          </style>
        </head>
        <body>
          <div class="loading">
            <h2>正在进行SAML认证...</h2>
            <p>请稍候，正在重定向到应用程序。</p>
          </div>
          
          <form id="samlForm" method="post" action="#" style="display: none;">
            <input type="hidden" name="SAMLResponse" value="${samlResponse}" />
            ${relayState ? `<input type="hidden" name="RelayState" value="${relayState}" />` : ''}
          </form>
          
          <script>
            // 自动提交表单
            document.getElementById('samlForm').submit();
          </script>
        </body>
      </html>
    `;
  }

  /**
   * 构建登出响应表单
   */
  private buildLogoutResponseForm(logoutResponse: string, relayState?: string): string {
    return `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <title>SAML登出</title>
          <style>
            body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
            .loading { color: #666; }
          </style>
        </head>
        <body>
          <div class="loading">
            <h2>正在完成登出...</h2>
            <p>请稍候，正在处理登出请求。</p>
          </div>
          
          <form id="logoutForm" method="post" action="#" style="display: none;">
            <input type="hidden" name="SAMLResponse" value="${logoutResponse}" />
            ${relayState ? `<input type="hidden" name="RelayState" value="${relayState}" />` : ''}
          </form>
          
          <script>
            // 自动提交表单
            document.getElementById('logoutForm').submit();
          </script>
        </body>
      </html>
    `;
  }
}

// 创建控制器实例
export const samlController = new SAMLController();
