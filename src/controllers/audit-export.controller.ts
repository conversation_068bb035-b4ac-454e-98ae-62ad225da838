/**
 * 审计数据导出控制器
 * 提供审计数据导出和报告生成的API接口
 */

import { Request, Response } from 'express';
import { body, query, validationResult } from 'express-validator';
import { logger } from '@/config/logger';
import { auditExportService, ExportFormat, ReportType, ExportOptions } from '@/services/audit-export.service';
import { securityAuditService, AuditEventType, AuditSeverity } from '@/services/security-audit.service';
import { metricsCollector } from '@/services/metrics-collector.service';

/**
 * 审计导出控制器类
 */
export class AuditExportController {
  /**
   * 导出审计数据
   */
  async exportAuditData(req: Request, res: Response): Promise<void> {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        res.status(400).json({
          success: false,
          message: '请求参数验证失败',
          errors: errors.array()
        });
        return;
      }

      const {
        format,
        reportType,
        startDate,
        endDate,
        userId,
        eventTypes,
        severity,
        includeMetadata = false,
        includeCharts = false,
        customFields,
        maxRecords = 10000,
        compression = false
      } = req.body;

      logger.info('开始导出审计数据', {
        format,
        reportType,
        startDate,
        endDate,
        userId: req.user?.id,
        requestedBy: req.user?.username
      });

      const options: ExportOptions = {
        format: format as ExportFormat,
        reportType: reportType as ReportType,
        startDate: new Date(startDate),
        endDate: new Date(endDate),
        userId,
        eventTypes: eventTypes as AuditEventType[],
        severity: severity as AuditSeverity[],
        includeMetadata,
        includeCharts,
        customFields,
        maxRecords,
        compression
      };

      const startTime = Date.now();
      const result = await auditExportService.exportAuditData(options);
      const duration = Date.now() - startTime;

      // 记录审计事件
      await securityAuditService.logEvent({
        type: AuditEventType.DATA_EXPORT,
        userId: req.user?.id,
        ipAddress: req.ip,
        userAgent: req.get('User-Agent'),
        details: {
          exportType: 'audit_data',
          format,
          reportType,
          recordCount: result.recordCount,
          success: result.success,
          duration
        }
      });

      if (result.success) {
        res.json({
          success: true,
          data: {
            fileName: result.fileName,
            fileSize: result.fileSize,
            recordCount: result.recordCount,
            exportTime: result.exportTime,
            downloadUrl: `/api/v1/audit/export/download/${result.fileName}`,
            expiresAt: result.expiresAt
          },
          meta: {
            duration: `${duration}ms`
          }
        });
      } else {
        res.status(400).json({
          success: false,
          message: result.error || '导出失败'
        });
      }

    } catch (error) {
      logger.error('审计数据导出失败', {
        error: error instanceof Error ? error.message : String(error),
        userId: req.user?.id
      });

      metricsCollector.incrementCounter('audit_export_errors_total');

      res.status(500).json({
        success: false,
        message: '导出服务暂时不可用'
      });
    }
  }

  /**
   * 生成合规报告
   */
  async generateComplianceReport(req: Request, res: Response): Promise<void> {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        res.status(400).json({
          success: false,
          message: '请求参数验证失败',
          errors: errors.array()
        });
        return;
      }

      const { startDate, endDate, format = ExportFormat.PDF } = req.body;

      logger.info('生成合规报告', {
        startDate,
        endDate,
        format,
        userId: req.user?.id
      });

      const result = await auditExportService.generateComplianceReport(
        new Date(startDate),
        new Date(endDate),
        format as ExportFormat
      );

      await securityAuditService.logEvent({
        type: AuditEventType.COMPLIANCE_REPORT_GENERATED,
        userId: req.user?.id,
        ipAddress: req.ip,
        details: {
          reportType: 'compliance',
          format,
          timeRange: { startDate, endDate },
          success: result.success
        }
      });

      if (result.success) {
        res.json({
          success: true,
          data: {
            fileName: result.fileName,
            fileSize: result.fileSize,
            recordCount: result.recordCount,
            downloadUrl: `/api/v1/audit/export/download/${result.fileName}`,
            expiresAt: result.expiresAt
          }
        });
      } else {
        res.status(400).json({
          success: false,
          message: result.error || '生成合规报告失败'
        });
      }

    } catch (error) {
      logger.error('生成合规报告失败', {
        error: error instanceof Error ? error.message : String(error)
      });

      res.status(500).json({
        success: false,
        message: '生成报告服务暂时不可用'
      });
    }
  }

  /**
   * 生成安全摘要报告
   */
  async generateSecuritySummary(req: Request, res: Response): Promise<void> {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        res.status(400).json({
          success: false,
          message: '请求参数验证失败',
          errors: errors.array()
        });
        return;
      }

      const { startDate, endDate, format = ExportFormat.PDF } = req.body;

      logger.info('生成安全摘要报告', {
        startDate,
        endDate,
        format,
        userId: req.user?.id
      });

      const result = await auditExportService.generateSecuritySummary(
        new Date(startDate),
        new Date(endDate),
        format as ExportFormat
      );

      await securityAuditService.logEvent({
        type: AuditEventType.SECURITY_REPORT_GENERATED,
        userId: req.user?.id,
        ipAddress: req.ip,
        details: {
          reportType: 'security_summary',
          format,
          timeRange: { startDate, endDate },
          success: result.success
        }
      });

      if (result.success) {
        res.json({
          success: true,
          data: {
            fileName: result.fileName,
            fileSize: result.fileSize,
            recordCount: result.recordCount,
            downloadUrl: `/api/v1/audit/export/download/${result.fileName}`,
            expiresAt: result.expiresAt
          }
        });
      } else {
        res.status(400).json({
          success: false,
          message: result.error || '生成安全摘要报告失败'
        });
      }

    } catch (error) {
      logger.error('生成安全摘要报告失败', {
        error: error instanceof Error ? error.message : String(error)
      });

      res.status(500).json({
        success: false,
        message: '生成报告服务暂时不可用'
      });
    }
  }

  /**
   * 下载导出文件
   */
  async downloadExportFile(req: Request, res: Response): Promise<void> {
    try {
      const { fileName } = req.params;

      if (!fileName || !/^[a-zA-Z0-9_\-\.]+$/.test(fileName)) {
        res.status(400).json({
          success: false,
          message: '无效的文件名'
        });
        return;
      }

      logger.info('下载导出文件', {
        fileName,
        userId: req.user?.id
      });

      const exportFiles = await auditExportService.getExportFiles();
      const file = exportFiles.find(f => f.fileName === fileName);

      if (!file) {
        res.status(404).json({
          success: false,
          message: '文件不存在或已过期'
        });
        return;
      }

      // 检查文件是否过期
      if (file.expiresAt < new Date()) {
        await auditExportService.deleteExportFile(fileName);
        res.status(404).json({
          success: false,
          message: '文件已过期'
        });
        return;
      }

      // 记录下载事件
      await securityAuditService.logEvent({
        type: AuditEventType.FILE_DOWNLOAD,
        userId: req.user?.id,
        ipAddress: req.ip,
        details: {
          fileName,
          fileSize: file.fileSize,
          downloadType: 'audit_export'
        }
      });

      // 设置响应头
      const fileExtension = fileName.split('.').pop()?.toLowerCase();
      const contentTypes: Record<string, string> = {
        'json': 'application/json',
        'csv': 'text/csv',
        'xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'pdf': 'application/pdf',
        'xml': 'application/xml'
      };

      const contentType = contentTypes[fileExtension || ''] || 'application/octet-stream';

      res.setHeader('Content-Type', contentType);
      res.setHeader('Content-Disposition', `attachment; filename="${fileName}"`);
      res.setHeader('Content-Length', file.fileSize);

      // 发送文件
      res.sendFile(file.filePath, (error) => {
        if (error) {
          logger.error('发送文件失败', { fileName, error });
          if (!res.headersSent) {
            res.status(500).json({
              success: false,
              message: '文件下载失败'
            });
          }
        }
      });

    } catch (error) {
      logger.error('下载导出文件失败', {
        fileName: req.params.fileName,
        error: error instanceof Error ? error.message : String(error)
      });

      res.status(500).json({
        success: false,
        message: '下载服务暂时不可用'
      });
    }
  }

  /**
   * 获取导出文件列表
   */
  async getExportFiles(req: Request, res: Response): Promise<void> {
    try {
      logger.info('获取导出文件列表', {
        userId: req.user?.id
      });

      const files = await auditExportService.getExportFiles();

      // 过滤掉已过期的文件信息（但不删除文件）
      const validFiles = files.filter(file => file.expiresAt > new Date());

      res.json({
        success: true,
        data: validFiles.map(file => ({
          fileName: file.fileName,
          fileSize: file.fileSize,
          createdAt: file.createdAt,
          expiresAt: file.expiresAt,
          downloadUrl: `/api/v1/audit/export/download/${file.fileName}`
        })),
        meta: {
          total: validFiles.length,
          queryTime: new Date().toISOString()
        }
      });

    } catch (error) {
      logger.error('获取导出文件列表失败', {
        error: error instanceof Error ? error.message : String(error)
      });

      res.status(500).json({
        success: false,
        message: '获取文件列表失败'
      });
    }
  }

  /**
   * 删除导出文件
   */
  async deleteExportFile(req: Request, res: Response): Promise<void> {
    try {
      const { fileName } = req.params;

      if (!fileName || !/^[a-zA-Z0-9_\-\.]+$/.test(fileName)) {
        res.status(400).json({
          success: false,
          message: '无效的文件名'
        });
        return;
      }

      logger.info('删除导出文件', {
        fileName,
        userId: req.user?.id
      });

      const success = await auditExportService.deleteExportFile(fileName);

      if (success) {
        await securityAuditService.logEvent({
          type: AuditEventType.FILE_DELETED,
          userId: req.user?.id,
          ipAddress: req.ip,
          details: {
            fileName,
            deleteType: 'audit_export'
          }
        });

        res.json({
          success: true,
          message: '文件删除成功'
        });
      } else {
        res.status(404).json({
          success: false,
          message: '文件不存在或删除失败'
        });
      }

    } catch (error) {
      logger.error('删除导出文件失败', {
        fileName: req.params.fileName,
        error: error instanceof Error ? error.message : String(error)
      });

      res.status(500).json({
        success: false,
        message: '删除文件失败'
      });
    }
  }
}

/**
 * 验证规则
 */
export const auditExportValidation = {
  exportData: [
    body('format')
      .isIn(['json', 'csv', 'excel', 'pdf', 'xml'])
      .withMessage('导出格式必须是json、csv、excel、pdf或xml'),
    body('reportType')
      .isIn(['security_summary', 'login_activity', 'permission_changes', 'threat_detection', 'compliance_report', 'full_audit'])
      .withMessage('报告类型无效'),
    body('startDate')
      .isISO8601()
      .withMessage('开始日期格式无效'),
    body('endDate')
      .isISO8601()
      .withMessage('结束日期格式无效')
      .custom((endDate, { req }) => {
        const start = new Date(req.body.startDate);
        const end = new Date(endDate);
        if (end <= start) {
          throw new Error('结束日期必须晚于开始日期');
        }
        // 限制时间范围不超过1年
        const maxRange = 365 * 24 * 60 * 60 * 1000; // 1年
        if (end.getTime() - start.getTime() > maxRange) {
          throw new Error('时间范围不能超过1年');
        }
        return true;
      }),
    body('maxRecords')
      .optional()
      .isInt({ min: 1, max: 100000 })
      .withMessage('最大记录数必须在1-100000之间'),
    body('includeMetadata')
      .optional()
      .isBoolean()
      .withMessage('includeMetadata必须是布尔值'),
    body('includeCharts')
      .optional()
      .isBoolean()
      .withMessage('includeCharts必须是布尔值')
  ],

  generateReport: [
    body('startDate')
      .isISO8601()
      .withMessage('开始日期格式无效'),
    body('endDate')
      .isISO8601()
      .withMessage('结束日期格式无效')
      .custom((endDate, { req }) => {
        const start = new Date(req.body.startDate);
        const end = new Date(endDate);
        if (end <= start) {
          throw new Error('结束日期必须晚于开始日期');
        }
        return true;
      }),
    body('format')
      .optional()
      .isIn(['json', 'csv', 'excel', 'pdf', 'xml'])
      .withMessage('导出格式必须是json、csv、excel、pdf或xml')
  ]
};

// 创建控制器实例
export const auditExportController = new AuditExportController();
