/**
 * 威胁情报控制器
 * 提供威胁情报查询和管理的API接口
 */

import { Request, Response } from 'express';
import { body, param, query, validationResult } from 'express-validator';
import { logger } from '@/config/logger';
import { threatIntelligenceService, ThreatType, ThreatLevel } from '@/services/threat-intelligence.service';
import { securityAuditService, AuditEventType } from '@/services/security-audit.service';
import { metricsCollector } from '@/services/metrics-collector.service';

/**
 * 威胁情报控制器类
 */
export class ThreatIntelligenceController {
  /**
   * 检查IP地址威胁
   */
  async checkIPThreat(req: Request, res: Response): Promise<void> {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        res.status(400).json({
          success: false,
          message: '请求参数验证失败',
          errors: errors.array()
        });
        return;
      }

      const { ip } = req.params;
      const { includeDetails = false } = req.query;

      logger.info('开始IP威胁检查', {
        ip,
        userId: req.user?.id,
        userAgent: req.get('User-Agent')
      });

      const startTime = Date.now();
      const result = await threatIntelligenceService.checkIPThreat(ip);
      const duration = Date.now() - startTime;

      // 记录性能指标
      metricsCollector.recordHistogram('threat_intel_check_duration', duration, {
        type: 'ip',
        result: result.isThreat ? 'threat' : 'clean'
      });

      // 记录审计日志
      await securityAuditService.logEvent({
        type: AuditEventType.THREAT_INTELLIGENCE_QUERY,
        userId: req.user?.id,
        ipAddress: req.ip,
        userAgent: req.get('User-Agent'),
        details: {
          queryType: 'ip',
          indicator: ip,
          result: result.isThreat,
          riskScore: result.riskScore,
          duration
        }
      });

      // 根据请求决定返回详细信息还是简化信息
      const response = includeDetails === 'true' ? result : {
        isThreat: result.isThreat,
        riskScore: result.riskScore,
        threatCount: result.threats.length,
        recommendations: result.recommendations
      };

      res.json({
        success: true,
        data: response,
        meta: {
          queryTime: new Date().toISOString(),
          duration: `${duration}ms`
        }
      });

    } catch (error) {
      logger.error('IP威胁检查失败', {
        ip: req.params.ip,
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined
      });

      metricsCollector.incrementCounter('threat_intel_errors', {
        type: 'ip',
        error: 'internal_error'
      });

      res.status(500).json({
        success: false,
        message: '威胁检查服务暂时不可用',
        error: process.env.NODE_ENV === 'development' ? error : undefined
      });
    }
  }

  /**
   * 检查域名威胁
   */
  async checkDomainThreat(req: Request, res: Response): Promise<void> {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        res.status(400).json({
          success: false,
          message: '请求参数验证失败',
          errors: errors.array()
        });
        return;
      }

      const { domain } = req.params;
      const { includeDetails = false } = req.query;

      logger.info('开始域名威胁检查', {
        domain,
        userId: req.user?.id
      });

      const startTime = Date.now();
      const result = await threatIntelligenceService.checkDomainThreat(domain);
      const duration = Date.now() - startTime;

      metricsCollector.recordHistogram('threat_intel_check_duration', duration, {
        type: 'domain',
        result: result.isThreat ? 'threat' : 'clean'
      });

      await securityAuditService.logEvent({
        type: AuditEventType.THREAT_INTELLIGENCE_QUERY,
        userId: req.user?.id,
        ipAddress: req.ip,
        details: {
          queryType: 'domain',
          indicator: domain,
          result: result.isThreat,
          riskScore: result.riskScore,
          duration
        }
      });

      const response = includeDetails === 'true' ? result : {
        isThreat: result.isThreat,
        riskScore: result.riskScore,
        threatCount: result.threats.length,
        recommendations: result.recommendations
      };

      res.json({
        success: true,
        data: response,
        meta: {
          queryTime: new Date().toISOString(),
          duration: `${duration}ms`
        }
      });

    } catch (error) {
      logger.error('域名威胁检查失败', {
        domain: req.params.domain,
        error: error instanceof Error ? error.message : String(error)
      });

      res.status(500).json({
        success: false,
        message: '威胁检查服务暂时不可用'
      });
    }
  }

  /**
   * 检查URL威胁
   */
  async checkURLThreat(req: Request, res: Response): Promise<void> {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        res.status(400).json({
          success: false,
          message: '请求参数验证失败',
          errors: errors.array()
        });
        return;
      }

      const { url } = req.body;
      const { includeDetails = false } = req.query;

      logger.info('开始URL威胁检查', {
        url: url.substring(0, 100),
        userId: req.user?.id
      });

      const startTime = Date.now();
      const result = await threatIntelligenceService.checkURLThreat(url);
      const duration = Date.now() - startTime;

      metricsCollector.recordHistogram('threat_intel_check_duration', duration, {
        type: 'url',
        result: result.isThreat ? 'threat' : 'clean'
      });

      await securityAuditService.logEvent({
        type: AuditEventType.THREAT_INTELLIGENCE_QUERY,
        userId: req.user?.id,
        ipAddress: req.ip,
        details: {
          queryType: 'url',
          indicator: url.substring(0, 100),
          result: result.isThreat,
          riskScore: result.riskScore,
          duration
        }
      });

      const response = includeDetails === 'true' ? result : {
        isThreat: result.isThreat,
        riskScore: result.riskScore,
        threatCount: result.threats.length,
        recommendations: result.recommendations
      };

      res.json({
        success: true,
        data: response,
        meta: {
          queryTime: new Date().toISOString(),
          duration: `${duration}ms`
        }
      });

    } catch (error) {
      logger.error('URL威胁检查失败', {
        error: error instanceof Error ? error.message : String(error)
      });

      res.status(500).json({
        success: false,
        message: '威胁检查服务暂时不可用'
      });
    }
  }

  /**
   * 批量威胁检查
   */
  async batchThreatCheck(req: Request, res: Response): Promise<void> {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        res.status(400).json({
          success: false,
          message: '请求参数验证失败',
          errors: errors.array()
        });
        return;
      }

      const { indicators } = req.body;

      if (!Array.isArray(indicators) || indicators.length === 0) {
        res.status(400).json({
          success: false,
          message: '指标列表不能为空'
        });
        return;
      }

      if (indicators.length > 100) {
        res.status(400).json({
          success: false,
          message: '批量检查最多支持100个指标'
        });
        return;
      }

      logger.info('开始批量威胁检查', {
        count: indicators.length,
        userId: req.user?.id
      });

      const startTime = Date.now();
      const results = await threatIntelligenceService.batchThreatCheck(indicators);
      const duration = Date.now() - startTime;

      // 统计结果
      let threatCount = 0;
      let cleanCount = 0;
      for (const result of results.values()) {
        if (result.isThreat) {
          threatCount++;
        } else {
          cleanCount++;
        }
      }

      metricsCollector.recordHistogram('threat_intel_batch_duration', duration);
      metricsCollector.incrementCounter('threat_intel_batch_queries', {
        total: indicators.length,
        threats: threatCount,
        clean: cleanCount
      });

      await securityAuditService.logEvent({
        type: AuditEventType.THREAT_INTELLIGENCE_BATCH_QUERY,
        userId: req.user?.id,
        ipAddress: req.ip,
        details: {
          indicatorCount: indicators.length,
          threatCount,
          cleanCount,
          duration
        }
      });

      // 转换Map为对象
      const resultsObject: Record<string, any> = {};
      for (const [key, value] of results) {
        resultsObject[key] = value;
      }

      res.json({
        success: true,
        data: {
          results: resultsObject,
          summary: {
            total: indicators.length,
            threats: threatCount,
            clean: cleanCount
          }
        },
        meta: {
          queryTime: new Date().toISOString(),
          duration: `${duration}ms`
        }
      });

    } catch (error) {
      logger.error('批量威胁检查失败', {
        error: error instanceof Error ? error.message : String(error)
      });

      res.status(500).json({
        success: false,
        message: '批量威胁检查服务暂时不可用'
      });
    }
  }

  /**
   * 获取威胁情报统计
   */
  async getThreatIntelligenceStats(req: Request, res: Response): Promise<void> {
    try {
      logger.info('获取威胁情报统计', {
        userId: req.user?.id
      });

      const stats = await threatIntelligenceService.getThreatIntelligenceStats();

      res.json({
        success: true,
        data: stats,
        meta: {
          queryTime: new Date().toISOString()
        }
      });

    } catch (error) {
      logger.error('获取威胁情报统计失败', {
        error: error instanceof Error ? error.message : String(error)
      });

      res.status(500).json({
        success: false,
        message: '获取统计信息失败'
      });
    }
  }
}

/**
 * 验证规则
 */
export const threatIntelligenceValidation = {
  checkIP: [
    param('ip')
      .isIP()
      .withMessage('请提供有效的IP地址'),
    query('includeDetails')
      .optional()
      .isBoolean()
      .withMessage('includeDetails必须是布尔值')
  ],

  checkDomain: [
    param('domain')
      .isFQDN()
      .withMessage('请提供有效的域名'),
    query('includeDetails')
      .optional()
      .isBoolean()
      .withMessage('includeDetails必须是布尔值')
  ],

  checkURL: [
    body('url')
      .isURL()
      .withMessage('请提供有效的URL')
      .isLength({ max: 2048 })
      .withMessage('URL长度不能超过2048字符'),
    query('includeDetails')
      .optional()
      .isBoolean()
      .withMessage('includeDetails必须是布尔值')
  ],

  batchCheck: [
    body('indicators')
      .isArray({ min: 1, max: 100 })
      .withMessage('指标列表必须是数组，且包含1-100个元素'),
    body('indicators.*.type')
      .isIn(['ip', 'domain', 'url'])
      .withMessage('指标类型必须是ip、domain或url'),
    body('indicators.*.value')
      .isString()
      .isLength({ min: 1, max: 2048 })
      .withMessage('指标值必须是非空字符串，且长度不超过2048字符')
  ]
};

// 创建控制器实例
export const threatIntelligenceController = new ThreatIntelligenceController();
