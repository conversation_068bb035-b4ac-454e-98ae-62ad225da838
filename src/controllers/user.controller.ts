/**
 * 用户控制器
 * 处理用户个人资料管理相关的HTTP请求
 */

import { Response } from 'express';
import { AuthenticatedRequest } from '@/middleware/auth.middleware';
import { prisma } from '@/config/database';
import { logger, logAuditEvent } from '@/config/logger';
import { validateUpdateProfileRequest } from '@/utils/validation';
import { UserProfile } from '@/types/database';
import { FederatedIdentityService } from '@/services/federated-identity.service';

/**
 * 用户控制器类
 */
export class UserController {
  private federatedIdentityService: FederatedIdentityService;

  constructor() {
    this.federatedIdentityService = new FederatedIdentityService();
  }
  /**
   * 获取当前用户信息
   */
  getCurrentUser = async (req: any, res: Response): Promise<void> => {
    try {
      const userId = req.user?.userId;

      if (!userId) {
        res.status(401).json({
          error: 'unauthorized',
          message: '未授权访问'
        });
        return;
      }

      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: {
          id: true,
          email: true,
          phone: true,
          username: true,
          nickname: true,
          firstName: true,
          lastName: true,
          avatar: true,
          emailVerified: true,
          phoneVerified: true,
          isActive: true,
          isLocked: true,
          lockReason: true,
          lastLoginAt: true,
          lastLoginIp: true,
          passwordChangedAt: true,
          createdAt: true,
          updatedAt: true
        }
      });

      if (!user) {
        res.status(404).json({
          error: 'user_not_found',
          message: '用户不存在'
        });
        return;
      }

      res.status(200).json(user);

    } catch (error) {
      logger.error('获取用户信息失败', { error, userId: req.user?.userId });
      
      res.status(500).json({
        error: 'get_user_failed',
        message: '获取用户信息失败'
      });
    }
  };

  /**
   * 更新用户资料
   */
  updateProfile = async (req: any, res: Response): Promise<void> => {
    try {
      const userId = req.user?.userId;

      if (!userId) {
        res.status(401).json({
          error: 'unauthorized',
          message: '未授权访问'
        });
        return;
      }

      // 验证请求数据
      const validationResult = validateUpdateProfileRequest(req.body);
      if (!validationResult.isValid) {
        res.status(400).json({
          error: 'validation_error',
          message: '请求数据验证失败',
          details: validationResult.errors
        });
        return;
      }

      const { nickname, firstName, lastName, avatar } = req.body;

      // 更新用户资料
      const updatedUser = await prisma.user.update({
        where: { id: userId },
        data: {
          ...(nickname !== undefined && { nickname }),
          ...(firstName !== undefined && { firstName }),
          ...(lastName !== undefined && { lastName }),
          ...(avatar !== undefined && { avatar })
        },
        select: {
          id: true,
          email: true,
          phone: true,
          username: true,
          nickname: true,
          firstName: true,
          lastName: true,
          avatar: true,
          emailVerified: true,
          phoneVerified: true,
          isActive: true,
          isLocked: true,
          lockReason: true,
          lastLoginAt: true,
          lastLoginIp: true,
          passwordChangedAt: true,
          createdAt: true,
          updatedAt: true
        }
      });

      // 记录审计日志
      logAuditEvent('user_profile_update', 'user', userId, {
        changes: { nickname, firstName, lastName, avatar }
      });

      res.status(200).json(updatedUser);

    } catch (error) {
      logger.error('更新用户资料失败', { error, userId: req.user?.userId });
      
      res.status(400).json({
        error: 'update_profile_failed',
        message: '更新用户资料失败'
      });
    }
  };

  /**
   * 获取用户会话列表
   */
  getUserSessions = async (req: any, res: Response): Promise<void> => {
    try {
      const userId = req.user?.userId;

      if (!userId) {
        res.status(401).json({
          error: 'unauthorized',
          message: '未授权访问'
        });
        return;
      }

      const sessions = await prisma.session.findMany({
        where: {
          userId,
          isActive: true,
          expiresAt: { gt: new Date() }
        },
        select: {
          id: true,
          deviceInfo: true,
          ipAddress: true,
          userAgent: true,
          lastAccessedAt: true,
          createdAt: true,
          expiresAt: true,
          authMethod: true,
          mfaVerified: true
        },
        orderBy: { lastAccessedAt: 'desc' }
      });

      // 解析设备信息
      const sessionsWithParsedDeviceInfo = sessions.map(session => ({
        ...session,
        deviceInfo: session.deviceInfo ? JSON.parse(session.deviceInfo as string) : null,
        isCurrent: session.id === req.user?.sessionId
      }));

      res.status(200).json(sessionsWithParsedDeviceInfo);

    } catch (error) {
      logger.error('获取用户会话失败', { error, userId: req.user?.userId });
      
      res.status(500).json({
        error: 'get_sessions_failed',
        message: '获取用户会话失败'
      });
    }
  };

  /**
   * 终止指定会话
   */
  terminateSession = async (req: any, res: Response): Promise<void> => {
    try {
      const userId = req.user?.userId;
      const { sessionId } = req.params;

      if (!userId) {
        res.status(401).json({
          error: 'unauthorized',
          message: '未授权访问'
        });
        return;
      }

      if (!sessionId) {
        res.status(400).json({
          error: 'missing_session_id',
          message: '缺少会话ID'
        });
        return;
      }

      // 检查会话是否属于当前用户
      const session = await prisma.session.findFirst({
        where: {
          id: sessionId,
          userId,
          isActive: true
        }
      });

      if (!session) {
        res.status(404).json({
          error: 'session_not_found',
          message: '会话不存在'
        });
        return;
      }

      // 终止会话
      await prisma.session.update({
        where: { id: sessionId },
        data: { isActive: false }
      });

      // 记录审计日志
      logAuditEvent('session_terminate', 'session', userId, {
        sessionId,
        ipAddress: req.ip
      });

      res.status(204).send();

    } catch (error) {
      logger.error('终止会话失败', { error, userId: req.user?.userId });
      
      res.status(400).json({
        error: 'terminate_session_failed',
        message: '终止会话失败'
      });
    }
  };

  /**
   * 终止所有其他会话
   */
  terminateAllOtherSessions = async (req: any, res: Response): Promise<void> => {
    try {
      const userId = req.user?.userId;
      const currentSessionId = req.user?.sessionId;

      if (!userId || !currentSessionId) {
        res.status(401).json({
          error: 'unauthorized',
          message: '未授权访问'
        });
        return;
      }

      // 终止除当前会话外的所有会话
      const result = await prisma.session.updateMany({
        where: {
          userId,
          id: { not: currentSessionId },
          isActive: true
        },
        data: { isActive: false }
      });

      // 记录审计日志
      logAuditEvent('sessions_terminate_all_others', 'session', userId, {
        currentSessionId,
        terminatedCount: result.count,
        ipAddress: req.ip
      });

      res.status(200).json({
        message: `已终止 ${result.count} 个其他会话`,
        terminatedCount: result.count
      });

    } catch (error) {
      logger.error('终止其他会话失败', { error, userId: req.user?.userId });
      
      res.status(400).json({
        error: 'terminate_sessions_failed',
        message: '终止其他会话失败'
      });
    }
  };

  /**
   * 获取用户的联合身份列表
   */
  getFederatedIdentities = async (req: any, res: Response): Promise<void> => {
    try {
      const userId = req.user?.userId;

      if (!userId) {
        res.status(401).json({
          error: 'unauthorized',
          message: '未授权访问'
        });
        return;
      }

      const federatedIdentities = await this.federatedIdentityService.getUserFederatedIdentities(userId);
      const stats = await this.federatedIdentityService.getFederatedIdentityStats(userId);

      res.status(200).json({
        connections: federatedIdentities,
        stats
      });

    } catch (error) {
      logger.error('获取联合身份失败', { error, userId: req.user?.userId });

      res.status(500).json({
        error: 'get_federated_identities_failed',
        message: '获取联合身份失败'
      });
    }
  };

  /**
   * 解除联合身份关联
   */
  disconnectFederatedIdentity = async (req: any, res: Response): Promise<void> => {
    try {
      const userId = req.user?.userId;
      const { connectionId } = req.params;

      if (!userId) {
        res.status(401).json({
          error: 'unauthorized',
          message: '未授权访问'
        });
        return;
      }

      if (!connectionId) {
        res.status(400).json({
          error: 'missing_connection_id',
          message: '缺少连接ID'
        });
        return;
      }

      await this.federatedIdentityService.disconnectFederatedIdentity(userId, connectionId);

      res.status(200).json({
        message: '联合身份关联已解除',
        connectionId,
        disconnectedAt: new Date().toISOString()
      });

    } catch (error) {
      logger.error('解除联合身份关联失败', { error, userId: req.user?.userId });

      const message = error instanceof Error ? error.message : '解除联合身份关联失败';
      const statusCode = message.includes('唯一的登录方式') ? 400 : 500;

      res.status(statusCode).json({
        error: 'disconnect_federated_identity_failed',
        message
      });
    }
  };
}
