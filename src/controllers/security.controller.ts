/**
 * 安全控制器
 * 提供安全管理、审计查询、漏洞扫描和合规性检查的API接口
 */

import { Request, Response } from 'express';
import { securityAuditService, AuditEventType, AuditSeverity } from '@/services/security-audit.service';
import { securityScannerService, ScanType } from '@/services/security-scanner.service';
import { automatedSecurityScannerService } from '@/services/automated-security-scanner.service';
import { logger } from '@/config/logger';
import { prisma } from '@/config/database';

/**
 * 安全控制器
 */
export class SecurityController {

  /**
   * 获取安全概览
   * GET /api/v1/security/overview
   */
  getSecurityOverview = async (req: Request, res: Response): Promise<void> => {
    try {
      const timeRange = this.parseTimeRange(req.query, 24); // 默认24小时

      // 并行获取各种安全数据
      const [
        threatDetection,
        latestScan,
        auditStats,
        complianceCheck
      ] = await Promise.all([
        securityAuditService.performThreatDetection(timeRange),
        securityScannerService.getLatestScanResult(),
        this.getAuditStatistics(timeRange),
        securityAuditService.performComplianceCheck('GDPR')
      ]);

      // 计算总体安全评分
      const securityScore = this.calculateSecurityScore(
        threatDetection,
        latestScan,
        auditStats,
        complianceCheck
      );

      const overview = {
        timestamp: new Date().toISOString(),
        securityScore,
        status: this.determineSecurityStatus(securityScore),
        threatDetection: {
          level: threatDetection.threatLevel,
          riskScore: threatDetection.riskScore,
          threatCount: threatDetection.threats.length,
          lastUpdated: threatDetection.timestamp
        },
        vulnerabilities: latestScan ? {
          total: latestScan.summary.total,
          critical: latestScan.summary.critical,
          high: latestScan.summary.high,
          medium: latestScan.summary.medium,
          low: latestScan.summary.low,
          lastScan: latestScan.endTime
        } : null,
        auditActivity: {
          totalEvents: auditStats.totalEvents,
          securityEvents: auditStats.securityEvents,
          failedAttempts: auditStats.failedAttempts,
          suspiciousActivity: auditStats.suspiciousActivity
        },
        compliance: {
          standard: complianceCheck.standard,
          compliant: complianceCheck.compliant,
          score: complianceCheck.score,
          checkCount: complianceCheck.checks.length
        },
        recommendations: this.generateSecurityRecommendations(
          threatDetection,
          latestScan,
          complianceCheck
        )
      };

      res.json({
        success: true,
        data: overview
      });

    } catch (error) {
      logger.error('获取安全概览失败', {
        error: error instanceof Error ? error.message : String(error)
      });

      res.status(500).json({
        success: false,
        error: 'INTERNAL_ERROR',
        message: '获取安全概览失败'
      });
    }
  };

  /**
   * 获取审计日志
   * GET /api/v1/security/audit-logs
   */
  getAuditLogs = async (req: Request, res: Response): Promise<void> => {
    try {
      const {
        page = 1,
        limit = 50,
        eventType,
        severity,
        userId,
        ipAddress,
        startDate,
        endDate
      } = req.query;

      const pageNum = parseInt(page as string);
      const limitNum = Math.min(parseInt(limit as string), 100); // 最多100条
      const offset = (pageNum - 1) * limitNum;

      // 构建查询条件
      const where: any = {};
      
      if (eventType) where.eventType = eventType;
      if (severity) where.severity = severity;
      if (userId) where.userId = userId;
      if (ipAddress) where.ipAddress = ipAddress;
      
      if (startDate || endDate) {
        where.createdAt = {};
        if (startDate) where.createdAt.gte = new Date(startDate as string);
        if (endDate) where.createdAt.lte = new Date(endDate as string);
      }

      // 查询审计日志
      const [logs, total] = await Promise.all([
        prisma.auditLog.findMany({
          where,
          orderBy: { createdAt: 'desc' },
          skip: offset,
          take: limitNum,
          select: {
            id: true,
            eventType: true,
            severity: true,
            userId: true,
            sessionId: true,
            ipAddress: true,
            userAgent: true,
            resource: true,
            action: true,
            success: true,
            errorMessage: true,
            riskScore: true,
            createdAt: true
          }
        }),
        prisma.auditLog.count({ where })
      ]);

      res.json({
        success: true,
        data: {
          logs,
          pagination: {
            page: pageNum,
            limit: limitNum,
            total,
            pages: Math.ceil(total / limitNum)
          }
        }
      });

    } catch (error) {
      logger.error('获取审计日志失败', {
        error: error instanceof Error ? error.message : String(error)
      });

      res.status(500).json({
        success: false,
        error: 'INTERNAL_ERROR',
        message: '获取审计日志失败'
      });
    }
  };

  /**
   * 获取威胁检测结果
   * GET /api/v1/security/threat-detection
   */
  getThreatDetection = async (req: Request, res: Response): Promise<void> => {
    try {
      const timeRange = this.parseTimeRange(req.query, 24); // 默认24小时
      const result = await securityAuditService.performThreatDetection(timeRange);

      res.json({
        success: true,
        data: result
      });

    } catch (error) {
      logger.error('威胁检测失败', {
        error: error instanceof Error ? error.message : String(error)
      });

      res.status(500).json({
        success: false,
        error: 'INTERNAL_ERROR',
        message: '威胁检测失败'
      });
    }
  };

  /**
   * 执行安全扫描
   * POST /api/v1/security/scan
   */
  performSecurityScan = async (req: Request, res: Response): Promise<void> => {
    try {
      const { scanType = 'full' } = req.body;

      // 记录扫描启动事件
      await securityAuditService.logAuditEvent({
        eventType: AuditEventType.ADMIN_ACTION,
        severity: AuditSeverity.MEDIUM,
        userId: req.user?.id,
        sessionId: req.sessionID,
        ipAddress: req.ip || 'unknown',
        userAgent: req.get('User-Agent'),
        resource: 'security_scan',
        action: 'scan_initiated',
        details: { scanType },
        success: true
      });

      let result;
      switch (scanType) {
        case 'full':
          result = await securityScannerService.performFullSecurityScan();
          break;
        case 'dependency':
          result = await securityScannerService.scanDependencies();
          break;
        case 'configuration':
          result = await securityScannerService.scanConfiguration();
          break;
        case 'code':
          result = await securityScannerService.scanCode();
          break;
        case 'infrastructure':
          result = await securityScannerService.scanInfrastructure();
          break;
        default:
          return res.status(400).json({
            success: false,
            error: 'INVALID_SCAN_TYPE',
            message: '无效的扫描类型'
          });
      }

      res.json({
        success: true,
        data: result
      });

    } catch (error) {
      logger.error('安全扫描失败', {
        error: error instanceof Error ? error.message : String(error),
        operator: req.user?.id
      });

      res.status(500).json({
        success: false,
        error: 'INTERNAL_ERROR',
        message: '安全扫描失败'
      });
    }
  };

  /**
   * 获取扫描历史
   * GET /api/v1/security/scan-history
   */
  getScanHistory = async (req: Request, res: Response): Promise<void> => {
    try {
      const { limit = 10 } = req.query;
      const limitNum = Math.min(parseInt(limit as string), 50);

      const history = securityScannerService.getScanHistory().slice(0, limitNum);

      res.json({
        success: true,
        data: {
          scans: history,
          total: history.length
        }
      });

    } catch (error) {
      logger.error('获取扫描历史失败', {
        error: error instanceof Error ? error.message : String(error)
      });

      res.status(500).json({
        success: false,
        error: 'INTERNAL_ERROR',
        message: '获取扫描历史失败'
      });
    }
  };

  /**
   * 获取合规性检查结果
   * GET /api/v1/security/compliance
   */
  getComplianceCheck = async (req: Request, res: Response): Promise<void> => {
    try {
      const { standard = 'GDPR' } = req.query;
      const result = await securityAuditService.performComplianceCheck(standard as string);

      res.json({
        success: true,
        data: result
      });

    } catch (error) {
      logger.error('合规性检查失败', {
        error: error instanceof Error ? error.message : String(error),
        standard: req.query.standard
      });

      res.status(500).json({
        success: false,
        error: 'INTERNAL_ERROR',
        message: '合规性检查失败'
      });
    }
  };

  /**
   * 获取安全统计
   * GET /api/v1/security/statistics
   */
  getSecurityStatistics = async (req: Request, res: Response): Promise<void> => {
    try {
      const timeRange = this.parseTimeRange(req.query, 24 * 7); // 默认7天

      const stats = await this.getDetailedSecurityStatistics(timeRange);

      res.json({
        success: true,
        data: stats
      });

    } catch (error) {
      logger.error('获取安全统计失败', {
        error: error instanceof Error ? error.message : String(error)
      });

      res.status(500).json({
        success: false,
        error: 'INTERNAL_ERROR',
        message: '获取安全统计失败'
      });
    }
  };

  /**
   * 生成安全报告
   * POST /api/v1/security/report
   */
  generateSecurityReport = async (req: Request, res: Response): Promise<void> => {
    try {
      const { 
        timeRange: customTimeRange,
        includeDetails = false,
        format = 'json'
      } = req.body;

      const timeRange = customTimeRange || this.parseTimeRange({}, 24 * 30); // 默认30天

      // 记录报告生成事件
      await securityAuditService.logAuditEvent({
        eventType: AuditEventType.ADMIN_ACTION,
        severity: AuditSeverity.LOW,
        userId: req.user?.id,
        sessionId: req.sessionID,
        ipAddress: req.ip || 'unknown',
        userAgent: req.get('User-Agent'),
        resource: 'security_report',
        action: 'report_generated',
        details: { timeRange, includeDetails, format },
        success: true
      });

      const report = await this.generateComprehensiveSecurityReport(timeRange, includeDetails);

      if (format === 'pdf') {
        // 这里可以集成PDF生成库
        res.setHeader('Content-Type', 'application/pdf');
        res.setHeader('Content-Disposition', 'attachment; filename=security-report.pdf');
        // 返回PDF内容
        res.json({
          success: true,
          message: 'PDF报告生成功能待实现',
          data: report
        });
      } else {
        res.json({
          success: true,
          data: report
        });
      }

    } catch (error) {
      logger.error('生成安全报告失败', {
        error: error instanceof Error ? error.message : String(error),
        operator: req.user?.id
      });

      res.status(500).json({
        success: false,
        error: 'INTERNAL_ERROR',
        message: '生成安全报告失败'
      });
    }
  };

  /**
   * 解析时间范围参数
   */
  private parseTimeRange(query: any, defaultHours: number = 24): { start: Date; end: Date } {
    const end = query.endDate ? new Date(query.endDate) : new Date();
    const start = query.startDate 
      ? new Date(query.startDate) 
      : new Date(end.getTime() - defaultHours * 60 * 60 * 1000);

    return { start, end };
  }

  /**
   * 获取审计统计
   */
  private async getAuditStatistics(timeRange: { start: Date; end: Date }) {
    const [
      totalEvents,
      securityEvents,
      failedAttempts,
      suspiciousActivity
    ] = await Promise.all([
      prisma.auditLog.count({
        where: { createdAt: { gte: timeRange.start, lte: timeRange.end } }
      }),
      prisma.auditLog.count({
        where: {
          createdAt: { gte: timeRange.start, lte: timeRange.end },
          eventType: { in: [
            AuditEventType.SECURITY_VIOLATION,
            AuditEventType.BRUTE_FORCE_ATTEMPT,
            AuditEventType.RATE_LIMIT_EXCEEDED
          ]}
        }
      }),
      prisma.auditLog.count({
        where: {
          createdAt: { gte: timeRange.start, lte: timeRange.end },
          success: false
        }
      }),
      prisma.auditLog.count({
        where: {
          createdAt: { gte: timeRange.start, lte: timeRange.end },
          eventType: AuditEventType.SUSPICIOUS_ACTIVITY
        }
      })
    ]);

    return {
      totalEvents,
      securityEvents,
      failedAttempts,
      suspiciousActivity
    };
  }

  /**
   * 计算安全评分
   */
  private calculateSecurityScore(
    threatDetection: any,
    latestScan: any,
    auditStats: any,
    complianceCheck: any
  ): number {
    let score = 100;

    // 威胁检测影响 (30%)
    switch (threatDetection.threatLevel) {
      case 'critical':
        score -= 30;
        break;
      case 'high':
        score -= 20;
        break;
      case 'medium':
        score -= 10;
        break;
      case 'low':
        score -= 5;
        break;
    }

    // 漏洞扫描影响 (30%)
    if (latestScan) {
      score -= latestScan.summary.critical * 5;
      score -= latestScan.summary.high * 3;
      score -= latestScan.summary.medium * 1;
    }

    // 审计活动影响 (20%)
    const securityEventRate = auditStats.totalEvents > 0 
      ? auditStats.securityEvents / auditStats.totalEvents 
      : 0;
    score -= securityEventRate * 20;

    // 合规性影响 (20%)
    score -= (100 - complianceCheck.score) * 0.2;

    return Math.max(0, Math.min(100, Math.round(score)));
  }

  /**
   * 确定安全状态
   */
  private determineSecurityStatus(score: number): string {
    if (score >= 90) return 'excellent';
    if (score >= 80) return 'good';
    if (score >= 70) return 'fair';
    if (score >= 60) return 'poor';
    return 'critical';
  }

  /**
   * 生成安全建议
   */
  private generateSecurityRecommendations(
    threatDetection: any,
    latestScan: any,
    complianceCheck: any
  ): string[] {
    const recommendations = new Set<string>();

    // 基于威胁检测的建议
    if (threatDetection.threatLevel === 'critical' || threatDetection.threatLevel === 'high') {
      recommendations.add('立即调查和处理高风险威胁');
      recommendations.add('加强实时监控和告警机制');
    }

    // 基于漏洞扫描的建议
    if (latestScan) {
      if (latestScan.summary.critical > 0) {
        recommendations.add('立即修复所有严重级别漏洞');
      }
      if (latestScan.summary.high > 0) {
        recommendations.add('优先修复高级别安全漏洞');
      }
      if (latestScan.summary.total > 10) {
        recommendations.add('建立定期漏洞扫描和修复流程');
      }
    }

    // 基于合规性的建议
    if (!complianceCheck.compliant) {
      recommendations.add(`提升${complianceCheck.standard}合规性`);
      recommendations.add('定期进行合规性审计');
    }

    // 通用建议
    recommendations.add('定期更新安全策略和程序');
    recommendations.add('加强员工安全意识培训');
    recommendations.add('实施多层次安全防护');

    return Array.from(recommendations);
  }

  /**
   * 获取详细安全统计
   */
  private async getDetailedSecurityStatistics(timeRange: { start: Date; end: Date }) {
    // 这里可以实现更详细的统计查询
    const basicStats = await this.getAuditStatistics(timeRange);
    
    return {
      ...basicStats,
      timeRange,
      // 可以添加更多统计信息
      eventsByType: await this.getEventsByType(timeRange),
      topRiskIPs: await this.getTopRiskIPs(timeRange),
      securityTrends: await this.getSecurityTrends(timeRange)
    };
  }

  /**
   * 按类型获取事件统计
   */
  private async getEventsByType(timeRange: { start: Date; end: Date }) {
    const events = await prisma.auditLog.groupBy({
      by: ['eventType'],
      where: { createdAt: { gte: timeRange.start, lte: timeRange.end } },
      _count: { eventType: true }
    });

    return events.map(event => ({
      eventType: event.eventType,
      count: event._count.eventType
    }));
  }

  /**
   * 获取高风险IP地址
   */
  private async getTopRiskIPs(timeRange: { start: Date; end: Date }) {
    const ips = await prisma.auditLog.groupBy({
      by: ['ipAddress'],
      where: {
        createdAt: { gte: timeRange.start, lte: timeRange.end },
        success: false
      },
      _count: { ipAddress: true },
      orderBy: { _count: { ipAddress: 'desc' } },
      take: 10
    });

    return ips.map(ip => ({
      ipAddress: ip.ipAddress,
      failedAttempts: ip._count.ipAddress
    }));
  }

  /**
   * 获取安全趋势
   */
  private async getSecurityTrends(timeRange: { start: Date; end: Date }) {
    // 简化实现，实际可以按天/小时分组统计
    return {
      message: '安全趋势分析功能待完善',
      timeRange
    };
  }

  /**
   * 生成综合安全报告
   */
  private async generateComprehensiveSecurityReport(
    timeRange: { start: Date; end: Date },
    includeDetails: boolean
  ) {
    const [
      threatDetection,
      latestScan,
      auditStats,
      complianceCheck
    ] = await Promise.all([
      securityAuditService.performThreatDetection(timeRange),
      securityScannerService.getLatestScanResult(),
      this.getDetailedSecurityStatistics(timeRange),
      securityAuditService.performComplianceCheck('GDPR')
    ]);

    const report = {
      reportId: `security-report-${Date.now()}`,
      generatedAt: new Date().toISOString(),
      timeRange,
      summary: {
        securityScore: this.calculateSecurityScore(threatDetection, latestScan, auditStats, complianceCheck),
        threatLevel: threatDetection.threatLevel,
        vulnerabilityCount: latestScan?.summary.total || 0,
        complianceStatus: complianceCheck.compliant
      },
      threatDetection,
      vulnerabilities: latestScan,
      auditStatistics: auditStats,
      compliance: complianceCheck,
      recommendations: this.generateSecurityRecommendations(threatDetection, latestScan, complianceCheck)
    };

    if (includeDetails) {
      // 添加详细信息
      report['detailedAuditLogs'] = await prisma.auditLog.findMany({
        where: { createdAt: { gte: timeRange.start, lte: timeRange.end } },
        orderBy: { createdAt: 'desc' },
        take: 1000 // 限制数量
      });
    }

    return report;
  }

  /**
   * 获取自动化扫描计划列表
   * GET /api/v1/security/automated-scans/schedules
   */
  getAutomatedScanSchedules = async (req: Request, res: Response): Promise<void> => {
    try {
      const schedules = automatedSecurityScannerService.getSchedules();

      res.json({
        success: true,
        data: {
          schedules,
          totalCount: schedules.length,
          activeCount: schedules.filter(s => s.enabled).length
        }
      });

    } catch (error) {
      logger.error('获取自动化扫描计划失败', {
        error: error instanceof Error ? error.message : String(error)
      });

      res.status(500).json({
        success: false,
        error: 'AUTOMATED_SCAN_SCHEDULES_ERROR',
        message: '获取自动化扫描计划失败'
      });
    }
  };

  /**
   * 获取自动化扫描计划详情
   * GET /api/v1/security/automated-scans/schedules/:scheduleId
   */
  getAutomatedScanSchedule = async (req: Request, res: Response): Promise<void> => {
    try {
      const { scheduleId } = req.params;
      const schedule = automatedSecurityScannerService.getSchedule(scheduleId);

      if (!schedule) {
        return res.status(404).json({
          success: false,
          error: 'SCHEDULE_NOT_FOUND',
          message: '扫描计划不存在'
        });
      }

      res.json({
        success: true,
        data: schedule
      });

    } catch (error) {
      logger.error('获取自动化扫描计划详情失败', {
        scheduleId: req.params.scheduleId,
        error: error instanceof Error ? error.message : String(error)
      });

      res.status(500).json({
        success: false,
        error: 'AUTOMATED_SCAN_SCHEDULE_ERROR',
        message: '获取自动化扫描计划详情失败'
      });
    }
  };

  /**
   * 启用/禁用自动化扫描计划
   * PUT /api/v1/security/automated-scans/schedules/:scheduleId/toggle
   */
  toggleAutomatedScanSchedule = async (req: Request, res: Response): Promise<void> => {
    try {
      const { scheduleId } = req.params;
      const { enabled } = req.body;

      if (typeof enabled !== 'boolean') {
        return res.status(400).json({
          success: false,
          error: 'INVALID_ENABLED_VALUE',
          message: 'enabled 参数必须是布尔值'
        });
      }

      await automatedSecurityScannerService.toggleSchedule(scheduleId, enabled);

      // 记录审计日志
      await securityAuditService.logEvent({
        eventType: AuditEventType.SECURITY_CONFIG_CHANGE,
        severity: AuditSeverity.MEDIUM,
        userId: req.user?.id || 'system',
        ipAddress: req.ip || 'unknown',
        userAgent: req.get('User-Agent') || 'unknown',
        details: {
          action: 'toggle_automated_scan_schedule',
          scheduleId,
          enabled,
          timestamp: new Date().toISOString()
        }
      });

      res.json({
        success: true,
        message: `扫描计划已${enabled ? '启用' : '禁用'}`,
        data: {
          scheduleId,
          enabled
        }
      });

    } catch (error) {
      logger.error('切换自动化扫描计划状态失败', {
        scheduleId: req.params.scheduleId,
        error: error instanceof Error ? error.message : String(error)
      });

      res.status(500).json({
        success: false,
        error: 'TOGGLE_SCHEDULE_ERROR',
        message: '切换扫描计划状态失败'
      });
    }
  };

  /**
   * 手动触发自动化扫描
   * POST /api/v1/security/automated-scans/trigger
   */
  triggerAutomatedScan = async (req: Request, res: Response): Promise<void> => {
    try {
      const { scanType = 'full' } = req.body;

      // 验证扫描类型
      const validScanTypes = ['full', 'dependency', 'configuration', 'code', 'infrastructure'];
      if (!validScanTypes.includes(scanType)) {
        return res.status(400).json({
          success: false,
          error: 'INVALID_SCAN_TYPE',
          message: '无效的扫描类型'
        });
      }

      // 执行扫描
      let result;
      switch (scanType) {
        case 'full':
          result = await securityScannerService.performFullSecurityScan();
          break;
        case 'dependency':
          result = await securityScannerService.scanDependencies();
          break;
        case 'configuration':
          result = await securityScannerService.scanConfiguration();
          break;
        case 'code':
          result = await securityScannerService.scanCode();
          break;
        case 'infrastructure':
          result = await securityScannerService.scanInfrastructure();
          break;
      }

      // 记录审计日志
      await securityAuditService.logEvent({
        eventType: AuditEventType.SECURITY_SCAN,
        severity: AuditSeverity.LOW,
        userId: req.user?.id || 'system',
        ipAddress: req.ip || 'unknown',
        userAgent: req.get('User-Agent') || 'unknown',
        details: {
          action: 'manual_security_scan',
          scanType,
          scanId: result.scanId,
          vulnerabilitiesFound: result.vulnerabilities?.length || 0,
          timestamp: new Date().toISOString()
        }
      });

      res.json({
        success: true,
        message: '安全扫描已完成',
        data: result
      });

    } catch (error) {
      logger.error('手动触发安全扫描失败', {
        scanType: req.body.scanType,
        error: error instanceof Error ? error.message : String(error)
      });

      res.status(500).json({
        success: false,
        error: 'MANUAL_SCAN_ERROR',
        message: '手动触发安全扫描失败'
      });
    }
  };
}

// 创建控制器实例
export const securityController = new SecurityController();
