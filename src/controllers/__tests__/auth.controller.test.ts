/**
 * 认证控制器测试
 */

import { Request, Response } from 'express';
import { authController } from '../auth.controller';
import { authService } from '@/services/auth.service';
import { validateLoginRequest, validateRegisterRequest } from '@/utils/validation';
import { logger } from '@/config/logger';

// Mock依赖
jest.mock('@/services/auth.service');
jest.mock('@/utils/validation');
jest.mock('@/config/logger');

const mockAuthService = authService as jest.Mocked<typeof authService>;
const mockValidateLoginRequest = validateLoginRequest as jest.MockedFunction<typeof validateLoginRequest>;
const mockValidateRegisterRequest = validateRegisterRequest as jest.MockedFunction<typeof validateRegisterRequest>;

describe('AuthController', () => {
  let mockRequest: Partial<Request>;
  let mockResponse: Partial<Response>;

  beforeEach(() => {
    jest.clearAllMocks();

    mockRequest = {
      body: {},
      ip: '127.0.0.1',
      get: jest.fn().mockReturnValue('test-user-agent')
    };

    mockResponse = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn().mockReturnThis(),
      cookie: jest.fn().mockReturnThis()
    };
  });

  describe('login', () => {
    test('应该成功登录有效用户', async () => {
      const loginData = {
        username: '<EMAIL>',
        password: 'password123'
      };

      const loginResult = {
        success: true,
        user: {
          id: 'user-123',
          email: '<EMAIL>',
          nickname: 'Test User'
        },
        tokens: {
          accessToken: 'access-token',
          refreshToken: 'refresh-token',
          expiresIn: 900,
          tokenType: 'Bearer'
        },
        session: {
          id: 'session-123',
          deviceInfo: { browser: 'Chrome' }
        }
      };

      mockRequest.body = loginData;
      mockValidateLoginRequest.mockReturnValue({ success: true, errors: [] });
      mockAuthService.login.mockResolvedValue(loginResult);

      await authController.login(mockRequest as Request, mockResponse as Response);

      expect(mockValidateLoginRequest).toHaveBeenCalledWith(loginData);
      expect(mockAuthService.login).toHaveBeenCalledWith({
        ...loginData,
        ipAddress: '127.0.0.1',
        userAgent: 'test-user-agent'
      });
      expect(mockResponse.cookie).toHaveBeenCalledWith('refreshToken', 'refresh-token', {
        httpOnly: true,
        secure: false,
        sameSite: 'strict',
        maxAge: 7 * 24 * 60 * 60 * 1000
      });
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        data: {
          user: loginResult.user,
          accessToken: loginResult.tokens.accessToken,
          expiresIn: loginResult.tokens.expiresIn,
          tokenType: loginResult.tokens.tokenType,
          session: loginResult.session
        }
      });
    });

    test('应该拒绝无效的登录数据', async () => {
      const loginData = {
        username: '',
        password: 'short'
      };

      mockRequest.body = loginData;
      mockValidateLoginRequest.mockReturnValue({
        success: false,
        errors: ['用户名不能为空', '密码长度不能少于6位']
      });

      await authController.login(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: 'VALIDATION_ERROR',
        message: '请求数据验证失败',
        details: ['用户名不能为空', '密码长度不能少于6位']
      });
      expect(mockAuthService.login).not.toHaveBeenCalled();
    });

    test('应该处理登录失败', async () => {
      const loginData = {
        username: '<EMAIL>',
        password: 'wrongpassword'
      };

      mockRequest.body = loginData;
      mockValidateLoginRequest.mockReturnValue({ success: true, errors: [] });
      mockAuthService.login.mockResolvedValue({
        success: false,
        error: 'INVALID_CREDENTIALS',
        message: '用户名或密码错误'
      });

      await authController.login(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(401);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: 'INVALID_CREDENTIALS',
        message: '用户名或密码错误'
      });
    });

    test('应该处理服务异常', async () => {
      const loginData = {
        username: '<EMAIL>',
        password: 'password123'
      };

      mockRequest.body = loginData;
      mockValidateLoginRequest.mockReturnValue({ success: true, errors: [] });
      mockAuthService.login.mockRejectedValue(new Error('Database connection failed'));

      await authController.login(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(500);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: 'INTERNAL_ERROR',
        message: '登录服务暂时不可用'
      });
      expect(logger.error).toHaveBeenCalledWith('登录失败', {
        error: 'Database connection failed',
        username: '<EMAIL>',
        ip: '127.0.0.1'
      });
    });
  });

  describe('register', () => {
    test('应该成功注册新用户', async () => {
      const registerData = {
        email: '<EMAIL>',
        password: 'password123',
        nickname: 'New User'
      };

      const registerResult = {
        success: true,
        user: {
          id: 'user-456',
          email: '<EMAIL>',
          nickname: 'New User'
        },
        tokens: {
          accessToken: 'access-token',
          refreshToken: 'refresh-token',
          expiresIn: 900,
          tokenType: 'Bearer'
        }
      };

      mockRequest.body = registerData;
      mockValidateRegisterRequest.mockReturnValue({ success: true, errors: [] });
      mockAuthService.register.mockResolvedValue(registerResult);

      await authController.register(mockRequest as Request, mockResponse as Response);

      expect(mockValidateRegisterRequest).toHaveBeenCalledWith(registerData);
      expect(mockAuthService.register).toHaveBeenCalledWith({
        ...registerData,
        ipAddress: '127.0.0.1',
        userAgent: 'test-user-agent'
      });
      expect(mockResponse.status).toHaveBeenCalledWith(201);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        data: {
          user: registerResult.user,
          accessToken: registerResult.tokens.accessToken,
          expiresIn: registerResult.tokens.expiresIn,
          tokenType: registerResult.tokens.tokenType
        }
      });
    });

    test('应该拒绝无效的注册数据', async () => {
      const registerData = {
        email: 'invalid-email',
        password: 'short',
        nickname: ''
      };

      mockRequest.body = registerData;
      mockValidateRegisterRequest.mockReturnValue({
        success: false,
        errors: ['邮箱格式不正确', '密码长度不能少于6位', '昵称不能为空']
      });

      await authController.register(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: 'VALIDATION_ERROR',
        message: '请求数据验证失败',
        details: ['邮箱格式不正确', '密码长度不能少于6位', '昵称不能为空']
      });
    });

    test('应该处理邮箱已存在的情况', async () => {
      const registerData = {
        email: '<EMAIL>',
        password: 'password123',
        nickname: 'Test User'
      };

      mockRequest.body = registerData;
      mockValidateRegisterRequest.mockReturnValue({ success: true, errors: [] });
      mockAuthService.register.mockResolvedValue({
        success: false,
        error: 'EMAIL_EXISTS',
        message: '邮箱已被注册'
      });

      await authController.register(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(409);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: 'EMAIL_EXISTS',
        message: '邮箱已被注册'
      });
    });
  });

  describe('logout', () => {
    test('应该成功登出用户', async () => {
      mockRequest.user = {
        id: 'user-123',
        email: '<EMAIL>'
      };

      mockAuthService.logout.mockResolvedValue({
        success: true,
        message: '登出成功'
      });

      await authController.logout(mockRequest as Request, mockResponse as Response);

      expect(mockAuthService.logout).toHaveBeenCalledWith('user-123', '127.0.0.1');
      expect(mockResponse.cookie).toHaveBeenCalledWith('refreshToken', '', {
        httpOnly: true,
        secure: false,
        sameSite: 'strict',
        maxAge: 0
      });
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        data: {
          message: '登出成功'
        }
      });
    });

    test('应该处理未认证用户的登出请求', async () => {
      mockRequest.user = undefined;

      await authController.logout(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(401);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: 'UNAUTHORIZED',
        message: '用户未认证'
      });
    });
  });

  describe('refreshToken', () => {
    test('应该成功刷新令牌', async () => {
      mockRequest.cookies = {
        refreshToken: 'valid-refresh-token'
      };

      const refreshResult = {
        success: true,
        tokens: {
          accessToken: 'new-access-token',
          refreshToken: 'new-refresh-token',
          expiresIn: 900,
          tokenType: 'Bearer'
        }
      };

      mockAuthService.refreshToken.mockResolvedValue(refreshResult);

      await authController.refreshToken(mockRequest as Request, mockResponse as Response);

      expect(mockAuthService.refreshToken).toHaveBeenCalledWith('valid-refresh-token');
      expect(mockResponse.cookie).toHaveBeenCalledWith('refreshToken', 'new-refresh-token', {
        httpOnly: true,
        secure: false,
        sameSite: 'strict',
        maxAge: 7 * 24 * 60 * 60 * 1000
      });
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        data: {
          accessToken: refreshResult.tokens.accessToken,
          expiresIn: refreshResult.tokens.expiresIn,
          tokenType: refreshResult.tokens.tokenType
        }
      });
    });

    test('应该拒绝缺失的刷新令牌', async () => {
      mockRequest.cookies = {};

      await authController.refreshToken(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(401);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: 'UNAUTHORIZED',
        message: '缺少刷新令牌'
      });
    });

    test('应该处理无效的刷新令牌', async () => {
      mockRequest.cookies = {
        refreshToken: 'invalid-refresh-token'
      };

      mockAuthService.refreshToken.mockResolvedValue({
        success: false,
        error: 'INVALID_TOKEN',
        message: '无效的刷新令牌'
      });

      await authController.refreshToken(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(401);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: 'INVALID_TOKEN',
        message: '无效的刷新令牌'
      });
    });
  });

  describe('getProfile', () => {
    test('应该返回用户资料', async () => {
      mockRequest.user = {
        id: 'user-123',
        email: '<EMAIL>'
      };

      const userProfile = {
        id: 'user-123',
        email: '<EMAIL>',
        nickname: 'Test User',
        avatar: 'https://example.com/avatar.jpg',
        createdAt: new Date(),
        lastLoginAt: new Date()
      };

      mockAuthService.getUserProfile.mockResolvedValue(userProfile);

      await authController.getProfile(mockRequest as Request, mockResponse as Response);

      expect(mockAuthService.getUserProfile).toHaveBeenCalledWith('user-123');
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        data: userProfile
      });
    });

    test('应该处理未认证用户', async () => {
      mockRequest.user = undefined;

      await authController.getProfile(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(401);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: 'UNAUTHORIZED',
        message: '用户未认证'
      });
    });
  });

  describe('updateProfile', () => {
    test('应该成功更新用户资料', async () => {
      mockRequest.user = {
        id: 'user-123',
        email: '<EMAIL>'
      };

      mockRequest.body = {
        nickname: 'Updated Name',
        avatar: 'https://example.com/new-avatar.jpg'
      };

      const updatedProfile = {
        id: 'user-123',
        email: '<EMAIL>',
        nickname: 'Updated Name',
        avatar: 'https://example.com/new-avatar.jpg'
      };

      mockAuthService.updateUserProfile.mockResolvedValue(updatedProfile);

      await authController.updateProfile(mockRequest as Request, mockResponse as Response);

      expect(mockAuthService.updateUserProfile).toHaveBeenCalledWith('user-123', {
        nickname: 'Updated Name',
        avatar: 'https://example.com/new-avatar.jpg'
      });
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        data: updatedProfile
      });
    });

    test('应该处理未认证用户', async () => {
      mockRequest.user = undefined;

      await authController.updateProfile(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(401);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: 'UNAUTHORIZED',
        message: '用户未认证'
      });
    });
  });

  describe('Cookie设置', () => {
    test('应该在生产环境中设置secure cookie', async () => {
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = 'production';

      const loginData = {
        username: '<EMAIL>',
        password: 'password123'
      };

      const loginResult = {
        success: true,
        user: { id: 'user-123', email: '<EMAIL>' },
        tokens: {
          accessToken: 'access-token',
          refreshToken: 'refresh-token',
          expiresIn: 900,
          tokenType: 'Bearer'
        },
        session: { id: 'session-123' }
      };

      mockRequest.body = loginData;
      mockValidateLoginRequest.mockReturnValue({ success: true, errors: [] });
      mockAuthService.login.mockResolvedValue(loginResult);

      await authController.login(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.cookie).toHaveBeenCalledWith('refreshToken', 'refresh-token', {
        httpOnly: true,
        secure: true,
        sameSite: 'strict',
        maxAge: 7 * 24 * 60 * 60 * 1000
      });

      process.env.NODE_ENV = originalEnv;
    });
  });
});
