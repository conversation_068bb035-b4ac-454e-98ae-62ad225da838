/**
 * 缓存管理控制器
 * 提供缓存管理和监控的API接口
 */

import { Request, Response } from 'express';
import { cacheManager } from '@/services/cache-manager.service';
import { sessionManager } from '@/services/session-manager.service';
import { jwtBlacklistService } from '@/services/jwt-blacklist.service';
import { redisHealthService } from '@/services/redis-health.service';
import { redisService } from '@/services/redis.service';
import { logger } from '@/config/logger';
import { validateRequest } from '@/utils/validation';
import { z } from 'zod';

/**
 * 缓存操作请求模式
 */
const cacheOperationSchema = z.object({
  key: z.string().min(1, '缓存键不能为空'),
  value: z.any().optional(),
  ttl: z.number().positive().optional(),
  namespace: z.string().optional()
});

/**
 * 批量缓存操作请求模式
 */
const batchCacheSchema = z.object({
  operations: z.array(z.object({
    key: z.string().min(1),
    value: z.any().optional(),
    ttl: z.number().positive().optional()
  }))
});

/**
 * 缓存管理控制器
 */
export class CacheController {
  
  /**
   * 获取缓存统计信息
   * GET /api/v1/cache/stats
   */
  getCacheStats = async (req: Request, res: Response): Promise<void> => {
    try {
      const { namespace } = req.query;
      
      let stats;
      if (namespace && typeof namespace === 'string') {
        stats = cacheManager.getStats(namespace);
      } else {
        stats = cacheManager.getAllStats();
      }

      res.json({
        success: true,
        data: {
          stats,
          timestamp: new Date().toISOString()
        }
      });

    } catch (error) {
      logger.error('获取缓存统计失败', { error: error.message });
      res.status(500).json({
        success: false,
        error: 'INTERNAL_ERROR',
        message: '获取缓存统计失败'
      });
    }
  };

  /**
   * 获取Redis健康状态
   * GET /api/v1/cache/health
   */
  getRedisHealth = async (req: Request, res: Response): Promise<void> => {
    try {
      const currentHealth = redisHealthService.getCurrentHealth();
      const healthTrend = redisHealthService.getHealthTrend(30); // 最近30分钟

      res.json({
        success: true,
        data: {
          current: currentHealth,
          trend: healthTrend,
          isConnected: redisService.isReady()
        }
      });

    } catch (error) {
      logger.error('获取Redis健康状态失败', { error: error.message });
      res.status(500).json({
        success: false,
        error: 'INTERNAL_ERROR',
        message: '获取Redis健康状态失败'
      });
    }
  };

  /**
   * 获取会话统计信息
   * GET /api/v1/cache/sessions/stats
   */
  getSessionStats = async (req: Request, res: Response): Promise<void> => {
    try {
      const stats = await sessionManager.getSessionStats();

      res.json({
        success: true,
        data: stats
      });

    } catch (error) {
      logger.error('获取会话统计失败', { error: error.message });
      res.status(500).json({
        success: false,
        error: 'INTERNAL_ERROR',
        message: '获取会话统计失败'
      });
    }
  };

  /**
   * 获取JWT黑名单统计
   * GET /api/v1/cache/blacklist/stats
   */
  getBlacklistStats = async (req: Request, res: Response): Promise<void> => {
    try {
      const stats = await jwtBlacklistService.getBlacklistStats();

      res.json({
        success: true,
        data: stats
      });

    } catch (error) {
      logger.error('获取黑名单统计失败', { error: error.message });
      res.status(500).json({
        success: false,
        error: 'INTERNAL_ERROR',
        message: '获取黑名单统计失败'
      });
    }
  };

  /**
   * 获取缓存值
   * GET /api/v1/cache/get/:key
   */
  getCacheValue = async (req: Request, res: Response): Promise<void> => {
    try {
      const { key } = req.params;
      const { namespace } = req.query;

      const value = await cacheManager.get(key, {
        namespace: namespace as string
      });

      if (value !== null) {
        res.json({
          success: true,
          data: {
            key,
            value,
            found: true
          }
        });
      } else {
        res.json({
          success: true,
          data: {
            key,
            value: null,
            found: false
          }
        });
      }

    } catch (error) {
      logger.error('获取缓存值失败', { key: req.params.key, error: error.message });
      res.status(500).json({
        success: false,
        error: 'INTERNAL_ERROR',
        message: '获取缓存值失败'
      });
    }
  };

  /**
   * 设置缓存值
   * POST /api/v1/cache/set
   */
  setCacheValue = async (req: Request, res: Response): Promise<void> => {
    try {
      const validation = validateRequest(cacheOperationSchema, req.body);
      if (!validation.success) {
        res.status(400).json({
          success: false,
          error: 'VALIDATION_ERROR',
          message: '请求参数验证失败',
          details: validation.errors
        });
        return;
      }

      const { key, value, ttl, namespace } = validation.data;

      const success = await cacheManager.set(key, value, {
        ttl,
        namespace
      });

      res.json({
        success,
        data: {
          key,
          cached: success,
          message: success ? '缓存设置成功' : '缓存设置失败'
        }
      });

    } catch (error) {
      logger.error('设置缓存值失败', { error: error.message });
      res.status(500).json({
        success: false,
        error: 'INTERNAL_ERROR',
        message: '设置缓存值失败'
      });
    }
  };

  /**
   * 删除缓存值
   * DELETE /api/v1/cache/delete/:key
   */
  deleteCacheValue = async (req: Request, res: Response): Promise<void> => {
    try {
      const { key } = req.params;
      
      const deletedCount = await redisService.del(key);

      res.json({
        success: true,
        data: {
          key,
          deleted: deletedCount > 0,
          deletedCount
        }
      });

    } catch (error) {
      logger.error('删除缓存值失败', { key: req.params.key, error: error.message });
      res.status(500).json({
        success: false,
        error: 'INTERNAL_ERROR',
        message: '删除缓存值失败'
      });
    }
  };

  /**
   * 批量获取缓存值
   * POST /api/v1/cache/mget
   */
  batchGetCache = async (req: Request, res: Response): Promise<void> => {
    try {
      const { keys } = req.body;

      if (!Array.isArray(keys) || keys.length === 0) {
        res.status(400).json({
          success: false,
          error: 'INVALID_REQUEST',
          message: '键列表不能为空'
        });
        return;
      }

      const values = await cacheManager.mget(keys);

      const result = keys.map((key, index) => ({
        key,
        value: values[index],
        found: values[index] !== null
      }));

      res.json({
        success: true,
        data: {
          results: result,
          totalKeys: keys.length,
          foundKeys: result.filter(r => r.found).length
        }
      });

    } catch (error) {
      logger.error('批量获取缓存失败', { error: error.message });
      res.status(500).json({
        success: false,
        error: 'INTERNAL_ERROR',
        message: '批量获取缓存失败'
      });
    }
  };

  /**
   * 批量设置缓存值
   * POST /api/v1/cache/mset
   */
  batchSetCache = async (req: Request, res: Response): Promise<void> => {
    try {
      const validation = validateRequest(batchCacheSchema, req.body);
      if (!validation.success) {
        res.status(400).json({
          success: false,
          error: 'VALIDATION_ERROR',
          message: '请求参数验证失败',
          details: validation.errors
        });
        return;
      }

      const { operations } = validation.data;

      const success = await cacheManager.mset(operations);

      res.json({
        success,
        data: {
          operationCount: operations.length,
          allSuccessful: success,
          message: success ? '批量缓存设置成功' : '批量缓存设置失败'
        }
      });

    } catch (error) {
      logger.error('批量设置缓存失败', { error: error.message });
      res.status(500).json({
        success: false,
        error: 'INTERNAL_ERROR',
        message: '批量设置缓存失败'
      });
    }
  };

  /**
   * 清理过期缓存
   * POST /api/v1/cache/cleanup
   */
  cleanupCache = async (req: Request, res: Response): Promise<void> => {
    try {
      // 清理过期会话
      const expiredSessions = await sessionManager.cleanupExpiredSessions();
      
      // 清理过期黑名单条目
      const expiredBlacklist = await jwtBlacklistService.cleanupExpiredEntries();

      res.json({
        success: true,
        data: {
          expiredSessions,
          expiredBlacklist,
          message: '缓存清理完成'
        }
      });

    } catch (error) {
      logger.error('缓存清理失败', { error: error.message });
      res.status(500).json({
        success: false,
        error: 'INTERNAL_ERROR',
        message: '缓存清理失败'
      });
    }
  };

  /**
   * 重置缓存统计
   * POST /api/v1/cache/stats/reset
   */
  resetCacheStats = async (req: Request, res: Response): Promise<void> => {
    try {
      const { namespace } = req.body;

      cacheManager.resetStats(namespace);
      redisHealthService.resetCounters();

      res.json({
        success: true,
        data: {
          message: '缓存统计已重置',
          namespace: namespace || 'all'
        }
      });

    } catch (error) {
      logger.error('重置缓存统计失败', { error: error.message });
      res.status(500).json({
        success: false,
        error: 'INTERNAL_ERROR',
        message: '重置缓存统计失败'
      });
    }
  };

  /**
   * 执行Redis命令（仅限管理员）
   * POST /api/v1/cache/command
   */
  executeRedisCommand = async (req: Request, res: Response): Promise<void> => {
    try {
      const { command, args = [] } = req.body;

      if (!command) {
        res.status(400).json({
          success: false,
          error: 'INVALID_REQUEST',
          message: '命令不能为空'
        });
        return;
      }

      // 安全检查：只允许安全的命令
      const allowedCommands = ['INFO', 'PING', 'DBSIZE', 'MEMORY', 'CLIENT'];
      if (!allowedCommands.some(allowed => command.toUpperCase().startsWith(allowed))) {
        res.status(403).json({
          success: false,
          error: 'FORBIDDEN_COMMAND',
          message: '不允许执行此命令'
        });
        return;
      }

      const client = redisService.getClient();
      const result = await client.call(command, ...args);

      res.json({
        success: true,
        data: {
          command,
          args,
          result
        }
      });

    } catch (error) {
      logger.error('执行Redis命令失败', { 
        command: req.body.command,
        error: error.message 
      });
      res.status(500).json({
        success: false,
        error: 'COMMAND_EXECUTION_FAILED',
        message: error.message
      });
    }
  };
}

// 创建控制器实例
export const cacheController = new CacheController();
