/**
 * OpenID Connect Provider控制器
 * 处理OAuth 2.0和OpenID Connect相关的HTTP请求
 */

import { Request, Response } from 'express';
import { oidcService } from '@/services/oidc.service';
import { oidcClientService } from '@/services/oidc-client.service';
import { logger, logSecurityEvent } from '@/config/logger';
import { cacheService } from '@/services/cache.service';
import { validateAuthorizationRequest, validateTokenRequest } from '@/utils/oidc-validation';

export class OIDCController {

  /**
   * OAuth 2.0 授权端点
   * GET /oauth2/authorize
   */
  authorize = async (req: Request, res: Response): Promise<void> => {
    try {
      const {
        response_type,
        client_id,
        redirect_uri,
        scope,
        state,
        nonce,
        code_challenge,
        code_challenge_method,
        prompt,
        max_age
      } = req.query as Record<string, string>;

      // 验证必需参数
      const validation = validateAuthorizationRequest({
        response_type,
        client_id,
        redirect_uri,
        scope
      });

      if (!validation.isValid) {
        res.status(400).json({
          error: 'invalid_request',
          error_description: validation.errors.join(', ')
        });
        return;
      }

      // 检查用户是否已登录
      const user = (req as any).user;
      if (!user) {
        // 重定向到登录页面，携带授权请求参数
        const loginUrl = `/login?${new URLSearchParams({
          response_type,
          client_id,
          redirect_uri,
          scope,
          ...(state && { state }),
          ...(nonce && { nonce }),
          ...(code_challenge && { code_challenge }),
          ...(code_challenge_method && { code_challenge_method })
        }).toString()}`;

        res.redirect(loginUrl);
        return;
      }

      // 处理授权请求
      const result = await oidcService.handleAuthorizationRequest({
        response_type,
        client_id,
        redirect_uri,
        scope,
        state,
        nonce,
        code_challenge,
        code_challenge_method,
        prompt,
        max_age: max_age ? parseInt(max_age) : undefined
      });

      // 检查是否需要用户同意
      if (prompt === 'consent' || this.requiresConsent(scope)) {
        // 重定向到同意页面
        const consentUrl = `/consent?${new URLSearchParams({
          response_type,
          client_id,
          redirect_uri,
          scope,
          state: result.state
        }).toString()}`;

        res.redirect(consentUrl);
        return;
      }

      // 根据响应类型处理不同的流程
      if (response_type === 'code') {
        // 授权码流程
        const code = await oidcService.generateAuthorizationCode(
          user.userId,
          client_id,
          redirect_uri,
          scope.split(' '),
          code_challenge,
          code_challenge_method
        );

        // 构建回调URL
        const callbackUrl = new URL(redirect_uri);
        callbackUrl.searchParams.set('code', code);
        if (state) {
          callbackUrl.searchParams.set('state', state);
        }

        logger.info('OIDC授权码流程成功', {
          userId: user.userId,
          clientId: client_id,
          scopes: scope.split(' ')
        });

        res.redirect(callbackUrl.toString());
      } else if (['token', 'id_token', 'id_token token'].includes(response_type)) {
        // 隐式流程
        const callbackUrl = await oidcService.handleImplicitFlow({
          response_type,
          client_id,
          redirect_uri,
          scope,
          state,
          nonce
        }, user.userId);

        logger.info('OIDC隐式流程成功', {
          userId: user.userId,
          clientId: client_id,
          responseType: response_type,
          scopes: scope.split(' ')
        });

        res.redirect(callbackUrl);
      } else {
        res.status(400).json({
          error: 'unsupported_response_type',
          error_description: `不支持的响应类型: ${response_type}`
        });
        return;
      }

    } catch (error) {
      logger.error('OIDC授权失败', {
        error: error instanceof Error ? error.message : String(error),
        query: req.query
      });

      const { redirect_uri, state } = req.query as Record<string, string>;
      
      if (redirect_uri) {
        const errorUrl = new URL(redirect_uri);
        errorUrl.searchParams.set('error', 'server_error');
        errorUrl.searchParams.set('error_description', '授权服务器内部错误');
        if (state) {
          errorUrl.searchParams.set('state', state);
        }
        res.redirect(errorUrl.toString());
      } else {
        res.status(500).json({
          error: 'server_error',
          error_description: '授权服务器内部错误'
        });
      }
    }
  };

  /**
   * OAuth 2.0 令牌端点
   * POST /oauth2/token
   */
  token = async (req: Request, res: Response): Promise<void> => {
    try {
      const {
        grant_type,
        code,
        redirect_uri,
        client_id,
        client_secret,
        code_verifier,
        refresh_token,
        scope
      } = req.body;

      // 验证请求
      const validation = validateTokenRequest({
        grant_type,
        ...(grant_type === 'authorization_code' && { code, redirect_uri }),
        ...(grant_type === 'refresh_token' && { refresh_token }),
        client_id
      });

      if (!validation.isValid) {
        res.status(400).json({
          error: 'invalid_request',
          error_description: validation.errors.join(', ')
        });
        return;
      }

      let tokenResponse;

      switch (grant_type) {
        case 'authorization_code':
          tokenResponse = await oidcService.exchangeCodeForTokens(
            code,
            client_id,
            client_secret,
            redirect_uri,
            code_verifier
          );
          break;

        case 'refresh_token':
          tokenResponse = await oidcService.refreshTokens(
            refresh_token,
            client_id,
            client_secret,
            scope
          );
          break;

        case 'client_credentials':
          tokenResponse = await oidcService.clientCredentialsGrant(
            client_id,
            client_secret,
            scope
          );
          break;

        default:
          res.status(400).json({
            error: 'unsupported_grant_type',
            error_description: `不支持的授权类型: ${grant_type}`
          });
          return;
      }

      // 设置缓存头
      res.set({
        'Cache-Control': 'no-store',
        'Pragma': 'no-cache'
      });

      res.status(200).json(tokenResponse);

    } catch (error) {
      logger.error('令牌请求失败', {
        error: error instanceof Error ? error.message : String(error),
        grantType: req.body.grant_type,
        clientId: req.body.client_id
      });

      if (error instanceof Error) {
        if (error.message.includes('无效的授权码')) {
          res.status(400).json({
            error: 'invalid_grant',
            error_description: '无效的授权码'
          });
        } else if (error.message.includes('客户端认证失败')) {
          res.status(401).json({
            error: 'invalid_client',
            error_description: '客户端认证失败'
          });
        } else {
          res.status(500).json({
            error: 'server_error',
            error_description: '令牌服务器内部错误'
          });
        }
      } else {
        res.status(500).json({
          error: 'server_error',
          error_description: '令牌服务器内部错误'
        });
      }
    }
  };

  /**
   * OpenID Connect 用户信息端点
   * GET /oauth2/userinfo
   */
  userinfo = async (req: Request, res: Response): Promise<void> => {
    try {
      const authHeader = req.headers.authorization;
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        res.status(401).json({
          error: 'invalid_token',
          error_description: '缺少或无效的访问令牌'
        });
        return;
      }

      const accessToken = authHeader.substring(7);
      const userInfo = await oidcService.getUserInfo(accessToken);

      res.status(200).json(userInfo);

    } catch (error) {
      logger.error('用户信息请求失败', {
        error: error instanceof Error ? error.message : String(error)
      });

      if (error instanceof Error && error.message.includes('用户不存在')) {
        res.status(401).json({
          error: 'invalid_token',
          error_description: '无效的访问令牌'
        });
      } else {
        res.status(500).json({
          error: 'server_error',
          error_description: '用户信息服务器内部错误'
        });
      }
    }
  };

  /**
   * OAuth 2.0 令牌撤销端点
   * POST /oauth2/revoke
   */
  revoke = async (req: Request, res: Response): Promise<void> => {
    try {
      const { token, client_id, client_secret } = req.body;

      if (!token || !client_id || !client_secret) {
        res.status(400).json({
          error: 'invalid_request',
          error_description: '缺少必需参数'
        });
        return;
      }

      await oidcService.revokeToken(token, client_id, client_secret);

      res.status(200).json({
        message: '令牌撤销成功'
      });

    } catch (error) {
      logger.error('令牌撤销失败', {
        error: error instanceof Error ? error.message : String(error),
        clientId: req.body.client_id
      });

      if (error instanceof Error && error.message.includes('客户端认证失败')) {
        res.status(401).json({
          error: 'invalid_client',
          error_description: '客户端认证失败'
        });
      } else {
        res.status(500).json({
          error: 'server_error',
          error_description: '撤销服务器内部错误'
        });
      }
    }
  };

  /**
   * OAuth 2.0 令牌内省端点 (RFC 7662)
   * POST /oauth2/introspect
   */
  introspect = async (req: Request, res: Response): Promise<void> => {
    try {
      const { token, token_type_hint, client_id, client_secret } = req.body;

      if (!token) {
        res.status(400).json({
          error: 'invalid_request',
          error_description: '缺少token参数'
        });
        return;
      }

      // 验证客户端（如果提供了凭据）
      if (client_id && client_secret) {
        // TODO: 验证客户端凭据
      }

      // 内省令牌
      try {
        const jwt = require('jsonwebtoken');
        const decoded = jwt.verify(token, require('@/config').config.jwt.secret) as any;

        // 检查令牌是否在黑名单中
        if (decoded.jti) {
          const isBlacklisted = await cacheService.isJWTBlacklisted(decoded.jti);
          if (isBlacklisted) {
            res.status(200).json({ active: false });
            return;
          }
        }

        res.status(200).json({
          active: true,
          scope: decoded.scopes?.join(' ') || '',
          client_id: decoded.aud,
          username: decoded.email,
          token_type: 'Bearer',
          exp: decoded.exp,
          iat: decoded.iat,
          sub: decoded.userId,
          aud: decoded.aud,
          iss: decoded.iss
        });

      } catch (jwtError) {
        res.status(200).json({ active: false });
      }

    } catch (error) {
      logger.error('令牌内省失败', {
        error: error instanceof Error ? error.message : String(error)
      });

      res.status(500).json({
        error: 'server_error',
        error_description: '内省服务器内部错误'
      });
    }
  };

  /**
   * 检查是否需要用户同意
   */
  private requiresConsent(scope: string): boolean {
    const scopes = scope.split(' ');
    
    // 如果请求敏感权限，需要用户同意
    const sensitiveScopes = ['email', 'profile', 'offline_access'];
    return scopes.some(s => sensitiveScopes.includes(s));
  }

  /**
   * 处理用户同意
   * POST /oauth2/consent
   */
  consent = async (req: Request, res: Response): Promise<void> => {
    try {
      const { state, approved_scopes, denied } = req.body;
      const user = (req as any).user;

      if (!user) {
        res.status(401).json({
          error: 'unauthorized',
          error_description: '用户未认证'
        });
        return;
      }

      // 获取授权请求信息
      const authRequest = await cacheService.getOAuthState(state);
      if (!authRequest) {
        res.status(400).json({
          error: 'invalid_request',
          error_description: '无效的状态参数'
        });
        return;
      }

      if (denied) {
        // 用户拒绝授权
        const errorUrl = new URL(authRequest.redirectUri);
        errorUrl.searchParams.set('error', 'access_denied');
        errorUrl.searchParams.set('error_description', '用户拒绝授权');
        if (authRequest.state) {
          errorUrl.searchParams.set('state', authRequest.state);
        }

        res.redirect(errorUrl.toString());
        return;
      }

      // 生成授权码
      const code = await oidcService.generateAuthorizationCode(
        user.userId,
        authRequest.clientId,
        authRequest.redirectUri,
        approved_scopes || authRequest.scopes,
        authRequest.codeChallenge,
        authRequest.codeChallengeMethod
      );

      // 构建回调URL
      const callbackUrl = new URL(authRequest.redirectUri);
      callbackUrl.searchParams.set('code', code);
      if (authRequest.state) {
        callbackUrl.searchParams.set('state', authRequest.state);
      }

      // 清理状态
      await cacheService.deleteOAuthState(state);

      res.redirect(callbackUrl.toString());

    } catch (error) {
      logger.error('用户同意处理失败', {
        error: error instanceof Error ? error.message : String(error)
      });

      res.status(500).json({
        error: 'server_error',
        error_description: '同意处理服务器内部错误'
      });
    }
  };

  /**
   * 创建OIDC客户端
   * POST /oauth2/clients
   */
  createClient = async (req: Request, res: Response): Promise<void> => {
    try {
      const client = await oidcClientService.createClient(req.body);

      res.status(201).json({
        message: '客户端创建成功',
        client
      });

    } catch (error) {
      logger.error('创建OIDC客户端失败', {
        error: error instanceof Error ? error.message : String(error)
      });

      res.status(400).json({
        error: 'client_creation_failed',
        error_description: error instanceof Error ? error.message : '客户端创建失败'
      });
    }
  };

  /**
   * 获取OIDC客户端信息
   * GET /oauth2/clients/:clientId
   */
  getClient = async (req: Request, res: Response): Promise<void> => {
    try {
      const { clientId } = req.params;
      const client = await oidcClientService.getClient(clientId);

      if (!client) {
        res.status(404).json({
          error: 'client_not_found',
          error_description: '客户端不存在'
        });
        return;
      }

      // 不返回客户端密钥
      const { clientSecret, ...clientInfo } = client;

      res.status(200).json(clientInfo);

    } catch (error) {
      logger.error('获取OIDC客户端失败', {
        error: error instanceof Error ? error.message : String(error),
        clientId: req.params.clientId
      });

      res.status(500).json({
        error: 'server_error',
        error_description: '获取客户端信息失败'
      });
    }
  };

  /**
   * 更新OIDC客户端
   * PUT /oauth2/clients/:clientId
   */
  updateClient = async (req: Request, res: Response): Promise<void> => {
    try {
      const { clientId } = req.params;
      const client = await oidcClientService.updateClient(clientId, req.body);

      // 不返回客户端密钥
      const { clientSecret, ...clientInfo } = client;

      res.status(200).json({
        message: '客户端更新成功',
        client: clientInfo
      });

    } catch (error) {
      logger.error('更新OIDC客户端失败', {
        error: error instanceof Error ? error.message : String(error),
        clientId: req.params.clientId
      });

      if (error instanceof Error && error.message.includes('不存在')) {
        res.status(404).json({
          error: 'client_not_found',
          error_description: '客户端不存在'
        });
      } else {
        res.status(400).json({
          error: 'client_update_failed',
          error_description: error instanceof Error ? error.message : '客户端更新失败'
        });
      }
    }
  };

  /**
   * 删除OIDC客户端
   * DELETE /oauth2/clients/:clientId
   */
  deleteClient = async (req: Request, res: Response): Promise<void> => {
    try {
      const { clientId } = req.params;
      await oidcClientService.deleteClient(clientId);

      res.status(204).send();

    } catch (error) {
      logger.error('删除OIDC客户端失败', {
        error: error instanceof Error ? error.message : String(error),
        clientId: req.params.clientId
      });

      if (error instanceof Error && error.message.includes('不存在')) {
        res.status(404).json({
          error: 'client_not_found',
          error_description: '客户端不存在'
        });
      } else {
        res.status(500).json({
          error: 'server_error',
          error_description: '删除客户端失败'
        });
      }
    }
  };

  /**
   * 重新生成客户端密钥
   * POST /oauth2/clients/:clientId/regenerate-secret
   */
  regenerateClientSecret = async (req: Request, res: Response): Promise<void> => {
    try {
      const { clientId } = req.params;
      const newSecret = await oidcClientService.regenerateClientSecret(clientId);

      res.status(200).json({
        message: '客户端密钥重新生成成功',
        client_secret: newSecret
      });

    } catch (error) {
      logger.error('重新生成客户端密钥失败', {
        error: error instanceof Error ? error.message : String(error),
        clientId: req.params.clientId
      });

      if (error instanceof Error && error.message.includes('不存在')) {
        res.status(404).json({
          error: 'client_not_found',
          error_description: '客户端不存在'
        });
      } else {
        res.status(500).json({
          error: 'server_error',
          error_description: '重新生成客户端密钥失败'
        });
      }
    }
  };

  /**
   * 列出OIDC客户端
   * GET /oauth2/clients
   */
  listClients = async (req: Request, res: Response): Promise<void> => {
    try {
      const {
        application_id,
        is_active,
        limit = '50',
        offset = '0'
      } = req.query as Record<string, string>;

      const result = await oidcClientService.listClients({
        applicationId: application_id,
        isActive: is_active ? is_active === 'true' : undefined,
        limit: parseInt(limit),
        offset: parseInt(offset)
      });

      // 不返回客户端密钥
      const clients = result.clients.map(({ clientSecret, ...client }) => client);

      res.status(200).json({
        clients,
        total: result.total,
        limit: parseInt(limit),
        offset: parseInt(offset)
      });

    } catch (error) {
      logger.error('列出OIDC客户端失败', {
        error: error instanceof Error ? error.message : String(error)
      });

      res.status(500).json({
        error: 'server_error',
        error_description: '列出客户端失败'
      });
    }
  };

  /**
   * OAuth 2.0 令牌撤销端点 (RFC 7009)
   * POST /oauth2/revoke
   */
  revoke = async (req: Request, res: Response): Promise<void> => {
    try {
      const { token, token_type_hint, client_id, client_secret } = req.body;

      // 验证必需参数
      if (!token) {
        res.status(400).json({
          error: 'invalid_request',
          error_description: '缺少token参数'
        });
        return;
      }

      if (!client_id || !client_secret) {
        res.status(400).json({
          error: 'invalid_client',
          error_description: '缺少客户端凭据'
        });
        return;
      }

      // 撤销令牌
      await oidcService.revokeToken(token, client_id, client_secret, token_type_hint);

      // RFC 7009: 成功撤销返回200状态码，无响应体
      res.status(200).send();

      logger.info('令牌撤销请求处理完成', {
        clientId: client_id,
        tokenTypeHint: token_type_hint
      });

    } catch (error) {
      logger.error('令牌撤销失败', {
        error: error instanceof Error ? error.message : String(error)
      });

      if (error instanceof Error && error.message.includes('无效的客户端')) {
        res.status(401).json({
          error: 'invalid_client',
          error_description: '无效的客户端凭据'
        });
      } else {
        // RFC 7009: 即使撤销失败，也应该返回成功
        res.status(200).send();
      }
    }
  };

  /**
   * 处理隐式流程授权
   * 用于支持 response_type=token 或 response_type=id_token
   */
  handleImplicitGrant = async (req: Request, res: Response): Promise<void> => {
    try {
      const {
        response_type,
        client_id,
        redirect_uri,
        scope,
        state,
        nonce
      } = req.query as Record<string, string>;

      // 检查用户是否已登录
      const user = (req as any).user;
      if (!user) {
        // 重定向到登录页面
        const loginUrl = `/login?${new URLSearchParams(req.query as Record<string, string>).toString()}`;
        res.redirect(loginUrl);
        return;
      }

      // 处理隐式流程
      const callbackUrl = await oidcService.handleImplicitFlow({
        response_type,
        client_id,
        redirect_uri,
        scope,
        state,
        nonce
      }, user.userId);

      logger.info('隐式流程授权成功', {
        userId: user.userId,
        clientId: client_id,
        responseType: response_type
      });

      res.redirect(callbackUrl);

    } catch (error) {
      logger.error('隐式流程失败', {
        error: error instanceof Error ? error.message : String(error)
      });

      // 构建错误回调URL
      const { redirect_uri, state } = req.query as Record<string, string>;
      if (redirect_uri) {
        const errorUrl = new URL(redirect_uri);
        errorUrl.hash = `error=server_error&error_description=${encodeURIComponent('授权服务器内部错误')}`;
        if (state) {
          errorUrl.hash += `&state=${state}`;
        }
        res.redirect(errorUrl.toString());
      } else {
        res.status(500).json({
          error: 'server_error',
          error_description: '授权服务器内部错误'
        });
      }
    }
  };
}
