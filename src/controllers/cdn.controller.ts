/**
 * CDN管理控制器
 * 提供CDN配置、缓存管理和统计信息的API
 */

import { Request, Response } from 'express';
import { cdnService } from '@/services/cdn.service';
import { logger } from '@/config/logger';
import { validateRequest } from '@/utils/validation';
import { z } from 'zod';

/**
 * 缓存清除请求验证模式
 */
const purgeCacheSchema = z.object({
  paths: z.array(z.string()).optional(),
  purgeAll: z.boolean().optional()
});

/**
 * 缓存预热请求验证模式
 */
const warmupCacheSchema = z.object({
  paths: z.array(z.string()).min(1, '至少需要一个资源路径')
});

/**
 * CDN控制器类
 */
export class CDNController {
  /**
   * 获取CDN配置和状态
   * GET /api/v1/cdn/status
   */
  getStatus = async (req: Request, res: Response): Promise<void> => {
    try {
      const stats = await cdnService.getStatistics();
      const isHealthy = await cdnService.healthCheck();

      res.json({
        success: true,
        data: {
          ...stats,
          healthy: isHealthy,
          timestamp: new Date().toISOString()
        }
      });

    } catch (error) {
      logger.error('获取CDN状态失败', {
        error: error instanceof Error ? error.message : String(error)
      });

      res.status(500).json({
        success: false,
        error: 'CDN_STATUS_ERROR',
        message: '获取CDN状态失败'
      });
    }
  };

  /**
   * 获取资源URL
   * GET /api/v1/cdn/asset-url
   */
  getAssetUrl = async (req: Request, res: Response): Promise<void> => {
    try {
      const { path: assetPath } = req.query;

      if (!assetPath || typeof assetPath !== 'string') {
        return res.status(400).json({
          success: false,
          error: 'INVALID_ASSET_PATH',
          message: '资源路径参数无效'
        });
      }

      const cdnUrl = cdnService.getAssetUrl(assetPath);
      const shouldUseCDN = cdnService.shouldUseCDN(assetPath);
      const cacheControl = cdnService.getCacheControl(assetPath);

      res.json({
        success: true,
        data: {
          originalPath: assetPath,
          cdnUrl,
          shouldUseCDN,
          cacheControl,
          timestamp: new Date().toISOString()
        }
      });

    } catch (error) {
      logger.error('获取资源URL失败', {
        error: error instanceof Error ? error.message : String(error)
      });

      res.status(500).json({
        success: false,
        error: 'ASSET_URL_ERROR',
        message: '获取资源URL失败'
      });
    }
  };

  /**
   * 清除CDN缓存
   * POST /api/v1/cdn/purge
   */
  purgeCache = async (req: Request, res: Response): Promise<void> => {
    try {
      const validation = validateRequest(purgeCacheSchema, req.body);
      if (!validation.success) {
        return res.status(400).json({
          success: false,
          error: 'VALIDATION_ERROR',
          message: '请求参数验证失败',
          details: validation.errors
        });
      }

      const { paths, purgeAll } = validation.data;

      if (purgeAll) {
        await cdnService.purgeCache();
        logger.info('CDN缓存全部清除', {
          operator: req.user?.username || 'unknown'
        });
      } else if (paths && paths.length > 0) {
        await cdnService.purgeCache(paths);
        logger.info('CDN缓存部分清除', {
          paths,
          operator: req.user?.username || 'unknown'
        });
      } else {
        return res.status(400).json({
          success: false,
          error: 'INVALID_PURGE_REQUEST',
          message: '必须指定要清除的路径或选择全部清除'
        });
      }

      res.json({
        success: true,
        message: '缓存清除操作已提交',
        data: {
          purgeAll: !!purgeAll,
          pathsCount: paths?.length || 0,
          timestamp: new Date().toISOString()
        }
      });

    } catch (error) {
      logger.error('清除CDN缓存失败', {
        error: error instanceof Error ? error.message : String(error)
      });

      res.status(500).json({
        success: false,
        error: 'PURGE_CACHE_ERROR',
        message: '清除CDN缓存失败'
      });
    }
  };

  /**
   * 预热CDN缓存
   * POST /api/v1/cdn/warmup
   */
  warmupCache = async (req: Request, res: Response): Promise<void> => {
    try {
      const validation = validateRequest(warmupCacheSchema, req.body);
      if (!validation.success) {
        return res.status(400).json({
          success: false,
          error: 'VALIDATION_ERROR',
          message: '请求参数验证失败',
          details: validation.errors
        });
      }

      const { paths } = validation.data;

      // 异步执行缓存预热
      cdnService.warmupCache(paths)
        .then(() => {
          logger.info('CDN缓存预热完成', {
            pathsCount: paths.length,
            operator: req.user?.username || 'unknown'
          });
        })
        .catch((error) => {
          logger.error('CDN缓存预热失败', {
            error: error instanceof Error ? error.message : String(error)
          });
        });

      res.json({
        success: true,
        message: '缓存预热操作已启动',
        data: {
          pathsCount: paths.length,
          timestamp: new Date().toISOString()
        }
      });

    } catch (error) {
      logger.error('启动CDN缓存预热失败', {
        error: error instanceof Error ? error.message : String(error)
      });

      res.status(500).json({
        success: false,
        error: 'WARMUP_CACHE_ERROR',
        message: '启动CDN缓存预热失败'
      });
    }
  };

  /**
   * 重新加载CDN配置
   * POST /api/v1/cdn/reload
   */
  reloadConfig = async (req: Request, res: Response): Promise<void> => {
    try {
      await cdnService.reloadConfig();

      logger.info('CDN配置重新加载', {
        operator: req.user?.username || 'unknown'
      });

      res.json({
        success: true,
        message: 'CDN配置已重新加载',
        data: {
          timestamp: new Date().toISOString()
        }
      });

    } catch (error) {
      logger.error('重新加载CDN配置失败', {
        error: error instanceof Error ? error.message : String(error)
      });

      res.status(500).json({
        success: false,
        error: 'RELOAD_CONFIG_ERROR',
        message: '重新加载CDN配置失败'
      });
    }
  };

  /**
   * 获取CDN使用统计
   * GET /api/v1/cdn/statistics
   */
  getStatistics = async (req: Request, res: Response): Promise<void> => {
    try {
      const { period = '24h' } = req.query;

      // 获取基础统计信息
      const basicStats = await cdnService.getStatistics();

      // TODO: 从监控系统获取详细统计数据
      const detailedStats = {
        period,
        requests: {
          total: 0,
          cdn: 0,
          local: 0,
          errors: 0
        },
        bandwidth: {
          total: 0,
          saved: 0,
          unit: 'MB'
        },
        performance: {
          avgResponseTime: 0,
          cacheHitRate: 0,
          errorRate: 0
        },
        topAssets: [],
        errorsByType: {}
      };

      res.json({
        success: true,
        data: {
          basic: basicStats,
          detailed: detailedStats,
          timestamp: new Date().toISOString()
        }
      });

    } catch (error) {
      logger.error('获取CDN统计信息失败', {
        error: error instanceof Error ? error.message : String(error)
      });

      res.status(500).json({
        success: false,
        error: 'STATISTICS_ERROR',
        message: '获取CDN统计信息失败'
      });
    }
  };

  /**
   * 健康检查
   * GET /api/v1/cdn/health
   */
  healthCheck = async (req: Request, res: Response): Promise<void> => {
    try {
      const isHealthy = await cdnService.healthCheck();
      const stats = await cdnService.getStatistics();

      const healthStatus = {
        healthy: isHealthy,
        enabled: stats.enabled,
        provider: stats.provider,
        baseUrl: stats.baseUrl,
        timestamp: new Date().toISOString()
      };

      if (isHealthy) {
        res.json({
          success: true,
          data: healthStatus
        });
      } else {
        res.status(503).json({
          success: false,
          error: 'CDN_UNHEALTHY',
          message: 'CDN服务不健康',
          data: healthStatus
        });
      }

    } catch (error) {
      logger.error('CDN健康检查失败', {
        error: error instanceof Error ? error.message : String(error)
      });

      res.status(500).json({
        success: false,
        error: 'HEALTH_CHECK_ERROR',
        message: 'CDN健康检查失败'
      });
    }
  };

  /**
   * 获取资源清单
   * GET /api/v1/cdn/manifest
   */
  getManifest = async (req: Request, res: Response): Promise<void> => {
    try {
      const stats = await cdnService.getStatistics();

      // 构建资源清单响应
      const manifest = {
        enabled: stats.enabled,
        provider: stats.provider,
        baseUrl: stats.baseUrl,
        assetTypes: stats.assetTypes,
        supportedExtensions: stats.supportedExtensions,
        manifestSize: stats.manifestSize,
        versionCacheSize: stats.versionCacheSize,
        timestamp: new Date().toISOString()
      };

      res.json({
        success: true,
        data: manifest
      });

    } catch (error) {
      logger.error('获取CDN资源清单失败', {
        error: error instanceof Error ? error.message : String(error)
      });

      res.status(500).json({
        success: false,
        error: 'MANIFEST_ERROR',
        message: '获取CDN资源清单失败'
      });
    }
  };
}

// 创建控制器实例
export const cdnController = new CDNController();
