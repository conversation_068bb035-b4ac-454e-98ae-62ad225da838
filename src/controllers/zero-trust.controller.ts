/**
 * 零信任控制器
 * 提供零信任架构管理、风险评估、设备管理和策略配置的API接口
 */

import { Request, Response } from 'express';
import { logger } from '@/config/logger';
import { riskAssessmentService } from '@/services/risk-assessment.service';
import { deviceFingerprintService } from '@/services/device-fingerprint.service';
import { adaptiveAuthService } from '@/services/adaptive-auth.service';
import { securityAuditService, AuditEventType, AuditSeverity } from '@/services/security-audit.service';

/**
 * 零信任控制器
 */
export class ZeroTrustController {

  /**
   * 获取零信任概览
   * GET /api/v1/zero-trust/overview
   */
  getZeroTrustOverview = async (req: Request, res: Response): Promise<void> => {
    try {
      const userId = req.user?.id;

      // 并行获取各种零信任数据
      const [
        riskAssessment,
        userDevices,
        authHistory,
        systemStats
      ] = await Promise.all([
        this.getCurrentRiskAssessment(req, userId),
        userId ? deviceFingerprintService.getUserDevices(userId) : [],
        userId ? adaptiveAuthService.getAuthHistory(userId, 5) : [],
        this.getZeroTrustSystemStats()
      ]);

      const overview = {
        timestamp: new Date().toISOString(),
        user: {
          id: userId,
          currentRisk: riskAssessment ? {
            score: riskAssessment.overallRiskScore,
            level: riskAssessment.riskLevel,
            factors: riskAssessment.riskFactors.length,
            confidence: riskAssessment.confidence
          } : null,
          devices: {
            total: userDevices.length,
            trusted: userDevices.filter(d => d.trustScore >= 70).length,
            verified: userDevices.filter(d => d.isVerified).length,
            blacklisted: userDevices.filter(d => d.isBlacklisted).length
          },
          recentAuth: authHistory.slice(0, 3)
        },
        system: systemStats,
        recommendations: this.generateUserRecommendations(riskAssessment, userDevices)
      };

      res.json({
        success: true,
        data: overview
      });

    } catch (error) {
      logger.error('获取零信任概览失败', {
        error: error instanceof Error ? error.message : String(error),
        userId: req.user?.id
      });

      res.status(500).json({
        success: false,
        error: 'INTERNAL_ERROR',
        message: '获取零信任概览失败'
      });
    }
  };

  /**
   * 执行风险评估
   * POST /api/v1/zero-trust/risk-assessment
   */
  performRiskAssessment = async (req: Request, res: Response): Promise<void> => {
    try {
      const { targetUserId, additionalContext } = req.body;
      const userId = targetUserId || req.user?.id;

      if (!userId) {
        return res.status(400).json({
          success: false,
          error: 'USER_ID_REQUIRED',
          message: '需要提供用户ID'
        });
      }

      // 执行风险评估
      const riskAssessment = await riskAssessmentService.assessRisk(req, userId, additionalContext);

      // 记录风险评估事件
      await securityAuditService.logAuditEvent({
        eventType: AuditEventType.ADMIN_ACTION,
        severity: AuditSeverity.LOW,
        userId: req.user?.id,
        sessionId: req.sessionID,
        ipAddress: req.ip || 'unknown',
        userAgent: req.get('User-Agent'),
        resource: 'risk_assessment',
        action: 'manual_assessment',
        details: {
          targetUserId: userId,
          riskScore: riskAssessment.overallRiskScore,
          riskLevel: riskAssessment.riskLevel,
          factorCount: riskAssessment.riskFactors.length
        },
        success: true
      });

      res.json({
        success: true,
        data: riskAssessment
      });

    } catch (error) {
      logger.error('风险评估失败', {
        error: error instanceof Error ? error.message : String(error),
        userId: req.user?.id
      });

      res.status(500).json({
        success: false,
        error: 'INTERNAL_ERROR',
        message: '风险评估失败'
      });
    }
  };

  /**
   * 获取设备列表
   * GET /api/v1/zero-trust/devices
   */
  getDevices = async (req: Request, res: Response): Promise<void> => {
    try {
      const { userId: targetUserId } = req.query;
      const userId = targetUserId as string || req.user?.id;

      if (!userId) {
        return res.status(400).json({
          success: false,
          error: 'USER_ID_REQUIRED',
          message: '需要提供用户ID'
        });
      }

      const devices = await deviceFingerprintService.getUserDevices(userId);

      // 为每个设备获取最新的信任评估
      const devicesWithTrust = await Promise.all(
        devices.map(async (device) => {
          try {
            const trustAssessment = await deviceFingerprintService.assessDeviceTrust(device, userId);
            return {
              ...device,
              currentTrustAssessment: trustAssessment
            };
          } catch (error) {
            return device;
          }
        })
      );

      res.json({
        success: true,
        data: {
          devices: devicesWithTrust,
          summary: {
            total: devices.length,
            trusted: devices.filter(d => d.trustScore >= 70).length,
            verified: devices.filter(d => d.isVerified).length,
            blacklisted: devices.filter(d => d.isBlacklisted).length
          }
        }
      });

    } catch (error) {
      logger.error('获取设备列表失败', {
        error: error instanceof Error ? error.message : String(error),
        userId: req.user?.id
      });

      res.status(500).json({
        success: false,
        error: 'INTERNAL_ERROR',
        message: '获取设备列表失败'
      });
    }
  };

  /**
   * 验证设备
   * POST /api/v1/zero-trust/devices/:deviceId/verify
   */
  verifyDevice = async (req: Request, res: Response): Promise<void> => {
    try {
      const { deviceId } = req.params;
      const { verificationMethod = 'manual' } = req.body;
      const userId = req.user?.id;

      if (!userId) {
        return res.status(401).json({
          success: false,
          error: 'UNAUTHORIZED',
          message: '需要认证'
        });
      }

      const success = await deviceFingerprintService.verifyDevice(deviceId, userId, verificationMethod);

      if (success) {
        await securityAuditService.logAuditEvent({
          eventType: AuditEventType.ADMIN_ACTION,
          severity: AuditSeverity.MEDIUM,
          userId,
          sessionId: req.sessionID,
          ipAddress: req.ip || 'unknown',
          userAgent: req.get('User-Agent'),
          resource: 'device',
          action: 'verify_device',
          details: {
            deviceId,
            verificationMethod
          },
          success: true
        });

        res.json({
          success: true,
          message: '设备验证成功'
        });
      } else {
        res.status(400).json({
          success: false,
          error: 'VERIFICATION_FAILED',
          message: '设备验证失败'
        });
      }

    } catch (error) {
      logger.error('设备验证失败', {
        error: error instanceof Error ? error.message : String(error),
        deviceId: req.params.deviceId,
        userId: req.user?.id
      });

      res.status(500).json({
        success: false,
        error: 'INTERNAL_ERROR',
        message: '设备验证失败'
      });
    }
  };

  /**
   * 将设备加入黑名单
   * POST /api/v1/zero-trust/devices/:deviceId/blacklist
   */
  blacklistDevice = async (req: Request, res: Response): Promise<void> => {
    try {
      const { deviceId } = req.params;
      const { reason } = req.body;
      const operatorId = req.user?.id;

      if (!operatorId) {
        return res.status(401).json({
          success: false,
          error: 'UNAUTHORIZED',
          message: '需要认证'
        });
      }

      if (!reason) {
        return res.status(400).json({
          success: false,
          error: 'REASON_REQUIRED',
          message: '需要提供黑名单原因'
        });
      }

      await deviceFingerprintService.blacklistDevice(deviceId, reason, operatorId);

      await securityAuditService.logAuditEvent({
        eventType: AuditEventType.ADMIN_ACTION,
        severity: AuditSeverity.HIGH,
        userId: operatorId,
        sessionId: req.sessionID,
        ipAddress: req.ip || 'unknown',
        userAgent: req.get('User-Agent'),
        resource: 'device',
        action: 'blacklist_device',
        details: {
          deviceId,
          reason
        },
        success: true
      });

      res.json({
        success: true,
        message: '设备已加入黑名单'
      });

    } catch (error) {
      logger.error('设备黑名单操作失败', {
        error: error instanceof Error ? error.message : String(error),
        deviceId: req.params.deviceId,
        userId: req.user?.id
      });

      res.status(500).json({
        success: false,
        error: 'INTERNAL_ERROR',
        message: '设备黑名单操作失败'
      });
    }
  };

  /**
   * 获取认证决策
   * POST /api/v1/zero-trust/auth-decision
   */
  getAuthDecision = async (req: Request, res: Response): Promise<void> => {
    try {
      const {
        userId: targetUserId,
        requestedResource,
        requestedAction,
        deviceFingerprint
      } = req.body;

      const userId = targetUserId || req.user?.id;

      const authContext = {
        userId,
        sessionId: req.sessionID,
        ipAddress: req.ip || 'unknown',
        userAgent: req.get('User-Agent') || 'unknown',
        deviceFingerprint,
        requestedResource,
        requestedAction
      };

      const authDecision = await adaptiveAuthService.makeAuthDecision(req, authContext);

      res.json({
        success: true,
        data: authDecision
      });

    } catch (error) {
      logger.error('获取认证决策失败', {
        error: error instanceof Error ? error.message : String(error),
        userId: req.user?.id
      });

      res.status(500).json({
        success: false,
        error: 'INTERNAL_ERROR',
        message: '获取认证决策失败'
      });
    }
  };

  /**
   * 获取零信任统计
   * GET /api/v1/zero-trust/statistics
   */
  getZeroTrustStatistics = async (req: Request, res: Response): Promise<void> => {
    try {
      const { timeRange = '24h' } = req.query;
      
      const stats = await this.calculateZeroTrustStatistics(timeRange as string);

      res.json({
        success: true,
        data: stats
      });

    } catch (error) {
      logger.error('获取零信任统计失败', {
        error: error instanceof Error ? error.message : String(error)
      });

      res.status(500).json({
        success: false,
        error: 'INTERNAL_ERROR',
        message: '获取零信任统计失败'
      });
    }
  };

  /**
   * 更新零信任策略
   * PUT /api/v1/zero-trust/policies
   */
  updateZeroTrustPolicies = async (req: Request, res: Response): Promise<void> => {
    try {
      const { policies } = req.body;
      const operatorId = req.user?.id;

      if (!operatorId) {
        return res.status(401).json({
          success: false,
          error: 'UNAUTHORIZED',
          message: '需要认证'
        });
      }

      // 验证策略格式
      if (!Array.isArray(policies)) {
        return res.status(400).json({
          success: false,
          error: 'INVALID_POLICIES',
          message: '策略格式无效'
        });
      }

      // 更新策略
      for (const policy of policies) {
        adaptiveAuthService.updateAuthPolicy(policy);
      }

      await securityAuditService.logAuditEvent({
        eventType: AuditEventType.SYSTEM_CONFIG_CHANGE,
        severity: AuditSeverity.HIGH,
        userId: operatorId,
        sessionId: req.sessionID,
        ipAddress: req.ip || 'unknown',
        userAgent: req.get('User-Agent'),
        resource: 'zero_trust_policies',
        action: 'update_policies',
        details: {
          policyCount: policies.length,
          policies: policies.map(p => ({ name: p.name, enabled: p.enabled }))
        },
        success: true
      });

      res.json({
        success: true,
        message: '零信任策略已更新'
      });

    } catch (error) {
      logger.error('更新零信任策略失败', {
        error: error instanceof Error ? error.message : String(error),
        userId: req.user?.id
      });

      res.status(500).json({
        success: false,
        error: 'INTERNAL_ERROR',
        message: '更新零信任策略失败'
      });
    }
  };

  /**
   * 获取当前风险评估
   */
  private async getCurrentRiskAssessment(req: Request, userId?: string) {
    try {
      if (!userId) return null;
      return await riskAssessmentService.assessRisk(req, userId);
    } catch (error) {
      logger.warn('获取当前风险评估失败', { error: error.message, userId });
      return null;
    }
  }

  /**
   * 获取零信任系统统计
   */
  private async getZeroTrustSystemStats() {
    try {
      // 这里应该从数据库或缓存中获取实际统计数据
      return {
        totalUsers: 1000,
        activeUsers: 750,
        totalDevices: 2500,
        trustedDevices: 1800,
        verifiedDevices: 1200,
        blacklistedDevices: 50,
        riskAssessments: {
          today: 5000,
          thisWeek: 35000,
          thisMonth: 150000
        },
        authDecisions: {
          allow: 4500,
          challenge: 400,
          deny: 100
        },
        averageRiskScore: 25.5,
        systemHealth: 'healthy'
      };
    } catch (error) {
      logger.warn('获取系统统计失败', { error: error.message });
      return null;
    }
  }

  /**
   * 生成用户建议
   */
  private generateUserRecommendations(riskAssessment: any, devices: any[]): string[] {
    const recommendations: string[] = [];

    if (riskAssessment) {
      if (riskAssessment.overallRiskScore > 60) {
        recommendations.push('当前风险评分较高，建议启用多因素认证');
      }
      
      if (riskAssessment.riskFactors.some((f: any) => f.type === 'location')) {
        recommendations.push('检测到地理位置异常，请验证当前位置');
      }
    }

    const unverifiedDevices = devices.filter(d => !d.isVerified).length;
    if (unverifiedDevices > 0) {
      recommendations.push(`您有 ${unverifiedDevices} 个未验证的设备，建议进行验证`);
    }

    const lowTrustDevices = devices.filter(d => d.trustScore < 50).length;
    if (lowTrustDevices > 0) {
      recommendations.push(`您有 ${lowTrustDevices} 个低信任度设备，建议检查安全性`);
    }

    if (recommendations.length === 0) {
      recommendations.push('您的安全状态良好，请继续保持');
    }

    return recommendations;
  }

  /**
   * 计算零信任统计
   */
  private async calculateZeroTrustStatistics(timeRange: string) {
    // 简化实现，实际应该从数据库查询
    const now = new Date();
    let since: Date;

    switch (timeRange) {
      case '1h':
        since = new Date(now.getTime() - 60 * 60 * 1000);
        break;
      case '24h':
        since = new Date(now.getTime() - 24 * 60 * 60 * 1000);
        break;
      case '7d':
        since = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case '30d':
        since = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        break;
      default:
        since = new Date(now.getTime() - 24 * 60 * 60 * 1000);
    }

    return {
      timeRange,
      period: { start: since, end: now },
      riskAssessments: {
        total: 1500,
        byLevel: {
          very_low: 600,
          low: 500,
          medium: 300,
          high: 80,
          very_high: 15,
          critical: 5
        },
        averageScore: 28.5
      },
      authDecisions: {
        total: 1200,
        allow: 900,
        challenge: 250,
        deny: 50
      },
      devices: {
        newDevices: 25,
        verifiedDevices: 18,
        blacklistedDevices: 3,
        trustScoreDistribution: {
          high: 800,
          medium: 600,
          low: 200
        }
      },
      topRiskFactors: [
        { type: 'location', count: 120, percentage: 8.0 },
        { type: 'device', count: 95, percentage: 6.3 },
        { type: 'behavior', count: 75, percentage: 5.0 },
        { type: 'network', count: 60, percentage: 4.0 },
        { type: 'time', count: 45, percentage: 3.0 }
      ]
    };
  }
}

// 创建控制器实例
export const zeroTrustController = new ZeroTrustController();
