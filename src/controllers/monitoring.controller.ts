/**
 * 监控控制器
 * 提供系统性能监控、健康检查和指标查询的API端点
 */

import { Request, Response } from 'express';
import { logger } from '@/config/logger';
import {
  getPerformanceReport,
  performanceCollector,
  DatabasePerformanceMonitor,
  MemoryMonitor,
  CachePerformanceMonitor
} from '@/middleware/performance.middleware';
import { redisService } from '@/services/redis.service';
import { prisma, checkDatabaseHealth } from '@/config/database';
import { metricsCollector, MetricType } from '@/services/metrics-collector.service';
import { logAggregator, LogLevel } from '@/services/log-aggregator.service';
import { alertManager, AlertSeverity, AlertStatus } from '@/services/alert-manager.service';
import { systemHealth } from '@/services/system-health.service';
import { validateRequest } from '@/utils/validation';
import { z } from 'zod';

/**
 * 请求验证模式
 */
const metricsQuerySchema = z.object({
  name: z.string().optional(),
  type: z.enum(['counter', 'gauge', 'histogram', 'summary', 'timer']).optional(),
  startTime: z.string().datetime().optional(),
  endTime: z.string().datetime().optional(),
  limit: z.number().positive().max(10000).optional()
});

const logQuerySchema = z.object({
  level: z.enum(['error', 'warn', 'info', 'debug', 'verbose']).optional(),
  service: z.string().optional(),
  startTime: z.string().datetime().optional(),
  endTime: z.string().datetime().optional(),
  search: z.string().optional(),
  limit: z.number().positive().max(1000).optional()
});

export class MonitoringController {

  /**
   * 系统健康检查
   * GET /api/v1/monitoring/health
   */
  health = async (req: Request, res: Response): Promise<void> => {
    try {
      const startTime = Date.now();
      
      // 检查各个组件的健康状态
      const [dbHealth, redisHealth] = await Promise.all([
        this.checkDatabaseHealth(),
        this.checkRedisHealth()
      ]);

      const memoryUsage = process.memoryUsage();
      const uptime = process.uptime();
      const responseTime = Date.now() - startTime;

      const health = {
        status: dbHealth.healthy && redisHealth.healthy ? 'healthy' : 'unhealthy',
        timestamp: new Date().toISOString(),
        uptime: `${Math.floor(uptime / 60)} minutes`,
        responseTime: `${responseTime}ms`,
        components: {
          database: dbHealth,
          redis: redisHealth,
          memory: {
            healthy: memoryUsage.heapUsed < 1024 * 1024 * 1024, // 1GB阈值
            heapUsed: `${(memoryUsage.heapUsed / 1024 / 1024).toFixed(2)} MB`,
            heapTotal: `${(memoryUsage.heapTotal / 1024 / 1024).toFixed(2)} MB`,
            rss: `${(memoryUsage.rss / 1024 / 1024).toFixed(2)} MB`
          }
        },
        version: process.env.npm_package_version || '1.0.0',
        environment: process.env.NODE_ENV || 'development'
      };

      const statusCode = health.status === 'healthy' ? 200 : 503;
      res.status(statusCode).json(health);

    } catch (error) {
      logger.error('健康检查失败', {
        error: error instanceof Error ? error.message : String(error)
      });

      res.status(503).json({
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        error: '健康检查执行失败'
      });
    }
  };

  /**
   * 性能指标报告
   * GET /api/v1/monitoring/metrics
   */
  metrics = async (req: Request, res: Response): Promise<void> => {
    try {
      const { timeWindow } = req.query;
      const windowMs = timeWindow ? parseInt(timeWindow as string) * 1000 : 5 * 60 * 1000;

      const report = getPerformanceReport();
      
      // 添加Redis性能指标
      const redisInfo = await this.getRedisMetrics();
      report.redis = redisInfo;

      // 添加请求统计
      const requestStats = this.getRequestStats(windowMs);
      report.requests = requestStats;

      res.status(200).json({
        timestamp: new Date().toISOString(),
        timeWindow: `${windowMs / 1000}s`,
        ...report
      });

    } catch (error) {
      logger.error('获取性能指标失败', {
        error: error instanceof Error ? error.message : String(error)
      });

      res.status(500).json({
        error: 'metrics_fetch_failed',
        message: '无法获取性能指标'
      });
    }
  };

  /**
   * 数据库性能指标
   * GET /api/v1/monitoring/database
   */
  databaseMetrics = async (req: Request, res: Response): Promise<void> => {
    try {
      const dbMonitor = DatabasePerformanceMonitor.getInstance();
      const stats = dbMonitor.getStats();

      // 获取数据库连接池信息
      const poolInfo = await this.getDatabasePoolInfo();

      res.status(200).json({
        timestamp: new Date().toISOString(),
        database: {
          ...stats,
          pool: poolInfo
        }
      });

    } catch (error) {
      logger.error('获取数据库指标失败', {
        error: error instanceof Error ? error.message : String(error)
      });

      res.status(500).json({
        error: 'database_metrics_failed',
        message: '无法获取数据库性能指标'
      });
    }
  };

  /**
   * 缓存性能指标
   * GET /api/v1/monitoring/cache
   */
  cacheMetrics = async (req: Request, res: Response): Promise<void> => {
    try {
      const cacheMonitor = CachePerformanceMonitor.getInstance();
      const stats = cacheMonitor.getStats();

      // 获取Redis详细信息
      const redisInfo = await this.getRedisMetrics();

      res.status(200).json({
        timestamp: new Date().toISOString(),
        cache: {
          application: stats,
          redis: redisInfo
        }
      });

    } catch (error) {
      logger.error('获取缓存指标失败', {
        error: error instanceof Error ? error.message : String(error)
      });

      res.status(500).json({
        error: 'cache_metrics_failed',
        message: '无法获取缓存性能指标'
      });
    }
  };

  /**
   * 系统资源使用情况
   * GET /api/v1/monitoring/system
   */
  systemMetrics = async (req: Request, res: Response): Promise<void> => {
    try {
      const memoryMonitor = MemoryMonitor.getInstance();
      const currentUsage = memoryMonitor.getCurrentUsage();

      // 获取系统负载信息
      const loadAverage = process.platform !== 'win32' ? require('os').loadavg() : [0, 0, 0];
      const cpuCount = require('os').cpus().length;

      res.status(200).json({
        timestamp: new Date().toISOString(),
        system: {
          ...currentUsage,
          load: {
            '1min': loadAverage[0],
            '5min': loadAverage[1],
            '15min': loadAverage[2],
            cpuCount
          },
          platform: process.platform,
          nodeVersion: process.version,
          pid: process.pid
        }
      });

    } catch (error) {
      logger.error('获取系统指标失败', {
        error: error instanceof Error ? error.message : String(error)
      });

      res.status(500).json({
        error: 'system_metrics_failed',
        message: '无法获取系统资源指标'
      });
    }
  };

  /**
   * 实时性能监控 (WebSocket或SSE)
   * GET /api/v1/monitoring/realtime
   */
  realtime = async (req: Request, res: Response): Promise<void> => {
    try {
      // 设置SSE头
      res.writeHead(200, {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Cache-Control'
      });

      // 发送初始数据
      const sendMetrics = async () => {
        try {
          const report = getPerformanceReport();
          const data = JSON.stringify(report);
          res.write(`data: ${data}\n\n`);
        } catch (error) {
          logger.error('发送实时指标失败', { error });
        }
      };

      // 立即发送一次
      await sendMetrics();

      // 每5秒发送一次更新
      const interval = setInterval(sendMetrics, 5000);

      // 客户端断开连接时清理
      req.on('close', () => {
        clearInterval(interval);
        logger.debug('实时监控客户端断开连接');
      });

    } catch (error) {
      logger.error('实时监控初始化失败', {
        error: error instanceof Error ? error.message : String(error)
      });

      res.status(500).json({
        error: 'realtime_monitoring_failed',
        message: '无法启动实时监控'
      });
    }
  };

  /**
   * 性能报警配置
   * GET/POST /api/v1/monitoring/alerts
   */
  alerts = async (req: Request, res: Response): Promise<void> => {
    try {
      if (req.method === 'GET') {
        // 获取当前报警配置
        const alertConfig = await this.getAlertConfiguration();
        res.status(200).json(alertConfig);
      } else if (req.method === 'POST') {
        // 更新报警配置
        const newConfig = req.body;
        await this.updateAlertConfiguration(newConfig);
        res.status(200).json({
          message: '报警配置已更新',
          config: newConfig
        });
      }
    } catch (error) {
      logger.error('报警配置操作失败', {
        error: error instanceof Error ? error.message : String(error)
      });

      res.status(500).json({
        error: 'alert_config_failed',
        message: '报警配置操作失败'
      });
    }
  };

  /**
   * 检查数据库健康状态
   */
  private async checkDatabaseHealth(): Promise<any> {
    try {
      const startTime = Date.now();
      const isHealthy = await checkDatabaseHealth();
      const responseTime = Date.now() - startTime;

      return {
        healthy: isHealthy,
        responseTime: `${responseTime}ms`,
        status: isHealthy ? 'connected' : 'disconnected'
      };
    } catch (error) {
      return {
        healthy: false,
        responseTime: 'timeout',
        status: 'error',
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * 检查Redis健康状态
   */
  private async checkRedisHealth(): Promise<any> {
    try {
      const startTime = Date.now();
      const client = redisService.getClient();
      
      if (!client) {
        return {
          healthy: false,
          status: 'not_initialized'
        };
      }

      await client.ping();
      const responseTime = Date.now() - startTime;

      return {
        healthy: true,
        responseTime: `${responseTime}ms`,
        status: 'connected'
      };
    } catch (error) {
      return {
        healthy: false,
        responseTime: 'timeout',
        status: 'error',
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * 获取Redis性能指标
   */
  private async getRedisMetrics(): Promise<any> {
    try {
      const client = redisService.getClient();
      if (!client) {
        return { status: 'not_available' };
      }

      const info = await client.info();
      const memory = await client.info('memory');
      const stats = await client.info('stats');

      return {
        status: 'available',
        memory: this.parseRedisInfo(memory),
        stats: this.parseRedisInfo(stats),
        connectionCount: await client.client('list').then(list => list.split('\n').length - 1)
      };
    } catch (error) {
      return {
        status: 'error',
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * 解析Redis INFO命令输出
   */
  private parseRedisInfo(info: string): Record<string, any> {
    const result: Record<string, any> = {};
    const lines = info.split('\n');

    for (const line of lines) {
      if (line.includes(':')) {
        const [key, value] = line.split(':');
        result[key.trim()] = value.trim();
      }
    }

    return result;
  }

  /**
   * 获取数据库连接池信息
   */
  private async getDatabasePoolInfo(): Promise<any> {
    try {
      // Prisma不直接暴露连接池信息，这里返回基本信息
      return {
        status: 'active',
        provider: 'prisma',
        // 可以添加更多连接池相关信息
      };
    } catch (error) {
      return {
        status: 'error',
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * 获取请求统计信息
   */
  private getRequestStats(timeWindow: number): any {
    const apiStats = performanceCollector.getStats('api_response', timeWindow);
    
    if (!apiStats) {
      return { message: 'No request data available' };
    }

    return {
      ...apiStats,
      timeWindow: `${timeWindow / 1000}s`
    };
  }

  /**
   * 获取报警配置
   */
  private async getAlertConfiguration(): Promise<any> {
    // 这里可以从数据库或配置文件读取报警配置
    return {
      responseTime: {
        warning: 1000,
        critical: 5000
      },
      memory: {
        warning: 80,
        critical: 95
      },
      database: {
        slowQuery: 100,
        connectionPool: 80
      },
      cache: {
        hitRate: 80
      }
    };
  }

  /**
   * 更新报警配置
   */
  private async updateAlertConfiguration(config: any): Promise<void> {
    // 这里可以将配置保存到数据库或配置文件
    logger.info('报警配置已更新', { config });
  }

  /**
   * 获取高级系统健康状态
   * GET /api/v1/monitoring/health/advanced
   */
  advancedHealth = async (req: Request, res: Response): Promise<void> => {
    try {
      const currentHealth = systemHealth.getCurrentHealth();

      if (!currentHealth) {
        const health = await systemHealth.performHealthCheck();
        res.json({
          success: true,
          data: health
        });
        return;
      }

      res.json({
        success: true,
        data: currentHealth
      });

    } catch (error) {
      logger.error('获取高级健康状态失败', { error: error.message });
      res.status(500).json({
        success: false,
        error: 'INTERNAL_ERROR',
        message: '获取高级健康状态失败'
      });
    }
  };

  /**
   * 获取指标数据
   * GET /api/v1/monitoring/metrics/query
   */
  queryMetrics = async (req: Request, res: Response): Promise<void> => {
    try {
      const validation = validateRequest(metricsQuerySchema, req.query);
      if (!validation.success) {
        res.status(400).json({
          success: false,
          error: 'VALIDATION_ERROR',
          message: '请求参数验证失败',
          details: validation.errors
        });
        return;
      }

      const options = {
        ...validation.data,
        startTime: validation.data.startTime ? new Date(validation.data.startTime) : undefined,
        endTime: validation.data.endTime ? new Date(validation.data.endTime) : undefined
      };

      const metrics = metricsCollector.queryMetrics(options);

      res.json({
        success: true,
        data: {
          metrics,
          count: metrics.length
        }
      });

    } catch (error) {
      logger.error('查询指标失败', { error: error.message });
      res.status(500).json({
        success: false,
        error: 'INTERNAL_ERROR',
        message: '查询指标失败'
      });
    }
  };

  /**
   * 查询日志
   * GET /api/v1/monitoring/logs/query
   */
  queryLogs = async (req: Request, res: Response): Promise<void> => {
    try {
      const validation = validateRequest(logQuerySchema, req.query);
      if (!validation.success) {
        res.status(400).json({
          success: false,
          error: 'VALIDATION_ERROR',
          message: '请求参数验证失败',
          details: validation.errors
        });
        return;
      }

      const options = {
        ...validation.data,
        startTime: validation.data.startTime ? new Date(validation.data.startTime) : undefined,
        endTime: validation.data.endTime ? new Date(validation.data.endTime) : undefined
      };

      const logs = logAggregator.queryLogs(options);

      res.json({
        success: true,
        data: {
          logs,
          count: logs.length
        }
      });

    } catch (error) {
      logger.error('查询日志失败', { error: error.message });
      res.status(500).json({
        success: false,
        error: 'INTERNAL_ERROR',
        message: '查询日志失败'
      });
    }
  };

  /**
   * 获取活跃告警
   * GET /api/v1/monitoring/alerts/active
   */
  getActiveAlerts = async (req: Request, res: Response): Promise<void> => {
    try {
      const alerts = alertManager.getActiveAlerts();

      res.json({
        success: true,
        data: {
          alerts,
          count: alerts.length
        }
      });

    } catch (error) {
      logger.error('获取活跃告警失败', { error: error.message });
      res.status(500).json({
        success: false,
        error: 'INTERNAL_ERROR',
        message: '获取活跃告警失败'
      });
    }
  };

  /**
   * 获取监控概览
   * GET /api/v1/monitoring/overview
   */
  getOverview = async (req: Request, res: Response): Promise<void> => {
    try {
      const [
        currentHealth,
        metricsStats,
        logStats,
        alertStats
      ] = await Promise.all([
        systemHealth.getCurrentHealth() || systemHealth.performHealthCheck(),
        metricsCollector.getStats(),
        logAggregator.getLogStats(),
        alertManager.getAlertStats()
      ]);

      const overview = {
        health: {
          overall: currentHealth.overall,
          components: currentHealth.components.length,
          unhealthyComponents: currentHealth.components.filter(c => c.status !== 'healthy').length
        },
        metrics: {
          totalMetrics: metricsStats.totalMetrics,
          metricNames: metricsStats.metricNames
        },
        logs: {
          totalLogs: logStats.totalLogs,
          errorRate: logStats.errorRate
        },
        alerts: {
          active: alertStats.active,
          critical: alertStats.bySeverity.critical || 0
        }
      };

      res.json({
        success: true,
        data: overview
      });

    } catch (error) {
      logger.error('获取监控概览失败', { error: error.message });
      res.status(500).json({
        success: false,
        error: 'INTERNAL_ERROR',
        message: '获取监控概览失败'
      });
    }
  };
}
