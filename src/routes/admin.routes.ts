/**
 * 管理员路由
 * 提供系统管理、用户管理、OAuth客户端管理等管理员功能的API端点
 */

import { Router } from 'express';
import { AdminController } from '@/controllers/admin.controller';
import { authenticateToken, requireRole } from '@/middleware/auth.middleware';
import rateLimit from 'express-rate-limit';

const router = Router();
const adminController = new AdminController();

// 管理员端点速率限制
const adminRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 200, // 每个IP最多200次请求
  message: {
    error: 'admin_rate_limit_exceeded',
    message: '管理员请求过于频繁，请稍后再试'
  },
  standardHeaders: true,
  legacyHeaders: false
});

// 敏感操作速率限制（更严格）
const sensitiveRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 50, // 每个IP最多50次敏感操作
  message: {
    error: 'sensitive_operation_rate_limit_exceeded',
    message: '敏感操作过于频繁，请稍后再试'
  },
  standardHeaders: true,
  legacyHeaders: false
});

// 所有管理员路由都需要认证和管理员权限
router.use(authenticateToken as any);
router.use(requireRole(['admin', 'super_admin']) as any);
router.use(adminRateLimit);

/**
 * 系统统计和监控
 */

/**
 * @route GET /api/v1/admin/stats
 * @desc 获取系统统计信息
 * @access Private (管理员)
 */
router.get('/stats', adminController.getSystemStats);

/**
 * @route GET /api/v1/admin/health
 * @desc 获取系统健康状态
 * @access Private (管理员)
 */
router.get('/health', adminController.getSystemHealth);

/**
 * @route GET /api/v1/admin/metrics
 * @desc 获取性能指标
 * @access Private (管理员)
 */
router.get('/metrics', async (req, res) => {
  // 重定向到监控端点
  res.redirect('/api/v1/monitoring/metrics');
});

/**
 * 用户管理
 */

/**
 * @route GET /api/v1/admin/users
 * @desc 获取用户列表
 * @access Private (管理员)
 * @query {number} [page=1] - 页码
 * @query {number} [limit=20] - 每页数量
 * @query {string} [search] - 搜索关键词
 * @query {boolean} [isActive] - 是否活跃
 * @query {string} [role] - 角色筛选
 */
router.get('/users', adminController.getUsers);

/**
 * @route GET /api/v1/admin/users/:id
 * @desc 获取用户详情
 * @access Private (管理员)
 */
router.get('/users/:id', adminController.getUser);

/**
 * @route POST /api/v1/admin/users
 * @desc 创建用户
 * @access Private (管理员)
 * @body {string} email - 邮箱
 * @body {string} [password] - 密码（可选，系统生成临时密码）
 * @body {string} [nickname] - 昵称
 * @body {string} [firstName] - 名
 * @body {string} [lastName] - 姓
 * @body {string[]} [roles] - 角色列表
 * @body {boolean} [isActive] - 是否启用
 */
router.post('/users', sensitiveRateLimit, adminController.createUser);

/**
 * @route PUT /api/v1/admin/users/:id
 * @desc 更新用户
 * @access Private (管理员)
 */
router.put('/users/:id', adminController.updateUser);

/**
 * @route DELETE /api/v1/admin/users/:id
 * @desc 删除用户
 * @access Private (管理员)
 */
router.delete('/users/:id', sensitiveRateLimit, adminController.deleteUser);

/**
 * OAuth客户端管理
 */

/**
 * @route GET /api/v1/admin/oauth-clients
 * @desc 获取OAuth客户端列表
 * @access Private (管理员)
 * @query {number} [page=1] - 页码
 * @query {number} [limit=20] - 每页数量
 * @query {string} [search] - 搜索关键词
 * @query {boolean} [isActive] - 是否启用
 */
router.get('/oauth-clients', adminController.getOAuthClients);

/**
 * @route POST /api/v1/admin/oauth-clients
 * @desc 创建OAuth客户端
 * @access Private (管理员)
 * @body {string} name - 客户端名称
 * @body {string} [description] - 描述
 * @body {string[]} redirectUris - 重定向URI列表
 * @body {string[]} [grantTypes] - 授权类型
 * @body {string[]} [responseTypes] - 响应类型
 * @body {string[]} [scopes] - 权限范围
 * @body {boolean} [requirePkce] - 是否需要PKCE
 * @body {boolean} [requireConsent] - 是否需要用户同意
 */
router.post('/oauth-clients', sensitiveRateLimit, adminController.createOAuthClient);

/**
 * @route PUT /api/v1/admin/oauth-clients/:id
 * @desc 更新OAuth客户端
 * @access Private (管理员)
 */
router.put('/oauth-clients/:id', adminController.updateOAuthClient);

/**
 * @route DELETE /api/v1/admin/oauth-clients/:id
 * @desc 删除OAuth客户端
 * @access Private (管理员)
 */
router.delete('/oauth-clients/:id', sensitiveRateLimit, adminController.deleteOAuthClient);

/**
 * @route POST /api/v1/admin/oauth-clients/:id/regenerate-secret
 * @desc 重新生成客户端密钥
 * @access Private (管理员)
 */
router.post('/oauth-clients/:id/regenerate-secret', 
  sensitiveRateLimit, 
  adminController.regenerateClientSecret
);

/**
 * 审计和安全
 */

/**
 * @route GET /api/v1/admin/audit-logs
 * @desc 获取审计日志
 * @access Private (管理员)
 * @query {number} [page=1] - 页码
 * @query {number} [limit=20] - 每页数量
 * @query {string} [action] - 操作类型
 * @query {string} [userId] - 用户ID
 * @query {string} [startDate] - 开始日期
 * @query {string} [endDate] - 结束日期
 */
router.get('/audit-logs', adminController.getAuditLogs);

/**
 * 系统维护
 */

/**
 * @route POST /api/v1/admin/maintenance/clear-cache
 * @desc 清理缓存
 * @access Private (管理员)
 * @body {string} [type] - 缓存类型 (redis, all)
 */
router.post('/maintenance/clear-cache', 
  sensitiveRateLimit, 
  adminController.clearCache
);

/**
 * @route POST /api/v1/admin/maintenance/cleanup-tokens
 * @desc 清理过期令牌
 * @access Private (管理员)
 */
router.post('/maintenance/cleanup-tokens', 
  sensitiveRateLimit, 
  adminController.cleanupExpiredTokens
);

/**
 * @route POST /api/v1/admin/maintenance/cleanup-sessions
 * @desc 清理过期会话
 * @access Private (管理员)
 */
router.post('/maintenance/cleanup-sessions', 
  sensitiveRateLimit, 
  adminController.cleanupExpiredSessions
);

export default router;
