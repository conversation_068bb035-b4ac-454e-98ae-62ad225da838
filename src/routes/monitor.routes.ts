/**
 * 实时监控路由
 * 提供系统监控数据和告警管理的API接口
 */

import { Router } from 'express';
import { realTimeMonitorService, webSocketMonitorService } from '@/services/real-time-monitor.service';
import { logger } from '@/config/logger';
import { authenticateToken } from '@/middleware/auth.middleware';
import rateLimit from 'express-rate-limit';
import { query, param, validationResult } from 'express-validator';

const router = Router();

// 监控API速率限制
const monitorRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 300, // 每个IP最多300次监控请求
  message: {
    error: 'monitor_rate_limit_exceeded',
    message: '监控API请求过于频繁，请稍后再试'
  },
  standardHeaders: true,
  legacyHeaders: false
});

/**
 * 验证中间件
 */
const handleValidationErrors = (req: any, res: any, next: any) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      error: 'validation_error',
      message: '请求参数验证失败',
      details: errors.array()
    });
  }
  next();
};

/**
 * 监控权限检查中间件
 */
const requireMonitorPermission = (req: any, res: any, next: any) => {
  const user = req.user;
  
  if (!user) {
    return res.status(401).json({
      error: 'unauthorized',
      message: '未认证'
    });
  }

  // 检查监控权限
  if (!user.isAdmin && !user.permissions?.includes('monitor.read')) {
    return res.status(403).json({
      error: 'forbidden',
      message: '需要监控查看权限'
    });
  }

  next();
};

/**
 * @route GET /monitor/status
 * @desc 获取监控服务状态
 * @access Private (监控权限)
 */
router.get('/status',
  monitorRateLimit,
  authenticateToken as any,
  requireMonitorPermission,
  (req, res) => {
    try {
      const status = {
        isRunning: realTimeMonitorService.listenerCount('metricsUpdated') > 0,
        connectedClients: webSocketMonitorService.getClientCount(),
        uptime: process.uptime(),
        timestamp: new Date().toISOString()
      };

      res.json({
        success: true,
        status,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      logger.error('获取监控状态失败', { error });
      res.status(500).json({
        error: 'server_error',
        message: '获取监控状态失败'
      });
    }
  }
);

/**
 * @route GET /monitor/metrics/current
 * @desc 获取当前系统指标
 * @access Private (监控权限)
 */
router.get('/metrics/current',
  monitorRateLimit,
  authenticateToken as any,
  requireMonitorPermission,
  (req, res) => {
    try {
      const metrics = realTimeMonitorService.getCurrentMetrics();

      if (!metrics) {
        return res.status(404).json({
          error: 'no_metrics',
          message: '暂无监控数据'
        });
      }

      res.json({
        success: true,
        metrics,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      logger.error('获取当前指标失败', { error });
      res.status(500).json({
        error: 'server_error',
        message: '获取当前指标失败'
      });
    }
  }
);

/**
 * @route GET /monitor/metrics/history
 * @desc 获取历史监控指标
 * @access Private (监控权限)
 */
router.get('/metrics/history',
  monitorRateLimit,
  authenticateToken as any,
  requireMonitorPermission,
  [
    query('limit').optional().isInt({ min: 1, max: 1000 }),
    query('startTime').optional().isISO8601(),
    query('endTime').optional().isISO8601()
  ],
  handleValidationErrors,
  (req, res) => {
    try {
      const limit = parseInt(req.query.limit as string) || 100;
      const startTime = req.query.startTime ? new Date(req.query.startTime as string) : null;
      const endTime = req.query.endTime ? new Date(req.query.endTime as string) : null;

      let metrics = realTimeMonitorService.getHistoricalMetrics(limit);

      // 按时间范围过滤
      if (startTime || endTime) {
        metrics = metrics.filter(metric => {
          const timestamp = metric.timestamp;
          if (startTime && timestamp < startTime) return false;
          if (endTime && timestamp > endTime) return false;
          return true;
        });
      }

      res.json({
        success: true,
        metrics,
        count: metrics.length,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      logger.error('获取历史指标失败', { error });
      res.status(500).json({
        error: 'server_error',
        message: '获取历史指标失败'
      });
    }
  }
);

/**
 * @route GET /monitor/alerts
 * @desc 获取系统告警
 * @access Private (监控权限)
 */
router.get('/alerts',
  monitorRateLimit,
  authenticateToken as any,
  requireMonitorPermission,
  [
    query('active').optional().isBoolean(),
    query('level').optional().isIn(['info', 'warning', 'error', 'critical']),
    query('source').optional().isString(),
    query('limit').optional().isInt({ min: 1, max: 500 })
  ],
  handleValidationErrors,
  (req, res) => {
    try {
      const activeOnly = req.query.active === 'true';
      const level = req.query.level as string;
      const source = req.query.source as string;
      const limit = parseInt(req.query.limit as string) || 100;

      let alerts = activeOnly 
        ? realTimeMonitorService.getActiveAlerts()
        : realTimeMonitorService.getAllAlerts(limit);

      // 按级别过滤
      if (level) {
        alerts = alerts.filter(alert => alert.level === level);
      }

      // 按来源过滤
      if (source) {
        alerts = alerts.filter(alert => alert.source === source);
      }

      res.json({
        success: true,
        alerts,
        count: alerts.length,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      logger.error('获取系统告警失败', { error });
      res.status(500).json({
        error: 'server_error',
        message: '获取系统告警失败'
      });
    }
  }
);

/**
 * @route POST /monitor/alerts/:alertId/acknowledge
 * @desc 确认告警
 * @access Private (监控权限)
 */
router.post('/alerts/:alertId/acknowledge',
  monitorRateLimit,
  authenticateToken as any,
  requireMonitorPermission,
  [
    param('alertId').isString().notEmpty()
  ],
  handleValidationErrors,
  (req, res) => {
    try {
      const { alertId } = req.params;

      const success = realTimeMonitorService.acknowledgeAlert(alertId);

      if (!success) {
        return res.status(404).json({
          error: 'alert_not_found',
          message: '告警不存在'
        });
      }

      logger.info('告警已确认', { 
        alertId, 
        userId: req.user.id,
        username: req.user.username 
      });

      res.json({
        success: true,
        message: '告警已确认',
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      logger.error('确认告警失败', { error, alertId: req.params.alertId });
      res.status(500).json({
        error: 'server_error',
        message: '确认告警失败'
      });
    }
  }
);

/**
 * @route POST /monitor/start
 * @desc 启动监控服务
 * @access Private (管理员)
 */
router.post('/start',
  monitorRateLimit,
  authenticateToken as any,
  (req, res) => {
    try {
      // 检查管理员权限
      if (!req.user.isAdmin && !req.user.permissions?.includes('monitor.admin')) {
        return res.status(403).json({
          error: 'forbidden',
          message: '需要监控管理权限'
        });
      }

      realTimeMonitorService.start();

      logger.info('监控服务已启动', { 
        userId: req.user.id,
        username: req.user.username 
      });

      res.json({
        success: true,
        message: '监控服务已启动',
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      logger.error('启动监控服务失败', { error });
      res.status(500).json({
        error: 'server_error',
        message: '启动监控服务失败'
      });
    }
  }
);

/**
 * @route POST /monitor/stop
 * @desc 停止监控服务
 * @access Private (管理员)
 */
router.post('/stop',
  monitorRateLimit,
  authenticateToken as any,
  (req, res) => {
    try {
      // 检查管理员权限
      if (!req.user.isAdmin && !req.user.permissions?.includes('monitor.admin')) {
        return res.status(403).json({
          error: 'forbidden',
          message: '需要监控管理权限'
        });
      }

      realTimeMonitorService.stop();

      logger.info('监控服务已停止', { 
        userId: req.user.id,
        username: req.user.username 
      });

      res.json({
        success: true,
        message: '监控服务已停止',
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      logger.error('停止监控服务失败', { error });
      res.status(500).json({
        error: 'server_error',
        message: '停止监控服务失败'
      });
    }
  }
);

/**
 * @route GET /monitor/summary
 * @desc 获取监控摘要信息
 * @access Private (监控权限)
 */
router.get('/summary',
  monitorRateLimit,
  authenticateToken as any,
  requireMonitorPermission,
  (req, res) => {
    try {
      const currentMetrics = realTimeMonitorService.getCurrentMetrics();
      const activeAlerts = realTimeMonitorService.getActiveAlerts();
      const historicalMetrics = realTimeMonitorService.getHistoricalMetrics(10);

      const summary = {
        current: currentMetrics,
        alerts: {
          total: activeAlerts.length,
          critical: activeAlerts.filter(a => a.level === 'critical').length,
          warning: activeAlerts.filter(a => a.level === 'warning').length,
          info: activeAlerts.filter(a => a.level === 'info').length
        },
        trends: {
          memoryUsage: historicalMetrics.map(m => ({
            timestamp: m.timestamp,
            value: (m.system.memory.heapUsed / m.system.memory.heapTotal) * 100
          })),
          cpuUsage: historicalMetrics.map(m => ({
            timestamp: m.timestamp,
            value: m.system.cpu
          })),
          activeUsers: historicalMetrics.map(m => ({
            timestamp: m.timestamp,
            value: m.authentication.activeUsers
          }))
        },
        status: {
          isRunning: realTimeMonitorService.listenerCount('metricsUpdated') > 0,
          connectedClients: webSocketMonitorService.getClientCount(),
          uptime: process.uptime()
        }
      };

      res.json({
        success: true,
        summary,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      logger.error('获取监控摘要失败', { error });
      res.status(500).json({
        error: 'server_error',
        message: '获取监控摘要失败'
      });
    }
  }
);

export default router;
