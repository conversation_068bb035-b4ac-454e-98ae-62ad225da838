/**
 * 认证路由
 * 定义认证相关的API端点
 */

import { Router } from 'express';
import { AuthController } from '@/controllers/auth.controller';
import { authenticateToken, rateLimitByUser } from '@/middleware/auth.middleware';
import {
  loginRateLimit,
  registerRateLimit,
  passwordResetRateLimit,
  generalApiRateLimit
} from '@/middleware/security.middleware';

const router = Router();
const authController = new AuthController();

// 使用统一的安全中间件进行速率限制
// loginRateLimit, registerRateLimit, passwordResetRateLimit 已从安全中间件导入

/**
 * @route POST /api/v1/auth/register
 * @desc 用户注册
 * @access Public
 */
router.post('/register', registerRateLimit, authController.register);

/**
 * @route POST /api/v1/auth/login
 * @desc 用户登录
 * @access Public
 */
router.post('/login', loginRateLimit, authController.login);

/**
 * @route POST /api/v1/auth/logout
 * @desc 用户登出
 * @access Private
 */
router.post('/logout', authenticateToken as any, authController.logout);

/**
 * @route POST /api/v1/auth/refresh-token
 * @desc 刷新访问令牌
 * @access Public
 */
router.post('/refresh-token', generalApiRateLimit, authController.refreshToken);

/**
 * @route POST /api/v1/auth/forgot-password
 * @desc 忘记密码
 * @access Public
 */
router.post('/forgot-password', passwordResetRateLimit, authController.forgotPassword);

/**
 * @route POST /api/v1/auth/reset-password
 * @desc 重置密码
 * @access Public
 */
router.post('/reset-password', passwordResetRateLimit, authController.resetPassword);

/**
 * @route POST /api/v1/auth/change-password
 * @desc 修改密码
 * @access Private
 */
router.post('/change-password',
  authenticateToken as any,
  rateLimitByUser(5, 15 * 60 * 1000), // 每个用户15分钟内最多5次
  authController.changePassword
);

/**
 * @route GET /api/v1/auth/verify-email
 * @desc 验证邮箱
 * @access Public
 */
router.get('/verify-email', generalApiRateLimit, authController.verifyEmail);

/**
 * @route POST /api/v1/auth/validate-token
 * @desc 验证访问令牌（供API网关使用）
 * @access Public
 */
router.post('/validate-token', generalApiRateLimit, authController.validateToken);

/**
 * @route GET /api/v1/auth/introspect
 * @desc 令牌内省端点（RFC 7662）
 * @access Public
 */
router.post('/introspect', generalApiRateLimit, authController.introspectToken);

export default router;
