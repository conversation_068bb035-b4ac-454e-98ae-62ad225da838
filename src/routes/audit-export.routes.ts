/**
 * 审计数据导出路由
 * 定义审计数据导出和报告生成的API路由
 */

import { Router } from 'express';
import { auditExportController, auditExportValidation } from '@/controllers/audit-export.controller';
import { authMiddleware } from '@/middleware/auth.middleware';
import { rbacMiddleware } from '@/middleware/rbac.middleware';
import { rateLimitMiddleware } from '@/middleware/rate-limit.middleware';
import { securityMiddleware } from '@/middleware/security.middleware';

const router = Router();

/**
 * 审计导出路由配置
 * 所有路由都需要认证和适当的权限
 */

// 应用通用中间件
router.use(authMiddleware);
router.use(securityMiddleware.validateRequest);

/**
 * @route   POST /api/v1/audit/export
 * @desc    导出审计数据
 * @access  需要 audit:export:create 权限
 * @body    {
 *   format: 'json'|'csv'|'excel'|'pdf'|'xml',
 *   reportType: string,
 *   startDate: string,
 *   endDate: string,
 *   userId?: string,
 *   eventTypes?: string[],
 *   severity?: string[],
 *   includeMetadata?: boolean,
 *   includeCharts?: boolean,
 *   customFields?: string[],
 *   maxRecords?: number,
 *   compression?: boolean
 * }
 */
router.post(
  '/',
  rbacMiddleware.requirePermission('audit:export:create'),
  rateLimitMiddleware({
    windowMs: 15 * 60 * 1000, // 15分钟
    max: 5, // 每15分钟最多5次导出请求
    message: '导出请求过于频繁，请稍后再试'
  }),
  auditExportValidation.exportData,
  auditExportController.exportAuditData
);

/**
 * @route   POST /api/v1/audit/export/compliance-report
 * @desc    生成合规报告
 * @access  需要 audit:compliance:report 权限
 * @body    {
 *   startDate: string,
 *   endDate: string,
 *   format?: 'json'|'csv'|'excel'|'pdf'|'xml'
 * }
 */
router.post(
  '/compliance-report',
  rbacMiddleware.requirePermission('audit:compliance:report'),
  rateLimitMiddleware({
    windowMs: 30 * 60 * 1000, // 30分钟
    max: 3, // 每30分钟最多3次合规报告请求
    message: '合规报告生成请求过于频繁，请稍后再试'
  }),
  auditExportValidation.generateReport,
  auditExportController.generateComplianceReport
);

/**
 * @route   POST /api/v1/audit/export/security-summary
 * @desc    生成安全摘要报告
 * @access  需要 audit:security:report 权限
 * @body    {
 *   startDate: string,
 *   endDate: string,
 *   format?: 'json'|'csv'|'excel'|'pdf'|'xml'
 * }
 */
router.post(
  '/security-summary',
  rbacMiddleware.requirePermission('audit:security:report'),
  rateLimitMiddleware({
    windowMs: 30 * 60 * 1000, // 30分钟
    max: 3, // 每30分钟最多3次安全报告请求
    message: '安全报告生成请求过于频繁，请稍后再试'
  }),
  auditExportValidation.generateReport,
  auditExportController.generateSecuritySummary
);

/**
 * @route   GET /api/v1/audit/export/files
 * @desc    获取导出文件列表
 * @access  需要 audit:export:read 权限
 */
router.get(
  '/files',
  rbacMiddleware.requirePermission('audit:export:read'),
  rateLimitMiddleware({
    windowMs: 60 * 1000, // 1分钟
    max: 20, // 每分钟最多20次请求
    message: '文件列表查询请求过于频繁，请稍后再试'
  }),
  auditExportController.getExportFiles
);

/**
 * @route   GET /api/v1/audit/export/download/:fileName
 * @desc    下载导出文件
 * @access  需要 audit:export:download 权限
 * @params  fileName - 文件名
 */
router.get(
  '/download/:fileName',
  rbacMiddleware.requirePermission('audit:export:download'),
  rateLimitMiddleware({
    windowMs: 60 * 1000, // 1分钟
    max: 10, // 每分钟最多10次下载请求
    message: '文件下载请求过于频繁，请稍后再试'
  }),
  auditExportController.downloadExportFile
);

/**
 * @route   DELETE /api/v1/audit/export/files/:fileName
 * @desc    删除导出文件
 * @access  需要 audit:export:delete 权限
 * @params  fileName - 文件名
 */
router.delete(
  '/files/:fileName',
  rbacMiddleware.requirePermission('audit:export:delete'),
  rateLimitMiddleware({
    windowMs: 60 * 1000, // 1分钟
    max: 10, // 每分钟最多10次删除请求
    message: '文件删除请求过于频繁，请稍后再试'
  }),
  auditExportController.deleteExportFile
);

export { router as auditExportRoutes };
