/**
 * 管理员API路由
 * 为React Admin提供RESTful API接口
 */

import { Router } from 'express';
import { adminDataProviderService } from '@/services/admin-data-provider.service';
import { logger } from '@/config/logger';
import { authenticateToken } from '@/middleware/auth.middleware';
import rateLimit from 'express-rate-limit';
import { query, param, validationResult } from 'express-validator';

const router = Router();

// 管理员API速率限制配置
const adminApiRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 1000, // 每个IP最多1000次管理API请求
  message: {
    error: 'admin_api_rate_limit_exceeded',
    message: '管理API请求过于频繁，请稍后再试'
  },
  standardHeaders: true,
  legacyHeaders: false
});

/**
 * 验证中间件
 */
const handleValidationErrors = (req: any, res: any, next: any) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      error: 'validation_error',
      message: '请求参数验证失败',
      details: errors.array()
    });
  }
  next();
};

/**
 * 管理员权限检查中间件
 */
const requireAdminPermission = (req: any, res: any, next: any) => {
  const user = req.user;
  
  if (!user) {
    return res.status(401).json({
      error: 'unauthorized',
      message: '未认证'
    });
  }

  // 检查管理员权限
  if (!user.isAdmin && !user.permissions?.includes('admin.access')) {
    return res.status(403).json({
      error: 'forbidden',
      message: '需要管理员权限'
    });
  }

  next();
};

/**
 * @route GET /admin-api/:resource
 * @desc 获取资源列表
 * @access Private (管理员)
 */
router.get('/:resource',
  adminApiRateLimit,
  authenticateToken as any,
  requireAdminPermission,
  [
    param('resource').isIn(['users', 'applications', 'permissions', 'permission-requests', 'audit-logs']),
    query('_start').optional().isInt({ min: 0 }),
    query('_end').optional().isInt({ min: 1 }),
    query('_sort').optional().isString(),
    query('_order').optional().isIn(['ASC', 'DESC']),
    query('q').optional().isString()
  ],
  handleValidationErrors,
  async (req, res) => {
    try {
      const { resource } = req.params;
      const {
        _start = 0,
        _end = 25,
        _sort = 'createdAt',
        _order = 'DESC',
        q,
        ...filters
      } = req.query;

      // 转换React Admin参数格式
      const page = Math.floor(Number(_start) / (Number(_end) - Number(_start))) + 1;
      const perPage = Number(_end) - Number(_start);

      const params = {
        pagination: { page, perPage },
        sort: { field: _sort as string, order: _order as 'ASC' | 'DESC' },
        filter: { q, ...filters }
      };

      const result = await adminDataProviderService.getList(resource, params);

      // 设置React Admin需要的响应头
      res.set('X-Total-Count', result.total.toString());
      res.set('Access-Control-Expose-Headers', 'X-Total-Count');

      res.json(result.data);

    } catch (error) {
      logger.error('获取资源列表失败', { error, resource: req.params.resource });
      res.status(500).json({
        error: 'server_error',
        message: '获取资源列表失败'
      });
    }
  }
);

/**
 * @route GET /admin-api/:resource/:id
 * @desc 获取单个资源
 * @access Private (管理员)
 */
router.get('/:resource/:id',
  adminApiRateLimit,
  authenticateToken as any,
  requireAdminPermission,
  [
    param('resource').isIn(['users', 'applications', 'permissions', 'permission-requests', 'audit-logs']),
    param('id').isUUID()
  ],
  handleValidationErrors,
  async (req, res) => {
    try {
      const { resource, id } = req.params;

      const result = await adminDataProviderService.getOne(resource, { id });

      res.json(result.data);

    } catch (error) {
      logger.error('获取单个资源失败', { error, resource: req.params.resource, id: req.params.id });
      
      if (error.message.includes('不存在')) {
        res.status(404).json({
          error: 'not_found',
          message: '资源不存在'
        });
      } else {
        res.status(500).json({
          error: 'server_error',
          message: '获取资源失败'
        });
      }
    }
  }
);

/**
 * @route POST /admin-api/:resource
 * @desc 创建资源
 * @access Private (管理员)
 */
router.post('/:resource',
  adminApiRateLimit,
  authenticateToken as any,
  requireAdminPermission,
  [
    param('resource').isIn(['users', 'applications', 'permissions'])
  ],
  handleValidationErrors,
  async (req, res) => {
    try {
      const { resource } = req.params;
      const data = req.body;

      const result = await adminDataProviderService.create(resource, { data });

      res.status(201).json(result.data);

    } catch (error) {
      logger.error('创建资源失败', { error, resource: req.params.resource });
      res.status(500).json({
        error: 'server_error',
        message: '创建资源失败'
      });
    }
  }
);

/**
 * @route PUT /admin-api/:resource/:id
 * @desc 更新资源
 * @access Private (管理员)
 */
router.put('/:resource/:id',
  adminApiRateLimit,
  authenticateToken as any,
  requireAdminPermission,
  [
    param('resource').isIn(['users', 'applications', 'permissions', 'permission-requests']),
    param('id').isUUID()
  ],
  handleValidationErrors,
  async (req, res) => {
    try {
      const { resource, id } = req.params;
      const data = req.body;

      const result = await adminDataProviderService.update(resource, { id, data });

      res.json(result.data);

    } catch (error) {
      logger.error('更新资源失败', { error, resource: req.params.resource, id: req.params.id });
      
      if (error.message.includes('不存在')) {
        res.status(404).json({
          error: 'not_found',
          message: '资源不存在'
        });
      } else {
        res.status(500).json({
          error: 'server_error',
          message: '更新资源失败'
        });
      }
    }
  }
);

/**
 * @route DELETE /admin-api/:resource/:id
 * @desc 删除资源
 * @access Private (管理员)
 */
router.delete('/:resource/:id',
  adminApiRateLimit,
  authenticateToken as any,
  requireAdminPermission,
  [
    param('resource').isIn(['users', 'applications', 'permissions']),
    param('id').isUUID()
  ],
  handleValidationErrors,
  async (req, res) => {
    try {
      const { resource, id } = req.params;

      const result = await adminDataProviderService.delete(resource, { id });

      res.json(result.data);

    } catch (error) {
      logger.error('删除资源失败', { error, resource: req.params.resource, id: req.params.id });
      
      if (error.message.includes('不存在')) {
        res.status(404).json({
          error: 'not_found',
          message: '资源不存在'
        });
      } else {
        res.status(500).json({
          error: 'server_error',
          message: '删除资源失败'
        });
      }
    }
  }
);

/**
 * @route DELETE /admin-api/:resource
 * @desc 批量删除资源
 * @access Private (管理员)
 */
router.delete('/:resource',
  adminApiRateLimit,
  authenticateToken as any,
  requireAdminPermission,
  [
    param('resource').isIn(['users', 'applications', 'permissions']),
    query('ids').isString()
  ],
  handleValidationErrors,
  async (req, res) => {
    try {
      const { resource } = req.params;
      const { ids } = req.query;

      // 解析ID列表
      const idList = (ids as string).split(',');

      const result = await adminDataProviderService.deleteMany(resource, { ids: idList });

      res.json(result.data);

    } catch (error) {
      logger.error('批量删除资源失败', { error, resource: req.params.resource });
      res.status(500).json({
        error: 'server_error',
        message: '批量删除资源失败'
      });
    }
  }
);

/**
 * @route GET /admin-api/stats/dashboard
 * @desc 获取仪表板统计数据
 * @access Private (管理员)
 */
router.get('/stats/dashboard',
  adminApiRateLimit,
  authenticateToken as any,
  requireAdminPermission,
  async (req, res) => {
    try {
      // 获取各种统计数据
      const [
        userCount,
        applicationCount,
        permissionCount,
        pendingRequestCount,
        todayLoginCount
      ] = await Promise.all([
        require('@/config/database').prisma.user.count(),
        require('@/config/database').prisma.application.count(),
        require('@/config/database').prisma.permission.count(),
        require('@/config/database').prisma.permissionRequest.count({
          where: { status: 'pending' }
        }),
        require('@/config/database').prisma.auditLog.count({
          where: {
            action: 'login',
            createdAt: {
              gte: new Date(new Date().setHours(0, 0, 0, 0))
            }
          }
        })
      ]);

      const stats = {
        users: {
          total: userCount,
          active: userCount, // 简化处理
          growth: '****%' // 模拟数据
        },
        applications: {
          total: applicationCount,
          active: applicationCount,
          growth: '+2.1%'
        },
        permissions: {
          total: permissionCount,
          active: permissionCount,
          growth: '+1.8%'
        },
        requests: {
          pending: pendingRequestCount,
          todayProcessed: 0, // 简化处理
          growth: '+12.3%'
        },
        activity: {
          todayLogins: todayLoginCount,
          activeUsers: userCount,
          growth: '+8.7%'
        }
      };

      res.json(stats);

    } catch (error) {
      logger.error('获取仪表板统计数据失败', { error });
      res.status(500).json({
        error: 'server_error',
        message: '获取统计数据失败'
      });
    }
  }
);

export default router;
