/**
 * 监控路由
 * 提供系统性能监控和健康检查的API端点
 */

import { Router } from 'express';
import { MonitoringController } from '@/controllers/monitoring.controller';
import { authenticateToken } from '@/middleware/auth.middleware';
import { requireRole } from '@/middleware/rbac.middleware';
import rateLimit from 'express-rate-limit';

const router = Router();
const monitoringController = new MonitoringController();

// 监控端点速率限制（相对宽松，因为监控需要频繁访问）
const monitoringRateLimit = rateLimit({
  windowMs: 1 * 60 * 1000, // 1分钟
  max: 100, // 每分钟最多100次请求
  message: {
    error: 'monitoring_rate_limit_exceeded',
    message: '监控请求过于频繁，请稍后再试'
  },
  standardHeaders: true,
  legacyHeaders: false
});

// 健康检查速率限制（更宽松）
const healthCheckRateLimit = rateLimit({
  windowMs: 1 * 60 * 1000, // 1分钟
  max: 200, // 每分钟最多200次请求
  message: {
    error: 'health_check_rate_limit_exceeded',
    message: '健康检查请求过于频繁，请稍后再试'
  },
  standardHeaders: true,
  legacyHeaders: false
});

/**
 * @route GET /api/v1/monitoring/health
 * @desc 系统健康检查
 * @access Public
 * @returns {Object} 系统健康状态信息
 */
router.get('/health', 
  healthCheckRateLimit,
  monitoringController.health
);

/**
 * @route GET /api/v1/monitoring/metrics
 * @desc 获取系统性能指标
 * @access Private (需要认证)
 * @query {number} [timeWindow] - 时间窗口（秒），默认300秒
 * @returns {Object} 系统性能指标报告
 */
router.get('/metrics',
  monitoringRateLimit,
  authenticateToken as any,
  monitoringController.metrics
);

/**
 * @route GET /api/v1/monitoring/database
 * @desc 获取数据库性能指标
 * @access Private (需要认证)
 * @returns {Object} 数据库性能指标
 */
router.get('/database',
  monitoringRateLimit,
  authenticateToken as any,
  monitoringController.databaseMetrics
);

/**
 * @route GET /api/v1/monitoring/cache
 * @desc 获取缓存性能指标
 * @access Private (需要认证)
 * @returns {Object} 缓存性能指标
 */
router.get('/cache',
  monitoringRateLimit,
  authenticateToken as any,
  monitoringController.cacheMetrics
);

/**
 * @route GET /api/v1/monitoring/system
 * @desc 获取系统资源使用情况
 * @access Private (需要认证)
 * @returns {Object} 系统资源指标
 */
router.get('/system',
  monitoringRateLimit,
  authenticateToken as any,
  monitoringController.systemMetrics
);

/**
 * @route GET /api/v1/monitoring/realtime
 * @desc 实时性能监控 (Server-Sent Events)
 * @access Private (需要认证)
 * @returns {Stream} 实时性能数据流
 */
router.get('/realtime',
  monitoringRateLimit,
  authenticateToken as any,
  monitoringController.realtime
);

/**
 * @route GET /api/v1/monitoring/alerts
 * @desc 获取报警配置
 * @access Private (需要认证)
 * @returns {Object} 报警配置信息
 */
router.get('/alerts',
  monitoringRateLimit,
  authenticateToken as any,
  monitoringController.alerts
);

/**
 * @route POST /api/v1/monitoring/alerts
 * @desc 更新报警配置
 * @access Private (需要认证)
 * @body {Object} 报警配置
 * @returns {Object} 更新结果
 */
router.post('/alerts',
  monitoringRateLimit,
  authenticateToken as any,
  monitoringController.alerts
);

/**
 * @route GET /api/v1/monitoring/health/advanced
 * @desc 高级系统健康状态
 * @access Private (需要管理员或操作员权限)
 * @returns {Object} 详细的系统健康状态信息
 */
router.get('/health/advanced',
  monitoringRateLimit,
  authenticateToken as any,
  requireRole(['admin', 'operator']),
  monitoringController.advancedHealth
);

/**
 * @route GET /api/v1/monitoring/metrics/query
 * @desc 查询指标数据
 * @access Private (需要管理员或操作员权限)
 * @query {string} [name] - 指标名称
 * @query {string} [type] - 指标类型
 * @query {string} [startTime] - 开始时间
 * @query {string} [endTime] - 结束时间
 * @query {number} [limit] - 返回数量限制
 * @returns {Object} 指标数据
 */
router.get('/metrics/query',
  monitoringRateLimit,
  authenticateToken as any,
  requireRole(['admin', 'operator']),
  monitoringController.queryMetrics
);

/**
 * @route GET /api/v1/monitoring/logs/query
 * @desc 查询日志数据
 * @access Private (需要管理员或操作员权限)
 * @query {string} [level] - 日志级别
 * @query {string} [service] - 服务名称
 * @query {string} [startTime] - 开始时间
 * @query {string} [endTime] - 结束时间
 * @query {string} [search] - 搜索关键词
 * @query {number} [limit] - 返回数量限制
 * @returns {Object} 日志数据
 */
router.get('/logs/query',
  monitoringRateLimit,
  authenticateToken as any,
  requireRole(['admin', 'operator']),
  monitoringController.queryLogs
);

/**
 * @route GET /api/v1/monitoring/alerts/active
 * @desc 获取活跃告警
 * @access Private (需要管理员或操作员权限)
 * @returns {Object} 活跃告警列表
 */
router.get('/alerts/active',
  monitoringRateLimit,
  authenticateToken as any,
  requireRole(['admin', 'operator']),
  monitoringController.getActiveAlerts
);

/**
 * @route GET /api/v1/monitoring/overview
 * @desc 监控概览
 * @access Private (需要管理员或操作员权限)
 * @returns {Object} 监控系统概览信息
 */
router.get('/overview',
  monitoringRateLimit,
  authenticateToken as any,
  requireRole(['admin', 'operator']),
  monitoringController.getOverview
);

export default router;
