/**
 * OpenID Connect Provider 路由
 * 基于 node-oidc-provider 的完整 OIDC Provider 实现
 */

import { Router } from 'express';
import { oidcProviderService } from '@/services/oidc-provider.service';
import { logger } from '@/utils/logger';
import { authenticateToken } from '@/middleware/auth.middleware';
import rateLimit from 'express-rate-limit';

const router = Router();

// OIDC 速率限制配置
const oidcRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 100, // 每个IP最多100次OIDC请求
  message: {
    error: 'oidc_rate_limit_exceeded',
    message: 'OIDC请求过于频繁，请稍后再试'
  },
  standardHeaders: true,
  legacyHeaders: false
});

/**
 * 初始化 OIDC Provider
 */
async function initializeProvider() {
  try {
    await oidcProviderService.initialize();
    logger.info('OIDC Provider 路由初始化成功');
  } catch (error) {
    logger.error('OIDC Provider 路由初始化失败', { error });
    throw error;
  }
}

// 初始化 Provider
initializeProvider().catch(error => {
  logger.error('OIDC Provider 初始化失败', { error });
});

/**
 * 获取 OIDC Provider 实例
 */
function getProvider() {
  try {
    return oidcProviderService.getProvider();
  } catch (error) {
    logger.error('获取 OIDC Provider 实例失败', { error });
    throw error;
  }
}

/**
 * 用户交互处理中间件
 */
async function handleInteraction(req: any, res: any, next: any) {
  try {
    const provider = getProvider();
    const { uid } = req.params;

    // 获取交互详情
    const interaction = await provider.interactionDetails(req, res);
    
    // 检查用户是否已登录
    const user = req.user;
    if (!user) {
      // 重定向到登录页面
      const loginUrl = `/login?interaction=${uid}`;
      res.redirect(loginUrl);
      return;
    }

    // 用户已登录，完成交互
    const result = {
      login: {
        accountId: user.userId,
        acr: 'urn:mace:incommon:iap:silver',
        amr: ['pwd'],
        remember: false,
        ts: Math.floor(Date.now() / 1000)
      }
    };

    // 如果需要同意，添加同意信息
    if (interaction.prompt.name === 'consent') {
      result.consent = {
        grantId: interaction.grantId,
        rejectedScopes: [],
        rejectedClaims: [],
        replace: false
      };
    }

    await provider.interactionFinished(req, res, result, {
      mergeWithLastSubmission: false
    });

  } catch (error) {
    logger.error('处理用户交互失败', { error });
    next(error);
  }
}

/**
 * 挂载 OIDC Provider 路由
 */
router.use('/oauth2', oidcRateLimit, (req, res, next) => {
  try {
    const provider = getProvider();
    return provider.callback()(req, res, next);
  } catch (error) {
    logger.error('OIDC Provider 回调失败', { error });
    res.status(500).json({
      error: 'server_error',
      error_description: 'OIDC Provider 服务不可用'
    });
  }
});

/**
 * 用户交互端点
 */
router.get('/interaction/:uid', authenticateToken as any, handleInteraction);
router.post('/interaction/:uid', authenticateToken as any, handleInteraction);

/**
 * 管理端点
 */

/**
 * @route GET /oidc/clients
 * @desc 获取 OIDC 客户端列表
 * @access Private (管理员)
 */
router.get('/clients', oidcRateLimit, authenticateToken as any, async (req, res) => {
  try {
    // TODO: 添加管理员权限检查
    
    const { page = 1, limit = 20, search } = req.query;
    const offset = (Number(page) - 1) * Number(limit);

    const where: any = {
      supportedProtocols: {
        has: 'oidc'
      }
    };

    if (search) {
      where.OR = [
        { name: { contains: search as string, mode: 'insensitive' } },
        { clientId: { contains: search as string, mode: 'insensitive' } }
      ];
    }

    const [applications, total] = await Promise.all([
      require('@/config/database').prisma.application.findMany({
        where,
        skip: offset,
        take: Number(limit),
        orderBy: { createdAt: 'desc' },
        select: {
          id: true,
          name: true,
          clientId: true,
          description: true,
          isActive: true,
          redirectUris: true,
          scopes: true,
          grantTypes: true,
          responseTypes: true,
          createdAt: true,
          updatedAt: true
        }
      }),
      require('@/config/database').prisma.application.count({ where })
    ]);

    res.json({
      clients: applications,
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total,
        pages: Math.ceil(total / Number(limit))
      }
    });

  } catch (error) {
    logger.error('获取 OIDC 客户端列表失败', { error });
    res.status(500).json({
      error: 'server_error',
      error_description: '获取客户端列表失败'
    });
  }
});

/**
 * @route POST /oidc/clients
 * @desc 创建 OIDC 客户端
 * @access Private (管理员)
 */
router.post('/clients', oidcRateLimit, authenticateToken as any, async (req, res) => {
  try {
    // TODO: 添加管理员权限检查和输入验证
    
    const {
      name,
      description,
      clientId,
      clientSecret,
      redirectUris = [],
      scopes = ['openid'],
      grantTypes = ['authorization_code'],
      responseTypes = ['code'],
      tokenEndpointAuthMethod = 'client_secret_basic',
      applicationType = 'web',
      subjectType = 'public'
    } = req.body;

    const application = await require('@/config/database').prisma.application.create({
      data: {
        name,
        description,
        clientId,
        clientSecret,
        supportedProtocols: ['oidc'],
        redirectUris,
        scopes,
        grantTypes,
        responseTypes,
        tokenEndpointAuthMethod,
        applicationType,
        subjectType,
        isActive: true
      }
    });

    // 重新加载客户端配置
    await oidcProviderService.reloadClients();

    res.status(201).json({
      message: 'OIDC 客户端创建成功',
      client: {
        id: application.id,
        name: application.name,
        clientId: application.clientId,
        isActive: application.isActive
      }
    });

  } catch (error) {
    logger.error('创建 OIDC 客户端失败', { error });
    res.status(500).json({
      error: 'server_error',
      error_description: '创建客户端失败'
    });
  }
});

/**
 * @route PUT /oidc/clients/:id
 * @desc 更新 OIDC 客户端
 * @access Private (管理员)
 */
router.put('/clients/:id', oidcRateLimit, authenticateToken as any, async (req, res) => {
  try {
    // TODO: 添加管理员权限检查和输入验证
    
    const { id } = req.params;
    const updateData = req.body;

    const application = await require('@/config/database').prisma.application.update({
      where: { id },
      data: {
        ...updateData,
        updatedAt: new Date()
      }
    });

    // 重新加载客户端配置
    await oidcProviderService.reloadClients();

    res.json({
      message: 'OIDC 客户端更新成功',
      client: application
    });

  } catch (error) {
    logger.error('更新 OIDC 客户端失败', { error });
    res.status(500).json({
      error: 'server_error',
      error_description: '更新客户端失败'
    });
  }
});

/**
 * @route DELETE /oidc/clients/:id
 * @desc 删除 OIDC 客户端
 * @access Private (管理员)
 */
router.delete('/clients/:id', oidcRateLimit, authenticateToken as any, async (req, res) => {
  try {
    // TODO: 添加管理员权限检查
    
    const { id } = req.params;

    await require('@/config/database').prisma.application.delete({
      where: { id }
    });

    // 重新加载客户端配置
    await oidcProviderService.reloadClients();

    res.json({
      message: 'OIDC 客户端删除成功'
    });

  } catch (error) {
    logger.error('删除 OIDC 客户端失败', { error });
    res.status(500).json({
      error: 'server_error',
      error_description: '删除客户端失败'
    });
  }
});

/**
 * @route GET /oidc/discovery
 * @desc 获取 OIDC 发现文档
 * @access Public
 */
router.get('/discovery', oidcRateLimit, async (req, res) => {
  try {
    const discovery = oidcProviderService.getDiscoveryDocument();
    res.json(discovery);
  } catch (error) {
    logger.error('获取 OIDC 发现文档失败', { error });
    res.status(500).json({
      error: 'server_error',
      error_description: '获取发现文档失败'
    });
  }
});

/**
 * @route GET /oidc/jwks
 * @desc 获取 JWKS
 * @access Public
 */
router.get('/jwks', oidcRateLimit, async (req, res) => {
  try {
    const jwks = await oidcProviderService.getJWKS();
    res.json(jwks);
  } catch (error) {
    logger.error('获取 JWKS 失败', { error });
    res.status(500).json({
      error: 'server_error',
      error_description: '获取 JWKS 失败'
    });
  }
});

export default router;
