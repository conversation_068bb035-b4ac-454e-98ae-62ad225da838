/**
 * 安全管理路由
 * 提供安全审计、威胁检测、漏洞扫描和合规性检查的API路由
 */

import { Router } from 'express';
import { securityController } from '@/controllers/security.controller';
import { authenticateToken } from '@/middleware/auth.middleware';
import { requireRole } from '@/middleware/rbac.middleware';
import { apiRateLimit } from '@/middleware/security.middleware';
import { responseCache } from '@/middleware/performance.middleware';

const router = Router();

// 应用认证中间件
router.use(authenticateToken);

// 应用限流中间件（安全相关API需要更严格的限流）
router.use(apiRateLimit);

/**
 * 获取安全概览
 * GET /api/v1/security/overview
 * 
 * 查询参数：
 * - startDate: 开始时间（ISO字符串，可选）
 * - endDate: 结束时间（ISO字符串，可选）
 * 
 * 响应格式：
 * {
 *   "success": true,
 *   "data": {
 *     "timestamp": "2024-01-01T12:00:00.000Z",
 *     "securityScore": 85,
 *     "status": "good",
 *     "threatDetection": {
 *       "level": "low",
 *       "riskScore": 15,
 *       "threatCount": 2,
 *       "lastUpdated": "2024-01-01T12:00:00.000Z"
 *     },
 *     "vulnerabilities": {
 *       "total": 5,
 *       "critical": 0,
 *       "high": 1,
 *       "medium": 2,
 *       "low": 2,
 *       "lastScan": "2024-01-01T10:00:00.000Z"
 *     },
 *     "auditActivity": {
 *       "totalEvents": 1500,
 *       "securityEvents": 25,
 *       "failedAttempts": 12,
 *       "suspiciousActivity": 3
 *     },
 *     "compliance": {
 *       "standard": "GDPR",
 *       "compliant": true,
 *       "score": 92,
 *       "checkCount": 15
 *     },
 *     "recommendations": [
 *       "修复高级别安全漏洞",
 *       "加强实时监控机制"
 *     ]
 *   }
 * }
 */
router.get('/overview', 
  requireRole(['admin', 'security_officer']),
  responseCache({ ttl: 300 }), // 5分钟缓存
  securityController.getSecurityOverview
);

/**
 * 获取审计日志
 * GET /api/v1/security/audit-logs
 * 
 * 查询参数：
 * - page: 页码（默认1）
 * - limit: 每页数量（默认50，最大100）
 * - eventType: 事件类型过滤
 * - severity: 严重级别过滤
 * - userId: 用户ID过滤
 * - ipAddress: IP地址过滤
 * - startDate: 开始时间
 * - endDate: 结束时间
 * 
 * 响应格式：
 * {
 *   "success": true,
 *   "data": {
 *     "logs": [
 *       {
 *         "id": "log-123",
 *         "eventType": "login_failed",
 *         "severity": "medium",
 *         "userId": "user-456",
 *         "sessionId": "session-789",
 *         "ipAddress": "*************",
 *         "userAgent": "Mozilla/5.0...",
 *         "resource": "authentication",
 *         "action": "login",
 *         "success": false,
 *         "errorMessage": "Invalid credentials",
 *         "riskScore": 3,
 *         "createdAt": "2024-01-01T12:00:00.000Z"
 *       }
 *     ],
 *     "pagination": {
 *       "page": 1,
 *       "limit": 50,
 *       "total": 1500,
 *       "pages": 30
 *     }
 *   }
 * }
 */
router.get('/audit-logs', 
  requireRole(['admin', 'security_officer', 'auditor']),
  securityController.getAuditLogs
);

/**
 * 获取威胁检测结果
 * GET /api/v1/security/threat-detection
 * 
 * 查询参数：
 * - startDate: 开始时间（ISO字符串，可选）
 * - endDate: 结束时间（ISO字符串，可选）
 * 
 * 响应格式：
 * {
 *   "success": true,
 *   "data": {
 *     "threatLevel": "medium",
 *     "threats": [
 *       {
 *         "type": "brute_force_attack",
 *         "description": "检测到 3 次暴力破解攻击",
 *         "severity": "critical",
 *         "evidence": {
 *           "events": 3,
 *           "timeRange": {
 *             "start": "2024-01-01T00:00:00.000Z",
 *             "end": "2024-01-01T12:00:00.000Z"
 *           }
 *         },
 *         "recommendations": [
 *           "启用账户锁定策略",
 *           "实施IP地址黑名单"
 *         ]
 *       }
 *     ],
 *     "riskScore": 35,
 *     "timestamp": "2024-01-01T12:00:00.000Z"
 *   }
 * }
 */
router.get('/threat-detection', 
  requireRole(['admin', 'security_officer']),
  responseCache({ ttl: 180 }), // 3分钟缓存
  securityController.getThreatDetection
);

/**
 * 执行安全扫描
 * POST /api/v1/security/scan
 * 
 * 请求体：
 * {
 *   "scanType": "full|dependency|configuration|code|infrastructure"
 * }
 * 
 * 响应格式：
 * {
 *   "success": true,
 *   "data": {
 *     "scanId": "scan_1704110400000_a1b2c3d4",
 *     "scanType": "full",
 *     "startTime": "2024-01-01T12:00:00.000Z",
 *     "endTime": "2024-01-01T12:05:00.000Z",
 *     "duration": 300000,
 *     "status": "completed",
 *     "vulnerabilities": [
 *       {
 *         "id": "vuln-123",
 *         "title": "过时的依赖项: lodash",
 *         "description": "lodash 版本过时，可能存在安全风险",
 *         "severity": "medium",
 *         "category": "dependency",
 *         "cve": "CVE-2021-23337",
 *         "affected": "lodash@4.17.20",
 *         "recommendation": "更新 lodash 到最新版本",
 *         "references": ["https://nvd.nist.gov/vuln/detail/CVE-2021-23337"],
 *         "discovered": "2024-01-01T12:02:00.000Z"
 *       }
 *     ],
 *     "summary": {
 *       "total": 5,
 *       "critical": 0,
 *       "high": 1,
 *       "medium": 2,
 *       "low": 2,
 *       "info": 0
 *     },
 *     "recommendations": [
 *       "定期更新依赖项到最新安全版本",
 *       "审查和加强系统配置安全性"
 *     ],
 *     "nextScanRecommended": "2024-01-08T12:00:00.000Z"
 *   }
 * }
 */
router.post('/scan', 
  requireRole(['admin', 'security_officer']),
  securityController.performSecurityScan
);

/**
 * 获取扫描历史
 * GET /api/v1/security/scan-history
 * 
 * 查询参数：
 * - limit: 返回数量限制（默认10，最大50）
 * 
 * 响应格式：
 * {
 *   "success": true,
 *   "data": {
 *     "scans": [
 *       {
 *         "scanId": "scan_1704110400000_a1b2c3d4",
 *         "scanType": "full",
 *         "startTime": "2024-01-01T12:00:00.000Z",
 *         "endTime": "2024-01-01T12:05:00.000Z",
 *         "duration": 300000,
 *         "status": "completed",
 *         "summary": {
 *           "total": 5,
 *           "critical": 0,
 *           "high": 1,
 *           "medium": 2,
 *           "low": 2,
 *           "info": 0
 *         }
 *       }
 *     ],
 *     "total": 25
 *   }
 * }
 */
router.get('/scan-history', 
  requireRole(['admin', 'security_officer']),
  responseCache({ ttl: 600 }), // 10分钟缓存
  securityController.getScanHistory
);

/**
 * 获取合规性检查结果
 * GET /api/v1/security/compliance
 * 
 * 查询参数：
 * - standard: 合规标准（GDPR|SOX|HIPAA|PCI_DSS，默认GDPR）
 * 
 * 响应格式：
 * {
 *   "success": true,
 *   "data": {
 *     "standard": "GDPR",
 *     "compliant": true,
 *     "score": 92,
 *     "checks": [
 *       {
 *         "requirement": "数据处理合法性",
 *         "status": "pass",
 *         "description": "所有数据处理都有合法依据"
 *       },
 *       {
 *         "requirement": "数据保护影响评估",
 *         "status": "warning",
 *         "description": "需要定期进行DPIA评估"
 *       }
 *     ],
 *     "recommendations": [
 *       "定期进行 GDPR 合规性审计",
 *       "建立合规性监控机制"
 *     ],
 *     "timestamp": "2024-01-01T12:00:00.000Z"
 *   }
 * }
 */
router.get('/compliance', 
  requireRole(['admin', 'security_officer', 'compliance_officer']),
  responseCache({ ttl: 3600 }), // 1小时缓存
  securityController.getComplianceCheck
);

/**
 * 获取安全统计
 * GET /api/v1/security/statistics
 * 
 * 查询参数：
 * - startDate: 开始时间（ISO字符串，可选）
 * - endDate: 结束时间（ISO字符串，可选）
 * 
 * 响应格式：
 * {
 *   "success": true,
 *   "data": {
 *     "totalEvents": 1500,
 *     "securityEvents": 25,
 *     "failedAttempts": 12,
 *     "suspiciousActivity": 3,
 *     "timeRange": {
 *       "start": "2023-12-25T12:00:00.000Z",
 *       "end": "2024-01-01T12:00:00.000Z"
 *     },
 *     "eventsByType": [
 *       {
 *         "eventType": "login_success",
 *         "count": 850
 *       },
 *       {
 *         "eventType": "login_failed",
 *         "count": 45
 *       }
 *     ],
 *     "topRiskIPs": [
 *       {
 *         "ipAddress": "*************",
 *         "failedAttempts": 15
 *       }
 *     ],
 *     "securityTrends": {
 *       "message": "安全趋势分析功能待完善"
 *     }
 *   }
 * }
 */
router.get('/statistics', 
  requireRole(['admin', 'security_officer']),
  responseCache({ ttl: 300 }), // 5分钟缓存
  securityController.getSecurityStatistics
);

/**
 * 生成安全报告
 * POST /api/v1/security/report
 * 
 * 请求体：
 * {
 *   "timeRange": {
 *     "start": "2023-12-01T00:00:00.000Z",
 *     "end": "2024-01-01T00:00:00.000Z"
 *   },
 *   "includeDetails": false,
 *   "format": "json|pdf"
 * }
 * 
 * 响应格式：
 * {
 *   "success": true,
 *   "data": {
 *     "reportId": "security-report-1704110400000",
 *     "generatedAt": "2024-01-01T12:00:00.000Z",
 *     "timeRange": {
 *       "start": "2023-12-01T00:00:00.000Z",
 *       "end": "2024-01-01T00:00:00.000Z"
 *     },
 *     "summary": {
 *       "securityScore": 85,
 *       "threatLevel": "low",
 *       "vulnerabilityCount": 5,
 *       "complianceStatus": true
 *     },
 *     "threatDetection": { ... },
 *     "vulnerabilities": { ... },
 *     "auditStatistics": { ... },
 *     "compliance": { ... },
 *     "recommendations": [
 *       "定期更新安全策略和程序",
 *       "加强员工安全意识培训"
 *     ]
 *   }
 * }
 */
router.post('/report',
  requireRole(['admin', 'security_officer']),
  securityController.generateSecurityReport
);

/**
 * 获取自动化扫描计划列表
 * GET /api/v1/security/automated-scans/schedules
 *
 * 响应格式：
 * {
 *   "success": true,
 *   "data": {
 *     "schedules": [
 *       {
 *         "id": "daily-full-scan",
 *         "name": "每日全面安全扫描",
 *         "cronExpression": "0 2 * * *",
 *         "scanType": "full",
 *         "enabled": true,
 *         "lastRun": "2024-01-01T02:00:00.000Z",
 *         "nextRun": "2024-01-02T02:00:00.000Z",
 *         "notificationThreshold": "medium"
 *       }
 *     ],
 *     "totalCount": 4,
 *     "activeCount": 3
 *   }
 * }
 */
router.get('/automated-scans/schedules',
  requireRole(['admin', 'security_officer']),
  responseCache({ ttl: 300 }), // 5分钟缓存
  securityController.getAutomatedScanSchedules
);

/**
 * 获取自动化扫描计划详情
 * GET /api/v1/security/automated-scans/schedules/:scheduleId
 *
 * 响应格式：
 * {
 *   "success": true,
 *   "data": {
 *     "id": "daily-full-scan",
 *     "name": "每日全面安全扫描",
 *     "cronExpression": "0 2 * * *",
 *     "scanType": "full",
 *     "enabled": true,
 *     "lastRun": "2024-01-01T02:00:00.000Z",
 *     "nextRun": "2024-01-02T02:00:00.000Z",
 *     "notificationThreshold": "medium"
 *   }
 * }
 */
router.get('/automated-scans/schedules/:scheduleId',
  requireRole(['admin', 'security_officer']),
  securityController.getAutomatedScanSchedule
);

/**
 * 启用/禁用自动化扫描计划
 * PUT /api/v1/security/automated-scans/schedules/:scheduleId/toggle
 *
 * 请求体：
 * {
 *   "enabled": true
 * }
 *
 * 响应格式：
 * {
 *   "success": true,
 *   "message": "扫描计划已启用",
 *   "data": {
 *     "scheduleId": "daily-full-scan",
 *     "enabled": true
 *   }
 * }
 */
router.put('/automated-scans/schedules/:scheduleId/toggle',
  requireRole(['admin', 'security_officer']),
  securityController.toggleAutomatedScanSchedule
);

/**
 * 手动触发自动化扫描
 * POST /api/v1/security/automated-scans/trigger
 *
 * 请求体：
 * {
 *   "scanType": "full|dependency|configuration|code|infrastructure"
 * }
 *
 * 响应格式：
 * {
 *   "success": true,
 *   "message": "安全扫描已完成",
 *   "data": {
 *     "scanId": "scan_1704110400000_a1b2c3d4",
 *     "scanType": "full",
 *     "startTime": "2024-01-01T12:00:00.000Z",
 *     "endTime": "2024-01-01T12:05:00.000Z",
 *     "duration": 300000,
 *     "status": "completed",
 *     "vulnerabilities": [...],
 *     "summary": {
 *       "total": 5,
 *       "critical": 0,
 *       "high": 1,
 *       "medium": 2,
 *       "low": 2,
 *       "info": 0
 *     }
 *   }
 * }
 */
router.post('/automated-scans/trigger',
  requireRole(['admin', 'security_officer']),
  securityController.triggerAutomatedScan
);

export default router;
