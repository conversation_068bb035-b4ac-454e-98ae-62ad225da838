/**
 * 系统配置路由
 * 提供动态系统配置管理的API接口
 */

import { Router } from 'express';
import { systemConfigService } from '@/services/system-config.service';
import { logger } from '@/config/logger';
import { authenticateToken } from '@/middleware/auth.middleware';
import rateLimit from 'express-rate-limit';
import { body, param, query, validationResult } from 'express-validator';

const router = Router();

// 系统配置API速率限制
const configApiRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 100, // 每个IP最多100次配置API请求
  message: {
    error: 'config_api_rate_limit_exceeded',
    message: '配置API请求过于频繁，请稍后再试'
  },
  standardHeaders: true,
  legacyHeaders: false
});

/**
 * 验证中间件
 */
const handleValidationErrors = (req: any, res: any, next: any) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      error: 'validation_error',
      message: '请求参数验证失败',
      details: errors.array()
    });
  }
  next();
};

/**
 * 管理员权限检查中间件
 */
const requireAdminPermission = (req: any, res: any, next: any) => {
  const user = req.user;
  
  if (!user) {
    return res.status(401).json({
      error: 'unauthorized',
      message: '未认证'
    });
  }

  // 检查管理员权限
  if (!user.isAdmin && !user.permissions?.includes('admin.config')) {
    return res.status(403).json({
      error: 'forbidden',
      message: '需要配置管理权限'
    });
  }

  next();
};

/**
 * @route GET /config/:key
 * @desc 获取单个配置项
 * @access Private (管理员)
 */
router.get('/:key',
  configApiRateLimit,
  authenticateToken as any,
  requireAdminPermission,
  [
    param('key').isString().notEmpty()
  ],
  handleValidationErrors,
  async (req, res) => {
    try {
      const { key } = req.params;

      const value = await systemConfigService.getConfig(key);

      if (value === null) {
        return res.status(404).json({
          error: 'config_not_found',
          message: '配置项不存在'
        });
      }

      res.json({
        key,
        value,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      logger.error('获取配置失败', { error, key: req.params.key });
      res.status(500).json({
        error: 'server_error',
        message: '获取配置失败'
      });
    }
  }
);

/**
 * @route GET /config/category/:category
 * @desc 获取分类配置
 * @access Private (管理员)
 */
router.get('/category/:category',
  configApiRateLimit,
  authenticateToken as any,
  requireAdminPermission,
  [
    param('category').isString().notEmpty()
  ],
  handleValidationErrors,
  async (req, res) => {
    try {
      const { category } = req.params;

      const configs = await systemConfigService.getConfigsByCategory(category);

      res.json({
        category,
        configs,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      logger.error('获取分类配置失败', { error, category: req.params.category });
      res.status(500).json({
        error: 'server_error',
        message: '获取分类配置失败'
      });
    }
  }
);

/**
 * @route POST /config/batch
 * @desc 批量获取配置
 * @access Private (管理员)
 */
router.post('/batch',
  configApiRateLimit,
  authenticateToken as any,
  requireAdminPermission,
  [
    body('keys').isArray().notEmpty(),
    body('keys.*').isString().notEmpty()
  ],
  handleValidationErrors,
  async (req, res) => {
    try {
      const { keys } = req.body;

      const configs = await systemConfigService.getConfigs(keys);

      res.json({
        configs,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      logger.error('批量获取配置失败', { error, keys: req.body.keys });
      res.status(500).json({
        error: 'server_error',
        message: '批量获取配置失败'
      });
    }
  }
);

/**
 * @route PUT /config/:key
 * @desc 设置单个配置项
 * @access Private (管理员)
 */
router.put('/:key',
  configApiRateLimit,
  authenticateToken as any,
  requireAdminPermission,
  [
    param('key').isString().notEmpty(),
    body('value').exists(),
    body('changeReason').optional().isString()
  ],
  handleValidationErrors,
  async (req, res) => {
    try {
      const { key } = req.params;
      const { value, changeReason } = req.body;
      const changedBy = req.user.id;

      await systemConfigService.setConfig(key, value, changedBy, changeReason);

      res.json({
        success: true,
        message: '配置更新成功',
        key,
        value,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      logger.error('设置配置失败', { error, key: req.params.key });
      
      if (error.message.includes('不存在')) {
        res.status(404).json({
          error: 'config_not_found',
          message: error.message
        });
      } else if (error.message.includes('验证') || error.message.includes('格式')) {
        res.status(400).json({
          error: 'validation_error',
          message: error.message
        });
      } else {
        res.status(500).json({
          error: 'server_error',
          message: '设置配置失败'
        });
      }
    }
  }
);

/**
 * @route PUT /config/batch
 * @desc 批量设置配置
 * @access Private (管理员)
 */
router.put('/batch',
  configApiRateLimit,
  authenticateToken as any,
  requireAdminPermission,
  [
    body('configs').isObject().notEmpty(),
    body('changeReason').optional().isString()
  ],
  handleValidationErrors,
  async (req, res) => {
    try {
      const { configs, changeReason } = req.body;
      const changedBy = req.user.id;

      await systemConfigService.setConfigs(configs, changedBy, changeReason);

      res.json({
        success: true,
        message: '批量配置更新成功',
        count: Object.keys(configs).length,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      logger.error('批量设置配置失败', { error });
      
      if (error.message.includes('验证') || error.message.includes('格式')) {
        res.status(400).json({
          error: 'validation_error',
          message: error.message
        });
      } else {
        res.status(500).json({
          error: 'server_error',
          message: '批量设置配置失败'
        });
      }
    }
  }
);

/**
 * @route POST /config
 * @desc 创建配置项
 * @access Private (管理员)
 */
router.post('/',
  configApiRateLimit,
  authenticateToken as any,
  requireAdminPermission,
  [
    body('key').isString().notEmpty(),
    body('value').exists(),
    body('type').isIn(['string', 'number', 'boolean', 'json', 'array']),
    body('category').isString().notEmpty(),
    body('description').isString().notEmpty(),
    body('isPublic').optional().isBoolean(),
    body('isRequired').optional().isBoolean(),
    body('defaultValue').optional(),
    body('validationRules').optional().isObject(),
    body('metadata').optional().isObject()
  ],
  handleValidationErrors,
  async (req, res) => {
    try {
      const configData = req.body;

      const config = await systemConfigService.createConfig(configData);

      res.status(201).json({
        success: true,
        message: '配置项创建成功',
        config,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      logger.error('创建配置项失败', { error, key: req.body.key });
      
      if (error.code === 'P2002') { // Prisma unique constraint error
        res.status(409).json({
          error: 'config_exists',
          message: '配置项已存在'
        });
      } else {
        res.status(500).json({
          error: 'server_error',
          message: '创建配置项失败'
        });
      }
    }
  }
);

/**
 * @route GET /config/:key/history
 * @desc 获取配置变更历史
 * @access Private (管理员)
 */
router.get('/:key/history',
  configApiRateLimit,
  authenticateToken as any,
  requireAdminPermission,
  [
    param('key').isString().notEmpty(),
    query('limit').optional().isInt({ min: 1, max: 100 }),
    query('offset').optional().isInt({ min: 0 })
  ],
  handleValidationErrors,
  async (req, res) => {
    try {
      const { key } = req.params;
      const limit = parseInt(req.query.limit as string) || 50;
      const offset = parseInt(req.query.offset as string) || 0;

      const history = await systemConfigService.getConfigHistory(key, limit, offset);

      res.json({
        key,
        history,
        pagination: {
          limit,
          offset,
          count: history.length
        },
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      logger.error('获取配置变更历史失败', { error, key: req.params.key });
      res.status(500).json({
        error: 'server_error',
        message: '获取配置变更历史失败'
      });
    }
  }
);

/**
 * @route GET /config/history/all
 * @desc 获取所有配置变更历史
 * @access Private (管理员)
 */
router.get('/history/all',
  configApiRateLimit,
  authenticateToken as any,
  requireAdminPermission,
  [
    query('limit').optional().isInt({ min: 1, max: 100 }),
    query('offset').optional().isInt({ min: 0 })
  ],
  handleValidationErrors,
  async (req, res) => {
    try {
      const limit = parseInt(req.query.limit as string) || 50;
      const offset = parseInt(req.query.offset as string) || 0;

      const history = await systemConfigService.getConfigHistory(undefined, limit, offset);

      res.json({
        history,
        pagination: {
          limit,
          offset,
          count: history.length
        },
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      logger.error('获取所有配置变更历史失败', { error });
      res.status(500).json({
        error: 'server_error',
        message: '获取配置变更历史失败'
      });
    }
  }
);

export default router;
