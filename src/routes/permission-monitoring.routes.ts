/**
 * 权限监控路由
 * 提供权限使用追踪、异常行为告警和合规性检查的API接口
 */

import { Router } from 'express';
import { 
  permissionMonitoringService,
  PermissionAnomalyType
} from '@/services/permission-monitoring.service';
import { logger } from '@/config/logger';
import { authenticateToken } from '@/middleware/auth.middleware';
import rateLimit from 'express-rate-limit';
import { body, param, query, validationResult } from 'express-validator';

const router = Router();

// 权限监控API速率限制
const monitoringRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 200, // 每个IP最多200次监控请求
  message: {
    error: 'monitoring_rate_limit_exceeded',
    message: '权限监控API请求过于频繁，请稍后再试'
  },
  standardHeaders: true,
  legacyHeaders: false
});

/**
 * 验证中间件
 */
const handleValidationErrors = (req: any, res: any, next: any) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      error: 'validation_error',
      message: '请求参数验证失败',
      details: errors.array()
    });
  }
  next();
};

/**
 * 权限监控权限检查中间件
 */
const requireMonitoringPermission = (req: any, res: any, next: any) => {
  const user = req.user;
  
  if (!user) {
    return res.status(401).json({
      error: 'unauthorized',
      message: '未认证'
    });
  }

  // 检查权限监控权限
  if (!user.isAdmin && !user.permissions?.includes('permission.monitor')) {
    return res.status(403).json({
      error: 'forbidden',
      message: '需要权限监控权限'
    });
  }

  next();
};

/**
 * @route POST /permission-monitoring/record-usage
 * @desc 记录权限使用
 * @access Private (系统内部调用)
 */
router.post('/record-usage',
  monitoringRateLimit,
  authenticateToken as any,
  [
    body('userId').isUUID(),
    body('resourceType').isString().notEmpty(),
    body('resourceId').isString().notEmpty(),
    body('action').isString().notEmpty(),
    body('result').isIn(['granted', 'denied']),
    body('reason').isString().notEmpty(),
    body('context').isObject()
  ],
  handleValidationErrors,
  async (req, res) => {
    try {
      const { userId, resourceType, resourceId, action, result, reason, context } = req.body;

      await permissionMonitoringService.recordPermissionUsage(
        userId,
        resourceType,
        resourceId,
        action,
        result,
        reason,
        context
      );

      res.json({
        success: true,
        message: '权限使用记录已保存',
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      logger.error('记录权限使用失败', { error, body: req.body });
      res.status(500).json({
        error: 'server_error',
        message: '记录权限使用失败'
      });
    }
  }
);

/**
 * @route GET /permission-monitoring/usage-statistics
 * @desc 获取权限使用统计
 * @access Private (权限监控)
 */
router.get('/usage-statistics',
  monitoringRateLimit,
  authenticateToken as any,
  requireMonitoringPermission,
  [
    query('startDate').isISO8601(),
    query('endDate').isISO8601(),
    query('userId').optional().isUUID(),
    query('resourceType').optional().isString(),
    query('action').optional().isString()
  ],
  handleValidationErrors,
  async (req, res) => {
    try {
      const { startDate, endDate, userId, resourceType, action } = req.query;

      const timeRange = {
        start: new Date(startDate as string),
        end: new Date(endDate as string)
      };

      const filters = {
        userId: userId as string,
        resourceType: resourceType as string,
        action: action as string
      };

      // 移除undefined值
      Object.keys(filters).forEach(key => {
        if (filters[key as keyof typeof filters] === undefined) {
          delete filters[key as keyof typeof filters];
        }
      });

      const statistics = await permissionMonitoringService.getPermissionUsageStatistics(
        timeRange,
        Object.keys(filters).length > 0 ? filters : undefined
      );

      res.json({
        success: true,
        statistics,
        timeRange,
        filters,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      logger.error('获取权限使用统计失败', { error, query: req.query });
      res.status(500).json({
        error: 'server_error',
        message: '获取权限使用统计失败'
      });
    }
  }
);

/**
 * @route GET /permission-monitoring/anomalies
 * @desc 获取权限异常列表
 * @access Private (权限监控)
 */
router.get('/anomalies',
  monitoringRateLimit,
  authenticateToken as any,
  requireMonitoringPermission,
  [
    query('type').optional().isIn(Object.values(PermissionAnomalyType)),
    query('severity').optional().isIn(['low', 'medium', 'high', 'critical']),
    query('userId').optional().isUUID(),
    query('resolved').optional().isBoolean(),
    query('limit').optional().isInt({ min: 1, max: 100 }),
    query('offset').optional().isInt({ min: 0 })
  ],
  handleValidationErrors,
  async (req, res) => {
    try {
      const filters = {
        type: req.query.type as PermissionAnomalyType,
        severity: req.query.severity as string,
        userId: req.query.userId as string,
        resolved: req.query.resolved === 'true',
        limit: parseInt(req.query.limit as string) || 50,
        offset: parseInt(req.query.offset as string) || 0
      };

      // 移除undefined值
      Object.keys(filters).forEach(key => {
        if (filters[key as keyof typeof filters] === undefined) {
          delete filters[key as keyof typeof filters];
        }
      });

      const result = await permissionMonitoringService.getPermissionAnomalies(filters);

      res.json({
        success: true,
        anomalies: result.anomalies,
        total: result.total,
        pagination: {
          limit: filters.limit,
          offset: filters.offset
        },
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      logger.error('获取权限异常列表失败', { error, query: req.query });
      res.status(500).json({
        error: 'server_error',
        message: '获取权限异常列表失败'
      });
    }
  }
);

/**
 * @route POST /permission-monitoring/anomalies/:anomalyId/resolve
 * @desc 解决权限异常
 * @access Private (权限监控)
 */
router.post('/anomalies/:anomalyId/resolve',
  monitoringRateLimit,
  authenticateToken as any,
  requireMonitoringPermission,
  [
    param('anomalyId').isUUID(),
    body('resolution').isString().notEmpty(),
    body('falsePositive').optional().isBoolean()
  ],
  handleValidationErrors,
  async (req, res) => {
    try {
      const { anomalyId } = req.params;
      const { resolution, falsePositive = false } = req.body;
      const resolvedBy = req.user.id;

      await permissionMonitoringService.resolveAnomaly(
        anomalyId,
        resolvedBy,
        resolution,
        falsePositive
      );

      res.json({
        success: true,
        message: '权限异常已解决',
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      logger.error('解决权限异常失败', { error, anomalyId: req.params.anomalyId });
      res.status(500).json({
        error: 'server_error',
        message: '解决权限异常失败'
      });
    }
  }
);

/**
 * @route POST /permission-monitoring/compliance/check
 * @desc 执行合规性检查
 * @access Private (权限监控)
 */
router.post('/compliance/check',
  monitoringRateLimit,
  authenticateToken as any,
  requireMonitoringPermission,
  [
    body('ruleIds').optional().isArray(),
    body('ruleIds.*').optional().isUUID()
  ],
  handleValidationErrors,
  async (req, res) => {
    try {
      const { ruleIds } = req.body;

      const results = await permissionMonitoringService.runComplianceChecks(ruleIds);

      res.json({
        success: true,
        results,
        count: results.length,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      logger.error('执行合规性检查失败', { error, body: req.body });
      res.status(500).json({
        error: 'server_error',
        message: '执行合规性检查失败'
      });
    }
  }
);

/**
 * @route GET /permission-monitoring/compliance/report
 * @desc 获取合规性报告
 * @access Private (权限监控)
 */
router.get('/compliance/report',
  monitoringRateLimit,
  authenticateToken as any,
  requireMonitoringPermission,
  [
    query('startDate').optional().isISO8601(),
    query('endDate').optional().isISO8601()
  ],
  handleValidationErrors,
  async (req, res) => {
    try {
      const { startDate, endDate } = req.query;

      let timeRange;
      if (startDate && endDate) {
        timeRange = {
          start: new Date(startDate as string),
          end: new Date(endDate as string)
        };
      }

      const report = await permissionMonitoringService.getComplianceReport(timeRange);

      res.json({
        success: true,
        report,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      logger.error('获取合规性报告失败', { error, query: req.query });
      res.status(500).json({
        error: 'server_error',
        message: '获取合规性报告失败'
      });
    }
  }
);

/**
 * @route GET /permission-monitoring/dashboard
 * @desc 获取权限监控仪表板数据
 * @access Private (权限监控)
 */
router.get('/dashboard',
  monitoringRateLimit,
  authenticateToken as any,
  requireMonitoringPermission,
  async (req, res) => {
    try {
      const now = new Date();
      const last24Hours = new Date(now.getTime() - 24 * 60 * 60 * 1000);
      const last7Days = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

      // 获取最近24小时的统计
      const [
        recentStats,
        weeklyStats,
        activeAnomalies,
        complianceReport
      ] = await Promise.all([
        permissionMonitoringService.getPermissionUsageStatistics({
          start: last24Hours,
          end: now
        }),
        permissionMonitoringService.getPermissionUsageStatistics({
          start: last7Days,
          end: now
        }),
        permissionMonitoringService.getPermissionAnomalies({
          resolved: false,
          limit: 10
        }),
        permissionMonitoringService.getComplianceReport({
          start: last7Days,
          end: now
        })
      ]);

      const dashboard = {
        overview: {
          recent24h: recentStats.overview,
          weekly: weeklyStats.overview,
          activeAnomalies: activeAnomalies.total,
          complianceScore: complianceReport.overview.complianceScore
        },
        recentAnomalies: activeAnomalies.anomalies.slice(0, 5),
        topUsers: recentStats.topUsers.slice(0, 5),
        topResources: recentStats.topResources.slice(0, 5),
        complianceSummary: complianceReport.overview
      };

      res.json({
        success: true,
        dashboard,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      logger.error('获取权限监控仪表板数据失败', { error });
      res.status(500).json({
        error: 'server_error',
        message: '获取仪表板数据失败'
      });
    }
  }
);

export default router;
