/**
 * API 文档路由
 * 提供 API 文档和接口说明
 */

import { Router, Request, Response } from 'express';
import { logger } from '@/config/logger';
import { config } from '@/config';

const router = Router();

/**
 * @route GET /api/docs
 * @desc 获取 API 文档主页
 * @access Public
 */
router.get('/docs', (req: Request, res: Response) => {
  try {
    const apiDocumentation = {
      title: 'ID Provider API 文档',
      version: '1.0.0',
      description: '身份提供商服务的完整 API 文档',
      baseUrl: `http://localhost:${config.server.port}`,
      
      // API 分类
      categories: {
        authentication: {
          title: '认证服务',
          description: '用户登录、注册、令牌管理等认证相关接口',
          endpoints: [
            'POST /api/v1/auth/login - 用户登录',
            'POST /api/v1/auth/register - 用户注册',
            'POST /api/v1/auth/logout - 用户登出',
            'POST /api/v1/auth/refresh - 刷新令牌',
            'POST /api/v1/auth/validate-token - 验证令牌',
            'POST /api/v1/auth/introspect - 令牌内省'
          ]
        },
        
        user: {
          title: '用户管理',
          description: '用户信息管理、个人资料等接口',
          endpoints: [
            'GET /api/v1/me/profile - 获取用户资料',
            'PUT /api/v1/me/profile - 更新用户资料',
            'POST /api/v1/me/change-password - 修改密码',
            'GET /api/v1/me/sessions - 获取用户会话',
            'DELETE /api/v1/me/sessions/:id - 删除指定会话'
          ]
        },
        
        oauth: {
          title: 'OAuth 2.0',
          description: 'OAuth 2.0 授权服务接口',
          endpoints: [
            'GET /api/v1/auth/authorize - 授权端点',
            'POST /api/v1/auth/token - 令牌端点',
            'GET /api/v1/auth/userinfo - 用户信息端点',
            'POST /api/v1/auth/revoke - 令牌撤销'
          ]
        },
        
        oidc: {
          title: 'OpenID Connect',
          description: 'OpenID Connect 身份认证接口',
          endpoints: [
            'GET /.well-known/openid-configuration - 发现端点',
            'GET /.well-known/jwks.json - JWKS 端点',
            'GET /oauth2/auth - OIDC 授权端点',
            'POST /oauth2/token - OIDC 令牌端点'
          ]
        },
        
        saml: {
          title: 'SAML 2.0',
          description: 'SAML 2.0 身份提供商接口',
          endpoints: [
            'GET /saml/metadata - SAML 元数据',
            'GET /saml/sso - SSO 端点',
            'POST /saml/sso - SSO 端点 (POST)',
            'GET /saml/sls - 单点登出'
          ]
        },
        
        gateway: {
          title: '网关集成',
          description: 'API 网关集成相关接口',
          endpoints: [
            'GET /api/v1/gateway/documentation - 网关集成文档',
            'GET /api/v1/gateway/config/:type - 获取网关配置',
            'GET /api/v1/gateway/examples/:type - 获取配置示例',
            'POST /api/v1/gateway/test - 测试网关集成'
          ]
        },
        
        monitoring: {
          title: '监控和指标',
          description: '系统监控、性能指标等接口',
          endpoints: [
            'GET /health - 健康检查',
            'GET /api/v1/monitoring/metrics - 性能指标',
            'GET /api/v1/monitoring/health - 详细健康状态',
            'GET /metrics - Prometheus 指标'
          ]
        },
        
        i18n: {
          title: '国际化',
          description: '多语言支持相关接口',
          endpoints: [
            'GET /api/v1/i18n/languages - 支持的语言列表',
            'GET /api/v1/i18n/translations - 翻译资源',
            'POST /api/v1/i18n/switch - 切换语言',
            'GET /api/v1/i18n/localization - 本地化配置'
          ]
        }
      },
      
      // 快速开始指南
      quickStart: {
        title: '快速开始',
        steps: [
          '1. 用户注册: POST /api/v1/auth/register',
          '2. 用户登录: POST /api/v1/auth/login',
          '3. 获取用户信息: GET /api/v1/me/profile',
          '4. 使用 JWT 令牌访问受保护的资源'
        ]
      },
      
      // 认证说明
      authentication: {
        title: '认证方式',
        description: '本 API 支持多种认证方式',
        methods: [
          {
            type: 'Bearer Token',
            description: '在请求头中添加: Authorization: Bearer <token>',
            example: 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...'
          },
          {
            type: 'Cookie',
            description: '使用会话 Cookie 进行认证',
            example: 'Cookie: session=abc123...'
          }
        ]
      },
      
      // 错误代码说明
      errorCodes: {
        title: '错误代码',
        codes: {
          400: 'Bad Request - 请求参数错误',
          401: 'Unauthorized - 未授权，需要登录',
          403: 'Forbidden - 禁止访问，权限不足',
          404: 'Not Found - 资源不存在',
          429: 'Too Many Requests - 请求过于频繁',
          500: 'Internal Server Error - 服务器内部错误'
        }
      },
      
      // 相关链接
      links: {
        gatewayDocs: `/api/v1/gateway/documentation`,
        healthCheck: `/health`,
        metrics: `/api/v1/monitoring/metrics`,
        oidcDiscovery: `/.well-known/openid-configuration`,
        jwks: `/.well-known/jwks.json`,
        samlMetadata: `/saml/metadata`
      }
    };

    res.status(200).json(apiDocumentation);

  } catch (error) {
    logger.error('获取 API 文档失败', { error });
    
    res.status(500).json({
      error: 'server_error',
      message: '服务器内部错误'
    });
  }
});

/**
 * @route GET /api/docs/openapi
 * @desc 获取 OpenAPI 规范
 * @access Public
 */
router.get('/docs/openapi', (req: Request, res: Response) => {
  try {
    const openApiSpec = {
      openapi: '3.0.0',
      info: {
        title: 'ID Provider API',
        version: '1.0.0',
        description: '身份提供商服务 API',
        contact: {
          name: 'API Support',
          email: '<EMAIL>'
        }
      },
      servers: [
        {
          url: `http://localhost:${config.server.port}`,
          description: '开发服务器'
        }
      ],
      paths: {
        '/health': {
          get: {
            summary: '健康检查',
            description: '检查服务器健康状态',
            responses: {
              '200': {
                description: '服务正常',
                content: {
                  'application/json': {
                    schema: {
                      type: 'object',
                      properties: {
                        status: { type: 'string', example: 'healthy' },
                        timestamp: { type: 'string', format: 'date-time' }
                      }
                    }
                  }
                }
              }
            }
          }
        },
        '/api/v1/auth/login': {
          post: {
            summary: '用户登录',
            description: '使用用户名和密码进行登录',
            requestBody: {
              required: true,
              content: {
                'application/json': {
                  schema: {
                    type: 'object',
                    required: ['username', 'password'],
                    properties: {
                      username: { type: 'string', example: '<EMAIL>' },
                      password: { type: 'string', example: 'password123' }
                    }
                  }
                }
              }
            },
            responses: {
              '200': {
                description: '登录成功',
                content: {
                  'application/json': {
                    schema: {
                      type: 'object',
                      properties: {
                        access_token: { type: 'string' },
                        token_type: { type: 'string', example: 'Bearer' },
                        expires_in: { type: 'number', example: 3600 }
                      }
                    }
                  }
                }
              },
              '401': {
                description: '认证失败'
              }
            }
          }
        }
      },
      components: {
        securitySchemes: {
          bearerAuth: {
            type: 'http',
            scheme: 'bearer',
            bearerFormat: 'JWT'
          }
        }
      }
    };

    res.status(200).json(openApiSpec);

  } catch (error) {
    logger.error('获取 OpenAPI 规范失败', { error });
    
    res.status(500).json({
      error: 'server_error',
      message: '服务器内部错误'
    });
  }
});

export default router;
