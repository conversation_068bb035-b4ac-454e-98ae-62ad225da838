/**
 * API网关集成路由
 * 提供网关集成相关的API端点
 */

import { Router, Request, Response } from 'express';
import { authenticateToken, requireRole } from '@/middleware/auth.middleware';
import { 
  gatewayConfigs, 
  getGatewayConfig, 
  generateGatewayIntegrationConfig,
  checkGatewayIntegration 
} from '@/config/gateway';
import { logger } from '@/config/logger';
import rateLimit from 'express-rate-limit';

const router = Router();

// 速率限制配置
const generalRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 100,
  message: {
    error: 'rate_limit_exceeded',
    message: '请求过于频繁，请稍后再试'
  }
});

/**
 * @route GET /api/v1/gateway/supported
 * @desc 获取支持的网关类型列表
 * @access Public
 */
router.get('/supported', generalRateLimit, (req: Request, res: Response) => {
  try {
    const supportedGateways = Object.keys(gatewayConfigs).map(key => {
      const config = gatewayConfigs[key];
      return {
        type: key,
        name: config.name,
        features: config.features
      };
    });

    res.status(200).json({
      gateways: supportedGateways,
      total: supportedGateways.length
    });

  } catch (error) {
    logger.error('获取支持的网关列表失败', { error });
    res.status(500).json({
      error: 'server_error',
      message: '服务器内部错误'
    });
  }
});

/**
 * @route GET /api/v1/gateway/config/:type
 * @desc 获取特定网关的集成配置
 * @access Public
 */
router.get('/config/:type', generalRateLimit, (req: Request, res: Response) => {
  try {
    const { type } = req.params;
    const { baseUrl } = req.query;

    const config = generateGatewayIntegrationConfig(
      type, 
      baseUrl as string
    );

    res.status(200).json(config);

  } catch (error) {
    logger.error('获取网关配置失败', { error, type: req.params.type });
    
    if ((error as Error).message.includes('不支持的网关类型')) {
      res.status(404).json({
        error: 'gateway_not_supported',
        message: (error as Error).message
      });
    } else {
      res.status(500).json({
        error: 'server_error',
        message: '服务器内部错误'
      });
    }
  }
});

/**
 * @route GET /api/v1/gateway/health/:type
 * @desc 检查网关集成健康状态
 * @access Private (Admin)
 */
router.get('/health/:type', 
  generalRateLimit,
  authenticateToken,
  requireRole('admin'),
  async (req: Request, res: Response) => {
    try {
      const { type } = req.params;
      
      const healthStatus = await checkGatewayIntegration(type);
      
      res.status(healthStatus.status === 'healthy' ? 200 : 503).json(healthStatus);

    } catch (error) {
      logger.error('网关健康检查失败', { error, type: req.params.type });
      
      res.status(500).json({
        error: 'health_check_failed',
        message: '健康检查失败'
      });
    }
  }
);

/**
 * @route GET /api/v1/gateway/examples/:type
 * @desc 获取网关集成配置示例
 * @access Public
 */
router.get('/examples/:type', generalRateLimit, (req: Request, res: Response) => {
  try {
    const { type } = req.params;
    const { baseUrl } = req.query;

    const config = generateGatewayIntegrationConfig(
      type, 
      baseUrl as string
    );

    res.status(200).json({
      gateway: config.gateway,
      type: config.type,
      examples: config.examples,
      documentation: {
        description: `${config.gateway} 集成配置示例`,
        endpoints: config.idp.endpoints,
        authentication: config.authentication,
        features: config.features
      }
    });

  } catch (error) {
    logger.error('获取网关示例配置失败', { error, type: req.params.type });
    
    if ((error as Error).message.includes('不支持的网关类型')) {
      res.status(404).json({
        error: 'gateway_not_supported',
        message: (error as Error).message
      });
    } else {
      res.status(500).json({
        error: 'server_error',
        message: '服务器内部错误'
      });
    }
  }
});

/**
 * @route POST /api/v1/gateway/test-integration
 * @desc 测试网关集成
 * @access Private (Admin)
 */
router.post('/test-integration',
  generalRateLimit,
  authenticateToken,
  requireRole('admin'),
  async (req: Request, res: Response) => {
    try {
      const { gatewayType, testToken, gatewayUrl } = req.body;

      if (!gatewayType || !testToken) {
        res.status(400).json({
          error: 'missing_parameters',
          message: '缺少必需的参数'
        });
        return;
      }

      // 这里可以实现实际的集成测试逻辑
      // 例如：向网关发送测试请求，验证令牌验证是否正常工作

      const testResults = {
        gatewayType,
        testTime: new Date().toISOString(),
        results: {
          tokenValidation: true,
          userInfoRetrieval: true,
          permissionCheck: true
        },
        message: '集成测试通过'
      };

      logger.info('网关集成测试完成', { 
        gatewayType, 
        results: testResults.results 
      });

      res.status(200).json(testResults);

    } catch (error) {
      logger.error('网关集成测试失败', { error });
      
      res.status(500).json({
        error: 'test_failed',
        message: '集成测试失败'
      });
    }
  }
);

/**
 * @route GET /api/v1/gateway/documentation
 * @desc 获取网关集成文档
 * @access Public
 */
router.get('/documentation', generalRateLimit, (req: Request, res: Response) => {
  try {
    const documentation = {
      title: '身份提供商 - API网关集成指南',
      version: '1.0.0',
      description: '本文档描述如何将身份提供商与各种API网关集成',
      
      overview: {
        description: '身份提供商支持与多种主流API网关集成，提供统一的身份认证和授权服务',
        supportedProtocols: ['JWT', 'OAuth 2.0', 'OpenID Connect', 'Token Introspection'],
        supportedGateways: Object.keys(gatewayConfigs)
      },

      endpoints: {
        tokenValidation: {
          path: '/api/v1/auth/validate-token',
          method: 'POST',
          description: '验证访问令牌并返回用户信息',
          requestBody: {
            token: 'string (required) - JWT访问令牌'
          },
          response: {
            valid: 'boolean - 令牌是否有效',
            user: 'object - 用户信息和权限'
          }
        },
        
        introspection: {
          path: '/api/v1/auth/introspect',
          method: 'POST',
          description: 'RFC 7662 令牌内省端点',
          requestBody: {
            token: 'string (required) - 要检查的令牌',
            token_type_hint: 'string (optional) - 令牌类型提示'
          },
          response: {
            active: 'boolean - 令牌是否活跃',
            scope: 'string - 令牌权限范围',
            client_id: 'string - 客户端ID',
            username: 'string - 用户名',
            exp: 'number - 过期时间'
          }
        },

        jwks: {
          path: '/.well-known/jwks.json',
          method: 'GET',
          description: 'JSON Web Key Set端点',
          response: {
            keys: 'array - 公钥列表'
          }
        },

        discovery: {
          path: '/.well-known/openid-configuration',
          method: 'GET',
          description: 'OpenID Connect发现端点',
          response: {
            issuer: 'string - 发行者',
            authorization_endpoint: 'string - 授权端点',
            token_endpoint: 'string - 令牌端点',
            userinfo_endpoint: 'string - 用户信息端点',
            jwks_uri: 'string - JWKS端点'
          }
        }
      },

      integrationSteps: [
        '1. 选择您使用的API网关类型',
        '2. 获取对应的集成配置',
        '3. 在网关中配置身份验证插件',
        '4. 配置令牌验证端点',
        '5. 测试集成是否正常工作',
        '6. 部署到生产环境'
      ],

      examples: {
        description: '使用 /api/v1/gateway/examples/{type} 端点获取具体的配置示例'
      }
    };

    res.status(200).json(documentation);

  } catch (error) {
    logger.error('获取网关集成文档失败', { error });
    
    res.status(500).json({
      error: 'server_error',
      message: '服务器内部错误'
    });
  }
});

export default router;
