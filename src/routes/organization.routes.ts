/**
 * 组织架构管理路由
 * 提供组织架构的CRUD操作和权限管理API
 */

import { Router } from 'express';
import { body, param, query, validationResult } from 'express-validator';
import { authenticateToken } from '@/middleware/auth.middleware';
import { requireOrganizationPermission, requireOrganizationMembership } from '@/middleware/organization-permission.middleware';
import { organizationService, OrganizationType, OrganizationStatus, DataIsolationLevel } from '@/services/organization.service';
import { organizationPermissionService } from '@/services/organization-permission.service';
import { logger } from '@/config/logger';
import rateLimit from 'express-rate-limit';

const router = Router();

// 速率限制配置
const organizationRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 100, // 每个IP最多100个请求
  message: {
    success: false,
    error: 'RATE_LIMIT_EXCEEDED',
    message: '请求过于频繁，请稍后再试'
  }
});

// 验证错误处理中间件
const handleValidationErrors = (req: any, res: any, next: any) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      error: 'VALIDATION_ERROR',
      message: '请求参数验证失败',
      details: errors.array()
    });
  }
  next();
};

/**
 * @route POST /organizations
 * @desc 创建组织
 * @access Private (需要组织管理权限)
 */
router.post('/',
  organizationRateLimit,
  authenticateToken as any,
  [
    body('name')
      .notEmpty()
      .withMessage('组织名称不能为空')
      .matches(/^[a-zA-Z0-9_-]+$/)
      .withMessage('组织名称只能包含字母、数字、下划线和连字符'),
    body('displayName').notEmpty().withMessage('显示名称不能为空'),
    body('description').optional().isString(),
    body('parentId').optional().isUUID().withMessage('父组织ID格式无效'),
    body('type').isIn(['company', 'division', 'department', 'team', 'group', 'project']).withMessage('组织类型无效'),
    body('status').optional().isIn(['active', 'inactive', 'archived']).withMessage('组织状态无效'),
    body('permissionInheritance').optional().isBoolean(),
    body('dataIsolationLevel').optional().isIn(['strict', 'inherit', 'none']).withMessage('数据隔离级别无效')
  ],
  handleValidationErrors,
  async (req, res) => {
    try {
      const userId = req.user?.id;
      
      // 如果有父组织，检查是否有在父组织中创建子组织的权限
      if (req.body.parentId) {
        const hasPermission = await organizationPermissionService.validatePermission({
          userId,
          organizationId: req.body.parentId,
          action: 'create:organization'
        });

        if (!hasPermission.granted) {
          return res.status(403).json({
            success: false,
            error: 'INSUFFICIENT_PERMISSION',
            message: '没有在父组织中创建子组织的权限'
          });
        }
      }

      const organizationData = {
        name: req.body.name,
        displayName: req.body.displayName,
        description: req.body.description,
        parentId: req.body.parentId,
        type: req.body.type as OrganizationType,
        status: (req.body.status as OrganizationStatus) || 'active',
        metadata: req.body.metadata || {},
        permissionInheritance: req.body.permissionInheritance !== false,
        dataIsolationLevel: (req.body.dataIsolationLevel as DataIsolationLevel) || 'inherit',
        settings: req.body.settings || {}
      };

      const organization = await organizationService.createOrganization(organizationData, userId);

      // 自动将创建者添加为组织所有者
      await organizationService.addOrganizationMember(
        organization.id,
        userId,
        'owner',
        ['*'], // 所有权限
        userId,
        { isOwner: true, isPrimary: !req.body.parentId }
      );

      res.status(201).json({
        success: true,
        message: '组织创建成功',
        data: {
          organization,
          userRole: 'owner'
        }
      });

    } catch (error) {
      logger.error('创建组织失败', {
        error: error instanceof Error ? error.message : String(error),
        userId: req.user?.id,
        body: req.body
      });

      res.status(500).json({
        success: false,
        error: 'ORGANIZATION_CREATION_FAILED',
        message: error instanceof Error ? error.message : '创建组织失败'
      });
    }
  }
);

/**
 * @route GET /organizations/:id
 * @desc 获取组织详情
 * @access Private (需要组织查看权限)
 */
router.get('/:id',
  organizationRateLimit,
  authenticateToken as any,
  param('id').isUUID().withMessage('组织ID格式无效'),
  handleValidationErrors,
  requireOrganizationPermission('read:organization'),
  async (req, res) => {
    try {
      const organizationId = req.params.id;
      const organization = await organizationService.getOrganizationById(organizationId);

      if (!organization) {
        return res.status(404).json({
          success: false,
          error: 'ORGANIZATION_NOT_FOUND',
          message: '组织不存在'
        });
      }

      // 获取组织成员数量
      const children = await organizationService.getChildOrganizations(organizationId);
      const userOrgs = await organizationService.getUserOrganizations(req.user.id);
      const membership = userOrgs.find(m => m.organizationId === organizationId);

      res.json({
        success: true,
        data: {
          organization,
          children: children.length,
          userRole: membership?.role || null,
          userPermissions: membership?.permissions || []
        }
      });

    } catch (error) {
      logger.error('获取组织详情失败', {
        error: error instanceof Error ? error.message : String(error),
        organizationId: req.params.id,
        userId: req.user?.id
      });

      res.status(500).json({
        success: false,
        error: 'GET_ORGANIZATION_FAILED',
        message: '获取组织详情失败'
      });
    }
  }
);

/**
 * @route GET /organizations/:id/hierarchy
 * @desc 获取组织层次结构
 * @access Private (需要组织查看权限)
 */
router.get('/:id/hierarchy',
  organizationRateLimit,
  authenticateToken as any,
  param('id').isUUID().withMessage('组织ID格式无效'),
  handleValidationErrors,
  requireOrganizationPermission('read:organization'),
  async (req, res) => {
    try {
      const organizationId = req.params.id;
      const organization = await organizationService.getOrganizationById(organizationId);

      if (!organization) {
        return res.status(404).json({
          success: false,
          error: 'ORGANIZATION_NOT_FOUND',
          message: '组织不存在'
        });
      }

      const hierarchy = await organizationService.getOrganizationHierarchy(organization.path);

      res.json({
        success: true,
        data: {
          hierarchy,
          currentOrganization: organization
        }
      });

    } catch (error) {
      logger.error('获取组织层次结构失败', {
        error: error instanceof Error ? error.message : String(error),
        organizationId: req.params.id,
        userId: req.user?.id
      });

      res.status(500).json({
        success: false,
        error: 'GET_HIERARCHY_FAILED',
        message: '获取组织层次结构失败'
      });
    }
  }
);

/**
 * @route POST /organizations/:id/members
 * @desc 添加组织成员
 * @access Private (需要组织管理权限)
 */
router.post('/:id/members',
  organizationRateLimit,
  authenticateToken as any,
  [
    param('id').isUUID().withMessage('组织ID格式无效'),
    body('userId').isUUID().withMessage('用户ID格式无效'),
    body('role').notEmpty().withMessage('角色不能为空'),
    body('permissions').optional().isArray().withMessage('权限必须是数组'),
    body('expiresAt').optional().isISO8601().withMessage('过期时间格式无效')
  ],
  handleValidationErrors,
  requireOrganizationPermission('manage:members'),
  async (req, res) => {
    try {
      const organizationId = req.params.id;
      const { userId, role, permissions = [], expiresAt } = req.body;

      const member = await organizationService.addOrganizationMember(
        organizationId,
        userId,
        role,
        permissions,
        req.user.id,
        {
          expiresAt: expiresAt ? new Date(expiresAt) : undefined
        }
      );

      res.status(201).json({
        success: true,
        message: '组织成员添加成功',
        data: member
      });

    } catch (error) {
      logger.error('添加组织成员失败', {
        error: error instanceof Error ? error.message : String(error),
        organizationId: req.params.id,
        userId: req.user?.id,
        body: req.body
      });

      res.status(500).json({
        success: false,
        error: 'ADD_MEMBER_FAILED',
        message: error instanceof Error ? error.message : '添加组织成员失败'
      });
    }
  }
);

/**
 * @route GET /organizations/:id/members
 * @desc 获取组织成员列表
 * @access Private (需要组织查看权限)
 */
router.get('/:id/members',
  organizationRateLimit,
  authenticateToken as any,
  [
    param('id').isUUID().withMessage('组织ID格式无效'),
    query('page').optional().isInt({ min: 1 }).withMessage('页码必须是正整数'),
    query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('每页数量必须在1-100之间'),
    query('role').optional().isString().withMessage('角色筛选无效'),
    query('status').optional().isIn(['active', 'inactive', 'pending', 'suspended']).withMessage('状态筛选无效')
  ],
  handleValidationErrors,
  requireOrganizationPermission('read:members'),
  async (req, res) => {
    try {
      const organizationId = req.params.id;
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 20;
      const role = req.query.role as string;
      const status = req.query.status as string;

      // 构建查询条件
      const whereClause: any = { organizationId };
      if (role) whereClause.role = role;
      if (status) whereClause.status = status;

      // 查询成员列表
      const [members, total] = await Promise.all([
        organizationService.prisma.organizationMember.findMany({
          where: whereClause,
          include: {
            user: {
              select: {
                id: true,
                email: true,
                username: true,
                firstName: true,
                lastName: true,
                avatar: true
              }
            }
          },
          skip: (page - 1) * limit,
          take: limit,
          orderBy: { joinedAt: 'desc' }
        }),
        organizationService.prisma.organizationMember.count({ where: whereClause })
      ]);

      res.json({
        success: true,
        data: {
          members,
          pagination: {
            page,
            limit,
            total,
            pages: Math.ceil(total / limit)
          }
        }
      });

    } catch (error) {
      logger.error('获取组织成员列表失败', {
        error: error instanceof Error ? error.message : String(error),
        organizationId: req.params.id,
        userId: req.user?.id
      });

      res.status(500).json({
        success: false,
        error: 'GET_MEMBERS_FAILED',
        message: '获取组织成员列表失败'
      });
    }
  }
);

/**
 * @route GET /organizations/my
 * @desc 获取当前用户的组织列表
 * @access Private
 */
router.get('/my',
  organizationRateLimit,
  authenticateToken as any,
  async (req, res) => {
    try {
      const userId = req.user?.id;
      const organizations = await organizationService.getUserOrganizations(userId);

      res.json({
        success: true,
        data: organizations
      });

    } catch (error) {
      logger.error('获取用户组织列表失败', {
        error: error instanceof Error ? error.message : String(error),
        userId: req.user?.id
      });

      res.status(500).json({
        success: false,
        error: 'GET_USER_ORGANIZATIONS_FAILED',
        message: '获取用户组织列表失败'
      });
    }
  }
);

export default router;
