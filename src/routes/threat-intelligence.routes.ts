/**
 * 威胁情报路由
 * 定义威胁情报相关的API路由
 */

import { Router } from 'express';
import { threatIntelligenceController, threatIntelligenceValidation } from '@/controllers/threat-intelligence.controller';
import { authMiddleware } from '@/middleware/auth.middleware';
import { rbacMiddleware } from '@/middleware/rbac.middleware';
import { rateLimitMiddleware } from '@/middleware/rate-limit.middleware';
import { securityMiddleware } from '@/middleware/security.middleware';

const router = Router();

/**
 * 威胁情报路由配置
 * 所有路由都需要认证和适当的权限
 */

// 应用通用中间件
router.use(authMiddleware);
router.use(securityMiddleware.validateRequest);

/**
 * @route   GET /api/v1/threat-intelligence/ip/:ip
 * @desc    检查IP地址威胁
 * @access  需要 security:threat_intel:read 权限
 * @params  ip - IP地址
 * @query   includeDetails - 是否包含详细信息 (可选)
 */
router.get(
  '/ip/:ip',
  rbacMiddleware.requirePermission('security:threat_intel:read'),
  rateLimitMiddleware({
    windowMs: 60 * 1000, // 1分钟
    max: 30, // 每分钟最多30次请求
    message: '威胁情报查询请求过于频繁，请稍后再试'
  }),
  threatIntelligenceValidation.checkIP,
  threatIntelligenceController.checkIPThreat
);

/**
 * @route   GET /api/v1/threat-intelligence/domain/:domain
 * @desc    检查域名威胁
 * @access  需要 security:threat_intel:read 权限
 * @params  domain - 域名
 * @query   includeDetails - 是否包含详细信息 (可选)
 */
router.get(
  '/domain/:domain',
  rbacMiddleware.requirePermission('security:threat_intel:read'),
  rateLimitMiddleware({
    windowMs: 60 * 1000,
    max: 30,
    message: '威胁情报查询请求过于频繁，请稍后再试'
  }),
  threatIntelligenceValidation.checkDomain,
  threatIntelligenceController.checkDomainThreat
);

/**
 * @route   POST /api/v1/threat-intelligence/url
 * @desc    检查URL威胁
 * @access  需要 security:threat_intel:read 权限
 * @body    { url: string }
 * @query   includeDetails - 是否包含详细信息 (可选)
 */
router.post(
  '/url',
  rbacMiddleware.requirePermission('security:threat_intel:read'),
  rateLimitMiddleware({
    windowMs: 60 * 1000,
    max: 20,
    message: 'URL威胁检查请求过于频繁，请稍后再试'
  }),
  threatIntelligenceValidation.checkURL,
  threatIntelligenceController.checkURLThreat
);

/**
 * @route   POST /api/v1/threat-intelligence/batch
 * @desc    批量威胁检查
 * @access  需要 security:threat_intel:batch 权限
 * @body    { indicators: Array<{type: 'ip'|'domain'|'url', value: string}> }
 */
router.post(
  '/batch',
  rbacMiddleware.requirePermission('security:threat_intel:batch'),
  rateLimitMiddleware({
    windowMs: 5 * 60 * 1000, // 5分钟
    max: 10, // 每5分钟最多10次批量请求
    message: '批量威胁检查请求过于频繁，请稍后再试'
  }),
  threatIntelligenceValidation.batchCheck,
  threatIntelligenceController.batchThreatCheck
);

/**
 * @route   GET /api/v1/threat-intelligence/stats
 * @desc    获取威胁情报统计信息
 * @access  需要 security:threat_intel:stats 权限
 */
router.get(
  '/stats',
  rbacMiddleware.requirePermission('security:threat_intel:stats'),
  rateLimitMiddleware({
    windowMs: 60 * 1000,
    max: 10,
    message: '统计信息查询请求过于频繁，请稍后再试'
  }),
  threatIntelligenceController.getThreatIntelligenceStats
);

export { router as threatIntelligenceRoutes };
