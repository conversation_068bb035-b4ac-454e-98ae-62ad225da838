/**
 * 权限管理路由
 * 提供权限元数据标准化和跨应用权限申请工作流功能
 */

import { Router } from 'express';
import { 
  permissionMetadataService, 
  crossAppPermissionWorkflowService,
  PermissionMetadata 
} from '@/services/permission-metadata.service';
import { logger } from '@/config/logger';
import { authenticateToken } from '@/middleware/auth.middleware';
import rateLimit from 'express-rate-limit';
import { body, param, query, validationResult } from 'express-validator';

const router = Router();

// 权限管理速率限制配置
const permissionRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 100, // 每个IP最多100次权限管理请求
  message: {
    error: 'permission_rate_limit_exceeded',
    message: '权限管理请求过于频繁，请稍后再试'
  },
  standardHeaders: true,
  legacyHeaders: false
});

/**
 * 验证中间件
 */
const handleValidationErrors = (req: any, res: any, next: any) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      error: 'validation_error',
      message: '请求参数验证失败',
      details: errors.array()
    });
  }
  next();
};

/**
 * @route POST /permissions
 * @desc 注册权限元数据
 * @access Private (管理员)
 */
router.post('/',
  permissionRateLimit,
  authenticateToken as any,
  [
    body('name').notEmpty().withMessage('权限名称不能为空'),
    body('displayName').notEmpty().withMessage('显示名称不能为空'),
    body('description').notEmpty().withMessage('描述不能为空'),
    body('category').notEmpty().withMessage('类别不能为空'),
    body('scope').isIn(['global', 'application', 'resource']).withMessage('范围必须是 global, application 或 resource'),
    body('type').isIn(['action', 'data', 'feature', 'admin']).withMessage('类型必须是 action, data, feature 或 admin'),
    body('level').isIn(['read', 'write', 'admin', 'owner']).withMessage('级别必须是 read, write, admin 或 owner'),
    body('dependencies').optional().isArray().withMessage('依赖必须是数组'),
    body('conflicts').optional().isArray().withMessage('冲突必须是数组'),
    body('tags').optional().isArray().withMessage('标签必须是数组')
  ],
  handleValidationErrors,
  async (req, res) => {
    try {
      // TODO: 添加管理员权限检查
      
      const permissionData: Omit<PermissionMetadata, 'id' | 'createdAt' | 'updatedAt'> = {
        name: req.body.name,
        displayName: req.body.displayName,
        description: req.body.description,
        category: req.body.category,
        scope: req.body.scope,
        type: req.body.type,
        level: req.body.level,
        dependencies: req.body.dependencies || [],
        conflicts: req.body.conflicts || [],
        prerequisites: req.body.prerequisites || [],
        implications: req.body.implications || [],
        tags: req.body.tags || [],
        metadata: req.body.metadata || {},
        isActive: req.body.isActive !== false
      };

      const permission = await permissionMetadataService.registerPermission(permissionData);

      res.status(201).json({
        message: '权限元数据注册成功',
        permission: {
          id: permission.id,
          name: permission.name,
          displayName: permission.displayName,
          category: permission.category,
          scope: permission.scope,
          type: permission.type,
          level: permission.level
        }
      });

    } catch (error) {
      logger.error('权限元数据注册失败', { error });
      res.status(500).json({
        error: 'server_error',
        error_description: '权限元数据注册失败'
      });
    }
  }
);

/**
 * @route GET /permissions/discover
 * @desc 发现权限及其依赖关系
 * @access Private
 */
router.get('/discover',
  permissionRateLimit,
  authenticateToken as any,
  [
    query('category').optional().isString(),
    query('scope').optional().isIn(['global', 'application', 'resource']),
    query('type').optional().isIn(['action', 'data', 'feature', 'admin']),
    query('tags').optional().isString(),
    query('search').optional().isString()
  ],
  handleValidationErrors,
  async (req, res) => {
    try {
      const query: any = {};

      if (req.query.category) {
        query.category = req.query.category;
      }
      if (req.query.scope) {
        query.scope = req.query.scope;
      }
      if (req.query.type) {
        query.type = req.query.type;
      }
      if (req.query.tags) {
        query.tags = (req.query.tags as string).split(',');
      }
      if (req.query.search) {
        query.search = req.query.search;
      }

      const result = await permissionMetadataService.discoverPermissions(query);

      res.json({
        message: '权限发现完成',
        result: {
          permissions: result.permissions.map(p => ({
            id: p.id,
            name: p.name,
            displayName: p.displayName,
            description: p.description,
            category: p.category,
            scope: p.scope,
            type: p.type,
            level: p.level,
            tags: p.tags
          })),
          dependencies: result.dependencies,
          conflicts: result.conflicts,
          recommendations: result.recommendations,
          warnings: result.warnings,
          total: result.permissions.length
        }
      });

    } catch (error) {
      logger.error('权限发现失败', { error });
      res.status(500).json({
        error: 'server_error',
        error_description: '权限发现失败'
      });
    }
  }
);

/**
 * @route POST /permissions/resolve
 * @desc 解析权限依赖关系
 * @access Private
 */
router.post('/resolve',
  permissionRateLimit,
  authenticateToken as any,
  [
    body('permissionIds').isArray().withMessage('权限ID列表必须是数组'),
    body('permissionIds.*').isString().withMessage('权限ID必须是字符串')
  ],
  handleValidationErrors,
  async (req, res) => {
    try {
      const { permissionIds } = req.body;

      const result = await permissionMetadataService.resolvePermissionDependencies(permissionIds);

      res.json({
        message: '权限依赖关系解析完成',
        result: {
          requested: permissionIds,
          resolved: result.resolved,
          missing: result.missing,
          conflicts: result.conflicts,
          summary: {
            requestedCount: permissionIds.length,
            resolvedCount: result.resolved.length,
            missingCount: result.missing.length,
            conflictCount: result.conflicts.length
          }
        }
      });

    } catch (error) {
      logger.error('权限依赖关系解析失败', { error });
      res.status(500).json({
        error: 'server_error',
        error_description: '权限依赖关系解析失败'
      });
    }
  }
);

/**
 * @route POST /permissions/requests
 * @desc 创建跨应用权限申请
 * @access Private
 */
router.post('/requests',
  permissionRateLimit,
  authenticateToken as any,
  [
    body('targetApplicationId').notEmpty().withMessage('目标应用ID不能为空'),
    body('requestedPermissions').isArray().withMessage('请求权限列表必须是数组'),
    body('requestedPermissions.*').isString().withMessage('权限ID必须是字符串'),
    body('justification').notEmpty().withMessage('申请理由不能为空'),
    body('urgency').isIn(['low', 'medium', 'high', 'critical']).withMessage('紧急程度必须是 low, medium, high 或 critical'),
    body('expiresAt').optional().isISO8601().withMessage('过期时间必须是有效的ISO8601格式')
  ],
  handleValidationErrors,
  async (req, res) => {
    try {
      const userId = (req as any).user.userId;
      const sourceApplicationId = (req as any).user.applicationId || 'default';

      const request = {
        userId,
        sourceApplicationId,
        targetApplicationId: req.body.targetApplicationId,
        requestedPermissions: req.body.requestedPermissions,
        justification: req.body.justification,
        urgency: req.body.urgency,
        expiresAt: req.body.expiresAt ? new Date(req.body.expiresAt) : undefined,
        metadata: req.body.metadata || {}
      };

      const result = await crossAppPermissionWorkflowService.createPermissionRequest(request);

      res.status(201).json({
        message: '权限申请创建成功',
        request: {
          requestId: result.requestId,
          status: result.status,
          approvers: result.approvers,
          estimatedProcessingTime: result.estimatedProcessingTime,
          estimatedCompletionTime: new Date(Date.now() + result.estimatedProcessingTime * 60 * 60 * 1000).toISOString()
        }
      });

    } catch (error) {
      logger.error('创建权限申请失败', { error });
      res.status(500).json({
        error: 'server_error',
        error_description: '创建权限申请失败'
      });
    }
  }
);

/**
 * @route POST /permissions/requests/:requestId/approve
 * @desc 处理权限申请审批
 * @access Private
 */
router.post('/requests/:requestId/approve',
  permissionRateLimit,
  authenticateToken as any,
  [
    param('requestId').isUUID().withMessage('请求ID必须是有效的UUID'),
    body('decision').isIn(['approve', 'reject']).withMessage('决定必须是 approve 或 reject'),
    body('comments').optional().isString().withMessage('评论必须是字符串')
  ],
  handleValidationErrors,
  async (req, res) => {
    try {
      const { requestId } = req.params;
      const { decision, comments } = req.body;
      const approverId = (req as any).user.userId;

      const result = await crossAppPermissionWorkflowService.processApproval(
        requestId,
        approverId,
        decision,
        comments
      );

      res.json({
        message: '权限申请审批处理完成',
        result: {
          requestId,
          status: result.status,
          nextApprovers: result.nextApprovers,
          completed: result.completed,
          decision,
          processedBy: approverId,
          processedAt: new Date().toISOString()
        }
      });

    } catch (error) {
      logger.error('处理权限申请审批失败', { error });
      res.status(500).json({
        error: 'server_error',
        error_description: '处理权限申请审批失败'
      });
    }
  }
);

/**
 * @route GET /permissions/requests
 * @desc 获取权限申请列表
 * @access Private
 */
router.get('/requests',
  permissionRateLimit,
  authenticateToken as any,
  [
    query('status').optional().isIn(['pending', 'approved', 'rejected']),
    query('page').optional().isInt({ min: 1 }),
    query('limit').optional().isInt({ min: 1, max: 100 })
  ],
  handleValidationErrors,
  async (req, res) => {
    try {
      const userId = (req as any).user.userId;
      const { status, page = 1, limit = 20 } = req.query;
      const offset = (Number(page) - 1) * Number(limit);

      const where: any = {
        OR: [
          { userId }, // 用户自己的申请
          { approvers: { array_contains: userId } } // 需要用户审批的申请
        ]
      };

      if (status) {
        where.status = status;
      }

      const [requests, total] = await Promise.all([
        require('@/config/database').prisma.permissionRequest.findMany({
          where,
          skip: offset,
          take: Number(limit),
          orderBy: { createdAt: 'desc' },
          include: {
            user: {
              select: {
                id: true,
                username: true,
                email: true
              }
            },
            sourceApplication: {
              select: {
                id: true,
                name: true
              }
            },
            targetApplication: {
              select: {
                id: true,
                name: true
              }
            }
          }
        }),
        require('@/config/database').prisma.permissionRequest.count({ where })
      ]);

      res.json({
        requests: requests.map((req: any) => ({
          id: req.id,
          user: req.user,
          sourceApplication: req.sourceApplication,
          targetApplication: req.targetApplication,
          requestedPermissions: req.requestedPermissions,
          justification: req.justification,
          urgency: req.urgency,
          status: req.status,
          approvers: req.approvers,
          estimatedProcessingTime: req.estimatedProcessingTime,
          createdAt: req.createdAt,
          processedAt: req.processedAt
        })),
        pagination: {
          page: Number(page),
          limit: Number(limit),
          total,
          pages: Math.ceil(total / Number(limit))
        }
      });

    } catch (error) {
      logger.error('获取权限申请列表失败', { error });
      res.status(500).json({
        error: 'server_error',
        error_description: '获取权限申请列表失败'
      });
    }
  }
);

export default router;
