/**
 * OpenID Connect Provider路由
 * 定义OAuth 2.0和OpenID Connect相关的API端点
 */

import { Router } from 'express';
import { OIDCController } from '@/controllers/oidc.controller';
import { authenticateToken, optionalAuthentication } from '@/middleware/auth.middleware';
import rateLimit from 'express-rate-limit';

const router = Router();
const oidcController = new OIDCController();

// OIDC速率限制配置
const oidcRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 100, // 每个IP最多100次OIDC请求
  message: {
    error: 'oidc_rate_limit_exceeded',
    message: 'OIDC请求过于频繁，请稍后再试'
  },
  standardHeaders: true,
  legacyHeaders: false
});

// 令牌端点速率限制（更严格）
const tokenRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 50, // 每个IP最多50次令牌请求
  message: {
    error: 'token_rate_limit_exceeded',
    message: '令牌请求过于频繁，请稍后再试'
  },
  standardHeaders: true,
  legacyHeaders: false
});

/**
 * @route GET /oauth2/authorize
 * @desc OAuth 2.0 授权端点
 * @access Public (需要用户认证)
 * @param {string} response_type - 响应类型 (code, token, id_token等)
 * @param {string} client_id - 客户端ID
 * @param {string} redirect_uri - 重定向URI
 * @param {string} scope - 权限范围
 * @param {string} [state] - 状态参数
 * @param {string} [nonce] - 随机数 (用于ID令牌)
 * @param {string} [code_challenge] - PKCE代码挑战
 * @param {string} [code_challenge_method] - PKCE挑战方法
 * @param {string} [prompt] - 提示参数 (none, login, consent, select_account)
 * @param {number} [max_age] - 最大认证年龄
 */
router.get('/authorize',
  oidcRateLimit,
  optionalAuthentication as any,
  oidcController.authorize
);

/**
 * @route POST /oauth2/token
 * @desc OAuth 2.0 令牌端点
 * @access Public
 * @param {string} grant_type - 授权类型
 * @param {string} [code] - 授权码 (authorization_code流程)
 * @param {string} [redirect_uri] - 重定向URI (authorization_code流程)
 * @param {string} [refresh_token] - 刷新令牌 (refresh_token流程)
 * @param {string} client_id - 客户端ID
 * @param {string} client_secret - 客户端密钥
 * @param {string} [code_verifier] - PKCE代码验证器
 * @param {string} [scope] - 权限范围 (可选，用于缩小范围)
 */
router.post('/token',
  tokenRateLimit,
  oidcController.token
);

/**
 * @route GET /oauth2/userinfo
 * @desc OpenID Connect 用户信息端点
 * @access Private (需要访问令牌)
 * @header {string} Authorization - Bearer访问令牌
 */
router.get('/userinfo',
  oidcRateLimit,
  oidcController.userinfo
);

/**
 * @route POST /oauth2/userinfo
 * @desc OpenID Connect 用户信息端点 (POST方法)
 * @access Private (需要访问令牌)
 * @header {string} Authorization - Bearer访问令牌
 */
router.post('/userinfo',
  oidcRateLimit,
  oidcController.userinfo
);

/**
 * @route POST /oauth2/revoke
 * @desc OAuth 2.0 令牌撤销端点
 * @access Public
 * @param {string} token - 要撤销的令牌
 * @param {string} [token_type_hint] - 令牌类型提示 (access_token, refresh_token)
 * @param {string} client_id - 客户端ID
 * @param {string} client_secret - 客户端密钥
 */
router.post('/revoke',
  oidcRateLimit,
  oidcController.revoke
);

/**
 * @route POST /oauth2/introspect
 * @desc OAuth 2.0 令牌内省端点 (RFC 7662)
 * @access Public (需要客户端认证)
 * @param {string} token - 要内省的令牌
 * @param {string} [token_type_hint] - 令牌类型提示
 * @param {string} client_id - 客户端ID
 * @param {string} client_secret - 客户端密钥
 */
router.post('/introspect',
  oidcRateLimit,
  oidcController.introspect
);

/**
 * @route POST /oauth2/consent
 * @desc 用户同意端点
 * @access Private (需要用户认证)
 * @param {string} state - 状态参数
 * @param {string[]} [approved_scopes] - 用户批准的权限范围
 * @param {boolean} [denied] - 用户是否拒绝授权
 */
router.post('/consent',
  oidcRateLimit,
  authenticateToken as any,
  oidcController.consent
);

/**
 * @route GET /oauth2/logout
 * @desc OpenID Connect 登出端点
 * @access Public
 * @param {string} [id_token_hint] - ID令牌提示
 * @param {string} [post_logout_redirect_uri] - 登出后重定向URI
 * @param {string} [state] - 状态参数
 */
router.get('/logout', oidcRateLimit, async (req, res) => {
  try {
    const {
      id_token_hint,
      post_logout_redirect_uri,
      state
    } = req.query as Record<string, string>;

    // TODO: 实现登出逻辑
    // 1. 验证id_token_hint
    // 2. 撤销相关会话
    // 3. 重定向到post_logout_redirect_uri

    if (post_logout_redirect_uri) {
      const logoutUrl = new URL(post_logout_redirect_uri);
      if (state) {
        logoutUrl.searchParams.set('state', state);
      }
      res.redirect(logoutUrl.toString());
    } else {
      res.status(200).json({
        message: '登出成功'
      });
    }

  } catch (error) {
    res.status(500).json({
      error: 'server_error',
      error_description: '登出服务器内部错误'
    });
  }
});

/**
 * @route GET /oauth2/certs
 * @desc JSON Web Key Set (JWKS) 端点的别名
 * @access Public
 */
router.get('/certs', oidcRateLimit, (req, res) => {
  res.redirect('/.well-known/jwks.json');
});

/**
 * @route GET /oauth2/.well-known/openid_configuration
 * @desc OpenID Connect 发现端点的别名
 * @access Public
 */
router.get('/.well-known/openid_configuration', oidcRateLimit, (req, res) => {
  res.redirect('/.well-known/openid-configuration');
});

/**
 * OIDC客户端管理端点 (需要管理员权限)
 */

/**
 * @route POST /oauth2/clients
 * @desc 创建新的OIDC客户端
 * @access Private (管理员)
 */
router.post('/clients',
  oidcRateLimit,
  authenticateToken as any,
  // TODO: 添加管理员权限检查中间件
  oidcController.createClient
);

/**
 * @route GET /oauth2/clients
 * @desc 列出OIDC客户端
 * @access Private (管理员)
 */
router.get('/clients',
  oidcRateLimit,
  authenticateToken as any,
  // TODO: 添加管理员权限检查中间件
  oidcController.listClients
);

/**
 * @route GET /oauth2/clients/:clientId
 * @desc 获取OIDC客户端信息
 * @access Private (管理员)
 */
router.get('/clients/:clientId',
  oidcRateLimit,
  authenticateToken as any,
  // TODO: 添加管理员权限检查中间件
  oidcController.getClient
);

/**
 * @route PUT /oauth2/clients/:clientId
 * @desc 更新OIDC客户端
 * @access Private (管理员)
 */
router.put('/clients/:clientId',
  oidcRateLimit,
  authenticateToken as any,
  // TODO: 添加管理员权限检查中间件
  oidcController.updateClient
);

/**
 * @route DELETE /oauth2/clients/:clientId
 * @desc 删除OIDC客户端
 * @access Private (管理员)
 */
router.delete('/clients/:clientId',
  oidcRateLimit,
  authenticateToken as any,
  // TODO: 添加管理员权限检查中间件
  oidcController.deleteClient
);

/**
 * @route POST /oauth2/clients/:clientId/regenerate-secret
 * @desc 重新生成客户端密钥
 * @access Private (管理员)
 */
router.post('/clients/:clientId/regenerate-secret',
  oidcRateLimit,
  authenticateToken as any,
  // TODO: 添加管理员权限检查中间件
  oidcController.regenerateClientSecret
);

export default router;
