/**
 * 联邦式组织架构管理路由
 * 提供应用组织架构同步和联邦权限验证的API接口
 */

import { Router } from 'express';
import { body, param, query, validationResult } from 'express-validator';
import { authenticateToken } from '@/middleware/auth.middleware';
import { federatedOrganizationService } from '@/services/federated-organization.service';
import { logger } from '@/config/logger';
import rateLimit from 'express-rate-limit';

const router = Router();

// 速率限制配置
const federatedRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 200, // 每个IP最多200个请求
  message: {
    success: false,
    error: 'RATE_LIMIT_EXCEEDED',
    message: '联邦同步请求过于频繁，请稍后再试'
  }
});

// 验证错误处理中间件
const handleValidationErrors = (req: any, res: any, next: any) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      error: 'VALIDATION_ERROR',
      message: '请求参数验证失败',
      details: errors.array()
    });
  }
  next();
};

/**
 * @route POST /applications/:appId/organizations/sync
 * @desc 同步应用组织架构
 * @access Private (应用认证)
 */
router.post('/:appId/organizations/sync',
  federatedRateLimit,
  authenticateToken as any,
  [
    param('appId').isUUID().withMessage('应用ID格式无效'),
    body('organizations')
      .isArray({ min: 1 })
      .withMessage('组织列表不能为空'),
    body('organizations.*.id')
      .notEmpty()
      .withMessage('组织ID不能为空'),
    body('organizations.*.name')
      .notEmpty()
      .matches(/^[a-zA-Z0-9_-]+$/)
      .withMessage('组织名称格式无效'),
    body('organizations.*.displayName')
      .notEmpty()
      .withMessage('组织显示名称不能为空'),
    body('organizations.*.type')
      .isIn(['company', 'division', 'department', 'team', 'group', 'project'])
      .withMessage('组织类型无效'),
    body('syncType')
      .optional()
      .isIn(['full', 'incremental'])
      .withMessage('同步类型无效')
  ],
  handleValidationErrors,
  async (req, res) => {
    try {
      const applicationId = req.params.appId;
      const { organizations, syncType = 'incremental' } = req.body;

      // 验证应用权限
      if (req.user?.applicationId !== applicationId) {
        return res.status(403).json({
          success: false,
          error: 'INSUFFICIENT_PERMISSION',
          message: '没有权限同步该应用的组织架构'
        });
      }

      logger.info('开始同步应用组织架构', {
        applicationId,
        organizationCount: organizations.length,
        syncType,
        userId: req.user?.id
      });

      const result = await federatedOrganizationService.registerApplicationOrganization(
        applicationId,
        organizations
      );

      res.json({
        success: true,
        message: '组织架构同步成功',
        data: {
          syncType,
          syncedOrganizations: result.syncedCount,
          newMappings: result.newMappings,
          updatedMappings: result.updatedMappings,
          conflicts: result.conflicts,
          timestamp: new Date().toISOString()
        }
      });

    } catch (error) {
      logger.error('同步应用组织架构失败', {
        applicationId: req.params.appId,
        error: error instanceof Error ? error.message : String(error),
        userId: req.user?.id
      });

      res.status(500).json({
        success: false,
        error: 'SYNC_FAILED',
        message: error instanceof Error ? error.message : '同步组织架构失败'
      });
    }
  }
);

/**
 * @route GET /applications/:appId/organizations/:orgId/mappings
 * @desc 查询组织映射关系
 * @access Private
 */
router.get('/:appId/organizations/:orgId/mappings',
  federatedRateLimit,
  authenticateToken as any,
  [
    param('appId').isUUID().withMessage('应用ID格式无效'),
    param('orgId').notEmpty().withMessage('组织ID不能为空')
  ],
  handleValidationErrors,
  async (req, res) => {
    try {
      const { appId: applicationId, orgId: organizationId } = req.params;

      const mappings = await federatedOrganizationService.getOrganizationMappings(
        applicationId,
        organizationId
      );

      res.json({
        success: true,
        data: {
          sourceOrganization: mappings.source,
          targetOrganizations: mappings.targets,
          mappingHistory: mappings.history,
          confidence: mappings.confidence
        }
      });

    } catch (error) {
      logger.error('查询组织映射关系失败', {
        applicationId: req.params.appId,
        organizationId: req.params.orgId,
        error: error instanceof Error ? error.message : String(error)
      });

      res.status(500).json({
        success: false,
        error: 'GET_MAPPINGS_FAILED',
        message: '查询组织映射关系失败'
      });
    }
  }
);

/**
 * @route POST /permissions/federated-check
 * @desc 联邦权限验证
 * @access Private
 */
router.post('/permissions/federated-check',
  federatedRateLimit,
  authenticateToken as any,
  [
    body('userId').isUUID().withMessage('用户ID格式无效'),
    body('applicationId').isUUID().withMessage('应用ID格式无效'),
    body('permission').notEmpty().withMessage('权限不能为空'),
    body('organizationContext').optional().isString()
  ],
  handleValidationErrors,
  async (req, res) => {
    try {
      const { userId, applicationId, permission, organizationContext } = req.body;

      // 解析联邦权限
      const result = await federatedOrganizationService.resolveFederatedPermissions(
        userId,
        applicationId,
        organizationContext
      );

      // 检查是否拥有所需权限
      const hasPermission = result.permissions.includes(permission) ||
                           result.permissions.includes('*');

      res.json({
        success: true,
        data: {
          granted: hasPermission,
          permission,
          userId,
          applicationId,
          organizationContext,
          allPermissions: result.permissions,
          organizationMemberships: result.organizationMemberships,
          mappingConfidence: result.mappingConfidence,
          resolvedAt: result.resolvedAt
        }
      });

    } catch (error) {
      logger.error('联邦权限验证失败', {
        body: req.body,
        error: error instanceof Error ? error.message : String(error)
      });

      res.status(500).json({
        success: false,
        error: 'FEDERATED_PERMISSION_CHECK_FAILED',
        message: '联邦权限验证失败'
      });
    }
  }
);

/**
 * @route GET /applications/:appId/federation/status
 * @desc 获取应用联邦状态
 * @access Private
 */
router.get('/:appId/federation/status',
  federatedRateLimit,
  authenticateToken as any,
  param('appId').isUUID().withMessage('应用ID格式无效'),
  handleValidationErrors,
  async (req, res) => {
    try {
      const applicationId = req.params.appId;

      const status = await federatedOrganizationService.getFederationStatus(applicationId);

      res.json({
        success: true,
        data: {
          applicationId,
          isRegistered: status.isRegistered,
          lastSyncAt: status.lastSyncAt,
          syncStatus: status.syncStatus,
          totalOrganizations: status.totalOrganizations,
          mappedOrganizations: status.mappedOrganizations,
          conflictCount: status.conflictCount,
          mappingAccuracy: status.mappingAccuracy,
          healthScore: status.healthScore
        }
      });

    } catch (error) {
      logger.error('获取联邦状态失败', {
        applicationId: req.params.appId,
        error: error instanceof Error ? error.message : String(error)
      });

      res.status(500).json({
        success: false,
        error: 'GET_FEDERATION_STATUS_FAILED',
        message: '获取联邦状态失败'
      });
    }
  }
);

/**
 * @route GET /applications/:appId/federation/conflicts
 * @desc 获取映射冲突列表
 * @access Private (管理员权限)
 */
router.get('/:appId/federation/conflicts',
  federatedRateLimit,
  authenticateToken as any,
  [
    param('appId').isUUID().withMessage('应用ID格式无效'),
    query('status')
      .optional()
      .isIn(['pending', 'resolved', 'ignored'])
      .withMessage('冲突状态无效'),
    query('page')
      .optional()
      .isInt({ min: 1 })
      .withMessage('页码必须是正整数'),
    query('limit')
      .optional()
      .isInt({ min: 1, max: 100 })
      .withMessage('每页数量必须在1-100之间')
  ],
  handleValidationErrors,
  async (req, res) => {
    try {
      const applicationId = req.params.appId;
      const status = req.query.status as string;
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 20;

      const conflicts = await federatedOrganizationService.getMappingConflicts(
        applicationId,
        { status, page, limit }
      );

      res.json({
        success: true,
        data: {
          conflicts: conflicts.items,
          pagination: {
            page,
            limit,
            total: conflicts.total,
            pages: Math.ceil(conflicts.total / limit)
          }
        }
      });

    } catch (error) {
      logger.error('获取映射冲突列表失败', {
        applicationId: req.params.appId,
        error: error instanceof Error ? error.message : String(error)
      });

      res.status(500).json({
        success: false,
        error: 'GET_CONFLICTS_FAILED',
        message: '获取映射冲突列表失败'
      });
    }
  }
);

/**
 * @route POST /applications/:appId/federation/conflicts/:conflictId/resolve
 * @desc 解决映射冲突
 * @access Private (管理员权限)
 */
router.post('/:appId/federation/conflicts/:conflictId/resolve',
  federatedRateLimit,
  authenticateToken as any,
  [
    param('appId').isUUID().withMessage('应用ID格式无效'),
    param('conflictId').isUUID().withMessage('冲突ID格式无效'),
    body('resolutionStrategy')
      .isIn(['manual_review', 'auto_merge', 'create_separate', 'use_latest'])
      .withMessage('解决策略无效'),
    body('resolutionNotes')
      .optional()
      .isLength({ max: 500 })
      .withMessage('解决备注长度不能超过500字符')
  ],
  handleValidationErrors,
  async (req, res) => {
    try {
      const { appId: applicationId, conflictId } = req.params;
      const { resolutionStrategy, resolutionNotes } = req.body;

      const result = await federatedOrganizationService.resolveConflict(
        conflictId,
        resolutionStrategy,
        req.user?.id,
        resolutionNotes
      );

      res.json({
        success: true,
        message: '冲突解决成功',
        data: {
          conflictId,
          resolutionStrategy,
          resolvedAt: result.resolvedAt,
          newMappings: result.newMappings
        }
      });

    } catch (error) {
      logger.error('解决映射冲突失败', {
        applicationId: req.params.appId,
        conflictId: req.params.conflictId,
        error: error instanceof Error ? error.message : String(error)
      });

      res.status(500).json({
        success: false,
        error: 'RESOLVE_CONFLICT_FAILED',
        message: '解决映射冲突失败'
      });
    }
  }
);

export default router;
