/**
 * Prometheus监控路由
 * 提供Prometheus指标端点和监控配置
 */

import { Router } from 'express';
import { prometheusService } from '@/services/prometheus.service';
import { logger } from '@/config/logger';
import rateLimit from 'express-rate-limit';

const router = Router();

// Prometheus端点速率限制
const prometheusRateLimit = rateLimit({
  windowMs: 1 * 60 * 1000, // 1分钟
  max: 60, // 每分钟最多60次请求
  message: {
    error: 'prometheus_rate_limit_exceeded',
    message: 'Prometheus请求过于频繁，请稍后再试'
  },
  standardHeaders: true,
  legacyHeaders: false,
  // 跳过内部请求
  skip: (req) => {
    const userAgent = req.get('User-Agent') || '';
    return userAgent.includes('Prometheus') || 
           userAgent.includes('Grafana') ||
           req.ip === '127.0.0.1' ||
           req.ip === '::1';
  }
});

/**
 * @route GET /metrics
 * @desc Prometheus指标端点
 * @access Public (但有速率限制)
 */
router.get('/metrics', prometheusRateLimit, async (req, res) => {
  try {
    await prometheusService.handleMetricsRequest(req, res);
    
    // 记录指标访问日志
    logger.debug('Prometheus指标被访问', {
      service: 'prometheus',
      ip: req.ip,
      userAgent: req.get('User-Agent')
    });

  } catch (error) {
    logger.error('Prometheus指标端点错误', {
      service: 'prometheus',
      error: error instanceof Error ? error.message : String(error),
      ip: req.ip
    });

    res.status(500).json({
      error: 'metrics_error',
      message: '指标服务暂时不可用'
    });
  }
});

/**
 * @route GET /metrics/summary
 * @desc 获取指标摘要信息
 * @access Public
 */
router.get('/metrics/summary', prometheusRateLimit, (req, res) => {
  try {
    const summary = prometheusService.getMetricsSummary();
    
    if (!summary) {
      res.status(500).json({
        error: 'metrics_summary_error',
        message: '无法获取指标摘要'
      });
      return;
    }

    res.status(200).json({
      status: 'success',
      data: summary
    });

  } catch (error) {
    logger.error('获取指标摘要失败', {
      service: 'prometheus',
      error: error instanceof Error ? error.message : String(error)
    });

    res.status(500).json({
      error: 'metrics_summary_error',
      message: '获取指标摘要失败'
    });
  }
});

/**
 * @route GET /health/prometheus
 * @desc Prometheus服务健康检查
 * @access Public
 */
router.get('/health/prometheus', (req, res) => {
  try {
    const summary = prometheusService.getMetricsSummary();
    const isHealthy = summary && summary.totalMetrics > 0;

    res.status(isHealthy ? 200 : 503).json({
      status: isHealthy ? 'healthy' : 'unhealthy',
      service: 'prometheus',
      timestamp: new Date().toISOString(),
      metrics: {
        total: summary?.totalMetrics || 0,
        lastUpdated: summary?.lastUpdated
      }
    });

  } catch (error) {
    logger.error('Prometheus健康检查失败', {
      service: 'prometheus',
      error: error instanceof Error ? error.message : String(error)
    });

    res.status(503).json({
      status: 'unhealthy',
      service: 'prometheus',
      timestamp: new Date().toISOString(),
      error: 'Health check failed'
    });
  }
});

export default router;
