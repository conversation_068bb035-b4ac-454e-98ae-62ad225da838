/**
 * 审计日志查询路由
 * 提供高级审计日志查询、分析和导出功能的API接口
 */

import { Router } from 'express';
import { auditQueryService, ExportFormat } from '@/services/audit-query.service';
import { logger } from '@/config/logger';
import { authenticateToken } from '@/middleware/auth.middleware';
import rateLimit from 'express-rate-limit';
import { query, validationResult } from 'express-validator';

const router = Router();

// 审计查询API速率限制
const auditQueryRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 200, // 每个IP最多200次查询请求
  message: {
    error: 'audit_query_rate_limit_exceeded',
    message: '审计查询请求过于频繁，请稍后再试'
  },
  standardHeaders: true,
  legacyHeaders: false
});

// 导出API速率限制（更严格）
const exportRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 10, // 每个IP最多10次导出请求
  message: {
    error: 'export_rate_limit_exceeded',
    message: '导出请求过于频繁，请稍后再试'
  },
  standardHeaders: true,
  legacyHeaders: false
});

/**
 * 验证中间件
 */
const handleValidationErrors = (req: any, res: any, next: any) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      error: 'validation_error',
      message: '请求参数验证失败',
      details: errors.array()
    });
  }
  next();
};

/**
 * 审计权限检查中间件
 */
const requireAuditPermission = (req: any, res: any, next: any) => {
  const user = req.user;
  
  if (!user) {
    return res.status(401).json({
      error: 'unauthorized',
      message: '未认证'
    });
  }

  // 检查审计权限
  if (!user.isAdmin && !user.permissions?.includes('audit.read')) {
    return res.status(403).json({
      error: 'forbidden',
      message: '需要审计查看权限'
    });
  }

  next();
};

/**
 * @route GET /audit/logs
 * @desc 查询审计日志
 * @access Private (审计权限)
 */
router.get('/logs',
  auditQueryRateLimit,
  authenticateToken as any,
  requireAuditPermission,
  [
    query('page').optional().isInt({ min: 1 }),
    query('pageSize').optional().isInt({ min: 1, max: 1000 }),
    query('sortBy').optional().isString(),
    query('sortOrder').optional().isIn(['asc', 'desc']),
    query('userId').optional().isUUID(),
    query('action').optional().isString(),
    query('resource').optional().isString(),
    query('level').optional().isString(),
    query('startDate').optional().isISO8601(),
    query('endDate').optional().isISO8601(),
    query('ipAddress').optional().isString(),
    query('location').optional().isString(),
    query('searchText').optional().isString(),
    query('riskScoreMin').optional().isFloat({ min: 0, max: 100 }),
    query('riskScoreMax').optional().isFloat({ min: 0, max: 100 }),
    query('hasAnomalies').optional().isBoolean()
  ],
  handleValidationErrors,
  async (req, res) => {
    try {
      const params = {
        page: parseInt(req.query.page as string) || 1,
        pageSize: parseInt(req.query.pageSize as string) || 50,
        sortBy: req.query.sortBy as string || 'createdAt',
        sortOrder: (req.query.sortOrder as 'asc' | 'desc') || 'desc',
        userId: req.query.userId as string,
        action: req.query.action as string,
        resource: req.query.resource as string,
        level: req.query.level as string,
        startDate: req.query.startDate ? new Date(req.query.startDate as string) : undefined,
        endDate: req.query.endDate ? new Date(req.query.endDate as string) : undefined,
        ipAddress: req.query.ipAddress as string,
        location: req.query.location as string,
        searchText: req.query.searchText as string,
        riskScoreMin: req.query.riskScoreMin ? parseFloat(req.query.riskScoreMin as string) : undefined,
        riskScoreMax: req.query.riskScoreMax ? parseFloat(req.query.riskScoreMax as string) : undefined,
        hasAnomalies: req.query.hasAnomalies === 'true'
      };

      // 移除undefined值
      Object.keys(params).forEach(key => {
        if (params[key as keyof typeof params] === undefined) {
          delete params[key as keyof typeof params];
        }
      });

      const result = await auditQueryService.queryAuditLogs(params);

      res.json({
        success: true,
        data: result.data,
        pagination: {
          page: result.page,
          pageSize: result.pageSize,
          total: result.total,
          totalPages: result.totalPages
        },
        aggregations: result.aggregations,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      logger.error('查询审计日志失败', { error, query: req.query });
      res.status(500).json({
        error: 'server_error',
        message: '查询审计日志失败'
      });
    }
  }
);

/**
 * @route GET /audit/statistics
 * @desc 获取审计统计信息
 * @access Private (审计权限)
 */
router.get('/statistics',
  auditQueryRateLimit,
  authenticateToken as any,
  requireAuditPermission,
  [
    query('startDate').optional().isISO8601(),
    query('endDate').optional().isISO8601(),
    query('userId').optional().isUUID(),
    query('action').optional().isString(),
    query('resource').optional().isString()
  ],
  handleValidationErrors,
  async (req, res) => {
    try {
      const params = {
        startDate: req.query.startDate ? new Date(req.query.startDate as string) : undefined,
        endDate: req.query.endDate ? new Date(req.query.endDate as string) : undefined,
        userId: req.query.userId as string,
        action: req.query.action as string,
        resource: req.query.resource as string
      };

      // 移除undefined值
      Object.keys(params).forEach(key => {
        if (params[key as keyof typeof params] === undefined) {
          delete params[key as keyof typeof params];
        }
      });

      const statistics = await auditQueryService.getAuditStatistics(params);

      res.json({
        success: true,
        statistics,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      logger.error('获取审计统计失败', { error, query: req.query });
      res.status(500).json({
        error: 'server_error',
        message: '获取审计统计失败'
      });
    }
  }
);

/**
 * @route GET /audit/export
 * @desc 导出审计日志
 * @access Private (审计权限)
 */
router.get('/export',
  exportRateLimit,
  authenticateToken as any,
  requireAuditPermission,
  [
    query('format').isIn(['csv', 'xlsx', 'json']),
    query('maxRecords').optional().isInt({ min: 1, max: 50000 }),
    query('userId').optional().isUUID(),
    query('action').optional().isString(),
    query('resource').optional().isString(),
    query('level').optional().isString(),
    query('startDate').optional().isISO8601(),
    query('endDate').optional().isISO8601()
  ],
  handleValidationErrors,
  async (req, res) => {
    try {
      const format = req.query.format as ExportFormat;
      const maxRecords = parseInt(req.query.maxRecords as string) || 10000;

      const params = {
        userId: req.query.userId as string,
        action: req.query.action as string,
        resource: req.query.resource as string,
        level: req.query.level as string,
        startDate: req.query.startDate ? new Date(req.query.startDate as string) : undefined,
        endDate: req.query.endDate ? new Date(req.query.endDate as string) : undefined
      };

      // 移除undefined值
      Object.keys(params).forEach(key => {
        if (params[key as keyof typeof params] === undefined) {
          delete params[key as keyof typeof params];
        }
      });

      const exportData = await auditQueryService.exportAuditLogs(params, format, maxRecords);

      // 设置响应头
      const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
      let filename = `audit-logs-${timestamp}`;
      let contentType = 'application/octet-stream';

      switch (format) {
        case ExportFormat.CSV:
          filename += '.csv';
          contentType = 'text/csv';
          break;
        case ExportFormat.XLSX:
          filename += '.xlsx';
          contentType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
          break;
        case ExportFormat.JSON:
          filename += '.json';
          contentType = 'application/json';
          break;
      }

      res.setHeader('Content-Type', contentType);
      res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
      res.setHeader('Content-Length', exportData.length);

      res.send(exportData);

      // 记录导出操作
      logger.info('审计日志导出成功', {
        userId: req.user.id,
        format,
        maxRecords,
        fileSize: exportData.length,
        filters: Object.keys(params).length
      });

    } catch (error) {
      logger.error('导出审计日志失败', { error, query: req.query });
      res.status(500).json({
        error: 'server_error',
        message: '导出审计日志失败'
      });
    }
  }
);

/**
 * @route GET /audit/anomalies
 * @desc 检测异常行为
 * @access Private (审计权限)
 */
router.get('/anomalies',
  auditQueryRateLimit,
  authenticateToken as any,
  requireAuditPermission,
  [
    query('startDate').optional().isISO8601(),
    query('endDate').optional().isISO8601(),
    query('userId').optional().isUUID(),
    query('severity').optional().isIn(['low', 'medium', 'high', 'critical'])
  ],
  handleValidationErrors,
  async (req, res) => {
    try {
      const params = {
        startDate: req.query.startDate ? new Date(req.query.startDate as string) : undefined,
        endDate: req.query.endDate ? new Date(req.query.endDate as string) : undefined,
        userId: req.query.userId as string
      };

      // 移除undefined值
      Object.keys(params).forEach(key => {
        if (params[key as keyof typeof params] === undefined) {
          delete params[key as keyof typeof params];
        }
      });

      const anomalies = await auditQueryService.detectAnomalies(params);

      // 根据严重程度过滤
      const severity = req.query.severity as string;
      const filteredAnomalies = severity 
        ? anomalies.filter(anomaly => anomaly.severity === severity)
        : anomalies;

      res.json({
        success: true,
        anomalies: filteredAnomalies,
        total: filteredAnomalies.length,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      logger.error('异常行为检测失败', { error, query: req.query });
      res.status(500).json({
        error: 'server_error',
        message: '异常行为检测失败'
      });
    }
  }
);

/**
 * @route GET /audit/timeline
 * @desc 获取审计时间线
 * @access Private (审计权限)
 */
router.get('/timeline',
  auditQueryRateLimit,
  authenticateToken as any,
  requireAuditPermission,
  [
    query('startDate').isISO8601(),
    query('endDate').isISO8601(),
    query('granularity').optional().isIn(['hour', 'day', 'week', 'month']),
    query('userId').optional().isUUID(),
    query('action').optional().isString()
  ],
  handleValidationErrors,
  async (req, res) => {
    try {
      const startDate = new Date(req.query.startDate as string);
      const endDate = new Date(req.query.endDate as string);
      const granularity = req.query.granularity as string || 'day';

      // 这里可以实现时间线数据的生成逻辑
      // 简化实现，返回模拟数据
      const timeline = [];
      const current = new Date(startDate);
      
      while (current <= endDate) {
        timeline.push({
          timestamp: current.toISOString(),
          count: Math.floor(Math.random() * 100),
          riskEvents: Math.floor(Math.random() * 10)
        });
        
        // 根据粒度增加时间
        switch (granularity) {
          case 'hour':
            current.setHours(current.getHours() + 1);
            break;
          case 'day':
            current.setDate(current.getDate() + 1);
            break;
          case 'week':
            current.setDate(current.getDate() + 7);
            break;
          case 'month':
            current.setMonth(current.getMonth() + 1);
            break;
        }
      }

      res.json({
        success: true,
        timeline,
        granularity,
        period: {
          start: startDate.toISOString(),
          end: endDate.toISOString()
        },
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      logger.error('获取审计时间线失败', { error, query: req.query });
      res.status(500).json({
        error: 'server_error',
        message: '获取审计时间线失败'
      });
    }
  }
);

export default router;
