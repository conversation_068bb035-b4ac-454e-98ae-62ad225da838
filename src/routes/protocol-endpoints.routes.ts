/**
 * 标准化协议端点路由
 * 实现标准SSO协议端点，包括授权、令牌、用户信息等端点
 */

import { Router } from 'express';
import { oidcProviderService } from '@/services/oidc-provider.service';
import { samlIdPService } from '@/services/saml-idp.service';
import { logger } from '@/config/logger';
import { getServerUrl } from '@/config';
import rateLimit from 'express-rate-limit';

const router = Router();

// 协议端点速率限制配置
const protocolRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 200, // 每个IP最多200次协议请求
  message: {
    error: 'protocol_rate_limit_exceeded',
    message: '协议端点请求过于频繁，请稍后再试'
  },
  standardHeaders: true,
  legacyHeaders: false
});

/**
 * @route GET /.well-known/openid-configuration
 * @desc OpenID Connect 发现端点
 * @access Public
 */
router.get('/.well-known/openid-configuration', protocolRateLimit, async (req, res) => {
  try {
    const baseUrl = getServerUrl();
    
    const configuration = {
      issuer: baseUrl,
      authorization_endpoint: `${baseUrl}/oauth2/authorize`,
      token_endpoint: `${baseUrl}/oauth2/token`,
      userinfo_endpoint: `${baseUrl}/oauth2/userinfo`,
      jwks_uri: `${baseUrl}/.well-known/jwks.json`,
      
      // 撤销和内省端点
      revocation_endpoint: `${baseUrl}/oauth2/revoke`,
      introspection_endpoint: `${baseUrl}/oauth2/introspect`,
      
      // 登出端点
      end_session_endpoint: `${baseUrl}/oauth2/logout`,
      
      // 设备授权端点
      device_authorization_endpoint: `${baseUrl}/oauth2/device/auth`,
      
      // 推送授权请求端点
      pushed_authorization_request_endpoint: `${baseUrl}/oauth2/par`,
      
      // 支持的响应类型
      response_types_supported: [
        'code',
        'id_token',
        'token',
        'code id_token',
        'code token',
        'id_token token',
        'code id_token token'
      ],
      
      // 支持的授权类型
      grant_types_supported: [
        'authorization_code',
        'implicit',
        'refresh_token',
        'client_credentials',
        'urn:ietf:params:oauth:grant-type:device_code'
      ],
      
      // 支持的主题类型
      subject_types_supported: ['public', 'pairwise'],
      
      // 支持的ID令牌签名算法
      id_token_signing_alg_values_supported: [
        'RS256', 'RS384', 'RS512', 'ES256', 'ES384', 'ES512'
      ],
      
      // 支持的ID令牌加密算法
      id_token_encryption_alg_values_supported: [
        'RSA-OAEP', 'RSA-OAEP-256', 'A128KW', 'A192KW', 'A256KW'
      ],
      
      id_token_encryption_enc_values_supported: [
        'A128CBC-HS256', 'A192CBC-HS384', 'A256CBC-HS512',
        'A128GCM', 'A192GCM', 'A256GCM'
      ],
      
      // 支持的令牌端点认证方法
      token_endpoint_auth_methods_supported: [
        'client_secret_basic',
        'client_secret_post',
        'client_secret_jwt',
        'private_key_jwt',
        'none'
      ],
      
      // 支持的令牌端点签名算法
      token_endpoint_auth_signing_alg_values_supported: [
        'RS256', 'RS384', 'RS512', 'ES256', 'ES384', 'ES512'
      ],
      
      // 支持的声明
      claims_supported: [
        'sub', 'iss', 'aud', 'exp', 'iat', 'auth_time', 'nonce', 'acr', 'amr', 'azp',
        'name', 'family_name', 'given_name', 'middle_name', 'nickname', 'preferred_username',
        'profile', 'picture', 'website', 'gender', 'birthdate', 'zoneinfo', 'locale', 'updated_at',
        'email', 'email_verified', 'phone_number', 'phone_number_verified', 'address'
      ],
      
      // 支持的范围
      scopes_supported: ['openid', 'profile', 'email', 'phone', 'address', 'offline_access'],
      
      // 支持的代码挑战方法
      code_challenge_methods_supported: ['S256'],
      
      // 支持的请求参数
      request_parameter_supported: true,
      request_uri_parameter_supported: true,
      require_request_uri_registration: false,
      
      // 支持的声明参数
      claims_parameter_supported: true,
      
      // 支持的显示值
      display_values_supported: ['page', 'popup', 'touch', 'wap'],
      
      // 支持的提示值
      prompt_values_supported: ['none', 'login', 'consent', 'select_account'],
      
      // 支持的ACR值
      acr_values_supported: ['urn:mace:incommon:iap:silver', 'urn:mace:incommon:iap:bronze'],
      
      // 其他功能
      frontchannel_logout_supported: true,
      frontchannel_logout_session_supported: true,
      backchannel_logout_supported: true,
      backchannel_logout_session_supported: true,
      
      // DPoP支持
      dpop_signing_alg_values_supported: ['RS256', 'ES256'],
      
      // 推送授权请求支持
      require_pushed_authorization_requests: false,
      
      // 设备流程支持
      device_authorization_endpoint_auth_methods_supported: [
        'client_secret_basic', 'client_secret_post', 'private_key_jwt'
      ]
    };

    // 设置缓存头
    res.set({
      'Cache-Control': 'public, max-age=3600',
      'Content-Type': 'application/json'
    });

    res.status(200).json(configuration);

  } catch (error) {
    logger.error('OpenID Connect发现端点错误', { error });
    
    res.status(500).json({
      error: 'server_error',
      message: '无法生成OpenID Connect配置'
    });
  }
});

/**
 * @route GET /.well-known/jwks.json
 * @desc JSON Web Key Set (JWKS) 端点
 * @access Public
 */
router.get('/.well-known/jwks.json', protocolRateLimit, async (req, res) => {
  try {
    const jwks = await oidcProviderService.getJWKS();

    // 设置缓存头
    res.set({
      'Cache-Control': 'public, max-age=3600',
      'Content-Type': 'application/json'
    });

    res.status(200).json(jwks);

  } catch (error) {
    logger.error('JWKS端点错误', { error });
    
    res.status(500).json({
      error: 'server_error',
      message: '无法获取JWKS'
    });
  }
});

/**
 * @route GET /.well-known/saml-metadata
 * @desc SAML IdP 元数据端点
 * @access Public
 */
router.get('/.well-known/saml-metadata', protocolRateLimit, async (req, res) => {
  try {
    const metadata = samlIdPService.generateMetadata();

    res.set({
      'Content-Type': 'application/samlmetadata+xml',
      'Cache-Control': 'public, max-age=3600'
    });

    res.send(metadata);

  } catch (error) {
    logger.error('SAML元数据端点错误', { error });
    
    res.status(500).json({
      error: 'server_error',
      message: '无法生成SAML元数据'
    });
  }
});

/**
 * @route GET /.well-known/protocol-support
 * @desc 协议支持发现端点
 * @access Public
 */
router.get('/.well-known/protocol-support', protocolRateLimit, async (req, res) => {
  try {
    const baseUrl = getServerUrl();
    
    const protocolSupport = {
      issuer: baseUrl,
      protocols_supported: [
        {
          name: 'OpenID Connect 1.0',
          version: '1.0',
          discovery_endpoint: `${baseUrl}/.well-known/openid-configuration`,
          specification: 'https://openid.net/specs/openid-connect-core-1_0.html'
        },
        {
          name: 'OAuth 2.0',
          version: '2.0',
          specification: 'https://tools.ietf.org/html/rfc6749'
        },
        {
          name: 'SAML 2.0',
          version: '2.0',
          metadata_endpoint: `${baseUrl}/.well-known/saml-metadata`,
          specification: 'https://docs.oasis-open.org/security/saml/v2.0/saml-core-2.0-os.pdf'
        }
      ],
      endpoints: {
        authorization: `${baseUrl}/oauth2/authorize`,
        token: `${baseUrl}/oauth2/token`,
        userinfo: `${baseUrl}/oauth2/userinfo`,
        jwks: `${baseUrl}/.well-known/jwks.json`,
        revocation: `${baseUrl}/oauth2/revoke`,
        introspection: `${baseUrl}/oauth2/introspect`,
        end_session: `${baseUrl}/oauth2/logout`,
        device_authorization: `${baseUrl}/oauth2/device/auth`,
        saml_sso: `${baseUrl}/saml/sso`,
        saml_slo: `${baseUrl}/saml/slo`,
        saml_metadata: `${baseUrl}/saml/metadata`
      },
      features_supported: [
        'authorization_code_flow',
        'implicit_flow',
        'hybrid_flow',
        'client_credentials_flow',
        'device_authorization_flow',
        'refresh_tokens',
        'pkce',
        'dpop',
        'pushed_authorization_requests',
        'request_objects',
        'jwt_secured_authorization_response_mode',
        'backchannel_logout',
        'frontchannel_logout',
        'session_management',
        'saml_sso',
        'saml_slo'
      ]
    };

    // 设置缓存头
    res.set({
      'Cache-Control': 'public, max-age=3600',
      'Content-Type': 'application/json'
    });

    res.status(200).json(protocolSupport);

  } catch (error) {
    logger.error('协议支持发现端点错误', { error });
    
    res.status(500).json({
      error: 'server_error',
      message: '无法生成协议支持信息'
    });
  }
});

/**
 * @route GET /health
 * @desc 健康检查端点
 * @access Public
 */
router.get('/health', protocolRateLimit, async (req, res) => {
  try {
    const health = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      version: process.env.npm_package_version || '1.0.0',
      services: {
        oidc_provider: 'operational',
        saml_idp: 'operational',
        database: 'operational',
        cache: 'operational'
      }
    };

    res.status(200).json(health);

  } catch (error) {
    logger.error('健康检查端点错误', { error });
    
    res.status(503).json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: 'Service unavailable'
    });
  }
});

export default router;
