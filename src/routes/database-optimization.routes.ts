/**
 * 数据库优化路由
 * 提供数据库查询优化和性能监控的REST API端点
 */

import { Router } from 'express';
import { databaseOptimizationController } from '@/controllers/database-optimization.controller';
import { authenticateToken } from '@/middleware/auth.middleware';
import { requireRole } from '@/middleware/rbac.middleware';
import { apiRateLimit } from '@/middleware/security.middleware';

const router = Router();

// 应用认证中间件
router.use(authenticateToken);

// 应用限流中间件
router.use(apiRateLimit);

/**
 * 获取查询性能统计
 * GET /api/v1/database/query-stats
 * 
 * 响应格式：
 * {
 *   "success": true,
 *   "data": {
 *     "totalQueries": 1500,
 *     "slowQueries": 45,
 *     "averageDuration": 25.5,
 *     "queryTypeDistribution": {
 *       "SELECT": 1200,
 *       "INSERT": 150,
 *       "UPDATE": 100,
 *       "DELETE": 50
 *     },
 *     "topSlowQueries": [...],
 *     "nPlusOneProblems": [...],
 *     "indexSuggestions": [...],
 *     "timestamp": "2024-01-01T12:00:00.000Z"
 *   }
 * }
 */
router.get('/query-stats', requireRole(['admin', 'operator']), databaseOptimizationController.getQueryStats);

/**
 * 获取慢查询列表
 * GET /api/v1/database/slow-queries
 * 
 * 查询参数：
 * - limit: 返回数量限制（默认50，最大1000）
 * 
 * 响应格式：
 * {
 *   "success": true,
 *   "data": {
 *     "slowQueries": [
 *       {
 *         "id": "query_1234567890_abc123",
 *         "query": "SELECT * FROM users WHERE...",
 *         "queryType": "SELECT",
 *         "duration": 1250,
 *         "timestamp": "2024-01-01T12:00:00.000Z",
 *         "isSlowQuery": true,
 *         "severity": "high",
 *         "suggestions": ["添加索引", "优化WHERE条件"]
 *       }
 *     ],
 *     "count": 45,
 *     "timestamp": "2024-01-01T12:00:00.000Z"
 *   }
 * }
 */
router.get('/slow-queries', requireRole(['admin', 'operator']), databaseOptimizationController.getSlowQueries);

/**
 * 获取查询历史
 * GET /api/v1/database/query-history
 * 
 * 查询参数：
 * - limit: 返回数量限制（默认100，最大1000）
 * 
 * 响应格式：
 * {
 *   "success": true,
 *   "data": {
 *     "queries": [...],
 *     "count": 100,
 *     "timestamp": "2024-01-01T12:00:00.000Z"
 *   }
 * }
 */
router.get('/query-history', requireRole(['admin', 'operator']), databaseOptimizationController.getQueryHistory);

/**
 * 获取连接池状态
 * GET /api/v1/database/connection-pool
 * 
 * 响应格式：
 * {
 *   "success": true,
 *   "data": {
 *     "stats": {
 *       "totalConnections": 20,
 *       "activeConnections": 8,
 *       "idleConnections": 12,
 *       "pendingRequests": 2,
 *       "averageAcquireTime": 15.5,
 *       "connectionErrors": 0,
 *       "uptime": 3600000
 *     },
 *     "currentHealth": {
 *       "status": "healthy",
 *       "score": 95,
 *       "issues": [],
 *       "recommendations": []
 *     },
 *     "healthHistory": [...],
 *     "timestamp": "2024-01-01T12:00:00.000Z"
 *   }
 * }
 */
router.get('/connection-pool', requireRole(['admin', 'operator']), databaseOptimizationController.getConnectionPoolStatus);

/**
 * 获取索引分析结果
 * GET /api/v1/database/index-analysis
 * 
 * 响应格式：
 * {
 *   "success": true,
 *   "data": {
 *     "totalIndexes": 45,
 *     "unusedIndexes": [...],
 *     "duplicateIndexes": [...],
 *     "missingIndexes": [...],
 *     "oversizedIndexes": [...],
 *     "recommendations": [...],
 *     "totalSize": 52428800,
 *     "potentialSavings": 10485760,
 *     "timestamp": "2024-01-01T12:00:00.000Z"
 *   }
 * }
 */
router.get('/index-analysis', requireRole(['admin', 'operator']), databaseOptimizationController.getIndexAnalysis);

/**
 * 获取所有索引信息
 * GET /api/v1/database/indexes
 * 
 * 响应格式：
 * {
 *   "success": true,
 *   "data": {
 *     "indexes": [
 *       {
 *         "name": "idx_users_email",
 *         "tableName": "users",
 *         "columns": ["email"],
 *         "type": "BTREE",
 *         "isUnique": true,
 *         "size": 1048576,
 *         "usage": {
 *           "scans": 1500,
 *           "efficiency": 95.5
 *         },
 *         "recommendation": {
 *           "type": "keep",
 *           "priority": "low",
 *           "reason": "索引正在被使用"
 *         }
 *       }
 *     ],
 *     "count": 45,
 *     "timestamp": "2024-01-01T12:00:00.000Z"
 *   }
 * }
 */
router.get('/indexes', requireRole(['admin', 'operator']), databaseOptimizationController.getAllIndexes);

/**
 * 执行索引操作
 * POST /api/v1/database/index-operation
 * 
 * 请求体：
 * {
 *   "operation": "create|drop|analyze",
 *   "sql": "CREATE INDEX idx_users_created_at ON users (created_at);"
 * }
 * 
 * 响应格式：
 * {
 *   "success": true,
 *   "data": {
 *     "operation": "create",
 *     "sql": "CREATE INDEX idx_users_created_at ON users (created_at);",
 *     "executed": true,
 *     "message": "索引操作执行成功"
 *   }
 * }
 */
router.post('/index-operation', requireRole(['admin']), databaseOptimizationController.executeIndexOperation);

/**
 * 获取数据库统计信息
 * GET /api/v1/database/stats
 * 
 * 响应格式：
 * {
 *   "success": true,
 *   "data": {
 *     "tables": {
 *       "users": 1500,
 *       "activeSessions": 250,
 *       "applications": 25,
 *       "auditLogs": 50000,
 *       "riskAssessments": 3000
 *     },
 *     "totalRecords": 54775,
 *     "executionTime": 45,
 *     "timestamp": "2024-01-01T12:00:00.000Z"
 *   }
 * }
 */
router.get('/stats', requireRole(['admin', 'operator']), databaseOptimizationController.getDatabaseStats);

/**
 * 执行数据清理
 * POST /api/v1/database/cleanup
 * 
 * 响应格式：
 * {
 *   "success": true,
 *   "data": {
 *     "deletedSessions": 150,
 *     "deletedTokens": 75,
 *     "deletedCodes": 25,
 *     "executionTime": 1250,
 *     "message": "数据清理完成",
 *     "timestamp": "2024-01-01T12:00:00.000Z"
 *   }
 * }
 */
router.post('/cleanup', requireRole(['admin']), databaseOptimizationController.performDataCleanup);

/**
 * 获取优化建议
 * GET /api/v1/database/optimization-suggestions
 * 
 * 响应格式：
 * {
 *   "success": true,
 *   "data": {
 *     "suggestions": [
 *       {
 *         "category": "query_performance",
 *         "priority": "high",
 *         "title": "慢查询比例过高",
 *         "description": "慢查询占总查询的15.2%",
 *         "impact": "严重影响系统性能",
 *         "action": "优化慢查询SQL语句，添加必要索引"
 *       },
 *       {
 *         "category": "index_optimization",
 *         "priority": "medium",
 *         "title": "未使用的索引",
 *         "description": "发现5个未使用的索引",
 *         "impact": "可节省10MB存储空间",
 *         "action": "删除未使用的索引以减少维护开销"
 *       }
 *     ],
 *     "summary": {
 *       "totalSuggestions": 8,
 *       "highPriority": 2,
 *       "mediumPriority": 4,
 *       "lowPriority": 2
 *     },
 *     "timestamp": "2024-01-01T12:00:00.000Z"
 *   }
 * }
 */
router.get('/optimization-suggestions', requireRole(['admin', 'operator']), databaseOptimizationController.getOptimizationSuggestions);

/**
 * 重置查询统计
 * POST /api/v1/database/reset-stats
 * 
 * 响应格式：
 * {
 *   "success": true,
 *   "data": {
 *     "message": "查询统计已重置",
 *     "timestamp": "2024-01-01T12:00:00.000Z"
 *   }
 * }
 */
router.post('/reset-stats', requireRole(['admin']), databaseOptimizationController.resetQueryStats);

export default router;
