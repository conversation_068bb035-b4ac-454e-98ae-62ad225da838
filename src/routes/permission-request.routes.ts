/**
 * 权限申请工作流路由
 * 提供权限申请、审批和管理的API接口
 */

import { Router } from 'express';
import { body, param, query, validationResult } from 'express-validator';
import { authenticateToken } from '@/middleware/auth.middleware';
import { requireOrganizationPermission } from '@/middleware/organization-permission.middleware';
import { 
  permissionRequestService, 
  PermissionRequestType, 
  PermissionRequestPriority,
  PermissionRequestStatus 
} from '@/services/permission-request.service';
import { logger } from '@/config/logger';
import rateLimit from 'express-rate-limit';

const router = Router();

// 速率限制配置
const requestRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 50, // 每个IP最多50个请求
  message: {
    success: false,
    error: 'RATE_LIMIT_EXCEEDED',
    message: '权限申请请求过于频繁，请稍后再试'
  }
});

// 验证错误处理中间件
const handleValidationErrors = (req: any, res: any, next: any) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      error: 'VALIDATION_ERROR',
      message: '请求参数验证失败',
      details: errors.array()
    });
  }
  next();
};

/**
 * @route POST /permission-requests
 * @desc 创建权限申请
 * @access Private
 */
router.post('/',
  requestRateLimit,
  authenticateToken as any,
  [
    body('targetOrganizationId')
      .isUUID()
      .withMessage('目标组织ID格式无效'),
    body('requestedPermissions')
      .isArray({ min: 1 })
      .withMessage('申请权限列表不能为空'),
    body('requestedPermissions.*')
      .isString()
      .notEmpty()
      .withMessage('权限名称不能为空'),
    body('requestType')
      .isIn(['temporary', 'project', 'data_sharing', 'delegation'])
      .withMessage('申请类型无效'),
    body('reason')
      .notEmpty()
      .isLength({ min: 10, max: 500 })
      .withMessage('申请原因长度必须在10-500字符之间'),
    body('businessJustification')
      .optional()
      .isLength({ max: 1000 })
      .withMessage('业务理由长度不能超过1000字符'),
    body('priority')
      .optional()
      .isIn(['low', 'normal', 'high', 'urgent'])
      .withMessage('优先级无效'),
    body('requestedDuration')
      .optional()
      .isInt({ min: 1, max: 720 })
      .withMessage('申请时长必须在1-720小时之间'),
    body('targetUserId')
      .optional()
      .isUUID()
      .withMessage('目标用户ID格式无效')
  ],
  handleValidationErrors,
  async (req, res) => {
    try {
      const userId = req.user?.id;
      
      const requestParams = {
        targetOrganizationId: req.body.targetOrganizationId,
        requestedPermissions: req.body.requestedPermissions,
        requestType: req.body.requestType as PermissionRequestType,
        reason: req.body.reason,
        businessJustification: req.body.businessJustification,
        priority: (req.body.priority as PermissionRequestPriority) || 'normal',
        requestedDuration: req.body.requestedDuration,
        targetUserId: req.body.targetUserId
      };

      const request = await permissionRequestService.createPermissionRequest(
        userId,
        requestParams
      );

      res.status(201).json({
        success: true,
        message: '权限申请创建成功',
        data: request
      });

    } catch (error) {
      logger.error('创建权限申请失败', {
        error: error instanceof Error ? error.message : String(error),
        userId: req.user?.id,
        body: req.body
      });

      res.status(500).json({
        success: false,
        error: 'PERMISSION_REQUEST_CREATION_FAILED',
        message: error instanceof Error ? error.message : '创建权限申请失败'
      });
    }
  }
);

/**
 * @route GET /permission-requests/:id
 * @desc 获取权限申请详情
 * @access Private
 */
router.get('/:id',
  requestRateLimit,
  authenticateToken as any,
  param('id').isUUID().withMessage('申请ID格式无效'),
  handleValidationErrors,
  async (req, res) => {
    try {
      const requestId = req.params.id;
      const userId = req.user?.id;

      const request = await permissionRequestService.getPermissionRequestById(requestId);

      if (!request) {
        return res.status(404).json({
          success: false,
          error: 'PERMISSION_REQUEST_NOT_FOUND',
          message: '权限申请不存在'
        });
      }

      // 检查用户是否有权限查看该申请
      const canView = request.requesterId === userId || 
                     request.targetUserId === userId ||
                     await this.canUserViewRequest(userId, request);

      if (!canView) {
        return res.status(403).json({
          success: false,
          error: 'INSUFFICIENT_PERMISSION',
          message: '没有权限查看该申请'
        });
      }

      res.json({
        success: true,
        data: request
      });

    } catch (error) {
      logger.error('获取权限申请详情失败', {
        error: error instanceof Error ? error.message : String(error),
        requestId: req.params.id,
        userId: req.user?.id
      });

      res.status(500).json({
        success: false,
        error: 'GET_PERMISSION_REQUEST_FAILED',
        message: '获取权限申请详情失败'
      });
    }
  }
);

/**
 * @route POST /permission-requests/:id/approve
 * @desc 审批权限申请
 * @access Private (需要审批权限)
 */
router.post('/:id/approve',
  requestRateLimit,
  authenticateToken as any,
  [
    param('id').isUUID().withMessage('申请ID格式无效'),
    body('approvedPermissions')
      .optional()
      .isArray()
      .withMessage('批准权限列表格式无效'),
    body('approvedDuration')
      .optional()
      .isInt({ min: 1, max: 720 })
      .withMessage('批准时长必须在1-720小时之间'),
    body('conditions')
      .optional()
      .isObject()
      .withMessage('条件参数格式无效'),
    body('comments')
      .optional()
      .isLength({ max: 500 })
      .withMessage('备注长度不能超过500字符')
  ],
  handleValidationErrors,
  async (req, res) => {
    try {
      const requestId = req.params.id;
      const approverId = req.user?.id;

      const approveParams = {
        requestId,
        approverId,
        approvedPermissions: req.body.approvedPermissions,
        approvedDuration: req.body.approvedDuration,
        conditions: req.body.conditions,
        comments: req.body.comments
      };

      const approvedRequest = await permissionRequestService.approvePermissionRequest(
        approveParams
      );

      res.json({
        success: true,
        message: '权限申请审批成功',
        data: approvedRequest
      });

    } catch (error) {
      logger.error('审批权限申请失败', {
        error: error instanceof Error ? error.message : String(error),
        requestId: req.params.id,
        approverId: req.user?.id
      });

      res.status(500).json({
        success: false,
        error: 'PERMISSION_REQUEST_APPROVAL_FAILED',
        message: error instanceof Error ? error.message : '审批权限申请失败'
      });
    }
  }
);

/**
 * @route POST /permission-requests/:id/reject
 * @desc 拒绝权限申请
 * @access Private (需要审批权限)
 */
router.post('/:id/reject',
  requestRateLimit,
  authenticateToken as any,
  [
    param('id').isUUID().withMessage('申请ID格式无效'),
    body('reason')
      .notEmpty()
      .isLength({ min: 5, max: 500 })
      .withMessage('拒绝原因长度必须在5-500字符之间'),
    body('comments')
      .optional()
      .isLength({ max: 500 })
      .withMessage('备注长度不能超过500字符')
  ],
  handleValidationErrors,
  async (req, res) => {
    try {
      const requestId = req.params.id;
      const rejectorId = req.user?.id;

      const rejectParams = {
        requestId,
        rejectorId,
        reason: req.body.reason,
        comments: req.body.comments
      };

      const rejectedRequest = await permissionRequestService.rejectPermissionRequest(
        rejectParams
      );

      res.json({
        success: true,
        message: '权限申请拒绝成功',
        data: rejectedRequest
      });

    } catch (error) {
      logger.error('拒绝权限申请失败', {
        error: error instanceof Error ? error.message : String(error),
        requestId: req.params.id,
        rejectorId: req.user?.id
      });

      res.status(500).json({
        success: false,
        error: 'PERMISSION_REQUEST_REJECTION_FAILED',
        message: error instanceof Error ? error.message : '拒绝权限申请失败'
      });
    }
  }
);

/**
 * @route GET /permission-requests
 * @desc 获取权限申请列表
 * @access Private
 */
router.get('/',
  requestRateLimit,
  authenticateToken as any,
  [
    query('status')
      .optional()
      .isIn(['pending', 'approved', 'rejected', 'expired', 'revoked'])
      .withMessage('状态筛选无效'),
    query('type')
      .optional()
      .isIn(['temporary', 'project', 'data_sharing', 'delegation'])
      .withMessage('类型筛选无效'),
    query('page')
      .optional()
      .isInt({ min: 1 })
      .withMessage('页码必须是正整数'),
    query('limit')
      .optional()
      .isInt({ min: 1, max: 100 })
      .withMessage('每页数量必须在1-100之间'),
    query('organizationId')
      .optional()
      .isUUID()
      .withMessage('组织ID格式无效')
  ],
  handleValidationErrors,
  async (req, res) => {
    try {
      const userId = req.user?.id;
      const options = {
        status: req.query.status as PermissionRequestStatus,
        type: req.query.type as PermissionRequestType,
        page: parseInt(req.query.page as string) || 1,
        limit: parseInt(req.query.limit as string) || 20
      };

      const result = await permissionRequestService.getUserPermissionRequests(
        userId,
        options
      );

      res.json({
        success: true,
        data: {
          requests: result.requests,
          pagination: {
            page: options.page,
            limit: options.limit,
            total: result.total,
            pages: Math.ceil(result.total / options.limit)
          }
        }
      });

    } catch (error) {
      logger.error('获取权限申请列表失败', {
        error: error instanceof Error ? error.message : String(error),
        userId: req.user?.id,
        query: req.query
      });

      res.status(500).json({
        success: false,
        error: 'GET_PERMISSION_REQUESTS_FAILED',
        message: '获取权限申请列表失败'
      });
    }
  }
);

/**
 * @route GET /permission-requests/pending-approval
 * @desc 获取待审批的权限申请列表
 * @access Private (需要审批权限)
 */
router.get('/pending-approval',
  requestRateLimit,
  authenticateToken as any,
  [
    query('organizationId')
      .optional()
      .isUUID()
      .withMessage('组织ID格式无效'),
    query('priority')
      .optional()
      .isIn(['low', 'normal', 'high', 'urgent'])
      .withMessage('优先级筛选无效'),
    query('page')
      .optional()
      .isInt({ min: 1 })
      .withMessage('页码必须是正整数'),
    query('limit')
      .optional()
      .isInt({ min: 1, max: 100 })
      .withMessage('每页数量必须在1-100之间')
  ],
  handleValidationErrors,
  async (req, res) => {
    try {
      const userId = req.user?.id;
      const organizationId = req.query.organizationId as string;
      const priority = req.query.priority as PermissionRequestPriority;
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 20;

      // 获取用户可以审批的组织列表
      const userOrgs = await organizationService.getUserOrganizations(userId);
      const approverOrgIds = userOrgs
        .filter(org => org.role === 'admin' || org.role === 'manager' || org.isOwner)
        .map(org => org.organizationId);

      if (approverOrgIds.length === 0) {
        return res.json({
          success: true,
          data: {
            requests: [],
            pagination: { page, limit, total: 0, pages: 0 }
          }
        });
      }

      // 构建查询条件
      const whereClause: any = {
        status: 'pending',
        targetOrganizationId: {
          in: organizationId ? [organizationId] : approverOrgIds
        }
      };

      if (priority) {
        whereClause.priority = priority;
      }

      // 查询待审批申请
      const [requests, total] = await Promise.all([
        prisma.permissionRequest.findMany({
          where: whereClause,
          include: {
            requester: {
              select: {
                id: true,
                email: true,
                username: true,
                firstName: true,
                lastName: true
              }
            },
            targetOrganization: true,
            targetUser: {
              select: {
                id: true,
                email: true,
                username: true,
                firstName: true,
                lastName: true
              }
            }
          },
          skip: (page - 1) * limit,
          take: limit,
          orderBy: [
            { priority: 'desc' },
            { createdAt: 'asc' }
          ]
        }),
        prisma.permissionRequest.count({ where: whereClause })
      ]);

      res.json({
        success: true,
        data: {
          requests,
          pagination: {
            page,
            limit,
            total,
            pages: Math.ceil(total / limit)
          }
        }
      });

    } catch (error) {
      logger.error('获取待审批权限申请列表失败', {
        error: error instanceof Error ? error.message : String(error),
        userId: req.user?.id,
        query: req.query
      });

      res.status(500).json({
        success: false,
        error: 'GET_PENDING_REQUESTS_FAILED',
        message: '获取待审批权限申请列表失败'
      });
    }
  }
);

/**
 * 检查用户是否可以查看权限申请
 */
async function canUserViewRequest(userId: string, request: any): Promise<boolean> {
  try {
    // 检查用户是否为目标组织的管理员
    const hasPermission = await organizationPermissionService.validatePermission({
      userId,
      organizationId: request.targetOrganizationId,
      action: 'view:permission_request'
    });

    return hasPermission.granted;
  } catch (error) {
    logger.error('检查权限申请查看权限失败', { userId, requestId: request.id, error });
    return false;
  }
}

export default router;
