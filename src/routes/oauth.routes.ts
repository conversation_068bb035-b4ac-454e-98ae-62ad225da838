/**
 * OAuth路由
 * 定义第三方OAuth登录相关的API端点
 */

import { Router } from 'express';
import { OAuthController } from '@/controllers/oauth.controller';
import { authenticateToken } from '@/middleware/auth.middleware';
import rateLimit from 'express-rate-limit';

const router = Router();
const oauthController = new OAuthController();

// OAuth速率限制配置
const oauthRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 20, // 每个IP最多20次OAuth请求
  message: {
    error: 'oauth_rate_limit_exceeded',
    message: 'OAuth请求过于频繁，请稍后再试'
  },
  standardHeaders: true,
  legacyHeaders: false
});

/**
 * @route GET /api/v1/auth/providers
 * @desc 获取支持的OAuth提供商列表
 * @access Public
 */
router.get('/providers', oauthRateLimit, oauthController.getProviders);

/**
 * @route GET /api/v1/auth/google
 * @desc 发起Google OAuth登录
 * @access Public
 */
router.get('/google', oauthRateLimit, oauthController.googleAuth);

/**
 * @route GET /api/v1/auth/google/callback
 * @desc Google OAuth回调处理
 * @access Public
 */
router.get('/google/callback', oauthController.googleCallback);

/**
 * @route GET /api/v1/auth/github
 * @desc 发起GitHub OAuth登录
 * @access Public
 */
router.get('/github', oauthRateLimit, oauthController.githubAuth);

/**
 * @route GET /api/v1/auth/github/callback
 * @desc GitHub OAuth回调处理
 * @access Public
 */
router.get('/github/callback', oauthController.githubCallback);

/**
 * @route GET /api/v1/auth/wechat
 * @desc 发起微信OAuth登录
 * @access Public
 */
router.get('/wechat', oauthRateLimit, oauthController.wechatAuth);

/**
 * @route GET /api/v1/auth/wechat/callback
 * @desc 微信OAuth回调处理
 * @access Public
 */
router.get('/wechat/callback', oauthController.wechatCallback);

/**
 * @route GET /api/v1/auth/weibo
 * @desc 发起微博OAuth登录
 * @access Public
 */
router.get('/weibo', oauthRateLimit, oauthController.weiboAuth);

/**
 * @route GET /api/v1/auth/weibo/callback
 * @desc 微博OAuth回调处理
 * @access Public
 */
router.get('/weibo/callback', oauthController.weiboCallback);

/**
 * @route GET /api/v1/auth/qq
 * @desc 发起QQ OAuth登录
 * @access Public
 */
router.get('/qq', oauthRateLimit, oauthController.qqAuth);

/**
 * @route GET /api/v1/auth/qq/callback
 * @desc QQ OAuth回调处理
 * @access Public
 */
router.get('/qq/callback', oauthController.qqCallback);

/**
 * @route GET /api/v1/auth/alipay
 * @desc 发起支付宝OAuth登录
 * @access Public
 */
router.get('/alipay', oauthRateLimit, oauthController.alipayAuth);

/**
 * @route GET /api/v1/auth/alipay/callback
 * @desc 支付宝OAuth回调处理
 * @access Public
 */
router.get('/alipay/callback', oauthController.alipayCallback);

/**
 * @route GET /api/v1/auth/dingtalk
 * @desc 发起钉钉OAuth登录
 * @access Public
 */
router.get('/dingtalk', oauthRateLimit, oauthController.dingtalkAuth);

/**
 * @route GET /api/v1/auth/dingtalk/callback
 * @desc 钉钉OAuth回调处理
 * @access Public
 */
router.get('/dingtalk/callback', oauthController.dingtalkCallback);

/**
 * @route DELETE /api/v1/auth/disconnect/:provider
 * @desc 解除OAuth提供商关联
 * @access Private
 */
router.delete('/disconnect/:provider',
  authenticateToken as any,
  oauthRateLimit,
  oauthController.disconnectProvider
);

export default router;
