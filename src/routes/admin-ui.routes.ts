/**
 * 管理员UI路由
 * 提供管理员Web界面的静态文件服务
 */

import { Router } from 'express';
import path from 'path';
import fs from 'fs';
import { logger } from '@/config/logger';
import { authenticateToken } from '@/middleware/auth.middleware';

const router = Router();

/**
 * 管理员权限检查中间件
 */
const requireAdminAccess = (req: any, res: any, next: any) => {
  const user = req.user;
  
  if (!user) {
    return res.redirect('/auth/login?redirect=/admin');
  }

  // 检查管理员权限
  if (!user.isAdmin && !user.permissions?.includes('admin.access')) {
    return res.status(403).render('error', {
      title: '访问被拒绝',
      message: '您没有访问管理控制台的权限',
      code: 403
    });
  }

  next();
};

/**
 * @route GET /admin
 * @desc 管理员控制台主页
 * @access Private (管理员)
 */
router.get('/',
  authenticateToken as any,
  requireAdminAccess,
  (req, res) => {
    try {
      const adminHtmlPath = path.join(process.cwd(), 'public', 'admin.html');
      
      // 检查文件是否存在
      if (!fs.existsSync(adminHtmlPath)) {
        logger.error('管理员HTML文件不存在', { path: adminHtmlPath });
        return res.status(500).render('error', {
          title: '服务器错误',
          message: '管理控制台文件缺失',
          code: 500
        });
      }

      // 读取HTML文件
      let htmlContent = fs.readFileSync(adminHtmlPath, 'utf8');
      
      // 注入用户信息和配置
      const userInfo = {
        id: req.user.id,
        username: req.user.username,
        email: req.user.email,
        isAdmin: req.user.isAdmin,
        permissions: req.user.permissions || []
      };

      const appConfig = {
        API_BASE_URL: '/admin-api',
        CONFIG_API_URL: '/config',
        USER_INFO: userInfo,
        VERSION: process.env.APP_VERSION || '1.0.0',
        BUILD_TIME: new Date().toISOString(),
        FEATURES: {
          DARK_MODE: true,
          EXPORT: true,
          NOTIFICATIONS: true,
          REAL_TIME: process.env.ENABLE_REAL_TIME === 'true'
        }
      };

      // 替换配置占位符
      htmlContent = htmlContent.replace(
        'window.APP_CONFIG = {',
        `window.APP_CONFIG = ${JSON.stringify(appConfig, null, 2).slice(1)}`
      );

      res.setHeader('Content-Type', 'text/html; charset=utf-8');
      res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
      res.setHeader('Pragma', 'no-cache');
      res.setHeader('Expires', '0');
      
      res.send(htmlContent);

    } catch (error) {
      logger.error('提供管理员界面失败', { error });
      res.status(500).render('error', {
        title: '服务器错误',
        message: '无法加载管理控制台',
        code: 500
      });
    }
  }
);

/**
 * @route GET /admin/health
 * @desc 管理员界面健康检查
 * @access Private (管理员)
 */
router.get('/health',
  authenticateToken as any,
  requireAdminAccess,
  (req, res) => {
    try {
      const health = {
        status: 'healthy',
        timestamp: new Date().toISOString(),
        version: process.env.APP_VERSION || '1.0.0',
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        user: {
          id: req.user.id,
          username: req.user.username,
          isAdmin: req.user.isAdmin
        }
      };

      res.json(health);

    } catch (error) {
      logger.error('管理员界面健康检查失败', { error });
      res.status(500).json({
        status: 'unhealthy',
        error: '健康检查失败'
      });
    }
  }
);

/**
 * @route GET /admin/config
 * @desc 获取管理员界面配置
 * @access Private (管理员)
 */
router.get('/config',
  authenticateToken as any,
  requireAdminAccess,
  (req, res) => {
    try {
      const config = {
        api: {
          baseUrl: '/admin-api',
          configUrl: '/config',
          timeout: 30000
        },
        ui: {
          theme: 'light',
          language: 'zh-CN',
          pageSize: 25,
          enableDarkMode: true,
          enableExport: true,
          enableNotifications: true
        },
        features: {
          realTime: process.env.ENABLE_REAL_TIME === 'true',
          audit: true,
          backup: true,
          monitoring: true
        },
        user: {
          id: req.user.id,
          username: req.user.username,
          email: req.user.email,
          isAdmin: req.user.isAdmin,
          permissions: req.user.permissions || []
        },
        system: {
          version: process.env.APP_VERSION || '1.0.0',
          buildTime: process.env.BUILD_TIME || new Date().toISOString(),
          environment: process.env.NODE_ENV || 'development'
        }
      };

      res.json(config);

    } catch (error) {
      logger.error('获取管理员界面配置失败', { error });
      res.status(500).json({
        error: 'config_error',
        message: '获取配置失败'
      });
    }
  }
);

/**
 * @route POST /admin/logout
 * @desc 管理员登出
 * @access Private (管理员)
 */
router.post('/logout',
  authenticateToken as any,
  requireAdminAccess,
  async (req, res) => {
    try {
      // 这里可以添加登出逻辑，如清除会话、记录审计日志等
      logger.info('管理员登出', { 
        userId: req.user.id, 
        username: req.user.username 
      });

      res.json({
        success: true,
        message: '登出成功',
        redirectUrl: '/auth/login'
      });

    } catch (error) {
      logger.error('管理员登出失败', { error });
      res.status(500).json({
        error: 'logout_error',
        message: '登出失败'
      });
    }
  }
);

/**
 * @route GET /admin/manifest.json
 * @desc PWA清单文件
 * @access Public
 */
router.get('/manifest.json', (req, res) => {
  const manifest = {
    name: '身份提供商管理控制台',
    short_name: '管理控制台',
    description: '企业级身份认证和权限管理平台的管理控制台',
    start_url: '/admin',
    display: 'standalone',
    background_color: '#1976d2',
    theme_color: '#1976d2',
    orientation: 'portrait-primary',
    icons: [
      {
        src: '/icons/icon-72x72.png',
        sizes: '72x72',
        type: 'image/png'
      },
      {
        src: '/icons/icon-96x96.png',
        sizes: '96x96',
        type: 'image/png'
      },
      {
        src: '/icons/icon-128x128.png',
        sizes: '128x128',
        type: 'image/png'
      },
      {
        src: '/icons/icon-144x144.png',
        sizes: '144x144',
        type: 'image/png'
      },
      {
        src: '/icons/icon-152x152.png',
        sizes: '152x152',
        type: 'image/png'
      },
      {
        src: '/icons/icon-192x192.png',
        sizes: '192x192',
        type: 'image/png'
      },
      {
        src: '/icons/icon-384x384.png',
        sizes: '384x384',
        type: 'image/png'
      },
      {
        src: '/icons/icon-512x512.png',
        sizes: '512x512',
        type: 'image/png'
      }
    ]
  };

  res.setHeader('Content-Type', 'application/json');
  res.setHeader('Cache-Control', 'public, max-age=86400'); // 缓存1天
  res.json(manifest);
});

/**
 * @route GET /admin/sw.js
 * @desc Service Worker文件
 * @access Public
 */
router.get('/sw.js', (req, res) => {
  const serviceWorker = `
// 管理控制台 Service Worker
const CACHE_NAME = 'admin-console-v1';
const urlsToCache = [
  '/admin',
  '/admin/manifest.json',
  '/fonts/roboto-v30-latin-regular.woff2',
  '/fonts/roboto-v30-latin-500.woff2'
];

self.addEventListener('install', function(event) {
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then(function(cache) {
        return cache.addAll(urlsToCache);
      })
  );
});

self.addEventListener('fetch', function(event) {
  event.respondWith(
    caches.match(event.request)
      .then(function(response) {
        if (response) {
          return response;
        }
        return fetch(event.request);
      }
    )
  );
});

self.addEventListener('activate', function(event) {
  event.waitUntil(
    caches.keys().then(function(cacheNames) {
      return Promise.all(
        cacheNames.map(function(cacheName) {
          if (cacheName !== CACHE_NAME) {
            return caches.delete(cacheName);
          }
        })
      );
    })
  );
});
`;

  res.setHeader('Content-Type', 'application/javascript');
  res.setHeader('Cache-Control', 'public, max-age=3600'); // 缓存1小时
  res.send(serviceWorker);
});

export default router;
