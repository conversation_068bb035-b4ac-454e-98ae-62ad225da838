/**
 * JWKS (JSON Web Key Set) 路由
 * 提供公钥端点供API网关验证JWT令牌
 */

import { Router, Request, Response } from 'express';
import { logger } from '@/config/logger';
import { config } from '@/config';
import crypto from 'crypto';
import rateLimit from 'express-rate-limit';

const router = Router();

// 速率限制配置
const jwksRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 1000, // JWKS端点访问频率较高
  message: {
    error: 'rate_limit_exceeded',
    message: '请求过于频繁，请稍后再试'
  }
});

/**
 * 生成RSA密钥对（用于JWT签名）
 * 在生产环境中，这些密钥应该从安全的密钥管理服务获取
 */
function generateKeyPair() {
  const { publicKey, privateKey } = crypto.generateKeyPairSync('rsa', {
    modulusLength: 2048,
    publicKeyEncoding: {
      type: 'spki',
      format: 'pem'
    },
    privateKeyEncoding: {
      type: 'pkcs8',
      format: 'pem'
    }
  });

  return { publicKey, privateKey };
}

/**
 * 将PEM格式的公钥转换为JWK格式
 */
function pemToJwk(pem: string, keyId: string) {
  // 移除PEM头尾和换行符
  const pemContent = pem
    .replace(/-----BEGIN PUBLIC KEY-----/, '')
    .replace(/-----END PUBLIC KEY-----/, '')
    .replace(/\n/g, '');

  // 解析公钥
  const keyBuffer = Buffer.from(pemContent, 'base64');
  const key = crypto.createPublicKey({
    key: keyBuffer,
    format: 'der',
    type: 'spki'
  });

  // 导出为JWK格式
  const jwk = key.export({ format: 'jwk' });

  return {
    kty: jwk.kty,
    use: 'sig',
    key_ops: ['verify'],
    alg: 'RS256',
    kid: keyId,
    n: jwk.n,
    e: jwk.e
  };
}

/**
 * 获取或生成密钥对
 * 在实际应用中，应该从配置文件或密钥管理服务获取
 */
function getKeyPair() {
  // 这里使用JWT_SECRET作为种子生成一致的密钥对
  // 在生产环境中应该使用专门的RSA密钥对
  const seed = config.jwt.secret;
  const hash = crypto.createHash('sha256').update(seed).digest('hex');
  
  // 为了演示，我们使用固定的密钥ID
  const keyId = hash.substring(0, 8);
  
  // 在实际应用中，这里应该从安全存储中获取密钥
  const { publicKey, privateKey } = generateKeyPair();
  
  return {
    keyId,
    publicKey,
    privateKey
  };
}

/**
 * @route GET /.well-known/jwks.json
 * @desc JSON Web Key Set端点
 * @access Public
 */
router.get('/.well-known/jwks.json', jwksRateLimit, (req: Request, res: Response) => {
  try {
    const { keyId, publicKey } = getKeyPair();
    
    // 转换为JWK格式
    const jwk = pemToJwk(publicKey, keyId);
    
    const jwks = {
      keys: [jwk]
    };

    // 设置缓存头
    res.set({
      'Cache-Control': 'public, max-age=3600', // 缓存1小时
      'Content-Type': 'application/json'
    });

    res.status(200).json(jwks);

  } catch (error) {
    logger.error('JWKS端点错误', { error });
    
    res.status(500).json({
      error: 'server_error',
      message: '无法生成JWKS'
    });
  }
});

/**
 * @route GET /.well-known/openid-configuration
 * @desc OpenID Connect发现端点
 * @access Public
 */
router.get('/.well-known/openid-configuration', jwksRateLimit, (req: Request, res: Response) => {
  try {
    const baseUrl = `${req.protocol}://${req.get('host')}`;
    
    const configuration = {
      issuer: baseUrl,
      authorization_endpoint: `${baseUrl}/oauth2/authorize`,
      token_endpoint: `${baseUrl}/oauth2/token`,
      userinfo_endpoint: `${baseUrl}/api/v1/me`,
      jwks_uri: `${baseUrl}/.well-known/jwks.json`,
      introspection_endpoint: `${baseUrl}/api/v1/auth/introspect`,
      
      // 支持的响应类型
      response_types_supported: [
        'code',
        'token',
        'id_token',
        'code token',
        'code id_token',
        'token id_token',
        'code token id_token'
      ],
      
      // 支持的授权类型
      grant_types_supported: [
        'authorization_code',
        'client_credentials',
        'refresh_token',
        'implicit'
      ],
      
      // 支持的主题标识符类型
      subject_types_supported: ['public'],
      
      // 支持的ID令牌签名算法
      id_token_signing_alg_values_supported: ['RS256'],
      
      // 支持的权限范围
      scopes_supported: [
        'openid',
        'profile',
        'email',
        'offline_access'
      ],
      
      // 支持的令牌端点认证方法
      token_endpoint_auth_methods_supported: [
        'client_secret_basic',
        'client_secret_post',
        'private_key_jwt'
      ],
      
      // 支持的声明
      claims_supported: [
        'sub',
        'iss',
        'aud',
        'exp',
        'iat',
        'email',
        'email_verified',
        'name',
        'nickname',
        'picture'
      ],
      
      // 支持的代码挑战方法
      code_challenge_methods_supported: ['S256'],
      
      // 其他端点
      revocation_endpoint: `${baseUrl}/oauth2/revoke`,
      end_session_endpoint: `${baseUrl}/oauth2/logout`
    };

    // 设置缓存头
    res.set({
      'Cache-Control': 'public, max-age=3600',
      'Content-Type': 'application/json'
    });

    res.status(200).json(configuration);

  } catch (error) {
    logger.error('OpenID Connect发现端点错误', { error });
    
    res.status(500).json({
      error: 'server_error',
      message: '无法生成OpenID Connect配置'
    });
  }
});

/**
 * @route GET /api/v1/jwks/key-info
 * @desc 获取密钥信息（管理员专用）
 * @access Private (Admin)
 */
router.get('/api/v1/jwks/key-info', (req: Request, res: Response) => {
  try {
    const { keyId } = getKeyPair();
    
    const keyInfo = {
      keyId,
      algorithm: 'RS256',
      usage: 'JWT签名验证',
      createdAt: new Date().toISOString(),
      status: 'active'
    };

    res.status(200).json(keyInfo);

  } catch (error) {
    logger.error('获取密钥信息失败', { error });
    
    res.status(500).json({
      error: 'server_error',
      message: '无法获取密钥信息'
    });
  }
});

export default router;
