/**
 * 非标准应用路由
 * 定义非标准应用相关的API端点
 */

import { Router } from 'express';
import { NonStandardAppController } from '@/controllers/non-standard-app.controller';
import { authenticateToken, requireRole } from '@/middleware/auth.middleware';
import { ProtocolAdapterService } from '@/services/protocol-adapter.service';
import { PluginManager } from '@/services/plugin-manager.service';
import rateLimit from 'express-rate-limit';

const router = Router();

// 创建服务实例
const protocolAdapterService = new ProtocolAdapterService();
const pluginManager = new PluginManager();
const nonStandardAppController = new NonStandardAppController(
  protocolAdapterService,
  pluginManager
);

// 速率限制配置
const authRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 100, // 每个IP最多100次认证请求
  message: {
    error: 'auth_rate_limit_exceeded',
    message: '认证请求过于频繁，请稍后再试'
  }
});

const adminRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 50, // 每个IP最多50次管理请求
  message: {
    error: 'admin_rate_limit_exceeded',
    message: '管理请求过于频繁，请稍后再试'
  }
});

/**
 * 公开端点 - 认证相关
 */

/**
 * @route GET /nsa/protocols
 * @desc 获取支持的协议列表
 * @access Public
 */
router.get('/protocols', nonStandardAppController.getSupportedProtocols);

/**
 * @route GET /nsa/protocols/:protocolName/metadata
 * @desc 获取协议元数据
 * @access Public
 */
router.get('/protocols/:protocolName/metadata', nonStandardAppController.getProtocolMetadata);

/**
 * @route GET /nsa/auth/:applicationId/:protocolName
 * @desc 处理自定义认证请求
 * @access Public
 */
router.get('/auth/:applicationId/:protocolName', 
  authRateLimit,
  nonStandardAppController.handleCustomAuth
);

/**
 * @route POST /nsa/auth/:applicationId/:protocolName
 * @desc 处理自定义认证请求（POST方式）
 * @access Public
 */
router.post('/auth/:applicationId/:protocolName',
  authRateLimit,
  nonStandardAppController.handleCustomAuth
);

/**
 * @route POST /nsa/token/:applicationId/:protocolName
 * @desc 处理令牌端点请求
 * @access Public
 */
router.post('/token/:applicationId/:protocolName',
  authRateLimit,
  nonStandardAppController.handleTokenEndpoint
);

/**
 * @route GET /nsa/userinfo/:applicationId/:protocolName
 * @desc 处理用户信息端点请求
 * @access Public
 */
router.get('/userinfo/:applicationId/:protocolName',
  authRateLimit,
  nonStandardAppController.handleUserInfoEndpoint
);

/**
 * @route POST /nsa/userinfo/:applicationId/:protocolName
 * @desc 处理用户信息端点请求（POST方式）
 * @access Public
 */
router.post('/userinfo/:applicationId/:protocolName',
  authRateLimit,
  nonStandardAppController.handleUserInfoEndpoint
);

/**
 * 管理端点 - 需要认证和授权
 */

/**
 * @route POST /nsa/admin/apps
 * @desc 创建非标准应用配置
 * @access Private (Admin)
 */
router.post('/admin/apps',
  authenticateToken as any,
  requireRole('admin'),
  adminRateLimit,
  nonStandardAppController.createNonStandardApp
);

/**
 * @route PUT /nsa/admin/apps/:applicationId
 * @desc 更新非标准应用配置
 * @access Private (Admin)
 */
router.put('/admin/apps/:applicationId',
  authenticateToken as any,
  requireRole('admin'),
  adminRateLimit,
  nonStandardAppController.updateNonStandardApp
);

/**
 * @route POST /nsa/admin/apps/:applicationId/test
 * @desc 测试非标准应用连接
 * @access Private (Admin)
 */
router.post('/admin/apps/:applicationId/test',
  authenticateToken as any,
  requireRole('admin'),
  adminRateLimit,
  nonStandardAppController.testAppConnection
);

/**
 * @route POST /nsa/admin/handlers/:handlerName/execute
 * @desc 执行自定义处理器
 * @access Private (Admin)
 */
router.post('/admin/handlers/:handlerName/execute',
  authenticateToken as any,
  requireRole('admin'),
  adminRateLimit,
  nonStandardAppController.executeCustomHandler
);

/**
 * 插件管理端点
 */

/**
 * @route GET /nsa/admin/plugins
 * @desc 获取插件列表
 * @access Private (Admin)
 */
router.get('/admin/plugins',
  authenticateToken as any,
  requireRole('admin'),
  async (req, res) => {
    try {
      const loadedPlugins = pluginManager.getLoadedPlugins();
      const availableAdapters = pluginManager.getAvailableProtocolAdapters();
      const availableHandlers = pluginManager.getAvailableCustomHandlers();

      res.json({
        loaded_plugins: loadedPlugins,
        available_adapters: availableAdapters,
        available_handlers: availableHandlers
      });
    } catch (error) {
      res.status(500).json({
        error: 'plugin_list_failed',
        message: '获取插件列表失败'
      });
    }
  }
);

/**
 * @route POST /nsa/admin/plugins/:pluginName/enable
 * @desc 启用插件
 * @access Private (Admin)
 */
router.post('/admin/plugins/:pluginName/enable',
  authenticateToken as any,
  requireRole('admin'),
  adminRateLimit,
  async (req, res) => {
    try {
      const { pluginName } = req.params;
      await pluginManager.enablePlugin(pluginName);
      
      res.json({
        success: true,
        message: `插件 ${pluginName} 启用成功`
      });
    } catch (error) {
      res.status(500).json({
        error: 'plugin_enable_failed',
        message: error.message
      });
    }
  }
);

/**
 * @route POST /nsa/admin/plugins/:pluginName/disable
 * @desc 禁用插件
 * @access Private (Admin)
 */
router.post('/admin/plugins/:pluginName/disable',
  authenticateToken as any,
  requireRole('admin'),
  adminRateLimit,
  async (req, res) => {
    try {
      const { pluginName } = req.params;
      await pluginManager.disablePlugin(pluginName);
      
      res.json({
        success: true,
        message: `插件 ${pluginName} 禁用成功`
      });
    } catch (error) {
      res.status(500).json({
        error: 'plugin_disable_failed',
        message: error.message
      });
    }
  }
);

/**
 * @route GET /nsa/admin/plugins/health
 * @desc 插件健康检查
 * @access Private (Admin)
 */
router.get('/admin/plugins/health',
  authenticateToken as any,
  requireRole('admin'),
  async (req, res) => {
    try {
      const healthStatus = await pluginManager.healthCheck();
      
      res.json({
        overall_health: Object.values(healthStatus).every(status => status),
        plugin_status: healthStatus
      });
    } catch (error) {
      res.status(500).json({
        error: 'health_check_failed',
        message: '插件健康检查失败'
      });
    }
  }
);

/**
 * 协议适配器管理端点
 */

/**
 * @route POST /nsa/admin/protocols/reload
 * @desc 重新加载协议配置
 * @access Private (Admin)
 */
router.post('/admin/protocols/reload',
  authenticateToken as any,
  requireRole('admin'),
  adminRateLimit,
  async (req, res) => {
    try {
      await protocolAdapterService.reloadConfigs();
      
      res.json({
        success: true,
        message: '协议配置重新加载成功'
      });
    } catch (error) {
      res.status(500).json({
        error: 'protocol_reload_failed',
        message: '协议配置重新加载失败'
      });
    }
  }
);

/**
 * 初始化服务
 */
async function initializeServices() {
  try {
    await protocolAdapterService.initialize();
    await pluginManager.initialize();
    console.log('非标准应用服务初始化完成');
  } catch (error) {
    console.error('非标准应用服务初始化失败', error);
  }
}

// 导出路由和初始化函数
export { router as nonStandardAppRoutes, initializeServices };
export default router;
