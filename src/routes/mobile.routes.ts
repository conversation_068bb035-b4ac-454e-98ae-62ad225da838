/**
 * 移动端路由
 * 提供移动设备管理、认证、SDK生成等API路由
 */

import { Router } from 'express';
import { mobileController } from '@/controllers/mobile.controller';
import { authenticateToken } from '@/middleware/auth.middleware';
import { requireRole } from '@/middleware/rbac.middleware';
import { apiRateLimit } from '@/middleware/security.middleware';
import { responseCache } from '@/middleware/performance.middleware';

const router = Router();

// 应用认证中间件
router.use(authenticateToken);

// 应用限流中间件
router.use(apiRateLimit);

/**
 * 设备管理路由
 */

/**
 * 注册移动设备
 * POST /api/v1/mobile/devices/register
 * 
 * 请求体：
 * {
 *   "deviceType": "ios|android|windows_phone|other",
 *   "deviceId": "unique-device-identifier",
 *   "deviceName": "iPhone 15 Pro",
 *   "osVersion": "17.0",
 *   "appVersion": "1.0.0",
 *   "manufacturer": "Apple",
 *   "model": "iPhone15,2",
 *   "screenResolution": "1179x2556",
 *   "isJailbroken": false,
 *   "isRooted": false,
 *   "hasSecureHardware": true,
 *   "supportedBiometrics": ["face_id", "touch_id"],
 *   "pushToken": "fcm-token-or-apns-token",
 *   "publicKey": "device-public-key",
 *   "attestationData": "device-attestation-data",
 *   "metadata": {}
 * }
 * 
 * 响应格式：
 * {
 *   "success": true,
 *   "message": "设备注册成功",
 *   "data": {
 *     "deviceId": "unique-device-identifier",
 *     "trustScore": 85,
 *     "supportedBiometrics": ["face_id", "touch_id"],
 *     "registeredAt": "2024-01-01T12:00:00.000Z"
 *   }
 * }
 */
router.post('/devices/register', 
  requireRole(['user', 'admin']),
  mobileController.registerDevice
);

/**
 * 获取用户设备列表
 * GET /api/v1/mobile/devices
 * 
 * 响应格式：
 * {
 *   "success": true,
 *   "data": {
 *     "devices": [
 *       {
 *         "deviceId": "unique-device-identifier",
 *         "deviceName": "iPhone 15 Pro",
 *         "deviceType": "ios",
 *         "manufacturer": "Apple",
 *         "model": "iPhone15,2",
 *         "osVersion": "17.0",
 *         "appVersion": "1.0.0",
 *         "trustScore": 85,
 *         "supportedBiometrics": ["face_id", "touch_id"],
 *         "isActive": true,
 *         "lastSeen": "2024-01-01T12:00:00.000Z",
 *         "registeredAt": "2024-01-01T10:00:00.000Z"
 *       }
 *     ],
 *     "totalDevices": 1,
 *     "activeDevices": 1
 *   }
 * }
 */
router.get('/devices', 
  requireRole(['user', 'admin']),
  responseCache({ ttl: 300 }), // 5分钟缓存
  mobileController.getUserDevices
);

/**
 * 停用设备
 * POST /api/v1/mobile/devices/:deviceId/deactivate
 * 
 * 响应格式：
 * {
 *   "success": true,
 *   "message": "设备已停用"
 * }
 */
router.post('/devices/:deviceId/deactivate', 
  requireRole(['user', 'admin']),
  mobileController.deactivateDevice
);

/**
 * 生物识别管理路由
 */

/**
 * 注册生物识别
 * POST /api/v1/mobile/biometric/register
 * 
 * 请求体：
 * {
 *   "deviceId": "unique-device-identifier",
 *   "biometricType": "fingerprint|face_id|touch_id|voice|iris|palm",
 *   "templateData": "encrypted-biometric-template-data",
 *   "quality": 95
 * }
 * 
 * 响应格式：
 * {
 *   "success": true,
 *   "message": "生物识别注册成功",
 *   "data": {
 *     "templateId": "template-uuid",
 *     "biometricType": "face_id",
 *     "quality": 95,
 *     "createdAt": "2024-01-01T12:00:00.000Z"
 *   }
 * }
 */
router.post('/biometric/register', 
  requireRole(['user', 'admin']),
  mobileController.registerBiometric
);

/**
 * 移动认证路由
 */

/**
 * 创建移动认证会话
 * POST /api/v1/mobile/auth/session
 * 
 * 请求体：
 * {
 *   "deviceId": "unique-device-identifier",
 *   "authMethod": "biometric|pin|pattern|password|push_notification|qr_code|nfc|bluetooth",
 *   "ttl": 300,
 *   "maxAttempts": 3,
 *   "metadata": {}
 * }
 * 
 * 响应格式：
 * {
 *   "success": true,
 *   "message": "认证会话创建成功",
 *   "data": {
 *     "sessionId": "session-uuid",
 *     "challenge": "base64-encoded-challenge",
 *     "authMethod": "biometric",
 *     "expiresAt": "2024-01-01T12:05:00.000Z",
 *     "maxAttempts": 3
 *   }
 * }
 */
router.post('/auth/session', 
  requireRole(['user', 'admin']),
  mobileController.createAuthSession
);

/**
 * 验证移动认证
 * POST /api/v1/mobile/auth/verify
 * 
 * 请求体：
 * {
 *   "sessionId": "session-uuid",
 *   "response": "authentication-response",
 *   "additionalData": {
 *     "biometricType": "face_id",
 *     "location": {...}
 *   }
 * }
 * 
 * 成功响应格式：
 * {
 *   "success": true,
 *   "message": "认证验证成功",
 *   "data": {
 *     "sessionId": "session-uuid",
 *     "isSuccessful": true,
 *     "completedAt": "2024-01-01T12:01:00.000Z",
 *     "attempts": 1
 *   }
 * }
 * 
 * 失败响应格式：
 * {
 *   "success": false,
 *   "error": "AUTH_VERIFICATION_FAILED",
 *   "message": "认证验证失败",
 *   "data": {
 *     "sessionId": "session-uuid",
 *     "attempts": 2,
 *     "maxAttempts": 3
 *   }
 * }
 */
router.post('/auth/verify', 
  mobileController.verifyMobileAuth
);

/**
 * 推送通知路由
 */

/**
 * 发送推送通知
 * POST /api/v1/mobile/push/send
 * 
 * 请求体：
 * {
 *   "deviceId": "unique-device-identifier",
 *   "title": "身份验证请求",
 *   "body": "请确认您的登录请求",
 *   "data": {
 *     "sessionId": "session-uuid",
 *     "action": "login_request"
 *   },
 *   "priority": "high",
 *   "ttl": 300
 * }
 * 
 * 响应格式：
 * {
 *   "success": true,
 *   "message": "推送通知发送成功",
 *   "data": {
 *     "notificationId": "notification-uuid",
 *     "status": "sent",
 *     "sentAt": "2024-01-01T12:00:00.000Z"
 *   }
 * }
 */
router.post('/push/send', 
  requireRole(['user', 'admin']),
  mobileController.sendPushNotification
);

/**
 * SDK管理路由
 */

/**
 * 生成SDK
 * POST /api/v1/mobile/sdk/generate
 * 
 * 请求体：
 * {
 *   "platform": "ios|android|react_native|flutter|xamarin|cordova",
 *   "applicationId": "com.example.app",
 *   "clientId": "oauth-client-id",
 *   "clientSecret": "oauth-client-secret",
 *   "serverUrl": "https://auth.example.com",
 *   "redirectUri": "com.example.app://auth/callback",
 *   "scopes": ["openid", "profile", "email"],
 *   "features": [
 *     {
 *       "name": "biometric",
 *       "enabled": true,
 *       "config": {}
 *     }
 *   ],
 *   "customization": {
 *     "appName": "My App",
 *     "primaryColor": "#007AFF",
 *     "secondaryColor": "#5856D6",
 *     "theme": "auto",
 *     "language": "en"
 *   },
 *   "security": {
 *     "certificatePinning": true,
 *     "jailbreakDetection": true,
 *     "rootDetection": true,
 *     "obfuscation": true
 *   }
 * }
 * 
 * 响应格式：
 * {
 *   "success": true,
 *   "message": "SDK生成成功",
 *   "data": {
 *     "packageId": "sdk-package-uuid",
 *     "platform": "ios",
 *     "version": "1.0.0",
 *     "fileName": "com.example.app-ios-sdk-1.0.0.zip",
 *     "fileSize": 1048576,
 *     "downloadUrl": "/api/v1/mobile/sdk/download/sdk-package-uuid",
 *     "checksum": "sha256-hash",
 *     "expiresAt": "2024-01-31T12:00:00.000Z"
 *   }
 * }
 */
router.post('/sdk/generate', 
  requireRole(['admin', 'developer']),
  mobileController.generateSDK
);

/**
 * 获取SDK配置模板
 * GET /api/v1/mobile/sdk/template/:platform
 * 
 * 响应格式：
 * {
 *   "success": true,
 *   "data": {
 *     "platform": "ios",
 *     "version": "1.0.0",
 *     "serverUrl": "https://auth.example.com",
 *     "scopes": ["openid", "profile", "email"],
 *     "features": [...],
 *     "customization": {...},
 *     "security": {...}
 *   }
 * }
 */
router.get('/sdk/template/:platform', 
  requireRole(['admin', 'developer']),
  responseCache({ ttl: 3600 }), // 1小时缓存
  mobileController.getSDKTemplate
);

/**
 * 获取SDK包列表
 * GET /api/v1/mobile/sdk/packages
 * 
 * 查询参数：
 * - applicationId: 应用ID（必需）
 * 
 * 响应格式：
 * {
 *   "success": true,
 *   "data": {
 *     "packages": [
 *       {
 *         "id": "sdk-package-uuid",
 *         "platform": "ios",
 *         "version": "1.0.0",
 *         "fileName": "com.example.app-ios-sdk-1.0.0.zip",
 *         "fileSize": 1048576,
 *         "downloadUrl": "/api/v1/mobile/sdk/download/sdk-package-uuid",
 *         "checksum": "sha256-hash",
 *         "createdAt": "2024-01-01T12:00:00.000Z",
 *         "expiresAt": "2024-01-31T12:00:00.000Z",
 *         "downloadCount": 5,
 *         "isActive": true
 *       }
 *     ],
 *     "totalPackages": 1
 *   }
 * }
 */
router.get('/sdk/packages', 
  requireRole(['admin', 'developer']),
  responseCache({ ttl: 600 }), // 10分钟缓存
  mobileController.getSDKPackages
);

/**
 * 下载SDK包
 * GET /api/v1/mobile/sdk/download/:packageId
 * 
 * 响应格式：
 * {
 *   "success": true,
 *   "message": "SDK包下载准备就绪",
 *   "data": {
 *     "downloadUrl": "/api/v1/mobile/sdk/download/sdk-package-uuid",
 *     "fileName": "com.example.app-ios-sdk-1.0.0.zip",
 *     "fileSize": 1048576,
 *     "checksum": "sha256-hash"
 *   }
 * }
 */
router.get('/sdk/download/:packageId', 
  requireRole(['admin', 'developer']),
  mobileController.downloadSDKPackage
);

/**
 * 获取集成指南
 * GET /api/v1/mobile/sdk/guide/:platform
 * 
 * 查询参数：
 * - language: 语言代码（可选，默认为'en'）
 * 
 * 响应格式：
 * {
 *   "success": true,
 *   "data": {
 *     "platform": "ios",
 *     "language": "en",
 *     "guide": "# iOS SDK Integration Guide\n\n..."
 *   }
 * }
 */
router.get('/sdk/guide/:platform', 
  responseCache({ ttl: 3600 }), // 1小时缓存
  mobileController.getIntegrationGuide
);

export default router;
