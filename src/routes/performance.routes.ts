/**
 * 性能优化路由
 * 提供性能监控、优化建议和系统健康检查的API路由
 */

import { Router } from 'express';
import { performanceController } from '@/controllers/performance.controller';
import { authenticateToken } from '@/middleware/auth.middleware';
import { requireRole } from '@/middleware/rbac.middleware';
import { apiRateLimit } from '@/middleware/security.middleware';
import { responseCache } from '@/middleware/performance.middleware';

const router = Router();

// 应用认证中间件
router.use(authenticateToken);

// 应用限流中间件
router.use(apiRateLimit);

/**
 * 获取系统性能概览
 * GET /api/v1/performance/overview
 * 
 * 响应格式：
 * {
 *   "success": true,
 *   "data": {
 *     "timestamp": "2024-01-01T12:00:00.000Z",
 *     "status": "healthy|warning|critical",
 *     "system": {
 *       "status": "healthy",
 *       "cpu": 45.2,
 *       "memory": 67.8,
 *       "disk": 23.4,
 *       "uptime": 86400
 *     },
 *     "database": {
 *       "status": "healthy",
 *       "responseTime": 15.5,
 *       "connectionPool": {
 *         "totalConnections": 10,
 *         "activeConnections": 3,
 *         "idleConnections": 7
 *       },
 *       "queryStats": {
 *         "totalQueries": 1500,
 *         "successRate": 99.2,
 *         "avgDuration": 25.3
 *       }
 *     },
 *     "cache": {
 *       "status": "healthy",
 *       "connected": true,
 *       "version": "6.2.0"
 *     },
 *     "performance": { ... },
 *     "issues": []
 *   }
 * }
 */
router.get('/overview', 
  requireRole(['admin', 'operator']),
  responseCache({ ttl: 30 }), // 30秒缓存
  performanceController.getPerformanceOverview
);

/**
 * 获取系统资源使用情况
 * GET /api/v1/performance/system
 * 
 * 响应格式：
 * {
 *   "success": true,
 *   "data": {
 *     "resources": {
 *       "cpu": {
 *         "usage": 45.2,
 *         "loadAverage": [1.2, 1.5, 1.8],
 *         "cores": 8
 *       },
 *       "memory": {
 *         "total": 16777216000,
 *         "used": 11372339200,
 *         "free": **********,
 *         "usage": 67.8
 *       },
 *       "disk": {
 *         "total": 107374182400,
 *         "used": 25127219200,
 *         "free": 82246963200,
 *         "usage": 23.4
 *       },
 *       "network": {
 *         "bytesReceived": **********,
 *         "bytesSent": 987654321,
 *         "packetsReceived": 123456,
 *         "packetsSent": 98765
 *       },
 *       "process": {
 *         "pid": 12345,
 *         "uptime": 86400,
 *         "memoryUsage": { ... },
 *         "cpuUsage": { ... }
 *       }
 *     },
 *     "monitoring": {
 *       "isMonitoring": true,
 *       "uptime": 86400
 *     },
 *     "timestamp": "2024-01-01T12:00:00.000Z"
 *   }
 * }
 */
router.get('/system', 
  requireRole(['admin', 'operator']),
  responseCache({ ttl: 10 }), // 10秒缓存
  performanceController.getSystemResources
);

/**
 * 获取数据库性能统计
 * GET /api/v1/performance/database
 * 
 * 查询参数：
 * - start: 开始时间（ISO字符串）
 * - end: 结束时间（ISO字符串）
 * 
 * 响应格式：
 * {
 *   "success": true,
 *   "data": {
 *     "queryStats": {
 *       "totalQueries": 1500,
 *       "successfulQueries": 1488,
 *       "failedQueries": 12,
 *       "successRate": 99.2,
 *       "avgDuration": 25.3,
 *       "slowQueries": 5,
 *       "slowQueryRate": 0.33,
 *       "queryBreakdown": [
 *         {
 *           "query": "getUser",
 *           "count": 500,
 *           "avgDuration": 15.2,
 *           "errorRate": 0.2
 *         }
 *       ]
 *     },
 *     "connectionPool": {
 *       "totalConnections": 10,
 *       "activeConnections": 3,
 *       "idleConnections": 7,
 *       "waitingConnections": 0,
 *       "maxConnections": 10
 *     },
 *     "health": {
 *       "status": "healthy",
 *       "responseTime": 15.5,
 *       "timestamp": "2024-01-01T12:00:00.000Z"
 *     },
 *     "timeRange": {
 *       "start": "2024-01-01T11:00:00.000Z",
 *       "end": "2024-01-01T12:00:00.000Z"
 *     },
 *     "timestamp": "2024-01-01T12:00:00.000Z"
 *   }
 * }
 */
router.get('/database', 
  requireRole(['admin', 'operator']),
  responseCache({ ttl: 30 }), // 30秒缓存
  performanceController.getDatabasePerformance
);

/**
 * 获取缓存性能统计
 * GET /api/v1/performance/cache
 * 
 * 响应格式：
 * {
 *   "success": true,
 *   "data": {
 *     "stats": {
 *       "totalKeys": 1250,
 *       "keysByPrefix": {
 *         "cache": 800,
 *         "session": 300,
 *         "jwt_blacklist": 50,
 *         "rate_limit": 100
 *       },
 *       "hits": 8500,
 *       "misses": 1500,
 *       "hitRate": 85.0
 *     },
 *     "health": {
 *       "status": "healthy",
 *       "connected": true,
 *       "version": "6.2.0",
 *       "uptime": 86400,
 *       "memoryUsage": 52428800
 *     },
 *     "timestamp": "2024-01-01T12:00:00.000Z"
 *   }
 * }
 */
router.get('/cache', 
  requireRole(['admin', 'operator']),
  responseCache({ ttl: 30 }), // 30秒缓存
  performanceController.getCachePerformance
);

/**
 * 获取性能指标
 * GET /api/v1/performance/metrics
 * 
 * 查询参数：
 * - format: 返回格式（json|prometheus，默认json）
 * 
 * 响应格式（JSON）：
 * {
 *   "success": true,
 *   "data": {
 *     "metrics": {
 *       "http_requests_total": 15000,
 *       "http_request_duration_avg": 125.5,
 *       "database_query_duration_avg": 25.3,
 *       "cache_hit_rate": 85.0,
 *       "system_cpu_usage": 45.2,
 *       "system_memory_usage": 67.8
 *     },
 *     "timestamp": "2024-01-01T12:00:00.000Z"
 *   }
 * }
 * 
 * 响应格式（Prometheus）：
 * # HELP http_requests_total Total number of HTTP requests
 * # TYPE http_requests_total counter
 * http_requests_total{method="GET",route="/api/v1/auth/profile",status_code="200"} 1500
 * ...
 */
router.get('/metrics', 
  requireRole(['admin', 'operator']),
  performanceController.getPerformanceMetrics
);

/**
 * 获取性能优化建议
 * GET /api/v1/performance/recommendations
 * 
 * 响应格式：
 * {
 *   "success": true,
 *   "data": {
 *     "recommendations": [
 *       {
 *         "type": "system|database|cache|api",
 *         "priority": "high|medium|low",
 *         "title": "CPU使用率过高",
 *         "description": "当前CPU使用率为85.2%，建议优化代码或增加服务器资源",
 *         "actions": [
 *           "检查是否有CPU密集型操作",
 *           "考虑使用缓存减少计算",
 *           "优化算法复杂度",
 *           "考虑水平扩展"
 *         ]
 *       }
 *     ],
 *     "priority": {
 *       "high": 2,
 *       "medium": 3,
 *       "low": 1,
 *       "total": 6
 *     },
 *     "timestamp": "2024-01-01T12:00:00.000Z"
 *   }
 * }
 */
router.get('/recommendations', 
  requireRole(['admin', 'operator']),
  responseCache({ ttl: 60 }), // 1分钟缓存
  performanceController.getOptimizationRecommendations
);

/**
 * 清理过期数据
 * POST /api/v1/performance/cleanup
 * 
 * 响应格式：
 * {
 *   "success": true,
 *   "data": {
 *     "message": "过期数据清理完成",
 *     "result": {
 *       "expiredSessions": 25,
 *       "expiredTokens": 12
 *     },
 *     "timestamp": "2024-01-01T12:00:00.000Z"
 *   }
 * }
 */
router.post('/cleanup', 
  requireRole(['admin']),
  performanceController.cleanupExpiredData
);

/**
 * 启动系统监控
 * POST /api/v1/performance/monitoring/start
 * 
 * 请求体：
 * {
 *   "interval": 30000  // 监控间隔（毫秒），可选，默认30秒
 * }
 * 
 * 响应格式：
 * {
 *   "success": true,
 *   "data": {
 *     "message": "系统监控已启动",
 *     "interval": 30000,
 *     "timestamp": "2024-01-01T12:00:00.000Z"
 *   }
 * }
 */
router.post('/monitoring/start', 
  requireRole(['admin']),
  performanceController.startMonitoring
);

/**
 * 停止系统监控
 * POST /api/v1/performance/monitoring/stop
 * 
 * 响应格式：
 * {
 *   "success": true,
 *   "data": {
 *     "message": "系统监控已停止",
 *     "timestamp": "2024-01-01T12:00:00.000Z"
 *   }
 * }
 */
router.post('/monitoring/stop', 
  requireRole(['admin']),
  performanceController.stopMonitoring
);

export default router;
