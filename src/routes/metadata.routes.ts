/**
 * 元数据管理路由
 * 提供各种协议的元数据生成和管理功能
 */

import { Router } from 'express';
import { metadataGeneratorService, MetadataType } from '@/services/metadata-generator.service';
import { logger } from '@/config/logger';
import { authenticateToken } from '@/middleware/auth.middleware';
import rateLimit from 'express-rate-limit';

const router = Router();

// 元数据速率限制配置
const metadataRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 50, // 每个IP最多50次元数据请求
  message: {
    error: 'metadata_rate_limit_exceeded',
    message: '元数据请求过于频繁，请稍后再试'
  },
  standardHeaders: true,
  legacyHeaders: false
});

/**
 * @route GET /metadata
 * @desc 获取支持的元数据类型列表
 * @access Public
 */
router.get('/', metadataRateLimit, async (req, res) => {
  try {
    const supportedTypes = metadataGeneratorService.getSupportedMetadataTypes();
    
    res.json({
      supported_metadata_types: supportedTypes,
      endpoints: {
        oidc: '/metadata/oidc',
        saml: '/metadata/saml',
        oauth2: '/metadata/oauth2',
        federation: '/metadata/federation'
      },
      description: '支持的身份协议元数据类型'
    });

  } catch (error) {
    logger.error('获取元数据类型列表失败', { error });
    res.status(500).json({
      error: 'server_error',
      error_description: '获取元数据类型失败'
    });
  }
});

/**
 * @route GET /metadata/:type
 * @desc 根据类型生成元数据
 * @access Public
 * @param {string} type - 元数据类型 (oidc, saml, oauth2, federation)
 */
router.get('/:type', metadataRateLimit, async (req, res) => {
  try {
    const { type } = req.params;
    const { format = 'json' } = req.query;

    // 验证元数据类型
    const supportedTypes = metadataGeneratorService.getSupportedMetadataTypes();
    if (!supportedTypes.includes(type as MetadataType)) {
      res.status(400).json({
        error: 'invalid_metadata_type',
        error_description: `不支持的元数据类型: ${type}`,
        supported_types: supportedTypes
      });
      return;
    }

    // 生成元数据
    const metadata = await metadataGeneratorService.generateMetadata(type as MetadataType);

    // 设置响应头
    let contentType = 'application/json';
    if (type === 'saml' || type === 'federation') {
      contentType = 'application/xml';
    }

    res.set({
      'Content-Type': contentType,
      'Cache-Control': 'public, max-age=3600' // 缓存1小时
    });

    // 返回元数据
    if (typeof metadata === 'string') {
      res.send(metadata);
    } else {
      res.json(metadata);
    }

    logger.info('元数据生成成功', { type, format });

  } catch (error) {
    logger.error('元数据生成失败', { 
      error: error instanceof Error ? error.message : String(error),
      type: req.params.type 
    });

    res.status(500).json({
      error: 'server_error',
      error_description: '元数据生成失败'
    });
  }
});

/**
 * @route GET /metadata/oidc/discovery
 * @desc OpenID Connect 发现文档（别名）
 * @access Public
 */
router.get('/oidc/discovery', metadataRateLimit, async (req, res) => {
  try {
    const metadata = await metadataGeneratorService.generateOIDCMetadata();

    res.set({
      'Content-Type': 'application/json',
      'Cache-Control': 'public, max-age=3600'
    });

    res.json(metadata);

  } catch (error) {
    logger.error('OIDC 发现文档生成失败', { error });
    res.status(500).json({
      error: 'server_error',
      error_description: 'OIDC 发现文档生成失败'
    });
  }
});

/**
 * @route GET /metadata/saml/idp
 * @desc SAML IdP 元数据（别名）
 * @access Public
 */
router.get('/saml/idp', metadataRateLimit, async (req, res) => {
  try {
    const metadata = await metadataGeneratorService.generateSAMLMetadata();

    res.set({
      'Content-Type': 'application/samlmetadata+xml',
      'Cache-Control': 'public, max-age=3600'
    });

    res.send(metadata);

  } catch (error) {
    logger.error('SAML IdP 元数据生成失败', { error });
    res.status(500).json({
      error: 'server_error',
      error_description: 'SAML IdP 元数据生成失败'
    });
  }
});

/**
 * @route GET /metadata/oauth2/authorization-server
 * @desc OAuth 2.0 授权服务器元数据（别名）
 * @access Public
 */
router.get('/oauth2/authorization-server', metadataRateLimit, async (req, res) => {
  try {
    const metadata = await metadataGeneratorService.generateOAuth2Metadata();

    res.set({
      'Content-Type': 'application/json',
      'Cache-Control': 'public, max-age=3600'
    });

    res.json(metadata);

  } catch (error) {
    logger.error('OAuth 2.0 授权服务器元数据生成失败', { error });
    res.status(500).json({
      error: 'server_error',
      error_description: 'OAuth 2.0 授权服务器元数据生成失败'
    });
  }
});

/**
 * @route POST /metadata/refresh
 * @desc 刷新元数据缓存
 * @access Private (管理员)
 */
router.post('/refresh', metadataRateLimit, authenticateToken as any, async (req, res) => {
  try {
    // TODO: 添加管理员权限检查
    
    const { type } = req.body;

    if (type) {
      // 刷新特定类型的元数据
      const supportedTypes = metadataGeneratorService.getSupportedMetadataTypes();
      if (!supportedTypes.includes(type as MetadataType)) {
        res.status(400).json({
          error: 'invalid_metadata_type',
          error_description: `不支持的元数据类型: ${type}`,
          supported_types: supportedTypes
        });
        return;
      }

      // 重新生成元数据（这里可以清除缓存）
      await metadataGeneratorService.generateMetadata(type as MetadataType);
      
      res.json({
        message: `${type} 元数据缓存已刷新`,
        type,
        timestamp: new Date().toISOString()
      });

    } else {
      // 刷新所有元数据
      const supportedTypes = metadataGeneratorService.getSupportedMetadataTypes();
      
      for (const metadataType of supportedTypes) {
        await metadataGeneratorService.generateMetadata(metadataType);
      }

      res.json({
        message: '所有元数据缓存已刷新',
        types: supportedTypes,
        timestamp: new Date().toISOString()
      });
    }

    logger.info('元数据缓存刷新成功', { type: type || 'all' });

  } catch (error) {
    logger.error('元数据缓存刷新失败', { error });
    res.status(500).json({
      error: 'server_error',
      error_description: '元数据缓存刷新失败'
    });
  }
});

/**
 * @route GET /metadata/validate/:type
 * @desc 验证元数据格式
 * @access Private (管理员)
 */
router.get('/validate/:type', metadataRateLimit, authenticateToken as any, async (req, res) => {
  try {
    // TODO: 添加管理员权限检查
    
    const { type } = req.params;

    // 验证元数据类型
    const supportedTypes = metadataGeneratorService.getSupportedMetadataTypes();
    if (!supportedTypes.includes(type as MetadataType)) {
      res.status(400).json({
        error: 'invalid_metadata_type',
        error_description: `不支持的元数据类型: ${type}`,
        supported_types: supportedTypes
      });
      return;
    }

    // 生成并验证元数据
    const metadata = await metadataGeneratorService.generateMetadata(type as MetadataType);
    
    // 基本验证
    const validation = {
      type,
      valid: true,
      errors: [] as string[],
      warnings: [] as string[],
      metadata_size: JSON.stringify(metadata).length,
      generated_at: new Date().toISOString()
    };

    // 根据类型进行特定验证
    if (type === 'oidc') {
      if (!metadata.issuer) {
        validation.errors.push('缺少 issuer 字段');
        validation.valid = false;
      }
      if (!metadata.authorization_endpoint) {
        validation.errors.push('缺少 authorization_endpoint 字段');
        validation.valid = false;
      }
      if (!metadata.token_endpoint) {
        validation.errors.push('缺少 token_endpoint 字段');
        validation.valid = false;
      }
      if (!metadata.jwks_uri) {
        validation.errors.push('缺少 jwks_uri 字段');
        validation.valid = false;
      }
    }

    res.json(validation);

    logger.info('元数据验证完成', { type, valid: validation.valid });

  } catch (error) {
    logger.error('元数据验证失败', { error, type: req.params.type });
    res.status(500).json({
      error: 'server_error',
      error_description: '元数据验证失败'
    });
  }
});

export default router;
