/**
 * 缓存管理路由
 * 提供缓存管理和监控的REST API端点
 */

import { Router } from 'express';
import { cacheController } from '@/controllers/cache.controller';
import { authenticateToken } from '@/middleware/auth.middleware';
import { requireRole } from '@/middleware/rbac.middleware';
import { apiRateLimit } from '@/middleware/security.middleware';

const router = Router();

// 应用认证中间件
router.use(authenticateToken);

// 应用限流中间件
router.use(apiRateLimit);

/**
 * 获取缓存统计信息
 * GET /api/v1/cache/stats
 * 
 * 查询参数：
 * - namespace: 缓存命名空间（可选）
 * 
 * 响应格式：
 * {
 *   "success": true,
 *   "data": {
 *     "stats": {
 *       "default": {
 *         "hits": 150,
 *         "misses": 25,
 *         "hitRate": 0.857,
 *         "totalRequests": 175,
 *         "totalSize": 1024000,
 *         "keyCount": 42,
 *         "avgResponseTime": 15.5
 *       }
 *     },
 *     "timestamp": "2024-01-01T12:00:00.000Z"
 *   }
 * }
 */
router.get('/stats', requireRole(['admin', 'operator']), cacheController.getCacheStats);

/**
 * 获取Redis健康状态
 * GET /api/v1/cache/health
 * 
 * 响应格式：
 * {
 *   "success": true,
 *   "data": {
 *     "current": {
 *       "status": "healthy",
 *       "timestamp": "2024-01-01T12:00:00.000Z",
 *       "metrics": {
 *         "connectedClients": 5,
 *         "usedMemory": 1024000,
 *         "responseTime": 15,
 *         "hitRate": 85.7
 *       },
 *       "issues": [],
 *       "recommendations": []
 *     },
 *     "trend": {
 *       "avgResponseTime": 12.3,
 *       "avgMemoryUsage": 45.2,
 *       "avgErrorRate": 0.1,
 *       "avgHitRate": 87.5
 *     },
 *     "isConnected": true
 *   }
 * }
 */
router.get('/health', requireRole(['admin', 'operator']), cacheController.getRedisHealth);

/**
 * 获取会话统计信息
 * GET /api/v1/cache/sessions/stats
 * 
 * 响应格式：
 * {
 *   "success": true,
 *   "data": {
 *     "totalSessions": 150,
 *     "activeSessions": 120,
 *     "expiredSessions": 30,
 *     "userSessionCounts": {
 *       "user-123": 3,
 *       "user-456": 2
 *     },
 *     "deviceTypeCounts": {
 *       "desktop": 80,
 *       "mobile": 40
 *     },
 *     "avgSessionDuration": 3600000
 *   }
 * }
 */
router.get('/sessions/stats', requireRole(['admin', 'operator']), cacheController.getSessionStats);

/**
 * 获取JWT黑名单统计
 * GET /api/v1/cache/blacklist/stats
 * 
 * 响应格式：
 * {
 *   "success": true,
 *   "data": {
 *     "totalEntries": 25,
 *     "entriesByReason": {
 *       "user_logout": 15,
 *       "admin_revoke": 5,
 *       "security_breach": 3,
 *       "password_change": 2
 *     },
 *     "entriesByTokenType": {
 *       "access": 20,
 *       "refresh": 5
 *     },
 *     "recentRevocations": 8,
 *     "topUsers": [
 *       { "userId": "user-123", "count": 5 },
 *       { "userId": "user-456", "count": 3 }
 *     ]
 *   }
 * }
 */
router.get('/blacklist/stats', requireRole(['admin', 'operator']), cacheController.getBlacklistStats);

/**
 * 获取缓存值
 * GET /api/v1/cache/get/:key
 * 
 * 路径参数：
 * - key: 缓存键
 * 
 * 查询参数：
 * - namespace: 缓存命名空间（可选）
 * 
 * 响应格式：
 * {
 *   "success": true,
 *   "data": {
 *     "key": "user:123:profile",
 *     "value": { "id": 123, "name": "John Doe" },
 *     "found": true
 *   }
 * }
 */
router.get('/get/:key', requireRole(['admin', 'operator']), cacheController.getCacheValue);

/**
 * 设置缓存值
 * POST /api/v1/cache/set
 * 
 * 请求体：
 * {
 *   "key": "user:123:profile",
 *   "value": { "id": 123, "name": "John Doe" },
 *   "ttl": 3600,
 *   "namespace": "user"
 * }
 * 
 * 响应格式：
 * {
 *   "success": true,
 *   "data": {
 *     "key": "user:123:profile",
 *     "cached": true,
 *     "message": "缓存设置成功"
 *   }
 * }
 */
router.post('/set', requireRole(['admin']), cacheController.setCacheValue);

/**
 * 删除缓存值
 * DELETE /api/v1/cache/delete/:key
 * 
 * 路径参数：
 * - key: 缓存键
 * 
 * 响应格式：
 * {
 *   "success": true,
 *   "data": {
 *     "key": "user:123:profile",
 *     "deleted": true,
 *     "deletedCount": 1
 *   }
 * }
 */
router.delete('/delete/:key', requireRole(['admin']), cacheController.deleteCacheValue);

/**
 * 批量获取缓存值
 * POST /api/v1/cache/mget
 * 
 * 请求体：
 * {
 *   "keys": ["user:123:profile", "user:456:profile", "user:789:profile"]
 * }
 * 
 * 响应格式：
 * {
 *   "success": true,
 *   "data": {
 *     "results": [
 *       { "key": "user:123:profile", "value": {...}, "found": true },
 *       { "key": "user:456:profile", "value": null, "found": false },
 *       { "key": "user:789:profile", "value": {...}, "found": true }
 *     ],
 *     "totalKeys": 3,
 *     "foundKeys": 2
 *   }
 * }
 */
router.post('/mget', requireRole(['admin', 'operator']), cacheController.batchGetCache);

/**
 * 批量设置缓存值
 * POST /api/v1/cache/mset
 * 
 * 请求体：
 * {
 *   "operations": [
 *     { "key": "user:123:profile", "value": {...}, "ttl": 3600 },
 *     { "key": "user:456:profile", "value": {...}, "ttl": 1800 }
 *   ]
 * }
 * 
 * 响应格式：
 * {
 *   "success": true,
 *   "data": {
 *     "operationCount": 2,
 *     "allSuccessful": true,
 *     "message": "批量缓存设置成功"
 *   }
 * }
 */
router.post('/mset', requireRole(['admin']), cacheController.batchSetCache);

/**
 * 清理过期缓存
 * POST /api/v1/cache/cleanup
 * 
 * 响应格式：
 * {
 *   "success": true,
 *   "data": {
 *     "expiredSessions": 15,
 *     "expiredBlacklist": 8,
 *     "message": "缓存清理完成"
 *   }
 * }
 */
router.post('/cleanup', requireRole(['admin']), cacheController.cleanupCache);

/**
 * 重置缓存统计
 * POST /api/v1/cache/stats/reset
 * 
 * 请求体：
 * {
 *   "namespace": "user"  // 可选，不提供则重置所有
 * }
 * 
 * 响应格式：
 * {
 *   "success": true,
 *   "data": {
 *     "message": "缓存统计已重置",
 *     "namespace": "user"
 *   }
 * }
 */
router.post('/stats/reset', requireRole(['admin']), cacheController.resetCacheStats);

/**
 * 执行Redis命令（仅限管理员）
 * POST /api/v1/cache/command
 * 
 * 请求体：
 * {
 *   "command": "INFO",
 *   "args": ["memory"]
 * }
 * 
 * 响应格式：
 * {
 *   "success": true,
 *   "data": {
 *     "command": "INFO",
 *     "args": ["memory"],
 *     "result": "# Memory\nused_memory:1024000\n..."
 *   }
 * }
 */
router.post('/command', requireRole(['admin']), cacheController.executeRedisCommand);

export default router;
