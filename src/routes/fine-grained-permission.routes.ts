/**
 * 细粒度权限控制路由
 * 提供资源级别权限管理和动态权限调整的API接口
 */

import { Router } from 'express';
import { 
  fineGrainedPermissionService,
  ResourceType,
  ActionType,
  PermissionEffect,
  PermissionContext
} from '@/services/fine-grained-permission.service';
import { logger } from '@/config/logger';
import { authenticateToken } from '@/middleware/auth.middleware';
import rateLimit from 'express-rate-limit';
import { body, param, query, validationResult } from 'express-validator';

const router = Router();

// 细粒度权限API速率限制
const permissionRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 500, // 每个IP最多500次权限请求
  message: {
    error: 'permission_rate_limit_exceeded',
    message: '权限API请求过于频繁，请稍后再试'
  },
  standardHeaders: true,
  legacyHeaders: false
});

/**
 * 验证中间件
 */
const handleValidationErrors = (req: any, res: any, next: any) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      error: 'validation_error',
      message: '请求参数验证失败',
      details: errors.array()
    });
  }
  next();
};

/**
 * 权限管理权限检查中间件
 */
const requirePermissionManagement = (req: any, res: any, next: any) => {
  const user = req.user;
  
  if (!user) {
    return res.status(401).json({
      error: 'unauthorized',
      message: '未认证'
    });
  }

  // 检查权限管理权限
  if (!user.isAdmin && !user.permissions?.includes('permission.manage')) {
    return res.status(403).json({
      error: 'forbidden',
      message: '需要权限管理权限'
    });
  }

  next();
};

/**
 * @route POST /fine-grained-permission/evaluate
 * @desc 评估权限
 * @access Private
 */
router.post('/evaluate',
  permissionRateLimit,
  authenticateToken as any,
  [
    body('resourceType').isIn(Object.values(ResourceType)),
    body('resourceId').isString().notEmpty(),
    body('action').isIn(Object.values(ActionType)),
    body('userId').optional().isUUID(),
    body('additionalContext').optional().isObject()
  ],
  handleValidationErrors,
  async (req, res) => {
    try {
      const { resourceType, resourceId, action, userId, additionalContext } = req.body;
      
      // 使用请求中的用户ID或当前用户ID
      const targetUserId = userId || req.user.id;

      // 构建权限评估上下文
      const context: PermissionContext = {
        userId: targetUserId,
        userRoles: req.user.roles || [],
        userAttributes: req.user.attributes || {},
        resourceType,
        resourceId,
        action,
        environment: {
          timestamp: new Date(),
          ipAddress: req.ip,
          userAgent: req.get('User-Agent'),
          sessionId: req.sessionID
        },
        additionalContext
      };

      const result = await fineGrainedPermissionService.evaluatePermission(context);

      res.json({
        success: true,
        result,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      logger.error('权限评估失败', { error, body: req.body });
      res.status(500).json({
        error: 'server_error',
        message: '权限评估失败'
      });
    }
  }
);

/**
 * @route POST /fine-grained-permission/batch-evaluate
 * @desc 批量评估权限
 * @access Private
 */
router.post('/batch-evaluate',
  permissionRateLimit,
  authenticateToken as any,
  [
    body('requests').isArray({ min: 1, max: 50 }),
    body('requests.*.resourceType').isIn(Object.values(ResourceType)),
    body('requests.*.resourceId').isString().notEmpty(),
    body('requests.*.action').isIn(Object.values(ActionType))
  ],
  handleValidationErrors,
  async (req, res) => {
    try {
      const { requests } = req.body;

      const contexts: PermissionContext[] = requests.map((request: any) => ({
        userId: request.userId || req.user.id,
        userRoles: req.user.roles || [],
        userAttributes: req.user.attributes || {},
        resourceType: request.resourceType,
        resourceId: request.resourceId,
        action: request.action,
        environment: {
          timestamp: new Date(),
          ipAddress: req.ip,
          userAgent: req.get('User-Agent'),
          sessionId: req.sessionID
        },
        additionalContext: request.additionalContext
      }));

      const results = await fineGrainedPermissionService.batchEvaluatePermissions(contexts);

      res.json({
        success: true,
        results,
        count: results.length,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      logger.error('批量权限评估失败', { error, body: req.body });
      res.status(500).json({
        error: 'server_error',
        message: '批量权限评估失败'
      });
    }
  }
);

/**
 * @route POST /fine-grained-permission/policies
 * @desc 创建权限策略
 * @access Private (权限管理)
 */
router.post('/policies',
  permissionRateLimit,
  authenticateToken as any,
  requirePermissionManagement,
  [
    body('name').isString().notEmpty(),
    body('description').isString().notEmpty(),
    body('version').isString().notEmpty(),
    body('isActive').isBoolean(),
    body('rules').isArray().notEmpty(),
    body('priority').isInt({ min: 0, max: 1000 })
  ],
  handleValidationErrors,
  async (req, res) => {
    try {
      const policyData = req.body;

      const policy = await fineGrainedPermissionService.createPermissionPolicy(policyData);

      res.status(201).json({
        success: true,
        policy,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      logger.error('创建权限策略失败', { error, body: req.body });
      res.status(500).json({
        error: 'server_error',
        message: '创建权限策略失败'
      });
    }
  }
);

/**
 * @route PUT /fine-grained-permission/policies/:policyId
 * @desc 更新权限策略
 * @access Private (权限管理)
 */
router.put('/policies/:policyId',
  permissionRateLimit,
  authenticateToken as any,
  requirePermissionManagement,
  [
    param('policyId').isUUID(),
    body('name').optional().isString().notEmpty(),
    body('description').optional().isString().notEmpty(),
    body('version').optional().isString().notEmpty(),
    body('isActive').optional().isBoolean(),
    body('rules').optional().isArray(),
    body('priority').optional().isInt({ min: 0, max: 1000 })
  ],
  handleValidationErrors,
  async (req, res) => {
    try {
      const { policyId } = req.params;
      const updates = req.body;

      const policy = await fineGrainedPermissionService.updatePermissionPolicy(policyId, updates);

      res.json({
        success: true,
        policy,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      logger.error('更新权限策略失败', { error, policyId: req.params.policyId });
      res.status(500).json({
        error: 'server_error',
        message: '更新权限策略失败'
      });
    }
  }
);

/**
 * @route DELETE /fine-grained-permission/policies/:policyId
 * @desc 删除权限策略
 * @access Private (权限管理)
 */
router.delete('/policies/:policyId',
  permissionRateLimit,
  authenticateToken as any,
  requirePermissionManagement,
  [
    param('policyId').isUUID()
  ],
  handleValidationErrors,
  async (req, res) => {
    try {
      const { policyId } = req.params;

      await fineGrainedPermissionService.deletePermissionPolicy(policyId);

      res.json({
        success: true,
        message: '权限策略删除成功',
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      logger.error('删除权限策略失败', { error, policyId: req.params.policyId });
      res.status(500).json({
        error: 'server_error',
        message: '删除权限策略失败'
      });
    }
  }
);

/**
 * @route GET /fine-grained-permission/users/:userId/permissions
 * @desc 获取用户资源权限
 * @access Private (权限管理)
 */
router.get('/users/:userId/permissions',
  permissionRateLimit,
  authenticateToken as any,
  requirePermissionManagement,
  [
    param('userId').isUUID(),
    query('resourceType').optional().isIn(Object.values(ResourceType)),
    query('resourceId').optional().isString()
  ],
  handleValidationErrors,
  async (req, res) => {
    try {
      const { userId } = req.params;
      const { resourceType, resourceId } = req.query;

      const permissions = await fineGrainedPermissionService.getUserResourcePermissions(
        userId,
        resourceType as ResourceType,
        resourceId as string
      );

      res.json({
        success: true,
        permissions,
        count: permissions.length,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      logger.error('获取用户资源权限失败', { error, userId: req.params.userId });
      res.status(500).json({
        error: 'server_error',
        message: '获取用户资源权限失败'
      });
    }
  }
);

/**
 * @route POST /fine-grained-permission/grant
 * @desc 授予资源权限
 * @access Private (权限管理)
 */
router.post('/grant',
  permissionRateLimit,
  authenticateToken as any,
  requirePermissionManagement,
  [
    body('subjectId').isUUID(),
    body('subjectType').isIn(['user', 'role']),
    body('resourceType').isIn(Object.values(ResourceType)),
    body('resourceId').isString().notEmpty(),
    body('action').isIn(Object.values(ActionType)),
    body('effect').optional().isIn(Object.values(PermissionEffect)),
    body('conditions').optional().isArray(),
    body('expiresAt').optional().isISO8601()
  ],
  handleValidationErrors,
  async (req, res) => {
    try {
      const {
        subjectId,
        subjectType,
        resourceType,
        resourceId,
        action,
        effect = PermissionEffect.ALLOW,
        conditions,
        expiresAt
      } = req.body;

      const permission = await fineGrainedPermissionService.grantResourcePermission(
        subjectId,
        subjectType,
        resourceType,
        resourceId,
        action,
        effect,
        conditions,
        expiresAt ? new Date(expiresAt) : undefined
      );

      res.status(201).json({
        success: true,
        permission,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      logger.error('授予资源权限失败', { error, body: req.body });
      res.status(500).json({
        error: 'server_error',
        message: '授予资源权限失败'
      });
    }
  }
);

/**
 * @route DELETE /fine-grained-permission/revoke/:permissionId
 * @desc 撤销资源权限
 * @access Private (权限管理)
 */
router.delete('/revoke/:permissionId',
  permissionRateLimit,
  authenticateToken as any,
  requirePermissionManagement,
  [
    param('permissionId').isUUID()
  ],
  handleValidationErrors,
  async (req, res) => {
    try {
      const { permissionId } = req.params;

      await fineGrainedPermissionService.revokeResourcePermission(permissionId);

      res.json({
        success: true,
        message: '资源权限撤销成功',
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      logger.error('撤销资源权限失败', { error, permissionId: req.params.permissionId });
      res.status(500).json({
        error: 'server_error',
        message: '撤销资源权限失败'
      });
    }
  }
);

export default router;
