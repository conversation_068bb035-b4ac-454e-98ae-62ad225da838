/**
 * 零信任架构路由
 * 提供零信任管理、风险评估、设备管理和策略配置的API路由
 */

import { Router } from 'express';
import { zeroTrustController } from '@/controllers/zero-trust.controller';
import { authenticateToken } from '@/middleware/auth.middleware';
import { requireRole } from '@/middleware/rbac.middleware';
import { apiRateLimit } from '@/middleware/security.middleware';
import { responseCache } from '@/middleware/performance.middleware';
import { zeroTrustAuth, deviceTrustVerification } from '@/middleware/zero-trust.middleware';

const router = Router();

// 应用认证中间件
router.use(authenticateToken);

// 应用零信任中间件（对部分端点）
const zeroTrustProtected = [
  '/devices/:deviceId/blacklist',
  '/policies'
];

router.use((req, res, next) => {
  if (zeroTrustProtected.some(path => req.path.match(path.replace(':deviceId', '[^/]+')))) {
    return zeroTrustAuth({ strictMode: true })(req, res, next);
  }
  next();
});

// 应用限流中间件
router.use(apiRateLimit);

/**
 * 获取零信任概览
 * GET /api/v1/zero-trust/overview
 * 
 * 响应格式：
 * {
 *   "success": true,
 *   "data": {
 *     "timestamp": "2024-01-01T12:00:00.000Z",
 *     "user": {
 *       "id": "user-123",
 *       "currentRisk": {
 *         "score": 25,
 *         "level": "low",
 *         "factors": 5,
 *         "confidence": 0.85
 *       },
 *       "devices": {
 *         "total": 3,
 *         "trusted": 2,
 *         "verified": 2,
 *         "blacklisted": 0
 *       },
 *       "recentAuth": [
 *         {
 *           "timestamp": "2024-01-01T11:30:00.000Z",
 *           "success": true,
 *           "method": "password",
 *           "riskScore": 20
 *         }
 *       ]
 *     },
 *     "system": {
 *       "totalUsers": 1000,
 *       "activeUsers": 750,
 *       "totalDevices": 2500,
 *       "trustedDevices": 1800,
 *       "averageRiskScore": 25.5,
 *       "systemHealth": "healthy"
 *     },
 *     "recommendations": [
 *       "您的安全状态良好，请继续保持"
 *     ]
 *   }
 * }
 */
router.get('/overview', 
  requireRole(['user', 'admin', 'security_officer']),
  responseCache({ ttl: 300 }), // 5分钟缓存
  zeroTrustController.getZeroTrustOverview
);

/**
 * 执行风险评估
 * POST /api/v1/zero-trust/risk-assessment
 * 
 * 请求体：
 * {
 *   "targetUserId": "user-123", // 可选，默认为当前用户
 *   "additionalContext": {
 *     "requestedResource": "/api/v1/admin/users",
 *     "requestedAction": "read"
 *   }
 * }
 * 
 * 响应格式：
 * {
 *   "success": true,
 *   "data": {
 *     "userId": "user-123",
 *     "sessionId": "session-456",
 *     "ipAddress": "*************",
 *     "userAgent": "Mozilla/5.0...",
 *     "overallRiskScore": 35,
 *     "riskLevel": "medium",
 *     "riskFactors": [
 *       {
 *         "type": "location",
 *         "name": "地理位置风险",
 *         "score": 25,
 *         "weight": 0.2,
 *         "confidence": 0.8,
 *         "details": {
 *           "country": "US",
 *           "vpnProxy": false
 *         }
 *       }
 *     ],
 *     "recommendations": [
 *       "增强监控和日志记录"
 *     ],
 *     "requiredActions": [
 *       "ENHANCED_LOGGING"
 *     ],
 *     "confidence": 0.82,
 *     "timestamp": "2024-01-01T12:00:00.000Z",
 *     "expiresAt": "2024-01-01T12:05:00.000Z"
 *   }
 * }
 */
router.post('/risk-assessment', 
  requireRole(['admin', 'security_officer']),
  zeroTrustController.performRiskAssessment
);

/**
 * 获取设备列表
 * GET /api/v1/zero-trust/devices
 * 
 * 查询参数：
 * - userId: 目标用户ID（可选，默认为当前用户）
 * 
 * 响应格式：
 * {
 *   "success": true,
 *   "data": {
 *     "devices": [
 *       {
 *         "id": "dev_1704110400000_a1b2c3d4",
 *         "fingerprint": "abc123...",
 *         "components": {
 *           "userAgent": "Mozilla/5.0...",
 *           "screenResolution": "1920x1080",
 *           "platform": "Windows"
 *         },
 *         "trustScore": 85,
 *         "riskScore": 15,
 *         "confidence": 0.9,
 *         "firstSeen": "2024-01-01T10:00:00.000Z",
 *         "lastSeen": "2024-01-01T12:00:00.000Z",
 *         "seenCount": 25,
 *         "isVerified": true,
 *         "isBlacklisted": false,
 *         "tags": ["verified", "windows", "desktop"],
 *         "currentTrustAssessment": {
 *           "trustScore": 85,
 *           "riskScore": 15,
 *           "confidence": 0.9,
 *           "factors": [...],
 *           "recommendations": [],
 *           "requiresVerification": false
 *         }
 *       }
 *     ],
 *     "summary": {
 *       "total": 3,
 *       "trusted": 2,
 *       "verified": 2,
 *       "blacklisted": 0
 *     }
 *   }
 * }
 */
router.get('/devices', 
  requireRole(['user', 'admin', 'security_officer']),
  responseCache({ ttl: 180 }), // 3分钟缓存
  zeroTrustController.getDevices
);

/**
 * 验证设备
 * POST /api/v1/zero-trust/devices/:deviceId/verify
 * 
 * 请求体：
 * {
 *   "verificationMethod": "manual|email|sms|push"
 * }
 * 
 * 响应格式：
 * {
 *   "success": true,
 *   "message": "设备验证成功"
 * }
 */
router.post('/devices/:deviceId/verify', 
  requireRole(['user', 'admin']),
  deviceTrustVerification(30), // 最低30分信任度才能验证
  zeroTrustController.verifyDevice
);

/**
 * 将设备加入黑名单
 * POST /api/v1/zero-trust/devices/:deviceId/blacklist
 * 
 * 请求体：
 * {
 *   "reason": "恶意活动检测"
 * }
 * 
 * 响应格式：
 * {
 *   "success": true,
 *   "message": "设备已加入黑名单"
 * }
 */
router.post('/devices/:deviceId/blacklist', 
  requireRole(['admin', 'security_officer']),
  zeroTrustController.blacklistDevice
);

/**
 * 获取认证决策
 * POST /api/v1/zero-trust/auth-decision
 * 
 * 请求体：
 * {
 *   "userId": "user-123", // 可选，默认为当前用户
 *   "requestedResource": "/api/v1/admin/users",
 *   "requestedAction": "read",
 *   "deviceFingerprint": "abc123..."
 * }
 * 
 * 响应格式：
 * {
 *   "success": true,
 *   "data": {
 *     "decision": "challenge",
 *     "requirementLevel": "enhanced",
 *     "requiredMethods": ["password", "mfa_totp"],
 *     "optionalMethods": ["mfa_sms", "mfa_email"],
 *     "sessionDuration": 3600,
 *     "restrictions": ["periodic_reauth"],
 *     "reason": "风险评分较高，需要额外验证",
 *     "confidence": 0.85,
 *     "riskScore": 45,
 *     "timestamp": "2024-01-01T12:00:00.000Z",
 *     "expiresAt": "2024-01-01T13:00:00.000Z"
 *   }
 * }
 */
router.post('/auth-decision', 
  requireRole(['admin', 'security_officer']),
  zeroTrustController.getAuthDecision
);

/**
 * 获取零信任统计
 * GET /api/v1/zero-trust/statistics
 * 
 * 查询参数：
 * - timeRange: 时间范围（1h|24h|7d|30d，默认24h）
 * 
 * 响应格式：
 * {
 *   "success": true,
 *   "data": {
 *     "timeRange": "24h",
 *     "period": {
 *       "start": "2023-12-31T12:00:00.000Z",
 *       "end": "2024-01-01T12:00:00.000Z"
 *     },
 *     "riskAssessments": {
 *       "total": 1500,
 *       "byLevel": {
 *         "very_low": 600,
 *         "low": 500,
 *         "medium": 300,
 *         "high": 80,
 *         "very_high": 15,
 *         "critical": 5
 *       },
 *       "averageScore": 28.5
 *     },
 *     "authDecisions": {
 *       "total": 1200,
 *       "allow": 900,
 *       "challenge": 250,
 *       "deny": 50
 *     },
 *     "devices": {
 *       "newDevices": 25,
 *       "verifiedDevices": 18,
 *       "blacklistedDevices": 3,
 *       "trustScoreDistribution": {
 *         "high": 800,
 *         "medium": 600,
 *         "low": 200
 *       }
 *     },
 *     "topRiskFactors": [
 *       {
 *         "type": "location",
 *         "count": 120,
 *         "percentage": 8.0
 *       }
 *     ]
 *   }
 * }
 */
router.get('/statistics', 
  requireRole(['admin', 'security_officer']),
  responseCache({ ttl: 600 }), // 10分钟缓存
  zeroTrustController.getZeroTrustStatistics
);

/**
 * 更新零信任策略
 * PUT /api/v1/zero-trust/policies
 * 
 * 请求体：
 * {
 *   "policies": [
 *     {
 *       "name": "high_risk_policy",
 *       "description": "高风险情况下的严格认证要求",
 *       "conditions": [
 *         {
 *           "type": "risk_score",
 *           "operator": "gte",
 *           "value": 70
 *         }
 *       ],
 *       "requirements": [
 *         {
 *           "level": "maximum",
 *           "methods": ["password", "mfa_totp", "device_verification"],
 *           "sessionDuration": 900,
 *           "restrictions": ["no_sensitive_operations", "enhanced_monitoring"]
 *         }
 *       ],
 *       "enabled": true,
 *       "priority": 100
 *     }
 *   ]
 * }
 * 
 * 响应格式：
 * {
 *   "success": true,
 *   "message": "零信任策略已更新"
 * }
 */
router.put('/policies', 
  requireRole(['admin', 'security_officer']),
  zeroTrustController.updateZeroTrustPolicies
);

export default router;
