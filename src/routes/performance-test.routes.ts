/**
 * 性能测试路由
 * 提供性能测试、基准测试和负载测试的REST API端点
 */

import { Router } from 'express';
import { performanceTestController } from '@/controllers/performance-test.controller';
import { authenticateToken } from '@/middleware/auth.middleware';
import { requireRole } from '@/middleware/rbac.middleware';
import { apiRateLimit } from '@/middleware/security.middleware';

const router = Router();

// 应用认证中间件
router.use(authenticateToken);

// 应用限流中间件
router.use(apiRateLimit);

/**
 * 运行性能测试
 * POST /api/v1/performance/test
 * 
 * 请求体：
 * {
 *   "type": "load|stress|spike|volume|endurance|baseline",
 *   "name": "测试名称",
 *   "description": "测试描述",
 *   "duration": 60000,
 *   "concurrency": 10,
 *   "rampUpTime": 5000,
 *   "rampDownTime": 5000,
 *   "targetRPS": 100,
 *   "maxErrors": 50,
 *   "scenarioType": "mixed|authentication|api|database|cache",
 *   "thresholds": {
 *     "responseTime": {
 *       "avg": 500,
 *       "p95": 1000,
 *       "p99": 2000
 *     },
 *     "errorRate": 5,
 *     "throughput": 50
 *   }
 * }
 * 
 * 响应格式：
 * {
 *   "success": true,
 *   "data": {
 *     "testId": "test_1234567890_abc123",
 *     "config": { ... },
 *     "summary": {
 *       "duration": 60000,
 *       "totalRequests": 1500,
 *       "successfulRequests": 1450,
 *       "failedRequests": 50,
 *       "errorRate": 3.33,
 *       "avgResponseTime": 125.5,
 *       "throughput": 25.0
 *     },
 *     "thresholds": {
 *       "passed": true,
 *       "failures": []
 *     },
 *     "detailedResults": { ... }
 *   }
 * }
 */
router.post('/test', requireRole(['admin', 'operator']), performanceTestController.runPerformanceTest);

/**
 * 获取性能测试结果
 * GET /api/v1/performance/test/:testId
 * 
 * 响应格式：
 * {
 *   "success": true,
 *   "data": {
 *     "testId": "test_1234567890_abc123",
 *     "config": { ... },
 *     "startTime": "2024-01-01T12:00:00.000Z",
 *     "endTime": "2024-01-01T12:01:00.000Z",
 *     "duration": 60000,
 *     "totalRequests": 1500,
 *     "successfulRequests": 1450,
 *     "failedRequests": 50,
 *     "errorRate": 3.33,
 *     "responseTime": {
 *       "min": 50,
 *       "max": 2000,
 *       "avg": 125.5,
 *       "median": 120,
 *       "p95": 250,
 *       "p99": 500
 *     },
 *     "throughput": {
 *       "rps": 25.0,
 *       "rpm": 1500
 *     },
 *     "resourceUsage": {
 *       "cpu": { "avg": 45.2, "max": 78.5 },
 *       "memory": { "avg": 256, "max": 512 },
 *       "connections": { "avg": 15, "max": 25 }
 *     },
 *     "errors": [...],
 *     "thresholdResults": { ... },
 *     "timeline": [...]
 *   }
 * }
 */
router.get('/test/:testId', requireRole(['admin', 'operator']), performanceTestController.getTestResult);

/**
 * 获取所有测试结果
 * GET /api/v1/performance/tests
 * 
 * 查询参数：
 * - limit: 返回数量限制（默认50）
 * - offset: 偏移量（默认0）
 * 
 * 响应格式：
 * {
 *   "success": true,
 *   "data": {
 *     "tests": [
 *       {
 *         "testId": "test_1234567890_abc123",
 *         "name": "负载测试",
 *         "type": "load",
 *         "startTime": "2024-01-01T12:00:00.000Z",
 *         "duration": 60000,
 *         "totalRequests": 1500,
 *         "errorRate": 3.33,
 *         "avgResponseTime": 125.5,
 *         "throughput": 25.0,
 *         "thresholdsPassed": true
 *       }
 *     ],
 *     "total": 25,
 *     "limit": 50,
 *     "offset": 0
 *   }
 * }
 */
router.get('/tests', requireRole(['admin', 'operator']), performanceTestController.getAllTestResults);

/**
 * 获取活跃测试
 * GET /api/v1/performance/tests/active
 * 
 * 响应格式：
 * {
 *   "success": true,
 *   "data": {
 *     "activeTests": [
 *       {
 *         "testId": "test_1234567890_abc123",
 *         "name": "负载测试",
 *         "type": "load",
 *         "startTime": "2024-01-01T12:00:00.000Z",
 *         "duration": 60000,
 *         "concurrency": 10,
 *         "elapsedTime": 30000
 *       }
 *     ],
 *     "count": 1
 *   }
 * }
 */
router.get('/tests/active', requireRole(['admin', 'operator']), performanceTestController.getActiveTests);

/**
 * 中止测试
 * POST /api/v1/performance/test/:testId/abort
 * 
 * 响应格式：
 * {
 *   "success": true,
 *   "data": {
 *     "message": "测试已中止",
 *     "testId": "test_1234567890_abc123"
 *   }
 * }
 */
router.post('/test/:testId/abort', requireRole(['admin']), performanceTestController.abortTest);

/**
 * 运行基准测试
 * POST /api/v1/performance/benchmark
 * 
 * 请求体：
 * {
 *   "categories": ["database", "cache", "api", "auth"],
 *   "saveAsBaseline": true,
 *   "compareWithBaseline": "Database Benchmark"
 * }
 * 
 * 响应格式：
 * {
 *   "success": true,
 *   "data": {
 *     "results": [
 *       {
 *         "name": "Database Benchmark",
 *         "category": "database",
 *         "timestamp": "2024-01-01T12:00:00.000Z",
 *         "environment": {
 *           "nodeVersion": "v18.17.0",
 *           "platform": "linux",
 *           "arch": "x64",
 *           "cpus": 8,
 *           "memory": 16384
 *         },
 *         "results": {
 *           "simpleQuery": {
 *             "opsPerSecond": 1500,
 *             "avgLatency": 15.5,
 *             "minLatency": 5,
 *             "maxLatency": 50,
 *             "p95Latency": 25,
 *             "p99Latency": 35,
 *             "errorRate": 0,
 *             "throughput": 1500,
 *             "memoryUsage": 1024,
 *             "cpuUsage": 25.5
 *           }
 *         },
 *         "comparison": {
 *           "baseline": "Previous Database Benchmark",
 *           "improvements": {
 *             "simpleQuery": {
 *               "opsPerSecondChange": 15.5,
 *               "latencyChange": -10.2,
 *               "throughputChange": 12.8
 *             }
 *           }
 *         }
 *       }
 *     ],
 *     "summary": {
 *       "totalTests": 4,
 *       "categories": ["database", "cache", "api", "auth"],
 *       "timestamp": "2024-01-01T12:00:00.000Z",
 *       "environment": { ... }
 *     }
 *   }
 * }
 */
router.post('/benchmark', requireRole(['admin', 'operator']), performanceTestController.runBenchmark);

/**
 * 获取基准测试历史
 * GET /api/v1/performance/benchmarks
 * 
 * 响应格式：
 * {
 *   "success": true,
 *   "data": {
 *     "baselines": [
 *       {
 *         "name": "Database Benchmark",
 *         "category": "database",
 *         "timestamp": "2024-01-01T12:00:00.000Z",
 *         "environment": { ... },
 *         "summary": {
 *           "testCount": 4,
 *           "avgOpsPerSecond": 1250,
 *           "avgLatency": 18.5,
 *           "totalThroughput": 5000
 *         }
 *       }
 *     ],
 *     "results": [...]
 *   }
 * }
 */
router.get('/benchmarks', requireRole(['admin', 'operator']), performanceTestController.getBenchmarkHistory);

/**
 * 获取可用测试场景
 * GET /api/v1/performance/scenarios
 * 
 * 响应格式：
 * {
 *   "success": true,
 *   "data": {
 *     "scenarioTypes": ["authentication", "oauth", "api", "database", "cache", "mixed"],
 *     "scenarios": {
 *       "authentication": [
 *         {
 *           "name": "user_login",
 *           "weight": 0.4,
 *           "description": "user_login scenario with weight 0.4"
 *         },
 *         {
 *           "name": "user_registration",
 *           "weight": 0.1,
 *           "description": "user_registration scenario with weight 0.1"
 *         }
 *       ],
 *       "api": [...],
 *       "database": [...],
 *       "cache": [...],
 *       "mixed": [...]
 *     },
 *     "totalScenarios": 25
 *   }
 * }
 */
router.get('/scenarios', requireRole(['admin', 'operator']), performanceTestController.getAvailableScenarios);

/**
 * 获取性能测试报告
 * GET /api/v1/performance/report
 * 
 * 响应格式：
 * {
 *   "success": true,
 *   "data": {
 *     "summary": {
 *       "totalTests": 25,
 *       "totalBenchmarks": 8,
 *       "lastTestDate": 1704110400000,
 *       "lastBenchmarkDate": 1704110400000
 *     },
 *     "performanceTests": {
 *       "recent": [...],
 *       "statistics": {
 *         "avgResponseTime": 125.5,
 *         "avgThroughput": 45.2,
 *         "avgErrorRate": 2.1,
 *         "successRate": 95.5
 *       }
 *     },
 *     "benchmarks": {
 *       "recent": [...],
 *       "trends": {
 *         "database": [...],
 *         "cache": [...],
 *         "api": [...],
 *         "auth": [...]
 *       }
 *     }
 *   }
 * }
 */
router.get('/report', requireRole(['admin', 'operator']), performanceTestController.getPerformanceReport);

/**
 * 获取自动化性能测试计划列表
 * GET /api/v1/performance/automated/schedules
 *
 * 响应格式：
 * {
 *   "success": true,
 *   "data": {
 *     "schedules": [
 *       {
 *         "id": "daily-load-test",
 *         "name": "每日负载测试",
 *         "cronExpression": "0 3 * * *",
 *         "enabled": true,
 *         "testType": "load",
 *         "lastRun": "2024-01-01T03:00:00.000Z",
 *         "nextRun": "2024-01-02T03:00:00.000Z",
 *         "thresholds": {
 *           "responseTime": { "avg": 200, "p95": 500, "p99": 1000 },
 *           "throughput": { "min": 90 },
 *           "errorRate": { "max": 1 }
 *         }
 *       }
 *     ],
 *     "totalCount": 3,
 *     "activeCount": 2
 *   }
 * }
 */
router.get('/automated/schedules',
  requireRole(['admin', 'operator']),
  performanceTestController.getAutomatedTestSchedules
);

/**
 * 获取自动化测试历史和趋势分析
 * GET /api/v1/performance/automated/history/:scheduleId
 *
 * 查询参数：
 * - limit: 返回的历史记录数量（默认50）
 *
 * 响应格式：
 * {
 *   "success": true,
 *   "data": {
 *     "scheduleId": "daily-load-test",
 *     "baseline": {
 *       "responseTime": { "avg": 150, "p95": 300 },
 *       "throughput": { "rps": 95 },
 *       "errorRate": 0.5
 *     },
 *     "history": [
 *       {
 *         "timestamp": "2024-01-01T03:00:00.000Z",
 *         "result": {
 *           "testId": "test_123",
 *           "responseTime": { "avg": 160, "p95": 320 },
 *           "throughput": { "rps": 92 },
 *           "errorRate": 0.8
 *         }
 *       }
 *     ],
 *     "trends": {
 *       "responseTime": [...],
 *       "throughput": [...],
 *       "errorRate": [...]
 *     },
 *     "summary": {
 *       "totalTests": 100,
 *       "recentTests": 50,
 *       "hasBaseline": true
 *     }
 *   }
 * }
 */
router.get('/automated/history/:scheduleId',
  requireRole(['admin', 'operator']),
  performanceTestController.getAutomatedTestHistory
);

/**
 * 手动触发自动化性能测试
 * POST /api/v1/performance/automated/trigger/:scheduleId
 *
 * 响应格式：
 * {
 *   "success": true,
 *   "message": "自动化性能测试已触发",
 *   "data": {
 *     "scheduleId": "daily-load-test",
 *     "status": "triggered",
 *     "timestamp": "2024-01-01T10:00:00.000Z"
 *   }
 * }
 */
router.post('/automated/trigger/:scheduleId',
  requireRole(['admin', 'operator']),
  performanceTestController.triggerAutomatedTest
);

/**
 * 获取性能回归报告
 * GET /api/v1/performance/automated/regression/:scheduleId
 *
 * 查询参数：
 * - days: 分析的天数（默认7天）
 *
 * 响应格式：
 * {
 *   "success": true,
 *   "data": {
 *     "scheduleId": "daily-load-test",
 *     "reportPeriod": "7 days",
 *     "baseline": {
 *       "timestamp": "2024-01-01T00:00:00.000Z",
 *       "summary": {...}
 *     },
 *     "regression": {
 *       "totalRegressions": 2,
 *       "totalImprovements": 1,
 *       "regressions": [
 *         {
 *           "timestamp": "2024-01-05T03:00:00.000Z",
 *           "metric": "avg_response_time",
 *           "degradationPercent": 25.5,
 *           "currentValue": 188,
 *           "baselineValue": 150
 *         }
 *       ],
 *       "improvements": [...]
 *     },
 *     "summary": {
 *       "totalTests": 7,
 *       "regressionsDetected": 2,
 *       "improvementsDetected": 1
 *     }
 *   }
 * }
 */
router.get('/automated/regression/:scheduleId',
  requireRole(['admin', 'operator']),
  performanceTestController.getRegressionReport
);

export default router;
