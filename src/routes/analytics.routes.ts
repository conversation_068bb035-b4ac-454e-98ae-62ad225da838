/**
 * 分析和报告路由
 * 提供用户行为分析、安全事件分析、报告生成等API路由
 */

import { Router } from 'express';
import { analyticsController } from '@/controllers/analytics.controller';
import { authenticateToken } from '@/middleware/auth.middleware';
import { requireRole } from '@/middleware/rbac.middleware';
import { apiRateLimit } from '@/middleware/security.middleware';
import { responseCache } from '@/middleware/performance.middleware';

const router = Router();

// 应用认证中间件
router.use(authenticateToken);

// 应用限流中间件
router.use(apiRateLimit);

/**
 * 用户行为分析路由
 */

/**
 * 记录用户行为事件
 * POST /api/v1/analytics/behavior/events
 * 
 * 请求体：
 * {
 *   "eventType": "login|logout|password_change|profile_update|application_access|permission_request|security_event|api_call|resource_access|session_activity",
 *   "ipAddress": "*************",
 *   "userAgent": "Mozilla/5.0...",
 *   "location": {
 *     "country": "China",
 *     "region": "Beijing",
 *     "city": "Beijing",
 *     "latitude": 39.9042,
 *     "longitude": 116.4074
 *   },
 *   "device": {
 *     "type": "desktop",
 *     "os": "Windows 10",
 *     "browser": "Chrome",
 *     "fingerprint": "device-fingerprint-hash"
 *   },
 *   "context": {
 *     "applicationId": "app-123",
 *     "resourceId": "resource-456",
 *     "action": "read",
 *     "result": "success",
 *     "duration": 150,
 *     "metadata": {}
 *   }
 * }
 * 
 * 响应格式：
 * {
 *   "success": true,
 *   "message": "用户行为事件已记录"
 * }
 */
router.post('/behavior/events', 
  requireRole(['user', 'admin']),
  analyticsController.recordBehaviorEvent
);

/**
 * 获取用户行为分析
 * GET /api/v1/analytics/behavior/analysis/:userId
 * 
 * 查询参数：
 * - startDate: 开始日期 (可选)
 * - endDate: 结束日期 (可选)
 * - period: 时间周期 (7d|30d|90d, 默认30d)
 * 
 * 响应格式：
 * {
 *   "success": true,
 *   "data": {
 *     "userId": "user-123",
 *     "analysisDate": "2024-01-01T12:00:00.000Z",
 *     "timeRange": {
 *       "start": "2023-12-01T00:00:00.000Z",
 *       "end": "2024-01-01T00:00:00.000Z"
 *     },
 *     "summary": {
 *       "totalEvents": 1250,
 *       "uniqueSessions": 45,
 *       "uniqueDevices": 3,
 *       "uniqueLocations": 2,
 *       "averageSessionDuration": 1800,
 *       "mostActiveHours": ["09:00", "14:00", "18:00"],
 *       "topApplications": [
 *         {"applicationId": "app-1", "count": 500},
 *         {"applicationId": "app-2", "count": 300}
 *       ]
 *     },
 *     "patterns": [...],
 *     "anomalies": [...],
 *     "riskAssessment": {...},
 *     "predictions": {...}
 *   }
 * }
 */
router.get('/behavior/analysis/:userId', 
  requireRole(['user', 'admin']),
  responseCache({ ttl: 300 }), // 5分钟缓存
  analyticsController.getUserBehaviorAnalysis
);

/**
 * 获取用户行为趋势
 * GET /api/v1/analytics/behavior/trends/:userId
 * 
 * 查询参数：
 * - period: 时间周期 (day|week|month, 默认week)
 * 
 * 响应格式：
 * {
 *   "success": true,
 *   "data": {
 *     "loginTrends": [
 *       {"date": "2024-01-01", "count": 5},
 *       {"date": "2024-01-02", "count": 3}
 *     ],
 *     "applicationUsage": [
 *       {"applicationId": "app-1", "usage": 100, "trend": "up"},
 *       {"applicationId": "app-2", "usage": 80, "trend": "stable"}
 *     ],
 *     "riskTrends": [
 *       {"date": "2024-01-01", "riskScore": 25}
 *     ],
 *     "deviceTrends": [
 *       {"deviceType": "desktop", "count": 80, "percentage": 80},
 *       {"deviceType": "mobile", "count": 20, "percentage": 20}
 *     ],
 *     "locationTrends": [
 *       {"location": "Beijing", "count": 90, "percentage": 90},
 *       {"location": "Shanghai", "count": 10, "percentage": 10}
 *     ]
 *   }
 * }
 */
router.get('/behavior/trends/:userId', 
  requireRole(['user', 'admin']),
  responseCache({ ttl: 600 }), // 10分钟缓存
  analyticsController.getUserBehaviorTrends
);

/**
 * 获取用户行为洞察
 * GET /api/v1/analytics/behavior/insights/:userId
 * 
 * 响应格式：
 * {
 *   "success": true,
 *   "data": {
 *     "insights": [
 *       {
 *         "type": "pattern|anomaly|risk|recommendation",
 *         "title": "发现稳定的时间模式",
 *         "description": "用户主要在9点、14点、18点活跃",
 *         "severity": "info|warning|critical",
 *         "actionRequired": false,
 *         "recommendations": ["保持当前安全措施"]
 *       }
 *     ],
 *     "score": {
 *       "behaviorConsistency": 85,
 *       "securityPosture": 90,
 *       "riskLevel": 75,
 *       "overallScore": 83
 *     }
 *   }
 * }
 */
router.get('/behavior/insights/:userId', 
  requireRole(['user', 'admin']),
  responseCache({ ttl: 900 }), // 15分钟缓存
  analyticsController.getUserBehaviorInsights
);

/**
 * 安全分析路由
 */

/**
 * 获取安全态势
 * GET /api/v1/analytics/security/posture
 * 
 * 响应格式：
 * {
 *   "success": true,
 *   "data": {
 *     "timestamp": "2024-01-01T12:00:00.000Z",
 *     "overallScore": 85,
 *     "threatLevel": "low|medium|high|very_high|critical",
 *     "activeThreats": 3,
 *     "resolvedThreats": 15,
 *     "falsePositives": 2,
 *     "metrics": {
 *       "attacksBlocked": 25,
 *       "suspiciousActivities": 8,
 *       "compromisedAccounts": 0,
 *       "maliciousIps": 12,
 *       "averageResponseTime": 300
 *     },
 *     "trends": {
 *       "threatTrend": "increasing|decreasing|stable",
 *       "severityTrend": "escalating|de-escalating|stable",
 *       "volumeTrend": "increasing|decreasing|stable"
 *     },
 *     "recommendations": [
 *       "加强网络监控",
 *       "更新安全策略"
 *     ]
 *   }
 * }
 */
router.get('/security/posture', 
  requireRole(['admin', 'security_analyst']),
  responseCache({ ttl: 300 }), // 5分钟缓存
  analyticsController.getSecurityPosture
);

/**
 * 获取威胁分析报告
 * GET /api/v1/analytics/security/threats
 * 
 * 查询参数：
 * - startDate: 开始日期 (可选)
 * - endDate: 结束日期 (可选)
 * - period: 时间周期 (1d|7d|30d, 默认7d)
 * 
 * 响应格式：
 * {
 *   "success": true,
 *   "data": {
 *     "summary": {
 *       "totalThreats": 45,
 *       "criticalThreats": 3,
 *       "blockedAttacks": 25,
 *       "compromisedAccounts": 0,
 *       "topThreatTypes": [
 *         {"type": "brute_force", "count": 15},
 *         {"type": "suspicious_login", "count": 12}
 *       ],
 *       "topSourceIps": [
 *         {"ip": "*************", "count": 8, "country": "Unknown"},
 *         {"ip": "*********", "count": 5, "country": "Unknown"}
 *       ]
 *     },
 *     "timeline": [...],
 *     "patterns": [...],
 *     "recommendations": {
 *       "immediate": ["阻止恶意IP地址"],
 *       "shortTerm": ["更新安全策略"],
 *       "longTerm": ["实施零信任架构"]
 *     }
 *   }
 * }
 */
router.get('/security/threats', 
  requireRole(['admin', 'security_analyst']),
  responseCache({ ttl: 600 }), // 10分钟缓存
  analyticsController.getThreatAnalysis
);

/**
 * 更新威胁情报
 * POST /api/v1/analytics/security/threat-intelligence
 * 
 * 请求体：
 * {
 *   "type": "ip|domain|hash|pattern",
 *   "value": "*************",
 *   "threatType": "brute_force|credential_stuffing|account_takeover|privilege_escalation|data_exfiltration|suspicious_login|anomalous_behavior|malicious_ip|bot_attack|ddos_attack|injection_attack|xss_attack",
 *   "severity": "low|medium|high|critical",
 *   "source": "internal|external_feed|manual",
 *   "confidence": 0.9,
 *   "tags": ["malicious", "blacklist"],
 *   "description": "已知恶意IP地址"
 * }
 * 
 * 响应格式：
 * {
 *   "success": true,
 *   "message": "威胁情报已更新"
 * }
 */
router.post('/security/threat-intelligence', 
  requireRole(['admin', 'security_analyst']),
  analyticsController.updateThreatIntelligence
);

/**
 * 报告管理路由
 */

/**
 * 创建报告配置
 * POST /api/v1/analytics/reports/configs
 * 
 * 请求体：
 * {
 *   "name": "每日安全摘要",
 *   "type": "security_summary|user_behavior|system_performance|compliance|threat_intelligence|audit_summary|custom",
 *   "frequency": "daily|weekly|monthly|quarterly|yearly|on_demand",
 *   "format": ["pdf", "html", "json", "csv", "excel"],
 *   "recipients": ["<EMAIL>", "<EMAIL>"],
 *   "parameters": {
 *     "timeRange": {
 *       "period": "last_24h|last_7d|last_30d|last_90d|last_year"
 *     },
 *     "filters": {
 *       "userIds": ["user-1", "user-2"],
 *       "applicationIds": ["app-1", "app-2"],
 *       "eventTypes": ["login", "logout"],
 *       "severityLevels": ["high", "critical"]
 *     },
 *     "sections": ["summary", "details", "recommendations"]
 *   },
 *   "template": {
 *     "header": "<h1>安全报告</h1>",
 *     "footer": "<p>机密文档</p>",
 *     "logo": "company-logo.png"
 *   }
 * }
 * 
 * 响应格式：
 * {
 *   "success": true,
 *   "message": "报告配置已创建",
 *   "data": {
 *     "id": "rpt_config_123",
 *     "name": "每日安全摘要",
 *     "type": "security_summary",
 *     "frequency": "daily",
 *     "isActive": true,
 *     "createdAt": "2024-01-01T12:00:00.000Z",
 *     "nextScheduled": "2024-01-02T12:00:00.000Z"
 *   }
 * }
 */
router.post('/reports/configs', 
  requireRole(['admin', 'report_manager']),
  analyticsController.createReportConfig
);

/**
 * 获取报告配置列表
 * GET /api/v1/analytics/reports/configs
 * 
 * 响应格式：
 * {
 *   "success": true,
 *   "data": {
 *     "configs": [
 *       {
 *         "id": "rpt_config_123",
 *         "name": "每日安全摘要",
 *         "type": "security_summary",
 *         "frequency": "daily",
 *         "format": ["pdf", "html"],
 *         "recipients": ["<EMAIL>"],
 *         "isActive": true,
 *         "createdAt": "2024-01-01T12:00:00.000Z",
 *         "lastGenerated": "2024-01-01T12:00:00.000Z",
 *         "nextScheduled": "2024-01-02T12:00:00.000Z"
 *       }
 *     ],
 *     "totalConfigs": 1
 *   }
 * }
 */
router.get('/reports/configs', 
  requireRole(['admin', 'report_manager']),
  responseCache({ ttl: 600 }), // 10分钟缓存
  analyticsController.getReportConfigs
);

/**
 * 生成报告
 * POST /api/v1/analytics/reports/generate
 * 
 * 请求体：
 * {
 *   "configId": "rpt_config_123",
 *   "parameters": {
 *     "timeRange": {
 *       "start": "2024-01-01T00:00:00.000Z",
 *       "end": "2024-01-01T23:59:59.999Z"
 *     },
 *     "filters": {
 *       "severityLevels": ["high", "critical"]
 *     }
 *   }
 * }
 * 
 * 响应格式：
 * {
 *   "success": true,
 *   "message": "报告生成成功",
 *   "data": {
 *     "reports": [
 *       {
 *         "id": "rpt_123",
 *         "configId": "rpt_config_123",
 *         "type": "security_summary",
 *         "format": "pdf",
 *         "fileName": "security_summary_2024-01-01.pdf",
 *         "fileSize": 1048576,
 *         "generatedAt": "2024-01-01T12:00:00.000Z",
 *         "downloadUrl": "/api/v1/analytics/reports/rpt_123/download"
 *       }
 *     ],
 *     "totalReports": 1
 *   }
 * }
 */
router.post('/reports/generate', 
  requireRole(['admin', 'report_manager']),
  analyticsController.generateReport
);

/**
 * 获取报告列表
 * GET /api/v1/analytics/reports
 * 
 * 查询参数：
 * - type: 报告类型 (可选)
 * - startDate: 开始日期 (可选)
 * - endDate: 结束日期 (可选)
 * - configId: 配置ID (可选)
 * 
 * 响应格式：
 * {
 *   "success": true,
 *   "data": {
 *     "reports": [
 *       {
 *         "id": "rpt_123",
 *         "configId": "rpt_config_123",
 *         "type": "security_summary",
 *         "format": "pdf",
 *         "fileName": "security_summary_2024-01-01.pdf",
 *         "fileSize": 1048576,
 *         "generatedAt": "2024-01-01T12:00:00.000Z",
 *         "timeRange": {
 *           "start": "2024-01-01T00:00:00.000Z",
 *           "end": "2024-01-01T23:59:59.999Z"
 *         },
 *         "recipients": ["<EMAIL>"],
 *         "downloadCount": 3,
 *         "isArchived": false
 *       }
 *     ],
 *     "totalReports": 1
 *   }
 * }
 */
router.get('/reports', 
  requireRole(['admin', 'report_manager']),
  responseCache({ ttl: 300 }), // 5分钟缓存
  analyticsController.getReports
);

/**
 * 下载报告
 * GET /api/v1/analytics/reports/:reportId/download
 * 
 * 响应格式：
 * {
 *   "success": true,
 *   "message": "报告下载准备就绪",
 *   "data": {
 *     "fileName": "security_summary_2024-01-01.pdf",
 *     "mimeType": "application/pdf",
 *     "downloadUrl": "/files/reports/security_summary_2024-01-01.pdf"
 *   }
 * }
 */
router.get('/reports/:reportId/download', 
  requireRole(['admin', 'report_manager']),
  analyticsController.downloadReport
);

export default router;
