/**
 * CDN管理路由
 * 提供CDN配置、缓存管理和统计信息的REST API端点
 */

import { Router } from 'express';
import { cdnController } from '@/controllers/cdn.controller';
import { authenticateToken } from '@/middleware/auth.middleware';
import { requireRole } from '@/middleware/rbac.middleware';
import { apiRateLimit } from '@/middleware/security.middleware';

const router = Router();

// 应用认证中间件
router.use(authenticateToken);

// 应用限流中间件
router.use(apiRateLimit);

/**
 * 获取CDN状态和配置
 * GET /api/v1/cdn/status
 * 
 * 响应格式：
 * {
 *   "success": true,
 *   "data": {
 *     "enabled": true,
 *     "provider": "cloudflare",
 *     "baseUrl": "https://cdn.example.com",
 *     "assetTypes": ["js", "css", "images", "fonts"],
 *     "manifestSize": 150,
 *     "versionCacheSize": 75,
 *     "supportedExtensions": [".js", ".css", ".png", ".jpg", ...],
 *     "healthy": true,
 *     "timestamp": "2024-01-01T12:00:00.000Z"
 *   }
 * }
 */
router.get('/status', requireRole(['admin', 'operator']), cdnController.getStatus);

/**
 * 获取资源的CDN URL
 * GET /api/v1/cdn/asset-url?path=/assets/app.js
 * 
 * 查询参数：
 * - path: 资源路径
 * 
 * 响应格式：
 * {
 *   "success": true,
 *   "data": {
 *     "originalPath": "/assets/app.js",
 *     "cdnUrl": "https://cdn.example.com/assets/app.js",
 *     "shouldUseCDN": true,
 *     "cacheControl": "public, max-age=31536000, immutable",
 *     "timestamp": "2024-01-01T12:00:00.000Z"
 *   }
 * }
 */
router.get('/asset-url', requireRole(['admin', 'operator']), cdnController.getAssetUrl);

/**
 * 清除CDN缓存
 * POST /api/v1/cdn/purge
 * 
 * 请求体：
 * {
 *   "paths": ["/assets/app.js", "/assets/style.css"], // 可选，指定要清除的路径
 *   "purgeAll": false // 可选，是否清除所有缓存
 * }
 * 
 * 响应格式：
 * {
 *   "success": true,
 *   "message": "缓存清除操作已提交",
 *   "data": {
 *     "purgeAll": false,
 *     "pathsCount": 2,
 *     "timestamp": "2024-01-01T12:00:00.000Z"
 *   }
 * }
 */
router.post('/purge', requireRole(['admin']), cdnController.purgeCache);

/**
 * 预热CDN缓存
 * POST /api/v1/cdn/warmup
 * 
 * 请求体：
 * {
 *   "paths": ["/assets/app.js", "/assets/style.css", "/images/logo.png"]
 * }
 * 
 * 响应格式：
 * {
 *   "success": true,
 *   "message": "缓存预热操作已启动",
 *   "data": {
 *     "pathsCount": 3,
 *     "timestamp": "2024-01-01T12:00:00.000Z"
 *   }
 * }
 */
router.post('/warmup', requireRole(['admin', 'operator']), cdnController.warmupCache);

/**
 * 重新加载CDN配置
 * POST /api/v1/cdn/reload
 * 
 * 响应格式：
 * {
 *   "success": true,
 *   "message": "CDN配置已重新加载",
 *   "data": {
 *     "timestamp": "2024-01-01T12:00:00.000Z"
 *   }
 * }
 */
router.post('/reload', requireRole(['admin']), cdnController.reloadConfig);

/**
 * 获取CDN使用统计
 * GET /api/v1/cdn/statistics?period=24h
 * 
 * 查询参数：
 * - period: 统计周期 (1h, 24h, 7d, 30d)
 * 
 * 响应格式：
 * {
 *   "success": true,
 *   "data": {
 *     "basic": {
 *       "enabled": true,
 *       "provider": "cloudflare",
 *       "baseUrl": "https://cdn.example.com",
 *       "assetTypes": ["js", "css", "images", "fonts"],
 *       "manifestSize": 150,
 *       "versionCacheSize": 75
 *     },
 *     "detailed": {
 *       "period": "24h",
 *       "requests": {
 *         "total": 10000,
 *         "cdn": 8500,
 *         "local": 1500,
 *         "errors": 50
 *       },
 *       "bandwidth": {
 *         "total": 2048,
 *         "saved": 1740,
 *         "unit": "MB"
 *       },
 *       "performance": {
 *         "avgResponseTime": 120,
 *         "cacheHitRate": 85.0,
 *         "errorRate": 0.5
 *       },
 *       "topAssets": [
 *         {"path": "/assets/app.js", "requests": 2000, "bandwidth": 500},
 *         {"path": "/assets/style.css", "requests": 1800, "bandwidth": 200}
 *       ],
 *       "errorsByType": {
 *         "404": 30,
 *         "500": 15,
 *         "timeout": 5
 *       }
 *     },
 *     "timestamp": "2024-01-01T12:00:00.000Z"
 *   }
 * }
 */
router.get('/statistics', requireRole(['admin', 'operator']), cdnController.getStatistics);

/**
 * CDN健康检查
 * GET /api/v1/cdn/health
 * 
 * 响应格式：
 * {
 *   "success": true,
 *   "data": {
 *     "healthy": true,
 *     "enabled": true,
 *     "provider": "cloudflare",
 *     "baseUrl": "https://cdn.example.com",
 *     "timestamp": "2024-01-01T12:00:00.000Z"
 *   }
 * }
 */
router.get('/health', requireRole(['admin', 'operator']), cdnController.healthCheck);

/**
 * 获取CDN资源清单
 * GET /api/v1/cdn/manifest
 * 
 * 响应格式：
 * {
 *   "success": true,
 *   "data": {
 *     "enabled": true,
 *     "provider": "cloudflare",
 *     "baseUrl": "https://cdn.example.com",
 *     "assetTypes": ["js", "css", "images", "fonts", "static"],
 *     "supportedExtensions": [".js", ".mjs", ".css", ".png", ".jpg", ...],
 *     "manifestSize": 150,
 *     "versionCacheSize": 75,
 *     "timestamp": "2024-01-01T12:00:00.000Z"
 *   }
 * }
 */
router.get('/manifest', requireRole(['admin', 'operator']), cdnController.getManifest);

export default router;
