/**
 * 协议适配器管理路由
 * 提供适配器管理的REST API端点
 */

import { Router } from 'express';
import { adapterController } from '@/controllers/adapter.controller';
import { apiRateLimit } from '@/middleware/security.middleware';
import { authenticateToken } from '@/middleware/auth.middleware';

const router = Router();

// 应用认证和限流中间件
router.use(authenticateToken);
router.use(apiRateLimit);

/**
 * 获取所有已注册的适配器
 * GET /api/v1/adapters
 * 
 * 响应格式：
 * {
 *   "success": true,
 *   "data": {
 *     "adapters": [
 *       {
 *         "name": "custom-oauth",
 *         "version": "2.0.0",
 *         "description": "自定义OAuth 2.0协议适配器",
 *         "author": "Identity Provider Team",
 *         "supportedMethods": ["authorization_code", "client_credentials"],
 *         "category": "oauth",
 *         "status": "active",
 *         "tags": ["oauth", "custom"]
 *       }
 *     ],
 *     "count": 4
 *   }
 * }
 */
router.get('/', adapterController.getRegisteredAdapters);

/**
 * 获取适配器元数据
 * GET /api/v1/adapters/:name
 * 
 * 路径参数：
 * - name: 适配器名称
 * 
 * 响应格式：
 * {
 *   "success": true,
 *   "data": {
 *     "name": "custom-oauth",
 *     "version": "2.0.0",
 *     "description": "自定义OAuth 2.0协议适配器",
 *     "supportedMethods": ["authorization_code", "client_credentials"],
 *     "configSchema": { ... }
 *   }
 * }
 */
router.get('/:name', adapterController.getAdapterMetadata);

/**
 * 获取所有适配器实例
 * GET /api/v1/adapters/instances
 * 
 * 查询参数：
 * - adapterName: 按适配器名称筛选
 * - status: 按状态筛选 (active/inactive)
 * 
 * 响应格式：
 * {
 *   "success": true,
 *   "data": {
 *     "instances": [
 *       {
 *         "id": "custom-oauth-1234567890-abc123",
 *         "name": "custom-oauth",
 *         "version": "2.0.0",
 *         "isActive": true,
 *         "createdAt": "2024-01-01T00:00:00.000Z",
 *         "lastUsed": "2024-01-01T12:00:00.000Z",
 *         "usageCount": 42,
 *         "category": "oauth",
 *         "status": "active"
 *       }
 *     ],
 *     "count": 1
 *   }
 * }
 */
router.get('/instances', adapterController.getAdapterInstances);

/**
 * 创建适配器实例
 * POST /api/v1/adapters/instances
 * 
 * 请求体：
 * {
 *   "adapterName": "custom-oauth",
 *   "config": {
 *     "endpoints": {
 *       "authorization": "https://example.com/oauth/authorize",
 *       "token": "https://example.com/oauth/token"
 *     },
 *     "oauth": {
 *       "authorizationEndpoint": "https://example.com/oauth/authorize",
 *       "tokenEndpoint": "https://example.com/oauth/token"
 *     },
 *     "client": {
 *       "clientId": "example-client",
 *       "redirectUris": ["https://app.example.com/callback"],
 *       "allowedScopes": ["read", "write"]
 *     }
 *   },
 *   "instanceId": "optional-custom-id"
 * }
 * 
 * 响应格式：
 * {
 *   "success": true,
 *   "data": {
 *     "instanceId": "custom-oauth-1234567890-abc123",
 *     "message": "适配器实例创建成功"
 *   }
 * }
 */
router.post('/instances', adapterController.createAdapterInstance);

/**
 * 获取特定适配器实例
 * GET /api/v1/adapters/instances/:instanceId
 * 
 * 路径参数：
 * - instanceId: 适配器实例ID
 * 
 * 响应格式：
 * {
 *   "success": true,
 *   "data": {
 *     "id": "custom-oauth-1234567890-abc123",
 *     "name": "custom-oauth",
 *     "version": "2.0.0",
 *     "description": "自定义OAuth 2.0协议适配器",
 *     "isActive": true,
 *     "createdAt": "2024-01-01T00:00:00.000Z",
 *     "lastUsed": "2024-01-01T12:00:00.000Z",
 *     "usageCount": 42,
 *     "supportedMethods": ["authorization_code", "client_credentials"],
 *     "category": "oauth",
 *     "status": "active",
 *     "config": { ... }
 *   }
 * }
 */
router.get('/instances/:instanceId', adapterController.getAdapterInstance);

/**
 * 更新适配器实例配置
 * PUT /api/v1/adapters/instances/:instanceId/config
 * 
 * 路径参数：
 * - instanceId: 适配器实例ID
 * 
 * 请求体：
 * {
 *   "config": {
 *     "endpoints": {
 *       "authorization": "https://new-example.com/oauth/authorize",
 *       "token": "https://new-example.com/oauth/token"
 *     },
 *     "oauth": {
 *       "authorizationEndpoint": "https://new-example.com/oauth/authorize",
 *       "tokenEndpoint": "https://new-example.com/oauth/token"
 *     }
 *   }
 * }
 * 
 * 响应格式：
 * {
 *   "success": true,
 *   "data": {
 *     "message": "适配器实例配置更新成功"
 *   }
 * }
 */
router.put('/instances/:instanceId/config', adapterController.updateAdapterInstanceConfig);

/**
 * 停止适配器实例
 * POST /api/v1/adapters/instances/:instanceId/stop
 * 
 * 路径参数：
 * - instanceId: 适配器实例ID
 * 
 * 响应格式：
 * {
 *   "success": true,
 *   "data": {
 *     "message": "适配器实例停止成功"
 *   }
 * }
 */
router.post('/instances/:instanceId/stop', adapterController.stopAdapterInstance);

/**
 * 重启适配器实例
 * POST /api/v1/adapters/instances/:instanceId/restart
 * 
 * 路径参数：
 * - instanceId: 适配器实例ID
 * 
 * 响应格式：
 * {
 *   "success": true,
 *   "data": {
 *     "message": "适配器实例重启成功"
 *   }
 * }
 */
router.post('/instances/:instanceId/restart', adapterController.restartAdapterInstance);

/**
 * 获取适配器使用统计
 * GET /api/v1/adapters/stats
 * 
 * 响应格式：
 * {
 *   "success": true,
 *   "data": {
 *     "stats": {
 *       "custom-oauth": {
 *         "instanceCount": 2,
 *         "totalUsage": 150,
 *         "activeInstances": 2
 *       },
 *       "webhook": {
 *         "instanceCount": 1,
 *         "totalUsage": 25,
 *         "activeInstances": 1
 *       }
 *     },
 *     "timestamp": "2024-01-01T12:00:00.000Z"
 *   }
 * }
 */
router.get('/stats', adapterController.getAdapterStats);

/**
 * 加载外部适配器
 * POST /api/v1/adapters/load
 * 
 * 请求体：
 * {
 *   "adapterPath": "/path/to/external/adapter.js"
 * }
 * 
 * 响应格式：
 * {
 *   "success": true,
 *   "data": {
 *     "adapterName": "external-adapter",
 *     "message": "外部适配器加载成功"
 *   }
 * }
 */
router.post('/load', adapterController.loadExternalAdapter);

/**
 * 清理非活跃实例
 * POST /api/v1/adapters/cleanup
 * 
 * 清理超过24小时未使用的适配器实例
 * 
 * 响应格式：
 * {
 *   "success": true,
 *   "data": {
 *     "message": "非活跃实例清理完成"
 *   }
 * }
 */
router.post('/cleanup', adapterController.cleanupInactiveInstances);

export default router;
