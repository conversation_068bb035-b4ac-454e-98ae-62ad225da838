/**
 * SAML 2.0 路由配置
 * 定义SAML协议相关的API端点
 */

import { Router } from 'express';
import { samlController } from '@/controllers/saml.controller';
import { apiRateLimit } from '@/middleware/security.middleware';
import { prisma } from '@/config/database';

const router = Router();

// 应用中间件
// router.use(requestLoggingMiddleware); // 暂时禁用

/**
 * SAML元数据端点
 * GET /saml/metadata
 * 
 * 返回IdP的SAML元数据，供SP配置使用
 */
router.get('/metadata',
  apiRateLimit, // 使用通用API限流
  samlController.metadata
);

/**
 * SAML SSO端点 - HTTP-Redirect绑定
 * GET /saml/sso
 * 
 * 处理来自SP的SAML认证请求（HTTP-Redirect绑定）
 * 查询参数：
 * - SAMLRequest: Base64编码的SAML认证请求
 * - RelayState: 可选的状态参数
 * - SigAlg: 签名算法（如果请求已签名）
 * - Signature: 请求签名（如果请求已签名）
 */
router.get('/sso',
  apiRateLimit,
  samlController.sso
);

/**
 * SAML SSO端点 - HTTP-POST绑定
 * POST /saml/sso
 *
 * 处理来自SP的SAML认证请求（HTTP-POST绑定）
 * 请求体参数：
 * - SAMLRequest: Base64编码的SAML认证请求
 * - RelayState: 可选的状态参数
 */
router.post('/sso',
  apiRateLimit,
  samlController.sso
);

/**
 * SAML认证完成端点
 * POST /saml/authenticate
 * 
 * 内部端点，用于用户登录后生成SAML响应
 * 请求体参数：
 * - requestId: SAML请求ID
 * - userId: 用户ID
 * - attributes: 可选的自定义属性
 */
router.post('/authenticate',
  apiRateLimit,
  samlController.authenticate
);

/**
 * SAML SLO端点 - HTTP-Redirect绑定
 * GET /saml/slo
 * 
 * 处理来自SP的SAML登出请求（HTTP-Redirect绑定）
 * 查询参数：
 * - SAMLRequest: Base64编码的SAML登出请求
 * - RelayState: 可选的状态参数
 * - SigAlg: 签名算法（如果请求已签名）
 * - Signature: 请求签名（如果请求已签名）
 */
router.get('/slo',
  apiRateLimit,
  samlController.slo
);

/**
 * SAML SLO端点 - HTTP-POST绑定
 * POST /saml/slo
 *
 * 处理来自SP的SAML登出请求（HTTP-POST绑定）
 * 请求体参数：
 * - SAMLRequest: Base64编码的SAML登出请求
 * - RelayState: 可选的状态参数
 */
router.post('/slo',
  apiRateLimit,
  samlController.slo
);

/**
 * SAML SP管理端点（管理员功能）
 */

/**
 * 获取SP列表
 * GET /saml/sp
 */
router.get('/sp',
  apiRateLimit,
  async (req, res) => {
    try {
      // TODO: 添加管理员权限验证
      
      const { page = 1, limit = 20, search } = req.query;
      const offset = (Number(page) - 1) * Number(limit);

      const where: any = {
        supportedProtocols: {
          array_contains: 'saml'
        }
      };

      if (search) {
        where.OR = [
          { name: { contains: search as string, mode: 'insensitive' } },
          { clientId: { contains: search as string, mode: 'insensitive' } }
        ];
      }

      const [applications, total] = await Promise.all([
        prisma.application.findMany({
          where,
          skip: offset,
          take: Number(limit),
          orderBy: { createdAt: 'desc' },
          select: {
            id: true,
            name: true,
            clientId: true,
            description: true,
            isActive: true,
            samlConfig: true,
            createdAt: true,
            updatedAt: true
          }
        }),
        prisma.application.count({ where })
      ]);

      res.json({
        applications,
        pagination: {
          page: Number(page),
          limit: Number(limit),
          total,
          pages: Math.ceil(total / Number(limit))
        }
      });

    } catch (error) {
      res.status(500).json({
        error: 'server_error',
        error_description: '获取SP列表失败'
      });
    }
  }
);

/**
 * 创建SP配置
 * POST /saml/sp
 */
router.post('/sp',
  apiRateLimit,
  async (req, res) => {
    try {
      // TODO: 添加管理员权限验证和输入验证
      
      const {
        name,
        description,
        entityId,
        acsUrl,
        sloUrl,
        certificate,
        wantAssertionsSigned = false,
        wantNameId = true,
        signMetadata = false,
        requiredAttributes = [],
        optionalAttributes = []
      } = req.body;

      const application = await prisma.application.create({
        data: {
          name,
          description,
          clientId: entityId,
          clientSecret: '', // SAML不需要客户端密钥
          supportedProtocols: ['saml'],
          isActive: true,
          samlConfig: {
            entityId,
            acsUrl,
            sloUrl,
            certificate,
            wantAssertionsSigned,
            wantNameId,
            signMetadata,
            requiredAttributes,
            optionalAttributes
          }
        }
      });

      res.status(201).json({
        message: 'SP配置创建成功',
        application: {
          id: application.id,
          name: application.name,
          entityId: application.clientId,
          isActive: application.isActive
        }
      });

    } catch (error) {
      res.status(500).json({
        error: 'server_error',
        error_description: '创建SP配置失败'
      });
    }
  }
);

/**
 * 更新SP配置
 * PUT /saml/sp/:id
 */
router.put('/sp/:id',
  apiRateLimit,
  async (req, res) => {
    try {
      // TODO: 添加管理员权限验证和输入验证
      
      const { id } = req.params;
      const updateData = req.body;

      const application = await prisma.application.update({
        where: { id },
        data: {
          ...updateData,
          updatedAt: new Date()
        }
      });

      res.json({
        message: 'SP配置更新成功',
        application
      });

    } catch (error) {
      res.status(500).json({
        error: 'server_error',
        error_description: '更新SP配置失败'
      });
    }
  }
);

/**
 * 删除SP配置
 * DELETE /saml/sp/:id
 */
router.delete('/sp/:id',
  apiRateLimit,
  async (req, res) => {
    try {
      // TODO: 添加管理员权限验证
      
      const { id } = req.params;

      await prisma.application.delete({
        where: { id }
      });

      res.json({
        message: 'SP配置删除成功'
      });

    } catch (error) {
      res.status(500).json({
        error: 'server_error',
        error_description: '删除SP配置失败'
      });
    }
  }
);

export default router;
