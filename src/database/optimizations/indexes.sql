-- 数据库索引优化脚本
-- 为身份提供商系统的核心表添加必要索引，提升查询性能

-- ============================================================================
-- 用户表 (users) 索引优化
-- ============================================================================

-- 用户邮箱唯一索引（已存在，确保唯一性）
CREATE UNIQUE INDEX IF NOT EXISTS idx_users_email_unique 
ON users(email) WHERE deleted_at IS NULL;

-- 用户名唯一索引（已存在，确保唯一性）
CREATE UNIQUE INDEX IF NOT EXISTS idx_users_username_unique 
ON users(username) WHERE deleted_at IS NULL;

-- 用户状态索引（用于查询活跃用户）
CREATE INDEX IF NOT EXISTS idx_users_status 
ON users(status) WHERE deleted_at IS NULL;

-- 用户创建时间索引（用于按时间排序和统计）
CREATE INDEX IF NOT EXISTS idx_users_created_at 
ON users(created_at);

-- 用户最后登录时间索引（用于活跃度分析）
CREATE INDEX IF NOT EXISTS idx_users_last_login_at 
ON users(last_login_at) WHERE last_login_at IS NOT NULL;

-- 用户邮箱验证状态索引
CREATE INDEX IF NOT EXISTS idx_users_email_verified 
ON users(email_verified) WHERE deleted_at IS NULL;

-- 复合索引：状态+创建时间（用于分页查询活跃用户）
CREATE INDEX IF NOT EXISTS idx_users_status_created_at 
ON users(status, created_at) WHERE deleted_at IS NULL;

-- ============================================================================
-- 用户会话表 (user_sessions) 索引优化
-- ============================================================================

-- 会话ID唯一索引（主要查询字段）
CREATE UNIQUE INDEX IF NOT EXISTS idx_user_sessions_session_id 
ON user_sessions(session_id);

-- 用户ID索引（用于查询用户的所有会话）
CREATE INDEX IF NOT EXISTS idx_user_sessions_user_id 
ON user_sessions(user_id);

-- 设备ID索引（用于设备管理）
CREATE INDEX IF NOT EXISTS idx_user_sessions_device_id 
ON user_sessions(device_id) WHERE device_id IS NOT NULL;

-- 会话状态索引（用于查询活跃会话）
CREATE INDEX IF NOT EXISTS idx_user_sessions_is_active 
ON user_sessions(is_active);

-- 过期时间索引（用于清理过期会话）
CREATE INDEX IF NOT EXISTS idx_user_sessions_expires_at 
ON user_sessions(expires_at);

-- 复合索引：用户ID+活跃状态（用于查询用户活跃会话）
CREATE INDEX IF NOT EXISTS idx_user_sessions_user_active 
ON user_sessions(user_id, is_active);

-- 复合索引：用户ID+设备ID（用于设备会话管理）
CREATE INDEX IF NOT EXISTS idx_user_sessions_user_device 
ON user_sessions(user_id, device_id) WHERE device_id IS NOT NULL;

-- ============================================================================
-- OAuth客户端表 (oauth_clients) 索引优化
-- ============================================================================

-- 客户端ID唯一索引（主要查询字段）
CREATE UNIQUE INDEX IF NOT EXISTS idx_oauth_clients_client_id 
ON oauth_clients(client_id);

-- 客户端状态索引（用于查询活跃客户端）
CREATE INDEX IF NOT EXISTS idx_oauth_clients_status 
ON oauth_clients(status);

-- 客户端类型索引（用于按类型查询）
CREATE INDEX IF NOT EXISTS idx_oauth_clients_client_type 
ON oauth_clients(client_type);

-- 创建时间索引（用于统计和排序）
CREATE INDEX IF NOT EXISTS idx_oauth_clients_created_at 
ON oauth_clients(created_at);

-- ============================================================================
-- OAuth授权码表 (oauth_authorization_codes) 索引优化
-- ============================================================================

-- 授权码唯一索引（主要查询字段）
CREATE UNIQUE INDEX IF NOT EXISTS idx_oauth_auth_codes_code 
ON oauth_authorization_codes(code);

-- 客户端ID索引（用于查询客户端的授权码）
CREATE INDEX IF NOT EXISTS idx_oauth_auth_codes_client_id 
ON oauth_authorization_codes(client_id);

-- 用户ID索引（用于查询用户的授权码）
CREATE INDEX IF NOT EXISTS idx_oauth_auth_codes_user_id 
ON oauth_authorization_codes(user_id);

-- 过期时间索引（用于清理过期授权码）
CREATE INDEX IF NOT EXISTS idx_oauth_auth_codes_expires_at 
ON oauth_authorization_codes(expires_at);

-- 复合索引：客户端ID+用户ID（用于查询特定客户端的用户授权）
CREATE INDEX IF NOT EXISTS idx_oauth_auth_codes_client_user 
ON oauth_authorization_codes(client_id, user_id);

-- ============================================================================
-- OAuth访问令牌表 (oauth_access_tokens) 索引优化
-- ============================================================================

-- 令牌哈希唯一索引（主要查询字段）
CREATE UNIQUE INDEX IF NOT EXISTS idx_oauth_access_tokens_token_hash 
ON oauth_access_tokens(token_hash);

-- JTI唯一索引（用于令牌撤销）
CREATE UNIQUE INDEX IF NOT EXISTS idx_oauth_access_tokens_jti 
ON oauth_access_tokens(jti);

-- 客户端ID索引（用于查询客户端的令牌）
CREATE INDEX IF NOT EXISTS idx_oauth_access_tokens_client_id 
ON oauth_access_tokens(client_id);

-- 用户ID索引（用于查询用户的令牌）
CREATE INDEX IF NOT EXISTS idx_oauth_access_tokens_user_id 
ON oauth_access_tokens(user_id) WHERE user_id IS NOT NULL;

-- 过期时间索引（用于清理过期令牌）
CREATE INDEX IF NOT EXISTS idx_oauth_access_tokens_expires_at 
ON oauth_access_tokens(expires_at);

-- 撤销状态索引（用于查询有效令牌）
CREATE INDEX IF NOT EXISTS idx_oauth_access_tokens_revoked 
ON oauth_access_tokens(revoked);

-- 复合索引：客户端ID+用户ID+撤销状态（用于查询有效的用户令牌）
CREATE INDEX IF NOT EXISTS idx_oauth_access_tokens_client_user_revoked 
ON oauth_access_tokens(client_id, user_id, revoked) WHERE user_id IS NOT NULL;

-- ============================================================================
-- OAuth刷新令牌表 (oauth_refresh_tokens) 索引优化
-- ============================================================================

-- 令牌哈希唯一索引（主要查询字段）
CREATE UNIQUE INDEX IF NOT EXISTS idx_oauth_refresh_tokens_token_hash 
ON oauth_refresh_tokens(token_hash);

-- JTI唯一索引（用于令牌撤销）
CREATE UNIQUE INDEX IF NOT EXISTS idx_oauth_refresh_tokens_jti 
ON oauth_refresh_tokens(jti);

-- 访问令牌JTI索引（用于关联查询）
CREATE INDEX IF NOT EXISTS idx_oauth_refresh_tokens_access_token_jti 
ON oauth_refresh_tokens(access_token_jti);

-- 过期时间索引（用于清理过期令牌）
CREATE INDEX IF NOT EXISTS idx_oauth_refresh_tokens_expires_at 
ON oauth_refresh_tokens(expires_at);

-- 撤销状态索引（用于查询有效令牌）
CREATE INDEX IF NOT EXISTS idx_oauth_refresh_tokens_revoked 
ON oauth_refresh_tokens(revoked);

-- ============================================================================
-- 角色表 (roles) 索引优化
-- ============================================================================

-- 角色名称唯一索引（主要查询字段）
CREATE UNIQUE INDEX IF NOT EXISTS idx_roles_name_unique 
ON roles(name);

-- 角色状态索引（用于查询活跃角色）
CREATE INDEX IF NOT EXISTS idx_roles_is_active 
ON roles(is_active);

-- 角色类型索引（如果有类型字段）
CREATE INDEX IF NOT EXISTS idx_roles_type 
ON roles(type) WHERE type IS NOT NULL;

-- ============================================================================
-- 权限表 (permissions) 索引优化
-- ============================================================================

-- 权限名称唯一索引（主要查询字段）
CREATE UNIQUE INDEX IF NOT EXISTS idx_permissions_name_unique 
ON permissions(name);

-- 权限资源索引（用于按资源查询权限）
CREATE INDEX IF NOT EXISTS idx_permissions_resource 
ON permissions(resource) WHERE resource IS NOT NULL;

-- 权限动作索引（用于按动作查询权限）
CREATE INDEX IF NOT EXISTS idx_permissions_action 
ON permissions(action) WHERE action IS NOT NULL;

-- 复合索引：资源+动作（用于精确权限查询）
CREATE INDEX IF NOT EXISTS idx_permissions_resource_action 
ON permissions(resource, action) WHERE resource IS NOT NULL AND action IS NOT NULL;

-- ============================================================================
-- 用户角色关联表 (user_roles) 索引优化
-- ============================================================================

-- 用户ID索引（用于查询用户的角色）
CREATE INDEX IF NOT EXISTS idx_user_roles_user_id 
ON user_roles(user_id);

-- 角色ID索引（用于查询角色的用户）
CREATE INDEX IF NOT EXISTS idx_user_roles_role_id 
ON user_roles(role_id);

-- 复合唯一索引：用户ID+角色ID（防止重复关联）
CREATE UNIQUE INDEX IF NOT EXISTS idx_user_roles_user_role_unique 
ON user_roles(user_id, role_id);

-- ============================================================================
-- 角色权限关联表 (role_permissions) 索引优化
-- ============================================================================

-- 角色ID索引（用于查询角色的权限）
CREATE INDEX IF NOT EXISTS idx_role_permissions_role_id 
ON role_permissions(role_id);

-- 权限ID索引（用于查询权限的角色）
CREATE INDEX IF NOT EXISTS idx_role_permissions_permission_id 
ON role_permissions(permission_id);

-- 复合唯一索引：角色ID+权限ID（防止重复关联）
CREATE UNIQUE INDEX IF NOT EXISTS idx_role_permissions_role_permission_unique 
ON role_permissions(role_id, permission_id);

-- ============================================================================
-- 多因素认证设备表 (mfa_devices) 索引优化
-- ============================================================================

-- 用户ID索引（用于查询用户的MFA设备）
CREATE INDEX IF NOT EXISTS idx_mfa_devices_user_id 
ON mfa_devices(user_id);

-- 设备类型索引（用于按类型查询设备）
CREATE INDEX IF NOT EXISTS idx_mfa_devices_device_type 
ON mfa_devices(device_type);

-- 设备状态索引（用于查询活跃设备）
CREATE INDEX IF NOT EXISTS idx_mfa_devices_is_active 
ON mfa_devices(is_active);

-- 复合索引：用户ID+设备类型+状态（用于查询用户的特定类型活跃设备）
CREATE INDEX IF NOT EXISTS idx_mfa_devices_user_type_active 
ON mfa_devices(user_id, device_type, is_active);

-- ============================================================================
-- 审计日志表 (audit_logs) 索引优化
-- ============================================================================

-- 用户ID索引（用于查询用户的操作日志）
CREATE INDEX IF NOT EXISTS idx_audit_logs_user_id 
ON audit_logs(user_id) WHERE user_id IS NOT NULL;

-- 操作类型索引（用于按操作类型查询）
CREATE INDEX IF NOT EXISTS idx_audit_logs_action 
ON audit_logs(action);

-- 资源类型索引（用于按资源类型查询）
CREATE INDEX IF NOT EXISTS idx_audit_logs_resource_type 
ON audit_logs(resource_type) WHERE resource_type IS NOT NULL;

-- 创建时间索引（用于按时间查询和清理）
CREATE INDEX IF NOT EXISTS idx_audit_logs_created_at 
ON audit_logs(created_at);

-- IP地址索引（用于安全分析）
CREATE INDEX IF NOT EXISTS idx_audit_logs_ip_address 
ON audit_logs(ip_address) WHERE ip_address IS NOT NULL;

-- 复合索引：用户ID+创建时间（用于查询用户的操作历史）
CREATE INDEX IF NOT EXISTS idx_audit_logs_user_created 
ON audit_logs(user_id, created_at) WHERE user_id IS NOT NULL;

-- 复合索引：操作类型+创建时间（用于统计分析）
CREATE INDEX IF NOT EXISTS idx_audit_logs_action_created 
ON audit_logs(action, created_at);

-- ============================================================================
-- 系统配置表 (system_configs) 索引优化
-- ============================================================================

-- 配置键唯一索引（主要查询字段）
CREATE UNIQUE INDEX IF NOT EXISTS idx_system_configs_key_unique 
ON system_configs(config_key);

-- 配置分组索引（用于按分组查询配置）
CREATE INDEX IF NOT EXISTS idx_system_configs_group 
ON system_configs(config_group) WHERE config_group IS NOT NULL;

-- 配置状态索引（用于查询活跃配置）
CREATE INDEX IF NOT EXISTS idx_system_configs_is_active 
ON system_configs(is_active);

-- 复合索引：分组+状态（用于查询分组的活跃配置）
CREATE INDEX IF NOT EXISTS idx_system_configs_group_active 
ON system_configs(config_group, is_active) WHERE config_group IS NOT NULL;

-- ============================================================================
-- 索引维护和统计信息更新
-- ============================================================================

-- 更新表统计信息（PostgreSQL自动收集，但可以手动触发）
ANALYZE users;
ANALYZE user_sessions;
ANALYZE oauth_clients;
ANALYZE oauth_authorization_codes;
ANALYZE oauth_access_tokens;
ANALYZE oauth_refresh_tokens;
ANALYZE roles;
ANALYZE permissions;
ANALYZE user_roles;
ANALYZE role_permissions;
ANALYZE mfa_devices;
ANALYZE audit_logs;
ANALYZE system_configs;

-- 创建索引使用情况监控视图
CREATE OR REPLACE VIEW index_usage_stats AS
SELECT 
    schemaname,
    tablename,
    indexname,
    idx_tup_read,
    idx_tup_fetch,
    idx_scan
FROM pg_stat_user_indexes
ORDER BY idx_scan DESC;

-- 创建表大小监控视图
CREATE OR REPLACE VIEW table_size_stats AS
SELECT 
    schemaname,
    tablename,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size,
    pg_total_relation_size(schemaname||'.'||tablename) as size_bytes
FROM pg_tables 
WHERE schemaname = 'public'
ORDER BY size_bytes DESC;
