/**
 * OAuth状态管理服务
 * 负责管理OAuth认证过程中的状态信息
 */

import { Redis } from 'ioredis';
import { redisService } from '@/services/redis.service';
import { logger } from '@/config/logger';
import { OAuthState, OAuthSecurity } from '@/utils/oauth-security';

/**
 * OAuth状态管理服务类
 */
export class OAuthStateService {
  private redis: Redis;
  private readonly STATE_PREFIX = 'oauth:state:';
  private readonly STATE_EXPIRY = 600; // 10分钟

  constructor() {
    this.redis = redisService.getClient();
  }

  /**
   * 存储OAuth状态
   */
  async storeState(state: OAuthState): Promise<void> {
    try {
      const key = `${this.STATE_PREFIX}${state.state}`;
      const value = JSON.stringify(state);
      
      await this.redis.setex(key, this.STATE_EXPIRY, value);
      
      logger.debug('OAuth状态已存储', {
        state: state.state,
        provider: state.provider,
        expiry: this.STATE_EXPIRY
      });
    } catch (error) {
      logger.error('存储OAuth状态失败', { error, state: state.state });
      throw new Error('存储OAuth状态失败');
    }
  }

  /**
   * 获取OAuth状态
   */
  async getState(stateParam: string): Promise<OAuthState | null> {
    try {
      const key = `${this.STATE_PREFIX}${stateParam}`;
      const value = await this.redis.get(key);
      
      if (!value) {
        logger.warn('OAuth状态未找到', { state: stateParam });
        return null;
      }
      
      const state = JSON.parse(value) as OAuthState;
      
      logger.debug('OAuth状态已获取', {
        state: state.state,
        provider: state.provider
      });
      
      return state;
    } catch (error) {
      logger.error('获取OAuth状态失败', { error, state: stateParam });
      return null;
    }
  }

  /**
   * 删除OAuth状态（防止重放攻击）
   */
  async deleteState(stateParam: string): Promise<void> {
    try {
      const key = `${this.STATE_PREFIX}${stateParam}`;
      await this.redis.del(key);
      
      logger.debug('OAuth状态已删除', { state: stateParam });
    } catch (error) {
      logger.error('删除OAuth状态失败', { error, state: stateParam });
    }
  }

  /**
   * 验证并消费OAuth状态
   */
  async validateAndConsumeState(
    receivedState: string
  ): Promise<{ isValid: boolean; state?: OAuthState; reason?: string }> {
    try {
      // 获取存储的状态
      const storedState = await this.getState(receivedState);
      
      if (!storedState) {
        OAuthSecurity.logSecurityEvent('state_not_found', {
          receivedState
        }, 'high');
        return { isValid: false, reason: 'state_not_found' };
      }
      
      // 验证状态
      const validation = OAuthSecurity.validateState(receivedState, storedState);
      
      if (!validation.isValid) {
        OAuthSecurity.logSecurityEvent('state_validation_failed', {
          receivedState,
          reason: validation.reason,
          provider: storedState.provider
        }, 'high');
        
        // 删除无效状态
        await this.deleteState(receivedState);
        return { isValid: false, reason: validation.reason };
      }
      
      // 状态验证成功，删除状态防止重放攻击
      await this.deleteState(receivedState);
      
      logger.info('OAuth状态验证成功', {
        state: receivedState,
        provider: storedState.provider
      });
      
      return { isValid: true, state: storedState };
    } catch (error) {
      logger.error('验证OAuth状态失败', { error, receivedState });
      return { isValid: false, reason: 'validation_error' };
    }
  }

  /**
   * 清理过期的OAuth状态
   */
  async cleanupExpiredStates(): Promise<number> {
    try {
      const pattern = `${this.STATE_PREFIX}*`;
      const keys = await this.redis.keys(pattern);
      
      let deletedCount = 0;
      const now = Date.now();
      
      for (const key of keys) {
        try {
          const value = await this.redis.get(key);
          if (value) {
            const state = JSON.parse(value) as OAuthState;
            
            // 检查是否过期
            if (now - state.timestamp > 600000) { // 10分钟
              await this.redis.del(key);
              deletedCount++;
            }
          }
        } catch (parseError) {
          // 如果解析失败，删除该键
          await this.redis.del(key);
          deletedCount++;
        }
      }
      
      if (deletedCount > 0) {
        logger.info('清理过期OAuth状态完成', { deletedCount });
      }
      
      return deletedCount;
    } catch (error) {
      logger.error('清理过期OAuth状态失败', { error });
      return 0;
    }
  }

  /**
   * 获取OAuth状态统计信息
   */
  async getStateStats(): Promise<{
    totalStates: number;
    statesByProvider: Record<string, number>;
  }> {
    try {
      const pattern = `${this.STATE_PREFIX}*`;
      const keys = await this.redis.keys(pattern);
      
      const statesByProvider: Record<string, number> = {};
      
      for (const key of keys) {
        try {
          const value = await this.redis.get(key);
          if (value) {
            const state = JSON.parse(value) as OAuthState;
            statesByProvider[state.provider] = (statesByProvider[state.provider] || 0) + 1;
          }
        } catch (parseError) {
          // 忽略解析错误
        }
      }
      
      return {
        totalStates: keys.length,
        statesByProvider
      };
    } catch (error) {
      logger.error('获取OAuth状态统计失败', { error });
      return {
        totalStates: 0,
        statesByProvider: {}
      };
    }
  }

  /**
   * 存储PKCE代码验证器
   */
  async storeCodeVerifier(state: string, codeVerifier: string): Promise<void> {
    try {
      const key = `oauth:pkce:${state}`;
      await this.redis.setex(key, this.STATE_EXPIRY, codeVerifier);
      
      logger.debug('PKCE代码验证器已存储', { state });
    } catch (error) {
      logger.error('存储PKCE代码验证器失败', { error, state });
      throw new Error('存储PKCE代码验证器失败');
    }
  }

  /**
   * 获取并删除PKCE代码验证器
   */
  async getAndDeleteCodeVerifier(state: string): Promise<string | null> {
    try {
      const key = `oauth:pkce:${state}`;
      const codeVerifier = await this.redis.get(key);
      
      if (codeVerifier) {
        await this.redis.del(key);
        logger.debug('PKCE代码验证器已获取并删除', { state });
      }
      
      return codeVerifier;
    } catch (error) {
      logger.error('获取PKCE代码验证器失败', { error, state });
      return null;
    }
  }

  /**
   * 记录OAuth尝试
   */
  async recordOAuthAttempt(
    provider: string,
    ipAddress: string,
    userAgent: string
  ): Promise<void> {
    try {
      const key = `oauth:attempts:${provider}:${ipAddress}`;
      const attempts = await this.redis.incr(key);
      
      if (attempts === 1) {
        // 设置1小时过期时间
        await this.redis.expire(key, 3600);
      }
      
      // 记录详细信息
      const attemptKey = `oauth:attempt:${Date.now()}:${Math.random()}`;
      const attemptData = {
        provider,
        ipAddress,
        userAgent,
        timestamp: Date.now(),
        attempts
      };
      
      await this.redis.setex(attemptKey, 86400, JSON.stringify(attemptData)); // 24小时
      
      // 如果尝试次数过多，记录安全事件
      if (attempts > 10) {
        OAuthSecurity.logSecurityEvent('excessive_oauth_attempts', {
          provider,
          ipAddress,
          attempts
        }, 'high');
      }
    } catch (error) {
      logger.error('记录OAuth尝试失败', { error, provider, ipAddress });
    }
  }
}
