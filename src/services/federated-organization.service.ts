/**
 * 联邦式组织架构管理服务
 * 解决多应用组织架构数据冲突问题
 */

import { prisma } from '@/config/database';
import { logger } from '@/config/logger';
import { cacheService } from './cache.service';
import { organizationService } from './organization.service';

/**
 * 联邦组织架构接口定义
 */
export interface FederatedOrganization {
  id: string;
  name: string;
  displayName: string;
  path: string;
  type: string;
  sourceApplication?: string;
  isFederated: boolean;
  canonicalPath?: string;
  mappings: OrganizationMapping[];
}

export interface OrganizationMapping {
  id: string;
  sourceOrgId: string;
  sourceApplication: string;
  targetOrgId: string;
  mappingType: 'exact' | 'parent' | 'child' | 'equivalent';
  confidence: number;
  createdBy: string;
  createdAt: Date;
  validFrom: Date;
  validUntil?: Date;
}

export interface ApplicationOrganization {
  id: string;
  name: string;
  displayName: string;
  parentId?: string;
  type: string;
  members: ApplicationOrgMember[];
  metadata?: Record<string, any>;
}

export interface ApplicationOrgMember {
  userId: string;
  role: string;
  permissions: string[];
  joinedAt?: Date;
}

export interface FederatedPermissionResult {
  userId: string;
  applicationId: string;
  permissions: string[];
  organizationMemberships: any[];
  mappingConfidence: number;
  resolvedAt: Date;
}

/**
 * 联邦式组织架构管理服务类
 */
export class FederatedOrganizationService {
  private readonly CACHE_TTL = 300; // 5分钟缓存
  private readonly MIN_MAPPING_CONFIDENCE = 0.7; // 最小映射置信度

  /**
   * 注册应用的组织架构
   */
  async registerApplicationOrganization(
    applicationId: string,
    orgData: ApplicationOrganization[]
  ): Promise<{
    syncedCount: number;
    newMappings: number;
    updatedMappings: number;
    conflicts: any[];
  }> {
    try {
      logger.info('开始注册应用组织架构', { applicationId, orgCount: orgData.length });

      // 1. 验证应用权限
      await this.validateApplicationPermission(applicationId);

      // 2. 解析应用组织架构
      const parsedOrgs = await this.parseApplicationOrganizations(orgData);

      // 3. 智能映射到标准组织架构
      const mappings = await this.generateOrganizationMappings(
        applicationId,
        parsedOrgs
      );

      // 4. 检测冲突
      const conflicts = await this.detectMappingConflicts(mappings);

      // 5. 存储映射关系
      const result = await this.storeMappings(applicationId, mappings);

      // 6. 更新应用注册信息
      await this.updateApplicationRegistry(applicationId);

      // 7. 发布同步事件
      await this.publishSyncEvent(applicationId, 'organization_registered');

      logger.info('应用组织架构注册完成', {
        applicationId,
        result,
        conflicts: conflicts.length
      });

      return {
        syncedCount: parsedOrgs.length,
        newMappings: result.newMappings,
        updatedMappings: result.updatedMappings,
        conflicts
      };
    } catch (error) {
      logger.error('注册应用组织架构失败', {
        applicationId,
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * 智能组织映射算法
   */
  private async generateOrganizationMappings(
    applicationId: string,
    appOrgs: ApplicationOrganization[]
  ): Promise<OrganizationMapping[]> {
    const mappings: OrganizationMapping[] = [];

    for (const appOrg of appOrgs) {
      try {
        // 1. 精确匹配 (名称+路径)
        let targetOrg = await this.findExactMatch(appOrg);
        let mappingType: OrganizationMapping['mappingType'] = 'exact';
        let confidence = 1.0;

        if (!targetOrg) {
          // 2. 模糊匹配 (相似度算法)
          const similarMatch = await this.findSimilarMatch(appOrg);
          if (similarMatch && similarMatch.confidence >= this.MIN_MAPPING_CONFIDENCE) {
            targetOrg = similarMatch.organization;
            mappingType = 'equivalent';
            confidence = similarMatch.confidence;
          }
        }

        if (!targetOrg) {
          // 3. 创建新的标准组织
          targetOrg = await this.createStandardOrganization(appOrg, applicationId);
          mappingType = 'exact';
          confidence = 1.0;
        }

        mappings.push({
          id: `${applicationId}-${appOrg.id}-${targetOrg.id}`,
          sourceOrgId: appOrg.id,
          sourceApplication: applicationId,
          targetOrgId: targetOrg.id,
          mappingType,
          confidence,
          createdBy: 'system',
          createdAt: new Date(),
          validFrom: new Date()
        });

      } catch (error) {
        logger.error('生成组织映射失败', {
          applicationId,
          orgId: appOrg.id,
          error: error instanceof Error ? error.message : String(error)
        });
      }
    }

    return mappings;
  }

  /**
   * 精确匹配组织
   */
  private async findExactMatch(
    appOrg: ApplicationOrganization
  ): Promise<any | null> {
    try {
      // 基于名称和显示名称的精确匹配
      const exactMatch = await prisma.organization.findFirst({
        where: {
          OR: [
            { name: appOrg.name },
            { displayName: appOrg.displayName }
          ],
          type: appOrg.type
        }
      });

      return exactMatch;
    } catch (error) {
      logger.error('精确匹配组织失败', { appOrg, error });
      return null;
    }
  }

  /**
   * 模糊匹配组织
   */
  private async findSimilarMatch(
    appOrg: ApplicationOrganization
  ): Promise<{ organization: any; confidence: number } | null> {
    try {
      // 获取所有相同类型的组织
      const candidates = await prisma.organization.findMany({
        where: { type: appOrg.type }
      });

      let bestMatch: any = null;
      let bestConfidence = 0;

      for (const candidate of candidates) {
        // 计算相似度
        const nameScore = this.calculateStringSimilarity(appOrg.name, candidate.name);
        const displayNameScore = this.calculateStringSimilarity(
          appOrg.displayName,
          candidate.displayName
        );

        const confidence = Math.max(nameScore, displayNameScore);

        if (confidence > bestConfidence && confidence >= this.MIN_MAPPING_CONFIDENCE) {
          bestMatch = candidate;
          bestConfidence = confidence;
        }
      }

      return bestMatch ? { organization: bestMatch, confidence: bestConfidence } : null;
    } catch (error) {
      logger.error('模糊匹配组织失败', { appOrg, error });
      return null;
    }
  }

  /**
   * 字符串相似度计算 (Levenshtein距离)
   */
  private calculateStringSimilarity(str1: string, str2: string): number {
    const len1 = str1.length;
    const len2 = str2.length;

    if (len1 === 0) return len2 === 0 ? 1 : 0;
    if (len2 === 0) return 0;

    const matrix: number[][] = [];

    // 初始化矩阵
    for (let i = 0; i <= len1; i++) {
      matrix[i] = [i];
    }
    for (let j = 0; j <= len2; j++) {
      matrix[0][j] = j;
    }

    // 计算编辑距离
    for (let i = 1; i <= len1; i++) {
      for (let j = 1; j <= len2; j++) {
        const cost = str1[i - 1] === str2[j - 1] ? 0 : 1;
        matrix[i][j] = Math.min(
          matrix[i - 1][j] + 1,      // 删除
          matrix[i][j - 1] + 1,      // 插入
          matrix[i - 1][j - 1] + cost // 替换
        );
      }
    }

    const maxLen = Math.max(len1, len2);
    return (maxLen - matrix[len1][len2]) / maxLen;
  }

  /**
   * 创建标准组织
   */
  private async createStandardOrganization(
    appOrg: ApplicationOrganization,
    sourceApplication: string
  ): Promise<any> {
    try {
      // 生成标准组织路径
      const standardPath = await this.generateStandardPath(appOrg, sourceApplication);

      const organization = await organizationService.createOrganization({
        name: appOrg.name,
        displayName: appOrg.displayName,
        description: `从应用 ${sourceApplication} 同步的组织`,
        type: appOrg.type as any,
        status: 'active',
        metadata: {
          ...appOrg.metadata,
          sourceApplication,
          isFederated: true
        },
        permissionInheritance: true,
        dataIsolationLevel: 'inherit',
        settings: {}
      }, 'system');

      logger.info('创建标准组织成功', {
        orgId: organization.id,
        name: appOrg.name,
        sourceApplication
      });

      return organization;
    } catch (error) {
      logger.error('创建标准组织失败', {
        appOrg,
        sourceApplication,
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * 生成标准组织路径
   */
  private async generateStandardPath(
    appOrg: ApplicationOrganization,
    sourceApplication: string
  ): Promise<string> {
    // 简化的路径生成逻辑
    const basePath = `federated.${sourceApplication}`;
    return `${basePath}.${appOrg.name}`;
  }

  /**
   * 联邦权限解析
   */
  async resolveFederatedPermissions(
    userId: string,
    applicationId: string,
    organizationContext?: string
  ): Promise<FederatedPermissionResult> {
    try {
      const cacheKey = `federated:permissions:${userId}:${applicationId}:${organizationContext || 'default'}`;

      let result = await cacheService.get(cacheKey);
      if (!result) {
        result = await this.computeFederatedPermissions(
          userId,
          applicationId,
          organizationContext
        );
        await cacheService.setex(cacheKey, this.CACHE_TTL, result);
      }

      return result as FederatedPermissionResult;
    } catch (error) {
      logger.error('联邦权限解析失败', {
        userId,
        applicationId,
        organizationContext,
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * 计算联邦权限
   */
  private async computeFederatedPermissions(
    userId: string,
    applicationId: string,
    organizationContext?: string
  ): Promise<FederatedPermissionResult> {
    // 1. 获取用户在应用中的组织关系
    const appOrgMemberships = await this.getUserAppOrganizations(
      userId,
      applicationId
    );

    // 2. 映射到标准组织架构
    const standardOrgMemberships = await this.mapToStandardOrganizations(
      appOrgMemberships,
      applicationId
    );

    // 3. 解析有效权限
    const permissions = new Set<string>();
    let totalConfidence = 0;
    let mappingCount = 0;

    for (const membership of standardOrgMemberships) {
      // 获取组织权限
      const orgPermissions = await organizationService.getUserOrganizations(userId);
      const relevantMembership = orgPermissions.find(
        m => m.organizationId === membership.targetOrgId
      );

      if (relevantMembership) {
        relevantMembership.permissions.forEach(p => permissions.add(p));
        totalConfidence += membership.confidence;
        mappingCount++;
      }
    }

    const averageConfidence = mappingCount > 0 ? totalConfidence / mappingCount : 0;

    return {
      userId,
      applicationId,
      permissions: Array.from(permissions),
      organizationMemberships: standardOrgMemberships,
      mappingConfidence: averageConfidence,
      resolvedAt: new Date()
    };
  }

  /**
   * 获取用户在应用中的组织关系
   */
  private async getUserAppOrganizations(
    userId: string,
    applicationId: string
  ): Promise<any[]> {
    // 这里需要调用应用的API获取用户组织关系
    // 或者从同步的数据中查询
    try {
      const mappings = await prisma.organizationMapping.findMany({
        where: {
          sourceApplication: applicationId
        },
        include: {
          sourceOrganization: true
        }
      });

      // 简化实现：返回映射的组织关系
      return mappings.map(mapping => ({
        userId,
        organizationId: mapping.sourceOrgId,
        applicationId,
        role: 'member', // 默认角色
        permissions: []
      }));
    } catch (error) {
      logger.error('获取用户应用组织关系失败', { userId, applicationId, error });
      return [];
    }
  }

  /**
   * 映射到标准组织架构
   */
  private async mapToStandardOrganizations(
    appOrgMemberships: any[],
    applicationId: string
  ): Promise<any[]> {
    const standardMemberships = [];

    for (const membership of appOrgMemberships) {
      try {
        const mapping = await prisma.organizationMapping.findFirst({
          where: {
            sourceOrgId: membership.organizationId,
            sourceApplication: applicationId
          }
        });

        if (mapping) {
          standardMemberships.push({
            ...membership,
            targetOrgId: mapping.targetOrgId,
            mappingType: mapping.mappingType,
            confidence: mapping.confidence
          });
        }
      } catch (error) {
        logger.error('组织映射失败', {
          membership,
          applicationId,
          error: error instanceof Error ? error.message : String(error)
        });
      }
    }

    return standardMemberships;
  }

  /**
   * 验证应用权限
   */
  private async validateApplicationPermission(applicationId: string): Promise<void> {
    const application = await prisma.application.findUnique({
      where: { id: applicationId }
    });

    if (!application) {
      throw new Error(`应用不存在: ${applicationId}`);
    }

    if (application.status !== 'active') {
      throw new Error(`应用状态无效: ${application.status}`);
    }
  }

  /**
   * 解析应用组织架构
   */
  private async parseApplicationOrganizations(
    orgData: ApplicationOrganization[]
  ): Promise<ApplicationOrganization[]> {
    // 验证和标准化组织数据
    return orgData.map(org => ({
      ...org,
      name: org.name.toLowerCase().replace(/[^a-z0-9_-]/g, '_'),
      type: org.type || 'department'
    }));
  }

  /**
   * 检测映射冲突
   */
  private async detectMappingConflicts(mappings: OrganizationMapping[]): Promise<any[]> {
    const conflicts = [];

    // 检测重复映射
    const targetOrgCounts = new Map<string, number>();
    mappings.forEach(mapping => {
      const count = targetOrgCounts.get(mapping.targetOrgId) || 0;
      targetOrgCounts.set(mapping.targetOrgId, count + 1);
    });

    targetOrgCounts.forEach((count, targetOrgId) => {
      if (count > 1) {
        conflicts.push({
          type: 'duplicate_mapping',
          targetOrgId,
          count,
          mappings: mappings.filter(m => m.targetOrgId === targetOrgId)
        });
      }
    });

    return conflicts;
  }

  /**
   * 存储映射关系
   */
  private async storeMappings(
    applicationId: string,
    mappings: OrganizationMapping[]
  ): Promise<{ newMappings: number; updatedMappings: number }> {
    let newMappings = 0;
    let updatedMappings = 0;

    for (const mapping of mappings) {
      try {
        const existing = await prisma.organizationMapping.findFirst({
          where: {
            sourceOrgId: mapping.sourceOrgId,
            sourceApplication: mapping.sourceApplication
          }
        });

        if (existing) {
          await prisma.organizationMapping.update({
            where: { id: existing.id },
            data: {
              targetOrgId: mapping.targetOrgId,
              mappingType: mapping.mappingType,
              confidence: mapping.confidence,
              validFrom: mapping.validFrom
            }
          });
          updatedMappings++;
        } else {
          await prisma.organizationMapping.create({
            data: mapping
          });
          newMappings++;
        }
      } catch (error) {
        logger.error('存储映射关系失败', { mapping, error });
      }
    }

    return { newMappings, updatedMappings };
  }

  /**
   * 更新应用注册信息
   */
  private async updateApplicationRegistry(applicationId: string): Promise<void> {
    try {
      await prisma.applicationOrgRegistry.upsert({
        where: { applicationId },
        update: {
          lastSyncAt: new Date(),
          syncStatus: 'active'
        },
        create: {
          applicationId,
          orgSchemaVersion: '1.0',
          syncEndpoint: '',
          lastSyncAt: new Date(),
          syncStatus: 'active'
        }
      });
    } catch (error) {
      logger.error('更新应用注册信息失败', { applicationId, error });
    }
  }

  /**
   * 发布同步事件
   */
  private async publishSyncEvent(applicationId: string, eventType: string): Promise<void> {
    // 发布事件到消息队列或事件总线
    logger.info('发布同步事件', { applicationId, eventType });
  }

  /**
   * 获取组织映射关系
   */
  async getOrganizationMappings(
    applicationId: string,
    organizationId: string
  ): Promise<{
    source: any;
    targets: any[];
    history: any[];
    confidence: number;
  }> {
    try {
      const mappings = await prisma.organizationMapping.findMany({
        where: {
          sourceOrgId: organizationId,
          sourceApplication: applicationId
        },
        include: {
          targetOrganization: true
        },
        orderBy: { createdAt: 'desc' }
      });

      const currentMapping = mappings.find(m => m.isActive);
      const confidence = currentMapping?.confidence || 0;

      return {
        source: { id: organizationId, applicationId },
        targets: mappings.map(m => m.targetOrganization),
        history: mappings,
        confidence
      };
    } catch (error) {
      logger.error('获取组织映射关系失败', { applicationId, organizationId, error });
      throw error;
    }
  }

  /**
   * 获取联邦状态
   */
  async getFederationStatus(applicationId: string): Promise<{
    isRegistered: boolean;
    lastSyncAt?: Date;
    syncStatus: string;
    totalOrganizations: number;
    mappedOrganizations: number;
    conflictCount: number;
    mappingAccuracy: number;
    healthScore: number;
  }> {
    try {
      const registry = await prisma.applicationOrgRegistry.findUnique({
        where: { applicationId }
      });

      if (!registry) {
        return {
          isRegistered: false,
          syncStatus: 'not_registered',
          totalOrganizations: 0,
          mappedOrganizations: 0,
          conflictCount: 0,
          mappingAccuracy: 0,
          healthScore: 0
        };
      }

      const mappingAccuracy = registry.totalOrganizations > 0
        ? registry.mappedOrganizations / registry.totalOrganizations
        : 0;

      const healthScore = this.calculateHealthScore(registry, mappingAccuracy);

      return {
        isRegistered: true,
        lastSyncAt: registry.lastSyncAt,
        syncStatus: registry.syncStatus,
        totalOrganizations: registry.totalOrganizations,
        mappedOrganizations: registry.mappedOrganizations,
        conflictCount: registry.conflictCount,
        mappingAccuracy,
        healthScore
      };
    } catch (error) {
      logger.error('获取联邦状态失败', { applicationId, error });
      throw error;
    }
  }

  /**
   * 计算健康度分数
   */
  private calculateHealthScore(
    registry: any,
    mappingAccuracy: number
  ): number {
    let score = 0;

    // 同步状态权重 40%
    if (registry.syncStatus === 'active') score += 40;
    else if (registry.syncStatus === 'pending') score += 20;

    // 映射准确度权重 40%
    score += mappingAccuracy * 40;

    // 冲突率权重 20%
    const conflictRate = registry.totalOrganizations > 0
      ? registry.conflictCount / registry.totalOrganizations
      : 0;
    score += (1 - conflictRate) * 20;

    return Math.round(score);
  }

  /**
   * 获取映射冲突列表
   */
  async getMappingConflicts(
    applicationId: string,
    options: {
      status?: string;
      page?: number;
      limit?: number;
    } = {}
  ): Promise<{ items: any[]; total: number }> {
    try {
      const { status, page = 1, limit = 20 } = options;

      const whereClause: any = { applicationId };
      if (status) whereClause.status = status;

      const [conflicts, total] = await Promise.all([
        prisma.mappingConflict.findMany({
          where: whereClause,
          skip: (page - 1) * limit,
          take: limit,
          orderBy: { detectedAt: 'desc' }
        }),
        prisma.mappingConflict.count({ where: whereClause })
      ]);

      return { items: conflicts, total };
    } catch (error) {
      logger.error('获取映射冲突列表失败', { applicationId, options, error });
      throw error;
    }
  }

  /**
   * 解决冲突
   */
  async resolveConflict(
    conflictId: string,
    resolutionStrategy: string,
    resolvedBy: string,
    resolutionNotes?: string
  ): Promise<{
    resolvedAt: Date;
    newMappings: number;
  }> {
    try {
      const conflict = await prisma.mappingConflict.findUnique({
        where: { id: conflictId }
      });

      if (!conflict) {
        throw new Error(`冲突不存在: ${conflictId}`);
      }

      // 更新冲突状态
      await prisma.mappingConflict.update({
        where: { id: conflictId },
        data: {
          status: 'resolved',
          resolutionStrategy,
          resolvedBy,
          resolvedAt: new Date(),
          resolutionNotes
        }
      });

      // 根据解决策略执行相应操作
      let newMappings = 0;
      switch (resolutionStrategy) {
        case 'auto_merge':
          newMappings = await this.autoMergeConflict(conflict);
          break;
        case 'create_separate':
          newMappings = await this.createSeparateMapping(conflict);
          break;
        case 'use_latest':
          newMappings = await this.useLatestMapping(conflict);
          break;
      }

      logger.info('冲突解决成功', {
        conflictId,
        resolutionStrategy,
        resolvedBy,
        newMappings
      });

      return {
        resolvedAt: new Date(),
        newMappings
      };
    } catch (error) {
      logger.error('解决冲突失败', { conflictId, resolutionStrategy, error });
      throw error;
    }
  }

  /**
   * 自动合并冲突
   */
  private async autoMergeConflict(conflict: any): Promise<number> {
    // 实现自动合并逻辑
    logger.info('执行自动合并', { conflictId: conflict.id });
    return 1;
  }

  /**
   * 创建独立映射
   */
  private async createSeparateMapping(conflict: any): Promise<number> {
    // 实现创建独立映射逻辑
    logger.info('创建独立映射', { conflictId: conflict.id });
    return 1;
  }

  /**
   * 使用最新映射
   */
  private async useLatestMapping(conflict: any): Promise<number> {
    // 实现使用最新映射逻辑
    logger.info('使用最新映射', { conflictId: conflict.id });
    return 1;
  }
}

export const federatedOrganizationService = new FederatedOrganizationService();
