/**
 * Prometheus监控服务
 * 提供应用程序指标收集和导出功能
 */

import { register, collectDefaultMetrics, Counter, Histogram, Gauge } from 'prom-client';
import { Request, Response } from 'express';
import { logger } from '@/config/logger';
import { redisService } from './redis.service';
import { prisma } from '@/config/database';

class PrometheusService {
  private initialized = false;

  // HTTP请求指标
  private httpRequestsTotal: Counter<string>;
  private httpRequestDuration: Histogram<string>;
  private httpRequestsInFlight: Gauge<string>;

  // 数据库指标
  private databaseConnectionsActive: Gauge<string>;
  private databaseQueryDuration: Histogram<string>;
  private databaseQueriesTotal: Counter<string>;

  // Redis指标
  private redisConnectionsActive: Gauge<string>;
  private redisCommandDuration: Histogram<string>;
  private redisCommandsTotal: Counter<string>;

  // 认证指标
  private authAttemptsTotal: Counter<string>;
  private authSuccessTotal: Counter<string>;
  private activeSessionsTotal: Gauge<string>;

  // 系统指标
  private memoryUsage: Gauge<string>;
  private cpuUsage: Gauge<string>;

  constructor() {
    this.initializeMetrics();
  }

  /**
   * 初始化Prometheus指标
   */
  private initializeMetrics(): void {
    if (this.initialized) return;

    try {
      // 清除现有注册
      register.clear();

      // 收集默认系统指标
      collectDefaultMetrics({
        register,
        prefix: 'idp_',
        gcDurationBuckets: [0.001, 0.01, 0.1, 1, 2, 5]
      });

      // HTTP请求指标
      this.httpRequestsTotal = new Counter({
        name: 'idp_http_requests_total',
        help: 'Total number of HTTP requests',
        labelNames: ['method', 'route', 'status_code'],
        registers: [register]
      });

      this.httpRequestDuration = new Histogram({
        name: 'idp_http_request_duration_seconds',
        help: 'Duration of HTTP requests in seconds',
        labelNames: ['method', 'route', 'status_code'],
        buckets: [0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1, 2, 5],
        registers: [register]
      });

      this.httpRequestsInFlight = new Gauge({
        name: 'idp_http_requests_in_flight',
        help: 'Number of HTTP requests currently being processed',
        registers: [register]
      });

      // 数据库指标
      this.databaseConnectionsActive = new Gauge({
        name: 'idp_database_connections_active',
        help: 'Number of active database connections',
        registers: [register]
      });

      this.databaseQueryDuration = new Histogram({
        name: 'idp_database_query_duration_seconds',
        help: 'Duration of database queries in seconds',
        labelNames: ['operation', 'table'],
        buckets: [0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1],
        registers: [register]
      });

      this.databaseQueriesTotal = new Counter({
        name: 'idp_database_queries_total',
        help: 'Total number of database queries',
        labelNames: ['operation', 'table', 'status'],
        registers: [register]
      });

      // Redis指标
      this.redisConnectionsActive = new Gauge({
        name: 'idp_redis_connections_active',
        help: 'Number of active Redis connections',
        registers: [register]
      });

      this.redisCommandDuration = new Histogram({
        name: 'idp_redis_command_duration_seconds',
        help: 'Duration of Redis commands in seconds',
        labelNames: ['command'],
        buckets: [0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1],
        registers: [register]
      });

      this.redisCommandsTotal = new Counter({
        name: 'idp_redis_commands_total',
        help: 'Total number of Redis commands',
        labelNames: ['command', 'status'],
        registers: [register]
      });

      // 认证指标
      this.authAttemptsTotal = new Counter({
        name: 'idp_auth_attempts_total',
        help: 'Total number of authentication attempts',
        labelNames: ['method', 'status'],
        registers: [register]
      });

      this.authSuccessTotal = new Counter({
        name: 'idp_auth_success_total',
        help: 'Total number of successful authentications',
        labelNames: ['method'],
        registers: [register]
      });

      this.activeSessionsTotal = new Gauge({
        name: 'idp_active_sessions_total',
        help: 'Number of active user sessions',
        registers: [register]
      });

      // 系统指标
      this.memoryUsage = new Gauge({
        name: 'idp_memory_usage_bytes',
        help: 'Memory usage in bytes',
        labelNames: ['type'],
        registers: [register]
      });

      this.cpuUsage = new Gauge({
        name: 'idp_cpu_usage_percent',
        help: 'CPU usage percentage',
        registers: [register]
      });

      this.initialized = true;

      // 启动定期指标更新
      this.startPeriodicMetricsUpdate();

      logger.info('Prometheus指标初始化完成', {
        service: 'prometheus',
        metricsCount: register.getMetricsAsArray().length
      });

    } catch (error) {
      logger.error('Prometheus指标初始化失败', {
        service: 'prometheus',
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  /**
   * 启动定期指标更新
   */
  private startPeriodicMetricsUpdate(): void {
    // 每30秒更新一次系统指标
    setInterval(async () => {
      try {
        await this.updateSystemMetrics();
      } catch (error) {
        logger.error('更新系统指标失败', {
          service: 'prometheus',
          error: error instanceof Error ? error.message : String(error)
        });
      }
    }, 30000);
  }

  /**
   * 更新系统指标
   */
  private async updateSystemMetrics(): Promise<void> {
    try {
      // 更新内存使用指标
      const memoryUsage = process.memoryUsage();
      this.memoryUsage.set({ type: 'rss' }, memoryUsage.rss);
      this.memoryUsage.set({ type: 'heap_used' }, memoryUsage.heapUsed);
      this.memoryUsage.set({ type: 'heap_total' }, memoryUsage.heapTotal);
      this.memoryUsage.set({ type: 'external' }, memoryUsage.external);

      // 更新CPU使用指标
      const cpuUsage = process.cpuUsage();
      this.cpuUsage.set(cpuUsage.user / 1000000); // 转换为秒

      // 更新活跃会话数
      const activeSessions = await prisma.session.count({
        where: {
          expiresAt: { gt: new Date() }
        }
      });
      this.activeSessionsTotal.set(activeSessions);

      // 更新Redis连接数
      if (redisService.isReady()) {
        this.redisConnectionsActive.set(1);
      } else {
        this.redisConnectionsActive.set(0);
      }

      // 更新数据库连接数（估算）
      this.databaseConnectionsActive.set(1);

    } catch (error) {
      logger.error('更新系统指标失败', {
        service: 'prometheus',
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  /**
   * 记录HTTP请求指标
   */
  recordHttpRequest(method: string, route: string, statusCode: number, duration: number): void {
    try {
      this.httpRequestsTotal.inc({
        method,
        route,
        status_code: statusCode.toString()
      });

      this.httpRequestDuration.observe({
        method,
        route,
        status_code: statusCode.toString()
      }, duration / 1000); // 转换为秒

    } catch (error) {
      logger.error('记录HTTP请求指标失败', {
        service: 'prometheus',
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  /**
   * 记录数据库查询指标
   */
  recordDatabaseQuery(operation: string, table: string, duration: number, success: boolean): void {
    try {
      this.databaseQueriesTotal.inc({
        operation,
        table,
        status: success ? 'success' : 'error'
      });

      this.databaseQueryDuration.observe({
        operation,
        table
      }, duration / 1000); // 转换为秒

    } catch (error) {
      logger.error('记录数据库查询指标失败', {
        service: 'prometheus',
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  /**
   * 记录Redis命令指标
   */
  recordRedisCommand(command: string, duration: number, success: boolean): void {
    try {
      this.redisCommandsTotal.inc({
        command,
        status: success ? 'success' : 'error'
      });

      this.redisCommandDuration.observe({
        command
      }, duration / 1000); // 转换为秒

    } catch (error) {
      logger.error('记录Redis命令指标失败', {
        service: 'prometheus',
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  /**
   * 记录认证尝试指标
   */
  recordAuthAttempt(method: string, success: boolean): void {
    try {
      this.authAttemptsTotal.inc({
        method,
        status: success ? 'success' : 'failure'
      });

      if (success) {
        this.authSuccessTotal.inc({ method });
      }

    } catch (error) {
      logger.error('记录认证指标失败', {
        service: 'prometheus',
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  /**
   * 增加正在处理的HTTP请求数
   */
  incrementHttpRequestsInFlight(): void {
    try {
      this.httpRequestsInFlight.inc();
    } catch (error) {
      logger.error('增加HTTP请求计数失败', {
        service: 'prometheus',
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  /**
   * 减少正在处理的HTTP请求数
   */
  decrementHttpRequestsInFlight(): void {
    try {
      this.httpRequestsInFlight.dec();
    } catch (error) {
      logger.error('减少HTTP请求计数失败', {
        service: 'prometheus',
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  /**
   * 获取指标数据
   */
  async getMetrics(): Promise<string> {
    try {
      return await register.metrics();
    } catch (error) {
      logger.error('获取Prometheus指标失败', {
        service: 'prometheus',
        error: error instanceof Error ? error.message : String(error)
      });
      return '';
    }
  }

  /**
   * 处理指标端点请求
   */
  async handleMetricsRequest(req: Request, res: Response): Promise<void> {
    try {
      const metrics = await this.getMetrics();
      
      res.set('Content-Type', register.contentType);
      res.status(200).send(metrics);

    } catch (error) {
      logger.error('处理指标请求失败', {
        service: 'prometheus',
        error: error instanceof Error ? error.message : String(error)
      });

      res.status(500).json({
        error: 'metrics_fetch_failed',
        message: '获取指标失败'
      });
    }
  }

  /**
   * 获取指标摘要
   */
  getMetricsSummary(): any {
    try {
      const metrics = register.getMetricsAsArray();
      
      return {
        totalMetrics: metrics.length,
        metricNames: metrics.map(m => m.name),
        lastUpdated: new Date().toISOString()
      };

    } catch (error) {
      logger.error('获取指标摘要失败', {
        service: 'prometheus',
        error: error instanceof Error ? error.message : String(error)
      });
      return null;
    }
  }
}

// 创建单例实例
export const prometheusService = new PrometheusService();
export default prometheusService;
