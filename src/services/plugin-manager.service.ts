/**
 * 插件管理服务
 * 负责加载、管理和执行认证插件
 */

import { IAuthPlugin, IProtocolAdapter } from '@/types/protocol-adapter';
import { logger } from '@/config/logger';
import { prisma } from '@/config/database';
import { EventEmitter } from 'events';
import path from 'path';
import fs from 'fs/promises';
import crypto from 'crypto';

/**
 * 插件加载错误
 */
export class PluginLoadError extends Error {
  constructor(
    public pluginName: string,
    message: string,
    public originalError?: Error
  ) {
    super(`插件 ${pluginName} 加载失败: ${message}`);
    this.name = 'PluginLoadError';
  }
}

/**
 * 插件管理器
 */
export class PluginManager extends EventEmitter {
  private loadedPlugins: Map<string, IAuthPlugin> = new Map();
  private protocolAdapters: Map<string, new () => IProtocolAdapter> = new Map();
  private customHandlers: Map<string, Function> = new Map();
  private pluginDirectory: string;

  constructor(pluginDirectory: string = './plugins') {
    super();
    this.pluginDirectory = path.resolve(pluginDirectory);
  }

  /**
   * 初始化插件管理器
   */
  async initialize(): Promise<void> {
    try {
      // 确保插件目录存在
      await this.ensurePluginDirectory();

      // 加载数据库中的插件配置
      await this.loadPluginsFromDatabase();

      logger.info('插件管理器初始化完成', {
        loadedPlugins: this.loadedPlugins.size,
        protocolAdapters: this.protocolAdapters.size,
        customHandlers: this.customHandlers.size
      });

    } catch (error) {
      logger.error('插件管理器初始化失败', { error });
      throw error;
    }
  }

  /**
   * 确保插件目录存在
   */
  private async ensurePluginDirectory(): Promise<void> {
    try {
      await fs.access(this.pluginDirectory);
    } catch {
      await fs.mkdir(this.pluginDirectory, { recursive: true });
      logger.info('创建插件目录', { directory: this.pluginDirectory });
    }
  }

  /**
   * 从数据库加载插件
   */
  private async loadPluginsFromDatabase(): Promise<void> {
    try {
      const plugins = await prisma.plugin.findMany({
        where: { isEnabled: true }
      });

      for (const pluginRecord of plugins) {
        try {
          await this.loadPlugin(pluginRecord);
        } catch (error) {
          logger.error('加载插件失败', {
            pluginName: pluginRecord.name,
            error
          });

          // 更新数据库中的错误状态
          await prisma.plugin.update({
            where: { id: pluginRecord.id },
            data: {
              isLoaded: false,
              loadError: error.message
            }
          });
        }
      }

    } catch (error) {
      logger.error('从数据库加载插件失败', { error });
      throw error;
    }
  }

  /**
   * 加载单个插件
   */
  private async loadPlugin(pluginRecord: any): Promise<void> {
    const { name, filePath, entryPoint, checksum, config } = pluginRecord;

    try {
      // 验证插件文件
      const fullPath = path.resolve(this.pluginDirectory, filePath);
      await this.validatePluginFile(fullPath, checksum);

      // 动态加载插件模块
      const pluginModule = await this.loadPluginModule(fullPath, entryPoint);

      // 创建插件实例
      const plugin: IAuthPlugin = new pluginModule();

      // 验证插件接口
      this.validatePluginInterface(plugin);

      // 初始化插件
      await plugin.initialize(config || {});

      // 注册插件
      this.loadedPlugins.set(name, plugin);

      // 注册协议适配器
      if (plugin.getProtocolAdapters) {
        const adapters = plugin.getProtocolAdapters();
        for (const [protocolName, adapterClass] of Object.entries(adapters)) {
          this.protocolAdapters.set(protocolName, adapterClass);
          logger.info('注册协议适配器', { pluginName: name, protocolName });
        }
      }

      // 注册自定义处理器
      if (plugin.getCustomHandlers) {
        const handlers = plugin.getCustomHandlers();
        for (const [handlerName, handlerFunc] of Object.entries(handlers)) {
          this.customHandlers.set(handlerName, handlerFunc);
          logger.info('注册自定义处理器', { pluginName: name, handlerName });
        }
      }

      // 更新数据库状态
      await prisma.plugin.update({
        where: { id: pluginRecord.id },
        data: {
          isLoaded: true,
          loadError: null
        }
      });

      // 发送插件加载事件
      this.emit('plugin_loaded', { name, plugin });

      logger.info('插件加载成功', { name, version: plugin.version });

    } catch (error) {
      const loadError = new PluginLoadError(name, error.message, error);
      logger.error('插件加载失败', { name, error: loadError });
      throw loadError;
    }
  }

  /**
   * 验证插件文件
   */
  private async validatePluginFile(filePath: string, expectedChecksum: string): Promise<void> {
    try {
      // 检查文件是否存在
      await fs.access(filePath);

      // 计算文件校验和
      const fileContent = await fs.readFile(filePath);
      const actualChecksum = crypto.createHash('sha256').update(fileContent).digest('hex');

      if (actualChecksum !== expectedChecksum) {
        throw new Error(`插件文件校验和不匹配: 期望 ${expectedChecksum}, 实际 ${actualChecksum}`);
      }

    } catch (error) {
      throw new Error(`插件文件验证失败: ${error.message}`);
    }
  }

  /**
   * 动态加载插件模块
   */
  private async loadPluginModule(filePath: string, entryPoint: string): Promise<any> {
    try {
      // 清除模块缓存
      delete require.cache[require.resolve(filePath)];

      // 加载模块
      const module = require(filePath);

      // 获取入口点
      const PluginClass = module[entryPoint] || module.default || module;

      if (typeof PluginClass !== 'function') {
        throw new Error(`入口点 ${entryPoint} 不是一个构造函数`);
      }

      return PluginClass;

    } catch (error) {
      throw new Error(`加载插件模块失败: ${error.message}`);
    }
  }

  /**
   * 验证插件接口
   */
  private validatePluginInterface(plugin: any): void {
    const requiredProperties = ['name', 'version', 'description', 'initialize', 'destroy'];

    for (const prop of requiredProperties) {
      if (!(prop in plugin)) {
        throw new Error(`插件缺少必需的属性或方法: ${prop}`);
      }
    }

    if (typeof plugin.initialize !== 'function') {
      throw new Error('插件的 initialize 方法必须是函数');
    }

    if (typeof plugin.destroy !== 'function') {
      throw new Error('插件的 destroy 方法必须是函数');
    }
  }

  /**
   * 安装插件
   */
  async installPlugin(
    name: string,
    version: string,
    filePath: string,
    entryPoint: string,
    config?: Record<string, any>
  ): Promise<void> {
    try {
      // 计算文件校验和
      const fullPath = path.resolve(this.pluginDirectory, filePath);
      const fileContent = await fs.readFile(fullPath);
      const checksum = crypto.createHash('sha256').update(fileContent).digest('hex');

      // 保存到数据库
      const pluginRecord = await prisma.plugin.create({
        data: {
          name,
          version,
          filePath,
          entryPoint,
          checksum,
          config,
          isEnabled: false,
          isLoaded: false
        }
      });

      logger.info('插件安装成功', { name, version, id: pluginRecord.id });

    } catch (error) {
      logger.error('插件安装失败', { name, error });
      throw error;
    }
  }

  /**
   * 启用插件
   */
  async enablePlugin(name: string): Promise<void> {
    try {
      const pluginRecord = await prisma.plugin.findUnique({
        where: { name }
      });

      if (!pluginRecord) {
        throw new Error(`插件 ${name} 不存在`);
      }

      if (pluginRecord.isEnabled) {
        throw new Error(`插件 ${name} 已经启用`);
      }

      // 更新数据库状态
      await prisma.plugin.update({
        where: { name },
        data: { isEnabled: true }
      });

      // 加载插件
      await this.loadPlugin(pluginRecord);

      logger.info('插件启用成功', { name });

    } catch (error) {
      logger.error('插件启用失败', { name, error });
      throw error;
    }
  }

  /**
   * 禁用插件
   */
  async disablePlugin(name: string): Promise<void> {
    try {
      const plugin = this.loadedPlugins.get(name);
      if (plugin) {
        // 销毁插件
        await plugin.destroy();

        // 移除注册的适配器和处理器
        this.removePluginRegistrations(name, plugin);

        // 从内存中移除
        this.loadedPlugins.delete(name);

        // 发送插件卸载事件
        this.emit('plugin_unloaded', { name, plugin });
      }

      // 更新数据库状态
      await prisma.plugin.update({
        where: { name },
        data: {
          isEnabled: false,
          isLoaded: false
        }
      });

      logger.info('插件禁用成功', { name });

    } catch (error) {
      logger.error('插件禁用失败', { name, error });
      throw error;
    }
  }

  /**
   * 移除插件注册
   */
  private removePluginRegistrations(pluginName: string, plugin: IAuthPlugin): void {
    // 移除协议适配器
    if (plugin.getProtocolAdapters) {
      const adapters = plugin.getProtocolAdapters();
      for (const protocolName of Object.keys(adapters)) {
        this.protocolAdapters.delete(protocolName);
        logger.info('移除协议适配器', { pluginName, protocolName });
      }
    }

    // 移除自定义处理器
    if (plugin.getCustomHandlers) {
      const handlers = plugin.getCustomHandlers();
      for (const handlerName of Object.keys(handlers)) {
        this.customHandlers.delete(handlerName);
        logger.info('移除自定义处理器', { pluginName, handlerName });
      }
    }
  }

  /**
   * 卸载插件
   */
  async uninstallPlugin(name: string): Promise<void> {
    try {
      // 先禁用插件
      await this.disablePlugin(name);

      // 从数据库删除
      await prisma.plugin.delete({
        where: { name }
      });

      logger.info('插件卸载成功', { name });

    } catch (error) {
      logger.error('插件卸载失败', { name, error });
      throw error;
    }
  }

  /**
   * 获取协议适配器
   */
  getProtocolAdapter(protocolName: string): (new () => IProtocolAdapter) | undefined {
    return this.protocolAdapters.get(protocolName);
  }

  /**
   * 获取自定义处理器
   */
  getCustomHandler(handlerName: string): Function | undefined {
    return this.customHandlers.get(handlerName);
  }

  /**
   * 执行自定义处理器
   */
  async executeCustomHandler(handlerName: string, ...args: any[]): Promise<any> {
    const handler = this.customHandlers.get(handlerName);
    if (!handler) {
      throw new Error(`自定义处理器 ${handlerName} 不存在`);
    }

    try {
      return await handler(...args);
    } catch (error) {
      logger.error('执行自定义处理器失败', { handlerName, error });
      throw error;
    }
  }

  /**
   * 获取已加载的插件列表
   */
  getLoadedPlugins(): string[] {
    return Array.from(this.loadedPlugins.keys());
  }

  /**
   * 获取可用的协议适配器列表
   */
  getAvailableProtocolAdapters(): string[] {
    return Array.from(this.protocolAdapters.keys());
  }

  /**
   * 获取可用的自定义处理器列表
   */
  getAvailableCustomHandlers(): string[] {
    return Array.from(this.customHandlers.keys());
  }

  /**
   * 插件健康检查
   */
  async healthCheck(): Promise<Record<string, boolean>> {
    const results: Record<string, boolean> = {};

    for (const [name, plugin] of this.loadedPlugins) {
      try {
        if (plugin.healthCheck) {
          results[name] = await plugin.healthCheck();
        } else {
          results[name] = true; // 如果没有健康检查方法，默认为健康
        }
      } catch (error) {
        logger.error('插件健康检查失败', { name, error });
        results[name] = false;
      }
    }

    return results;
  }

  /**
   * 销毁插件管理器
   */
  async destroy(): Promise<void> {
    try {
      // 销毁所有插件
      for (const [name, plugin] of this.loadedPlugins) {
        try {
          await plugin.destroy();
          logger.info('插件销毁成功', { name });
        } catch (error) {
          logger.error('插件销毁失败', { name, error });
        }
      }

      // 清空所有映射
      this.loadedPlugins.clear();
      this.protocolAdapters.clear();
      this.customHandlers.clear();

      logger.info('插件管理器销毁完成');

    } catch (error) {
      logger.error('插件管理器销毁失败', { error });
      throw error;
    }
  }
}
