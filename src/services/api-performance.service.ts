/**
 * API性能优化服务
 * 提供请求优化、缓存管理、并发控制和性能监控功能
 */

import { Request, Response, NextFunction } from 'express'
import { logger } from '@/config/logger'
import { cacheService } from './cache.service'
import { metricsCollector } from './metrics-collector.service'

export interface RequestMetrics {
  path: string
  method: string
  statusCode: number
  responseTime: number
  timestamp: Date
  userAgent?: string
  ip?: string
  userId?: string
}

export interface PerformanceConfig {
  enableCompression: boolean
  enableCaching: boolean
  enableRateLimit: boolean
  maxConcurrentRequests: number
  requestTimeout: number
  cacheMaxAge: number
}

/**
 * API性能优化服务类
 */
class ApiPerformanceService {
  private requestMetrics: RequestMetrics[] = []
  private activeRequests = new Map<string, number>()
  private maxMetricsHistory = 10000
  
  private config: PerformanceConfig = {
    enableCompression: true,
    enableCaching: true,
    enableRateLimit: true,
    maxConcurrentRequests: 100,
    requestTimeout: 30000,
    cacheMaxAge: 300
  }

  /**
   * 性能监控中间件
   */
  performanceMiddleware() {
    return (req: Request, res: Response, next: NextFunction) => {
      const startTime = Date.now()
      const requestId = `${req.method}-${req.path}-${Date.now()}`

      // 记录请求开始
      this.recordRequestStart(requestId, req)

      // 监听响应完成
      res.on('finish', () => {
        const responseTime = Date.now() - startTime
        this.recordRequestEnd(requestId, req, res, responseTime)
      })

      // 设置请求超时
      req.setTimeout(this.config.requestTimeout, () => {
        logger.warn('请求超时', {
          service: 'api-performance',
          path: req.path,
          method: req.method,
          timeout: this.config.requestTimeout
        })
      })

      next()
    }
  }

  /**
   * 响应缓存中间件
   */
  cacheMiddleware(options: { ttl?: number; keyGenerator?: (req: Request) => string } = {}) {
    return async (req: Request, res: Response, next: NextFunction) => {
      if (!this.config.enableCaching || req.method !== 'GET') {
        return next()
      }

      const cacheKey = options.keyGenerator 
        ? options.keyGenerator(req)
        : `api:${req.path}:${JSON.stringify(req.query)}`

      try {
        // 尝试从缓存获取
        const cachedResponse = await cacheService.getCached(cacheKey)
        
        if (cachedResponse) {
          res.set('X-Cache', 'HIT')
          res.set('Cache-Control', `public, max-age=${options.ttl || this.config.cacheMaxAge}`)
          return res.json(cachedResponse)
        }

        // 缓存未命中，继续处理请求
        res.set('X-Cache', 'MISS')
        
        // 拦截原始的json方法
        const originalJson = res.json.bind(res)
        res.json = (data: any) => {
          // 缓存响应数据
          if (res.statusCode === 200) {
            cacheService.cache(cacheKey, data, options.ttl || this.config.cacheMaxAge)
              .catch(error => {
                logger.error('缓存响应失败', {
                  service: 'api-performance',
                  cacheKey,
                  error: error.message
                })
              })
          }
          
          return originalJson(data)
        }

        next()

      } catch (error) {
        logger.error('缓存中间件错误', {
          service: 'api-performance',
          cacheKey,
          error: error instanceof Error ? error.message : String(error)
        })
        next()
      }
    }
  }

  /**
   * 并发控制中间件
   */
  concurrencyMiddleware() {
    return (req: Request, res: Response, next: NextFunction) => {
      const endpoint = `${req.method}:${req.route?.path || req.path}`
      const currentCount = this.activeRequests.get(endpoint) || 0

      if (currentCount >= this.config.maxConcurrentRequests) {
        return res.status(429).json({
          error: 'Too Many Requests',
          message: '服务器繁忙，请稍后重试',
          retryAfter: 5
        })
      }

      // 增加并发计数
      this.activeRequests.set(endpoint, currentCount + 1)

      // 请求完成时减少计数
      res.on('finish', () => {
        const count = this.activeRequests.get(endpoint) || 0
        if (count > 1) {
          this.activeRequests.set(endpoint, count - 1)
        } else {
          this.activeRequests.delete(endpoint)
        }
      })

      next()
    }
  }

  /**
   * 响应压缩中间件
   */
  compressionMiddleware() {
    return (req: Request, res: Response, next: NextFunction) => {
      if (!this.config.enableCompression) {
        return next()
      }

      // 检查客户端是否支持压缩
      const acceptEncoding = req.headers['accept-encoding'] || ''
      
      if (!acceptEncoding.includes('gzip') && !acceptEncoding.includes('deflate')) {
        return next()
      }

      // 拦截原始的json方法
      const originalJson = res.json.bind(res)
      res.json = (data: any) => {
        const jsonString = JSON.stringify(data)
        
        // 只对大于1KB的响应进行压缩
        if (jsonString.length > 1024) {
          res.set('Content-Encoding', 'gzip')
          res.set('Vary', 'Accept-Encoding')
        }
        
        return originalJson(data)
      }

      next()
    }
  }

  /**
   * 记录请求开始
   */
  private recordRequestStart(requestId: string, req: Request): void {
    // 记录活跃请求
    logger.debug('API请求开始', {
      service: 'api-performance',
      requestId,
      method: req.method,
      path: req.path,
      userAgent: req.headers['user-agent'],
      ip: req.ip
    })
  }

  /**
   * 记录请求结束
   */
  private recordRequestEnd(
    requestId: string,
    req: Request,
    res: Response,
    responseTime: number
  ): void {
    const metrics: RequestMetrics = {
      path: req.path,
      method: req.method,
      statusCode: res.statusCode,
      responseTime,
      timestamp: new Date(),
      userAgent: req.headers['user-agent'],
      ip: req.ip,
      userId: (req as any).user?.id
    }

    // 添加到指标历史
    this.addMetrics(metrics)

    // 记录到监控系统
    metricsCollector.recordHistogram('api_request_duration', responseTime, {
      method: req.method,
      path: req.route?.path || req.path,
      status_code: res.statusCode.toString()
    })

    metricsCollector.incrementCounter('api_requests_total', {
      method: req.method,
      path: req.route?.path || req.path,
      status_code: res.statusCode.toString()
    })

    // 记录慢请求
    if (responseTime > 1000) {
      logger.warn('API慢请求', {
        service: 'api-performance',
        requestId,
        method: req.method,
        path: req.path,
        responseTime,
        statusCode: res.statusCode
      })

      metricsCollector.incrementCounter('api_slow_requests_total', {
        method: req.method,
        path: req.route?.path || req.path
      })
    }

    // 记录错误请求
    if (res.statusCode >= 400) {
      logger.error('API错误请求', {
        service: 'api-performance',
        requestId,
        method: req.method,
        path: req.path,
        statusCode: res.statusCode,
        responseTime
      })

      metricsCollector.incrementCounter('api_errors_total', {
        method: req.method,
        path: req.route?.path || req.path,
        status_code: res.statusCode.toString()
      })
    }
  }

  /**
   * 添加指标到历史记录
   */
  private addMetrics(metrics: RequestMetrics): void {
    this.requestMetrics.push(metrics)
    
    // 保持历史记录大小限制
    if (this.requestMetrics.length > this.maxMetricsHistory) {
      this.requestMetrics.shift()
    }
  }

  /**
   * 获取性能统计
   */
  getPerformanceStats(timeRange?: { start: Date; end: Date }) {
    let filteredMetrics = this.requestMetrics

    if (timeRange) {
      filteredMetrics = this.requestMetrics.filter(
        m => m.timestamp >= timeRange.start && m.timestamp <= timeRange.end
      )
    }

    const totalRequests = filteredMetrics.length
    const avgResponseTime = totalRequests > 0
      ? filteredMetrics.reduce((sum, m) => sum + m.responseTime, 0) / totalRequests
      : 0

    const errorRequests = filteredMetrics.filter(m => m.statusCode >= 400).length
    const slowRequests = filteredMetrics.filter(m => m.responseTime > 1000).length

    const statusCodeDistribution = filteredMetrics.reduce((acc, m) => {
      const statusGroup = Math.floor(m.statusCode / 100) * 100
      acc[statusGroup] = (acc[statusGroup] || 0) + 1
      return acc
    }, {} as Record<number, number>)

    const pathStats = filteredMetrics.reduce((acc, m) => {
      const key = `${m.method} ${m.path}`
      if (!acc[key]) {
        acc[key] = { count: 0, totalTime: 0, errors: 0 }
      }
      acc[key].count++
      acc[key].totalTime += m.responseTime
      if (m.statusCode >= 400) {
        acc[key].errors++
      }
      return acc
    }, {} as Record<string, { count: number; totalTime: number; errors: number }>)

    return {
      totalRequests,
      avgResponseTime: Math.round(avgResponseTime),
      errorRate: totalRequests > 0 ? (errorRequests / totalRequests) * 100 : 0,
      slowRequestRate: totalRequests > 0 ? (slowRequests / totalRequests) * 100 : 0,
      statusCodeDistribution,
      pathStats: Object.entries(pathStats).map(([path, stats]) => ({
        path,
        count: stats.count,
        avgResponseTime: Math.round(stats.totalTime / stats.count),
        errorRate: (stats.errors / stats.count) * 100
      })).sort((a, b) => b.count - a.count),
      activeRequests: Array.from(this.activeRequests.entries()).map(([endpoint, count]) => ({
        endpoint,
        count
      }))
    }
  }

  /**
   * 清理过期指标
   */
  cleanupMetrics(maxAge: number = 24 * 60 * 60 * 1000): number {
    const cutoff = new Date(Date.now() - maxAge)
    const initialLength = this.requestMetrics.length
    
    this.requestMetrics = this.requestMetrics.filter(m => m.timestamp > cutoff)
    
    const cleaned = initialLength - this.requestMetrics.length
    
    if (cleaned > 0) {
      logger.info('清理过期性能指标', {
        service: 'api-performance',
        cleaned,
        remaining: this.requestMetrics.length
      })
    }
    
    return cleaned
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<PerformanceConfig>): void {
    this.config = { ...this.config, ...newConfig }
    
    logger.info('API性能配置已更新', {
      service: 'api-performance',
      config: this.config
    })
  }

  /**
   * 获取当前配置
   */
  getConfig(): PerformanceConfig {
    return { ...this.config }
  }
}

// 导出服务实例
export const apiPerformanceService = new ApiPerformanceService()
export default apiPerformanceService
