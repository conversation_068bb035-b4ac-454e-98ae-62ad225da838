/**
 * 移动端认证服务
 * 提供移动设备认证、生物识别、推送通知、设备管理等功能
 */

import crypto from 'crypto';
import { logger } from '@/config/logger';
import { prisma } from '@/config/database';
import { cacheService } from '@/services/cache.service';
import { securityAuditService, AuditEventType, AuditSeverity } from '@/services/security-audit.service';
import { metricsCollector } from '@/services/metrics-collector.service';

/**
 * 移动设备类型
 */
export enum MobileDeviceType {
  IOS = 'ios',
  ANDROID = 'android',
  WINDOWS_PHONE = 'windows_phone',
  OTHER = 'other'
}

/**
 * 生物识别类型
 */
export enum BiometricType {
  FINGERPRINT = 'fingerprint',
  FACE_ID = 'face_id',
  TOUCH_ID = 'touch_id',
  VOICE = 'voice',
  IRIS = 'iris',
  PALM = 'palm'
}

/**
 * 移动认证方法
 */
export enum MobileAuthMethod {
  BIOMETRIC = 'biometric',
  PIN = 'pin',
  PATTERN = 'pattern',
  PASSWORD = 'password',
  PUSH_NOTIFICATION = 'push_notification',
  QR_CODE = 'qr_code',
  NFC = 'nfc',
  BLUETOOTH = 'bluetooth'
}

/**
 * 移动设备信息接口
 */
interface MobileDeviceInfo {
  id: string;
  userId: string;
  deviceType: MobileDeviceType;
  deviceId: string; // 设备唯一标识
  deviceName: string;
  osVersion: string;
  appVersion: string;
  manufacturer: string;
  model: string;
  screenResolution: string;
  isJailbroken: boolean;
  isRooted: boolean;
  hasSecureHardware: boolean;
  supportedBiometrics: BiometricType[];
  pushToken?: string;
  publicKey?: string; // 设备公钥
  attestationData?: string; // 设备证明数据
  trustScore: number; // 0-100
  isActive: boolean;
  lastSeen: Date;
  registeredAt: Date;
  metadata: Record<string, any>;
}

/**
 * 生物识别模板接口
 */
interface BiometricTemplate {
  id: string;
  userId: string;
  deviceId: string;
  biometricType: BiometricType;
  templateHash: string; // 生物识别模板哈希
  quality: number; // 模板质量 0-100
  isActive: boolean;
  createdAt: Date;
  lastUsed?: Date;
  useCount: number;
  metadata: Record<string, any>;
}

/**
 * 移动认证会话接口
 */
interface MobileAuthSession {
  id: string;
  userId: string;
  deviceId: string;
  authMethod: MobileAuthMethod;
  challenge: string;
  challengeExpiry: Date;
  isCompleted: boolean;
  isSuccessful?: boolean;
  attempts: number;
  maxAttempts: number;
  createdAt: Date;
  completedAt?: Date;
  metadata: Record<string, any>;
}

/**
 * 推送通知接口
 */
interface PushNotification {
  id: string;
  userId: string;
  deviceId: string;
  title: string;
  body: string;
  data: Record<string, any>;
  priority: 'low' | 'normal' | 'high';
  ttl: number; // 生存时间（秒）
  sentAt: Date;
  deliveredAt?: Date;
  clickedAt?: Date;
  status: 'pending' | 'sent' | 'delivered' | 'clicked' | 'failed';
}

/**
 * 移动端认证服务
 */
export class MobileAuthService {
  private deviceCache = new Map<string, MobileDeviceInfo>();
  private sessionCache = new Map<string, MobileAuthSession>();

  /**
   * 注册移动设备
   */
  async registerDevice(deviceInfo: Partial<MobileDeviceInfo>): Promise<MobileDeviceInfo> {
    try {
      const deviceId = deviceInfo.deviceId || this.generateDeviceId();
      
      // 检查设备是否已存在
      const existingDevice = await this.getDeviceByDeviceId(deviceId);
      if (existingDevice) {
        // 更新设备信息
        return await this.updateDevice(existingDevice.id, deviceInfo);
      }

      // 创建新设备
      const device: MobileDeviceInfo = {
        id: this.generateId(),
        userId: deviceInfo.userId!,
        deviceType: deviceInfo.deviceType || MobileDeviceType.OTHER,
        deviceId,
        deviceName: deviceInfo.deviceName || 'Unknown Device',
        osVersion: deviceInfo.osVersion || 'Unknown',
        appVersion: deviceInfo.appVersion || '1.0.0',
        manufacturer: deviceInfo.manufacturer || 'Unknown',
        model: deviceInfo.model || 'Unknown',
        screenResolution: deviceInfo.screenResolution || 'Unknown',
        isJailbroken: deviceInfo.isJailbroken || false,
        isRooted: deviceInfo.isRooted || false,
        hasSecureHardware: deviceInfo.hasSecureHardware || false,
        supportedBiometrics: deviceInfo.supportedBiometrics || [],
        pushToken: deviceInfo.pushToken,
        publicKey: deviceInfo.publicKey,
        attestationData: deviceInfo.attestationData,
        trustScore: this.calculateInitialTrustScore(deviceInfo),
        isActive: true,
        lastSeen: new Date(),
        registeredAt: new Date(),
        metadata: deviceInfo.metadata || {}
      };

      // 保存到数据库
      await this.saveDevice(device);

      // 缓存设备信息
      this.deviceCache.set(deviceId, device);

      // 记录审计事件
      await securityAuditService.logAuditEvent({
        eventType: AuditEventType.DEVICE_REGISTRATION,
        severity: AuditSeverity.MEDIUM,
        userId: device.userId,
        ipAddress: 'mobile',
        userAgent: `${device.deviceType}/${device.osVersion}`,
        resource: 'mobile_device',
        action: 'register',
        details: {
          deviceId: device.deviceId,
          deviceType: device.deviceType,
          manufacturer: device.manufacturer,
          model: device.model,
          trustScore: device.trustScore
        },
        success: true
      });

      // 记录指标
      metricsCollector.incrementCounter('mobile_devices_registered_total', {
        device_type: device.deviceType,
        manufacturer: device.manufacturer
      });

      logger.info('移动设备注册成功', {
        deviceId: device.deviceId,
        userId: device.userId,
        deviceType: device.deviceType,
        trustScore: device.trustScore
      });

      return device;

    } catch (error) {
      logger.error('移动设备注册失败', {
        error: error instanceof Error ? error.message : String(error),
        deviceId: deviceInfo.deviceId,
        userId: deviceInfo.userId
      });
      throw error;
    }
  }

  /**
   * 注册生物识别模板
   */
  async registerBiometric(
    userId: string,
    deviceId: string,
    biometricType: BiometricType,
    templateData: string,
    quality: number = 80
  ): Promise<BiometricTemplate> {
    try {
      // 验证设备
      const device = await this.getDeviceByDeviceId(deviceId);
      if (!device || device.userId !== userId) {
        throw new Error('设备不存在或不属于当前用户');
      }

      // 检查设备是否支持该生物识别类型
      if (!device.supportedBiometrics.includes(biometricType)) {
        throw new Error(`设备不支持${biometricType}生物识别`);
      }

      // 生成模板哈希
      const templateHash = crypto.createHash('sha256').update(templateData).digest('hex');

      // 检查是否已存在相同模板
      const existingTemplate = await this.getBiometricTemplate(userId, deviceId, biometricType);
      if (existingTemplate) {
        // 更新现有模板
        existingTemplate.templateHash = templateHash;
        existingTemplate.quality = quality;
        await this.updateBiometricTemplate(existingTemplate);
        return existingTemplate;
      }

      // 创建新模板
      const template: BiometricTemplate = {
        id: this.generateId(),
        userId,
        deviceId,
        biometricType,
        templateHash,
        quality,
        isActive: true,
        createdAt: new Date(),
        useCount: 0,
        metadata: {}
      };

      // 保存到数据库
      await this.saveBiometricTemplate(template);

      // 记录审计事件
      await securityAuditService.logAuditEvent({
        eventType: AuditEventType.BIOMETRIC_REGISTRATION,
        severity: AuditSeverity.MEDIUM,
        userId,
        ipAddress: 'mobile',
        userAgent: `${device.deviceType}/${device.osVersion}`,
        resource: 'biometric_template',
        action: 'register',
        details: {
          deviceId,
          biometricType,
          quality,
          templateId: template.id
        },
        success: true
      });

      logger.info('生物识别模板注册成功', {
        userId,
        deviceId,
        biometricType,
        quality,
        templateId: template.id
      });

      return template;

    } catch (error) {
      logger.error('生物识别模板注册失败', {
        error: error instanceof Error ? error.message : String(error),
        userId,
        deviceId,
        biometricType
      });
      throw error;
    }
  }

  /**
   * 创建移动认证会话
   */
  async createAuthSession(
    userId: string,
    deviceId: string,
    authMethod: MobileAuthMethod,
    options: {
      ttl?: number;
      maxAttempts?: number;
      metadata?: Record<string, any>;
    } = {}
  ): Promise<MobileAuthSession> {
    try {
      const { ttl = 300, maxAttempts = 3, metadata = {} } = options;

      // 验证设备
      const device = await this.getDeviceByDeviceId(deviceId);
      if (!device || device.userId !== userId) {
        throw new Error('设备不存在或不属于当前用户');
      }

      // 生成挑战
      const challenge = this.generateChallenge();
      const challengeExpiry = new Date(Date.now() + ttl * 1000);

      // 创建认证会话
      const session: MobileAuthSession = {
        id: this.generateId(),
        userId,
        deviceId,
        authMethod,
        challenge,
        challengeExpiry,
        isCompleted: false,
        attempts: 0,
        maxAttempts,
        createdAt: new Date(),
        metadata
      };

      // 缓存会话
      this.sessionCache.set(session.id, session);

      // 设置过期清理
      setTimeout(() => {
        this.sessionCache.delete(session.id);
      }, ttl * 1000);

      // 如果是推送通知认证，发送推送
      if (authMethod === MobileAuthMethod.PUSH_NOTIFICATION) {
        await this.sendAuthPushNotification(session);
      }

      logger.info('移动认证会话创建成功', {
        sessionId: session.id,
        userId,
        deviceId,
        authMethod,
        ttl
      });

      return session;

    } catch (error) {
      logger.error('移动认证会话创建失败', {
        error: error instanceof Error ? error.message : String(error),
        userId,
        deviceId,
        authMethod
      });
      throw error;
    }
  }

  /**
   * 验证移动认证
   */
  async verifyMobileAuth(
    sessionId: string,
    response: string,
    additionalData?: Record<string, any>
  ): Promise<{ success: boolean; session: MobileAuthSession; details?: any }> {
    try {
      // 获取会话
      const session = this.sessionCache.get(sessionId);
      if (!session) {
        throw new Error('认证会话不存在或已过期');
      }

      // 检查会话状态
      if (session.isCompleted) {
        throw new Error('认证会话已完成');
      }

      if (new Date() > session.challengeExpiry) {
        throw new Error('认证会话已过期');
      }

      if (session.attempts >= session.maxAttempts) {
        throw new Error('认证尝试次数已达上限');
      }

      // 增加尝试次数
      session.attempts++;

      let verificationResult = false;
      let details: any = {};

      // 根据认证方法进行验证
      switch (session.authMethod) {
        case MobileAuthMethod.BIOMETRIC:
          verificationResult = await this.verifyBiometric(session, response, additionalData);
          break;
        case MobileAuthMethod.PIN:
        case MobileAuthMethod.PASSWORD:
          verificationResult = await this.verifyCredential(session, response);
          break;
        case MobileAuthMethod.PATTERN:
          verificationResult = await this.verifyPattern(session, response);
          break;
        case MobileAuthMethod.PUSH_NOTIFICATION:
          verificationResult = await this.verifyPushResponse(session, response);
          break;
        case MobileAuthMethod.QR_CODE:
          verificationResult = await this.verifyQRCode(session, response);
          break;
        default:
          throw new Error(`不支持的认证方法: ${session.authMethod}`);
      }

      // 更新会话状态
      session.isCompleted = true;
      session.isSuccessful = verificationResult;
      session.completedAt = new Date();

      if (verificationResult) {
        // 认证成功，更新设备信任度
        await this.updateDeviceTrustScore(session.deviceId, 5);
      } else {
        // 认证失败，降低设备信任度
        await this.updateDeviceTrustScore(session.deviceId, -2);
      }

      // 记录审计事件
      await securityAuditService.logAuditEvent({
        eventType: verificationResult ? AuditEventType.LOGIN_SUCCESS : AuditEventType.LOGIN_FAILED,
        severity: verificationResult ? AuditSeverity.LOW : AuditSeverity.MEDIUM,
        userId: session.userId,
        ipAddress: 'mobile',
        userAgent: 'mobile_app',
        resource: 'mobile_auth',
        action: 'verify',
        details: {
          sessionId,
          authMethod: session.authMethod,
          attempts: session.attempts,
          deviceId: session.deviceId
        },
        success: verificationResult
      });

      // 记录指标
      metricsCollector.incrementCounter('mobile_auth_attempts_total', {
        auth_method: session.authMethod,
        result: verificationResult ? 'success' : 'failure'
      });

      return {
        success: verificationResult,
        session,
        details
      };

    } catch (error) {
      logger.error('移动认证验证失败', {
        error: error instanceof Error ? error.message : String(error),
        sessionId
      });
      throw error;
    }
  }

  /**
   * 发送推送通知
   */
  async sendPushNotification(
    userId: string,
    deviceId: string,
    notification: Partial<PushNotification>
  ): Promise<PushNotification> {
    try {
      // 获取设备信息
      const device = await this.getDeviceByDeviceId(deviceId);
      if (!device || device.userId !== userId || !device.pushToken) {
        throw new Error('设备不存在、不属于当前用户或未配置推送令牌');
      }

      // 创建推送通知
      const pushNotification: PushNotification = {
        id: this.generateId(),
        userId,
        deviceId,
        title: notification.title || '身份验证',
        body: notification.body || '请确认您的身份',
        data: notification.data || {},
        priority: notification.priority || 'normal',
        ttl: notification.ttl || 300,
        sentAt: new Date(),
        status: 'pending'
      };

      // 发送推送通知（这里需要集成实际的推送服务）
      const sendResult = await this.sendPushToDevice(device, pushNotification);
      
      if (sendResult.success) {
        pushNotification.status = 'sent';
        pushNotification.sentAt = new Date();
      } else {
        pushNotification.status = 'failed';
      }

      // 保存推送记录
      await this.savePushNotification(pushNotification);

      logger.info('推送通知发送完成', {
        notificationId: pushNotification.id,
        userId,
        deviceId,
        status: pushNotification.status
      });

      return pushNotification;

    } catch (error) {
      logger.error('推送通知发送失败', {
        error: error instanceof Error ? error.message : String(error),
        userId,
        deviceId
      });
      throw error;
    }
  }

  /**
   * 获取用户的移动设备列表
   */
  async getUserDevices(userId: string): Promise<MobileDeviceInfo[]> {
    try {
      const devices = await prisma.mobileDevice.findMany({
        where: { userId, isActive: true },
        orderBy: { lastSeen: 'desc' }
      });

      return devices.map(device => this.mapPrismaToDevice(device));

    } catch (error) {
      logger.error('获取用户移动设备列表失败', {
        error: error instanceof Error ? error.message : String(error),
        userId
      });
      return [];
    }
  }

  /**
   * 停用移动设备
   */
  async deactivateDevice(userId: string, deviceId: string): Promise<void> {
    try {
      const device = await this.getDeviceByDeviceId(deviceId);
      if (!device || device.userId !== userId) {
        throw new Error('设备不存在或不属于当前用户');
      }

      // 更新设备状态
      device.isActive = false;
      await this.updateDevice(device.id, { isActive: false });

      // 清除缓存
      this.deviceCache.delete(deviceId);

      // 停用相关生物识别模板
      await this.deactivateBiometricTemplates(deviceId);

      // 记录审计事件
      await securityAuditService.logAuditEvent({
        eventType: AuditEventType.DEVICE_DEACTIVATION,
        severity: AuditSeverity.MEDIUM,
        userId,
        ipAddress: 'mobile',
        userAgent: 'mobile_app',
        resource: 'mobile_device',
        action: 'deactivate',
        details: { deviceId },
        success: true
      });

      logger.info('移动设备已停用', { userId, deviceId });

    } catch (error) {
      logger.error('停用移动设备失败', {
        error: error instanceof Error ? error.message : String(error),
        userId,
        deviceId
      });
      throw error;
    }
  }

  // 私有辅助方法

  private generateId(): string {
    return `mob_${Date.now()}_${crypto.randomBytes(8).toString('hex')}`;
  }

  private generateDeviceId(): string {
    return crypto.randomBytes(16).toString('hex');
  }

  private generateChallenge(): string {
    return crypto.randomBytes(32).toString('base64');
  }

  private calculateInitialTrustScore(deviceInfo: Partial<MobileDeviceInfo>): number {
    let score = 50; // 基础分数

    // 安全硬件加分
    if (deviceInfo.hasSecureHardware) score += 20;

    // 生物识别支持加分
    if (deviceInfo.supportedBiometrics && deviceInfo.supportedBiometrics.length > 0) {
      score += deviceInfo.supportedBiometrics.length * 5;
    }

    // 越狱/Root检测扣分
    if (deviceInfo.isJailbroken || deviceInfo.isRooted) score -= 30;

    // 设备证明加分
    if (deviceInfo.attestationData) score += 15;

    return Math.max(0, Math.min(100, score));
  }

  private async verifyBiometric(
    session: MobileAuthSession,
    response: string,
    additionalData?: Record<string, any>
  ): Promise<boolean> {
    // 简化的生物识别验证
    // 实际实现需要与设备端的生物识别SDK集成
    const biometricType = additionalData?.biometricType as BiometricType;
    if (!biometricType) return false;

    const template = await this.getBiometricTemplate(session.userId, session.deviceId, biometricType);
    if (!template || !template.isActive) return false;

    // 这里应该进行实际的生物识别匹配
    // 简化实现：检查响应是否包含正确的模板哈希
    const responseHash = crypto.createHash('sha256').update(response).digest('hex');
    const isMatch = responseHash === template.templateHash;

    if (isMatch) {
      // 更新使用统计
      template.lastUsed = new Date();
      template.useCount++;
      await this.updateBiometricTemplate(template);
    }

    return isMatch;
  }

  private async verifyCredential(session: MobileAuthSession, response: string): Promise<boolean> {
    // 验证PIN或密码
    const expectedHash = crypto.createHash('sha256').update(session.challenge + response).digest('hex');
    return session.metadata.expectedHash === expectedHash;
  }

  private async verifyPattern(session: MobileAuthSession, response: string): Promise<boolean> {
    // 验证图案密码
    return session.metadata.pattern === response;
  }

  private async verifyPushResponse(session: MobileAuthSession, response: string): Promise<boolean> {
    // 验证推送通知响应
    return response === 'approved';
  }

  private async verifyQRCode(session: MobileAuthSession, response: string): Promise<boolean> {
    // 验证二维码
    return response === session.challenge;
  }

  private async sendAuthPushNotification(session: MobileAuthSession): Promise<void> {
    await this.sendPushNotification(session.userId, session.deviceId, {
      title: '身份验证请求',
      body: '请确认您的登录请求',
      data: {
        sessionId: session.id,
        authMethod: session.authMethod,
        challenge: session.challenge
      },
      priority: 'high',
      ttl: 300
    });
  }

  private async sendPushToDevice(
    device: MobileDeviceInfo,
    notification: PushNotification
  ): Promise<{ success: boolean; details?: any }> {
    // 这里需要集成实际的推送服务 (FCM, APNs等)
    // 简化实现
    try {
      // 模拟推送发送
      await new Promise(resolve => setTimeout(resolve, 100));
      return { success: true };
    } catch (error) {
      return { success: false, details: { error: error.message } };
    }
  }

  private async updateDeviceTrustScore(deviceId: string, delta: number): Promise<void> {
    const device = await this.getDeviceByDeviceId(deviceId);
    if (device) {
      device.trustScore = Math.max(0, Math.min(100, device.trustScore + delta));
      await this.updateDevice(device.id, { trustScore: device.trustScore });
    }
  }

  // 数据库操作方法（简化实现）
  private async getDeviceByDeviceId(deviceId: string): Promise<MobileDeviceInfo | null> {
    const cached = this.deviceCache.get(deviceId);
    if (cached) return cached;

    try {
      const device = await prisma.mobileDevice.findUnique({
        where: { deviceId }
      });
      return device ? this.mapPrismaToDevice(device) : null;
    } catch (error) {
      return null;
    }
  }

  private async saveDevice(device: MobileDeviceInfo): Promise<void> {
    // 简化的数据库保存实现
    logger.debug('保存移动设备', { deviceId: device.deviceId });
  }

  private async updateDevice(id: string, updates: Partial<MobileDeviceInfo>): Promise<MobileDeviceInfo> {
    // 简化的设备更新实现
    const device = await this.getDeviceByDeviceId(id);
    if (device) {
      Object.assign(device, updates);
      this.deviceCache.set(device.deviceId, device);
    }
    return device!;
  }

  private async getBiometricTemplate(
    userId: string,
    deviceId: string,
    biometricType: BiometricType
  ): Promise<BiometricTemplate | null> {
    // 简化的生物识别模板查询
    return null;
  }

  private async saveBiometricTemplate(template: BiometricTemplate): Promise<void> {
    // 简化的生物识别模板保存
    logger.debug('保存生物识别模板', { templateId: template.id });
  }

  private async updateBiometricTemplate(template: BiometricTemplate): Promise<void> {
    // 简化的生物识别模板更新
    logger.debug('更新生物识别模板', { templateId: template.id });
  }

  private async deactivateBiometricTemplates(deviceId: string): Promise<void> {
    // 简化的生物识别模板停用
    logger.debug('停用生物识别模板', { deviceId });
  }

  private async savePushNotification(notification: PushNotification): Promise<void> {
    // 简化的推送通知保存
    logger.debug('保存推送通知', { notificationId: notification.id });
  }

  private mapPrismaToDevice(prismaDevice: any): MobileDeviceInfo {
    return {
      id: prismaDevice.id,
      userId: prismaDevice.userId,
      deviceType: prismaDevice.deviceType,
      deviceId: prismaDevice.deviceId,
      deviceName: prismaDevice.deviceName,
      osVersion: prismaDevice.osVersion,
      appVersion: prismaDevice.appVersion,
      manufacturer: prismaDevice.manufacturer,
      model: prismaDevice.model,
      screenResolution: prismaDevice.screenResolution,
      isJailbroken: prismaDevice.isJailbroken,
      isRooted: prismaDevice.isRooted,
      hasSecureHardware: prismaDevice.hasSecureHardware,
      supportedBiometrics: prismaDevice.supportedBiometrics,
      pushToken: prismaDevice.pushToken,
      publicKey: prismaDevice.publicKey,
      attestationData: prismaDevice.attestationData,
      trustScore: prismaDevice.trustScore,
      isActive: prismaDevice.isActive,
      lastSeen: prismaDevice.lastSeen,
      registeredAt: prismaDevice.registeredAt,
      metadata: prismaDevice.metadata
    };
  }
}

// 创建单例实例
export const mobileAuthService = new MobileAuthService();
