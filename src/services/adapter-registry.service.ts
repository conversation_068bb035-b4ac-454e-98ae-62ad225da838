/**
 * 协议适配器注册表服务
 * 管理所有可用的协议适配器，提供动态加载和配置功能
 */

import { BaseProtocolAdapter } from '@/adapters/base-protocol.adapter';
import { CustomOAuthAdapter } from '@/adapters/custom-oauth.adapter';
import { LegacySystemAdapter } from '@/adapters/legacy-system.adapter';
import { WebhookAdapter } from '@/adapters/webhook.adapter';
import { ApiGatewayAdapter } from '@/adapters/api-gateway.adapter';
import { ProtocolConfig, ProtocolAdapterError } from '@/types/protocol-adapter';
import { logger } from '@/config/logger';
import { prisma } from '@/config/database';
import path from 'path';
import fs from 'fs';

/**
 * 适配器元数据接口
 */
export interface AdapterMetadata {
  name: string;
  version: string;
  description: string;
  author: string;
  supportedMethods: string[];
  configSchema?: Record<string, any>;
  dependencies?: string[];
  tags?: string[];
  category: 'oauth' | 'saml' | 'legacy' | 'api-gateway' | 'webhook' | 'custom';
  status: 'active' | 'deprecated' | 'experimental';
}

/**
 * 适配器实例信息
 */
export interface AdapterInstance {
  id: string;
  name: string;
  adapter: BaseProtocolAdapter;
  config: ProtocolConfig;
  metadata: AdapterMetadata;
  isActive: boolean;
  createdAt: Date;
  lastUsed?: Date;
  usageCount: number;
}

/**
 * 适配器注册表服务
 */
export class AdapterRegistryService {
  private static instance: AdapterRegistryService;
  private adapters = new Map<string, typeof BaseProtocolAdapter>();
  private instances = new Map<string, AdapterInstance>();
  private metadata = new Map<string, AdapterMetadata>();

  private constructor() {
    this.registerBuiltinAdapters();
  }

  /**
   * 获取单例实例
   */
  public static getInstance(): AdapterRegistryService {
    if (!AdapterRegistryService.instance) {
      AdapterRegistryService.instance = new AdapterRegistryService();
    }
    return AdapterRegistryService.instance;
  }

  /**
   * 注册内置适配器
   */
  private registerBuiltinAdapters(): void {
    // 注册自定义OAuth适配器
    this.registerAdapter('custom-oauth', CustomOAuthAdapter, {
      name: 'custom-oauth',
      version: '2.0.0',
      description: '自定义OAuth 2.0协议适配器，支持非标准OAuth实现',
      author: 'Identity Provider Team',
      supportedMethods: ['authorization_code', 'client_credentials', 'password', 'refresh_token', 'custom_grant'],
      category: 'oauth',
      status: 'active',
      tags: ['oauth', 'custom', 'flexible']
    });

    // 注册遗留系统适配器
    this.registerAdapter('legacy-system', LegacySystemAdapter, {
      name: 'legacy-system',
      version: '1.0.0',
      description: '遗留系统协议适配器，支持传统企业系统集成',
      author: 'Identity Provider Team',
      supportedMethods: ['form_based', 'basic_auth', 'digest_auth', 'custom_header', 'session_cookie', 'api_key', 'ldap_bind'],
      category: 'legacy',
      status: 'active',
      tags: ['legacy', 'enterprise', 'ldap', 'forms']
    });

    // 注册Webhook适配器
    this.registerAdapter('webhook', WebhookAdapter, {
      name: 'webhook',
      version: '1.0.0',
      description: 'Webhook协议适配器，支持基于Webhook的认证和事件通知',
      author: 'Identity Provider Team',
      supportedMethods: ['webhook_auth', 'webhook_event'],
      category: 'webhook',
      status: 'active',
      tags: ['webhook', 'events', 'notifications']
    });

    // 注册API网关适配器
    this.registerAdapter('api-gateway', ApiGatewayAdapter, {
      name: 'api-gateway',
      version: '1.0.0',
      description: 'API网关协议适配器，支持与各种API网关的集成',
      author: 'Identity Provider Team',
      supportedMethods: ['jwt_validation', 'api_key_auth', 'oauth_introspection', 'custom_header_auth', 'mutual_tls_auth'],
      category: 'api-gateway',
      status: 'active',
      tags: ['api-gateway', 'kong', 'zuul', 'istio', 'nginx']
    });

    logger.info('内置协议适配器注册完成', {
      count: this.adapters.size,
      adapters: Array.from(this.adapters.keys())
    });
  }

  /**
   * 注册协议适配器
   */
  public registerAdapter(
    name: string,
    adapterClass: typeof BaseProtocolAdapter,
    metadata: AdapterMetadata
  ): void {
    if (this.adapters.has(name)) {
      logger.warn('协议适配器已存在，将被覆盖', { name });
    }

    this.adapters.set(name, adapterClass);
    this.metadata.set(name, metadata);

    logger.info('协议适配器注册成功', { name, version: metadata.version });
  }

  /**
   * 注销协议适配器
   */
  public unregisterAdapter(name: string): boolean {
    if (!this.adapters.has(name)) {
      return false;
    }

    // 停止所有该适配器的实例
    const instancesToStop = Array.from(this.instances.values())
      .filter(instance => instance.name === name);

    for (const instance of instancesToStop) {
      this.stopAdapterInstance(instance.id);
    }

    this.adapters.delete(name);
    this.metadata.delete(name);

    logger.info('协议适配器注销成功', { name });
    return true;
  }

  /**
   * 获取所有已注册的适配器
   */
  public getRegisteredAdapters(): AdapterMetadata[] {
    return Array.from(this.metadata.values());
  }

  /**
   * 获取适配器元数据
   */
  public getAdapterMetadata(name: string): AdapterMetadata | undefined {
    return this.metadata.get(name);
  }

  /**
   * 检查适配器是否已注册
   */
  public isAdapterRegistered(name: string): boolean {
    return this.adapters.has(name);
  }

  /**
   * 创建适配器实例
   */
  public async createAdapterInstance(
    name: string,
    config: ProtocolConfig,
    instanceId?: string
  ): Promise<string> {
    const AdapterClass = this.adapters.get(name);
    if (!AdapterClass) {
      throw new ProtocolAdapterError('ADAPTER_NOT_FOUND', `适配器未找到: ${name}`);
    }

    const metadata = this.metadata.get(name)!;
    const id = instanceId || this.generateInstanceId(name);

    try {
      // 创建适配器实例
      const adapter = new AdapterClass(config);
      await adapter.initialize();

      // 创建实例记录
      const instance: AdapterInstance = {
        id,
        name,
        adapter,
        config,
        metadata,
        isActive: true,
        createdAt: new Date(),
        usageCount: 0
      };

      this.instances.set(id, instance);

      // 保存到数据库
      await this.saveInstanceToDatabase(instance);

      logger.info('适配器实例创建成功', { name, instanceId: id });
      return id;

    } catch (error) {
      logger.error('适配器实例创建失败', { name, error: error.message });
      throw new ProtocolAdapterError('INSTANCE_CREATION_FAILED', error.message);
    }
  }

  /**
   * 获取适配器实例
   */
  public getAdapterInstance(instanceId: string): AdapterInstance | undefined {
    return this.instances.get(instanceId);
  }

  /**
   * 获取所有适配器实例
   */
  public getAllAdapterInstances(): AdapterInstance[] {
    return Array.from(this.instances.values());
  }

  /**
   * 获取指定适配器的所有实例
   */
  public getAdapterInstancesByName(name: string): AdapterInstance[] {
    return Array.from(this.instances.values())
      .filter(instance => instance.name === name);
  }

  /**
   * 停止适配器实例
   */
  public async stopAdapterInstance(instanceId: string): Promise<boolean> {
    const instance = this.instances.get(instanceId);
    if (!instance) {
      return false;
    }

    try {
      // 停止适配器
      if (instance.adapter && typeof instance.adapter.destroy === 'function') {
        await instance.adapter.destroy();
      }

      // 标记为非活跃
      instance.isActive = false;

      // 从内存中移除
      this.instances.delete(instanceId);

      // 更新数据库
      await this.updateInstanceInDatabase(instance);

      logger.info('适配器实例停止成功', { instanceId });
      return true;

    } catch (error) {
      logger.error('适配器实例停止失败', { instanceId, error: error.message });
      return false;
    }
  }

  /**
   * 重启适配器实例
   */
  public async restartAdapterInstance(instanceId: string): Promise<boolean> {
    const instance = this.instances.get(instanceId);
    if (!instance) {
      return false;
    }

    try {
      // 停止当前实例
      await this.stopAdapterInstance(instanceId);

      // 重新创建实例
      await this.createAdapterInstance(instance.name, instance.config, instanceId);

      logger.info('适配器实例重启成功', { instanceId });
      return true;

    } catch (error) {
      logger.error('适配器实例重启失败', { instanceId, error: error.message });
      return false;
    }
  }

  /**
   * 更新适配器实例配置
   */
  public async updateAdapterInstanceConfig(
    instanceId: string,
    newConfig: ProtocolConfig
  ): Promise<boolean> {
    const instance = this.instances.get(instanceId);
    if (!instance) {
      return false;
    }

    try {
      // 验证新配置
      await instance.adapter.validateConfig(newConfig);

      // 更新配置
      instance.config = newConfig;
      await instance.adapter.updateConfig(newConfig);

      // 更新数据库
      await this.updateInstanceInDatabase(instance);

      logger.info('适配器实例配置更新成功', { instanceId });
      return true;

    } catch (error) {
      logger.error('适配器实例配置更新失败', { instanceId, error: error.message });
      return false;
    }
  }

  /**
   * 记录适配器使用情况
   */
  public recordAdapterUsage(instanceId: string): void {
    const instance = this.instances.get(instanceId);
    if (instance) {
      instance.usageCount++;
      instance.lastUsed = new Date();
    }
  }

  /**
   * 获取适配器使用统计
   */
  public getAdapterUsageStats(): Record<string, any> {
    const stats: Record<string, any> = {};

    for (const instance of this.instances.values()) {
      if (!stats[instance.name]) {
        stats[instance.name] = {
          instanceCount: 0,
          totalUsage: 0,
          activeInstances: 0
        };
      }

      stats[instance.name].instanceCount++;
      stats[instance.name].totalUsage += instance.usageCount;
      
      if (instance.isActive) {
        stats[instance.name].activeInstances++;
      }
    }

    return stats;
  }

  /**
   * 动态加载外部适配器
   */
  public async loadExternalAdapter(adapterPath: string): Promise<string> {
    try {
      // 验证适配器文件
      if (!fs.existsSync(adapterPath)) {
        throw new Error('适配器文件不存在');
      }

      // 动态导入适配器
      const adapterModule = await import(adapterPath);
      const AdapterClass = adapterModule.default || adapterModule;

      if (!AdapterClass.prototype || !(AdapterClass.prototype instanceof BaseProtocolAdapter)) {
        throw new Error('无效的适配器类');
      }

      // 获取适配器元数据
      const tempInstance = new AdapterClass({});
      const metadata: AdapterMetadata = {
        name: tempInstance.name,
        version: tempInstance.version,
        description: '外部加载的适配器',
        author: 'External',
        supportedMethods: tempInstance.supportedMethods,
        category: 'custom',
        status: 'experimental'
      };

      // 注册适配器
      this.registerAdapter(tempInstance.name, AdapterClass, metadata);

      logger.info('外部适配器加载成功', { 
        name: tempInstance.name, 
        path: adapterPath 
      });

      return tempInstance.name;

    } catch (error) {
      logger.error('外部适配器加载失败', { path: adapterPath, error: error.message });
      throw new ProtocolAdapterError('EXTERNAL_ADAPTER_LOAD_FAILED', error.message);
    }
  }

  /**
   * 生成实例ID
   */
  private generateInstanceId(adapterName: string): string {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 8);
    return `${adapterName}-${timestamp}-${random}`;
  }

  /**
   * 保存实例到数据库
   */
  private async saveInstanceToDatabase(instance: AdapterInstance): Promise<void> {
    try {
      await prisma.protocolAdapter.create({
        data: {
          id: instance.id,
          name: instance.name,
          version: instance.metadata.version,
          config: instance.config as any,
          isActive: instance.isActive,
          usageCount: instance.usageCount,
          createdAt: instance.createdAt,
          lastUsed: instance.lastUsed
        }
      });
    } catch (error) {
      logger.error('保存适配器实例到数据库失败', { 
        instanceId: instance.id, 
        error: error.message 
      });
    }
  }

  /**
   * 更新数据库中的实例
   */
  private async updateInstanceInDatabase(instance: AdapterInstance): Promise<void> {
    try {
      await prisma.protocolAdapter.update({
        where: { id: instance.id },
        data: {
          config: instance.config as any,
          isActive: instance.isActive,
          usageCount: instance.usageCount,
          lastUsed: instance.lastUsed
        }
      });
    } catch (error) {
      logger.error('更新数据库中的适配器实例失败', { 
        instanceId: instance.id, 
        error: error.message 
      });
    }
  }

  /**
   * 从数据库加载实例
   */
  public async loadInstancesFromDatabase(): Promise<void> {
    try {
      const dbInstances = await prisma.protocolAdapter.findMany({
        where: { isActive: true }
      });

      for (const dbInstance of dbInstances) {
        try {
          await this.createAdapterInstance(
            dbInstance.name,
            dbInstance.config as ProtocolConfig,
            dbInstance.id
          );
        } catch (error) {
          logger.error('从数据库加载适配器实例失败', {
            instanceId: dbInstance.id,
            error: error.message
          });
        }
      }

      logger.info('从数据库加载适配器实例完成', { 
        count: dbInstances.length 
      });

    } catch (error) {
      logger.error('从数据库加载适配器实例失败', { error: error.message });
    }
  }

  /**
   * 清理非活跃实例
   */
  public async cleanupInactiveInstances(): Promise<void> {
    const cutoffTime = new Date(Date.now() - 24 * 60 * 60 * 1000); // 24小时前

    for (const [instanceId, instance] of this.instances.entries()) {
      if (!instance.isActive || (instance.lastUsed && instance.lastUsed < cutoffTime)) {
        await this.stopAdapterInstance(instanceId);
      }
    }
  }
}

// 导出单例实例
export const adapterRegistry = AdapterRegistryService.getInstance();
