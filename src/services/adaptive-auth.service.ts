/**
 * 自适应认证服务
 * 基于风险评估结果动态调整认证要求，实现零信任架构的核心认证逻辑
 */

import { Request } from 'express';
import { logger } from '@/config/logger';
import { riskAssessmentService, RiskLevel } from '@/services/risk-assessment.service';
import { deviceFingerprintService } from '@/services/device-fingerprint.service';
import { securityAuditService, AuditEventType, AuditSeverity } from '@/services/security-audit.service';
import { metricsCollector } from '@/services/metrics-collector.service';
import { cacheService } from '@/services/cache.service';

/**
 * 认证要求级别
 */
export enum AuthRequirementLevel {
  NONE = 'none',
  BASIC = 'basic',
  ENHANCED = 'enhanced',
  STRICT = 'strict',
  MAXIMUM = 'maximum'
}

/**
 * 认证方法类型
 */
export enum AuthMethodType {
  PASSWORD = 'password',
  MFA_TOTP = 'mfa_totp',
  MFA_SMS = 'mfa_sms',
  MFA_EMAIL = 'mfa_email',
  MFA_PUSH = 'mfa_push',
  BIOMETRIC = 'biometric',
  HARDWARE_TOKEN = 'hardware_token',
  DEVICE_VERIFICATION = 'device_verification',
  LOCATION_VERIFICATION = 'location_verification',
  BEHAVIORAL_VERIFICATION = 'behavioral_verification'
}

/**
 * 认证决策结果
 */
interface AuthDecision {
  decision: 'allow' | 'challenge' | 'deny';
  requirementLevel: AuthRequirementLevel;
  requiredMethods: AuthMethodType[];
  optionalMethods: AuthMethodType[];
  sessionDuration: number; // 秒
  restrictions: string[];
  reason: string;
  confidence: number;
  riskScore: number;
  timestamp: Date;
  expiresAt: Date;
}

/**
 * 认证上下文
 */
interface AuthContext {
  userId?: string;
  sessionId?: string;
  ipAddress: string;
  userAgent: string;
  deviceFingerprint?: string;
  location?: {
    country: string;
    region: string;
    city: string;
  };
  previousAuth?: {
    timestamp: Date;
    methods: AuthMethodType[];
    success: boolean;
  };
  requestedResource?: string;
  requestedAction?: string;
}

/**
 * 认证策略配置
 */
interface AuthPolicy {
  name: string;
  description: string;
  conditions: PolicyCondition[];
  requirements: AuthRequirement[];
  enabled: boolean;
  priority: number;
}

/**
 * 策略条件
 */
interface PolicyCondition {
  type: 'risk_score' | 'user_role' | 'resource_sensitivity' | 'time_of_day' | 'location' | 'device_trust';
  operator: 'gt' | 'gte' | 'lt' | 'lte' | 'eq' | 'ne' | 'in' | 'not_in';
  value: any;
}

/**
 * 认证要求
 */
interface AuthRequirement {
  level: AuthRequirementLevel;
  methods: AuthMethodType[];
  sessionDuration: number;
  restrictions: string[];
}

/**
 * 自适应认证服务
 */
export class AdaptiveAuthService {
  private authPolicies: AuthPolicy[] = [];
  private decisionCache = new Map<string, AuthDecision>();

  constructor() {
    this.initializeDefaultPolicies();
  }

  /**
   * 做出认证决策
   */
  async makeAuthDecision(req: Request, context: AuthContext): Promise<AuthDecision> {
    const startTime = Date.now();

    try {
      // 生成决策缓存键
      const cacheKey = this.generateDecisionCacheKey(context);
      
      // 检查缓存
      const cachedDecision = this.decisionCache.get(cacheKey);
      if (cachedDecision && !this.isDecisionExpired(cachedDecision)) {
        return cachedDecision;
      }

      // 执行风险评估
      const riskAssessment = await riskAssessmentService.assessRisk(req, context.userId, {
        requestedResource: context.requestedResource,
        requestedAction: context.requestedAction
      });

      // 获取设备信任评估
      let deviceTrust = null;
      if (context.deviceFingerprint) {
        const device = await deviceFingerprintService.identifyDevice(req);
        if (device) {
          deviceTrust = await deviceFingerprintService.assessDeviceTrust(device, context.userId);
        }
      }

      // 应用认证策略
      const applicablePolicies = this.findApplicablePolicies(riskAssessment, deviceTrust, context);
      const authRequirement = this.determineAuthRequirement(applicablePolicies, riskAssessment.riskLevel);

      // 生成认证决策
      const decision = this.generateAuthDecision(
        riskAssessment,
        deviceTrust,
        authRequirement,
        context
      );

      // 缓存决策
      this.decisionCache.set(cacheKey, decision);
      setTimeout(() => this.decisionCache.delete(cacheKey), decision.sessionDuration * 1000);

      // 记录审计事件
      await this.logAuthDecision(decision, context, riskAssessment);

      // 记录指标
      const duration = Date.now() - startTime;
      metricsCollector.recordHistogram('adaptive_auth_decision_duration', duration);
      metricsCollector.incrementCounter('adaptive_auth_decisions_total', {
        decision: decision.decision,
        requirement_level: decision.requirementLevel,
        risk_level: riskAssessment.riskLevel
      });

      logger.info('自适应认证决策完成', {
        userId: context.userId,
        decision: decision.decision,
        requirementLevel: decision.requirementLevel,
        riskScore: decision.riskScore,
        duration
      });

      return decision;

    } catch (error) {
      logger.error('自适应认证决策失败', {
        error: error instanceof Error ? error.message : String(error),
        userId: context.userId
      });

      // 返回安全默认决策
      return this.createFailsafeDecision(context);
    }
  }

  /**
   * 验证认证方法
   */
  async verifyAuthMethod(
    userId: string,
    method: AuthMethodType,
    credential: string,
    context: AuthContext
  ): Promise<{ success: boolean; details?: any }> {
    try {
      let verificationResult = { success: false, details: {} };

      switch (method) {
        case AuthMethodType.PASSWORD:
          verificationResult = await this.verifyPassword(userId, credential);
          break;
        case AuthMethodType.MFA_TOTP:
          verificationResult = await this.verifyTOTP(userId, credential);
          break;
        case AuthMethodType.MFA_SMS:
          verificationResult = await this.verifySMS(userId, credential);
          break;
        case AuthMethodType.MFA_EMAIL:
          verificationResult = await this.verifyEmail(userId, credential);
          break;
        case AuthMethodType.MFA_PUSH:
          verificationResult = await this.verifyPush(userId, credential);
          break;
        case AuthMethodType.DEVICE_VERIFICATION:
          verificationResult = await this.verifyDevice(userId, credential, context);
          break;
        case AuthMethodType.LOCATION_VERIFICATION:
          verificationResult = await this.verifyLocation(userId, context);
          break;
        case AuthMethodType.BEHAVIORAL_VERIFICATION:
          verificationResult = await this.verifyBehavior(userId, context);
          break;
        default:
          throw new Error(`不支持的认证方法: ${method}`);
      }

      // 记录验证结果
      await securityAuditService.logAuditEvent({
        eventType: verificationResult.success ? AuditEventType.LOGIN_SUCCESS : AuditEventType.LOGIN_FAILED,
        severity: verificationResult.success ? AuditSeverity.LOW : AuditSeverity.MEDIUM,
        userId,
        sessionId: context.sessionId,
        ipAddress: context.ipAddress,
        userAgent: context.userAgent,
        resource: 'authentication',
        action: `verify_${method}`,
        details: {
          method,
          ...verificationResult.details
        },
        success: verificationResult.success
      });

      return verificationResult;

    } catch (error) {
      logger.error('认证方法验证失败', {
        error: error instanceof Error ? error.message : String(error),
        userId,
        method
      });

      return { success: false, details: { error: error.message } };
    }
  }

  /**
   * 更新认证策略
   */
  updateAuthPolicy(policy: AuthPolicy): void {
    const existingIndex = this.authPolicies.findIndex(p => p.name === policy.name);
    
    if (existingIndex >= 0) {
      this.authPolicies[existingIndex] = policy;
    } else {
      this.authPolicies.push(policy);
    }

    // 按优先级排序
    this.authPolicies.sort((a, b) => b.priority - a.priority);

    logger.info('认证策略已更新', {
      policyName: policy.name,
      enabled: policy.enabled,
      priority: policy.priority
    });
  }

  /**
   * 获取用户的认证历史
   */
  async getAuthHistory(userId: string, limit: number = 10): Promise<any[]> {
    try {
      const history = await securityAuditService.getAuditEvents({
        userId,
        eventTypes: [AuditEventType.LOGIN_SUCCESS, AuditEventType.LOGIN_FAILED],
        limit
      });

      return history.map(event => ({
        timestamp: event.timestamp,
        success: event.success,
        method: event.details?.method,
        ipAddress: event.ipAddress,
        userAgent: event.userAgent,
        riskScore: event.riskScore
      }));

    } catch (error) {
      logger.error('获取认证历史失败', {
        error: error instanceof Error ? error.message : String(error),
        userId
      });
      return [];
    }
  }

  /**
   * 初始化默认策略
   */
  private initializeDefaultPolicies(): void {
    // 高风险策略
    this.authPolicies.push({
      name: 'high_risk_policy',
      description: '高风险情况下的严格认证要求',
      conditions: [
        { type: 'risk_score', operator: 'gte', value: 70 }
      ],
      requirements: [{
        level: AuthRequirementLevel.MAXIMUM,
        methods: [AuthMethodType.PASSWORD, AuthMethodType.MFA_TOTP, AuthMethodType.DEVICE_VERIFICATION],
        sessionDuration: 900, // 15分钟
        restrictions: ['no_sensitive_operations', 'enhanced_monitoring']
      }],
      enabled: true,
      priority: 100
    });

    // 中等风险策略
    this.authPolicies.push({
      name: 'medium_risk_policy',
      description: '中等风险情况下的增强认证要求',
      conditions: [
        { type: 'risk_score', operator: 'gte', value: 40 },
        { type: 'risk_score', operator: 'lt', value: 70 }
      ],
      requirements: [{
        level: AuthRequirementLevel.ENHANCED,
        methods: [AuthMethodType.PASSWORD, AuthMethodType.MFA_TOTP],
        sessionDuration: 3600, // 1小时
        restrictions: ['periodic_reauth']
      }],
      enabled: true,
      priority: 80
    });

    // 新设备策略
    this.authPolicies.push({
      name: 'new_device_policy',
      description: '新设备的额外验证要求',
      conditions: [
        { type: 'device_trust', operator: 'lt', value: 50 }
      ],
      requirements: [{
        level: AuthRequirementLevel.STRICT,
        methods: [AuthMethodType.PASSWORD, AuthMethodType.MFA_EMAIL, AuthMethodType.DEVICE_VERIFICATION],
        sessionDuration: 1800, // 30分钟
        restrictions: ['device_registration_required']
      }],
      enabled: true,
      priority: 90
    });

    // 默认策略
    this.authPolicies.push({
      name: 'default_policy',
      description: '默认认证要求',
      conditions: [],
      requirements: [{
        level: AuthRequirementLevel.BASIC,
        methods: [AuthMethodType.PASSWORD],
        sessionDuration: 7200, // 2小时
        restrictions: []
      }],
      enabled: true,
      priority: 10
    });
  }

  /**
   * 查找适用的策略
   */
  private findApplicablePolicies(riskAssessment: any, deviceTrust: any, context: AuthContext): AuthPolicy[] {
    const applicablePolicies: AuthPolicy[] = [];

    for (const policy of this.authPolicies) {
      if (!policy.enabled) continue;

      const isApplicable = policy.conditions.every(condition => 
        this.evaluateCondition(condition, riskAssessment, deviceTrust, context)
      );

      if (isApplicable) {
        applicablePolicies.push(policy);
      }
    }

    return applicablePolicies;
  }

  /**
   * 评估策略条件
   */
  private evaluateCondition(
    condition: PolicyCondition,
    riskAssessment: any,
    deviceTrust: any,
    context: AuthContext
  ): boolean {
    let actualValue: any;

    switch (condition.type) {
      case 'risk_score':
        actualValue = riskAssessment.overallRiskScore;
        break;
      case 'device_trust':
        actualValue = deviceTrust?.trustScore || 0;
        break;
      case 'user_role':
        actualValue = context.userId; // 简化实现
        break;
      case 'time_of_day':
        actualValue = new Date().getHours();
        break;
      case 'location':
        actualValue = context.location?.country;
        break;
      default:
        return false;
    }

    return this.compareValues(actualValue, condition.operator, condition.value);
  }

  /**
   * 比较值
   */
  private compareValues(actual: any, operator: string, expected: any): boolean {
    switch (operator) {
      case 'gt': return actual > expected;
      case 'gte': return actual >= expected;
      case 'lt': return actual < expected;
      case 'lte': return actual <= expected;
      case 'eq': return actual === expected;
      case 'ne': return actual !== expected;
      case 'in': return Array.isArray(expected) && expected.includes(actual);
      case 'not_in': return Array.isArray(expected) && !expected.includes(actual);
      default: return false;
    }
  }

  /**
   * 确定认证要求
   */
  private determineAuthRequirement(policies: AuthPolicy[], riskLevel: RiskLevel): AuthRequirement {
    if (policies.length === 0) {
      // 使用默认策略
      const defaultPolicy = this.authPolicies.find(p => p.name === 'default_policy');
      return defaultPolicy?.requirements[0] || {
        level: AuthRequirementLevel.BASIC,
        methods: [AuthMethodType.PASSWORD],
        sessionDuration: 3600,
        restrictions: []
      };
    }

    // 使用优先级最高的策略
    const highestPriorityPolicy = policies[0];
    return highestPriorityPolicy.requirements[0];
  }

  /**
   * 生成认证决策
   */
  private generateAuthDecision(
    riskAssessment: any,
    deviceTrust: any,
    requirement: AuthRequirement,
    context: AuthContext
  ): AuthDecision {
    let decision: 'allow' | 'challenge' | 'deny' = 'challenge';

    // 基于风险级别决定
    if (riskAssessment.riskLevel === RiskLevel.CRITICAL) {
      decision = 'deny';
    } else if (riskAssessment.riskLevel === RiskLevel.VERY_LOW && requirement.level === AuthRequirementLevel.BASIC) {
      decision = 'allow';
    }

    // 生成可选认证方法
    const optionalMethods: AuthMethodType[] = [];
    if (requirement.level !== AuthRequirementLevel.MAXIMUM) {
      optionalMethods.push(AuthMethodType.MFA_SMS, AuthMethodType.MFA_EMAIL);
    }

    return {
      decision,
      requirementLevel: requirement.level,
      requiredMethods: requirement.methods,
      optionalMethods,
      sessionDuration: requirement.sessionDuration,
      restrictions: requirement.restrictions,
      reason: this.generateDecisionReason(riskAssessment, deviceTrust, requirement),
      confidence: riskAssessment.confidence,
      riskScore: riskAssessment.overallRiskScore,
      timestamp: new Date(),
      expiresAt: new Date(Date.now() + requirement.sessionDuration * 1000)
    };
  }

  /**
   * 生成决策原因
   */
  private generateDecisionReason(riskAssessment: any, deviceTrust: any, requirement: AuthRequirement): string {
    const reasons: string[] = [];

    if (riskAssessment.riskLevel === RiskLevel.CRITICAL) {
      reasons.push('风险级别过高');
    } else if (riskAssessment.riskLevel === RiskLevel.VERY_HIGH) {
      reasons.push('风险级别很高');
    }

    if (deviceTrust && deviceTrust.trustScore < 50) {
      reasons.push('设备信任度低');
    }

    if (requirement.level === AuthRequirementLevel.MAXIMUM) {
      reasons.push('需要最高级别认证');
    }

    return reasons.length > 0 ? reasons.join(', ') : '标准认证流程';
  }

  /**
   * 记录认证决策
   */
  private async logAuthDecision(decision: AuthDecision, context: AuthContext, riskAssessment: any): Promise<void> {
    await securityAuditService.logAuditEvent({
      eventType: AuditEventType.PERMISSION_GRANTED,
      severity: decision.decision === 'deny' ? AuditSeverity.HIGH : AuditSeverity.LOW,
      userId: context.userId,
      sessionId: context.sessionId,
      ipAddress: context.ipAddress,
      userAgent: context.userAgent,
      resource: 'adaptive_auth',
      action: 'auth_decision',
      details: {
        decision: decision.decision,
        requirementLevel: decision.requirementLevel,
        requiredMethods: decision.requiredMethods,
        riskScore: decision.riskScore,
        reason: decision.reason
      },
      success: decision.decision !== 'deny'
    });
  }

  /**
   * 创建故障安全决策
   */
  private createFailsafeDecision(context: AuthContext): AuthDecision {
    return {
      decision: 'challenge',
      requirementLevel: AuthRequirementLevel.STRICT,
      requiredMethods: [AuthMethodType.PASSWORD, AuthMethodType.MFA_TOTP],
      optionalMethods: [],
      sessionDuration: 900, // 15分钟
      restrictions: ['enhanced_monitoring', 'manual_review_required'],
      reason: '系统错误，采用安全默认策略',
      confidence: 0.1,
      riskScore: 80,
      timestamp: new Date(),
      expiresAt: new Date(Date.now() + 900 * 1000)
    };
  }

  /**
   * 生成决策缓存键
   */
  private generateDecisionCacheKey(context: AuthContext): string {
    const data = `${context.userId || 'anon'}_${context.ipAddress}_${context.deviceFingerprint || 'unknown'}`;
    return Buffer.from(data).toString('base64');
  }

  /**
   * 检查决策是否过期
   */
  private isDecisionExpired(decision: AuthDecision): boolean {
    return new Date() > decision.expiresAt;
  }

  // 认证方法验证实现（简化）
  private async verifyPassword(userId: string, password: string): Promise<{ success: boolean; details: any }> {
    // 简化实现
    return { success: true, details: { method: 'password' } };
  }

  private async verifyTOTP(userId: string, code: string): Promise<{ success: boolean; details: any }> {
    // 简化实现
    return { success: true, details: { method: 'totp', code } };
  }

  private async verifySMS(userId: string, code: string): Promise<{ success: boolean; details: any }> {
    // 简化实现
    return { success: true, details: { method: 'sms', code } };
  }

  private async verifyEmail(userId: string, code: string): Promise<{ success: boolean; details: any }> {
    // 简化实现
    return { success: true, details: { method: 'email', code } };
  }

  private async verifyPush(userId: string, token: string): Promise<{ success: boolean; details: any }> {
    // 简化实现
    return { success: true, details: { method: 'push', token } };
  }

  private async verifyDevice(userId: string, deviceId: string, context: AuthContext): Promise<{ success: boolean; details: any }> {
    const success = await deviceFingerprintService.verifyDevice(deviceId, userId, 'manual');
    return { success, details: { method: 'device', deviceId } };
  }

  private async verifyLocation(userId: string, context: AuthContext): Promise<{ success: boolean; details: any }> {
    // 简化的地理位置验证
    return { success: true, details: { method: 'location', location: context.location } };
  }

  private async verifyBehavior(userId: string, context: AuthContext): Promise<{ success: boolean; details: any }> {
    // 简化的行为验证
    return { success: true, details: { method: 'behavior' } };
  }
}

// 创建单例实例
export const adaptiveAuthService = new AdaptiveAuthService();
