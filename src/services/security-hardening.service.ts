/**
 * 安全加固服务
 * 提供安全防护、威胁检测和安全策略执行功能
 */

import { Request, Response, NextFunction } from 'express'
import rateLimit from 'express-rate-limit'
import helmet from 'helmet'
import { logger } from '@/config/logger'
import { cacheService } from './cache.service'
import { metricsCollector } from './metrics-collector.service'

export interface SecurityConfig {
  enableRateLimit: boolean
  enableHelmet: boolean
  enableCSRF: boolean
  enableCORS: boolean
  maxLoginAttempts: number
  lockoutDuration: number
  passwordPolicy: {
    minLength: number
    requireUppercase: boolean
    requireLowercase: boolean
    requireNumbers: boolean
    requireSpecialChars: boolean
  }
  sessionSecurity: {
    httpOnly: boolean
    secure: boolean
    sameSite: 'strict' | 'lax' | 'none'
    maxAge: number
  }
}

export interface SecurityThreat {
  type: 'brute_force' | 'sql_injection' | 'xss' | 'csrf' | 'suspicious_activity'
  severity: 'low' | 'medium' | 'high' | 'critical'
  source: string
  details: any
  timestamp: Date
  blocked: boolean
}

export interface SecurityAudit {
  endpoint: string
  method: string
  ip: string
  userAgent: string
  userId?: string
  timestamp: Date
  risk_score: number
  threats: SecurityThreat[]
}

/**
 * 安全加固服务类
 */
class SecurityHardeningService {
  private config: SecurityConfig = {
    enableRateLimit: true,
    enableHelmet: true,
    enableCSRF: true,
    enableCORS: true,
    maxLoginAttempts: 5,
    lockoutDuration: 15 * 60 * 1000, // 15分钟
    passwordPolicy: {
      minLength: 8,
      requireUppercase: true,
      requireLowercase: true,
      requireNumbers: true,
      requireSpecialChars: true
    },
    sessionSecurity: {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 24 * 60 * 60 * 1000 // 24小时
    }
  }

  private threatHistory: SecurityThreat[] = []
  private blockedIPs = new Set<string>()
  private suspiciousActivities = new Map<string, number>()

  /**
   * 安全头部中间件
   */
  securityHeadersMiddleware() {
    if (!this.config.enableHelmet) {
      return (req: Request, res: Response, next: NextFunction) => next()
    }

    return helmet({
      contentSecurityPolicy: {
        directives: {
          defaultSrc: ["'self'"],
          styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
          fontSrc: ["'self'", "https://fonts.gstatic.com"],
          imgSrc: ["'self'", "data:", "https:"],
          scriptSrc: ["'self'"],
          connectSrc: ["'self'"],
          frameSrc: ["'none'"],
          objectSrc: ["'none'"],
          baseUri: ["'self'"]
        }
      },
      crossOriginEmbedderPolicy: false,
      hsts: {
        maxAge: 31536000,
        includeSubDomains: true,
        preload: true
      }
    })
  }

  /**
   * 速率限制中间件
   */
  rateLimitMiddleware(options: {
    windowMs?: number
    max?: number
    message?: string
    skipSuccessfulRequests?: boolean
  } = {}) {
    if (!this.config.enableRateLimit) {
      return (req: Request, res: Response, next: NextFunction) => next()
    }

    return rateLimit({
      windowMs: options.windowMs || 15 * 60 * 1000, // 15分钟
      max: options.max || 100, // 限制每个IP 15分钟内最多100个请求
      message: options.message || {
        error: 'Too Many Requests',
        message: '请求过于频繁，请稍后重试'
      },
      standardHeaders: true,
      legacyHeaders: false,
      skipSuccessfulRequests: options.skipSuccessfulRequests || false,
      handler: (req, res) => {
        this.recordThreat({
          type: 'brute_force',
          severity: 'medium',
          source: req.ip || 'unknown',
          details: {
            endpoint: req.path,
            method: req.method,
            userAgent: req.headers['user-agent']
          },
          timestamp: new Date(),
          blocked: true
        })

        metricsCollector.incrementCounter('security_rate_limit_triggered', {
          endpoint: req.path,
          method: req.method
        })

        res.status(429).json({
          error: 'Too Many Requests',
          message: '请求过于频繁，请稍后重试'
        })
      }
    })
  }

  /**
   * 登录保护中间件
   */
  loginProtectionMiddleware() {
    return async (req: Request, res: Response, next: NextFunction) => {
      const ip = req.ip || 'unknown'
      const lockoutKey = `login_lockout:${ip}`

      try {
        // 检查IP是否被锁定
        const lockoutData = await cacheService.getCached(lockoutKey)
        if (lockoutData) {
          this.recordThreat({
            type: 'brute_force',
            severity: 'high',
            source: ip,
            details: {
              endpoint: req.path,
              attempts: lockoutData.attempts,
              lockoutTime: lockoutData.lockoutTime
            },
            timestamp: new Date(),
            blocked: true
          })

          return res.status(423).json({
            error: 'Account Locked',
            message: `IP地址已被锁定，请${Math.ceil((lockoutData.lockoutTime - Date.now()) / 60000)}分钟后重试`
          })
        }

        // 拦截原始响应以监控登录失败
        const originalJson = res.json.bind(res)
        res.json = async (data: any) => {
          if (res.statusCode === 401 || res.statusCode === 403) {
            await this.handleLoginFailure(ip)
          } else if (res.statusCode === 200) {
            await this.handleLoginSuccess(ip)
          }
          return originalJson(data)
        }

        next()

      } catch (error) {
        logger.error('登录保护中间件错误', {
          service: 'security-hardening',
          ip,
          error: error instanceof Error ? error.message : String(error)
        })
        next()
      }
    }
  }

  /**
   * 威胁检测中间件
   */
  threatDetectionMiddleware() {
    return (req: Request, res: Response, next: NextFunction) => {
      const threats: SecurityThreat[] = []
      const ip = req.ip || 'unknown'

      // SQL注入检测
      if (this.detectSQLInjection(req)) {
        threats.push({
          type: 'sql_injection',
          severity: 'high',
          source: ip,
          details: {
            query: req.query,
            body: req.body,
            params: req.params
          },
          timestamp: new Date(),
          blocked: false
        })
      }

      // XSS检测
      if (this.detectXSS(req)) {
        threats.push({
          type: 'xss',
          severity: 'medium',
          source: ip,
          details: {
            query: req.query,
            body: req.body
          },
          timestamp: new Date(),
          blocked: false
        })
      }

      // 可疑活动检测
      if (this.detectSuspiciousActivity(req)) {
        threats.push({
          type: 'suspicious_activity',
          severity: 'low',
          source: ip,
          details: {
            userAgent: req.headers['user-agent'],
            path: req.path,
            method: req.method
          },
          timestamp: new Date(),
          blocked: false
        })
      }

      // 记录威胁
      threats.forEach(threat => {
        this.recordThreat(threat)
        metricsCollector.incrementCounter('security_threats_detected', {
          type: threat.type,
          severity: threat.severity
        })
      })

      // 如果检测到高危威胁，阻止请求
      const criticalThreats = threats.filter(t => t.severity === 'critical' || t.severity === 'high')
      if (criticalThreats.length > 0) {
        logger.warn('检测到高危安全威胁，阻止请求', {
          service: 'security-hardening',
          ip,
          threats: criticalThreats
        })

        return res.status(403).json({
          error: 'Security Threat Detected',
          message: '检测到安全威胁，请求被阻止'
        })
      }

      next()
    }
  }

  /**
   * 处理登录失败
   */
  private async handleLoginFailure(ip: string): Promise<void> {
    const attemptKey = `login_attempts:${ip}`
    const lockoutKey = `login_lockout:${ip}`

    try {
      let attempts = await cacheService.getCached(attemptKey) || 0
      attempts++

      if (attempts >= this.config.maxLoginAttempts) {
        // 锁定IP
        await cacheService.cache(lockoutKey, {
          attempts,
          lockoutTime: Date.now() + this.config.lockoutDuration
        }, this.config.lockoutDuration / 1000)

        // 清除尝试计数
        await cacheService.deleteCached(attemptKey)

        logger.warn('IP地址因多次登录失败被锁定', {
          service: 'security-hardening',
          ip,
          attempts
        })

        metricsCollector.incrementCounter('security_ip_lockouts', { ip })
      } else {
        // 记录失败尝试
        await cacheService.cache(attemptKey, attempts, 3600) // 1小时过期
      }

    } catch (error) {
      logger.error('处理登录失败错误', {
        service: 'security-hardening',
        ip,
        error: error instanceof Error ? error.message : String(error)
      })
    }
  }

  /**
   * 处理登录成功
   */
  private async handleLoginSuccess(ip: string): Promise<void> {
    const attemptKey = `login_attempts:${ip}`
    
    try {
      // 清除失败尝试记录
      await cacheService.deleteCached(attemptKey)
    } catch (error) {
      logger.error('处理登录成功错误', {
        service: 'security-hardening',
        ip,
        error: error instanceof Error ? error.message : String(error)
      })
    }
  }

  /**
   * SQL注入检测
   */
  private detectSQLInjection(req: Request): boolean {
    const sqlPatterns = [
      /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)/i,
      /(\b(OR|AND)\s+\d+\s*=\s*\d+)/i,
      /(\'|\"|;|--|\*|\|)/,
      /(\bSCRIPT\b)/i
    ]

    const checkValue = (value: any): boolean => {
      if (typeof value === 'string') {
        return sqlPatterns.some(pattern => pattern.test(value))
      }
      if (typeof value === 'object' && value !== null) {
        return Object.values(value).some(checkValue)
      }
      return false
    }

    return checkValue(req.query) || checkValue(req.body) || checkValue(req.params)
  }

  /**
   * XSS检测
   */
  private detectXSS(req: Request): boolean {
    const xssPatterns = [
      /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
      /<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi,
      /javascript:/gi,
      /on\w+\s*=/gi
    ]

    const checkValue = (value: any): boolean => {
      if (typeof value === 'string') {
        return xssPatterns.some(pattern => pattern.test(value))
      }
      if (typeof value === 'object' && value !== null) {
        return Object.values(value).some(checkValue)
      }
      return false
    }

    return checkValue(req.query) || checkValue(req.body)
  }

  /**
   * 可疑活动检测
   */
  private detectSuspiciousActivity(req: Request): boolean {
    const ip = req.ip || 'unknown'
    const userAgent = req.headers['user-agent'] || ''

    // 检测可疑User-Agent
    const suspiciousUAPatterns = [
      /bot|crawler|spider/i,
      /curl|wget|python|java/i,
      /scanner|exploit/i
    ]

    if (suspiciousUAPatterns.some(pattern => pattern.test(userAgent))) {
      return true
    }

    // 检测异常请求频率
    const activityKey = `activity:${ip}`
    const currentCount = this.suspiciousActivities.get(activityKey) || 0
    this.suspiciousActivities.set(activityKey, currentCount + 1)

    // 如果1分钟内请求超过50次，标记为可疑
    setTimeout(() => {
      this.suspiciousActivities.delete(activityKey)
    }, 60000)

    return currentCount > 50
  }

  /**
   * 记录威胁
   */
  private recordThreat(threat: SecurityThreat): void {
    this.threatHistory.push(threat)

    // 保持威胁历史大小限制
    if (this.threatHistory.length > 10000) {
      this.threatHistory.shift()
    }

    logger.warn('安全威胁检测', {
      service: 'security-hardening',
      threat
    })
  }

  /**
   * 验证密码强度
   */
  validatePasswordStrength(password: string): {
    isValid: boolean
    score: number
    issues: string[]
  } {
    const issues: string[] = []
    let score = 0

    if (password.length < this.config.passwordPolicy.minLength) {
      issues.push(`密码长度至少${this.config.passwordPolicy.minLength}位`)
    } else {
      score += 20
    }

    if (this.config.passwordPolicy.requireUppercase && !/[A-Z]/.test(password)) {
      issues.push('密码必须包含大写字母')
    } else {
      score += 20
    }

    if (this.config.passwordPolicy.requireLowercase && !/[a-z]/.test(password)) {
      issues.push('密码必须包含小写字母')
    } else {
      score += 20
    }

    if (this.config.passwordPolicy.requireNumbers && !/\d/.test(password)) {
      issues.push('密码必须包含数字')
    } else {
      score += 20
    }

    if (this.config.passwordPolicy.requireSpecialChars && !/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
      issues.push('密码必须包含特殊字符')
    } else {
      score += 20
    }

    return {
      isValid: issues.length === 0,
      score,
      issues
    }
  }

  /**
   * 获取安全统计
   */
  getSecurityStats(timeRange?: { start: Date; end: Date }) {
    let filteredThreats = this.threatHistory

    if (timeRange) {
      filteredThreats = this.threatHistory.filter(
        t => t.timestamp >= timeRange.start && t.timestamp <= timeRange.end
      )
    }

    const threatsByType = filteredThreats.reduce((acc, threat) => {
      acc[threat.type] = (acc[threat.type] || 0) + 1
      return acc
    }, {} as Record<string, number>)

    const threatsBySeverity = filteredThreats.reduce((acc, threat) => {
      acc[threat.severity] = (acc[threat.severity] || 0) + 1
      return acc
    }, {} as Record<string, number>)

    const blockedThreats = filteredThreats.filter(t => t.blocked).length

    return {
      totalThreats: filteredThreats.length,
      blockedThreats,
      threatsByType,
      threatsBySeverity,
      blockedIPs: this.blockedIPs.size,
      recentThreats: filteredThreats
        .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
        .slice(0, 10)
    }
  }

  /**
   * 更新安全配置
   */
  updateConfig(newConfig: Partial<SecurityConfig>): void {
    this.config = { ...this.config, ...newConfig }
    
    logger.info('安全配置已更新', {
      service: 'security-hardening',
      config: this.config
    })
  }

  /**
   * 获取当前配置
   */
  getConfig(): SecurityConfig {
    return { ...this.config }
  }
}

// 导出服务实例
export const securityHardeningService = new SecurityHardeningService()
export default securityHardeningService
