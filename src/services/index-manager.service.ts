/**
 * 数据库索引管理服务
 * 分析和管理数据库索引，提供索引优化建议
 */

import { prisma } from '@/config/database';
import { logger } from '@/config/logger';

/**
 * 索引类型枚举
 */
export enum IndexType {
  BTREE = 'BTREE',
  HASH = 'HASH',
  GIN = 'GIN',
  GIST = 'GIST',
  UNIQUE = 'UNIQUE',
  PARTIAL = 'PARTIAL',
  COMPOSITE = 'COMPOSITE'
}

/**
 * 索引信息接口
 */
export interface IndexInfo {
  name: string;
  tableName: string;
  columns: string[];
  type: IndexType;
  isUnique: boolean;
  isPartial: boolean;
  condition?: string;
  size: number;
  usage: IndexUsageStats;
  recommendation: IndexRecommendation;
}

/**
 * 索引使用统计
 */
export interface IndexUsageStats {
  scans: number;
  tuplesRead: number;
  tuplesReturned: number;
  efficiency: number; // 0-100
  lastUsed?: Date;
  createdAt: Date;
}

/**
 * 索引建议类型
 */
export enum IndexRecommendationType {
  CREATE = 'create',
  DROP = 'drop',
  MODIFY = 'modify',
  KEEP = 'keep'
}

/**
 * 索引建议
 */
export interface IndexRecommendation {
  type: IndexRecommendationType;
  priority: 'low' | 'medium' | 'high' | 'critical';
  reason: string;
  impact: string;
  sql?: string;
  estimatedImprovement?: number;
}

/**
 * 索引分析结果
 */
export interface IndexAnalysisResult {
  totalIndexes: number;
  unusedIndexes: IndexInfo[];
  duplicateIndexes: IndexInfo[][];
  missingIndexes: IndexRecommendation[];
  oversizedIndexes: IndexInfo[];
  recommendations: IndexRecommendation[];
  totalSize: number;
  potentialSavings: number;
}

/**
 * 数据库索引管理服务
 */
export class IndexManagerService {
  private readonly databaseType: string;
  
  constructor() {
    this.databaseType = this.detectDatabaseType();
  }

  /**
   * 检测数据库类型
   */
  private detectDatabaseType(): string {
    const databaseUrl = process.env.DATABASE_URL || '';
    if (databaseUrl.startsWith('postgresql://')) return 'postgresql';
    if (databaseUrl.startsWith('mysql://')) return 'mysql';
    if (databaseUrl.startsWith('sqlite://')) return 'sqlite';
    return 'unknown';
  }

  /**
   * 获取所有索引信息
   */
  async getAllIndexes(): Promise<IndexInfo[]> {
    try {
      let indexes: IndexInfo[] = [];
      
      switch (this.databaseType) {
        case 'postgresql':
          indexes = await this.getPostgreSQLIndexes();
          break;
        case 'mysql':
          indexes = await this.getMySQLIndexes();
          break;
        case 'sqlite':
          indexes = await this.getSQLiteIndexes();
          break;
        default:
          logger.warn('不支持的数据库类型', { type: this.databaseType });
      }

      logger.info('索引信息获取完成', { 
        count: indexes.length,
        databaseType: this.databaseType 
      });

      return indexes;

    } catch (error) {
      logger.error('获取索引信息失败', { error: error.message });
      throw error;
    }
  }

  /**
   * 获取PostgreSQL索引信息
   */
  private async getPostgreSQLIndexes(): Promise<IndexInfo[]> {
    const query = `
      SELECT 
        i.indexname as name,
        i.tablename as table_name,
        array_agg(a.attname ORDER BY a.attnum) as columns,
        i.indexdef as definition,
        ix.indisunique as is_unique,
        ix.indisprimary as is_primary,
        pg_relation_size(c.oid) as size_bytes,
        s.idx_scan as scans,
        s.idx_tup_read as tuples_read,
        s.idx_tup_fetch as tuples_returned
      FROM pg_indexes i
      JOIN pg_class c ON c.relname = i.indexname
      JOIN pg_index ix ON ix.indexrelid = c.oid
      JOIN pg_attribute a ON a.attrelid = ix.indrelid AND a.attnum = ANY(ix.indkey)
      LEFT JOIN pg_stat_user_indexes s ON s.indexrelname = i.indexname
      WHERE i.schemaname = 'public'
      GROUP BY i.indexname, i.tablename, i.indexdef, ix.indisunique, ix.indisprimary, c.oid, s.idx_scan, s.idx_tup_read, s.idx_tup_fetch
      ORDER BY i.tablename, i.indexname;
    `;

    const result = await prisma.$queryRawUnsafe(query);
    return this.parsePostgreSQLIndexes(result as any[]);
  }

  /**
   * 解析PostgreSQL索引结果
   */
  private parsePostgreSQLIndexes(rows: any[]): IndexInfo[] {
    return rows.map(row => ({
      name: row.name,
      tableName: row.table_name,
      columns: row.columns,
      type: this.determineIndexType(row.definition),
      isUnique: row.is_unique,
      isPartial: row.definition.includes('WHERE'),
      condition: this.extractWhereCondition(row.definition),
      size: parseInt(row.size_bytes) || 0,
      usage: {
        scans: parseInt(row.scans) || 0,
        tuplesRead: parseInt(row.tuples_read) || 0,
        tuplesReturned: parseInt(row.tuples_returned) || 0,
        efficiency: this.calculateEfficiency(row.scans, row.tuples_read, row.tuples_returned),
        createdAt: new Date() // 实际应该从系统表获取
      },
      recommendation: this.generateIndexRecommendation(row)
    }));
  }

  /**
   * 获取MySQL索引信息
   */
  private async getMySQLIndexes(): Promise<IndexInfo[]> {
    const query = `
      SELECT 
        s.INDEX_NAME as name,
        s.TABLE_NAME as table_name,
        GROUP_CONCAT(s.COLUMN_NAME ORDER BY s.SEQ_IN_INDEX) as columns,
        s.INDEX_TYPE as type,
        s.NON_UNIQUE = 0 as is_unique,
        COALESCE(st.CARDINALITY, 0) as cardinality
      FROM INFORMATION_SCHEMA.STATISTICS s
      LEFT JOIN INFORMATION_SCHEMA.STATISTICS st ON st.INDEX_NAME = s.INDEX_NAME AND st.TABLE_NAME = s.TABLE_NAME
      WHERE s.TABLE_SCHEMA = DATABASE()
      GROUP BY s.INDEX_NAME, s.TABLE_NAME, s.INDEX_TYPE, s.NON_UNIQUE
      ORDER BY s.TABLE_NAME, s.INDEX_NAME;
    `;

    const result = await prisma.$queryRawUnsafe(query);
    return this.parseMySQLIndexes(result as any[]);
  }

  /**
   * 解析MySQL索引结果
   */
  private parseMySQLIndexes(rows: any[]): IndexInfo[] {
    return rows.map(row => ({
      name: row.name,
      tableName: row.table_name,
      columns: row.columns.split(','),
      type: this.mapMySQLIndexType(row.type),
      isUnique: row.is_unique,
      isPartial: false, // MySQL不直接支持部分索引
      size: 0, // 需要额外查询获取
      usage: {
        scans: 0,
        tuplesRead: 0,
        tuplesReturned: 0,
        efficiency: 0,
        createdAt: new Date()
      },
      recommendation: this.generateBasicRecommendation()
    }));
  }

  /**
   * 获取SQLite索引信息
   */
  private async getSQLiteIndexes(): Promise<IndexInfo[]> {
    const query = `
      SELECT 
        name,
        tbl_name as table_name,
        sql as definition
      FROM sqlite_master 
      WHERE type = 'index' AND name NOT LIKE 'sqlite_%'
      ORDER BY tbl_name, name;
    `;

    const result = await prisma.$queryRawUnsafe(query);
    return this.parseSQLiteIndexes(result as any[]);
  }

  /**
   * 解析SQLite索引结果
   */
  private parseSQLiteIndexes(rows: any[]): IndexInfo[] {
    return rows.map(row => ({
      name: row.name,
      tableName: row.table_name,
      columns: this.extractColumnsFromSQL(row.definition),
      type: IndexType.BTREE, // SQLite主要使用B-tree
      isUnique: row.definition.includes('UNIQUE'),
      isPartial: row.definition.includes('WHERE'),
      condition: this.extractWhereCondition(row.definition),
      size: 0, // SQLite不直接提供索引大小
      usage: {
        scans: 0,
        tuplesRead: 0,
        tuplesReturned: 0,
        efficiency: 0,
        createdAt: new Date()
      },
      recommendation: this.generateBasicRecommendation()
    }));
  }

  /**
   * 分析索引使用情况
   */
  async analyzeIndexUsage(): Promise<IndexAnalysisResult> {
    try {
      const indexes = await this.getAllIndexes();
      
      // 分析未使用的索引
      const unusedIndexes = indexes.filter(idx => 
        idx.usage.scans === 0 && !idx.name.includes('pkey') && !idx.name.includes('PRIMARY')
      );

      // 分析重复索引
      const duplicateIndexes = this.findDuplicateIndexes(indexes);

      // 分析过大的索引
      const oversizedIndexes = indexes.filter(idx => 
        idx.size > 100 * 1024 * 1024 // 100MB
      );

      // 生成缺失索引建议
      const missingIndexes = await this.suggestMissingIndexes();

      // 生成综合建议
      const recommendations = this.generateComprehensiveRecommendations(
        indexes, unusedIndexes, duplicateIndexes, oversizedIndexes
      );

      const totalSize = indexes.reduce((sum, idx) => sum + idx.size, 0);
      const potentialSavings = unusedIndexes.reduce((sum, idx) => sum + idx.size, 0);

      const result: IndexAnalysisResult = {
        totalIndexes: indexes.length,
        unusedIndexes,
        duplicateIndexes,
        missingIndexes,
        oversizedIndexes,
        recommendations,
        totalSize,
        potentialSavings
      };

      logger.info('索引分析完成', {
        totalIndexes: result.totalIndexes,
        unusedCount: unusedIndexes.length,
        duplicateGroups: duplicateIndexes.length,
        oversizedCount: oversizedIndexes.length,
        totalSizeMB: Math.round(totalSize / 1024 / 1024),
        potentialSavingsMB: Math.round(potentialSavings / 1024 / 1024)
      });

      return result;

    } catch (error) {
      logger.error('索引分析失败', { error: error.message });
      throw error;
    }
  }

  /**
   * 查找重复索引
   */
  private findDuplicateIndexes(indexes: IndexInfo[]): IndexInfo[][] {
    const groups: Map<string, IndexInfo[]> = new Map();
    
    indexes.forEach(idx => {
      const key = `${idx.tableName}:${idx.columns.sort().join(',')}`;
      if (!groups.has(key)) {
        groups.set(key, []);
      }
      groups.get(key)!.push(idx);
    });

    return Array.from(groups.values()).filter(group => group.length > 1);
  }

  /**
   * 建议缺失的索引
   */
  private async suggestMissingIndexes(): Promise<IndexRecommendation[]> {
    const suggestions: IndexRecommendation[] = [];

    // 基于常见查询模式的索引建议
    const commonPatterns = [
      { table: 'users', columns: ['email'], reason: '用户邮箱查询频繁' },
      { table: 'sessions', columns: ['userId', 'isActive'], reason: '用户会话查询优化' },
      { table: 'audit_logs', columns: ['userId', 'createdAt'], reason: '审计日志查询优化' },
      { table: 'applications', columns: ['isActive', 'createdAt'], reason: '应用列表查询优化' },
      { table: 'risk_assessments', columns: ['userId', 'riskLevel'], reason: '风险评估查询优化' }
    ];

    for (const pattern of commonPatterns) {
      suggestions.push({
        type: IndexRecommendationType.CREATE,
        priority: 'medium',
        reason: pattern.reason,
        impact: '提高查询性能',
        sql: `CREATE INDEX idx_${pattern.table}_${pattern.columns.join('_')} ON ${pattern.table} (${pattern.columns.join(', ')});`,
        estimatedImprovement: 30
      });
    }

    return suggestions;
  }

  /**
   * 生成综合建议
   */
  private generateComprehensiveRecommendations(
    allIndexes: IndexInfo[],
    unusedIndexes: IndexInfo[],
    duplicateIndexes: IndexInfo[][],
    oversizedIndexes: IndexInfo[]
  ): IndexRecommendation[] {
    const recommendations: IndexRecommendation[] = [];

    // 未使用索引建议
    unusedIndexes.forEach(idx => {
      recommendations.push({
        type: IndexRecommendationType.DROP,
        priority: 'high',
        reason: `索引 ${idx.name} 从未被使用`,
        impact: `节省 ${Math.round(idx.size / 1024 / 1024)}MB 存储空间`,
        sql: `DROP INDEX ${idx.name};`
      });
    });

    // 重复索引建议
    duplicateIndexes.forEach(group => {
      const primary = group[0];
      const duplicates = group.slice(1);
      
      duplicates.forEach(idx => {
        recommendations.push({
          type: IndexRecommendationType.DROP,
          priority: 'medium',
          reason: `索引 ${idx.name} 与 ${primary.name} 重复`,
          impact: '减少维护开销和存储空间',
          sql: `DROP INDEX ${idx.name};`
        });
      });
    });

    // 过大索引建议
    oversizedIndexes.forEach(idx => {
      recommendations.push({
        type: IndexRecommendationType.MODIFY,
        priority: 'low',
        reason: `索引 ${idx.name} 过大 (${Math.round(idx.size / 1024 / 1024)}MB)`,
        impact: '考虑使用部分索引或重新设计',
        sql: `-- 考虑重建或优化索引 ${idx.name}`
      });
    });

    return recommendations;
  }

  /**
   * 执行索引建议
   */
  async executeRecommendation(recommendation: IndexRecommendation): Promise<boolean> {
    if (!recommendation.sql) {
      logger.warn('索引建议缺少SQL语句', { recommendation });
      return false;
    }

    try {
      await prisma.$executeRawUnsafe(recommendation.sql);
      
      logger.info('索引建议执行成功', {
        type: recommendation.type,
        sql: recommendation.sql
      });

      return true;

    } catch (error) {
      logger.error('索引建议执行失败', {
        sql: recommendation.sql,
        error: error.message
      });
      return false;
    }
  }

  /**
   * 辅助方法
   */
  private determineIndexType(definition: string): IndexType {
    if (definition.includes('UNIQUE')) return IndexType.UNIQUE;
    if (definition.includes('USING gin')) return IndexType.GIN;
    if (definition.includes('USING gist')) return IndexType.GIST;
    if (definition.includes('USING hash')) return IndexType.HASH;
    return IndexType.BTREE;
  }

  private mapMySQLIndexType(type: string): IndexType {
    switch (type.toUpperCase()) {
      case 'BTREE': return IndexType.BTREE;
      case 'HASH': return IndexType.HASH;
      default: return IndexType.BTREE;
    }
  }

  private extractWhereCondition(definition: string): string | undefined {
    const match = definition.match(/WHERE\s+(.+)$/i);
    return match ? match[1] : undefined;
  }

  private extractColumnsFromSQL(sql: string): string[] {
    const match = sql.match(/\(([^)]+)\)/);
    if (match) {
      return match[1].split(',').map(col => col.trim().replace(/"/g, ''));
    }
    return [];
  }

  private calculateEfficiency(scans: number, tuplesRead: number, tuplesReturned: number): number {
    if (scans === 0 || tuplesRead === 0) return 0;
    return Math.min(100, (tuplesReturned / tuplesRead) * 100);
  }

  private generateIndexRecommendation(row: any): IndexRecommendation {
    const scans = parseInt(row.scans) || 0;
    
    if (scans === 0) {
      return {
        type: IndexRecommendationType.DROP,
        priority: 'high',
        reason: '索引从未被使用',
        impact: '节省存储空间和维护开销'
      };
    }

    return {
      type: IndexRecommendationType.KEEP,
      priority: 'low',
      reason: '索引正在被使用',
      impact: '保持当前性能'
    };
  }

  private generateBasicRecommendation(): IndexRecommendation {
    return {
      type: IndexRecommendationType.KEEP,
      priority: 'low',
      reason: '需要进一步分析',
      impact: '未知'
    };
  }
}

// 创建单例实例
export const indexManager = new IndexManagerService();
