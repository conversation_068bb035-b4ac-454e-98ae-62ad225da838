/**
 * 安全事件分析服务
 * 提供安全事件检测、威胁分析、攻击模式识别和安全态势感知功能
 */

import { logger } from '@/config/logger';
import { prisma } from '@/config/database';
import { cacheService } from '@/services/cache.service';
import { metricsCollector } from '@/services/metrics-collector.service';
import { AuditEvent, AuditEventType, AuditSeverity } from '@/services/security-audit.service';

/**
 * 安全威胁类型
 */
export enum SecurityThreatType {
  BRUTE_FORCE = 'brute_force',
  CREDENTIAL_STUFFING = 'credential_stuffing',
  ACCOUNT_TAKEOVER = 'account_takeover',
  PRIVILEGE_ESCALATION = 'privilege_escalation',
  DATA_EXFILTRATION = 'data_exfiltration',
  SUSPICIOUS_LOGIN = 'suspicious_login',
  ANOMALOUS_BEHAVIOR = 'anomalous_behavior',
  MALICIOUS_IP = 'malicious_ip',
  BOT_ATTACK = 'bot_attack',
  DDOS_ATTACK = 'ddos_attack',
  INJECTION_ATTACK = 'injection_attack',
  XSS_ATTACK = 'xss_attack'
}

/**
 * 安全事件接口
 */
interface SecurityEvent {
  id: string;
  threatType: SecurityThreatType;
  severity: 'low' | 'medium' | 'high' | 'critical';
  timestamp: Date;
  sourceIp: string;
  targetUserId?: string;
  targetResource?: string;
  userAgent?: string;
  location?: {
    country: string;
    region: string;
    city: string;
  };
  details: {
    description: string;
    indicators: string[];
    affectedSystems: string[];
    attackVector: string;
    confidence: number;
    metadata: Record<string, any>;
  };
  status: 'detected' | 'investigating' | 'confirmed' | 'mitigated' | 'resolved' | 'false_positive';
  response: {
    actions: string[];
    mitigations: string[];
    blockedIps: string[];
    alertsSent: string[];
  };
  relatedEvents: string[];
}

/**
 * 威胁情报接口
 */
interface ThreatIntelligence {
  id: string;
  type: 'ip' | 'domain' | 'hash' | 'pattern';
  value: string;
  threatType: SecurityThreatType;
  severity: 'low' | 'medium' | 'high' | 'critical';
  source: string;
  confidence: number;
  firstSeen: Date;
  lastSeen: Date;
  tags: string[];
  description: string;
  isActive: boolean;
}

/**
 * 攻击模式接口
 */
interface AttackPattern {
  id: string;
  name: string;
  description: string;
  threatType: SecurityThreatType;
  indicators: {
    timePattern?: string;
    ipPattern?: string;
    userAgentPattern?: string;
    requestPattern?: string;
    behaviorPattern?: string;
  };
  frequency: number;
  lastDetected: Date;
  confidence: number;
  mitigations: string[];
}

/**
 * 安全态势接口
 */
interface SecurityPosture {
  timestamp: Date;
  overallScore: number; // 0-100
  threatLevel: 'very_low' | 'low' | 'medium' | 'high' | 'very_high' | 'critical';
  activeThreats: number;
  resolvedThreats: number;
  falsePositives: number;
  metrics: {
    attacksBlocked: number;
    suspiciousActivities: number;
    compromisedAccounts: number;
    maliciousIps: number;
    averageResponseTime: number;
  };
  trends: {
    threatTrend: 'increasing' | 'decreasing' | 'stable';
    severityTrend: 'escalating' | 'de-escalating' | 'stable';
    volumeTrend: 'increasing' | 'decreasing' | 'stable';
  };
  recommendations: string[];
}

/**
 * 安全事件分析服务
 */
export class SecurityEventAnalyticsService {
  private eventBuffer = new Map<string, SecurityEvent[]>();
  private threatIntelligence = new Map<string, ThreatIntelligence>();
  private attackPatterns = new Map<string, AttackPattern>();
  private securityPostureCache: SecurityPosture | null = null;

  constructor() {
    this.initializeThreatIntelligence();
    this.initializeAttackPatterns();
  }

  /**
   * 分析审计事件并检测安全威胁
   */
  async analyzeAuditEvent(auditEvent: AuditEvent): Promise<SecurityEvent[]> {
    try {
      const detectedEvents: SecurityEvent[] = [];

      // 暴力破解检测
      const bruteForceEvent = await this.detectBruteForce(auditEvent);
      if (bruteForceEvent) {
        detectedEvents.push(bruteForceEvent);
      }

      // 可疑登录检测
      const suspiciousLoginEvent = await this.detectSuspiciousLogin(auditEvent);
      if (suspiciousLoginEvent) {
        detectedEvents.push(suspiciousLoginEvent);
      }

      // 恶意IP检测
      const maliciousIpEvent = await this.detectMaliciousIp(auditEvent);
      if (maliciousIpEvent) {
        detectedEvents.push(maliciousIpEvent);
      }

      // 权限提升检测
      const privilegeEscalationEvent = await this.detectPrivilegeEscalation(auditEvent);
      if (privilegeEscalationEvent) {
        detectedEvents.push(privilegeEscalationEvent);
      }

      // 保存检测到的安全事件
      for (const event of detectedEvents) {
        await this.saveSecurityEvent(event);
        await this.triggerSecurityResponse(event);
      }

      // 更新指标
      if (detectedEvents.length > 0) {
        metricsCollector.incrementCounter('security_events_detected_total', {
          threat_type: detectedEvents[0].threatType,
          severity: detectedEvents[0].severity
        });
      }

      return detectedEvents;

    } catch (error) {
      logger.error('安全事件分析失败', {
        error: error instanceof Error ? error.message : String(error),
        auditEventId: auditEvent.id
      });
      return [];
    }
  }

  /**
   * 获取安全态势
   */
  async getSecurityPosture(): Promise<SecurityPosture> {
    try {
      // 检查缓存
      if (this.securityPostureCache) {
        const cacheAge = Date.now() - this.securityPostureCache.timestamp.getTime();
        if (cacheAge < 5 * 60 * 1000) { // 5分钟缓存
          return this.securityPostureCache;
        }
      }

      const endTime = new Date();
      const startTime = new Date(endTime.getTime() - 24 * 60 * 60 * 1000); // 最近24小时

      // 获取安全事件
      const securityEvents = await this.getSecurityEvents(startTime, endTime);

      // 计算威胁级别
      const threatLevel = this.calculateThreatLevel(securityEvents);

      // 计算整体安全评分
      const overallScore = this.calculateSecurityScore(securityEvents);

      // 统计指标
      const metrics = this.calculateSecurityMetrics(securityEvents);

      // 分析趋势
      const trends = await this.analyzeSecurityTrends(securityEvents);

      // 生成建议
      const recommendations = this.generateSecurityRecommendations(securityEvents, threatLevel);

      const posture: SecurityPosture = {
        timestamp: new Date(),
        overallScore,
        threatLevel,
        activeThreats: securityEvents.filter(e => e.status === 'detected' || e.status === 'investigating').length,
        resolvedThreats: securityEvents.filter(e => e.status === 'resolved').length,
        falsePositives: securityEvents.filter(e => e.status === 'false_positive').length,
        metrics,
        trends,
        recommendations
      };

      // 缓存结果
      this.securityPostureCache = posture;

      logger.info('安全态势分析完成', {
        overallScore,
        threatLevel,
        activeThreats: posture.activeThreats,
        totalEvents: securityEvents.length
      });

      return posture;

    } catch (error) {
      logger.error('获取安全态势失败', {
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * 获取威胁分析报告
   */
  async getThreatAnalysisReport(timeRange: { start: Date; end: Date }): Promise<{
    summary: {
      totalThreats: number;
      criticalThreats: number;
      blockedAttacks: number;
      compromisedAccounts: number;
      topThreatTypes: Array<{ type: SecurityThreatType; count: number }>;
      topSourceIps: Array<{ ip: string; count: number; country?: string }>;
    };
    timeline: Array<{
      timestamp: Date;
      threatType: SecurityThreatType;
      severity: string;
      count: number;
    }>;
    patterns: AttackPattern[];
    recommendations: {
      immediate: string[];
      shortTerm: string[];
      longTerm: string[];
    };
  }> {
    try {
      const securityEvents = await this.getSecurityEvents(timeRange.start, timeRange.end);

      // 生成摘要
      const summary = this.generateThreatSummary(securityEvents);

      // 生成时间线
      const timeline = this.generateThreatTimeline(securityEvents);

      // 识别攻击模式
      const patterns = await this.identifyAttackPatterns(securityEvents);

      // 生成建议
      const recommendations = this.generateThreatRecommendations(securityEvents, patterns);

      return {
        summary,
        timeline,
        patterns,
        recommendations
      };

    } catch (error) {
      logger.error('生成威胁分析报告失败', {
        error: error instanceof Error ? error.message : String(error),
        timeRange
      });
      throw error;
    }
  }

  /**
   * 更新威胁情报
   */
  async updateThreatIntelligence(intelligence: Partial<ThreatIntelligence>): Promise<void> {
    try {
      const threatIntel: ThreatIntelligence = {
        id: intelligence.id || this.generateId(),
        type: intelligence.type!,
        value: intelligence.value!,
        threatType: intelligence.threatType!,
        severity: intelligence.severity || 'medium',
        source: intelligence.source || 'internal',
        confidence: intelligence.confidence || 0.8,
        firstSeen: intelligence.firstSeen || new Date(),
        lastSeen: intelligence.lastSeen || new Date(),
        tags: intelligence.tags || [],
        description: intelligence.description || '',
        isActive: intelligence.isActive !== false
      };

      this.threatIntelligence.set(threatIntel.value, threatIntel);

      // 保存到数据库
      await this.saveThreatIntelligence(threatIntel);

      logger.info('威胁情报已更新', {
        type: threatIntel.type,
        value: threatIntel.value,
        threatType: threatIntel.threatType
      });

    } catch (error) {
      logger.error('更新威胁情报失败', {
        error: error instanceof Error ? error.message : String(error),
        intelligence
      });
      throw error;
    }
  }

  // 私有检测方法

  private async detectBruteForce(auditEvent: AuditEvent): Promise<SecurityEvent | null> {
    if (auditEvent.eventType !== AuditEventType.LOGIN_FAILED) {
      return null;
    }

    const timeWindow = 5 * 60 * 1000; // 5分钟窗口
    const threshold = 5; // 5次失败尝试

    // 获取同一IP的最近失败登录
    const recentFailures = await this.getRecentFailedLogins(auditEvent.ipAddress, timeWindow);

    if (recentFailures.length >= threshold) {
      return {
        id: this.generateId(),
        threatType: SecurityThreatType.BRUTE_FORCE,
        severity: 'high',
        timestamp: new Date(),
        sourceIp: auditEvent.ipAddress,
        targetUserId: auditEvent.userId,
        userAgent: auditEvent.userAgent,
        details: {
          description: `检测到来自${auditEvent.ipAddress}的暴力破解攻击`,
          indicators: [`${recentFailures.length}次失败登录尝试`, `时间窗口：${timeWindow/1000}秒`],
          affectedSystems: ['authentication'],
          attackVector: 'credential_brute_force',
          confidence: 0.9,
          metadata: {
            failureCount: recentFailures.length,
            timeWindow: timeWindow,
            targetUsers: [...new Set(recentFailures.map(f => f.userId))]
          }
        },
        status: 'detected',
        response: {
          actions: ['block_ip', 'alert_admin'],
          mitigations: ['rate_limiting', 'account_lockout'],
          blockedIps: [auditEvent.ipAddress],
          alertsSent: ['security_team']
        },
        relatedEvents: recentFailures.map(f => f.id)
      };
    }

    return null;
  }

  private async detectSuspiciousLogin(auditEvent: AuditEvent): Promise<SecurityEvent | null> {
    if (auditEvent.eventType !== AuditEventType.LOGIN_SUCCESS) {
      return null;
    }

    const suspiciousIndicators: string[] = [];
    let riskScore = 0;

    // 检查异常时间
    const hour = new Date(auditEvent.timestamp).getHours();
    if (hour < 6 || hour > 22) {
      suspiciousIndicators.push('异常登录时间');
      riskScore += 20;
    }

    // 检查新设备
    const isNewDevice = await this.isNewDevice(auditEvent.userId!, auditEvent.userAgent);
    if (isNewDevice) {
      suspiciousIndicators.push('新设备登录');
      riskScore += 30;
    }

    // 检查地理位置异常
    const isUnusualLocation = await this.isUnusualLocation(auditEvent.userId!, auditEvent.ipAddress);
    if (isUnusualLocation) {
      suspiciousIndicators.push('异常地理位置');
      riskScore += 40;
    }

    if (riskScore >= 50) {
      const severity = riskScore >= 80 ? 'critical' : riskScore >= 60 ? 'high' : 'medium';

      return {
        id: this.generateId(),
        threatType: SecurityThreatType.SUSPICIOUS_LOGIN,
        severity: severity as any,
        timestamp: new Date(),
        sourceIp: auditEvent.ipAddress,
        targetUserId: auditEvent.userId,
        userAgent: auditEvent.userAgent,
        details: {
          description: `检测到可疑登录活动`,
          indicators: suspiciousIndicators,
          affectedSystems: ['authentication'],
          attackVector: 'suspicious_access',
          confidence: riskScore / 100,
          metadata: {
            riskScore,
            loginTime: auditEvent.timestamp,
            userAgent: auditEvent.userAgent
          }
        },
        status: 'detected',
        response: {
          actions: ['require_mfa', 'alert_user'],
          mitigations: ['enhanced_monitoring'],
          blockedIps: [],
          alertsSent: ['user', 'security_team']
        },
        relatedEvents: [auditEvent.id]
      };
    }

    return null;
  }

  private async detectMaliciousIp(auditEvent: AuditEvent): Promise<SecurityEvent | null> {
    const threatIntel = this.threatIntelligence.get(auditEvent.ipAddress);
    
    if (threatIntel && threatIntel.isActive) {
      return {
        id: this.generateId(),
        threatType: SecurityThreatType.MALICIOUS_IP,
        severity: threatIntel.severity,
        timestamp: new Date(),
        sourceIp: auditEvent.ipAddress,
        targetUserId: auditEvent.userId,
        userAgent: auditEvent.userAgent,
        details: {
          description: `检测到来自恶意IP的访问`,
          indicators: [`威胁情报匹配: ${threatIntel.source}`, `置信度: ${threatIntel.confidence}`],
          affectedSystems: ['authentication'],
          attackVector: 'malicious_ip',
          confidence: threatIntel.confidence,
          metadata: {
            threatSource: threatIntel.source,
            threatTags: threatIntel.tags,
            firstSeen: threatIntel.firstSeen
          }
        },
        status: 'detected',
        response: {
          actions: ['block_ip', 'alert_admin'],
          mitigations: ['ip_blocking'],
          blockedIps: [auditEvent.ipAddress],
          alertsSent: ['security_team']
        },
        relatedEvents: [auditEvent.id]
      };
    }

    return null;
  }

  private async detectPrivilegeEscalation(auditEvent: AuditEvent): Promise<SecurityEvent | null> {
    if (auditEvent.eventType !== AuditEventType.PERMISSION_GRANTED) {
      return null;
    }

    // 检查是否为高权限操作
    const isHighPrivilege = auditEvent.details?.permission?.includes('admin') || 
                           auditEvent.details?.permission?.includes('super');

    if (isHighPrivilege) {
      return {
        id: this.generateId(),
        threatType: SecurityThreatType.PRIVILEGE_ESCALATION,
        severity: 'high',
        timestamp: new Date(),
        sourceIp: auditEvent.ipAddress,
        targetUserId: auditEvent.userId,
        userAgent: auditEvent.userAgent,
        details: {
          description: `检测到权限提升活动`,
          indicators: [`获得高级权限: ${auditEvent.details?.permission}`],
          affectedSystems: ['authorization'],
          attackVector: 'privilege_escalation',
          confidence: 0.8,
          metadata: {
            permission: auditEvent.details?.permission,
            grantedBy: auditEvent.details?.grantedBy
          }
        },
        status: 'detected',
        response: {
          actions: ['alert_admin', 'review_permission'],
          mitigations: ['enhanced_monitoring'],
          blockedIps: [],
          alertsSent: ['security_team', 'admin']
        },
        relatedEvents: [auditEvent.id]
      };
    }

    return null;
  }

  // 私有辅助方法

  private generateId(): string {
    return `sec_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private async getRecentFailedLogins(ipAddress: string, timeWindow: number): Promise<any[]> {
    // 简化实现：返回模拟数据
    return [
      { id: '1', userId: 'user1', timestamp: new Date() },
      { id: '2', userId: 'user2', timestamp: new Date() },
      { id: '3', userId: 'user1', timestamp: new Date() }
    ];
  }

  private async isNewDevice(userId: string, userAgent?: string): Promise<boolean> {
    // 简化实现：检查是否为新设备
    return Math.random() > 0.7; // 30%概率为新设备
  }

  private async isUnusualLocation(userId: string, ipAddress: string): Promise<boolean> {
    // 简化实现：检查是否为异常位置
    return Math.random() > 0.8; // 20%概率为异常位置
  }

  private calculateThreatLevel(events: SecurityEvent[]): SecurityPosture['threatLevel'] {
    const criticalEvents = events.filter(e => e.severity === 'critical').length;
    const highEvents = events.filter(e => e.severity === 'high').length;
    
    if (criticalEvents > 5) return 'critical';
    if (criticalEvents > 2 || highEvents > 10) return 'very_high';
    if (criticalEvents > 0 || highEvents > 5) return 'high';
    if (highEvents > 0) return 'medium';
    if (events.length > 0) return 'low';
    return 'very_low';
  }

  private calculateSecurityScore(events: SecurityEvent[]): number {
    let score = 100;
    
    events.forEach(event => {
      switch (event.severity) {
        case 'critical': score -= 20; break;
        case 'high': score -= 10; break;
        case 'medium': score -= 5; break;
        case 'low': score -= 2; break;
      }
    });
    
    return Math.max(0, score);
  }

  private calculateSecurityMetrics(events: SecurityEvent[]): SecurityPosture['metrics'] {
    return {
      attacksBlocked: events.filter(e => e.response.actions.includes('block_ip')).length,
      suspiciousActivities: events.filter(e => e.threatType === SecurityThreatType.SUSPICIOUS_LOGIN).length,
      compromisedAccounts: events.filter(e => e.threatType === SecurityThreatType.ACCOUNT_TAKEOVER).length,
      maliciousIps: new Set(events.filter(e => e.threatType === SecurityThreatType.MALICIOUS_IP).map(e => e.sourceIp)).size,
      averageResponseTime: 300 // 5分钟平均响应时间
    };
  }

  private async analyzeSecurityTrends(events: SecurityEvent[]): Promise<SecurityPosture['trends']> {
    // 简化实现：分析趋势
    return {
      threatTrend: 'stable',
      severityTrend: 'stable',
      volumeTrend: 'stable'
    };
  }

  private generateSecurityRecommendations(events: SecurityEvent[], threatLevel: string): string[] {
    const recommendations: string[] = [];
    
    if (threatLevel === 'critical' || threatLevel === 'very_high') {
      recommendations.push('立即启动事件响应流程');
      recommendations.push('加强网络监控');
      recommendations.push('考虑临时限制访问');
    }
    
    const bruteForceEvents = events.filter(e => e.threatType === SecurityThreatType.BRUTE_FORCE);
    if (bruteForceEvents.length > 0) {
      recommendations.push('加强密码策略');
      recommendations.push('实施账户锁定机制');
    }
    
    return recommendations;
  }

  private generateThreatSummary(events: SecurityEvent[]): any {
    const threatTypeCounts = new Map<SecurityThreatType, number>();
    const ipCounts = new Map<string, number>();
    
    events.forEach(event => {
      threatTypeCounts.set(event.threatType, (threatTypeCounts.get(event.threatType) || 0) + 1);
      ipCounts.set(event.sourceIp, (ipCounts.get(event.sourceIp) || 0) + 1);
    });
    
    return {
      totalThreats: events.length,
      criticalThreats: events.filter(e => e.severity === 'critical').length,
      blockedAttacks: events.filter(e => e.response.actions.includes('block_ip')).length,
      compromisedAccounts: events.filter(e => e.threatType === SecurityThreatType.ACCOUNT_TAKEOVER).length,
      topThreatTypes: Array.from(threatTypeCounts.entries())
        .sort((a, b) => b[1] - a[1])
        .slice(0, 5)
        .map(([type, count]) => ({ type, count })),
      topSourceIps: Array.from(ipCounts.entries())
        .sort((a, b) => b[1] - a[1])
        .slice(0, 10)
        .map(([ip, count]) => ({ ip, count }))
    };
  }

  private generateThreatTimeline(events: SecurityEvent[]): any[] {
    const timeline = new Map<string, Map<SecurityThreatType, { severity: string; count: number }>>();
    
    events.forEach(event => {
      const dateKey = event.timestamp.toISOString().split('T')[0];
      if (!timeline.has(dateKey)) {
        timeline.set(dateKey, new Map());
      }
      
      const dayMap = timeline.get(dateKey)!;
      const existing = dayMap.get(event.threatType) || { severity: event.severity, count: 0 };
      existing.count++;
      dayMap.set(event.threatType, existing);
    });
    
    const result: any[] = [];
    timeline.forEach((dayMap, date) => {
      dayMap.forEach((data, threatType) => {
        result.push({
          timestamp: new Date(date),
          threatType,
          severity: data.severity,
          count: data.count
        });
      });
    });
    
    return result.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());
  }

  private async identifyAttackPatterns(events: SecurityEvent[]): Promise<AttackPattern[]> {
    // 简化实现：返回已知攻击模式
    return Array.from(this.attackPatterns.values());
  }

  private generateThreatRecommendations(events: SecurityEvent[], patterns: AttackPattern[]): any {
    return {
      immediate: [
        '阻止恶意IP地址',
        '启用多因素认证',
        '加强监控'
      ],
      shortTerm: [
        '更新安全策略',
        '培训安全意识',
        '部署额外防护'
      ],
      longTerm: [
        '实施零信任架构',
        '升级安全基础设施',
        '建立威胁情报共享'
      ]
    };
  }

  private async getSecurityEvents(startTime: Date, endTime: Date): Promise<SecurityEvent[]> {
    // 简化实现：从缓冲区获取事件
    const allEvents: SecurityEvent[] = [];
    this.eventBuffer.forEach(events => {
      const filteredEvents = events.filter(event => 
        event.timestamp >= startTime && event.timestamp <= endTime
      );
      allEvents.push(...filteredEvents);
    });
    return allEvents;
  }

  private async saveSecurityEvent(event: SecurityEvent): Promise<void> {
    // 保存到缓冲区
    const events = this.eventBuffer.get(event.sourceIp) || [];
    events.push(event);
    this.eventBuffer.set(event.sourceIp, events);
    
    logger.debug('安全事件已保存', { eventId: event.id, threatType: event.threatType });
  }

  private async triggerSecurityResponse(event: SecurityEvent): Promise<void> {
    // 触发安全响应
    if (event.response.actions.includes('block_ip')) {
      logger.warn('触发IP阻止', { ip: event.sourceIp, threatType: event.threatType });
    }
    
    if (event.response.actions.includes('alert_admin')) {
      logger.warn('触发管理员告警', { eventId: event.id, severity: event.severity });
    }
  }

  private async saveThreatIntelligence(intelligence: ThreatIntelligence): Promise<void> {
    logger.debug('威胁情报已保存', { type: intelligence.type, value: intelligence.value });
  }

  private initializeThreatIntelligence(): void {
    // 初始化一些已知的恶意IP
    const knownMaliciousIps = [
      '*************',
      '*********',
      '***********'
    ];

    knownMaliciousIps.forEach(ip => {
      this.threatIntelligence.set(ip, {
        id: this.generateId(),
        type: 'ip',
        value: ip,
        threatType: SecurityThreatType.MALICIOUS_IP,
        severity: 'high',
        source: 'internal_blacklist',
        confidence: 0.9,
        firstSeen: new Date(),
        lastSeen: new Date(),
        tags: ['malicious', 'blacklist'],
        description: '已知恶意IP地址',
        isActive: true
      });
    });
  }

  private initializeAttackPatterns(): void {
    // 初始化攻击模式
    this.attackPatterns.set('brute_force_pattern', {
      id: 'brute_force_pattern',
      name: '暴力破解模式',
      description: '短时间内多次失败登录尝试',
      threatType: SecurityThreatType.BRUTE_FORCE,
      indicators: {
        timePattern: '5分钟内5次以上失败',
        requestPattern: 'POST /auth/login'
      },
      frequency: 0,
      lastDetected: new Date(),
      confidence: 0.9,
      mitigations: ['rate_limiting', 'account_lockout', 'ip_blocking']
    });
  }
}

// 创建单例实例
export const securityEventAnalyticsService = new SecurityEventAnalyticsService();
