/**
 * 自动化性能测试服务
 * 实现自动化性能回归测试和基准测试
 */

import { EventEmitter } from 'events';
import { logger } from '@/config/logger';
import { prisma } from '@/config/database';
import { performanceTestService, TestType } from './performance-test.service';
import { benchmarkService } from './benchmark.service';
import { metricsCollector } from './metrics-collector.service';
import cron from 'node-cron';

/**
 * 性能测试计划配置
 */
interface PerformanceTestSchedule {
  id: string;
  name: string;
  cronExpression: string;
  enabled: boolean;
  testType: TestType;
  config: any;
  scenarios: any[];
  thresholds: PerformanceThresholds;
  lastRun?: Date;
  nextRun?: Date;
}

/**
 * 性能阈值配置
 */
interface PerformanceThresholds {
  responseTime: {
    avg: number;
    p95: number;
    p99: number;
  };
  throughput: {
    min: number;
  };
  errorRate: {
    max: number;
  };
  resourceUsage: {
    cpu: number;
    memory: number;
  };
}

/**
 * 性能回归检测结果
 */
interface RegressionResult {
  testId: string;
  timestamp: Date;
  hasRegression: boolean;
  regressions: RegressionIssue[];
  improvements: PerformanceImprovement[];
  summary: {
    totalIssues: number;
    criticalIssues: number;
    warningIssues: number;
  };
}

/**
 * 性能回归问题
 */
interface RegressionIssue {
  metric: string;
  severity: 'critical' | 'warning' | 'info';
  currentValue: number;
  baselineValue: number;
  degradationPercent: number;
  description: string;
}

/**
 * 性能改进
 */
interface PerformanceImprovement {
  metric: string;
  currentValue: number;
  baselineValue: number;
  improvementPercent: number;
  description: string;
}

/**
 * 自动化性能测试服务类
 */
export class AutomatedPerformanceTestingService extends EventEmitter {
  private schedules: Map<string, PerformanceTestSchedule> = new Map();
  private cronJobs: Map<string, cron.ScheduledTask> = new Map();
  private testHistory: Map<string, any[]> = new Map();
  private baselines: Map<string, any> = new Map();

  constructor() {
    super();
    this.initializeDefaultSchedules();
  }

  /**
   * 初始化默认测试计划
   */
  private initializeDefaultSchedules(): void {
    const defaultSchedules: PerformanceTestSchedule[] = [
      {
        id: 'daily-load-test',
        name: '每日负载测试',
        cronExpression: '0 3 * * *', // 每天凌晨3点
        enabled: true,
        testType: TestType.LOAD,
        config: {
          concurrency: 50,
          duration: 300000, // 5分钟
          rampUpTime: 30000,
          rampDownTime: 30000,
          targetRPS: 100
        },
        scenarios: [
          {
            name: 'login_scenario',
            weight: 30,
            execute: async () => {
              // 登录场景实现
              return { success: true };
            }
          },
          {
            name: 'api_access_scenario',
            weight: 50,
            execute: async () => {
              // API访问场景实现
              return { success: true };
            }
          },
          {
            name: 'oauth_flow_scenario',
            weight: 20,
            execute: async () => {
              // OAuth流程场景实现
              return { success: true };
            }
          }
        ],
        thresholds: {
          responseTime: { avg: 200, p95: 500, p99: 1000 },
          throughput: { min: 90 },
          errorRate: { max: 1 },
          resourceUsage: { cpu: 80, memory: 80 }
        }
      },
      {
        id: 'weekly-stress-test',
        name: '每周压力测试',
        cronExpression: '0 2 * * 0', // 每周日凌晨2点
        enabled: true,
        testType: TestType.STRESS,
        config: {
          concurrency: 200,
          duration: 600000, // 10分钟
          rampUpTime: 60000,
          rampDownTime: 60000,
          targetRPS: 500
        },
        scenarios: [],
        thresholds: {
          responseTime: { avg: 500, p95: 1000, p99: 2000 },
          throughput: { min: 400 },
          errorRate: { max: 5 },
          resourceUsage: { cpu: 90, memory: 85 }
        }
      },
      {
        id: 'hourly-benchmark',
        name: '每小时基准测试',
        cronExpression: '0 * * * *', // 每小时
        enabled: true,
        testType: TestType.BASELINE,
        config: {
          concurrency: 10,
          duration: 60000, // 1分钟
          rampUpTime: 5000,
          rampDownTime: 5000
        },
        scenarios: [],
        thresholds: {
          responseTime: { avg: 100, p95: 200, p99: 500 },
          throughput: { min: 50 },
          errorRate: { max: 0.5 },
          resourceUsage: { cpu: 50, memory: 60 }
        }
      }
    ];

    defaultSchedules.forEach(schedule => {
      this.schedules.set(schedule.id, schedule);
    });
  }

  /**
   * 启动自动化性能测试服务
   */
  async start(): Promise<void> {
    try {
      logger.info('启动自动化性能测试服务');

      // 加载历史基准数据
      await this.loadBaselines();

      // 启动所有启用的测试计划
      for (const [id, schedule] of this.schedules) {
        if (schedule.enabled) {
          await this.startSchedule(id);
        }
      }

      // 启动监控任务
      this.startMonitoringTasks();

      logger.info('自动化性能测试服务启动成功', {
        activeSchedules: Array.from(this.schedules.values()).filter(s => s.enabled).length,
        totalSchedules: this.schedules.size
      });

    } catch (error) {
      logger.error('启动自动化性能测试服务失败', {
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * 停止自动化性能测试服务
   */
  async stop(): Promise<void> {
    try {
      logger.info('停止自动化性能测试服务');

      // 停止所有定时任务
      for (const [id, job] of this.cronJobs) {
        job.stop();
        job.destroy();
        this.cronJobs.delete(id);
      }

      logger.info('自动化性能测试服务已停止');

    } catch (error) {
      logger.error('停止自动化性能测试服务失败', {
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  /**
   * 启动指定的测试计划
   */
  private async startSchedule(scheduleId: string): Promise<void> {
    const schedule = this.schedules.get(scheduleId);
    if (!schedule) {
      throw new Error(`测试计划不存在: ${scheduleId}`);
    }

    try {
      // 停止现有的任务（如果存在）
      const existingJob = this.cronJobs.get(scheduleId);
      if (existingJob) {
        existingJob.stop();
        existingJob.destroy();
      }

      // 创建新的定时任务
      const job = cron.schedule(schedule.cronExpression, async () => {
        await this.executeScheduledTest(scheduleId);
      }, {
        scheduled: false,
        timezone: 'Asia/Shanghai'
      });

      // 启动任务
      job.start();
      this.cronJobs.set(scheduleId, job);

      // 更新下次运行时间
      schedule.nextRun = this.getNextRunTime(schedule.cronExpression);

      logger.info('性能测试计划已启动', {
        scheduleId,
        name: schedule.name,
        cronExpression: schedule.cronExpression,
        nextRun: schedule.nextRun
      });

    } catch (error) {
      logger.error('启动性能测试计划失败', {
        scheduleId,
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * 执行计划的性能测试
   */
  private async executeScheduledTest(scheduleId: string): Promise<void> {
    const schedule = this.schedules.get(scheduleId);
    if (!schedule) {
      logger.error('测试计划不存在', { scheduleId });
      return;
    }

    const startTime = new Date();
    logger.info('开始执行自动化性能测试', {
      scheduleId,
      testType: schedule.testType,
      startTime
    });

    try {
      let result;

      if (schedule.testType === TestType.BASELINE) {
        // 执行基准测试
        result = await this.executeBenchmarkTest(schedule);
      } else {
        // 执行性能测试
        result = await performanceTestService.runTest(
          schedule.testType,
          schedule.config,
          schedule.scenarios
        );
      }

      // 更新测试计划状态
      schedule.lastRun = startTime;
      schedule.nextRun = this.getNextRunTime(schedule.cronExpression);

      // 保存测试结果
      await this.saveTestResult(scheduleId, result);

      // 执行回归检测
      const regressionResult = await this.detectRegression(scheduleId, result);

      // 发送通知（如果有回归）
      if (regressionResult.hasRegression) {
        await this.sendRegressionAlert(schedule, regressionResult);
      }

      // 更新基准（如果是基准测试）
      if (schedule.testType === TestType.BASELINE) {
        await this.updateBaseline(scheduleId, result);
      }

      const endTime = new Date();
      const duration = endTime.getTime() - startTime.getTime();

      logger.info('自动化性能测试完成', {
        scheduleId,
        testType: schedule.testType,
        duration,
        hasRegression: regressionResult.hasRegression,
        regressionCount: regressionResult.regressions.length
      });

      // 发出测试完成事件
      this.emit('testCompleted', {
        scheduleId,
        result,
        regressionResult
      });

    } catch (error) {
      logger.error('自动化性能测试失败', {
        scheduleId,
        testType: schedule.testType,
        error: error instanceof Error ? error.message : String(error)
      });

      // 发送错误通知
      await this.sendErrorAlert(schedule, error);

      // 发出测试失败事件
      this.emit('testFailed', {
        scheduleId,
        error
      });
    }
  }

  /**
   * 执行基准测试
   */
  private async executeBenchmarkTest(schedule: PerformanceTestSchedule): Promise<any> {
    const results = await benchmarkService.runFullBenchmarkSuite();
    
    // 转换为统一的结果格式
    return {
      testId: `benchmark-${Date.now()}`,
      testType: TestType.BASELINE,
      startTime: new Date(),
      endTime: new Date(),
      results: results,
      summary: this.summarizeBenchmarkResults(results)
    };
  }

  /**
   * 汇总基准测试结果
   */
  private summarizeBenchmarkResults(results: any[]): any {
    const summary = {
      totalTests: results.length,
      avgResponseTime: 0,
      totalOperations: 0,
      categories: results.map(r => r.category)
    };

    // 计算平均响应时间
    let totalResponseTime = 0;
    let totalOps = 0;

    results.forEach(result => {
      if (result.results) {
        Object.values(result.results).forEach((test: any) => {
          if (test.responseTime && test.responseTime.avg) {
            totalResponseTime += test.responseTime.avg;
            totalOps += test.totalOperations || 0;
          }
        });
      }
    });

    summary.avgResponseTime = totalOps > 0 ? totalResponseTime / totalOps : 0;
    summary.totalOperations = totalOps;

    return summary;
  }

  /**
   * 检测性能回归
   */
  private async detectRegression(scheduleId: string, currentResult: any): Promise<RegressionResult> {
    const baseline = this.baselines.get(scheduleId);
    const regressions: RegressionIssue[] = [];
    const improvements: PerformanceImprovement[] = [];

    if (!baseline) {
      logger.info('没有基准数据，跳过回归检测', { scheduleId });
      return {
        testId: currentResult.testId,
        timestamp: new Date(),
        hasRegression: false,
        regressions: [],
        improvements: [],
        summary: { totalIssues: 0, criticalIssues: 0, warningIssues: 0 }
      };
    }

    // 检测响应时间回归
    if (currentResult.responseTime && baseline.responseTime) {
      const avgDegradation = this.calculateDegradation(
        currentResult.responseTime.avg,
        baseline.responseTime.avg
      );

      if (avgDegradation > 20) { // 20%阈值
        regressions.push({
          metric: 'avg_response_time',
          severity: avgDegradation > 50 ? 'critical' : 'warning',
          currentValue: currentResult.responseTime.avg,
          baselineValue: baseline.responseTime.avg,
          degradationPercent: avgDegradation,
          description: `平均响应时间退化 ${avgDegradation.toFixed(1)}%`
        });
      } else if (avgDegradation < -10) { // 改进10%以上
        improvements.push({
          metric: 'avg_response_time',
          currentValue: currentResult.responseTime.avg,
          baselineValue: baseline.responseTime.avg,
          improvementPercent: Math.abs(avgDegradation),
          description: `平均响应时间改进 ${Math.abs(avgDegradation).toFixed(1)}%`
        });
      }
    }

    // 检测吞吐量回归
    if (currentResult.throughput && baseline.throughput) {
      const throughputDegradation = this.calculateDegradation(
        currentResult.throughput.rps,
        baseline.throughput.rps
      );

      if (throughputDegradation > 15) { // 15%阈值
        regressions.push({
          metric: 'throughput',
          severity: throughputDegradation > 30 ? 'critical' : 'warning',
          currentValue: currentResult.throughput.rps,
          baselineValue: baseline.throughput.rps,
          degradationPercent: throughputDegradation,
          description: `吞吐量下降 ${throughputDegradation.toFixed(1)}%`
        });
      }
    }

    // 检测错误率回归
    if (currentResult.errorRate !== undefined && baseline.errorRate !== undefined) {
      const errorRateIncrease = currentResult.errorRate - baseline.errorRate;

      if (errorRateIncrease > 1) { // 错误率增加1%以上
        regressions.push({
          metric: 'error_rate',
          severity: errorRateIncrease > 5 ? 'critical' : 'warning',
          currentValue: currentResult.errorRate,
          baselineValue: baseline.errorRate,
          degradationPercent: (errorRateIncrease / baseline.errorRate) * 100,
          description: `错误率增加 ${errorRateIncrease.toFixed(2)}%`
        });
      }
    }

    const summary = {
      totalIssues: regressions.length,
      criticalIssues: regressions.filter(r => r.severity === 'critical').length,
      warningIssues: regressions.filter(r => r.severity === 'warning').length
    };

    return {
      testId: currentResult.testId,
      timestamp: new Date(),
      hasRegression: regressions.length > 0,
      regressions,
      improvements,
      summary
    };
  }

  /**
   * 计算性能退化百分比
   */
  private calculateDegradation(current: number, baseline: number): number {
    if (baseline === 0) return 0;
    return ((current - baseline) / baseline) * 100;
  }

  /**
   * 发送回归警报
   */
  private async sendRegressionAlert(schedule: PerformanceTestSchedule, regression: RegressionResult): Promise<void> {
    logger.warn('检测到性能回归', {
      scheduleId: schedule.id,
      scheduleName: schedule.name,
      totalIssues: regression.summary.totalIssues,
      criticalIssues: regression.summary.criticalIssues
    });

    // 记录指标
    metricsCollector.incrementCounter('performance_regression_detected', {
      schedule_id: schedule.id,
      severity: regression.summary.criticalIssues > 0 ? 'critical' : 'warning'
    });

    // TODO: 实现邮件/Slack通知
    // 这里可以集成邮件服务或Slack通知
  }

  /**
   * 发送错误警报
   */
  private async sendErrorAlert(schedule: PerformanceTestSchedule, error: any): Promise<void> {
    logger.error('性能测试执行失败', {
      scheduleId: schedule.id,
      scheduleName: schedule.name,
      error: error instanceof Error ? error.message : String(error)
    });

    // 记录指标
    metricsCollector.incrementCounter('performance_test_failures', {
      schedule_id: schedule.id
    });
  }

  /**
   * 保存测试结果
   */
  private async saveTestResult(scheduleId: string, result: any): Promise<void> {
    try {
      // 获取历史记录
      let history = this.testHistory.get(scheduleId) || [];
      
      // 添加新结果
      history.push({
        timestamp: new Date(),
        result: result
      });

      // 保留最近100次结果
      if (history.length > 100) {
        history = history.slice(-100);
      }

      this.testHistory.set(scheduleId, history);

      // TODO: 持久化到数据库
      logger.debug('测试结果已保存', { scheduleId });

    } catch (error) {
      logger.error('保存测试结果失败', {
        scheduleId,
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  /**
   * 更新基准数据
   */
  private async updateBaseline(scheduleId: string, result: any): Promise<void> {
    try {
      this.baselines.set(scheduleId, result);
      
      // TODO: 持久化基准数据到数据库
      logger.info('基准数据已更新', { scheduleId });

    } catch (error) {
      logger.error('更新基准数据失败', {
        scheduleId,
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  /**
   * 加载历史基准数据
   */
  private async loadBaselines(): Promise<void> {
    try {
      // TODO: 从数据库加载基准数据
      logger.info('基准数据加载完成');

    } catch (error) {
      logger.error('加载基准数据失败', {
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  /**
   * 获取下次运行时间
   */
  private getNextRunTime(cronExpression: string): Date {
    // 简化实现，实际应该使用cron解析库
    const now = new Date();
    return new Date(now.getTime() + 24 * 60 * 60 * 1000); // 临时返回24小时后
  }

  /**
   * 启动监控任务
   */
  private startMonitoringTasks(): void {
    // 每小时检查测试计划状态
    cron.schedule('0 * * * *', async () => {
      await this.monitorScheduleHealth();
    });

    // 每天清理旧的测试结果
    cron.schedule('0 2 * * *', async () => {
      await this.cleanupOldResults();
    });
  }

  /**
   * 监控测试计划健康状态
   */
  private async monitorScheduleHealth(): Promise<void> {
    try {
      logger.debug('检查性能测试计划健康状态');
      
      for (const [id, schedule] of this.schedules) {
        if (schedule.enabled && !this.cronJobs.has(id)) {
          logger.warn('发现未运行的测试计划，尝试重启', { scheduleId: id });
          await this.startSchedule(id);
        }
      }

    } catch (error) {
      logger.error('监控测试计划健康状态失败', {
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  /**
   * 清理旧的测试结果
   */
  private async cleanupOldResults(): Promise<void> {
    try {
      logger.info('清理旧的性能测试结果');
      
      const retentionDays = 30; // 保留30天的结果
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - retentionDays);

      // TODO: 实现清理逻辑
      
      logger.info('性能测试结果清理完成', { retentionDays });

    } catch (error) {
      logger.error('清理测试结果失败', {
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  /**
   * 获取测试计划列表
   */
  getSchedules(): PerformanceTestSchedule[] {
    return Array.from(this.schedules.values());
  }

  /**
   * 获取测试历史
   */
  getTestHistory(scheduleId: string): any[] {
    return this.testHistory.get(scheduleId) || [];
  }

  /**
   * 获取基准数据
   */
  getBaseline(scheduleId: string): any {
    return this.baselines.get(scheduleId);
  }

  /**
   * 手动执行测试
   */
  async executeTest(scheduleId: string): Promise<any> {
    const schedule = this.schedules.get(scheduleId);
    if (!schedule) {
      throw new Error(`测试计划不存在: ${scheduleId}`);
    }

    return await this.executeScheduledTest(scheduleId);
  }
}

// 创建服务实例
export const automatedPerformanceTestingService = new AutomatedPerformanceTestingService();
