/**
 * 审计数据导出服务
 * 支持多种格式的审计数据导出和报告生成
 */

import fs from 'fs/promises';
import path from 'path';
import { createWriteStream } from 'fs';
import { Transform } from 'stream';
import ExcelJS from 'exceljs';
import PDFDocument from 'pdfkit';
import { Parser } from 'json2csv';
import { logger } from '@/config/logger';
import { securityAuditService, AuditEvent, AuditEventType, AuditSeverity } from './security-audit.service';
import { metricsCollector } from './metrics-collector.service';

/**
 * 导出格式枚举
 */
export enum ExportFormat {
  JSON = 'json',
  CSV = 'csv',
  EXCEL = 'excel',
  PDF = 'pdf',
  XML = 'xml'
}

/**
 * 报告类型枚举
 */
export enum ReportType {
  SECURITY_SUMMARY = 'security_summary',
  LOGIN_ACTIVITY = 'login_activity',
  PERMISSION_CHANGES = 'permission_changes',
  THREAT_DETECTION = 'threat_detection',
  COMPLIANCE_REPORT = 'compliance_report',
  FULL_AUDIT = 'full_audit'
}

/**
 * 导出选项接口
 */
export interface ExportOptions {
  format: ExportFormat;
  reportType: ReportType;
  startDate: Date;
  endDate: Date;
  userId?: string;
  eventTypes?: AuditEventType[];
  severity?: AuditSeverity[];
  includeMetadata?: boolean;
  includeCharts?: boolean;
  customFields?: string[];
  maxRecords?: number;
  compression?: boolean;
}

/**
 * 导出结果接口
 */
export interface ExportResult {
  success: boolean;
  filePath?: string;
  fileName?: string;
  fileSize?: number;
  recordCount?: number;
  exportTime?: Date;
  downloadUrl?: string;
  expiresAt?: Date;
  error?: string;
}

/**
 * 报告统计接口
 */
export interface ReportStatistics {
  totalEvents: number;
  eventsByType: Record<string, number>;
  eventsBySeverity: Record<string, number>;
  eventsByUser: Record<string, number>;
  timeRange: {
    start: Date;
    end: Date;
  };
  topUsers: Array<{
    userId: string;
    username: string;
    eventCount: number;
  }>;
  securityIncidents: number;
  complianceViolations: number;
}

/**
 * 审计数据导出服务类
 */
export class AuditExportService {
  private exportDir = path.join(process.cwd(), 'exports');
  private tempDir = path.join(process.cwd(), 'temp');
  private maxFileAge = 24 * 60 * 60 * 1000; // 24小时

  constructor() {
    this.initializeDirectories();
    this.scheduleCleanup();
  }

  /**
   * 导出审计数据
   */
  async exportAuditData(options: ExportOptions): Promise<ExportResult> {
    try {
      logger.info('开始导出审计数据', {
        format: options.format,
        reportType: options.reportType,
        startDate: options.startDate,
        endDate: options.endDate
      });

      const startTime = Date.now();

      // 获取审计数据
      const auditData = await this.getAuditData(options);
      
      if (auditData.length === 0) {
        return {
          success: false,
          error: '指定时间范围内没有找到审计数据'
        };
      }

      // 生成统计信息
      const statistics = await this.generateStatistics(auditData, options);

      // 根据格式导出数据
      let result: ExportResult;
      switch (options.format) {
        case ExportFormat.JSON:
          result = await this.exportToJSON(auditData, statistics, options);
          break;
        case ExportFormat.CSV:
          result = await this.exportToCSV(auditData, options);
          break;
        case ExportFormat.EXCEL:
          result = await this.exportToExcel(auditData, statistics, options);
          break;
        case ExportFormat.PDF:
          result = await this.exportToPDF(auditData, statistics, options);
          break;
        case ExportFormat.XML:
          result = await this.exportToXML(auditData, statistics, options);
          break;
        default:
          throw new Error(`不支持的导出格式: ${options.format}`);
      }

      const duration = Date.now() - startTime;

      // 记录导出指标
      metricsCollector.recordHistogram('audit_export_duration', duration, {
        format: options.format,
        reportType: options.reportType
      });

      metricsCollector.incrementCounter('audit_exports_total', {
        format: options.format,
        reportType: options.reportType,
        success: result.success ? 'true' : 'false'
      });

      logger.info('审计数据导出完成', {
        success: result.success,
        fileName: result.fileName,
        recordCount: result.recordCount,
        duration: `${duration}ms`
      });

      return result;

    } catch (error) {
      logger.error('审计数据导出失败', {
        error: error instanceof Error ? error.message : String(error),
        options
      });

      metricsCollector.incrementCounter('audit_export_errors_total', {
        format: options.format,
        reportType: options.reportType
      });

      return {
        success: false,
        error: error instanceof Error ? error.message : '导出失败'
      };
    }
  }

  /**
   * 生成合规报告
   */
  async generateComplianceReport(
    startDate: Date,
    endDate: Date,
    format: ExportFormat = ExportFormat.PDF
  ): Promise<ExportResult> {
    const options: ExportOptions = {
      format,
      reportType: ReportType.COMPLIANCE_REPORT,
      startDate,
      endDate,
      includeMetadata: true,
      includeCharts: true
    };

    return this.exportAuditData(options);
  }

  /**
   * 生成安全摘要报告
   */
  async generateSecuritySummary(
    startDate: Date,
    endDate: Date,
    format: ExportFormat = ExportFormat.PDF
  ): Promise<ExportResult> {
    const options: ExportOptions = {
      format,
      reportType: ReportType.SECURITY_SUMMARY,
      startDate,
      endDate,
      severity: [AuditSeverity.HIGH, AuditSeverity.CRITICAL],
      includeCharts: true
    };

    return this.exportAuditData(options);
  }

  /**
   * 获取导出文件列表
   */
  async getExportFiles(): Promise<Array<{
    fileName: string;
    filePath: string;
    fileSize: number;
    createdAt: Date;
    expiresAt: Date;
  }>> {
    try {
      const files = await fs.readdir(this.exportDir);
      const fileInfos = [];

      for (const file of files) {
        const filePath = path.join(this.exportDir, file);
        const stats = await fs.stat(filePath);
        
        fileInfos.push({
          fileName: file,
          filePath,
          fileSize: stats.size,
          createdAt: stats.birthtime,
          expiresAt: new Date(stats.birthtime.getTime() + this.maxFileAge)
        });
      }

      return fileInfos.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());

    } catch (error) {
      logger.error('获取导出文件列表失败', { error });
      return [];
    }
  }

  /**
   * 删除导出文件
   */
  async deleteExportFile(fileName: string): Promise<boolean> {
    try {
      const filePath = path.join(this.exportDir, fileName);
      await fs.unlink(filePath);
      
      logger.info('导出文件已删除', { fileName });
      return true;

    } catch (error) {
      logger.error('删除导出文件失败', { fileName, error });
      return false;
    }
  }

  // 私有方法

  /**
   * 初始化目录
   */
  private async initializeDirectories(): Promise<void> {
    try {
      await fs.mkdir(this.exportDir, { recursive: true });
      await fs.mkdir(this.tempDir, { recursive: true });
    } catch (error) {
      logger.error('初始化导出目录失败', { error });
    }
  }

  /**
   * 获取审计数据
   */
  private async getAuditData(options: ExportOptions): Promise<AuditEvent[]> {
    const filters = {
      startDate: options.startDate,
      endDate: options.endDate,
      userId: options.userId,
      eventTypes: options.eventTypes,
      severity: options.severity,
      limit: options.maxRecords || 10000
    };

    return securityAuditService.getAuditEvents(filters);
  }

  /**
   * 生成统计信息
   */
  private async generateStatistics(
    auditData: AuditEvent[],
    options: ExportOptions
  ): Promise<ReportStatistics> {
    const eventsByType: Record<string, number> = {};
    const eventsBySeverity: Record<string, number> = {};
    const eventsByUser: Record<string, number> = {};

    for (const event of auditData) {
      // 按类型统计
      eventsByType[event.type] = (eventsByType[event.type] || 0) + 1;

      // 按严重程度统计
      eventsBySeverity[event.severity] = (eventsBySeverity[event.severity] || 0) + 1;

      // 按用户统计
      if (event.userId) {
        eventsByUser[event.userId] = (eventsByUser[event.userId] || 0) + 1;
      }
    }

    // 获取用户信息并生成Top用户列表
    const topUsers = Object.entries(eventsByUser)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 10)
      .map(([userId, eventCount]) => ({
        userId,
        username: userId, // 实际应用中应该查询用户名
        eventCount
      }));

    // 统计安全事件
    const securityIncidents = auditData.filter(event => 
      event.severity === AuditSeverity.HIGH || 
      event.severity === AuditSeverity.CRITICAL
    ).length;

    // 统计合规违规
    const complianceViolations = auditData.filter(event =>
      event.type === AuditEventType.PERMISSION_DENIED ||
      event.type === AuditEventType.UNAUTHORIZED_ACCESS
    ).length;

    return {
      totalEvents: auditData.length,
      eventsByType,
      eventsBySeverity,
      eventsByUser,
      timeRange: {
        start: options.startDate,
        end: options.endDate
      },
      topUsers,
      securityIncidents,
      complianceViolations
    };
  }

  /**
   * 导出为JSON格式
   */
  private async exportToJSON(
    auditData: AuditEvent[],
    statistics: ReportStatistics,
    options: ExportOptions
  ): Promise<ExportResult> {
    const fileName = this.generateFileName('audit_report', 'json', options);
    const filePath = path.join(this.exportDir, fileName);

    const exportData = {
      metadata: {
        exportTime: new Date(),
        reportType: options.reportType,
        timeRange: statistics.timeRange,
        recordCount: auditData.length
      },
      statistics: options.includeMetadata ? statistics : undefined,
      events: auditData
    };

    await fs.writeFile(filePath, JSON.stringify(exportData, null, 2));

    const stats = await fs.stat(filePath);

    return {
      success: true,
      filePath,
      fileName,
      fileSize: stats.size,
      recordCount: auditData.length,
      exportTime: new Date(),
      expiresAt: new Date(Date.now() + this.maxFileAge)
    };
  }

  /**
   * 导出为CSV格式
   */
  private async exportToCSV(
    auditData: AuditEvent[],
    options: ExportOptions
  ): Promise<ExportResult> {
    const fileName = this.generateFileName('audit_report', 'csv', options);
    const filePath = path.join(this.exportDir, fileName);

    // 定义CSV字段
    const fields = [
      'id',
      'type',
      'severity',
      'timestamp',
      'userId',
      'ipAddress',
      'userAgent',
      'resource',
      'action',
      'result',
      'message'
    ];

    // 如果包含自定义字段
    if (options.customFields) {
      fields.push(...options.customFields);
    }

    const parser = new Parser({ fields });
    const csv = parser.parse(auditData);

    await fs.writeFile(filePath, csv);

    const stats = await fs.stat(filePath);

    return {
      success: true,
      filePath,
      fileName,
      fileSize: stats.size,
      recordCount: auditData.length,
      exportTime: new Date(),
      expiresAt: new Date(Date.now() + this.maxFileAge)
    };
  }

  /**
   * 导出为Excel格式
   */
  private async exportToExcel(
    auditData: AuditEvent[],
    statistics: ReportStatistics,
    options: ExportOptions
  ): Promise<ExportResult> {
    const fileName = this.generateFileName('audit_report', 'xlsx', options);
    const filePath = path.join(this.exportDir, fileName);

    const workbook = new ExcelJS.Workbook();
    
    // 创建审计数据工作表
    const auditSheet = workbook.addWorksheet('审计数据');
    
    // 设置列标题
    auditSheet.columns = [
      { header: 'ID', key: 'id', width: 15 },
      { header: '事件类型', key: 'type', width: 20 },
      { header: '严重程度', key: 'severity', width: 12 },
      { header: '时间戳', key: 'timestamp', width: 20 },
      { header: '用户ID', key: 'userId', width: 15 },
      { header: 'IP地址', key: 'ipAddress', width: 15 },
      { header: '用户代理', key: 'userAgent', width: 30 },
      { header: '资源', key: 'resource', width: 20 },
      { header: '操作', key: 'action', width: 15 },
      { header: '结果', key: 'result', width: 10 },
      { header: '消息', key: 'message', width: 40 }
    ];

    // 添加数据
    auditSheet.addRows(auditData);

    // 如果包含统计信息，创建统计工作表
    if (options.includeMetadata) {
      const statsSheet = workbook.addWorksheet('统计信息');
      
      // 添加统计数据
      statsSheet.addRow(['总事件数', statistics.totalEvents]);
      statsSheet.addRow(['安全事件数', statistics.securityIncidents]);
      statsSheet.addRow(['合规违规数', statistics.complianceViolations]);
      statsSheet.addRow([]);
      
      // 按类型统计
      statsSheet.addRow(['事件类型统计']);
      for (const [type, count] of Object.entries(statistics.eventsByType)) {
        statsSheet.addRow([type, count]);
      }
    }

    await workbook.xlsx.writeFile(filePath);

    const stats = await fs.stat(filePath);

    return {
      success: true,
      filePath,
      fileName,
      fileSize: stats.size,
      recordCount: auditData.length,
      exportTime: new Date(),
      expiresAt: new Date(Date.now() + this.maxFileAge)
    };
  }

  /**
   * 导出为PDF格式
   */
  private async exportToPDF(
    auditData: AuditEvent[],
    statistics: ReportStatistics,
    options: ExportOptions
  ): Promise<ExportResult> {
    const fileName = this.generateFileName('audit_report', 'pdf', options);
    const filePath = path.join(this.exportDir, fileName);

    return new Promise((resolve, reject) => {
      const doc = new PDFDocument();
      const stream = createWriteStream(filePath);
      
      doc.pipe(stream);

      // 添加标题
      doc.fontSize(20).text('审计报告', { align: 'center' });
      doc.moveDown();

      // 添加报告信息
      doc.fontSize(12);
      doc.text(`报告类型: ${options.reportType}`);
      doc.text(`时间范围: ${options.startDate.toISOString()} - ${options.endDate.toISOString()}`);
      doc.text(`生成时间: ${new Date().toISOString()}`);
      doc.text(`记录总数: ${auditData.length}`);
      doc.moveDown();

      // 添加统计信息
      if (options.includeMetadata) {
        doc.fontSize(16).text('统计摘要');
        doc.fontSize(12);
        doc.text(`总事件数: ${statistics.totalEvents}`);
        doc.text(`安全事件数: ${statistics.securityIncidents}`);
        doc.text(`合规违规数: ${statistics.complianceViolations}`);
        doc.moveDown();

        // 事件类型统计
        doc.text('事件类型分布:');
        for (const [type, count] of Object.entries(statistics.eventsByType)) {
          doc.text(`  ${type}: ${count}`);
        }
        doc.moveDown();
      }

      // 添加审计数据（限制数量以避免PDF过大）
      const maxRecords = 100;
      const displayData = auditData.slice(0, maxRecords);
      
      doc.fontSize(16).text('审计事件详情');
      doc.fontSize(10);
      
      for (const event of displayData) {
        doc.text(`${event.timestamp} | ${event.type} | ${event.severity} | ${event.message}`);
      }

      if (auditData.length > maxRecords) {
        doc.text(`... 还有 ${auditData.length - maxRecords} 条记录未显示`);
      }

      doc.end();

      stream.on('finish', async () => {
        try {
          const stats = await fs.stat(filePath);
          resolve({
            success: true,
            filePath,
            fileName,
            fileSize: stats.size,
            recordCount: auditData.length,
            exportTime: new Date(),
            expiresAt: new Date(Date.now() + this.maxFileAge)
          });
        } catch (error) {
          reject(error);
        }
      });

      stream.on('error', reject);
    });
  }

  /**
   * 导出为XML格式
   */
  private async exportToXML(
    auditData: AuditEvent[],
    statistics: ReportStatistics,
    options: ExportOptions
  ): Promise<ExportResult> {
    const fileName = this.generateFileName('audit_report', 'xml', options);
    const filePath = path.join(this.exportDir, fileName);

    let xml = '<?xml version="1.0" encoding="UTF-8"?>\n';
    xml += '<auditReport>\n';
    xml += `  <metadata>\n`;
    xml += `    <exportTime>${new Date().toISOString()}</exportTime>\n`;
    xml += `    <reportType>${options.reportType}</reportType>\n`;
    xml += `    <recordCount>${auditData.length}</recordCount>\n`;
    xml += `  </metadata>\n`;

    if (options.includeMetadata) {
      xml += `  <statistics>\n`;
      xml += `    <totalEvents>${statistics.totalEvents}</totalEvents>\n`;
      xml += `    <securityIncidents>${statistics.securityIncidents}</securityIncidents>\n`;
      xml += `    <complianceViolations>${statistics.complianceViolations}</complianceViolations>\n`;
      xml += `  </statistics>\n`;
    }

    xml += `  <events>\n`;
    for (const event of auditData) {
      xml += `    <event>\n`;
      xml += `      <id>${this.escapeXml(event.id)}</id>\n`;
      xml += `      <type>${this.escapeXml(event.type)}</type>\n`;
      xml += `      <severity>${this.escapeXml(event.severity)}</severity>\n`;
      xml += `      <timestamp>${event.timestamp.toISOString()}</timestamp>\n`;
      xml += `      <userId>${this.escapeXml(event.userId || '')}</userId>\n`;
      xml += `      <message>${this.escapeXml(event.message || '')}</message>\n`;
      xml += `    </event>\n`;
    }
    xml += `  </events>\n`;
    xml += '</auditReport>\n';

    await fs.writeFile(filePath, xml);

    const stats = await fs.stat(filePath);

    return {
      success: true,
      filePath,
      fileName,
      fileSize: stats.size,
      recordCount: auditData.length,
      exportTime: new Date(),
      expiresAt: new Date(Date.now() + this.maxFileAge)
    };
  }

  /**
   * 生成文件名
   */
  private generateFileName(
    prefix: string,
    extension: string,
    options: ExportOptions
  ): string {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const reportType = options.reportType.replace(/_/g, '-');
    return `${prefix}_${reportType}_${timestamp}.${extension}`;
  }

  /**
   * XML转义
   */
  private escapeXml(text: string): string {
    return text
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#39;');
  }

  /**
   * 定期清理过期文件
   */
  private scheduleCleanup(): void {
    setInterval(async () => {
      try {
        const files = await this.getExportFiles();
        const now = new Date();

        for (const file of files) {
          if (file.expiresAt < now) {
            await this.deleteExportFile(file.fileName);
          }
        }
      } catch (error) {
        logger.error('清理过期导出文件失败', { error });
      }
    }, 60 * 60 * 1000); // 每小时清理一次
  }
}

// 创建服务实例
export const auditExportService = new AuditExportService();
