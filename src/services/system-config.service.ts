/**
 * 系统配置服务
 * 实现动态系统配置管理，支持在线修改系统参数
 */

import { prisma } from '@/config/database';
import { logger } from '@/config/logger';
import { cacheService } from './cache.service';
import { v4 as uuidv4 } from 'uuid';

/**
 * 配置项接口
 */
export interface ConfigItem {
  id: string;
  key: string;
  value: any;
  type: 'string' | 'number' | 'boolean' | 'json' | 'array';
  category: string;
  description: string;
  isPublic: boolean;
  isRequired: boolean;
  defaultValue: any;
  validationRules?: {
    min?: number;
    max?: number;
    pattern?: string;
    enum?: any[];
  };
  metadata?: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * 配置变更历史接口
 */
export interface ConfigChangeHistory {
  id: string;
  configKey: string;
  oldValue: any;
  newValue: any;
  changedBy: string;
  changeReason?: string;
  createdAt: Date;
}

export class SystemConfigService {

  /**
   * 获取配置项
   */
  async getConfig(key: string): Promise<any> {
    try {
      // 先从缓存获取
      const cachedValue = await cacheService.get(`config:${key}`);
      if (cachedValue !== null) {
        return this.parseConfigValue(cachedValue);
      }

      // 从数据库获取
      const config = await prisma.systemConfig.findUnique({
        where: { key }
      });

      if (!config) {
        return null;
      }

      // 缓存配置值
      await cacheService.set(`config:${key}`, config.value, 3600); // 缓存1小时

      return this.parseConfigValue(config.value, config.type);

    } catch (error) {
      logger.error('获取配置失败', { error, key });
      return null;
    }
  }

  /**
   * 获取多个配置项
   */
  async getConfigs(keys: string[]): Promise<Record<string, any>> {
    try {
      const configs: Record<string, any> = {};

      // 批量获取配置
      const configItems = await prisma.systemConfig.findMany({
        where: {
          key: { in: keys }
        }
      });

      configItems.forEach(config => {
        configs[config.key] = this.parseConfigValue(config.value, config.type);
      });

      return configs;

    } catch (error) {
      logger.error('批量获取配置失败', { error, keys });
      return {};
    }
  }

  /**
   * 获取分类配置
   */
  async getConfigsByCategory(category: string): Promise<Record<string, any>> {
    try {
      const configItems = await prisma.systemConfig.findMany({
        where: { category }
      });

      const configs: Record<string, any> = {};
      configItems.forEach(config => {
        configs[config.key] = this.parseConfigValue(config.value, config.type);
      });

      return configs;

    } catch (error) {
      logger.error('获取分类配置失败', { error, category });
      return {};
    }
  }

  /**
   * 设置配置项
   */
  async setConfig(
    key: string,
    value: any,
    changedBy: string,
    changeReason?: string
  ): Promise<void> {
    try {
      // 获取当前配置
      const currentConfig = await prisma.systemConfig.findUnique({
        where: { key }
      });

      if (!currentConfig) {
        throw new Error(`配置项不存在: ${key}`);
      }

      // 验证配置值
      this.validateConfigValue(value, currentConfig);

      // 序列化配置值
      const serializedValue = this.serializeConfigValue(value, currentConfig.type);

      // 记录变更历史
      if (currentConfig.value !== serializedValue) {
        await this.recordConfigChange(
          key,
          currentConfig.value,
          serializedValue,
          changedBy,
          changeReason
        );
      }

      // 更新配置
      await prisma.systemConfig.update({
        where: { key },
        data: {
          value: serializedValue,
          updatedAt: new Date()
        }
      });

      // 清除缓存
      await cacheService.del(`config:${key}`);

      // 触发配置变更事件
      await this.triggerConfigChangeEvent(key, value, changedBy);

      logger.info('配置更新成功', { key, changedBy });

    } catch (error) {
      logger.error('设置配置失败', { error, key, changedBy });
      throw error;
    }
  }

  /**
   * 批量设置配置
   */
  async setConfigs(
    configs: Record<string, any>,
    changedBy: string,
    changeReason?: string
  ): Promise<void> {
    try {
      // 使用事务批量更新
      await prisma.$transaction(async (tx) => {
        for (const [key, value] of Object.entries(configs)) {
          const currentConfig = await tx.systemConfig.findUnique({
            where: { key }
          });

          if (!currentConfig) {
            logger.warn('跳过不存在的配置项', { key });
            continue;
          }

          // 验证配置值
          this.validateConfigValue(value, currentConfig);

          // 序列化配置值
          const serializedValue = this.serializeConfigValue(value, currentConfig.type);

          // 记录变更历史
          if (currentConfig.value !== serializedValue) {
            await this.recordConfigChange(
              key,
              currentConfig.value,
              serializedValue,
              changedBy,
              changeReason
            );
          }

          // 更新配置
          await tx.systemConfig.update({
            where: { key },
            data: {
              value: serializedValue,
              updatedAt: new Date()
            }
          });

          // 清除缓存
          await cacheService.del(`config:${key}`);
        }
      });

      // 触发批量配置变更事件
      await this.triggerBatchConfigChangeEvent(configs, changedBy);

      logger.info('批量配置更新成功', { count: Object.keys(configs).length, changedBy });

    } catch (error) {
      logger.error('批量设置配置失败', { error, changedBy });
      throw error;
    }
  }

  /**
   * 创建配置项
   */
  async createConfig(config: Omit<ConfigItem, 'id' | 'createdAt' | 'updatedAt'>): Promise<ConfigItem> {
    try {
      const configItem = await prisma.systemConfig.create({
        data: {
          id: uuidv4(),
          key: config.key,
          value: this.serializeConfigValue(config.value, config.type),
          type: config.type,
          category: config.category,
          description: config.description,
          isPublic: config.isPublic,
          isRequired: config.isRequired,
          defaultValue: this.serializeConfigValue(config.defaultValue, config.type),
          validationRules: config.validationRules || {},
          metadata: config.metadata || {}
        }
      });

      logger.info('配置项创建成功', { key: config.key });

      return configItem as ConfigItem;

    } catch (error) {
      logger.error('创建配置项失败', { error, key: config.key });
      throw error;
    }
  }

  /**
   * 获取配置变更历史
   */
  async getConfigHistory(
    key?: string,
    limit: number = 50,
    offset: number = 0
  ): Promise<ConfigChangeHistory[]> {
    try {
      const where = key ? { configKey: key } : {};

      const history = await prisma.configChangeHistory.findMany({
        where,
        orderBy: { createdAt: 'desc' },
        take: limit,
        skip: offset
      });

      return history as ConfigChangeHistory[];

    } catch (error) {
      logger.error('获取配置变更历史失败', { error, key });
      return [];
    }
  }

  /**
   * 解析配置值
   */
  private parseConfigValue(value: string, type?: string): any {
    if (value === null || value === undefined) {
      return null;
    }

    try {
      switch (type) {
        case 'number':
          return Number(value);
        case 'boolean':
          return value === 'true' || value === true;
        case 'json':
        case 'array':
          return JSON.parse(value);
        case 'string':
        default:
          return value;
      }
    } catch (error) {
      logger.warn('解析配置值失败，返回原始值', { value, type, error });
      return value;
    }
  }

  /**
   * 序列化配置值
   */
  private serializeConfigValue(value: any, type: string): string {
    if (value === null || value === undefined) {
      return '';
    }

    try {
      switch (type) {
        case 'json':
        case 'array':
          return JSON.stringify(value);
        case 'boolean':
          return String(Boolean(value));
        case 'number':
          return String(Number(value));
        case 'string':
        default:
          return String(value);
      }
    } catch (error) {
      logger.warn('序列化配置值失败，返回字符串', { value, type, error });
      return String(value);
    }
  }

  /**
   * 验证配置值
   */
  private validateConfigValue(value: any, config: any): void {
    const rules = config.validationRules || {};

    // 必需字段检查
    if (config.isRequired && (value === null || value === undefined || value === '')) {
      throw new Error(`配置项 ${config.key} 是必需的`);
    }

    // 类型检查
    switch (config.type) {
      case 'number':
        if (isNaN(Number(value))) {
          throw new Error(`配置项 ${config.key} 必须是数字`);
        }
        const numValue = Number(value);
        if (rules.min !== undefined && numValue < rules.min) {
          throw new Error(`配置项 ${config.key} 不能小于 ${rules.min}`);
        }
        if (rules.max !== undefined && numValue > rules.max) {
          throw new Error(`配置项 ${config.key} 不能大于 ${rules.max}`);
        }
        break;

      case 'string':
        const strValue = String(value);
        if (rules.pattern && !new RegExp(rules.pattern).test(strValue)) {
          throw new Error(`配置项 ${config.key} 格式不正确`);
        }
        if (rules.min !== undefined && strValue.length < rules.min) {
          throw new Error(`配置项 ${config.key} 长度不能小于 ${rules.min}`);
        }
        if (rules.max !== undefined && strValue.length > rules.max) {
          throw new Error(`配置项 ${config.key} 长度不能大于 ${rules.max}`);
        }
        break;

      case 'boolean':
        if (typeof value !== 'boolean' && value !== 'true' && value !== 'false') {
          throw new Error(`配置项 ${config.key} 必须是布尔值`);
        }
        break;

      case 'json':
      case 'array':
        try {
          if (typeof value === 'string') {
            JSON.parse(value);
          }
        } catch (error) {
          throw new Error(`配置项 ${config.key} 必须是有效的JSON格式`);
        }
        break;
    }

    // 枚举值检查
    if (rules.enum && !rules.enum.includes(value)) {
      throw new Error(`配置项 ${config.key} 必须是以下值之一: ${rules.enum.join(', ')}`);
    }
  }

  /**
   * 记录配置变更历史
   */
  private async recordConfigChange(
    configKey: string,
    oldValue: any,
    newValue: any,
    changedBy: string,
    changeReason?: string
  ): Promise<void> {
    try {
      await prisma.configChangeHistory.create({
        data: {
          id: uuidv4(),
          configKey,
          oldValue,
          newValue,
          changedBy,
          changeReason,
          createdAt: new Date()
        }
      });
    } catch (error) {
      logger.error('记录配置变更历史失败', { error, configKey });
    }
  }

  /**
   * 触发配置变更事件
   */
  private async triggerConfigChangeEvent(
    key: string,
    value: any,
    changedBy: string
  ): Promise<void> {
    try {
      // 这里可以实现配置变更的事件通知
      // 例如：发送WebSocket消息、调用Webhook等
      logger.info('配置变更事件触发', { key, changedBy });
    } catch (error) {
      logger.error('触发配置变更事件失败', { error, key });
    }
  }

  /**
   * 触发批量配置变更事件
   */
  private async triggerBatchConfigChangeEvent(
    configs: Record<string, any>,
    changedBy: string
  ): Promise<void> {
    try {
      logger.info('批量配置变更事件触发', { 
        keys: Object.keys(configs), 
        changedBy 
      });
    } catch (error) {
      logger.error('触发批量配置变更事件失败', { error });
    }
  }
}

// 导出单例实例
export const systemConfigService = new SystemConfigService();
