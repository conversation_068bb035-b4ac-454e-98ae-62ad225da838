/**
 * SAML 2.0 身份提供商服务
 * 实现完整的SAML IdP功能
 */

import { v4 as uuidv4 } from 'uuid';
import * as xml2js from 'xml2js';
import { create } from 'xmlbuilder2';
import { prisma } from '@/config/database';
import { config, getServerUrl } from '@/config';
import { logger, logAuditEvent } from '@/config/logger';
import { cacheService } from './cache.service';
import {
  SAMLConfig,
  SAMLServiceProvider,
  SAMLRequest,
  SAMLResponse,
  SAMLAssertion,
  SAMLAttribute,
  SAMLMetadata,
  SAMLLogoutRequest,
  SAMLLogoutResponse,
  SAMLStatusCodes,
  NameIdFormats,
  AuthnContextClassRefs,
  SAMLBindings
} from '@/types/saml';
import {
  generateSAMLId,
  generateTimestamp,
  isTimestampValid,
  base64UrlEncode,
  base64UrlDecode,
  deflateString,
  inflateString,
  signXML,
  verifyXMLSignature,
  encryptXMLElement,
  formatCertificateForXML,
  generateTestCertificate
} from '@/utils/saml';

export class SAMLService {
  private samlConfig: SAMLConfig;

  constructor() {
    this.samlConfig = this.initializeSAMLConfig();
  }

  /**
   * 初始化SAML配置
   */
  private initializeSAMLConfig(): SAMLConfig {
    const baseUrl = getServerUrl();
    
    return {
      issuer: config.saml.issuer || baseUrl,
      ssoServiceUrl: `${baseUrl}/saml/sso`,
      sloServiceUrl: `${baseUrl}/saml/slo`,
      metadataUrl: `${baseUrl}/saml/metadata`,
      
      // 证书配置（生产环境应从文件或密钥管理服务获取）
      certificate: this.loadCertificate(),
      privateKey: this.loadPrivateKey(),
      
      // 签名和加密配置
      signatureAlgorithm: 'http://www.w3.org/2001/04/xmldsig-more#rsa-sha256',
      digestAlgorithm: 'http://www.w3.org/2001/04/xmlenc#sha256',
      encryptionAlgorithm: 'http://www.w3.org/2001/04/xmlenc#aes256-cbc',
      
      // 断言配置
      assertionLifetime: 300, // 5分钟
      clockSkew: 300, // 5分钟
      
      // 默认属性映射
      attributeMapping: {
        'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress': 'email',
        'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/givenname': 'firstName',
        'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/surname': 'lastName',
        'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name': 'nickname',
        'http://schemas.microsoft.com/ws/2008/06/identity/claims/role': 'roles'
      },
      
      // 默认名称ID格式
      nameIdFormat: NameIdFormats.EMAIL,
      
      // 默认绑定类型
      binding: 'HTTP-POST'
    };
  }

  /**
   * 加载证书
   */
  private loadCertificate(): string {
    try {
      // 在生产环境中，应该从安全的位置加载证书
      // 这里为了演示，使用一个简单的测试证书
      return `-----BEGIN CERTIFICATE-----
MIIDXTCCAkWgAwIBAgIJAKoK/heBjcOuMA0GCSqGSIb3DQEBBQUAMEUxCzAJBgNV
BAYTAkFVMRMwEQYDVQQIDApTb21lLVN0YXRlMSEwHwYDVQQKDBhJbnRlcm5ldCBX
aWRnaXRzIFB0eSBMdGQwHhcNMTIwOTEyMjE1MjAyWhcNMTUwOTEyMjE1MjAyWjBF
MQswCQYDVQQGEwJBVTETMBEGA1UECAwKU29tZS1TdGF0ZTEhMB8GA1UECgwYSW50
ZXJuZXQgV2lkZ2l0cyBQdHkgTHRkMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIB
CgKCAQEAwuqTiuGqfzf+zZiSqnWo268MaFrVfaNyegDhLaXiusb24teOFYGPiAhb
5jbM1KyYTBSWrfmps98ECS9dg+XhqJzV4t9v5rxIcIVoEiuRkrM7oQXPP9Bnvs4X
9B1gx7R3ivBzjHRDmvJfLu/Yxf4Nnk+hxqyNHyeHAacmjbMtgQy+99kkbDFpkqhL
ec6vEeXcjRdnQbpKVWw0nANiw52kqHyFRgxsvratisQitVlFRlVhbDsO40O2cINh
fqRIFiHhrfnuJrXhqOjdpCeAHPsGNNGwOBPiw5uVBrwVHyS1YRWN4AcJGjVel8CZ
2MbJ7bgk9Pmry2u2VfVyNA6w0qNWlwIDAQABo1AwTjAdBgNVHQ4EFgQUhKs/VJ3I
WyKwrl0KEyDMgl3IbrMwHwYDVR0jBBgwFoAUhKs/VJ3IWyKwrl0KEyDMgl3IbrMw
DAYDVR0TBAUwAwEB/zANBgkqhkiG9w0BAQUFAAOCAQEAcGKQNMfHgpNuaP9YdGfx
Zj4fHl3PjK9PMnS9qdN2Z+g734Vql+R6jBXHXq1+atUFKPhjxAY6/GnQf40Qy/bw
OpoHBT7wcB3aLVKRJapGigkTZhHOojg/6A5f8Z0VYrgk7HxtsQOfMLnBuU4+YrOD
3ddNTMqez4irzMXVmLJPaGS4Zg4UlcXGOHwjfmEbBfmpubWuRrSw+vALxI4B4R5c
s+BA6fXTQQXh88M2A8BhQBwqVSIyUFcCLNVRSQhcbzBYcMp1bnw2xEFzsgHO9A1R
JpQjHX7K8C4CWQsRGf0TjeddMdVXVOWrK88LnyNjAQGy+qmupS5B3HRmudHRiCXj
vQ==
-----END CERTIFICATE-----`;
    } catch (error) {
      logger.error('加载SAML证书失败', { error });
      throw new Error('SAML证书配置错误');
    }
  }

  /**
   * 加载私钥
   */
  private loadPrivateKey(): string {
    try {
      // 在生产环境中，应该从安全的位置加载私钥
      return `-----BEGIN PRIVATE KEY-----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-----END PRIVATE KEY-----`;
    } catch (error) {
      logger.error('加载SAML私钥失败', { error });
      throw new Error('SAML私钥配置错误');
    }
  }

  /**
   * 处理SAML认证请求
   */
  async handleAuthenticationRequest(
    samlRequest: string,
    relayState?: string,
    binding: 'HTTP-POST' | 'HTTP-Redirect' = 'HTTP-POST'
  ): Promise<{ requestId: string; spEntityId: string; acsUrl: string }> {
    try {
      // 解析SAML请求
      const parsedRequest = await this.parseSAMLRequest(samlRequest, binding);
      
      // 验证服务提供商
      const sp = await this.validateServiceProvider(parsedRequest.issuer);
      if (!sp) {
        throw new Error('未知的服务提供商');
      }

      // 验证请求
      await this.validateAuthenticationRequest(parsedRequest, sp);

      // 缓存请求信息（用于后续生成响应）
      await cacheService.setOAuthState(parsedRequest.id, {
        requestId: parsedRequest.id,
        spEntityId: parsedRequest.issuer,
        acsUrl: parsedRequest.acsUrl,
        relayState,
        binding,
        timestamp: new Date().toISOString()
      });

      logger.info('SAML认证请求处理成功', {
        requestId: parsedRequest.id,
        spEntityId: parsedRequest.issuer,
        acsUrl: parsedRequest.acsUrl
      });

      return {
        requestId: parsedRequest.id,
        spEntityId: parsedRequest.issuer,
        acsUrl: parsedRequest.acsUrl
      };

    } catch (error) {
      logger.error('SAML认证请求处理失败', { error });
      throw error;
    }
  }

  /**
   * 生成SAML响应
   */
  async generateAuthenticationResponse(
    requestId: string,
    userId: string,
    attributes?: Record<string, any>
  ): Promise<{ samlResponse: string; relayState?: string }> {
    try {
      // 获取缓存的请求信息
      const requestInfo = await cacheService.getOAuthState(requestId);
      if (!requestInfo) {
        throw new Error('请求信息已过期或不存在');
      }

      // 获取用户信息
      const user = await prisma.user.findUnique({
        where: { id: userId }
      });

      if (!user) {
        throw new Error('用户不存在');
      }

      // 获取服务提供商信息
      const sp = await this.validateServiceProvider(requestInfo.spEntityId);
      if (!sp) {
        throw new Error('服务提供商不存在');
      }

      // 生成SAML断言
      const assertion = this.generateAssertion(user, sp, requestId, attributes);

      // 生成SAML响应
      const response = this.generateSAMLResponse(assertion, requestInfo, sp);

      // 签名响应
      const signedResponse = signXML(
        response,
        this.samlConfig.privateKey,
        this.samlConfig.certificate,
        this.samlConfig.signatureAlgorithm
      );

      // 记录审计日志
      logAuditEvent('saml_sso_success', 'authentication', userId, {
        spEntityId: requestInfo.spEntityId,
        requestId,
        acsUrl: requestInfo.acsUrl
      });

      logger.info('SAML响应生成成功', {
        userId,
        spEntityId: requestInfo.spEntityId,
        requestId
      });

      return {
        samlResponse: Buffer.from(signedResponse).toString('base64'),
        relayState: requestInfo.relayState
      };

    } catch (error) {
      logger.error('SAML响应生成失败', { error, requestId, userId });
      throw error;
    }
  }

  /**
   * 生成IdP元数据
   */
  generateMetadata(): string {
    try {
      const baseUrl = getServerUrl();
      // 简化证书处理，直接使用测试证书内容
      const certificate = 'MIIDXTCCAkWgAwIBAgIJAKoK/heBjcOuMA0GCSqGSIb3DQEBBQUAMEUxCzAJBgNVBAYTAkFVMRMwEQYDVQQIDApTb21lLVN0YXRlMSEwHwYDVQQKDBhJbnRlcm5ldCBXaWRnaXRzIFB0eSBMdGQwHhcNMTIwOTEyMjE1MjAyWhcNMTUwOTEyMjE1MjAyWjBFMQswCQYDVQQGEwJBVTETMBEGA1UECAwKU29tZS1TdGF0ZTEhMB8GA1UECgwYSW50ZXJuZXQgV2lkZ2l0cyBQdHkgTHRkMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAwuqTiuGqfzf+zZiSqnWo268MaFrVfaNyegDhLaXiusb24teOFYGPiAhb5jbM1KyYTBSWrfmps98ECS9dg+XhqJzV4t9v5rxIcIVoEiuRkrM7oQXPP9Bnvs4X9B1gx7R3ivBzjHRDmvJfLu/Yxf4Nnk+hxqyNHyeHAacmjbMtgQy+99kkbDFpkqhLec6vEeXcjRdnQbpKVWw0nANiw52kqHyFRgxsvratisQitVlFRlVhbDsO40O2cINhfqRIFiHhrfnuJrXhqOjdpCeAHPsGNNGwOBPiw5uVBrwVHyS1YRWN4AcJGjVel8CZ2MbJ7bgk9Pmry2u2VfVyNA6w0qNWlwIDAQABo1AwTjAdBgNVHQ4EFgQUhKs/VJ3IWyKwrl0KEyDMgl3IbrMwHwYDVR0jBBgwFoAUhKs/VJ3IWyKwrl0KEyDMgl3IbrMwDAYDVR0TBAUwAwEB/zANBgkqhkiG9w0BAQUFAAOCAQEAcGKQNMfHgpNuaP9YdGfxZj4fHl3PjK9PMnS9qdN2Z+g734Vql+R6jBXHXq1+atUFKPhjxAY6/GnQf40Qy/bwOpoHBT7wcB3aLVKRJapGigkTZhHOojg/6A5f8Z0VYrgk7HxtsQOfMLnBuU4+YrOD3ddNTMqez4irzMXVmLJPaGS4Zg4UlcXGOHwjfmEbBfmpubWuRrSw+vALxI4B4R5cs+BA6fXTQQXh88M2A8BhQBwqVSIyUFcCLNVRSQhcbzBYcMp1bnw2xEFzsgHO9A1RJpQjHX7K8C4CWQsRGf0TjeddMdVXVOWrK88LnyNjAQGy+qmupS5B3HRmudHRiCXjvQ==';

      const metadata = create({ version: '1.0', encoding: 'UTF-8' })
        .ele('md:EntityDescriptor', {
          'xmlns:md': 'urn:oasis:names:tc:SAML:2.0:metadata',
          'xmlns:ds': 'http://www.w3.org/2000/09/xmldsig#',
          'entityID': this.samlConfig.issuer,
          'validUntil': generateTimestamp(86400 * 365), // 1年有效期
          'cacheDuration': 'PT1H' // 缓存1小时
        })
          .ele('md:IDPSSODescriptor', {
            'protocolSupportEnumeration': 'urn:oasis:names:tc:SAML:2.0:protocol',
            'WantAuthnRequestsSigned': 'false'
          })
            // 签名证书
            .ele('md:KeyDescriptor', { 'use': 'signing' })
              .ele('ds:KeyInfo')
                .ele('ds:X509Data')
                  .ele('ds:X509Certificate')
                  .txt(certificate).up()
                .up()
              .up()
            .up()
            // 加密证书
            .ele('md:KeyDescriptor', { 'use': 'encryption' })
              .ele('ds:KeyInfo')
                .ele('ds:X509Data')
                  .ele('ds:X509Certificate')
                  .txt(certificate).up()
                .up()
              .up()
            .up()
            // 支持的名称ID格式
            .ele('md:NameIDFormat')
            .txt(NameIdFormats.EMAIL).up()
            .ele('md:NameIDFormat')
            .txt(NameIdFormats.TRANSIENT).up()
            .ele('md:NameIDFormat')
            .txt(NameIdFormats.PERSISTENT).up()
            // SSO服务端点
            .ele('md:SingleSignOnService', {
              'Binding': SAMLBindings.HTTP_POST,
              'Location': `${baseUrl}/saml/sso`
            }).up()
            .ele('md:SingleSignOnService', {
              'Binding': SAMLBindings.HTTP_REDIRECT,
              'Location': `${baseUrl}/saml/sso`
            }).up()
            // SLO服务端点
            .ele('md:SingleLogoutService', {
              'Binding': SAMLBindings.HTTP_POST,
              'Location': `${baseUrl}/saml/slo`
            }).up()
            .ele('md:SingleLogoutService', {
              'Binding': SAMLBindings.HTTP_REDIRECT,
              'Location': `${baseUrl}/saml/slo`
            }).up()
          .up()
        .up();

      const metadataXml = metadata.end({ prettyPrint: true });

      logger.info('SAML元数据生成成功');
      return metadataXml;

    } catch (error) {
      logger.error('SAML元数据生成失败', { error });
      throw new Error('元数据生成失败');
    }
  }

  /**
   * 解析SAML请求
   */
  private async parseSAMLRequest(
    samlRequest: string,
    binding: 'HTTP-POST' | 'HTTP-Redirect'
  ): Promise<SAMLRequest> {
    try {
      let xmlString: string;

      if (binding === 'HTTP-Redirect') {
        // HTTP-Redirect绑定：Base64解码 + 解压缩
        const compressed = Buffer.from(samlRequest, 'base64');
        xmlString = inflateString(compressed);
      } else {
        // HTTP-POST绑定：Base64解码
        xmlString = Buffer.from(samlRequest, 'base64').toString('utf8');
      }

      // 解析XML
      const parser = new xml2js.Parser({ explicitArray: false });
      const result = await parser.parseStringPromise(xmlString);

      const authnRequest = result['samlp:AuthnRequest'] || result.AuthnRequest;
      if (!authnRequest) {
        throw new Error('无效的SAML认证请求');
      }

      return {
        id: authnRequest.$.ID,
        issuer: authnRequest['saml:Issuer'] || authnRequest.Issuer,
        destination: authnRequest.$.Destination,
        issueInstant: authnRequest.$.IssueInstant,
        protocolBinding: authnRequest.$.ProtocolBinding,
        acsUrl: authnRequest.$.AssertionConsumerServiceURL,
        nameIdPolicy: authnRequest['samlp:NameIDPolicy'] ? {
          format: authnRequest['samlp:NameIDPolicy'].$.Format,
          allowCreate: authnRequest['samlp:NameIDPolicy'].$.AllowCreate === 'true'
        } : undefined,
        forceAuthn: authnRequest.$.ForceAuthn === 'true',
        isPassive: authnRequest.$.IsPassive === 'true'
      };

    } catch (error) {
      logger.error('SAML请求解析失败', { error });
      throw new Error('SAML请求格式错误');
    }
  }

  /**
   * 验证服务提供商
   */
  private async validateServiceProvider(entityId: string): Promise<SAMLServiceProvider | null> {
    try {
      // 从数据库查找服务提供商配置
      const application = await prisma.application.findFirst({
        where: {
          clientId: entityId,
          isActive: true
        }
      });

      if (!application || !application.samlConfig) {
        return null;
      }

      const samlConfig = application.samlConfig as any;

      return {
        id: application.id,
        entityId: application.clientId,
        name: application.name,
        description: application.description,
        acsUrl: samlConfig.acsUrl,
        sloUrl: samlConfig.sloUrl,
        certificate: samlConfig.certificate,
        wantAssertionsSigned: samlConfig.wantAssertionsSigned || false,
        wantNameId: samlConfig.wantNameId !== false,
        signMetadata: samlConfig.signMetadata || false,
        requiredAttributes: samlConfig.requiredAttributes || [],
        optionalAttributes: samlConfig.optionalAttributes || [],
        isActive: application.isActive,
        createdAt: application.createdAt,
        updatedAt: application.updatedAt
      };

    } catch (error) {
      logger.error('验证服务提供商失败', { error, entityId });
      return null;
    }
  }

  /**
   * 验证认证请求
   */
  private async validateAuthenticationRequest(
    request: SAMLRequest,
    sp: SAMLServiceProvider
  ): Promise<void> {
    // 验证时间戳
    if (!isTimestampValid(request.issueInstant, this.samlConfig.clockSkew)) {
      throw new Error('请求时间戳无效');
    }

    // 验证目标URL
    if (request.destination !== this.samlConfig.ssoServiceUrl) {
      throw new Error('目标URL不匹配');
    }

    // 验证ACS URL
    if (request.acsUrl !== sp.acsUrl) {
      throw new Error('ACS URL不匹配');
    }

    // 验证协议绑定
    const supportedBindings = [SAMLBindings.HTTP_POST, SAMLBindings.HTTP_REDIRECT];
    if (!supportedBindings.includes(request.protocolBinding as any)) {
      throw new Error('不支持的协议绑定');
    }
  }

  /**
   * 生成SAML断言
   */
  private generateAssertion(
    user: any,
    sp: SAMLServiceProvider,
    requestId: string,
    customAttributes?: Record<string, any>
  ): string {
    try {
      const assertionId = generateSAMLId();
      const issueInstant = generateTimestamp();
      const notBefore = generateTimestamp(-60); // 1分钟前
      const notOnOrAfter = generateTimestamp(this.samlConfig.assertionLifetime);
      const sessionIndex = generateSAMLId();

      // 构建属性
      const attributes = this.buildAttributes(user, sp, customAttributes);

      const assertion = create({ version: '1.0', encoding: 'UTF-8' })
        .ele('saml:Assertion', {
          'xmlns:saml': 'urn:oasis:names:tc:SAML:2.0:assertion',
          'ID': assertionId,
          'IssueInstant': issueInstant,
          'Version': '2.0'
        })
          // 发行者
          .ele('saml:Issuer', this.samlConfig.issuer).up()

          // 主题
          .ele('saml:Subject')
            .ele('saml:NameID', {
              'Format': this.samlConfig.nameIdFormat,
              'SPNameQualifier': sp.entityId
            })
            .txt(this.getNameIdValue(user, this.samlConfig.nameIdFormat)).up()
            .ele('saml:SubjectConfirmation', { 'Method': 'urn:oasis:names:tc:SAML:2.0:cm:bearer' })
              .ele('saml:SubjectConfirmationData', {
                'InResponseTo': requestId,
                'NotOnOrAfter': notOnOrAfter,
                'Recipient': sp.acsUrl
              }).up()
            .up()
          .up()

          // 条件
          .ele('saml:Conditions', {
            'NotBefore': notBefore,
            'NotOnOrAfter': notOnOrAfter
          })
            .ele('saml:AudienceRestriction')
              .ele('saml:Audience', sp.entityId).up()
            .up()
          .up()

          // 认证声明
          .ele('saml:AuthnStatement', {
            'AuthnInstant': issueInstant,
            'SessionIndex': sessionIndex
          })
            .ele('saml:AuthnContext')
              .ele('saml:AuthnContextClassRef', AuthnContextClassRefs.PASSWORD_PROTECTED_TRANSPORT).up()
            .up()
          .up();

      // 添加属性声明
      if (attributes.length > 0) {
        const attributeStatement = assertion.ele('saml:AttributeStatement');

        attributes.forEach(attr => {
          const attrElement = attributeStatement.ele('saml:Attribute', {
            'Name': attr.name,
            'NameFormat': attr.nameFormat || 'urn:oasis:names:tc:SAML:2.0:attrname-format:uri'
          });

          if (attr.friendlyName) {
            attrElement.att('FriendlyName', attr.friendlyName);
          }

          attr.values.forEach(value => {
            attrElement.ele('saml:AttributeValue', {
              'xmlns:xsi': 'http://www.w3.org/2001/XMLSchema-instance',
              'xsi:type': 'xs:string'
            }).txt(value);
          });
        });
      }

      return assertion.end({ prettyPrint: true });

    } catch (error) {
      logger.error('SAML断言生成失败', { error });
      throw new Error('断言生成失败');
    }
  }

  /**
   * 生成SAML响应
   */
  private generateSAMLResponse(
    assertion: string,
    requestInfo: any,
    sp: SAMLServiceProvider
  ): string {
    try {
      const responseId = generateSAMLId();
      const issueInstant = generateTimestamp();

      const response = create({ version: '1.0', encoding: 'UTF-8' })
        .ele('samlp:Response', {
          'xmlns:samlp': 'urn:oasis:names:tc:SAML:2.0:protocol',
          'xmlns:saml': 'urn:oasis:names:tc:SAML:2.0:assertion',
          'ID': responseId,
          'InResponseTo': requestInfo.requestId,
          'IssueInstant': issueInstant,
          'Destination': requestInfo.acsUrl,
          'Version': '2.0'
        })
          // 发行者
          .ele('saml:Issuer', this.samlConfig.issuer).up()

          // 状态
          .ele('samlp:Status')
            .ele('samlp:StatusCode', { 'Value': SAMLStatusCodes.SUCCESS }).up()
          .up();

      // 手动添加断言内容（简化处理）
      const responseXml = response.end({ prettyPrint: true });
      const assertionXml = assertion.replace('<?xml version="1.0" encoding="UTF-8"?>', '');
      const finalResponse = responseXml.replace('</samlp:Response>', assertionXml + '</samlp:Response>');

      return finalResponse;

      return response.end({ prettyPrint: true });

    } catch (error) {
      logger.error('SAML响应生成失败', { error });
      throw new Error('响应生成失败');
    }
  }

  /**
   * 构建用户属性
   */
  private buildAttributes(
    user: any,
    sp: SAMLServiceProvider,
    customAttributes?: Record<string, any>
  ): SAMLAttribute[] {
    const attributes: SAMLAttribute[] = [];

    // 基础属性
    const attributeMap = {
      'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress': user.email,
      'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/givenname': user.firstName,
      'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/surname': user.lastName,
      'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name': user.nickname || `${user.firstName} ${user.lastName}`.trim(),
      'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier': user.id
    };

    // 角色属性（暂时跳过，因为用户模型中没有roles关系）
    // TODO: 实现用户角色查询
    // if (user.roles && user.roles.length > 0) {
    //   const roleNames = user.roles.map((ur: any) => ur.role.name);
    //   attributeMap['http://schemas.microsoft.com/ws/2008/06/identity/claims/role'] = roleNames.join(',');
    // }

    // 添加自定义属性
    if (customAttributes) {
      Object.assign(attributeMap, customAttributes);
    }

    // 转换为SAML属性格式
    Object.entries(attributeMap).forEach(([name, value]) => {
      if (value !== null && value !== undefined) {
        // 检查是否为必需或可选属性
        const isRequired = sp.requiredAttributes.includes(name);
        const isOptional = sp.optionalAttributes.includes(name);

        if (isRequired || isOptional || sp.requiredAttributes.length === 0) {
          attributes.push({
            name,
            nameFormat: 'urn:oasis:names:tc:SAML:2.0:attrname-format:uri',
            values: Array.isArray(value) ? value : [String(value)]
          });
        }
      }
    });

    return attributes;
  }

  /**
   * 获取名称ID值
   */
  private getNameIdValue(user: any, format: string): string {
    switch (format) {
      case NameIdFormats.EMAIL:
        return user.email;
      case NameIdFormats.PERSISTENT:
        return user.id;
      case NameIdFormats.TRANSIENT:
        return generateSAMLId();
      default:
        return user.email;
    }
  }

  /**
   * 处理单点登出请求
   */
  async handleLogoutRequest(
    samlRequest: string,
    relayState?: string,
    binding: 'HTTP-POST' | 'HTTP-Redirect' = 'HTTP-POST'
  ): Promise<{ logoutResponse: string; relayState?: string }> {
    try {
      // 解析登出请求
      const logoutRequest = await this.parseLogoutRequest(samlRequest, binding);

      // 验证请求
      await this.validateLogoutRequest(logoutRequest);

      // 执行登出操作
      await this.performLogout(logoutRequest);

      // 生成登出响应
      const logoutResponse = this.generateLogoutResponse(logoutRequest);

      logger.info('SAML登出请求处理成功', {
        requestId: logoutRequest.id,
        nameId: logoutRequest.nameId.value
      });

      return {
        logoutResponse: Buffer.from(logoutResponse).toString('base64'),
        relayState
      };

    } catch (error) {
      logger.error('SAML登出请求处理失败', { error });
      throw error;
    }
  }

  /**
   * 解析登出请求
   */
  private async parseLogoutRequest(
    samlRequest: string,
    binding: 'HTTP-POST' | 'HTTP-Redirect'
  ): Promise<SAMLLogoutRequest> {
    try {
      let xmlString: string;

      if (binding === 'HTTP-Redirect') {
        const compressed = Buffer.from(samlRequest, 'base64');
        xmlString = inflateString(compressed);
      } else {
        xmlString = Buffer.from(samlRequest, 'base64').toString('utf8');
      }

      const parser = new xml2js.Parser({ explicitArray: false });
      const result = await parser.parseStringPromise(xmlString);

      const logoutRequest = result['samlp:LogoutRequest'] || result.LogoutRequest;
      if (!logoutRequest) {
        throw new Error('无效的SAML登出请求');
      }

      return {
        id: logoutRequest.$.ID,
        issuer: logoutRequest['saml:Issuer'] || logoutRequest.Issuer,
        destination: logoutRequest.$.Destination,
        issueInstant: logoutRequest.$.IssueInstant,
        nameId: {
          format: logoutRequest['saml:NameID'].$.Format,
          value: logoutRequest['saml:NameID']._
        },
        sessionIndex: logoutRequest['samlp:SessionIndex']
      };

    } catch (error) {
      logger.error('SAML登出请求解析失败', { error });
      throw new Error('登出请求格式错误');
    }
  }

  /**
   * 验证登出请求
   */
  private async validateLogoutRequest(request: SAMLLogoutRequest): Promise<void> {
    // 验证时间戳
    if (!isTimestampValid(request.issueInstant, this.samlConfig.clockSkew)) {
      throw new Error('请求时间戳无效');
    }

    // 验证目标URL
    if (request.destination !== this.samlConfig.sloServiceUrl) {
      throw new Error('目标URL不匹配');
    }
  }

  /**
   * 执行登出操作
   */
  private async performLogout(request: SAMLLogoutRequest): Promise<void> {
    try {
      // 根据名称ID查找用户
      let userId: string;

      if (request.nameId.format === NameIdFormats.EMAIL) {
        const user = await prisma.user.findUnique({
          where: { email: request.nameId.value }
        });
        if (!user) {
          throw new Error('用户不存在');
        }
        userId = user.id;
      } else if (request.nameId.format === NameIdFormats.PERSISTENT) {
        userId = request.nameId.value;
      } else {
        throw new Error('不支持的名称ID格式');
      }

      // 终止用户会话
      if (request.sessionIndex) {
        await prisma.session.updateMany({
          where: {
            userId,
            sessionToken: request.sessionIndex
          },
          data: {
            expiresAt: new Date() // 立即过期
          }
        });
      } else {
        // 终止所有会话
        await prisma.session.updateMany({
          where: { userId },
          data: {
            expiresAt: new Date()
          }
        });
      }

      // 记录审计日志
      logAuditEvent('saml_slo_success', 'authentication', userId, {
        requestId: request.id,
        issuer: request.issuer,
        sessionIndex: request.sessionIndex
      });

    } catch (error) {
      logger.error('执行SAML登出失败', { error });
      throw error;
    }
  }

  /**
   * 生成登出响应
   */
  private generateLogoutResponse(request: SAMLLogoutRequest): string {
    try {
      const responseId = generateSAMLId();
      const issueInstant = generateTimestamp();

      const response = create({ version: '1.0', encoding: 'UTF-8' })
        .ele('samlp:LogoutResponse', {
          'xmlns:samlp': 'urn:oasis:names:tc:SAML:2.0:protocol',
          'xmlns:saml': 'urn:oasis:names:tc:SAML:2.0:assertion',
          'ID': responseId,
          'InResponseTo': request.id,
          'IssueInstant': issueInstant,
          'Version': '2.0'
        })
          .ele('saml:Issuer', this.samlConfig.issuer).up()
          .ele('samlp:Status')
            .ele('samlp:StatusCode', { 'Value': SAMLStatusCodes.SUCCESS }).up()
          .up()
        .up();

      return response.end({ prettyPrint: true });

    } catch (error) {
      logger.error('SAML登出响应生成失败', { error });
      throw new Error('登出响应生成失败');
    }
  }
}

// 创建单例实例
export const samlService = new SAMLService();
