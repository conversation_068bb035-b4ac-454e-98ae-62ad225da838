/**
 * 用户行为分析服务
 * 提供用户行为追踪、模式分析、异常检测和预测分析功能
 */

import { logger } from '@/config/logger';
import { prisma } from '@/config/database';
import { cacheService } from '@/services/cache.service';
import { metricsCollector } from '@/services/metrics-collector.service';

/**
 * 用户行为事件类型
 */
export enum UserBehaviorEventType {
  LOGIN = 'login',
  LOGOUT = 'logout',
  PASSWORD_CHANGE = 'password_change',
  PROFILE_UPDATE = 'profile_update',
  APPLICATION_ACCESS = 'application_access',
  PERMISSION_REQUEST = 'permission_request',
  SECURITY_EVENT = 'security_event',
  API_CALL = 'api_call',
  RESOURCE_ACCESS = 'resource_access',
  SESSION_ACTIVITY = 'session_activity'
}

/**
 * 用户行为事件接口
 */
interface UserBehaviorEvent {
  id: string;
  userId: string;
  sessionId?: string;
  eventType: UserBehaviorEventType;
  timestamp: Date;
  ipAddress: string;
  userAgent: string;
  location?: {
    country: string;
    region: string;
    city: string;
    latitude?: number;
    longitude?: number;
  };
  device?: {
    type: string;
    os: string;
    browser: string;
    fingerprint: string;
  };
  context: {
    applicationId?: string;
    resourceId?: string;
    action?: string;
    result?: 'success' | 'failure' | 'blocked';
    duration?: number;
    metadata?: Record<string, any>;
  };
  riskScore?: number;
  anomalyScore?: number;
}

/**
 * 用户行为模式接口
 */
interface UserBehaviorPattern {
  userId: string;
  patternType: 'temporal' | 'location' | 'device' | 'application' | 'security';
  pattern: {
    name: string;
    description: string;
    frequency: number;
    confidence: number;
    lastSeen: Date;
    characteristics: Record<string, any>;
  };
  baseline: {
    averageFrequency: number;
    typicalTimes: string[];
    commonLocations: string[];
    usualDevices: string[];
    normalApplications: string[];
  };
  deviations: {
    type: string;
    severity: 'low' | 'medium' | 'high' | 'critical';
    description: string;
    detectedAt: Date;
    score: number;
  }[];
}

/**
 * 用户行为分析结果接口
 */
interface UserBehaviorAnalysis {
  userId: string;
  analysisDate: Date;
  timeRange: {
    start: Date;
    end: Date;
  };
  summary: {
    totalEvents: number;
    uniqueSessions: number;
    uniqueDevices: number;
    uniqueLocations: number;
    averageSessionDuration: number;
    mostActiveHours: string[];
    topApplications: Array<{ applicationId: string; count: number }>;
  };
  patterns: UserBehaviorPattern[];
  anomalies: {
    type: string;
    severity: 'low' | 'medium' | 'high' | 'critical';
    description: string;
    events: UserBehaviorEvent[];
    riskScore: number;
    recommendations: string[];
  }[];
  riskAssessment: {
    overallRiskScore: number;
    riskLevel: 'very_low' | 'low' | 'medium' | 'high' | 'very_high' | 'critical';
    riskFactors: Array<{
      factor: string;
      score: number;
      weight: number;
      description: string;
    }>;
    recommendations: string[];
  };
  predictions: {
    nextLoginTime?: Date;
    likelyApplications: string[];
    riskTrends: Array<{
      date: Date;
      predictedRiskScore: number;
      confidence: number;
    }>;
  };
}

/**
 * 用户行为分析服务
 */
export class UserBehaviorAnalyticsService {
  private eventBuffer = new Map<string, UserBehaviorEvent[]>();
  private patternCache = new Map<string, UserBehaviorPattern[]>();
  private analysisCache = new Map<string, UserBehaviorAnalysis>();

  /**
   * 记录用户行为事件
   */
  async recordBehaviorEvent(event: Partial<UserBehaviorEvent>): Promise<void> {
    try {
      const behaviorEvent: UserBehaviorEvent = {
        id: this.generateEventId(),
        userId: event.userId!,
        sessionId: event.sessionId,
        eventType: event.eventType!,
        timestamp: event.timestamp || new Date(),
        ipAddress: event.ipAddress!,
        userAgent: event.userAgent!,
        location: event.location,
        device: event.device,
        context: event.context || {},
        riskScore: event.riskScore,
        anomalyScore: event.anomalyScore
      };

      // 保存到数据库
      await this.saveBehaviorEvent(behaviorEvent);

      // 添加到缓冲区用于实时分析
      const userEvents = this.eventBuffer.get(event.userId!) || [];
      userEvents.push(behaviorEvent);
      
      // 保持最近1000个事件
      if (userEvents.length > 1000) {
        userEvents.shift();
      }
      
      this.eventBuffer.set(event.userId!, userEvents);

      // 实时异常检测
      await this.performRealtimeAnomalyDetection(behaviorEvent);

      // 更新指标
      metricsCollector.incrementCounter('user_behavior_events_total', {
        event_type: behaviorEvent.eventType,
        result: behaviorEvent.context.result || 'unknown'
      });

      logger.debug('用户行为事件已记录', {
        userId: behaviorEvent.userId,
        eventType: behaviorEvent.eventType,
        timestamp: behaviorEvent.timestamp
      });

    } catch (error) {
      logger.error('记录用户行为事件失败', {
        error: error instanceof Error ? error.message : String(error),
        userId: event.userId,
        eventType: event.eventType
      });
    }
  }

  /**
   * 分析用户行为
   */
  async analyzeUserBehavior(
    userId: string,
    timeRange: { start: Date; end: Date }
  ): Promise<UserBehaviorAnalysis> {
    try {
      const cacheKey = `user_behavior_analysis:${userId}:${timeRange.start.getTime()}-${timeRange.end.getTime()}`;
      
      // 检查缓存
      const cached = this.analysisCache.get(cacheKey);
      if (cached) {
        return cached;
      }

      // 获取用户行为事件
      const events = await this.getUserBehaviorEvents(userId, timeRange);

      // 生成行为摘要
      const summary = this.generateBehaviorSummary(events);

      // 识别行为模式
      const patterns = await this.identifyBehaviorPatterns(userId, events);

      // 检测异常行为
      const anomalies = await this.detectBehaviorAnomalies(userId, events, patterns);

      // 评估风险
      const riskAssessment = this.assessUserRisk(events, patterns, anomalies);

      // 生成预测
      const predictions = this.generateBehaviorPredictions(events, patterns);

      const analysis: UserBehaviorAnalysis = {
        userId,
        analysisDate: new Date(),
        timeRange,
        summary,
        patterns,
        anomalies,
        riskAssessment,
        predictions
      };

      // 缓存结果
      this.analysisCache.set(cacheKey, analysis);
      
      // 设置过期时间
      setTimeout(() => {
        this.analysisCache.delete(cacheKey);
      }, 30 * 60 * 1000); // 30分钟

      logger.info('用户行为分析完成', {
        userId,
        timeRange,
        totalEvents: events.length,
        patternsFound: patterns.length,
        anomaliesDetected: anomalies.length,
        riskScore: riskAssessment.overallRiskScore
      });

      return analysis;

    } catch (error) {
      logger.error('用户行为分析失败', {
        error: error instanceof Error ? error.message : String(error),
        userId,
        timeRange
      });
      throw error;
    }
  }

  /**
   * 获取用户行为趋势
   */
  async getUserBehaviorTrends(
    userId: string,
    period: 'day' | 'week' | 'month' = 'week'
  ): Promise<{
    loginTrends: Array<{ date: string; count: number }>;
    applicationUsage: Array<{ applicationId: string; usage: number; trend: 'up' | 'down' | 'stable' }>;
    riskTrends: Array<{ date: string; riskScore: number }>;
    deviceTrends: Array<{ deviceType: string; count: number; percentage: number }>;
    locationTrends: Array<{ location: string; count: number; percentage: number }>;
  }> {
    try {
      const endDate = new Date();
      const startDate = new Date();
      
      switch (period) {
        case 'day':
          startDate.setDate(endDate.getDate() - 7); // 最近7天
          break;
        case 'week':
          startDate.setDate(endDate.getDate() - 30); // 最近30天
          break;
        case 'month':
          startDate.setMonth(endDate.getMonth() - 6); // 最近6个月
          break;
      }

      const events = await this.getUserBehaviorEvents(userId, { start: startDate, end: endDate });

      // 计算登录趋势
      const loginTrends = this.calculateLoginTrends(events, period);

      // 计算应用使用趋势
      const applicationUsage = this.calculateApplicationUsageTrends(events);

      // 计算风险趋势
      const riskTrends = this.calculateRiskTrends(events, period);

      // 计算设备趋势
      const deviceTrends = this.calculateDeviceTrends(events);

      // 计算位置趋势
      const locationTrends = this.calculateLocationTrends(events);

      return {
        loginTrends,
        applicationUsage,
        riskTrends,
        deviceTrends,
        locationTrends
      };

    } catch (error) {
      logger.error('获取用户行为趋势失败', {
        error: error instanceof Error ? error.message : String(error),
        userId,
        period
      });
      throw error;
    }
  }

  /**
   * 获取用户行为洞察
   */
  async getUserBehaviorInsights(userId: string): Promise<{
    insights: Array<{
      type: 'pattern' | 'anomaly' | 'risk' | 'recommendation';
      title: string;
      description: string;
      severity: 'info' | 'warning' | 'critical';
      actionRequired: boolean;
      recommendations?: string[];
    }>;
    score: {
      behaviorConsistency: number;
      securityPosture: number;
      riskLevel: number;
      overallScore: number;
    };
  }> {
    try {
      const timeRange = {
        start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 最近30天
        end: new Date()
      };

      const analysis = await this.analyzeUserBehavior(userId, timeRange);
      const insights: any[] = [];

      // 模式洞察
      analysis.patterns.forEach(pattern => {
        if (pattern.pattern.confidence > 0.8) {
          insights.push({
            type: 'pattern',
            title: `发现稳定的${pattern.patternType}模式`,
            description: pattern.pattern.description,
            severity: 'info',
            actionRequired: false
          });
        }
      });

      // 异常洞察
      analysis.anomalies.forEach(anomaly => {
        insights.push({
          type: 'anomaly',
          title: `检测到${anomaly.type}异常`,
          description: anomaly.description,
          severity: anomaly.severity === 'critical' ? 'critical' : 'warning',
          actionRequired: anomaly.severity === 'critical' || anomaly.severity === 'high',
          recommendations: anomaly.recommendations
        });
      });

      // 风险洞察
      if (analysis.riskAssessment.overallRiskScore > 70) {
        insights.push({
          type: 'risk',
          title: '用户风险评分较高',
          description: `当前风险评分为${analysis.riskAssessment.overallRiskScore}分`,
          severity: 'critical',
          actionRequired: true,
          recommendations: analysis.riskAssessment.recommendations
        });
      }

      // 计算评分
      const behaviorConsistency = this.calculateBehaviorConsistency(analysis.patterns);
      const securityPosture = this.calculateSecurityPosture(analysis);
      const riskLevel = 100 - analysis.riskAssessment.overallRiskScore;
      const overallScore = Math.round((behaviorConsistency + securityPosture + riskLevel) / 3);

      return {
        insights,
        score: {
          behaviorConsistency,
          securityPosture,
          riskLevel,
          overallScore
        }
      };

    } catch (error) {
      logger.error('获取用户行为洞察失败', {
        error: error instanceof Error ? error.message : String(error),
        userId
      });
      throw error;
    }
  }

  // 私有辅助方法

  private generateEventId(): string {
    return `evt_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private async saveBehaviorEvent(event: UserBehaviorEvent): Promise<void> {
    // 简化实现：保存到数据库
    logger.debug('保存用户行为事件', { eventId: event.id });
  }

  private async performRealtimeAnomalyDetection(event: UserBehaviorEvent): Promise<void> {
    // 简化的实时异常检测
    const userEvents = this.eventBuffer.get(event.userId) || [];
    
    // 检测异常登录时间
    if (event.eventType === UserBehaviorEventType.LOGIN) {
      const hour = event.timestamp.getHours();
      const recentLogins = userEvents
        .filter(e => e.eventType === UserBehaviorEventType.LOGIN)
        .slice(-10);
      
      const typicalHours = recentLogins.map(e => e.timestamp.getHours());
      const isUnusualTime = !typicalHours.includes(hour) && typicalHours.length > 5;
      
      if (isUnusualTime) {
        logger.warn('检测到异常登录时间', {
          userId: event.userId,
          loginTime: hour,
          typicalHours
        });
      }
    }
  }

  private async getUserBehaviorEvents(
    userId: string,
    timeRange: { start: Date; end: Date }
  ): Promise<UserBehaviorEvent[]> {
    // 简化实现：从缓冲区和数据库获取事件
    const bufferedEvents = this.eventBuffer.get(userId) || [];
    return bufferedEvents.filter(event => 
      event.timestamp >= timeRange.start && event.timestamp <= timeRange.end
    );
  }

  private generateBehaviorSummary(events: UserBehaviorEvent[]): any {
    const sessions = new Set(events.map(e => e.sessionId).filter(Boolean));
    const devices = new Set(events.map(e => e.device?.fingerprint).filter(Boolean));
    const locations = new Set(events.map(e => e.location?.city).filter(Boolean));
    
    const applicationCounts = new Map<string, number>();
    events.forEach(event => {
      if (event.context.applicationId) {
        const count = applicationCounts.get(event.context.applicationId) || 0;
        applicationCounts.set(event.context.applicationId, count + 1);
      }
    });

    const hourCounts = new Map<number, number>();
    events.forEach(event => {
      const hour = event.timestamp.getHours();
      const count = hourCounts.get(hour) || 0;
      hourCounts.set(hour, count + 1);
    });

    const mostActiveHours = Array.from(hourCounts.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 3)
      .map(([hour]) => `${hour}:00`);

    const topApplications = Array.from(applicationCounts.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 5)
      .map(([applicationId, count]) => ({ applicationId, count }));

    return {
      totalEvents: events.length,
      uniqueSessions: sessions.size,
      uniqueDevices: devices.size,
      uniqueLocations: locations.size,
      averageSessionDuration: 1800, // 简化实现
      mostActiveHours,
      topApplications
    };
  }

  private async identifyBehaviorPatterns(
    userId: string,
    events: UserBehaviorEvent[]
  ): Promise<UserBehaviorPattern[]> {
    const patterns: UserBehaviorPattern[] = [];

    // 时间模式分析
    const timePattern = this.analyzeTemporalPattern(events);
    if (timePattern) {
      patterns.push({
        userId,
        patternType: 'temporal',
        pattern: timePattern,
        baseline: {
          averageFrequency: 5,
          typicalTimes: ['09:00', '14:00', '18:00'],
          commonLocations: [],
          usualDevices: [],
          normalApplications: []
        },
        deviations: []
      });
    }

    return patterns;
  }

  private async detectBehaviorAnomalies(
    userId: string,
    events: UserBehaviorEvent[],
    patterns: UserBehaviorPattern[]
  ): Promise<any[]> {
    const anomalies: any[] = [];

    // 检测异常登录位置
    const locationAnomalies = this.detectLocationAnomalies(events);
    anomalies.push(...locationAnomalies);

    // 检测异常设备
    const deviceAnomalies = this.detectDeviceAnomalies(events);
    anomalies.push(...deviceAnomalies);

    return anomalies;
  }

  private assessUserRisk(
    events: UserBehaviorEvent[],
    patterns: UserBehaviorPattern[],
    anomalies: any[]
  ): any {
    let riskScore = 0;
    const riskFactors: any[] = [];

    // 基于异常数量评估风险
    const anomalyRisk = Math.min(anomalies.length * 10, 50);
    riskScore += anomalyRisk;
    
    if (anomalyRisk > 0) {
      riskFactors.push({
        factor: 'behavioral_anomalies',
        score: anomalyRisk,
        weight: 0.3,
        description: `检测到${anomalies.length}个行为异常`
      });
    }

    // 基于失败事件评估风险
    const failedEvents = events.filter(e => e.context.result === 'failure');
    const failureRisk = Math.min(failedEvents.length * 5, 30);
    riskScore += failureRisk;

    if (failureRisk > 0) {
      riskFactors.push({
        factor: 'failed_attempts',
        score: failureRisk,
        weight: 0.2,
        description: `${failedEvents.length}次失败尝试`
      });
    }

    const riskLevel = riskScore >= 80 ? 'critical' :
                     riskScore >= 60 ? 'very_high' :
                     riskScore >= 40 ? 'high' :
                     riskScore >= 20 ? 'medium' :
                     riskScore >= 10 ? 'low' : 'very_low';

    return {
      overallRiskScore: Math.min(riskScore, 100),
      riskLevel,
      riskFactors,
      recommendations: this.generateRiskRecommendations(riskScore, riskFactors)
    };
  }

  private generateBehaviorPredictions(events: UserBehaviorEvent[], patterns: UserBehaviorPattern[]): any {
    // 简化的预测实现
    const recentLogins = events
      .filter(e => e.eventType === UserBehaviorEventType.LOGIN)
      .slice(-10);

    const avgLoginInterval = recentLogins.length > 1 ? 
      (recentLogins[recentLogins.length - 1].timestamp.getTime() - recentLogins[0].timestamp.getTime()) / (recentLogins.length - 1) :
      24 * 60 * 60 * 1000; // 默认24小时

    const nextLoginTime = new Date(Date.now() + avgLoginInterval);

    const appUsage = new Map<string, number>();
    events.forEach(event => {
      if (event.context.applicationId) {
        const count = appUsage.get(event.context.applicationId) || 0;
        appUsage.set(event.context.applicationId, count + 1);
      }
    });

    const likelyApplications = Array.from(appUsage.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 3)
      .map(([appId]) => appId);

    return {
      nextLoginTime,
      likelyApplications,
      riskTrends: [
        { date: new Date(), predictedRiskScore: 25, confidence: 0.8 }
      ]
    };
  }

  private analyzeTemporalPattern(events: UserBehaviorEvent[]): any | null {
    if (events.length < 10) return null;

    const hourCounts = new Map<number, number>();
    events.forEach(event => {
      const hour = event.timestamp.getHours();
      hourCounts.set(hour, (hourCounts.get(hour) || 0) + 1);
    });

    const peakHours = Array.from(hourCounts.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 3)
      .map(([hour]) => hour);

    return {
      name: 'daily_activity_pattern',
      description: `用户主要在${peakHours.join(', ')}点活跃`,
      frequency: events.length,
      confidence: 0.8,
      lastSeen: new Date(),
      characteristics: {
        peakHours,
        totalEvents: events.length
      }
    };
  }

  private detectLocationAnomalies(events: UserBehaviorEvent[]): any[] {
    const anomalies: any[] = [];
    const locations = events.map(e => e.location?.city).filter(Boolean);
    const uniqueLocations = new Set(locations);

    if (uniqueLocations.size > 3) {
      anomalies.push({
        type: 'unusual_location',
        severity: 'medium',
        description: `用户从${uniqueLocations.size}个不同位置访问`,
        events: events.filter(e => e.location),
        riskScore: Math.min(uniqueLocations.size * 10, 50),
        recommendations: ['启用位置验证', '监控异常位置访问']
      });
    }

    return anomalies;
  }

  private detectDeviceAnomalies(events: UserBehaviorEvent[]): any[] {
    const anomalies: any[] = [];
    const devices = events.map(e => e.device?.fingerprint).filter(Boolean);
    const uniqueDevices = new Set(devices);

    if (uniqueDevices.size > 5) {
      anomalies.push({
        type: 'multiple_devices',
        severity: 'medium',
        description: `用户使用了${uniqueDevices.size}个不同设备`,
        events: events.filter(e => e.device),
        riskScore: Math.min(uniqueDevices.size * 5, 30),
        recommendations: ['启用设备验证', '限制并发设备数量']
      });
    }

    return anomalies;
  }

  private calculateLoginTrends(events: UserBehaviorEvent[], period: string): any[] {
    const loginEvents = events.filter(e => e.eventType === UserBehaviorEventType.LOGIN);
    const trends: any[] = [];

    // 简化实现：按日期分组
    const dateGroups = new Map<string, number>();
    loginEvents.forEach(event => {
      const date = event.timestamp.toISOString().split('T')[0];
      dateGroups.set(date, (dateGroups.get(date) || 0) + 1);
    });

    Array.from(dateGroups.entries()).forEach(([date, count]) => {
      trends.push({ date, count });
    });

    return trends.sort((a, b) => a.date.localeCompare(b.date));
  }

  private calculateApplicationUsageTrends(events: UserBehaviorEvent[]): any[] {
    const appUsage = new Map<string, number>();
    events.forEach(event => {
      if (event.context.applicationId) {
        const count = appUsage.get(event.context.applicationId) || 0;
        appUsage.set(event.context.applicationId, count + 1);
      }
    });

    return Array.from(appUsage.entries()).map(([applicationId, usage]) => ({
      applicationId,
      usage,
      trend: 'stable' as const // 简化实现
    }));
  }

  private calculateRiskTrends(events: UserBehaviorEvent[], period: string): any[] {
    // 简化实现：计算每日平均风险评分
    const dateGroups = new Map<string, number[]>();
    events.forEach(event => {
      if (event.riskScore !== undefined) {
        const date = event.timestamp.toISOString().split('T')[0];
        const scores = dateGroups.get(date) || [];
        scores.push(event.riskScore);
        dateGroups.set(date, scores);
      }
    });

    return Array.from(dateGroups.entries()).map(([date, scores]) => ({
      date,
      riskScore: Math.round(scores.reduce((sum, score) => sum + score, 0) / scores.length)
    }));
  }

  private calculateDeviceTrends(events: UserBehaviorEvent[]): any[] {
    const deviceTypes = new Map<string, number>();
    events.forEach(event => {
      if (event.device?.type) {
        const count = deviceTypes.get(event.device.type) || 0;
        deviceTypes.set(event.device.type, count + 1);
      }
    });

    const total = Array.from(deviceTypes.values()).reduce((sum, count) => sum + count, 0);
    
    return Array.from(deviceTypes.entries()).map(([deviceType, count]) => ({
      deviceType,
      count,
      percentage: Math.round((count / total) * 100)
    }));
  }

  private calculateLocationTrends(events: UserBehaviorEvent[]): any[] {
    const locations = new Map<string, number>();
    events.forEach(event => {
      if (event.location?.city) {
        const count = locations.get(event.location.city) || 0;
        locations.set(event.location.city, count + 1);
      }
    });

    const total = Array.from(locations.values()).reduce((sum, count) => sum + count, 0);
    
    return Array.from(locations.entries()).map(([location, count]) => ({
      location,
      count,
      percentage: Math.round((count / total) * 100)
    }));
  }

  private calculateBehaviorConsistency(patterns: UserBehaviorPattern[]): number {
    if (patterns.length === 0) return 50;
    
    const avgConfidence = patterns.reduce((sum, p) => sum + p.pattern.confidence, 0) / patterns.length;
    return Math.round(avgConfidence * 100);
  }

  private calculateSecurityPosture(analysis: UserBehaviorAnalysis): number {
    let score = 100;
    
    // 基于异常数量扣分
    score -= analysis.anomalies.length * 10;
    
    // 基于风险评分扣分
    score -= analysis.riskAssessment.overallRiskScore * 0.5;
    
    return Math.max(0, Math.round(score));
  }

  private generateRiskRecommendations(riskScore: number, riskFactors: any[]): string[] {
    const recommendations: string[] = [];

    if (riskScore > 70) {
      recommendations.push('立即启用多因素认证');
      recommendations.push('限制敏感操作权限');
      recommendations.push('增强会话监控');
    } else if (riskScore > 40) {
      recommendations.push('考虑启用额外验证');
      recommendations.push('监控异常行为');
    } else {
      recommendations.push('保持当前安全措施');
    }

    return recommendations;
  }
}

// 创建单例实例
export const userBehaviorAnalyticsService = new UserBehaviorAnalyticsService();
