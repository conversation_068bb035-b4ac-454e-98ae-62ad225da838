/**
 * 组织权限解析服务
 * 实现基于组织架构的权限继承、隔离和传递机制
 * 参考Cerbos层次权限控制最佳实践
 */

import { prisma } from '@/config/database';
import { logger } from '@/config/logger';
import { cacheService } from './cache.service';
import { organizationService, Organization } from './organization.service';

/**
 * 权限上下文接口
 */
export interface PermissionContext {
  userId: string;
  organizationId?: string;
  resourceType?: string;
  resourceId?: string;
  action: string;
  ipAddress?: string;
  userAgent?: string;
}

/**
 * 有效权限结果
 */
export interface EffectivePermissions {
  permissions: string[];
  organizationPath: string;
  inheritedFrom: string[];
  resolvedAt: string;
  validUntil?: string;
}

/**
 * 权限验证结果
 */
export interface PermissionValidationResult {
  granted: boolean;
  reason?: string;
  organizationPath?: string;
  inheritanceChain?: string[];
  conditions?: Record<string, any>;
  validatedAt: string;
}

/**
 * 层次权限函数 - 类似Cerbos的hierarchy函数
 */
export class HierarchyUtils {
  /**
   * 创建层次对象
   */
  static hierarchy(path: string | string[], delimiter: string = '.'): Hierarchy {
    const parts = Array.isArray(path) ? path : path.split(delimiter);
    return new Hierarchy(parts);
  }

  /**
   * 检查是否为祖先关系
   */
  static ancestorOf(ancestor: string, descendant: string): boolean {
    return descendant.startsWith(`${ancestor}.`) && ancestor !== descendant;
  }

  /**
   * 检查是否为后代关系
   */
  static descendantOf(descendant: string, ancestor: string): boolean {
    return this.ancestorOf(ancestor, descendant);
  }

  /**
   * 检查是否为直接父子关系
   */
  static immediateChildOf(child: string, parent: string): boolean {
    const childParts = child.split('.');
    const parentParts = parent.split('.');
    return childParts.length === parentParts.length + 1 && 
           child.startsWith(`${parent}.`);
  }

  /**
   * 获取公共祖先
   */
  static commonAncestors(path1: string, path2: string): string {
    const parts1 = path1.split('.');
    const parts2 = path2.split('.');
    const commonParts: string[] = [];

    const minLength = Math.min(parts1.length, parts2.length);
    for (let i = 0; i < minLength; i++) {
      if (parts1[i] === parts2[i]) {
        commonParts.push(parts1[i]);
      } else {
        break;
      }
    }

    return commonParts.join('.');
  }

  /**
   * 检查是否为兄弟关系
   */
  static siblingOf(path1: string, path2: string): boolean {
    const parts1 = path1.split('.');
    const parts2 = path2.split('.');
    
    if (parts1.length !== parts2.length) return false;
    if (parts1.length < 2) return false;
    
    // 检查父路径是否相同
    const parent1 = parts1.slice(0, -1).join('.');
    const parent2 = parts2.slice(0, -1).join('.');
    
    return parent1 === parent2 && path1 !== path2;
  }
}

/**
 * 层次对象类
 */
export class Hierarchy {
  constructor(private parts: string[]) {}

  ancestorOf(other: Hierarchy): boolean {
    return HierarchyUtils.ancestorOf(this.toString(), other.toString());
  }

  descendantOf(other: Hierarchy): boolean {
    return HierarchyUtils.descendantOf(this.toString(), other.toString());
  }

  immediateChildOf(other: Hierarchy): boolean {
    return HierarchyUtils.immediateChildOf(this.toString(), other.toString());
  }

  siblingOf(other: Hierarchy): boolean {
    return HierarchyUtils.siblingOf(this.toString(), other.toString());
  }

  commonAncestors(other: Hierarchy): Hierarchy {
    const commonPath = HierarchyUtils.commonAncestors(this.toString(), other.toString());
    return HierarchyUtils.hierarchy(commonPath);
  }

  size(): number {
    return this.parts.length;
  }

  get(index: number): string {
    return this.parts[index];
  }

  toString(): string {
    return this.parts.join('.');
  }
}

/**
 * 组织权限解析服务类
 */
export class OrganizationPermissionService {
  private readonly CACHE_TTL = 300; // 5分钟缓存

  /**
   * 解析用户在组织中的有效权限
   */
  async resolveUserPermissions(
    userId: string,
    organizationPath: string
  ): Promise<EffectivePermissions> {
    try {
      const cacheKey = `user:effective_permissions:${userId}:${organizationPath}`;
      
      let effectivePermissions = await cacheService.get(cacheKey);
      if (!effectivePermissions) {
        effectivePermissions = await this.computeEffectivePermissions(userId, organizationPath);
        await cacheService.setex(cacheKey, this.CACHE_TTL, effectivePermissions);
      }

      return effectivePermissions as EffectivePermissions;
    } catch (error) {
      logger.error('解析用户权限失败', {
        userId,
        organizationPath,
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * 计算有效权限
   */
  private async computeEffectivePermissions(
    userId: string,
    organizationPath: string
  ): Promise<EffectivePermissions> {
    // 1. 获取组织层次结构
    const hierarchy = await organizationService.getOrganizationHierarchy(organizationPath);
    
    // 2. 收集权限
    const permissions = new Set<string>();
    const inheritedFrom: string[] = [];

    // 3. 从根组织开始，逐级应用权限
    for (const org of hierarchy) {
      // 获取用户在该组织的直接权限
      const directPermissions = await this.getUserDirectPermissions(userId, org.id);
      directPermissions.forEach(p => permissions.add(p));

      // 获取组织级权限
      const orgPermissions = await this.getOrganizationPermissions(org.id);
      
      // 应用继承规则
      for (const orgPerm of orgPermissions) {
        if (await this.shouldInheritPermission(orgPerm, org, hierarchy)) {
          permissions.add(orgPerm.permissionId);
          inheritedFrom.push(org.path);
        }
      }
    }

    return {
      permissions: Array.from(permissions),
      organizationPath,
      inheritedFrom,
      resolvedAt: new Date().toISOString()
    };
  }

  /**
   * 获取用户在组织中的直接权限
   */
  private async getUserDirectPermissions(userId: string, organizationId: string): Promise<string[]> {
    try {
      const member = await prisma.organizationMember.findUnique({
        where: {
          userId_organizationId: {
            userId,
            organizationId
          }
        }
      });

      if (!member || member.status !== 'active') {
        return [];
      }

      // 返回用户在该组织的权限
      return Array.isArray(member.permissions) ? member.permissions : [];
    } catch (error) {
      logger.error('获取用户直接权限失败', { userId, organizationId, error });
      return [];
    }
  }

  /**
   * 获取组织权限配置
   */
  private async getOrganizationPermissions(organizationId: string): Promise<any[]> {
    try {
      const orgPermissions = await prisma.organizationPermission.findMany({
        where: {
          organizationId,
          isActive: true,
          OR: [
            { effectiveUntil: null },
            { effectiveUntil: { gt: new Date() } }
          ]
        },
        include: {
          permission: true
        }
      });

      return orgPermissions;
    } catch (error) {
      logger.error('获取组织权限失败', { organizationId, error });
      return [];
    }
  }

  /**
   * 判断权限是否应该被继承
   */
  private async shouldInheritPermission(
    orgPermission: any,
    currentOrg: Organization,
    hierarchy: Organization[]
  ): Promise<boolean> {
    // 检查继承规则
    switch (orgPermission.inheritanceRule) {
      case 'block':
        return false;
      case 'override':
        // 只在当前组织生效，不继承
        return currentOrg.id === orgPermission.organizationId;
      case 'inherit':
      default:
        // 检查作用域
        return this.checkPermissionScope(orgPermission, currentOrg, hierarchy);
    }
  }

  /**
   * 检查权限作用域
   */
  private checkPermissionScope(
    orgPermission: any,
    currentOrg: Organization,
    hierarchy: Organization[]
  ): boolean {
    const permissionOrg = hierarchy.find(org => org.id === orgPermission.organizationId);
    if (!permissionOrg) return false;

    switch (orgPermission.scope) {
      case 'self':
        return currentOrg.id === orgPermission.organizationId;
      case 'children':
        return HierarchyUtils.immediateChildOf(currentOrg.path, permissionOrg.path);
      case 'descendants':
        return HierarchyUtils.descendantOf(currentOrg.path, permissionOrg.path) ||
               currentOrg.id === orgPermission.organizationId;
      default:
        return false;
    }
  }

  /**
   * 验证用户权限
   */
  async validatePermission(
    context: PermissionContext
  ): Promise<PermissionValidationResult> {
    const startTime = Date.now();

    try {
      // 获取用户的组织关系
      const userOrgs = await organizationService.getUserOrganizations(context.userId);
      
      if (userOrgs.length === 0) {
        return {
          granted: false,
          reason: '用户不属于任何组织',
          validatedAt: new Date().toISOString()
        };
      }

      // 如果指定了组织，检查该组织的权限
      if (context.organizationId) {
        const targetOrg = await organizationService.getOrganizationById(context.organizationId);
        if (!targetOrg) {
          return {
            granted: false,
            reason: '目标组织不存在',
            validatedAt: new Date().toISOString()
          };
        }

        return await this.validateOrganizationPermission(context, targetOrg);
      }

      // 检查用户在所有组织中的权限
      for (const membership of userOrgs) {
        const org = await organizationService.getOrganizationById(membership.organizationId);
        if (org) {
          const result = await this.validateOrganizationPermission(
            { ...context, organizationId: org.id },
            org
          );
          if (result.granted) {
            return result;
          }
        }
      }

      return {
        granted: false,
        reason: '在所有组织中都没有所需权限',
        validatedAt: new Date().toISOString()
      };

    } catch (error) {
      logger.error('权限验证失败', {
        context,
        error: error instanceof Error ? error.message : String(error)
      });
      
      return {
        granted: false,
        reason: '权限验证过程中发生错误',
        validatedAt: new Date().toISOString()
      };
    }
  }

  /**
   * 验证组织权限
   */
  private async validateOrganizationPermission(
    context: PermissionContext,
    organization: Organization
  ): Promise<PermissionValidationResult> {
    try {
      // 获取用户在该组织的有效权限
      const effectivePermissions = await this.resolveUserPermissions(
        context.userId,
        organization.path
      );

      // 构建权限标识符
      const permissionIdentifier = context.resourceType 
        ? `${context.action}:${context.resourceType}`
        : context.action;

      // 检查是否拥有所需权限
      const hasPermission = effectivePermissions.permissions.some(permission => 
        permission === permissionIdentifier || 
        permission === context.action ||
        permission === '*' // 通配符权限
      );

      if (hasPermission) {
        // 记录权限使用日志
        await this.logPermissionUsage(context, organization, true);

        return {
          granted: true,
          organizationPath: organization.path,
          inheritanceChain: effectivePermissions.inheritedFrom,
          validatedAt: new Date().toISOString()
        };
      }

      return {
        granted: false,
        reason: `缺少权限: ${permissionIdentifier}`,
        organizationPath: organization.path,
        validatedAt: new Date().toISOString()
      };

    } catch (error) {
      logger.error('组织权限验证失败', {
        context,
        organizationId: organization.id,
        error: error instanceof Error ? error.message : String(error)
      });

      return {
        granted: false,
        reason: '权限验证过程中发生错误',
        validatedAt: new Date().toISOString()
      };
    }
  }

  /**
   * 记录权限使用日志
   */
  private async logPermissionUsage(
    context: PermissionContext,
    organization: Organization,
    granted: boolean
  ): Promise<void> {
    try {
      await prisma.organizationAccessLog.create({
        data: {
          userId: context.userId,
          sourceOrganizationId: organization.id,
          targetOrganizationId: context.organizationId || organization.id,
          accessType: context.action,
          resourceType: context.resourceType || 'unknown',
          resourceId: context.resourceId,
          accessGranted: granted,
          ipAddress: context.ipAddress || 'unknown',
          userAgent: context.userAgent,
          accessedAt: new Date()
        }
      });
    } catch (error) {
      logger.error('记录权限使用日志失败', { context, error });
      // 不抛出错误，避免影响主要流程
    }
  }

  /**
   * 检查跨组织数据访问权限
   */
  async checkCrossOrganizationAccess(
    userId: string,
    sourceOrgPath: string,
    targetOrgPath: string,
    action: string
  ): Promise<boolean> {
    try {
      // 检查是否为同一组织或父子关系
      if (sourceOrgPath === targetOrgPath) {
        return true; // 同一组织内访问
      }

      if (HierarchyUtils.ancestorOf(sourceOrgPath, targetOrgPath)) {
        return true; // 父组织访问子组织
      }

      if (HierarchyUtils.descendantOf(sourceOrgPath, targetOrgPath)) {
        // 子组织访问父组织，需要特殊权限
        const context: PermissionContext = {
          userId,
          action: `cross_org:${action}`,
          organizationId: await this.getOrganizationIdByPath(targetOrgPath)
        };
        
        const result = await this.validatePermission(context);
        return result.granted;
      }

      // 跨组织访问，需要明确授权
      const context: PermissionContext = {
        userId,
        action: `cross_org:${action}`,
        organizationId: await this.getOrganizationIdByPath(targetOrgPath)
      };
      
      const result = await this.validatePermission(context);
      return result.granted;

    } catch (error) {
      logger.error('检查跨组织访问权限失败', {
        userId,
        sourceOrgPath,
        targetOrgPath,
        action,
        error: error instanceof Error ? error.message : String(error)
      });
      return false;
    }
  }

  /**
   * 根据路径获取组织ID
   */
  private async getOrganizationIdByPath(path: string): Promise<string | undefined> {
    const org = await organizationService.getOrganizationByPath(path);
    return org?.id;
  }
}

export const organizationPermissionService = new OrganizationPermissionService();
