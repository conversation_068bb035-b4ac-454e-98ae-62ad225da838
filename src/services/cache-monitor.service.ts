/**
 * 缓存监控服务
 * 提供缓存性能监控、统计分析和健康检查功能
 */

import { redisService } from './redis.service'
import { logger } from '@/config/logger'
import { RedisKeys } from '@/config/redis'

export interface CacheMetrics {
  hitRate: number
  missRate: number
  totalRequests: number
  totalHits: number
  totalMisses: number
  memoryUsage: number
  keyCount: number
  expiredKeys: number
  evictedKeys: number
  connectionCount: number
  commandsPerSecond: number
  avgResponseTime: number
}

export interface CacheHealth {
  status: 'healthy' | 'warning' | 'critical'
  uptime: number
  memoryUsagePercent: number
  connectionStatus: boolean
  lastError?: string
  errorCount: number
}

export interface CacheKeyInfo {
  key: string
  type: string
  ttl: number
  size: number
  lastAccessed?: Date
}

/**
 * 缓存监控服务类
 */
class CacheMonitorService {
  private metricsHistory: CacheMetrics[] = []
  private maxHistorySize = 100
  private monitoringInterval: NodeJS.Timeout | null = null
  private isMonitoring = false

  /**
   * 开始监控
   */
  startMonitoring(intervalMs: number = 60000): void {
    if (this.isMonitoring) {
      return
    }

    this.isMonitoring = true
    this.monitoringInterval = setInterval(async () => {
      try {
        const metrics = await this.collectMetrics()
        this.addMetricsToHistory(metrics)
        
        // 检查健康状态
        const health = await this.checkHealth()
        if (health.status !== 'healthy') {
          logger.warn('缓存健康状态异常', { health })
        }

      } catch (error) {
        logger.error('缓存监控失败', {
          error: error instanceof Error ? error.message : String(error)
        })
      }
    }, intervalMs)

    logger.info('缓存监控已启动', { intervalMs })
  }

  /**
   * 停止监控
   */
  stopMonitoring(): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval)
      this.monitoringInterval = null
    }
    this.isMonitoring = false
    logger.info('缓存监控已停止')
  }

  /**
   * 收集缓存指标
   */
  async collectMetrics(): Promise<CacheMetrics> {
    try {
      const client = redisService.getClient()
      const info = await client.info()
      const parsedInfo = this.parseRedisInfo(info)

      // 获取统计信息
      const stats = parsedInfo.Stats || {}
      const memory = parsedInfo.Memory || {}
      const clients = parsedInfo.Clients || {}

      const totalConnections = parseInt(stats.total_connections_received || '0')
      const totalCommands = parseInt(stats.total_commands_processed || '0')
      const keyspaceHits = parseInt(stats.keyspace_hits || '0')
      const keyspaceMisses = parseInt(stats.keyspace_misses || '0')
      const totalRequests = keyspaceHits + keyspaceMisses

      const metrics: CacheMetrics = {
        hitRate: totalRequests > 0 ? keyspaceHits / totalRequests : 0,
        missRate: totalRequests > 0 ? keyspaceMisses / totalRequests : 0,
        totalRequests,
        totalHits: keyspaceHits,
        totalMisses: keyspaceMisses,
        memoryUsage: parseInt(memory.used_memory || '0'),
        keyCount: await this.getKeyCount(),
        expiredKeys: parseInt(stats.expired_keys || '0'),
        evictedKeys: parseInt(stats.evicted_keys || '0'),
        connectionCount: parseInt(clients.connected_clients || '0'),
        commandsPerSecond: this.calculateCommandsPerSecond(totalCommands),
        avgResponseTime: await this.measureResponseTime()
      }

      logger.debug('缓存指标收集完成', { metrics })
      return metrics

    } catch (error) {
      logger.error('收集缓存指标失败', {
        error: error instanceof Error ? error.message : String(error)
      })
      throw error
    }
  }

  /**
   * 检查缓存健康状态
   */
  async checkHealth(): Promise<CacheHealth> {
    try {
      const startTime = Date.now()
      
      // 测试连接
      await redisService.getClient().ping()
      const responseTime = Date.now() - startTime

      // 获取系统信息
      const info = await redisService.getClient().info()
      const parsedInfo = this.parseRedisInfo(info)
      
      const server = parsedInfo.Server || {}
      const memory = parsedInfo.Memory || {}
      
      const uptime = parseInt(server.uptime_in_seconds || '0')
      const usedMemory = parseInt(memory.used_memory || '0')
      const maxMemory = parseInt(memory.maxmemory || '0')
      const memoryUsagePercent = maxMemory > 0 ? (usedMemory / maxMemory) * 100 : 0

      // 判断健康状态
      let status: 'healthy' | 'warning' | 'critical' = 'healthy'
      
      if (responseTime > 1000 || memoryUsagePercent > 90) {
        status = 'critical'
      } else if (responseTime > 500 || memoryUsagePercent > 80) {
        status = 'warning'
      }

      const health: CacheHealth = {
        status,
        uptime,
        memoryUsagePercent,
        connectionStatus: true,
        errorCount: 0
      }

      return health

    } catch (error) {
      logger.error('缓存健康检查失败', {
        error: error instanceof Error ? error.message : String(error)
      })

      return {
        status: 'critical',
        uptime: 0,
        memoryUsagePercent: 0,
        connectionStatus: false,
        lastError: error instanceof Error ? error.message : String(error),
        errorCount: 1
      }
    }
  }

  /**
   * 获取缓存键信息
   */
  async getKeyInfo(pattern: string = '*', limit: number = 100): Promise<CacheKeyInfo[]> {
    try {
      const client = redisService.getClient()
      const keys = await client.keys(pattern)
      const limitedKeys = keys.slice(0, limit)

      const keyInfos: CacheKeyInfo[] = []

      for (const key of limitedKeys) {
        try {
          const [type, ttl, size] = await Promise.all([
            client.type(key),
            client.ttl(key),
            this.getKeySize(key)
          ])

          keyInfos.push({
            key,
            type,
            ttl,
            size
          })

        } catch (error) {
          // 跳过有问题的键
          continue
        }
      }

      return keyInfos

    } catch (error) {
      logger.error('获取缓存键信息失败', {
        pattern,
        error: error instanceof Error ? error.message : String(error)
      })
      return []
    }
  }

  /**
   * 清理过期键
   */
  async cleanupExpiredKeys(): Promise<number> {
    try {
      const client = redisService.getClient()
      
      // 获取所有键
      const keys = await client.keys('*')
      let deletedCount = 0

      for (const key of keys) {
        try {
          const ttl = await client.ttl(key)
          if (ttl === -2) { // 键已过期但未被删除
            await client.del(key)
            deletedCount++
          }
        } catch (error) {
          // 跳过有问题的键
          continue
        }
      }

      logger.info('过期键清理完成', { deletedCount })
      return deletedCount

    } catch (error) {
      logger.error('清理过期键失败', {
        error: error instanceof Error ? error.message : String(error)
      })
      return 0
    }
  }

  /**
   * 获取指标历史
   */
  getMetricsHistory(): CacheMetrics[] {
    return [...this.metricsHistory]
  }

  /**
   * 获取最新指标
   */
  getLatestMetrics(): CacheMetrics | null {
    return this.metricsHistory.length > 0 
      ? this.metricsHistory[this.metricsHistory.length - 1] 
      : null
  }

  /**
   * 清除指标历史
   */
  clearMetricsHistory(): void {
    this.metricsHistory = []
    logger.info('缓存指标历史已清除')
  }

  // ==================== 私有方法 ====================

  /**
   * 添加指标到历史记录
   */
  private addMetricsToHistory(metrics: CacheMetrics): void {
    this.metricsHistory.push(metrics)
    
    // 保持历史记录大小限制
    if (this.metricsHistory.length > this.maxHistorySize) {
      this.metricsHistory.shift()
    }
  }

  /**
   * 解析Redis INFO命令输出
   */
  private parseRedisInfo(info: string): Record<string, Record<string, string>> {
    const result: Record<string, Record<string, string>> = {}
    const sections = info.split('\r\n\r\n')
    
    sections.forEach(section => {
      const lines = section.split('\r\n')
      if (lines.length === 0) return
      
      const sectionName = lines[0].replace('# ', '')
      result[sectionName] = {}
      
      lines.slice(1).forEach(line => {
        if (line && line.includes(':')) {
          const [key, value] = line.split(':')
          result[sectionName][key] = value
        }
      })
    })
    
    return result
  }

  /**
   * 获取键数量
   */
  private async getKeyCount(): Promise<number> {
    try {
      const client = redisService.getClient()
      const info = await client.info('keyspace')
      
      // 解析keyspace信息
      const lines = info.split('\r\n')
      let totalKeys = 0
      
      lines.forEach(line => {
        if (line.startsWith('db')) {
          const match = line.match(/keys=(\d+)/)
          if (match) {
            totalKeys += parseInt(match[1])
          }
        }
      })
      
      return totalKeys
    } catch (error) {
      return 0
    }
  }

  /**
   * 计算每秒命令数
   */
  private calculateCommandsPerSecond(totalCommands: number): number {
    // 简化实现，实际应该基于时间窗口计算
    const lastMetrics = this.getLatestMetrics()
    if (!lastMetrics) {
      return 0
    }
    
    // 假设监控间隔为60秒
    const timeDiff = 60
    const commandDiff = totalCommands - (lastMetrics.totalHits + lastMetrics.totalMisses)
    
    return commandDiff / timeDiff
  }

  /**
   * 测量响应时间
   */
  private async measureResponseTime(): Promise<number> {
    try {
      const startTime = Date.now()
      await redisService.getClient().ping()
      return Date.now() - startTime
    } catch (error) {
      return -1
    }
  }

  /**
   * 获取键大小
   */
  private async getKeySize(key: string): Promise<number> {
    try {
      const client = redisService.getClient()
      const type = await client.type(key)
      
      switch (type) {
        case 'string':
          return (await client.strlen(key)) || 0
        case 'list':
          return (await client.llen(key)) || 0
        case 'set':
          return (await client.scard(key)) || 0
        case 'zset':
          return (await client.zcard(key)) || 0
        case 'hash':
          return (await client.hlen(key)) || 0
        default:
          return 0
      }
    } catch (error) {
      return 0
    }
  }
}

// 导出缓存监控服务实例
export const cacheMonitorService = new CacheMonitorService()
export default cacheMonitorService
