/**
 * 组织架构管理服务
 * 实现多层级组织架构的创建、管理和查询功能
 */

import { prisma } from '@/config/database';
import { logger } from '@/config/logger';
import { cacheService } from './cache.service';

/**
 * 组织架构接口定义
 */
export interface Organization {
  id: string;
  name: string;
  displayName: string;
  description?: string;
  parentId?: string;
  path: string;
  level: number;
  type: OrganizationType;
  status: OrganizationStatus;
  metadata: Record<string, any>;
  permissionInheritance: boolean;
  dataIsolationLevel: DataIsolationLevel;
  settings: Record<string, any>;
  createdBy: string;
  createdAt: Date;
  updatedBy: string;
  updatedAt: Date;
}

export type OrganizationType = 'company' | 'division' | 'department' | 'team' | 'group' | 'project';
export type OrganizationStatus = 'active' | 'inactive' | 'archived';
export type DataIsolationLevel = 'strict' | 'inherit' | 'none';

export interface OrganizationMember {
  id: string;
  userId: string;
  organizationId: string;
  role: string;
  permissions: string[];
  status: string;
  joinedAt: Date;
  expiresAt?: Date;
  isOwner: boolean;
  isPrimary: boolean;
}

export interface OrganizationHierarchy {
  organization: Organization;
  children: OrganizationHierarchy[];
  memberCount: number;
  permissionCount: number;
}

/**
 * 组织架构服务类
 */
export class OrganizationService {
  private readonly CACHE_TTL = 300; // 5分钟缓存
  private readonly MAX_HIERARCHY_DEPTH = 10; // 最大层级深度

  /**
   * 创建组织
   */
  async createOrganization(
    organizationData: Omit<Organization, 'id' | 'path' | 'level' | 'createdAt' | 'updatedAt'>,
    createdBy: string
  ): Promise<Organization> {
    try {
      // 验证父组织存在性
      let parentOrg: Organization | null = null;
      if (organizationData.parentId) {
        parentOrg = await this.getOrganizationById(organizationData.parentId);
        if (!parentOrg) {
          throw new Error(`父组织不存在: ${organizationData.parentId}`);
        }
        
        // 检查层级深度
        if (parentOrg.level >= this.MAX_HIERARCHY_DEPTH) {
          throw new Error(`组织层级深度超过限制: ${this.MAX_HIERARCHY_DEPTH}`);
        }
      }

      // 生成组织路径和层级
      const path = parentOrg 
        ? `${parentOrg.path}.${organizationData.name}`
        : organizationData.name;
      const level = parentOrg ? parentOrg.level + 1 : 0;

      // 检查路径唯一性
      const existingOrg = await prisma.organization.findUnique({
        where: { path }
      });
      if (existingOrg) {
        throw new Error(`组织路径已存在: ${path}`);
      }

      // 创建组织记录
      const organization = await prisma.organization.create({
        data: {
          name: organizationData.name,
          displayName: organizationData.displayName,
          description: organizationData.description,
          parentId: organizationData.parentId,
          path,
          level,
          type: organizationData.type,
          status: organizationData.status,
          metadata: organizationData.metadata,
          permissionInheritance: organizationData.permissionInheritance,
          dataIsolationLevel: organizationData.dataIsolationLevel,
          settings: organizationData.settings,
          createdBy,
          updatedBy: createdBy
        }
      });

      // 清除相关缓存
      await this.clearOrganizationCache(path);

      logger.info('组织创建成功', {
        organizationId: organization.id,
        path,
        createdBy
      });

      return organization as Organization;
    } catch (error) {
      logger.error('创建组织失败', {
        organizationData,
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * 获取组织信息
   */
  async getOrganizationById(organizationId: string): Promise<Organization | null> {
    try {
      const cacheKey = `org:${organizationId}`;
      
      let organization = await cacheService.get(cacheKey);
      if (!organization) {
        organization = await prisma.organization.findUnique({
          where: { id: organizationId }
        });
        
        if (organization) {
          await cacheService.setex(cacheKey, this.CACHE_TTL, organization);
        }
      }

      return organization as Organization | null;
    } catch (error) {
      logger.error('获取组织信息失败', {
        organizationId,
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * 根据路径获取组织
   */
  async getOrganizationByPath(path: string): Promise<Organization | null> {
    try {
      const cacheKey = `org:path:${path}`;
      
      let organization = await cacheService.get(cacheKey);
      if (!organization) {
        organization = await prisma.organization.findUnique({
          where: { path }
        });
        
        if (organization) {
          await cacheService.setex(cacheKey, this.CACHE_TTL, organization);
        }
      }

      return organization as Organization | null;
    } catch (error) {
      logger.error('根据路径获取组织失败', {
        path,
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * 获取组织层次结构
   */
  async getOrganizationHierarchy(organizationPath: string): Promise<Organization[]> {
    try {
      const cacheKey = `org:hierarchy:${organizationPath}`;
      
      let hierarchy = await cacheService.get(cacheKey);
      if (!hierarchy) {
        hierarchy = await this.buildHierarchy(organizationPath);
        await cacheService.setex(cacheKey, this.CACHE_TTL, hierarchy);
      }

      return hierarchy as Organization[];
    } catch (error) {
      logger.error('获取组织层次结构失败', {
        organizationPath,
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * 构建组织层次结构
   */
  private async buildHierarchy(path: string): Promise<Organization[]> {
    const pathParts = path.split('.');
    const hierarchy: Organization[] = [];

    for (let i = 1; i <= pathParts.length; i++) {
      const currentPath = pathParts.slice(0, i).join('.');
      const org = await this.getOrganizationByPath(currentPath);
      if (org) {
        hierarchy.push(org);
      }
    }

    return hierarchy;
  }

  /**
   * 获取子组织列表
   */
  async getChildOrganizations(
    parentId: string,
    includeInactive: boolean = false
  ): Promise<Organization[]> {
    try {
      const whereClause: any = { parentId };
      if (!includeInactive) {
        whereClause.status = 'active';
      }

      const children = await prisma.organization.findMany({
        where: whereClause,
        orderBy: { name: 'asc' }
      });

      return children as Organization[];
    } catch (error) {
      logger.error('获取子组织列表失败', {
        parentId,
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * 获取后代组织列表（所有子孙组织）
   */
  async getDescendantOrganizations(organizationPath: string): Promise<Organization[]> {
    try {
      const descendants = await prisma.organization.findMany({
        where: {
          path: {
            startsWith: `${organizationPath}.`
          },
          status: 'active'
        },
        orderBy: { path: 'asc' }
      });

      return descendants as Organization[];
    } catch (error) {
      logger.error('获取后代组织列表失败', {
        organizationPath,
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * 添加组织成员
   */
  async addOrganizationMember(
    organizationId: string,
    userId: string,
    role: string,
    permissions: string[] = [],
    addedBy: string,
    options: {
      isOwner?: boolean;
      isPrimary?: boolean;
      expiresAt?: Date;
    } = {}
  ): Promise<OrganizationMember> {
    try {
      // 检查组织是否存在
      const organization = await this.getOrganizationById(organizationId);
      if (!organization) {
        throw new Error(`组织不存在: ${organizationId}`);
      }

      // 检查是否已是成员
      const existingMember = await prisma.organizationMember.findUnique({
        where: {
          userId_organizationId: {
            userId,
            organizationId
          }
        }
      });

      if (existingMember) {
        throw new Error(`用户已是组织成员: ${userId}`);
      }

      // 创建成员记录
      const member = await prisma.organizationMember.create({
        data: {
          userId,
          organizationId,
          role,
          permissions,
          isOwner: options.isOwner || false,
          isPrimary: options.isPrimary || false,
          expiresAt: options.expiresAt,
          createdBy: addedBy
        }
      });

      // 清除用户权限缓存
      await this.clearUserPermissionCache(userId);

      logger.info('组织成员添加成功', {
        organizationId,
        userId,
        role,
        addedBy
      });

      return member as OrganizationMember;
    } catch (error) {
      logger.error('添加组织成员失败', {
        organizationId,
        userId,
        role,
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * 获取用户的组织成员关系
   */
  async getUserOrganizations(userId: string): Promise<OrganizationMember[]> {
    try {
      const cacheKey = `user:orgs:${userId}`;
      
      let memberships = await cacheService.get(cacheKey);
      if (!memberships) {
        memberships = await prisma.organizationMember.findMany({
          where: {
            userId,
            status: 'active'
          },
          include: {
            organization: true
          },
          orderBy: { joinedAt: 'desc' }
        });
        
        await cacheService.setex(cacheKey, this.CACHE_TTL, memberships);
      }

      return memberships as OrganizationMember[];
    } catch (error) {
      logger.error('获取用户组织关系失败', {
        userId,
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * 检查用户是否在组织中
   */
  async isUserInOrganization(
    userId: string,
    organizationId: string
  ): Promise<boolean> {
    try {
      const member = await prisma.organizationMember.findUnique({
        where: {
          userId_organizationId: {
            userId,
            organizationId
          }
        }
      });

      return member?.status === 'active';
    } catch (error) {
      logger.error('检查用户组织关系失败', {
        userId,
        organizationId,
        error: error instanceof Error ? error.message : String(error)
      });
      return false;
    }
  }

  /**
   * 清除组织相关缓存
   */
  private async clearOrganizationCache(path: string): Promise<void> {
    const patterns = [
      `org:path:${path}`,
      `org:hierarchy:${path}*`,
      `org:children:*`,
      `user:orgs:*`
    ];

    for (const pattern of patterns) {
      await cacheService.del(pattern);
    }
  }

  /**
   * 清除用户权限缓存
   */
  private async clearUserPermissionCache(userId: string): Promise<void> {
    const patterns = [
      `user:orgs:${userId}`,
      `user:permissions:${userId}*`,
      `user:effective_permissions:${userId}*`
    ];

    for (const pattern of patterns) {
      await cacheService.del(pattern);
    }
  }
}

export const organizationService = new OrganizationService();
