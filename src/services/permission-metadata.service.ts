/**
 * 权限元数据标准化服务
 * 实现权限发现和依赖管理机制
 */

import { prisma } from '@/config/database';
import { logger } from '@/config/logger';
import { cacheService } from './cache.service';
import { v4 as uuidv4 } from 'uuid';

/**
 * 权限元数据接口
 */
export interface PermissionMetadata {
  id: string;
  name: string;
  displayName: string;
  description: string;
  category: string;
  scope: 'global' | 'application' | 'resource';
  type: 'action' | 'data' | 'feature' | 'admin';
  level: 'read' | 'write' | 'admin' | 'owner';
  dependencies: string[]; // 依赖的其他权限ID
  conflicts: string[]; // 冲突的权限ID
  prerequisites: string[]; // 前置条件权限ID
  implications: string[]; // 隐含授予的权限ID
  tags: string[];
  metadata: Record<string, any>;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * 权限发现结果
 */
export interface PermissionDiscoveryResult {
  permissions: PermissionMetadata[];
  dependencies: Record<string, string[]>;
  conflicts: Record<string, string[]>;
  recommendations: string[];
  warnings: string[];
}

/**
 * 权限申请上下文
 */
export interface PermissionRequestContext {
  userId: string;
  applicationId: string;
  requestedPermissions: string[];
  justification: string;
  expiresAt?: Date;
  metadata?: Record<string, any>;
}

export class PermissionMetadataService {

  /**
   * 注册权限元数据
   */
  async registerPermission(permission: Omit<PermissionMetadata, 'id' | 'createdAt' | 'updatedAt'>): Promise<PermissionMetadata> {
    try {
      const permissionData = {
        id: uuidv4(),
        ...permission,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      // 验证权限依赖关系
      await this.validatePermissionDependencies(permissionData);

      // 保存到数据库
      const savedPermission = await prisma.permission.create({
        data: {
          id: permissionData.id,
          name: permissionData.name,
          displayName: permissionData.displayName,
          description: permissionData.description,
          category: permissionData.category,
          scope: permissionData.scope,
          type: permissionData.type,
          level: permissionData.level,
          dependencies: permissionData.dependencies,
          conflicts: permissionData.conflicts,
          prerequisites: permissionData.prerequisites,
          implications: permissionData.implications,
          tags: permissionData.tags,
          metadata: permissionData.metadata,
          isActive: permissionData.isActive
        }
      });

      // 清除相关缓存
      await this.clearPermissionCache();

      logger.info('权限元数据注册成功', { 
        permissionId: savedPermission.id,
        name: savedPermission.name 
      });

      return savedPermission as PermissionMetadata;

    } catch (error) {
      logger.error('权限元数据注册失败', { error, permission });
      throw error;
    }
  }

  /**
   * 发现权限及其依赖关系
   */
  async discoverPermissions(
    query: {
      category?: string;
      scope?: string;
      type?: string;
      tags?: string[];
      search?: string;
    }
  ): Promise<PermissionDiscoveryResult> {
    try {
      // 构建查询条件
      const where: any = {
        isActive: true
      };

      if (query.category) {
        where.category = query.category;
      }

      if (query.scope) {
        where.scope = query.scope;
      }

      if (query.type) {
        where.type = query.type;
      }

      if (query.tags && query.tags.length > 0) {
        where.tags = {
          hasSome: query.tags
        };
      }

      if (query.search) {
        where.OR = [
          { name: { contains: query.search, mode: 'insensitive' } },
          { displayName: { contains: query.search, mode: 'insensitive' } },
          { description: { contains: query.search, mode: 'insensitive' } }
        ];
      }

      // 查询权限
      const permissions = await prisma.permission.findMany({
        where,
        orderBy: [
          { category: 'asc' },
          { name: 'asc' }
        ]
      });

      // 构建依赖关系图
      const dependencies: Record<string, string[]> = {};
      const conflicts: Record<string, string[]> = {};

      permissions.forEach(permission => {
        if (permission.dependencies && permission.dependencies.length > 0) {
          dependencies[permission.id] = permission.dependencies as string[];
        }
        if (permission.conflicts && permission.conflicts.length > 0) {
          conflicts[permission.id] = permission.conflicts as string[];
        }
      });

      // 生成推荐和警告
      const recommendations = await this.generateRecommendations(permissions as PermissionMetadata[]);
      const warnings = await this.generateWarnings(permissions as PermissionMetadata[], dependencies, conflicts);

      const result: PermissionDiscoveryResult = {
        permissions: permissions as PermissionMetadata[],
        dependencies,
        conflicts,
        recommendations,
        warnings
      };

      logger.info('权限发现完成', { 
        count: permissions.length,
        query 
      });

      return result;

    } catch (error) {
      logger.error('权限发现失败', { error, query });
      throw error;
    }
  }

  /**
   * 解析权限依赖关系
   */
  async resolvePermissionDependencies(permissionIds: string[]): Promise<{
    resolved: string[];
    missing: string[];
    conflicts: Array<{ permission1: string; permission2: string }>;
  }> {
    try {
      const resolved = new Set<string>();
      const missing: string[] = [];
      const conflicts: Array<{ permission1: string; permission2: string }> = [];

      // 获取所有相关权限
      const permissions = await prisma.permission.findMany({
        where: {
          id: { in: permissionIds },
          isActive: true
        }
      });

      const permissionMap = new Map(permissions.map(p => [p.id, p]));

      // 递归解析依赖
      const resolveDependencies = (permissionId: string, visited = new Set<string>()) => {
        if (visited.has(permissionId)) {
          // 检测到循环依赖
          logger.warn('检测到权限循环依赖', { permissionId, visited: Array.from(visited) });
          return;
        }

        const permission = permissionMap.get(permissionId);
        if (!permission) {
          missing.push(permissionId);
          return;
        }

        visited.add(permissionId);
        resolved.add(permissionId);

        // 解析前置条件
        if (permission.prerequisites) {
          (permission.prerequisites as string[]).forEach(prereq => {
            resolveDependencies(prereq, new Set(visited));
          });
        }

        // 解析依赖
        if (permission.dependencies) {
          (permission.dependencies as string[]).forEach(dep => {
            resolveDependencies(dep, new Set(visited));
          });
        }

        // 添加隐含权限
        if (permission.implications) {
          (permission.implications as string[]).forEach(impl => {
            resolved.add(impl);
          });
        }
      };

      // 解析所有请求的权限
      permissionIds.forEach(id => resolveDependencies(id));

      // 检查冲突
      const resolvedArray = Array.from(resolved);
      for (let i = 0; i < resolvedArray.length; i++) {
        for (let j = i + 1; j < resolvedArray.length; j++) {
          const perm1 = permissionMap.get(resolvedArray[i]);
          const perm2 = permissionMap.get(resolvedArray[j]);

          if (perm1?.conflicts && (perm1.conflicts as string[]).includes(resolvedArray[j])) {
            conflicts.push({ permission1: resolvedArray[i], permission2: resolvedArray[j] });
          }
          if (perm2?.conflicts && (perm2.conflicts as string[]).includes(resolvedArray[i])) {
            conflicts.push({ permission1: resolvedArray[j], permission2: resolvedArray[i] });
          }
        }
      }

      logger.info('权限依赖关系解析完成', {
        requested: permissionIds.length,
        resolved: resolved.size,
        missing: missing.length,
        conflicts: conflicts.length
      });

      return {
        resolved: Array.from(resolved),
        missing,
        conflicts
      };

    } catch (error) {
      logger.error('权限依赖关系解析失败', { error, permissionIds });
      throw error;
    }
  }

  /**
   * 验证用户权限申请
   */
  async validatePermissionRequest(context: PermissionRequestContext): Promise<{
    valid: boolean;
    errors: string[];
    warnings: string[];
    resolvedPermissions: string[];
  }> {
    try {
      const errors: string[] = [];
      const warnings: string[] = [];

      // 解析权限依赖
      const resolution = await this.resolvePermissionDependencies(context.requestedPermissions);

      // 检查缺失的权限
      if (resolution.missing.length > 0) {
        errors.push(`请求的权限不存在: ${resolution.missing.join(', ')}`);
      }

      // 检查权限冲突
      if (resolution.conflicts.length > 0) {
        resolution.conflicts.forEach(conflict => {
          errors.push(`权限冲突: ${conflict.permission1} 与 ${conflict.permission2} 不能同时授予`);
        });
      }

      // 检查用户当前权限
      const userPermissions = await this.getUserPermissions(context.userId, context.applicationId);
      const newPermissions = resolution.resolved.filter(p => !userPermissions.includes(p));

      if (newPermissions.length === 0) {
        warnings.push('用户已拥有所有请求的权限');
      }

      // 检查权限级别升级
      const permissionUpgrades = await this.checkPermissionUpgrades(
        context.userId,
        context.applicationId,
        newPermissions
      );

      if (permissionUpgrades.length > 0) {
        warnings.push(`检测到权限级别升级: ${permissionUpgrades.join(', ')}`);
      }

      const valid = errors.length === 0;

      logger.info('权限申请验证完成', {
        userId: context.userId,
        applicationId: context.applicationId,
        valid,
        errorsCount: errors.length,
        warningsCount: warnings.length
      });

      return {
        valid,
        errors,
        warnings,
        resolvedPermissions: resolution.resolved
      };

    } catch (error) {
      logger.error('权限申请验证失败', { error, context });
      throw error;
    }
  }

  /**
   * 获取用户当前权限
   */
  private async getUserPermissions(userId: string, applicationId: string): Promise<string[]> {
    try {
      const userPermissions = await prisma.userPermission.findMany({
        where: {
          userId,
          applicationId,
          isActive: true,
          OR: [
            { expiresAt: null },
            { expiresAt: { gt: new Date() } }
          ]
        },
        select: {
          permissionId: true
        }
      });

      return userPermissions.map(up => up.permissionId);

    } catch (error) {
      logger.error('获取用户权限失败', { error, userId, applicationId });
      return [];
    }
  }

  /**
   * 检查权限级别升级
   */
  private async checkPermissionUpgrades(
    userId: string,
    applicationId: string,
    newPermissionIds: string[]
  ): Promise<string[]> {
    try {
      const upgrades: string[] = [];

      // 获取新权限的详细信息
      const newPermissions = await prisma.permission.findMany({
        where: {
          id: { in: newPermissionIds }
        }
      });

      // 获取用户当前权限
      const currentPermissions = await prisma.userPermission.findMany({
        where: {
          userId,
          applicationId,
          isActive: true
        },
        include: {
          permission: true
        }
      });

      // 检查是否有权限级别升级
      newPermissions.forEach(newPerm => {
        const existingPerm = currentPermissions.find(cp => 
          cp.permission.category === newPerm.category && 
          cp.permission.scope === newPerm.scope
        );

        if (existingPerm) {
          const levelOrder = ['read', 'write', 'admin', 'owner'];
          const currentLevel = levelOrder.indexOf(existingPerm.permission.level);
          const newLevel = levelOrder.indexOf(newPerm.level);

          if (newLevel > currentLevel) {
            upgrades.push(`${newPerm.name} (${existingPerm.permission.level} -> ${newPerm.level})`);
          }
        }
      });

      return upgrades;

    } catch (error) {
      logger.error('检查权限级别升级失败', { error, userId, applicationId });
      return [];
    }
  }

  /**
   * 验证权限依赖关系
   */
  private async validatePermissionDependencies(permission: PermissionMetadata): Promise<void> {
    // 检查依赖的权限是否存在
    if (permission.dependencies && permission.dependencies.length > 0) {
      const existingDeps = await prisma.permission.findMany({
        where: {
          id: { in: permission.dependencies },
          isActive: true
        },
        select: { id: true }
      });

      const existingDepIds = existingDeps.map(d => d.id);
      const missingDeps = permission.dependencies.filter(d => !existingDepIds.includes(d));

      if (missingDeps.length > 0) {
        throw new Error(`依赖的权限不存在: ${missingDeps.join(', ')}`);
      }
    }

    // 检查冲突的权限是否存在
    if (permission.conflicts && permission.conflicts.length > 0) {
      const existingConflicts = await prisma.permission.findMany({
        where: {
          id: { in: permission.conflicts },
          isActive: true
        },
        select: { id: true }
      });

      const existingConflictIds = existingConflicts.map(c => c.id);
      const missingConflicts = permission.conflicts.filter(c => !existingConflictIds.includes(c));

      if (missingConflicts.length > 0) {
        throw new Error(`冲突的权限不存在: ${missingConflicts.join(', ')}`);
      }
    }
  }

  /**
   * 生成权限推荐
   */
  private async generateRecommendations(permissions: PermissionMetadata[]): Promise<string[]> {
    const recommendations: string[] = [];

    // 基于权限类别和级别生成推荐
    const categories = [...new Set(permissions.map(p => p.category))];
    
    categories.forEach(category => {
      const categoryPerms = permissions.filter(p => p.category === category);
      const hasRead = categoryPerms.some(p => p.level === 'read');
      const hasWrite = categoryPerms.some(p => p.level === 'write');

      if (hasWrite && !hasRead) {
        recommendations.push(`建议为 ${category} 类别添加读取权限`);
      }
    });

    return recommendations;
  }

  /**
   * 生成权限警告
   */
  private async generateWarnings(
    permissions: PermissionMetadata[],
    dependencies: Record<string, string[]>,
    conflicts: Record<string, string[]>
  ): Promise<string[]> {
    const warnings: string[] = [];

    // 检查循环依赖
    const visited = new Set<string>();
    const recursionStack = new Set<string>();

    const hasCycle = (permId: string): boolean => {
      if (recursionStack.has(permId)) {
        return true;
      }
      if (visited.has(permId)) {
        return false;
      }

      visited.add(permId);
      recursionStack.add(permId);

      const deps = dependencies[permId] || [];
      for (const dep of deps) {
        if (hasCycle(dep)) {
          return true;
        }
      }

      recursionStack.delete(permId);
      return false;
    };

    permissions.forEach(perm => {
      if (hasCycle(perm.id)) {
        warnings.push(`检测到权限 ${perm.name} 存在循环依赖`);
      }
    });

    return warnings;
  }

  /**
   * 清除权限缓存
   */
  private async clearPermissionCache(): Promise<void> {
    try {
      await cacheService.del('permissions:*');
      logger.info('权限缓存已清除');
    } catch (error) {
      logger.error('清除权限缓存失败', { error });
    }
  }
}

// 导出单例实例
export const permissionMetadataService = new PermissionMetadataService();

/**
 * 跨应用权限申请工作流服务
 */
export class CrossAppPermissionWorkflowService {

  /**
   * 创建权限申请
   */
  async createPermissionRequest(request: {
    userId: string;
    sourceApplicationId: string;
    targetApplicationId: string;
    requestedPermissions: string[];
    justification: string;
    urgency: 'low' | 'medium' | 'high' | 'critical';
    expiresAt?: Date;
    metadata?: Record<string, any>;
  }): Promise<{
    requestId: string;
    status: 'pending' | 'approved' | 'rejected';
    approvers: string[];
    estimatedProcessingTime: number;
  }> {
    try {
      const requestId = uuidv4();

      // 验证权限申请
      const validation = await permissionMetadataService.validatePermissionRequest({
        userId: request.userId,
        applicationId: request.targetApplicationId,
        requestedPermissions: request.requestedPermissions,
        justification: request.justification,
        expiresAt: request.expiresAt
      });

      if (!validation.valid) {
        throw new Error(`权限申请验证失败: ${validation.errors.join(', ')}`);
      }

      // 确定审批者
      const approvers = await this.determineApprovers(
        request.targetApplicationId,
        validation.resolvedPermissions,
        request.urgency
      );

      // 估算处理时间
      const estimatedProcessingTime = this.estimateProcessingTime(
        validation.resolvedPermissions,
        request.urgency,
        approvers.length
      );

      // 创建申请记录
      const permissionRequest = await prisma.permissionRequest.create({
        data: {
          id: requestId,
          userId: request.userId,
          sourceApplicationId: request.sourceApplicationId,
          targetApplicationId: request.targetApplicationId,
          requestedPermissions: request.requestedPermissions,
          resolvedPermissions: validation.resolvedPermissions,
          justification: request.justification,
          urgency: request.urgency,
          status: 'pending',
          approvers: approvers,
          expiresAt: request.expiresAt,
          metadata: request.metadata || {},
          estimatedProcessingTime: estimatedProcessingTime
        }
      });

      // 发送通知给审批者
      await this.notifyApprovers(requestId, approvers);

      logger.info('跨应用权限申请创建成功', {
        requestId,
        userId: request.userId,
        targetApplicationId: request.targetApplicationId,
        permissionsCount: validation.resolvedPermissions.length
      });

      return {
        requestId,
        status: 'pending',
        approvers,
        estimatedProcessingTime
      };

    } catch (error) {
      logger.error('创建跨应用权限申请失败', { error, request });
      throw error;
    }
  }

  /**
   * 处理权限申请审批
   */
  async processApproval(
    requestId: string,
    approverId: string,
    decision: 'approve' | 'reject',
    comments?: string
  ): Promise<{
    status: 'pending' | 'approved' | 'rejected';
    nextApprovers: string[];
    completed: boolean;
  }> {
    try {
      // 获取申请详情
      const request = await prisma.permissionRequest.findUnique({
        where: { id: requestId }
      });

      if (!request) {
        throw new Error('权限申请不存在');
      }

      if (request.status !== 'pending') {
        throw new Error('权限申请已处理');
      }

      // 记录审批决定
      await prisma.permissionApproval.create({
        data: {
          id: uuidv4(),
          requestId,
          approverId,
          decision,
          comments,
          approvedAt: new Date()
        }
      });

      // 更新申请状态
      const approvers = request.approvers as string[];
      const remainingApprovers = approvers.filter(a => a !== approverId);

      let newStatus = request.status;
      let completed = false;

      if (decision === 'reject') {
        // 拒绝申请
        newStatus = 'rejected';
        completed = true;
      } else if (remainingApprovers.length === 0) {
        // 所有审批者都同意
        newStatus = 'approved';
        completed = true;

        // 授予权限
        await this.grantPermissions(request);
      }

      // 更新申请状态
      await prisma.permissionRequest.update({
        where: { id: requestId },
        data: {
          status: newStatus,
          approvers: remainingApprovers,
          processedAt: completed ? new Date() : undefined
        }
      });

      // 发送通知
      if (completed) {
        await this.notifyRequestor(requestId, newStatus);
      } else {
        await this.notifyApprovers(requestId, remainingApprovers);
      }

      logger.info('权限申请审批处理完成', {
        requestId,
        approverId,
        decision,
        newStatus,
        completed
      });

      return {
        status: newStatus,
        nextApprovers: remainingApprovers,
        completed
      };

    } catch (error) {
      logger.error('处理权限申请审批失败', { error, requestId, approverId });
      throw error;
    }
  }

  /**
   * 确定审批者
   */
  private async determineApprovers(
    applicationId: string,
    permissions: string[],
    urgency: string
  ): Promise<string[]> {
    try {
      const approvers: string[] = [];

      // 获取应用管理员
      const appAdmins = await prisma.userPermission.findMany({
        where: {
          applicationId,
          permission: {
            name: 'app.admin'
          },
          isActive: true
        },
        select: {
          userId: true
        }
      });

      approvers.push(...appAdmins.map(a => a.userId));

      // 根据权限级别确定额外审批者
      const highLevelPermissions = await prisma.permission.findMany({
        where: {
          id: { in: permissions },
          level: { in: ['admin', 'owner'] }
        }
      });

      if (highLevelPermissions.length > 0) {
        // 需要超级管理员审批
        const superAdmins = await prisma.userPermission.findMany({
          where: {
            permission: {
              name: 'system.admin'
            },
            isActive: true
          },
          select: {
            userId: true
          }
        });

        approvers.push(...superAdmins.map(a => a.userId));
      }

      // 根据紧急程度调整审批流程
      if (urgency === 'critical') {
        // 紧急情况下只需要一个管理员审批
        return approvers.slice(0, 1);
      }

      // 去重并返回
      return [...new Set(approvers)];

    } catch (error) {
      logger.error('确定审批者失败', { error, applicationId, permissions });
      return [];
    }
  }

  /**
   * 估算处理时间（小时）
   */
  private estimateProcessingTime(
    permissions: string[],
    urgency: string,
    approverCount: number
  ): number {
    let baseTime = 24; // 基础24小时

    // 根据权限数量调整
    baseTime += permissions.length * 2;

    // 根据审批者数量调整
    baseTime += approverCount * 12;

    // 根据紧急程度调整
    switch (urgency) {
      case 'critical':
        baseTime *= 0.25;
        break;
      case 'high':
        baseTime *= 0.5;
        break;
      case 'medium':
        baseTime *= 1;
        break;
      case 'low':
        baseTime *= 2;
        break;
    }

    return Math.max(1, Math.round(baseTime));
  }

  /**
   * 授予权限
   */
  private async grantPermissions(request: any): Promise<void> {
    try {
      const permissions = request.resolvedPermissions as string[];

      // 批量创建用户权限
      const userPermissions = permissions.map(permissionId => ({
        id: uuidv4(),
        userId: request.userId,
        applicationId: request.targetApplicationId,
        permissionId,
        grantedBy: 'system', // 通过工作流授予
        grantedAt: new Date(),
        expiresAt: request.expiresAt,
        isActive: true
      }));

      await prisma.userPermission.createMany({
        data: userPermissions
      });

      logger.info('权限授予成功', {
        userId: request.userId,
        applicationId: request.targetApplicationId,
        permissionsCount: permissions.length
      });

    } catch (error) {
      logger.error('权限授予失败', { error, request });
      throw error;
    }
  }

  /**
   * 通知审批者
   */
  private async notifyApprovers(requestId: string, approvers: string[]): Promise<void> {
    // TODO: 实现通知逻辑（邮件、短信、应用内通知等）
    logger.info('通知审批者', { requestId, approvers });
  }

  /**
   * 通知申请者
   */
  private async notifyRequestor(requestId: string, status: string): Promise<void> {
    // TODO: 实现通知逻辑
    logger.info('通知申请者', { requestId, status });
  }
}

// 导出跨应用权限工作流服务实例
export const crossAppPermissionWorkflowService = new CrossAppPermissionWorkflowService();
