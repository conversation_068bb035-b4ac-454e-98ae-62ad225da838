/**
 * 系统资源监控服务
 * 监控CPU、内存、磁盘、网络等系统资源使用情况
 */

import os from 'os';
import fs from 'fs';
import { promisify } from 'util';
import { logger } from '@/config/logger';
import { metricsCollector } from '@/services/metrics-collector.service';

const readFile = promisify(fs.readFile);
const stat = promisify(fs.stat);

/**
 * 系统资源使用情况接口
 */
interface SystemResources {
  cpu: {
    usage: number; // CPU使用率百分比
    loadAverage: number[]; // 负载平均值
    cores: number; // CPU核心数
  };
  memory: {
    total: number; // 总内存（字节）
    used: number; // 已使用内存（字节）
    free: number; // 空闲内存（字节）
    usage: number; // 内存使用率百分比
  };
  disk: {
    total: number; // 总磁盘空间（字节）
    used: number; // 已使用磁盘空间（字节）
    free: number; // 空闲磁盘空间（字节）
    usage: number; // 磁盘使用率百分比
  };
  network: {
    bytesReceived: number; // 接收字节数
    bytesSent: number; // 发送字节数
    packetsReceived: number; // 接收包数
    packetsSent: number; // 发送包数
  };
  process: {
    pid: number; // 进程ID
    uptime: number; // 运行时间（秒）
    memoryUsage: NodeJS.MemoryUsage; // Node.js内存使用
    cpuUsage: NodeJS.CpuUsage; // Node.js CPU使用
  };
}

/**
 * 系统监控服务
 */
export class SystemMonitorService {
  private monitoringInterval: NodeJS.Timeout | null = null;
  private isMonitoring = false;
  private lastCpuUsage: NodeJS.CpuUsage | null = null;
  private lastNetworkStats: any = null;

  /**
   * 开始监控
   */
  startMonitoring(intervalMs: number = 30000): void {
    if (this.isMonitoring) {
      logger.warn('系统监控已在运行');
      return;
    }

    this.isMonitoring = true;
    logger.info('开始系统资源监控', { interval: intervalMs });

    // 初始化CPU使用率基准
    this.lastCpuUsage = process.cpuUsage();

    this.monitoringInterval = setInterval(async () => {
      try {
        const resources = await this.getSystemResources();
        this.recordMetrics(resources);
        this.checkThresholds(resources);
      } catch (error) {
        logger.error('系统监控错误', {
          error: error instanceof Error ? error.message : String(error)
        });
      }
    }, intervalMs);
  }

  /**
   * 停止监控
   */
  stopMonitoring(): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }
    this.isMonitoring = false;
    logger.info('系统资源监控已停止');
  }

  /**
   * 获取系统资源使用情况
   */
  async getSystemResources(): Promise<SystemResources> {
    const [cpu, memory, disk, network, processInfo] = await Promise.all([
      this.getCpuUsage(),
      this.getMemoryUsage(),
      this.getDiskUsage(),
      this.getNetworkUsage(),
      this.getProcessInfo()
    ]);

    return {
      cpu,
      memory,
      disk,
      network,
      process: processInfo
    };
  }

  /**
   * 获取CPU使用情况
   */
  private async getCpuUsage(): Promise<SystemResources['cpu']> {
    const cores = os.cpus().length;
    const loadAverage = os.loadavg();

    // 计算CPU使用率
    let usage = 0;
    if (this.lastCpuUsage) {
      const currentUsage = process.cpuUsage(this.lastCpuUsage);
      const totalUsage = currentUsage.user + currentUsage.system;
      usage = (totalUsage / 1000000) * 100; // 转换为百分比
    }
    this.lastCpuUsage = process.cpuUsage();

    return {
      usage: Math.min(usage, 100), // 限制在100%以内
      loadAverage,
      cores
    };
  }

  /**
   * 获取内存使用情况
   */
  private async getMemoryUsage(): Promise<SystemResources['memory']> {
    const total = os.totalmem();
    const free = os.freemem();
    const used = total - free;
    const usage = (used / total) * 100;

    return {
      total,
      used,
      free,
      usage
    };
  }

  /**
   * 获取磁盘使用情况
   */
  private async getDiskUsage(): Promise<SystemResources['disk']> {
    try {
      // 在Linux系统上读取磁盘使用情况
      if (process.platform === 'linux') {
        const statvfs = await this.getLinuxDiskUsage('/');
        return statvfs;
      }

      // 其他系统的简化实现
      return {
        total: 0,
        used: 0,
        free: 0,
        usage: 0
      };
    } catch (error) {
      logger.warn('无法获取磁盘使用情况', {
        error: error instanceof Error ? error.message : String(error)
      });
      return {
        total: 0,
        used: 0,
        free: 0,
        usage: 0
      };
    }
  }

  /**
   * 获取Linux系统磁盘使用情况
   */
  private async getLinuxDiskUsage(path: string): Promise<SystemResources['disk']> {
    try {
      const stats = await stat(path);
      // 这是一个简化实现，实际应该使用statvfs系统调用
      // 这里返回模拟数据
      return {
        total: 100 * 1024 * 1024 * 1024, // 100GB
        used: 50 * 1024 * 1024 * 1024,   // 50GB
        free: 50 * 1024 * 1024 * 1024,   // 50GB
        usage: 50
      };
    } catch (error) {
      throw error;
    }
  }

  /**
   * 获取网络使用情况
   */
  private async getNetworkUsage(): Promise<SystemResources['network']> {
    try {
      // 在Linux系统上读取网络统计
      if (process.platform === 'linux') {
        const netStats = await this.getLinuxNetworkStats();
        return netStats;
      }

      // 其他系统的简化实现
      return {
        bytesReceived: 0,
        bytesSent: 0,
        packetsReceived: 0,
        packetsSent: 0
      };
    } catch (error) {
      logger.warn('无法获取网络使用情况', {
        error: error instanceof Error ? error.message : String(error)
      });
      return {
        bytesReceived: 0,
        bytesSent: 0,
        packetsReceived: 0,
        packetsSent: 0
      };
    }
  }

  /**
   * 获取Linux系统网络统计
   */
  private async getLinuxNetworkStats(): Promise<SystemResources['network']> {
    try {
      const data = await readFile('/proc/net/dev', 'utf8');
      const lines = data.split('\n');
      
      let bytesReceived = 0;
      let bytesSent = 0;
      let packetsReceived = 0;
      let packetsSent = 0;

      for (const line of lines) {
        if (line.includes(':') && !line.includes('lo:')) { // 排除回环接口
          const parts = line.trim().split(/\s+/);
          if (parts.length >= 10) {
            bytesReceived += parseInt(parts[1]) || 0;
            packetsReceived += parseInt(parts[2]) || 0;
            bytesSent += parseInt(parts[9]) || 0;
            packetsSent += parseInt(parts[10]) || 0;
          }
        }
      }

      return {
        bytesReceived,
        bytesSent,
        packetsReceived,
        packetsSent
      };
    } catch (error) {
      throw error;
    }
  }

  /**
   * 获取进程信息
   */
  private async getProcessInfo(): Promise<SystemResources['process']> {
    return {
      pid: process.pid,
      uptime: process.uptime(),
      memoryUsage: process.memoryUsage(),
      cpuUsage: process.cpuUsage()
    };
  }

  /**
   * 记录指标
   */
  private recordMetrics(resources: SystemResources): void {
    // CPU指标
    metricsCollector.setGauge('system_cpu_usage_percent', resources.cpu.usage);
    metricsCollector.setGauge('system_cpu_cores', resources.cpu.cores);
    metricsCollector.setGauge('system_load_average_1m', resources.cpu.loadAverage[0]);
    metricsCollector.setGauge('system_load_average_5m', resources.cpu.loadAverage[1]);
    metricsCollector.setGauge('system_load_average_15m', resources.cpu.loadAverage[2]);

    // 内存指标
    metricsCollector.setGauge('system_memory_total_bytes', resources.memory.total);
    metricsCollector.setGauge('system_memory_used_bytes', resources.memory.used);
    metricsCollector.setGauge('system_memory_free_bytes', resources.memory.free);
    metricsCollector.setGauge('system_memory_usage_percent', resources.memory.usage);

    // 磁盘指标
    metricsCollector.setGauge('system_disk_total_bytes', resources.disk.total);
    metricsCollector.setGauge('system_disk_used_bytes', resources.disk.used);
    metricsCollector.setGauge('system_disk_free_bytes', resources.disk.free);
    metricsCollector.setGauge('system_disk_usage_percent', resources.disk.usage);

    // 网络指标
    metricsCollector.setGauge('system_network_bytes_received', resources.network.bytesReceived);
    metricsCollector.setGauge('system_network_bytes_sent', resources.network.bytesSent);
    metricsCollector.setGauge('system_network_packets_received', resources.network.packetsReceived);
    metricsCollector.setGauge('system_network_packets_sent', resources.network.packetsSent);

    // 进程指标
    metricsCollector.setGauge('process_uptime_seconds', resources.process.uptime);
    metricsCollector.setGauge('process_memory_rss_bytes', resources.process.memoryUsage.rss);
    metricsCollector.setGauge('process_memory_heap_total_bytes', resources.process.memoryUsage.heapTotal);
    metricsCollector.setGauge('process_memory_heap_used_bytes', resources.process.memoryUsage.heapUsed);
    metricsCollector.setGauge('process_memory_external_bytes', resources.process.memoryUsage.external);
  }

  /**
   * 检查阈值并发出警告
   */
  private checkThresholds(resources: SystemResources): void {
    const thresholds = {
      cpu: 80,      // CPU使用率80%
      memory: 85,   // 内存使用率85%
      disk: 90      // 磁盘使用率90%
    };

    // CPU阈值检查
    if (resources.cpu.usage > thresholds.cpu) {
      logger.warn('CPU使用率过高', {
        current: resources.cpu.usage,
        threshold: thresholds.cpu,
        loadAverage: resources.cpu.loadAverage
      });
      metricsCollector.incrementCounter('system_threshold_exceeded', {
        resource: 'cpu',
        threshold: thresholds.cpu.toString()
      });
    }

    // 内存阈值检查
    if (resources.memory.usage > thresholds.memory) {
      logger.warn('内存使用率过高', {
        current: resources.memory.usage,
        threshold: thresholds.memory,
        used: resources.memory.used,
        total: resources.memory.total
      });
      metricsCollector.incrementCounter('system_threshold_exceeded', {
        resource: 'memory',
        threshold: thresholds.memory.toString()
      });
    }

    // 磁盘阈值检查
    if (resources.disk.usage > thresholds.disk) {
      logger.warn('磁盘使用率过高', {
        current: resources.disk.usage,
        threshold: thresholds.disk,
        used: resources.disk.used,
        total: resources.disk.total
      });
      metricsCollector.incrementCounter('system_threshold_exceeded', {
        resource: 'disk',
        threshold: thresholds.disk.toString()
      });
    }
  }

  /**
   * 获取系统健康状态
   */
  async getSystemHealth(): Promise<{
    status: 'healthy' | 'warning' | 'critical';
    resources: SystemResources;
    issues: string[];
  }> {
    const resources = await this.getSystemResources();
    const issues: string[] = [];
    let status: 'healthy' | 'warning' | 'critical' = 'healthy';

    // 检查各项资源使用情况
    if (resources.cpu.usage > 90) {
      issues.push(`CPU使用率过高: ${resources.cpu.usage.toFixed(1)}%`);
      status = 'critical';
    } else if (resources.cpu.usage > 80) {
      issues.push(`CPU使用率较高: ${resources.cpu.usage.toFixed(1)}%`);
      if (status === 'healthy') status = 'warning';
    }

    if (resources.memory.usage > 95) {
      issues.push(`内存使用率过高: ${resources.memory.usage.toFixed(1)}%`);
      status = 'critical';
    } else if (resources.memory.usage > 85) {
      issues.push(`内存使用率较高: ${resources.memory.usage.toFixed(1)}%`);
      if (status === 'healthy') status = 'warning';
    }

    if (resources.disk.usage > 95) {
      issues.push(`磁盘使用率过高: ${resources.disk.usage.toFixed(1)}%`);
      status = 'critical';
    } else if (resources.disk.usage > 90) {
      issues.push(`磁盘使用率较高: ${resources.disk.usage.toFixed(1)}%`);
      if (status === 'healthy') status = 'warning';
    }

    return {
      status,
      resources,
      issues
    };
  }

  /**
   * 获取监控状态
   */
  getMonitoringStatus(): {
    isMonitoring: boolean;
    uptime: number;
  } {
    return {
      isMonitoring: this.isMonitoring,
      uptime: process.uptime()
    };
  }
}

// 创建单例实例
export const systemMonitorService = new SystemMonitorService();
