/**
 * 实时威胁检测和响应服务
 * 
 * 功能说明：
 * 1. 实时监控安全威胁
 * 2. 自动威胁检测和分析
 * 3. 威胁响应和缓解措施
 * 4. 威胁情报集成
 * 5. 安全事件关联分析
 */

import { PrismaClient } from '@prisma/client';
import Redis from 'ioredis';
import { EventEmitter } from 'events';
import { Request } from 'express';
import { DataCollector, EventType } from '../analytics/DataCollector';

/**
 * 威胁类型枚举
 */
export enum ThreatType {
  BRUTE_FORCE = 'brute_force',
  CREDENTIAL_STUFFING = 'credential_stuffing',
  ACCOUNT_TAKEOVER = 'account_takeover',
  SUSPICIOUS_LOGIN = 'suspicious_login',
  IMPOSSIBLE_TRAVEL = 'impossible_travel',
  BOT_ATTACK = 'bot_attack',
  DDoS = 'ddos',
  SQL_INJECTION = 'sql_injection',
  XSS = 'xss',
  MALWARE = 'malware',
  PHISHING = 'phishing',
  DATA_EXFILTRATION = 'data_exfiltration'
}

/**
 * 威胁严重程度枚举
 */
export enum ThreatSeverity {
  INFO = 'info',
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

/**
 * 响应动作枚举
 */
export enum ResponseAction {
  LOG = 'log',                    // 仅记录
  ALERT = 'alert',               // 发送告警
  RATE_LIMIT = 'rate_limit',     // 限制频率
  BLOCK_IP = 'block_ip',         // 阻止IP
  BLOCK_USER = 'block_user',     // 阻止用户
  REQUIRE_MFA = 'require_mfa',   // 要求MFA
  QUARANTINE = 'quarantine',     // 隔离
  TERMINATE_SESSION = 'terminate_session' // 终止会话
}

/**
 * 威胁检测结果接口
 */
export interface ThreatDetection {
  id: string;
  type: ThreatType;
  severity: ThreatSeverity;
  confidence: number; // 0-1
  description: string;
  source: {
    ip: string;
    userAgent?: string;
    userId?: string;
    sessionId?: string;
  };
  indicators: Array<{
    type: string;
    value: string;
    confidence: number;
  }>;
  evidence: Record<string, any>;
  timestamp: Date;
  status: 'active' | 'mitigated' | 'false_positive';
}

/**
 * 威胁响应结果接口
 */
export interface ThreatResponse {
  detectionId: string;
  actions: ResponseAction[];
  success: boolean;
  message: string;
  timestamp: Date;
  details: Record<string, any>;
}

/**
 * 威胁检测规则接口
 */
export interface DetectionRule {
  id: string;
  name: string;
  description: string;
  threatType: ThreatType;
  severity: ThreatSeverity;
  conditions: {
    timeWindow: number; // 秒
    threshold: number;
    field: string;
    operator: 'gt' | 'lt' | 'eq' | 'contains' | 'regex';
    value: any;
  }[];
  actions: ResponseAction[];
  isActive: boolean;
  priority: number;
}

/**
 * 实时威胁检测服务类
 */
export class ThreatDetectionService extends EventEmitter {
  private prisma: PrismaClient;
  private redis: Redis;
  private dataCollector: DataCollector;
  
  // 检测规则
  private detectionRules: DetectionRule[] = [];
  
  // IP黑名单缓存
  private ipBlacklist = new Set<string>();
  
  // 用户黑名单缓存
  private userBlacklist = new Set<string>();
  
  // 检测统计
  private stats = {
    totalDetections: 0,
    activeThreats: 0,
    mitigatedThreats: 0,
    falsePositives: 0
  };

  constructor(
    prisma: PrismaClient,
    redis: Redis,
    dataCollector: DataCollector
  ) {
    super();
    this.prisma = prisma;
    this.redis = redis;
    this.dataCollector = dataCollector;
    
    this.initializeDetectionRules();
    this.startThreatMonitoring();
  }

  /**
   * 初始化检测规则
   */
  private initializeDetectionRules(): void {
    this.detectionRules = [
      {
        id: 'brute_force_login',
        name: '暴力破解检测',
        description: '检测短时间内多次登录失败',
        threatType: ThreatType.BRUTE_FORCE,
        severity: ThreatSeverity.HIGH,
        conditions: [
          {
            timeWindow: 300, // 5分钟
            threshold: 5,
            field: 'failed_logins',
            operator: 'gt',
            value: 5
          }
        ],
        actions: [ResponseAction.BLOCK_IP, ResponseAction.ALERT],
        isActive: true,
        priority: 1
      },
      {
        id: 'impossible_travel',
        name: '不可能旅行检测',
        description: '检测地理位置异常跳跃',
        threatType: ThreatType.IMPOSSIBLE_TRAVEL,
        severity: ThreatSeverity.MEDIUM,
        conditions: [
          {
            timeWindow: 7200, // 2小时
            threshold: 1,
            field: 'location_jump',
            operator: 'gt',
            value: 1000 // 1000公里
          }
        ],
        actions: [ResponseAction.REQUIRE_MFA, ResponseAction.ALERT],
        isActive: true,
        priority: 2
      },
      {
        id: 'suspicious_user_agent',
        name: '可疑User-Agent检测',
        description: '检测自动化工具和恶意User-Agent',
        threatType: ThreatType.BOT_ATTACK,
        severity: ThreatSeverity.MEDIUM,
        conditions: [
          {
            timeWindow: 60,
            threshold: 1,
            field: 'user_agent',
            operator: 'regex',
            value: /(bot|crawler|scanner|curl|wget|python|java)/i
          }
        ],
        actions: [ResponseAction.RATE_LIMIT, ResponseAction.LOG],
        isActive: true,
        priority: 3
      },
      {
        id: 'high_frequency_requests',
        name: '高频请求检测',
        description: '检测异常高频的API请求',
        threatType: ThreatType.DDoS,
        severity: ThreatSeverity.HIGH,
        conditions: [
          {
            timeWindow: 60, // 1分钟
            threshold: 100,
            field: 'request_count',
            operator: 'gt',
            value: 100
          }
        ],
        actions: [ResponseAction.RATE_LIMIT, ResponseAction.BLOCK_IP],
        isActive: true,
        priority: 4
      },
      {
        id: 'credential_stuffing',
        name: '凭据填充检测',
        description: '检测使用泄露凭据的登录尝试',
        threatType: ThreatType.CREDENTIAL_STUFFING,
        severity: ThreatSeverity.HIGH,
        conditions: [
          {
            timeWindow: 300,
            threshold: 3,
            field: 'different_users_same_ip',
            operator: 'gt',
            value: 3
          }
        ],
        actions: [ResponseAction.BLOCK_IP, ResponseAction.ALERT],
        isActive: true,
        priority: 5
      }
    ];
  }

  /**
   * 开始威胁监控
   */
  private startThreatMonitoring(): void {
    // 监听数据收集器的事件
    this.dataCollector.on('eventCollected', (eventData) => {
      this.analyzeEvent(eventData).catch(console.error);
    });

    // 监听关键事件
    this.dataCollector.on('criticalEvent', (eventData) => {
      this.handleCriticalEvent(eventData).catch(console.error);
    });

    // 定期清理过期数据
    setInterval(() => {
      this.cleanupExpiredData().catch(console.error);
    }, 60000); // 每分钟清理一次

    console.log('威胁检测服务已启动');
  }

  /**
   * 分析事件数据
   */
  private async analyzeEvent(eventData: any): Promise<void> {
    try {
      // 对每个检测规则进行评估
      for (const rule of this.detectionRules) {
        if (!rule.isActive) continue;

        const detection = await this.evaluateRule(rule, eventData);
        if (detection) {
          await this.handleThreatDetection(detection);
        }
      }
    } catch (error) {
      console.error('事件分析失败:', error);
    }
  }

  /**
   * 评估检测规则
   */
  private async evaluateRule(rule: DetectionRule, eventData: any): Promise<ThreatDetection | null> {
    try {
      let allConditionsMet = true;
      const evidence: Record<string, any> = {};

      for (const condition of rule.conditions) {
        const result = await this.evaluateCondition(condition, eventData);
        evidence[condition.field] = result;
        
        if (!result.met) {
          allConditionsMet = false;
          break;
        }
      }

      if (!allConditionsMet) {
        return null;
      }

      // 创建威胁检测结果
      const detection: ThreatDetection = {
        id: `threat_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        type: rule.threatType,
        severity: rule.severity,
        confidence: this.calculateConfidence(rule, evidence),
        description: rule.description,
        source: {
          ip: eventData.ip,
          userAgent: eventData.userAgent,
          userId: eventData.userId,
          sessionId: eventData.sessionId
        },
        indicators: this.extractIndicators(eventData, rule),
        evidence,
        timestamp: new Date(),
        status: 'active'
      };

      return detection;
    } catch (error) {
      console.error('规则评估失败:', error);
      return null;
    }
  }

  /**
   * 评估单个条件
   */
  private async evaluateCondition(condition: any, eventData: any): Promise<{ met: boolean; value: any; count?: number }> {
    const { timeWindow, threshold, field, operator, value } = condition;
    const now = Date.now();
    const windowStart = now - (timeWindow * 1000);

    switch (field) {
      case 'failed_logins':
        return this.evaluateFailedLogins(eventData.ip, windowStart, threshold);
        
      case 'location_jump':
        return this.evaluateLocationJump(eventData.userId, eventData.country, windowStart);
        
      case 'user_agent':
        return this.evaluateUserAgent(eventData.userAgent, operator, value);
        
      case 'request_count':
        return this.evaluateRequestCount(eventData.ip, windowStart, threshold);
        
      case 'different_users_same_ip':
        return this.evaluateDifferentUsers(eventData.ip, windowStart, threshold);
        
      default:
        return { met: false, value: null };
    }
  }

  /**
   * 评估登录失败次数
   */
  private async evaluateFailedLogins(ip: string, windowStart: number, threshold: number): Promise<{ met: boolean; value: any; count: number }> {
    const key = `failed_logins:${ip}`;
    const count = await this.redis.zcount(key, windowStart, '+inf');
    
    return {
      met: count >= threshold,
      value: count,
      count
    };
  }

  /**
   * 评估地理位置跳跃
   */
  private async evaluateLocationJump(userId: string, currentCountry: string, windowStart: number): Promise<{ met: boolean; value: any }> {
    if (!userId || !currentCountry) {
      return { met: false, value: null };
    }

    // 获取用户最近的位置记录
    const lastLocation = await this.redis.get(`last_location:${userId}`);
    if (!lastLocation) {
      // 记录当前位置
      await this.redis.setex(`last_location:${userId}`, 7200, JSON.stringify({
        country: currentCountry,
        timestamp: Date.now()
      }));
      return { met: false, value: null };
    }

    const lastLocationData = JSON.parse(lastLocation);
    const timeDiff = Date.now() - lastLocationData.timestamp;

    // 如果在时间窗口内且位置不同
    if (timeDiff < 7200000 && lastLocationData.country !== currentCountry) { // 2小时
      // 简化的距离计算（实际应该使用地理坐标）
      const distance = this.calculateDistance(lastLocationData.country, currentCountry);
      
      // 更新位置记录
      await this.redis.setex(`last_location:${userId}`, 7200, JSON.stringify({
        country: currentCountry,
        timestamp: Date.now()
      }));

      return {
        met: distance > 1000, // 1000公里
        value: { distance, timeDiff, from: lastLocationData.country, to: currentCountry }
      };
    }

    return { met: false, value: null };
  }

  /**
   * 评估User-Agent
   */
  private evaluateUserAgent(userAgent: string, operator: string, pattern: any): { met: boolean; value: any } {
    if (!userAgent) {
      return { met: true, value: 'empty_user_agent' };
    }

    switch (operator) {
      case 'regex':
        const regex = new RegExp(pattern);
        return {
          met: regex.test(userAgent),
          value: userAgent
        };
        
      case 'contains':
        return {
          met: userAgent.toLowerCase().includes(pattern.toLowerCase()),
          value: userAgent
        };
        
      default:
        return { met: false, value: userAgent };
    }
  }

  /**
   * 评估请求频率
   */
  private async evaluateRequestCount(ip: string, windowStart: number, threshold: number): Promise<{ met: boolean; value: any; count: number }> {
    const key = `requests:${ip}`;
    const count = await this.redis.zcount(key, windowStart, '+inf');
    
    return {
      met: count >= threshold,
      value: count,
      count
    };
  }

  /**
   * 评估同IP不同用户
   */
  private async evaluateDifferentUsers(ip: string, windowStart: number, threshold: number): Promise<{ met: boolean; value: any; count: number }> {
    const key = `users_by_ip:${ip}`;
    const users = await this.redis.zrangebyscore(key, windowStart, '+inf');
    const uniqueUsers = new Set(users).size;
    
    return {
      met: uniqueUsers >= threshold,
      value: uniqueUsers,
      count: uniqueUsers
    };
  }

  /**
   * 处理威胁检测
   */
  private async handleThreatDetection(detection: ThreatDetection): Promise<void> {
    try {
      // 保存检测结果
      await this.saveThreatDetection(detection);
      
      // 更新统计
      this.stats.totalDetections++;
      this.stats.activeThreats++;
      
      // 发出威胁检测事件
      this.emit('threatDetected', detection);
      
      // 执行响应动作
      const rule = this.detectionRules.find(r => r.threatType === detection.type);
      if (rule) {
        const response = await this.executeThreatResponse(detection, rule.actions);
        this.emit('threatResponse', response);
      }
      
      console.log(`威胁检测: ${detection.type} - ${detection.description}`);
    } catch (error) {
      console.error('威胁检测处理失败:', error);
    }
  }

  /**
   * 执行威胁响应
   */
  private async executeThreatResponse(detection: ThreatDetection, actions: ResponseAction[]): Promise<ThreatResponse> {
    const response: ThreatResponse = {
      detectionId: detection.id,
      actions,
      success: true,
      message: '威胁响应执行成功',
      timestamp: new Date(),
      details: {}
    };

    try {
      for (const action of actions) {
        switch (action) {
          case ResponseAction.LOG:
            await this.logThreat(detection);
            break;
            
          case ResponseAction.ALERT:
            await this.sendAlert(detection);
            break;
            
          case ResponseAction.RATE_LIMIT:
            await this.applyRateLimit(detection.source.ip);
            break;
            
          case ResponseAction.BLOCK_IP:
            await this.blockIP(detection.source.ip);
            break;
            
          case ResponseAction.BLOCK_USER:
            if (detection.source.userId) {
              await this.blockUser(detection.source.userId);
            }
            break;
            
          case ResponseAction.REQUIRE_MFA:
            if (detection.source.userId) {
              await this.requireMFA(detection.source.userId);
            }
            break;
            
          case ResponseAction.TERMINATE_SESSION:
            if (detection.source.sessionId) {
              await this.terminateSession(detection.source.sessionId);
            }
            break;
        }
      }
    } catch (error) {
      response.success = false;
      response.message = `威胁响应执行失败: ${error.message}`;
      console.error('威胁响应执行失败:', error);
    }

    return response;
  }

  /**
   * 处理关键事件
   */
  private async handleCriticalEvent(eventData: any): Promise<void> {
    // 对关键事件进行特殊处理
    const detection: ThreatDetection = {
      id: `critical_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type: ThreatType.SUSPICIOUS_LOGIN,
      severity: ThreatSeverity.CRITICAL,
      confidence: 1.0,
      description: '检测到关键安全事件',
      source: {
        ip: eventData.ip,
        userAgent: eventData.userAgent,
        userId: eventData.userId,
        sessionId: eventData.sessionId
      },
      indicators: [],
      evidence: { eventData },
      timestamp: new Date(),
      status: 'active'
    };

    await this.handleThreatDetection(detection);
  }

  /**
   * 计算置信度
   */
  private calculateConfidence(rule: DetectionRule, evidence: Record<string, any>): number {
    // 简化的置信度计算
    let confidence = 0.5; // 基础置信度
    
    // 根据规则优先级调整
    confidence += (rule.priority / 10) * 0.2;
    
    // 根据证据强度调整
    const evidenceCount = Object.keys(evidence).length;
    confidence += (evidenceCount / rule.conditions.length) * 0.3;
    
    return Math.min(1.0, confidence);
  }

  /**
   * 提取威胁指标
   */
  private extractIndicators(eventData: any, rule: DetectionRule): Array<{ type: string; value: string; confidence: number }> {
    const indicators = [];
    
    if (eventData.ip) {
      indicators.push({
        type: 'ip',
        value: eventData.ip,
        confidence: 0.8
      });
    }
    
    if (eventData.userAgent) {
      indicators.push({
        type: 'user_agent',
        value: eventData.userAgent,
        confidence: 0.6
      });
    }
    
    return indicators;
  }

  /**
   * 保存威胁检测结果
   */
  private async saveThreatDetection(detection: ThreatDetection): Promise<void> {
    await this.prisma.securityEvent.create({
      data: {
        type: detection.type,
        severity: detection.severity,
        userId: detection.source.userId,
        ip: detection.source.ip,
        userAgent: detection.source.userAgent,
        description: detection.description,
        details: {
          detectionId: detection.id,
          confidence: detection.confidence,
          indicators: detection.indicators,
          evidence: detection.evidence
        }
      }
    });
  }

  // 响应动作实现（简化）
  private async logThreat(detection: ThreatDetection): Promise<void> {
    console.log(`威胁日志: ${JSON.stringify(detection)}`);
  }

  private async sendAlert(detection: ThreatDetection): Promise<void> {
    console.log(`威胁告警: ${detection.type} - ${detection.description}`);
    // 实际实现中应该发送邮件、短信或推送通知
  }

  private async applyRateLimit(ip: string): Promise<void> {
    await this.redis.setex(`rate_limit:${ip}`, 3600, '1'); // 1小时限制
    console.log(`应用速率限制: ${ip}`);
  }

  private async blockIP(ip: string): Promise<void> {
    this.ipBlacklist.add(ip);
    await this.redis.sadd('blocked_ips', ip);
    console.log(`阻止IP: ${ip}`);
  }

  private async blockUser(userId: string): Promise<void> {
    this.userBlacklist.add(userId);
    await this.redis.sadd('blocked_users', userId);
    console.log(`阻止用户: ${userId}`);
  }

  private async requireMFA(userId: string): Promise<void> {
    await this.redis.setex(`require_mfa:${userId}`, 3600, '1');
    console.log(`要求MFA: ${userId}`);
  }

  private async terminateSession(sessionId: string): Promise<void> {
    await this.redis.del(`session:${sessionId}`);
    console.log(`终止会话: ${sessionId}`);
  }

  /**
   * 清理过期数据
   */
  private async cleanupExpiredData(): Promise<void> {
    const now = Date.now();
    const oneHourAgo = now - 3600000;

    // 清理过期的计数器
    const keys = await this.redis.keys('failed_logins:*');
    for (const key of keys) {
      await this.redis.zremrangebyscore(key, '-inf', oneHourAgo);
    }
  }

  /**
   * 计算地理距离（简化实现）
   */
  private calculateDistance(country1: string, country2: string): number {
    // 简化的距离计算，实际应该使用地理坐标
    const distances: Record<string, Record<string, number>> = {
      'CN': { 'US': 11000, 'JP': 1500, 'KR': 950 },
      'US': { 'CN': 11000, 'JP': 10000, 'KR': 10500 },
      'JP': { 'CN': 1500, 'US': 10000, 'KR': 550 }
    };
    
    return distances[country1]?.[country2] || 5000; // 默认5000公里
  }

  /**
   * 获取威胁统计
   */
  getStats(): typeof this.stats {
    return { ...this.stats };
  }

  /**
   * 检查IP是否被阻止
   */
  isIPBlocked(ip: string): boolean {
    return this.ipBlacklist.has(ip);
  }

  /**
   * 检查用户是否被阻止
   */
  isUserBlocked(userId: string): boolean {
    return this.userBlacklist.has(userId);
  }
}

export default ThreatDetectionService;
