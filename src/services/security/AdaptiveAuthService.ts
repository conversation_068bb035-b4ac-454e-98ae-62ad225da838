/**
 * 自适应认证服务
 * 
 * 功能说明：
 * 1. 根据风险评估结果动态调整认证强度
 * 2. 实现多因素认证流程管理
 * 3. 提供认证决策引擎
 * 4. 集成各种认证方式
 * 5. 支持认证策略配置
 */

import { PrismaClient } from '@prisma/client';
import Redis from 'ioredis';
import { Request, Response } from 'express';
import { RiskAssessmentEngine, RiskLevel, AuthenticationStrength } from './RiskAssessmentEngine';
import { MFAService } from '../auth/MFAService';
import { NotificationService } from '../notification/NotificationService';

/**
 * 认证步骤枚举
 */
export enum AuthStep {
  PRIMARY = 'primary',           // 主要认证（用户名密码）
  MFA_SMS = 'mfa_sms',          // 短信验证
  MFA_TOTP = 'mfa_totp',        // TOTP验证
  MFA_PUSH = 'mfa_push',        // 推送验证
  BIOMETRIC = 'biometric',       // 生物识别
  SECURITY_QUESTIONS = 'security_questions', // 安全问题
  EMAIL_VERIFICATION = 'email_verification', // 邮箱验证
  ADMIN_APPROVAL = 'admin_approval',         // 管理员审批
  DEVICE_VERIFICATION = 'device_verification', // 设备验证
  LOCATION_VERIFICATION = 'location_verification' // 位置验证
}

/**
 * 认证会话状态枚举
 */
export enum AuthSessionStatus {
  PENDING = 'pending',           // 等待中
  IN_PROGRESS = 'in_progress',   // 进行中
  COMPLETED = 'completed',       // 已完成
  FAILED = 'failed',            // 失败
  EXPIRED = 'expired',          // 已过期
  BLOCKED = 'blocked'           // 被阻止
}

/**
 * 认证会话接口
 */
export interface AuthSession {
  id: string;
  userId: string;
  sessionId: string;
  status: AuthSessionStatus;
  riskLevel: RiskLevel;
  requiredSteps: AuthStep[];
  completedSteps: AuthStep[];
  currentStep?: AuthStep;
  attempts: number;
  maxAttempts: number;
  createdAt: Date;
  expiresAt: Date;
  metadata: Record<string, any>;
}

/**
 * 认证结果接口
 */
export interface AuthResult {
  success: boolean;
  sessionId: string;
  nextStep?: AuthStep;
  remainingSteps: AuthStep[];
  message: string;
  requiresAction?: {
    type: string;
    data: Record<string, any>;
  };
  expiresAt?: Date;
}

/**
 * 认证策略配置接口
 */
export interface AuthPolicy {
  id: string;
  name: string;
  description: string;
  conditions: {
    riskLevels: RiskLevel[];
    userGroups?: string[];
    timeWindows?: Array<{ start: string; end: string }>;
    locations?: string[];
  };
  requirements: {
    steps: AuthStep[];
    maxAttempts: number;
    sessionTimeout: number; // 分钟
    allowFallback: boolean;
  };
  isActive: boolean;
  priority: number;
}

/**
 * 自适应认证服务类
 */
export class AdaptiveAuthService {
  private prisma: PrismaClient;
  private redis: Redis;
  private riskEngine: RiskAssessmentEngine;
  private mfaService: MFAService;
  private notificationService: NotificationService;
  
  // 默认认证策略
  private readonly DEFAULT_POLICIES: AuthPolicy[] = [
    {
      id: 'very_low_risk',
      name: '极低风险策略',
      description: '适用于极低风险用户的基础认证',
      conditions: { riskLevels: [RiskLevel.VERY_LOW] },
      requirements: {
        steps: [AuthStep.PRIMARY],
        maxAttempts: 5,
        sessionTimeout: 480, // 8小时
        allowFallback: true
      },
      isActive: true,
      priority: 1
    },
    {
      id: 'low_risk',
      name: '低风险策略',
      description: '适用于低风险用户的标准认证',
      conditions: { riskLevels: [RiskLevel.LOW] },
      requirements: {
        steps: [AuthStep.PRIMARY],
        maxAttempts: 5,
        sessionTimeout: 240, // 4小时
        allowFallback: true
      },
      isActive: true,
      priority: 2
    },
    {
      id: 'medium_risk',
      name: '中等风险策略',
      description: '适用于中等风险用户的增强认证',
      conditions: { riskLevels: [RiskLevel.MEDIUM] },
      requirements: {
        steps: [AuthStep.PRIMARY, AuthStep.MFA_SMS],
        maxAttempts: 3,
        sessionTimeout: 120, // 2小时
        allowFallback: true
      },
      isActive: true,
      priority: 3
    },
    {
      id: 'high_risk',
      name: '高风险策略',
      description: '适用于高风险用户的严格认证',
      conditions: { riskLevels: [RiskLevel.HIGH] },
      requirements: {
        steps: [AuthStep.PRIMARY, AuthStep.MFA_TOTP, AuthStep.EMAIL_VERIFICATION],
        maxAttempts: 3,
        sessionTimeout: 60, // 1小时
        allowFallback: false
      },
      isActive: true,
      priority: 4
    },
    {
      id: 'very_high_risk',
      name: '极高风险策略',
      description: '适用于极高风险用户的最严格认证',
      conditions: { riskLevels: [RiskLevel.VERY_HIGH] },
      requirements: {
        steps: [
          AuthStep.PRIMARY,
          AuthStep.MFA_PUSH,
          AuthStep.SECURITY_QUESTIONS,
          AuthStep.ADMIN_APPROVAL
        ],
        maxAttempts: 2,
        sessionTimeout: 30, // 30分钟
        allowFallback: false
      },
      isActive: true,
      priority: 5
    }
  ];

  constructor(
    prisma: PrismaClient,
    redis: Redis,
    riskEngine: RiskAssessmentEngine,
    mfaService: MFAService,
    notificationService: NotificationService
  ) {
    this.prisma = prisma;
    this.redis = redis;
    this.riskEngine = riskEngine;
    this.mfaService = mfaService;
    this.notificationService = notificationService;
  }

  /**
   * 开始自适应认证流程
   */
  async startAuthentication(
    userId: string,
    req: Request,
    primaryCredentials: { username: string; password: string }
  ): Promise<AuthResult> {
    try {
      // 1. 验证主要凭据
      const user = await this.validatePrimaryCredentials(primaryCredentials);
      if (!user) {
        return {
          success: false,
          sessionId: '',
          remainingSteps: [],
          message: '用户名或密码错误'
        };
      }

      // 2. 执行风险评估
      const riskAssessment = await this.riskEngine.assessRisk(userId, req);
      
      // 3. 如果风险级别为关键，直接阻止访问
      if (riskAssessment.riskLevel === RiskLevel.CRITICAL) {
        await this.logSecurityEvent(userId, 'CRITICAL_RISK_BLOCKED', req, riskAssessment);
        return {
          success: false,
          sessionId: '',
          remainingSteps: [],
          message: '访问被阻止：检测到高风险活动'
        };
      }

      // 4. 根据风险级别确定认证策略
      const policy = await this.selectAuthPolicy(riskAssessment.riskLevel, user);
      
      // 5. 创建认证会话
      const authSession = await this.createAuthSession(
        userId,
        req.session?.id || '',
        riskAssessment.riskLevel,
        policy
      );

      // 6. 标记主要认证步骤为完成
      await this.completeAuthStep(authSession.id, AuthStep.PRIMARY);

      // 7. 确定下一步
      const nextStep = this.getNextAuthStep(authSession);
      
      if (!nextStep) {
        // 认证完成
        await this.completeAuthentication(authSession.id);
        return {
          success: true,
          sessionId: authSession.id,
          remainingSteps: [],
          message: '认证成功'
        };
      }

      // 8. 准备下一步认证
      const actionData = await this.prepareAuthStep(nextStep, user, req);

      return {
        success: false, // 还需要额外步骤
        sessionId: authSession.id,
        nextStep,
        remainingSteps: authSession.requiredSteps.filter(
          step => !authSession.completedSteps.includes(step)
        ),
        message: `需要完成${this.getStepDescription(nextStep)}`,
        requiresAction: {
          type: nextStep,
          data: actionData
        },
        expiresAt: authSession.expiresAt
      };

    } catch (error) {
      console.error('自适应认证失败:', error);
      throw error;
    }
  }

  /**
   * 继续认证流程
   */
  async continueAuthentication(
    sessionId: string,
    step: AuthStep,
    credentials: Record<string, any>,
    req: Request
  ): Promise<AuthResult> {
    try {
      // 1. 获取认证会话
      const authSession = await this.getAuthSession(sessionId);
      if (!authSession) {
        return {
          success: false,
          sessionId,
          remainingSteps: [],
          message: '认证会话不存在或已过期'
        };
      }

      // 2. 检查会话状态
      if (authSession.status !== AuthSessionStatus.IN_PROGRESS) {
        return {
          success: false,
          sessionId,
          remainingSteps: [],
          message: '认证会话状态无效'
        };
      }

      // 3. 验证当前步骤
      if (authSession.currentStep !== step) {
        return {
          success: false,
          sessionId,
          remainingSteps: authSession.requiredSteps.filter(
            s => !authSession.completedSteps.includes(s)
          ),
          message: '认证步骤不匹配'
        };
      }

      // 4. 执行步骤验证
      const stepResult = await this.validateAuthStep(step, credentials, authSession, req);
      
      if (!stepResult.success) {
        // 增加尝试次数
        await this.incrementAuthAttempts(sessionId);
        
        // 检查是否超过最大尝试次数
        if (authSession.attempts + 1 >= authSession.maxAttempts) {
          await this.failAuthentication(sessionId, '超过最大尝试次数');
          return {
            success: false,
            sessionId,
            remainingSteps: [],
            message: '认证失败：超过最大尝试次数'
          };
        }

        return {
          success: false,
          sessionId,
          remainingSteps: authSession.requiredSteps.filter(
            s => !authSession.completedSteps.includes(s)
          ),
          message: stepResult.message
        };
      }

      // 5. 标记步骤完成
      await this.completeAuthStep(sessionId, step);

      // 6. 获取更新后的会话
      const updatedSession = await this.getAuthSession(sessionId);
      if (!updatedSession) {
        throw new Error('无法获取更新后的认证会话');
      }

      // 7. 确定下一步
      const nextStep = this.getNextAuthStep(updatedSession);
      
      if (!nextStep) {
        // 认证完成
        await this.completeAuthentication(sessionId);
        return {
          success: true,
          sessionId,
          remainingSteps: [],
          message: '认证成功'
        };
      }

      // 8. 准备下一步
      const user = await this.prisma.user.findUnique({
        where: { id: updatedSession.userId }
      });
      
      const actionData = await this.prepareAuthStep(nextStep, user!, req);

      return {
        success: false,
        sessionId,
        nextStep,
        remainingSteps: updatedSession.requiredSteps.filter(
          s => !updatedSession.completedSteps.includes(s)
        ),
        message: `需要完成${this.getStepDescription(nextStep)}`,
        requiresAction: {
          type: nextStep,
          data: actionData
        },
        expiresAt: updatedSession.expiresAt
      };

    } catch (error) {
      console.error('继续认证失败:', error);
      throw error;
    }
  }

  /**
   * 验证主要凭据
   */
  private async validatePrimaryCredentials(credentials: {
    username: string;
    password: string;
  }): Promise<any> {
    // 实际实现中应该验证用户名和密码
    const user = await this.prisma.user.findFirst({
      where: {
        OR: [
          { email: credentials.username },
          { username: credentials.username }
        ]
      }
    });

    if (!user) return null;

    // 这里应该验证密码哈希
    // const isValidPassword = await bcrypt.compare(credentials.password, user.passwordHash);
    // return isValidPassword ? user : null;
    
    return user; // 简化实现
  }

  /**
   * 选择认证策略
   */
  private async selectAuthPolicy(riskLevel: RiskLevel, user: any): Promise<AuthPolicy> {
    // 查找匹配的策略
    const matchingPolicies = this.DEFAULT_POLICIES.filter(policy =>
      policy.isActive && policy.conditions.riskLevels.includes(riskLevel)
    );

    // 按优先级排序，返回最高优先级的策略
    matchingPolicies.sort((a, b) => b.priority - a.priority);
    
    return matchingPolicies[0] || this.DEFAULT_POLICIES[0];
  }

  /**
   * 创建认证会话
   */
  private async createAuthSession(
    userId: string,
    sessionId: string,
    riskLevel: RiskLevel,
    policy: AuthPolicy
  ): Promise<AuthSession> {
    const authSession: AuthSession = {
      id: `auth_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      userId,
      sessionId,
      status: AuthSessionStatus.IN_PROGRESS,
      riskLevel,
      requiredSteps: policy.requirements.steps,
      completedSteps: [],
      currentStep: policy.requirements.steps[0],
      attempts: 0,
      maxAttempts: policy.requirements.maxAttempts,
      createdAt: new Date(),
      expiresAt: new Date(Date.now() + policy.requirements.sessionTimeout * 60 * 1000),
      metadata: {
        policyId: policy.id,
        policyName: policy.name
      }
    };

    // 保存到Redis
    await this.redis.setex(
      `auth_session:${authSession.id}`,
      policy.requirements.sessionTimeout * 60,
      JSON.stringify(authSession)
    );

    return authSession;
  }

  /**
   * 获取认证会话
   */
  private async getAuthSession(sessionId: string): Promise<AuthSession | null> {
    const data = await this.redis.get(`auth_session:${sessionId}`);
    return data ? JSON.parse(data) : null;
  }

  /**
   * 更新认证会话
   */
  private async updateAuthSession(authSession: AuthSession): Promise<void> {
    const ttl = Math.max(0, Math.floor((authSession.expiresAt.getTime() - Date.now()) / 1000));
    await this.redis.setex(
      `auth_session:${authSession.id}`,
      ttl,
      JSON.stringify(authSession)
    );
  }

  /**
   * 完成认证步骤
   */
  private async completeAuthStep(sessionId: string, step: AuthStep): Promise<void> {
    const authSession = await this.getAuthSession(sessionId);
    if (!authSession) return;

    authSession.completedSteps.push(step);
    authSession.currentStep = this.getNextAuthStep(authSession);
    
    await this.updateAuthSession(authSession);
  }

  /**
   * 获取下一个认证步骤
   */
  private getNextAuthStep(authSession: AuthSession): AuthStep | undefined {
    return authSession.requiredSteps.find(
      step => !authSession.completedSteps.includes(step)
    );
  }

  /**
   * 验证认证步骤
   */
  private async validateAuthStep(
    step: AuthStep,
    credentials: Record<string, any>,
    authSession: AuthSession,
    req: Request
  ): Promise<{ success: boolean; message: string }> {
    switch (step) {
      case AuthStep.MFA_SMS:
        return this.mfaService.verifySMS(authSession.userId, credentials.code);
        
      case AuthStep.MFA_TOTP:
        return this.mfaService.verifyTOTP(authSession.userId, credentials.code);
        
      case AuthStep.MFA_PUSH:
        return this.mfaService.verifyPush(authSession.userId, credentials.token);
        
      case AuthStep.EMAIL_VERIFICATION:
        return this.validateEmailVerification(authSession.userId, credentials.code);
        
      case AuthStep.SECURITY_QUESTIONS:
        return this.validateSecurityQuestions(authSession.userId, credentials.answers);
        
      default:
        return { success: false, message: '不支持的认证步骤' };
    }
  }

  /**
   * 准备认证步骤
   */
  private async prepareAuthStep(
    step: AuthStep,
    user: any,
    req: Request
  ): Promise<Record<string, any>> {
    switch (step) {
      case AuthStep.MFA_SMS:
        await this.mfaService.sendSMS(user.id, user.phone);
        return { message: '短信验证码已发送' };
        
      case AuthStep.MFA_TOTP:
        return { message: '请输入TOTP验证码' };
        
      case AuthStep.MFA_PUSH:
        const pushToken = await this.mfaService.sendPush(user.id);
        return { token: pushToken, message: '推送通知已发送' };
        
      case AuthStep.EMAIL_VERIFICATION:
        await this.sendEmailVerification(user.id, user.email);
        return { message: '邮箱验证码已发送' };
        
      case AuthStep.SECURITY_QUESTIONS:
        const questions = await this.getSecurityQuestions(user.id);
        return { questions, message: '请回答安全问题' };
        
      default:
        return {};
    }
  }

  /**
   * 增加认证尝试次数
   */
  private async incrementAuthAttempts(sessionId: string): Promise<void> {
    const authSession = await this.getAuthSession(sessionId);
    if (!authSession) return;

    authSession.attempts++;
    await this.updateAuthSession(authSession);
  }

  /**
   * 完成认证
   */
  private async completeAuthentication(sessionId: string): Promise<void> {
    const authSession = await this.getAuthSession(sessionId);
    if (!authSession) return;

    authSession.status = AuthSessionStatus.COMPLETED;
    await this.updateAuthSession(authSession);

    // 记录成功的认证事件
    console.log(`认证成功 - 用户: ${authSession.userId}, 会话: ${sessionId}`);
  }

  /**
   * 认证失败
   */
  private async failAuthentication(sessionId: string, reason: string): Promise<void> {
    const authSession = await this.getAuthSession(sessionId);
    if (!authSession) return;

    authSession.status = AuthSessionStatus.FAILED;
    authSession.metadata.failureReason = reason;
    await this.updateAuthSession(authSession);

    // 记录失败的认证事件
    console.log(`认证失败 - 用户: ${authSession.userId}, 原因: ${reason}`);
  }

  /**
   * 获取步骤描述
   */
  private getStepDescription(step: AuthStep): string {
    const descriptions = {
      [AuthStep.PRIMARY]: '用户名密码验证',
      [AuthStep.MFA_SMS]: '短信验证',
      [AuthStep.MFA_TOTP]: 'TOTP验证',
      [AuthStep.MFA_PUSH]: '推送验证',
      [AuthStep.BIOMETRIC]: '生物识别验证',
      [AuthStep.SECURITY_QUESTIONS]: '安全问题验证',
      [AuthStep.EMAIL_VERIFICATION]: '邮箱验证',
      [AuthStep.ADMIN_APPROVAL]: '管理员审批',
      [AuthStep.DEVICE_VERIFICATION]: '设备验证',
      [AuthStep.LOCATION_VERIFICATION]: '位置验证'
    };
    
    return descriptions[step] || '未知验证步骤';
  }

  // 辅助方法（简化实现）
  private async validateEmailVerification(userId: string, code: string): Promise<{ success: boolean; message: string }> {
    // 实际实现中应该验证邮箱验证码
    return { success: true, message: '邮箱验证成功' };
  }

  private async validateSecurityQuestions(userId: string, answers: string[]): Promise<{ success: boolean; message: string }> {
    // 实际实现中应该验证安全问题答案
    return { success: true, message: '安全问题验证成功' };
  }

  private async sendEmailVerification(userId: string, email: string): Promise<void> {
    // 实际实现中应该发送邮箱验证码
    console.log(`发送邮箱验证码到: ${email}`);
  }

  private async getSecurityQuestions(userId: string): Promise<Array<{ id: string; question: string }>> {
    // 实际实现中应该获取用户的安全问题
    return [
      { id: '1', question: '您的第一个宠物的名字是什么？' },
      { id: '2', question: '您母亲的姓名是什么？' }
    ];
  }

  private async logSecurityEvent(
    userId: string,
    eventType: string,
    req: Request,
    riskAssessment: any
  ): Promise<void> {
    await this.prisma.securityEvent.create({
      data: {
        type: eventType,
        severity: 'critical',
        userId,
        ip: req.ip || '127.0.0.1',
        userAgent: req.get('User-Agent'),
        description: `关键风险级别检测，访问被阻止`,
        details: {
          riskScore: riskAssessment.overallScore,
          riskLevel: riskAssessment.riskLevel,
          factors: riskAssessment.factors
        }
      }
    });
  }
}

export default AdaptiveAuthService;
