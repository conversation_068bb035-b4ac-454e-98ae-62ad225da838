/**
 * 多因素认证(MFA)服务
 * 处理TOTP、邮件验证码、短信验证码等MFA功能
 */

import speakeasy from 'speakeasy';
import QRCode from 'qrcode';
import { prisma } from '@/config/database';
import { logger, logAuditEvent, logSecurityEvent } from '@/config/logger';
import { generateVerificationCode } from '@/utils/password';
import { MFADevice, User } from '@prisma/client';
import { TOTPSetup, MFADeviceSecure } from '@/types/database';
import crypto from 'crypto';

/**
 * MFA启用请求接口
 */
export interface EnableMfaRequest {
  userId: string;
  method: 'totp' | 'email' | 'sms';
  name: string;
  phoneNumber?: string;
  emailAddress?: string;
}

/**
 * MFA验证请求接口
 */
export interface VerifyMfaRequest {
  userId: string;
  deviceId: string;
  code: string;
  sessionId?: string;
}

/**
 * MFA验证结果接口
 */
export interface MfaVerificationResult {
  success: boolean;
  deviceId: string;
  method: string;
  remainingAttempts?: number;
}

/**
 * 多因素认证服务类
 */
export class MfaService {
  private readonly TOTP_ISSUER = 'IdP';
  private readonly TOTP_DIGITS = 6;
  private readonly TOTP_PERIOD = 30;
  private readonly BACKUP_CODES_COUNT = 10;
  private readonly EMAIL_CODE_EXPIRY = 5 * 60 * 1000; // 5分钟
  private readonly SMS_CODE_EXPIRY = 5 * 60 * 1000; // 5分钟

  /**
   * 启用TOTP多因素认证
   * @param request MFA启用请求
   * @returns TOTP设置信息
   */
  async enableTotp(request: EnableMfaRequest): Promise<TOTPSetup> {
    const { userId, name } = request;

    try {
      // 检查用户是否存在
      const user = await prisma.user.findUnique({
        where: { id: userId, isActive: true }
      });

      if (!user) {
        throw new Error('用户不存在');
      }

      // 生成TOTP密钥
      const secret = speakeasy.generateSecret({
        name: `${this.TOTP_ISSUER}:${user.email}`,
        issuer: this.TOTP_ISSUER,
        length: 32
      });

      // 生成备用恢复码
      const backupCodes = this.generateBackupCodes();

      // 加密存储密钥和备用码
      const encryptedSecret = this.encryptData(secret.base32);
      const encryptedBackupCodes = backupCodes.map(code => this.encryptData(code));

      // 创建MFA设备记录（未验证状态）
      const mfaDevice = await prisma.mFADevice.create({
        data: {
          userId,
          type: 'totp',
          name,
          secret: encryptedSecret,
          backupCodes: encryptedBackupCodes,
          isActive: true,
          isVerified: false
        }
      });

      // 生成QR码URI
      const qrCodeUri = await QRCode.toDataURL(secret.otpauth_url!);

      // 记录审计日志
      logAuditEvent('mfa_totp_enable_start', 'mfa_device', userId, {
        deviceId: mfaDevice.id,
        name
      });

      logger.info('TOTP MFA启用开始', { userId, deviceId: mfaDevice.id });

      return {
        secret: secret.base32,
        qrCodeUri,
        backupCodes
      };

    } catch (error) {
      logger.error('TOTP MFA启用失败', { error, userId });
      throw error;
    }
  }

  /**
   * 启用邮件多因素认证
   * @param request MFA启用请求
   * @returns MFA设备信息
   */
  async enableEmail(request: EnableMfaRequest): Promise<MFADeviceSecure> {
    const { userId, name, emailAddress } = request;

    try {
      if (!emailAddress) {
        throw new Error('邮箱地址是必需的');
      }

      // 检查用户是否存在
      const user = await prisma.user.findUnique({
        where: { id: userId, isActive: true }
      });

      if (!user) {
        throw new Error('用户不存在');
      }

      // 创建MFA设备记录
      const mfaDevice = await prisma.mFADevice.create({
        data: {
          userId,
          type: 'email',
          name,
          emailAddress,
          isActive: true,
          isVerified: false
        }
      });

      // 记录审计日志
      logAuditEvent('mfa_email_enable', 'mfa_device', userId, {
        deviceId: mfaDevice.id,
        name,
        emailAddress
      });

      logger.info('邮件MFA启用成功', { userId, deviceId: mfaDevice.id });

      return this.sanitizeMfaDevice(mfaDevice);

    } catch (error) {
      logger.error('邮件MFA启用失败', { error, userId });
      throw error;
    }
  }

  /**
   * 启用短信多因素认证
   * @param request MFA启用请求
   * @returns MFA设备信息
   */
  async enableSms(request: EnableMfaRequest): Promise<MFADeviceSecure> {
    const { userId, name, phoneNumber } = request;

    try {
      if (!phoneNumber) {
        throw new Error('手机号是必需的');
      }

      // 检查用户是否存在
      const user = await prisma.user.findUnique({
        where: { id: userId, isActive: true }
      });

      if (!user) {
        throw new Error('用户不存在');
      }

      // 创建MFA设备记录
      const mfaDevice = await prisma.mFADevice.create({
        data: {
          userId,
          type: 'sms',
          name,
          phoneNumber,
          isActive: true,
          isVerified: false
        }
      });

      // 记录审计日志
      logAuditEvent('mfa_sms_enable', 'mfa_device', userId, {
        deviceId: mfaDevice.id,
        name,
        phoneNumber
      });

      logger.info('短信MFA启用成功', { userId, deviceId: mfaDevice.id });

      return this.sanitizeMfaDevice(mfaDevice);

    } catch (error) {
      logger.error('短信MFA启用失败', { error, userId });
      throw error;
    }
  }

  /**
   * 验证MFA代码
   * @param request MFA验证请求
   * @returns 验证结果
   */
  async verifyMfa(request: VerifyMfaRequest): Promise<MfaVerificationResult> {
    const { userId, deviceId, code, sessionId } = request;

    try {
      // 获取MFA设备
      const mfaDevice = await prisma.mFADevice.findFirst({
        where: {
          id: deviceId,
          userId,
          isActive: true
        }
      });

      if (!mfaDevice) {
        logSecurityEvent('mfa_verification_device_not_found', { deviceId }, userId);
        throw new Error('MFA设备不存在');
      }

      let verificationResult = false;

      switch (mfaDevice.type) {
        case 'totp':
          verificationResult = await this.verifyTotpCode(mfaDevice, code);
          break;
        case 'email':
          verificationResult = await this.verifyEmailCode(mfaDevice, code);
          break;
        case 'sms':
          verificationResult = await this.verifySmsCode(mfaDevice, code);
          break;
        default:
          throw new Error('不支持的MFA类型');
      }

      if (verificationResult) {
        // 验证成功，标记设备为已验证
        await prisma.mFADevice.update({
          where: { id: deviceId },
          data: {
            isVerified: true,
            lastUsedAt: new Date()
          }
        });

        // 如果有会话ID，更新会话的MFA验证状态
        if (sessionId) {
          await prisma.session.update({
            where: { id: sessionId },
            data: { mfaVerified: true }
          });
        }

        // 记录审计日志
        logAuditEvent('mfa_verification_success', 'mfa_device', userId, {
          deviceId,
          method: mfaDevice.type,
          sessionId
        });

        logger.info('MFA验证成功', { userId, deviceId, method: mfaDevice.type });
      } else {
        // 验证失败
        logSecurityEvent('mfa_verification_failed', {
          deviceId,
          method: mfaDevice.type
        }, userId);

        logger.warn('MFA验证失败', { userId, deviceId, method: mfaDevice.type });
      }

      return {
        success: verificationResult,
        deviceId,
        method: mfaDevice.type
      };

    } catch (error) {
      logger.error('MFA验证失败', { error, userId, deviceId });
      throw error;
    }
  }

  /**
   * 获取用户的MFA设备列表
   * @param userId 用户ID
   * @returns MFA设备列表
   */
  async getUserMfaDevices(userId: string): Promise<MFADeviceSecure[]> {
    try {
      const devices = await prisma.mFADevice.findMany({
        where: {
          userId,
          isActive: true
        },
        orderBy: { createdAt: 'desc' }
      });

      return devices.map(device => this.sanitizeMfaDevice(device));

    } catch (error) {
      logger.error('获取用户MFA设备失败', { error, userId });
      throw error;
    }
  }

  /**
   * 禁用MFA设备
   * @param userId 用户ID
   * @param deviceId 设备ID
   */
  async disableMfaDevice(userId: string, deviceId: string): Promise<void> {
    try {
      const device = await prisma.mFADevice.findFirst({
        where: {
          id: deviceId,
          userId,
          isActive: true
        }
      });

      if (!device) {
        throw new Error('MFA设备不存在');
      }

      await prisma.mFADevice.update({
        where: { id: deviceId },
        data: { isActive: false }
      });

      // 记录审计日志
      logAuditEvent('mfa_device_disable', 'mfa_device', userId, {
        deviceId,
        method: device.type
      });

      logger.info('MFA设备禁用成功', { userId, deviceId });

    } catch (error) {
      logger.error('MFA设备禁用失败', { error, userId, deviceId });
      throw error;
    }
  }

  /**
   * 发送邮件验证码
   * @param userId 用户ID
   * @param deviceId 设备ID
   */
  async sendEmailCode(userId: string, deviceId: string): Promise<void> {
    try {
      const device = await prisma.mFADevice.findFirst({
        where: {
          id: deviceId,
          userId,
          type: 'email',
          isActive: true
        }
      });

      if (!device || !device.emailAddress) {
        throw new Error('邮件MFA设备不存在');
      }

      // 生成验证码
      const code = generateVerificationCode(6);
      const expiresAt = new Date(Date.now() + this.EMAIL_CODE_EXPIRY);

      // 存储验证码（这里简化处理，实际应该有专门的表）
      // TODO: 创建verification_codes表

      // TODO: 发送邮件
      // await this.sendMfaEmail(device.emailAddress, code);

      logger.info('邮件验证码发送成功', { userId, deviceId });

    } catch (error) {
      logger.error('邮件验证码发送失败', { error, userId, deviceId });
      throw error;
    }
  }

  /**
   * 发送短信验证码
   * @param userId 用户ID
   * @param deviceId 设备ID
   */
  async sendSmsCode(userId: string, deviceId: string): Promise<void> {
    try {
      const device = await prisma.mFADevice.findFirst({
        where: {
          id: deviceId,
          userId,
          type: 'sms',
          isActive: true
        }
      });

      if (!device || !device.phoneNumber) {
        throw new Error('短信MFA设备不存在');
      }

      // 生成验证码
      const code = generateVerificationCode(6);
      const expiresAt = new Date(Date.now() + this.SMS_CODE_EXPIRY);

      // 存储验证码（这里简化处理，实际应该有专门的表）
      // TODO: 创建verification_codes表

      // TODO: 发送短信
      // await this.sendMfaSms(device.phoneNumber, code);

      logger.info('短信验证码发送成功', { userId, deviceId });

    } catch (error) {
      logger.error('短信验证码发送失败', { error, userId, deviceId });
      throw error;
    }
  }

  /**
   * 验证TOTP代码
   * @param device MFA设备
   * @param code 验证码
   * @returns 验证结果
   */
  private async verifyTotpCode(device: MFADevice, code: string): Promise<boolean> {
    try {
      if (!device.secret) {
        return false;
      }

      // 解密密钥
      const secret = this.decryptData(device.secret);

      // 验证TOTP代码
      const verified = speakeasy.totp.verify({
        secret,
        encoding: 'base32',
        token: code,
        digits: this.TOTP_DIGITS,
        step: this.TOTP_PERIOD,
        window: 2 // 允许前后2个时间窗口
      });

      return verified;

    } catch (error) {
      logger.error('TOTP验证失败', { error, deviceId: device.id });
      return false;
    }
  }

  /**
   * 验证邮件验证码
   * @param device MFA设备
   * @param code 验证码
   * @returns 验证结果
   */
  private async verifyEmailCode(device: MFADevice, code: string): Promise<boolean> {
    try {
      // TODO: 从verification_codes表中查找并验证代码
      // 这里简化处理，实际需要实现完整的验证码存储和验证逻辑
      return true;

    } catch (error) {
      logger.error('邮件验证码验证失败', { error, deviceId: device.id });
      return false;
    }
  }

  /**
   * 验证短信验证码
   * @param device MFA设备
   * @param code 验证码
   * @returns 验证结果
   */
  private async verifySmsCode(device: MFADevice, code: string): Promise<boolean> {
    try {
      // TODO: 从verification_codes表中查找并验证代码
      // 这里简化处理，实际需要实现完整的验证码存储和验证逻辑
      return true;

    } catch (error) {
      logger.error('短信验证码验证失败', { error, deviceId: device.id });
      return false;
    }
  }

  /**
   * 生成备用恢复码
   * @returns 备用恢复码数组
   */
  private generateBackupCodes(): string[] {
    const codes: string[] = [];

    for (let i = 0; i < this.BACKUP_CODES_COUNT; i++) {
      // 生成8位随机字符串
      const code = crypto.randomBytes(4).toString('hex').toUpperCase();
      codes.push(code);
    }

    return codes;
  }

  /**
   * 加密数据
   * @param data 原始数据
   * @returns 加密后的数据
   */
  private encryptData(data: string): string {
    // TODO: 实现真正的加密逻辑
    // 这里简化处理，实际应该使用AES等加密算法
    return Buffer.from(data).toString('base64');
  }

  /**
   * 解密数据
   * @param encryptedData 加密的数据
   * @returns 解密后的数据
   */
  private decryptData(encryptedData: string): string {
    // TODO: 实现真正的解密逻辑
    // 这里简化处理，实际应该使用AES等解密算法
    return Buffer.from(encryptedData, 'base64').toString();
  }

  /**
   * 清理MFA设备敏感信息
   * @param device MFA设备
   * @returns 清理后的设备信息
   */
  private sanitizeMfaDevice(device: MFADevice): MFADeviceSecure {
    const { secret, backupCodes, ...sanitized } = device;
    return sanitized;
  }
}
