/**
 * 实时监控服务
 * 提供系统实时状态监控和告警功能
 */

import { EventEmitter } from 'events';
import { prisma } from '@/config/database';
import { logger } from '@/config/logger';
import { cacheService } from './cache.service';
import { systemConfigService } from './system-config.service';

/**
 * 监控指标接口
 */
export interface MonitorMetrics {
  timestamp: Date;
  system: {
    uptime: number;
    memory: NodeJS.MemoryUsage;
    cpu: number;
    loadAverage: number[];
  };
  database: {
    connections: number;
    queryTime: number;
    errorRate: number;
  };
  authentication: {
    activeUsers: number;
    loginRate: number;
    failureRate: number;
  };
  permissions: {
    requestsPerMinute: number;
    approvalRate: number;
    pendingRequests: number;
  };
  security: {
    riskEvents: number;
    anomalies: number;
    blockedIPs: number;
  };
}

/**
 * 告警级别枚举
 */
export enum AlertLevel {
  INFO = 'info',
  WARNING = 'warning',
  ERROR = 'error',
  CRITICAL = 'critical'
}

/**
 * 告警接口
 */
export interface Alert {
  id: string;
  level: AlertLevel;
  title: string;
  message: string;
  source: string;
  timestamp: Date;
  acknowledged: boolean;
  metadata?: Record<string, any>;
}

/**
 * 监控阈值配置
 */
interface MonitorThresholds {
  memory: {
    warning: number;
    critical: number;
  };
  cpu: {
    warning: number;
    critical: number;
  };
  database: {
    connectionWarning: number;
    connectionCritical: number;
    queryTimeWarning: number;
    queryTimeCritical: number;
  };
  authentication: {
    failureRateWarning: number;
    failureRateCritical: number;
  };
  security: {
    riskEventsWarning: number;
    riskEventsCritical: number;
  };
}

export class RealTimeMonitorService extends EventEmitter {
  private isRunning = false;
  private monitorInterval: NodeJS.Timeout | null = null;
  private metrics: MonitorMetrics[] = [];
  private alerts: Alert[] = [];
  private thresholds: MonitorThresholds;

  constructor() {
    super();
    
    // 默认阈值配置
    this.thresholds = {
      memory: {
        warning: 80, // 80% 内存使用率
        critical: 95 // 95% 内存使用率
      },
      cpu: {
        warning: 70, // 70% CPU使用率
        critical: 90 // 90% CPU使用率
      },
      database: {
        connectionWarning: 80, // 80% 连接池使用率
        connectionCritical: 95, // 95% 连接池使用率
        queryTimeWarning: 1000, // 1秒查询时间
        queryTimeCritical: 5000 // 5秒查询时间
      },
      authentication: {
        failureRateWarning: 10, // 10% 登录失败率
        failureRateCritical: 25 // 25% 登录失败率
      },
      security: {
        riskEventsWarning: 5, // 每分钟5个风险事件
        riskEventsCritical: 20 // 每分钟20个风险事件
      }
    };
  }

  /**
   * 启动监控
   */
  async start(): Promise<void> {
    if (this.isRunning) {
      return;
    }

    try {
      // 加载配置
      await this.loadConfiguration();

      // 启动监控循环
      this.isRunning = true;
      this.monitorInterval = setInterval(() => {
        this.collectMetrics();
      }, 30000); // 每30秒收集一次指标

      // 立即收集一次指标
      await this.collectMetrics();

      logger.info('实时监控服务已启动');
      this.emit('started');

    } catch (error) {
      logger.error('启动实时监控服务失败', { error });
      throw error;
    }
  }

  /**
   * 停止监控
   */
  stop(): void {
    if (!this.isRunning) {
      return;
    }

    this.isRunning = false;
    
    if (this.monitorInterval) {
      clearInterval(this.monitorInterval);
      this.monitorInterval = null;
    }

    logger.info('实时监控服务已停止');
    this.emit('stopped');
  }

  /**
   * 获取当前指标
   */
  getCurrentMetrics(): MonitorMetrics | null {
    return this.metrics.length > 0 ? this.metrics[this.metrics.length - 1] : null;
  }

  /**
   * 获取历史指标
   */
  getHistoricalMetrics(limit: number = 100): MonitorMetrics[] {
    return this.metrics.slice(-limit);
  }

  /**
   * 获取活跃告警
   */
  getActiveAlerts(): Alert[] {
    return this.alerts.filter(alert => !alert.acknowledged);
  }

  /**
   * 获取所有告警
   */
  getAllAlerts(limit: number = 100): Alert[] {
    return this.alerts.slice(-limit);
  }

  /**
   * 确认告警
   */
  acknowledgeAlert(alertId: string): boolean {
    const alert = this.alerts.find(a => a.id === alertId);
    if (alert) {
      alert.acknowledged = true;
      this.emit('alertAcknowledged', alert);
      return true;
    }
    return false;
  }

  /**
   * 收集系统指标
   */
  private async collectMetrics(): Promise<void> {
    try {
      const metrics: MonitorMetrics = {
        timestamp: new Date(),
        system: await this.collectSystemMetrics(),
        database: await this.collectDatabaseMetrics(),
        authentication: await this.collectAuthenticationMetrics(),
        permissions: await this.collectPermissionMetrics(),
        security: await this.collectSecurityMetrics()
      };

      // 添加到指标历史
      this.metrics.push(metrics);
      
      // 保持最近1000个指标
      if (this.metrics.length > 1000) {
        this.metrics = this.metrics.slice(-1000);
      }

      // 检查告警条件
      await this.checkAlerts(metrics);

      // 发送指标更新事件
      this.emit('metricsUpdated', metrics);

      // 缓存当前指标
      await cacheService.set('monitor:current_metrics', metrics, 60);

    } catch (error) {
      logger.error('收集监控指标失败', { error });
    }
  }

  /**
   * 收集系统指标
   */
  private async collectSystemMetrics(): Promise<MonitorMetrics['system']> {
    const memory = process.memoryUsage();
    
    return {
      uptime: process.uptime(),
      memory,
      cpu: await this.getCpuUsage(),
      loadAverage: require('os').loadavg()
    };
  }

  /**
   * 收集数据库指标
   */
  private async collectDatabaseMetrics(): Promise<MonitorMetrics['database']> {
    try {
      const startTime = Date.now();
      
      // 执行简单查询测试数据库响应时间
      await prisma.$queryRaw`SELECT 1`;
      
      const queryTime = Date.now() - startTime;

      // 获取数据库连接信息（简化实现）
      const connections = 10; // 这里应该从数据库获取实际连接数
      const errorRate = 0; // 这里应该计算实际错误率

      return {
        connections,
        queryTime,
        errorRate
      };

    } catch (error) {
      logger.error('收集数据库指标失败', { error });
      return {
        connections: 0,
        queryTime: 0,
        errorRate: 100
      };
    }
  }

  /**
   * 收集认证指标
   */
  private async collectAuthenticationMetrics(): Promise<MonitorMetrics['authentication']> {
    try {
      const now = new Date();
      const oneMinuteAgo = new Date(now.getTime() - 60000);

      // 获取活跃用户数
      const activeUsers = await prisma.session.count({
        where: {
          expiresAt: { gt: now }
        }
      });

      // 获取最近一分钟的登录尝试
      const loginAttempts = await prisma.auditLog.count({
        where: {
          action: { in: ['login', 'login_failed'] },
          createdAt: { gte: oneMinuteAgo }
        }
      });

      // 获取最近一分钟的登录失败
      const loginFailures = await prisma.auditLog.count({
        where: {
          action: 'login_failed',
          createdAt: { gte: oneMinuteAgo }
        }
      });

      const loginRate = loginAttempts;
      const failureRate = loginAttempts > 0 ? (loginFailures / loginAttempts) * 100 : 0;

      return {
        activeUsers,
        loginRate,
        failureRate
      };

    } catch (error) {
      logger.error('收集认证指标失败', { error });
      return {
        activeUsers: 0,
        loginRate: 0,
        failureRate: 0
      };
    }
  }

  /**
   * 收集权限指标
   */
  private async collectPermissionMetrics(): Promise<MonitorMetrics['permissions']> {
    try {
      const now = new Date();
      const oneMinuteAgo = new Date(now.getTime() - 60000);

      // 获取最近一分钟的权限请求
      const requestsPerMinute = await prisma.permissionRequest.count({
        where: {
          createdAt: { gte: oneMinuteAgo }
        }
      });

      // 获取待处理的权限请求
      const pendingRequests = await prisma.permissionRequest.count({
        where: {
          status: 'pending'
        }
      });

      // 计算批准率（简化实现）
      const approvalRate = 85; // 这里应该计算实际批准率

      return {
        requestsPerMinute,
        approvalRate,
        pendingRequests
      };

    } catch (error) {
      logger.error('收集权限指标失败', { error });
      return {
        requestsPerMinute: 0,
        approvalRate: 0,
        pendingRequests: 0
      };
    }
  }

  /**
   * 收集安全指标
   */
  private async collectSecurityMetrics(): Promise<MonitorMetrics['security']> {
    try {
      const now = new Date();
      const oneMinuteAgo = new Date(now.getTime() - 60000);

      // 获取最近一分钟的风险事件
      const riskEvents = await prisma.auditLog.count({
        where: {
          level: { in: ['warning', 'error', 'critical'] },
          createdAt: { gte: oneMinuteAgo }
        }
      });

      // 获取异常事件（简化实现）
      const anomalies = Math.floor(riskEvents * 0.1); // 假设10%的风险事件是异常

      // 获取被阻止的IP数量（简化实现）
      const blockedIPs = 0; // 这里应该从实际的IP黑名单获取

      return {
        riskEvents,
        anomalies,
        blockedIPs
      };

    } catch (error) {
      logger.error('收集安全指标失败', { error });
      return {
        riskEvents: 0,
        anomalies: 0,
        blockedIPs: 0
      };
    }
  }

  /**
   * 获取CPU使用率
   */
  private async getCpuUsage(): Promise<number> {
    return new Promise((resolve) => {
      const startUsage = process.cpuUsage();
      
      setTimeout(() => {
        const endUsage = process.cpuUsage(startUsage);
        const totalUsage = endUsage.user + endUsage.system;
        const cpuPercent = (totalUsage / 1000000) * 100; // 转换为百分比
        resolve(Math.min(cpuPercent, 100));
      }, 100);
    });
  }

  /**
   * 检查告警条件
   */
  private async checkAlerts(metrics: MonitorMetrics): Promise<void> {
    // 检查内存使用率
    const memoryUsage = (metrics.system.memory.heapUsed / metrics.system.memory.heapTotal) * 100;
    if (memoryUsage > this.thresholds.memory.critical) {
      this.createAlert(AlertLevel.CRITICAL, '内存使用率过高', `内存使用率达到 ${memoryUsage.toFixed(1)}%`, 'system');
    } else if (memoryUsage > this.thresholds.memory.warning) {
      this.createAlert(AlertLevel.WARNING, '内存使用率警告', `内存使用率达到 ${memoryUsage.toFixed(1)}%`, 'system');
    }

    // 检查CPU使用率
    if (metrics.system.cpu > this.thresholds.cpu.critical) {
      this.createAlert(AlertLevel.CRITICAL, 'CPU使用率过高', `CPU使用率达到 ${metrics.system.cpu.toFixed(1)}%`, 'system');
    } else if (metrics.system.cpu > this.thresholds.cpu.warning) {
      this.createAlert(AlertLevel.WARNING, 'CPU使用率警告', `CPU使用率达到 ${metrics.system.cpu.toFixed(1)}%`, 'system');
    }

    // 检查数据库查询时间
    if (metrics.database.queryTime > this.thresholds.database.queryTimeCritical) {
      this.createAlert(AlertLevel.CRITICAL, '数据库响应缓慢', `查询时间达到 ${metrics.database.queryTime}ms`, 'database');
    } else if (metrics.database.queryTime > this.thresholds.database.queryTimeWarning) {
      this.createAlert(AlertLevel.WARNING, '数据库响应警告', `查询时间达到 ${metrics.database.queryTime}ms`, 'database');
    }

    // 检查登录失败率
    if (metrics.authentication.failureRate > this.thresholds.authentication.failureRateCritical) {
      this.createAlert(AlertLevel.CRITICAL, '登录失败率过高', `登录失败率达到 ${metrics.authentication.failureRate.toFixed(1)}%`, 'authentication');
    } else if (metrics.authentication.failureRate > this.thresholds.authentication.failureRateWarning) {
      this.createAlert(AlertLevel.WARNING, '登录失败率警告', `登录失败率达到 ${metrics.authentication.failureRate.toFixed(1)}%`, 'authentication');
    }

    // 检查安全风险事件
    if (metrics.security.riskEvents > this.thresholds.security.riskEventsCritical) {
      this.createAlert(AlertLevel.CRITICAL, '安全风险事件激增', `每分钟风险事件数量达到 ${metrics.security.riskEvents}`, 'security');
    } else if (metrics.security.riskEvents > this.thresholds.security.riskEventsWarning) {
      this.createAlert(AlertLevel.WARNING, '安全风险事件增加', `每分钟风险事件数量达到 ${metrics.security.riskEvents}`, 'security');
    }
  }

  /**
   * 创建告警
   */
  private createAlert(level: AlertLevel, title: string, message: string, source: string): void {
    const alert: Alert = {
      id: `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      level,
      title,
      message,
      source,
      timestamp: new Date(),
      acknowledged: false
    };

    this.alerts.push(alert);
    
    // 保持最近500个告警
    if (this.alerts.length > 500) {
      this.alerts = this.alerts.slice(-500);
    }

    logger.warn('系统告警', { alert });
    this.emit('alertCreated', alert);
  }

  /**
   * 加载配置
   */
  private async loadConfiguration(): Promise<void> {
    try {
      // 从系统配置加载监控阈值
      const configs = await systemConfigService.getConfigsByCategory('monitoring');
      
      // 更新阈值配置
      if (configs['monitoring.memory.warning']) {
        this.thresholds.memory.warning = parseFloat(configs['monitoring.memory.warning']);
      }
      if (configs['monitoring.memory.critical']) {
        this.thresholds.memory.critical = parseFloat(configs['monitoring.memory.critical']);
      }
      // ... 其他配置项

    } catch (error) {
      logger.warn('加载监控配置失败，使用默认配置', { error });
    }
  }
}

// 导出单例实例
export const realTimeMonitorService = new RealTimeMonitorService();

/**
 * WebSocket监控服务
 * 为管理员界面提供实时数据推送
 */
export class WebSocketMonitorService {
  private clients: Set<any> = new Set();

  constructor() {
    // 监听监控服务事件
    realTimeMonitorService.on('metricsUpdated', (metrics) => {
      this.broadcast('metrics', metrics);
    });

    realTimeMonitorService.on('alertCreated', (alert) => {
      this.broadcast('alert', alert);
    });

    realTimeMonitorService.on('alertAcknowledged', (alert) => {
      this.broadcast('alertAcknowledged', alert);
    });
  }

  /**
   * 添加客户端连接
   */
  addClient(ws: any): void {
    this.clients.add(ws);

    // 发送当前状态
    const currentMetrics = realTimeMonitorService.getCurrentMetrics();
    if (currentMetrics) {
      ws.send(JSON.stringify({
        type: 'metrics',
        data: currentMetrics
      }));
    }

    const activeAlerts = realTimeMonitorService.getActiveAlerts();
    if (activeAlerts.length > 0) {
      ws.send(JSON.stringify({
        type: 'alerts',
        data: activeAlerts
      }));
    }

    // 处理连接关闭
    ws.on('close', () => {
      this.clients.delete(ws);
    });

    logger.info('WebSocket客户端已连接', { clientCount: this.clients.size });
  }

  /**
   * 广播消息给所有客户端
   */
  private broadcast(type: string, data: any): void {
    const message = JSON.stringify({ type, data });

    this.clients.forEach(client => {
      try {
        if (client.readyState === 1) { // WebSocket.OPEN
          client.send(message);
        }
      } catch (error) {
        logger.error('发送WebSocket消息失败', { error });
        this.clients.delete(client);
      }
    });
  }

  /**
   * 获取连接的客户端数量
   */
  getClientCount(): number {
    return this.clients.size;
  }
}

// 导出WebSocket监控服务实例
export const webSocketMonitorService = new WebSocketMonitorService();
