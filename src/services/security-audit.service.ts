/**
 * 安全审计服务
 * 提供全面的安全审计、威胁检测、合规性检查和安全事件记录功能
 */

import { Request } from 'express';
import { prisma } from '@/config/database';
import { logger } from '@/config/logger';
import { cacheService } from '@/services/cache.service';
import { metricsCollector } from '@/services/metrics-collector.service';
import crypto from 'crypto';

/**
 * 审计事件类型枚举
 */
export enum AuditEventType {
  // 认证相关
  LOGIN_SUCCESS = 'login_success',
  LOGIN_FAILED = 'login_failed',
  LOGOUT = 'logout',
  PASSWORD_CHANGE = 'password_change',
  PASSWORD_RESET = 'password_reset',
  
  // 授权相关
  PERMISSION_GRANTED = 'permission_granted',
  PERMISSION_DENIED = 'permission_denied',
  ROLE_ASSIGNED = 'role_assigned',
  ROLE_REMOVED = 'role_removed',
  
  // 数据操作
  DATA_ACCESS = 'data_access',
  DATA_MODIFY = 'data_modify',
  DATA_DELETE = 'data_delete',
  DATA_EXPORT = 'data_export',
  
  // 系统操作
  SYSTEM_CONFIG_CHANGE = 'system_config_change',
  USER_CREATE = 'user_create',
  USER_UPDATE = 'user_update',
  USER_DELETE = 'user_delete',
  
  // 安全事件
  SECURITY_VIOLATION = 'security_violation',
  SUSPICIOUS_ACTIVITY = 'suspicious_activity',
  BRUTE_FORCE_ATTEMPT = 'brute_force_attempt',
  RATE_LIMIT_EXCEEDED = 'rate_limit_exceeded',
  
  // OAuth/OIDC
  OAUTH_AUTHORIZE = 'oauth_authorize',
  OAUTH_TOKEN_ISSUED = 'oauth_token_issued',
  OAUTH_TOKEN_REVOKED = 'oauth_token_revoked',
  
  // 管理操作
  ADMIN_ACTION = 'admin_action',
  BULK_OPERATION = 'bulk_operation',
  SYSTEM_MAINTENANCE = 'system_maintenance'
}

/**
 * 审计事件严重级别
 */
export enum AuditSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

/**
 * 审计事件接口
 */
interface AuditEvent {
  id?: string;
  eventType: AuditEventType;
  severity: AuditSeverity;
  userId?: string;
  sessionId?: string;
  ipAddress: string;
  userAgent?: string;
  resource?: string;
  action?: string;
  details: Record<string, any>;
  timestamp: Date;
  success: boolean;
  errorMessage?: string;
  riskScore?: number;
}

/**
 * 安全威胁检测结果
 */
interface ThreatDetectionResult {
  threatLevel: 'none' | 'low' | 'medium' | 'high' | 'critical';
  threats: Array<{
    type: string;
    description: string;
    severity: AuditSeverity;
    evidence: Record<string, any>;
    recommendations: string[];
  }>;
  riskScore: number;
  timestamp: Date;
}

/**
 * 合规性检查结果
 */
interface ComplianceCheckResult {
  standard: string;
  compliant: boolean;
  score: number;
  checks: Array<{
    requirement: string;
    status: 'pass' | 'fail' | 'warning';
    description: string;
    evidence?: Record<string, any>;
  }>;
  recommendations: string[];
  timestamp: Date;
}

/**
 * 安全审计服务
 */
export class SecurityAuditService {
  private suspiciousActivityCache = new Map<string, number>();
  private bruteForceAttempts = new Map<string, Array<{ timestamp: Date; success: boolean }>>();

  /**
   * 记录审计事件
   */
  async logAuditEvent(event: Omit<AuditEvent, 'id' | 'timestamp'>): Promise<void> {
    try {
      const auditEvent: AuditEvent = {
        ...event,
        timestamp: new Date()
      };

      // 计算风险评分
      auditEvent.riskScore = this.calculateRiskScore(auditEvent);

      // 存储到数据库
      await prisma.auditLog.create({
        data: {
          eventType: auditEvent.eventType,
          severity: auditEvent.severity,
          userId: auditEvent.userId,
          sessionId: auditEvent.sessionId,
          ipAddress: auditEvent.ipAddress,
          userAgent: auditEvent.userAgent,
          resource: auditEvent.resource,
          action: auditEvent.action,
          details: JSON.stringify(auditEvent.details),
          success: auditEvent.success,
          errorMessage: auditEvent.errorMessage,
          riskScore: auditEvent.riskScore,
          createdAt: auditEvent.timestamp
        }
      });

      // 记录指标
      metricsCollector.incrementCounter('security_audit_events_total', {
        event_type: auditEvent.eventType,
        severity: auditEvent.severity,
        success: auditEvent.success.toString()
      });

      // 检查是否需要实时告警
      await this.checkForRealTimeAlerts(auditEvent);

      // 更新威胁检测缓存
      await this.updateThreatDetectionCache(auditEvent);

      logger.info('审计事件已记录', {
        eventType: auditEvent.eventType,
        severity: auditEvent.severity,
        userId: auditEvent.userId,
        riskScore: auditEvent.riskScore
      });

    } catch (error) {
      logger.error('记录审计事件失败', {
        error: error instanceof Error ? error.message : String(error),
        event: event.eventType
      });
      throw error;
    }
  }

  /**
   * 记录认证事件
   */
  async logAuthenticationEvent(
    req: Request,
    eventType: AuditEventType,
    success: boolean,
    userId?: string,
    details: Record<string, any> = {}
  ): Promise<void> {
    const severity = this.determineSeverity(eventType, success);
    
    await this.logAuditEvent({
      eventType,
      severity,
      userId,
      sessionId: req.sessionID,
      ipAddress: req.ip || 'unknown',
      userAgent: req.get('User-Agent'),
      resource: 'authentication',
      action: eventType,
      details: {
        ...details,
        endpoint: req.path,
        method: req.method
      },
      success,
      errorMessage: success ? undefined : details.error
    });
  }

  /**
   * 记录数据访问事件
   */
  async logDataAccessEvent(
    req: Request,
    resource: string,
    action: string,
    success: boolean,
    details: Record<string, any> = {}
  ): Promise<void> {
    const userId = req.user?.id;
    const eventType = this.mapActionToEventType(action);
    const severity = this.determineSeverity(eventType, success);

    await this.logAuditEvent({
      eventType,
      severity,
      userId,
      sessionId: req.sessionID,
      ipAddress: req.ip || 'unknown',
      userAgent: req.get('User-Agent'),
      resource,
      action,
      details: {
        ...details,
        endpoint: req.path,
        method: req.method,
        query: req.query,
        params: req.params
      },
      success,
      errorMessage: success ? undefined : details.error
    });
  }

  /**
   * 记录安全违规事件
   */
  async logSecurityViolation(
    req: Request,
    violationType: string,
    description: string,
    details: Record<string, any> = {}
  ): Promise<void> {
    await this.logAuditEvent({
      eventType: AuditEventType.SECURITY_VIOLATION,
      severity: AuditSeverity.HIGH,
      userId: req.user?.id,
      sessionId: req.sessionID,
      ipAddress: req.ip || 'unknown',
      userAgent: req.get('User-Agent'),
      resource: 'security',
      action: violationType,
      details: {
        ...details,
        description,
        endpoint: req.path,
        method: req.method
      },
      success: false,
      errorMessage: description
    });
  }

  /**
   * 检测暴力破解攻击
   */
  async detectBruteForceAttack(ipAddress: string, success: boolean): Promise<boolean> {
    const key = `brute_force:${ipAddress}`;
    const now = new Date();
    const timeWindow = 15 * 60 * 1000; // 15分钟窗口
    const maxAttempts = 10; // 最大尝试次数

    // 获取或初始化尝试记录
    if (!this.bruteForceAttempts.has(key)) {
      this.bruteForceAttempts.set(key, []);
    }

    const attempts = this.bruteForceAttempts.get(key)!;
    
    // 清理过期记录
    const validAttempts = attempts.filter(attempt => 
      now.getTime() - attempt.timestamp.getTime() < timeWindow
    );

    // 添加当前尝试
    validAttempts.push({ timestamp: now, success });
    this.bruteForceAttempts.set(key, validAttempts);

    // 检查是否构成暴力破解
    const failedAttempts = validAttempts.filter(attempt => !attempt.success);
    
    if (failedAttempts.length >= maxAttempts) {
      // 记录暴力破解事件
      await this.logAuditEvent({
        eventType: AuditEventType.BRUTE_FORCE_ATTEMPT,
        severity: AuditSeverity.CRITICAL,
        ipAddress,
        resource: 'authentication',
        action: 'brute_force_detected',
        details: {
          attemptCount: failedAttempts.length,
          timeWindow: timeWindow / 1000,
          attempts: validAttempts
        },
        success: false,
        errorMessage: `检测到来自 ${ipAddress} 的暴力破解攻击`
      });

      // 记录指标
      metricsCollector.incrementCounter('security_brute_force_attacks_total', {
        ip_address: ipAddress
      });

      return true;
    }

    return false;
  }

  /**
   * 检测可疑活动
   */
  async detectSuspiciousActivity(
    userId: string,
    ipAddress: string,
    userAgent: string,
    action: string
  ): Promise<boolean> {
    const suspiciousIndicators = [];

    // 检查IP地址变化
    const lastKnownIP = await this.getLastKnownIP(userId);
    if (lastKnownIP && lastKnownIP !== ipAddress) {
      const ipDistance = await this.calculateIPDistance(lastKnownIP, ipAddress);
      if (ipDistance > 1000) { // 距离超过1000公里
        suspiciousIndicators.push({
          type: 'ip_location_change',
          description: `用户从不同地理位置登录`,
          evidence: { lastIP: lastKnownIP, currentIP: ipAddress, distance: ipDistance }
        });
      }
    }

    // 检查用户代理变化
    const lastKnownUA = await this.getLastKnownUserAgent(userId);
    if (lastKnownUA && this.calculateUASimilarity(lastKnownUA, userAgent) < 0.7) {
      suspiciousIndicators.push({
        type: 'user_agent_change',
        description: '用户代理发生显著变化',
        evidence: { lastUA: lastKnownUA, currentUA: userAgent }
      });
    }

    // 检查异常时间访问
    const hour = new Date().getHours();
    const userTypicalHours = await this.getUserTypicalAccessHours(userId);
    if (userTypicalHours.length > 0 && !userTypicalHours.includes(hour)) {
      suspiciousIndicators.push({
        type: 'unusual_time_access',
        description: '在非典型时间访问',
        evidence: { currentHour: hour, typicalHours: userTypicalHours }
      });
    }

    // 检查高频操作
    const recentActionCount = await this.getRecentActionCount(userId, action, 5 * 60 * 1000); // 5分钟内
    if (recentActionCount > 50) { // 5分钟内超过50次相同操作
      suspiciousIndicators.push({
        type: 'high_frequency_actions',
        description: '高频率执行相同操作',
        evidence: { action, count: recentActionCount, timeWindow: '5分钟' }
      });
    }

    // 如果发现可疑指标，记录事件
    if (suspiciousIndicators.length > 0) {
      await this.logAuditEvent({
        eventType: AuditEventType.SUSPICIOUS_ACTIVITY,
        severity: AuditSeverity.MEDIUM,
        userId,
        ipAddress,
        userAgent,
        resource: 'user_behavior',
        action: 'suspicious_activity_detected',
        details: {
          indicators: suspiciousIndicators,
          indicatorCount: suspiciousIndicators.length
        },
        success: false,
        errorMessage: '检测到可疑用户活动'
      });

      metricsCollector.incrementCounter('security_suspicious_activities_total', {
        user_id: userId,
        indicator_count: suspiciousIndicators.length.toString()
      });

      return true;
    }

    return false;
  }

  /**
   * 执行威胁检测
   */
  async performThreatDetection(timeRange?: { start: Date; end: Date }): Promise<ThreatDetectionResult> {
    const end = timeRange?.end || new Date();
    const start = timeRange?.start || new Date(end.getTime() - 24 * 60 * 60 * 1000); // 默认24小时

    try {
      const threats = [];
      let totalRiskScore = 0;

      // 检测暴力破解攻击
      const bruteForceEvents = await this.getBruteForceEvents(start, end);
      if (bruteForceEvents.length > 0) {
        threats.push({
          type: 'brute_force_attack',
          description: `检测到 ${bruteForceEvents.length} 次暴力破解攻击`,
          severity: AuditSeverity.CRITICAL,
          evidence: { events: bruteForceEvents.length, timeRange: { start, end } },
          recommendations: [
            '启用账户锁定策略',
            '实施IP地址黑名单',
            '加强密码复杂度要求',
            '启用多因素认证'
          ]
        });
        totalRiskScore += bruteForceEvents.length * 10;
      }

      // 检测可疑活动
      const suspiciousEvents = await this.getSuspiciousEvents(start, end);
      if (suspiciousEvents.length > 5) {
        threats.push({
          type: 'suspicious_activity',
          description: `检测到 ${suspiciousEvents.length} 次可疑活动`,
          severity: AuditSeverity.MEDIUM,
          evidence: { events: suspiciousEvents.length, timeRange: { start, end } },
          recommendations: [
            '加强用户行为监控',
            '实施设备指纹识别',
            '启用地理位置验证',
            '增强会话管理'
          ]
        });
        totalRiskScore += suspiciousEvents.length * 2;
      }

      // 检测权限滥用
      const privilegeAbuseEvents = await this.getPrivilegeAbuseEvents(start, end);
      if (privilegeAbuseEvents.length > 0) {
        threats.push({
          type: 'privilege_abuse',
          description: `检测到 ${privilegeAbuseEvents.length} 次权限滥用`,
          severity: AuditSeverity.HIGH,
          evidence: { events: privilegeAbuseEvents.length, timeRange: { start, end } },
          recommendations: [
            '审查用户权限分配',
            '实施最小权限原则',
            '加强权限变更审批',
            '定期权限审计'
          ]
        });
        totalRiskScore += privilegeAbuseEvents.length * 5;
      }

      // 检测数据泄露风险
      const dataExportEvents = await this.getDataExportEvents(start, end);
      if (dataExportEvents.length > 10) {
        threats.push({
          type: 'data_exfiltration_risk',
          description: `检测到 ${dataExportEvents.length} 次大量数据导出`,
          severity: AuditSeverity.HIGH,
          evidence: { events: dataExportEvents.length, timeRange: { start, end } },
          recommendations: [
            '限制数据导出权限',
            '实施数据分类标记',
            '加强数据导出审批',
            '监控大量数据访问'
          ]
        });
        totalRiskScore += dataExportEvents.length * 3;
      }

      // 确定威胁级别
      let threatLevel: 'none' | 'low' | 'medium' | 'high' | 'critical' = 'none';
      if (totalRiskScore >= 100) threatLevel = 'critical';
      else if (totalRiskScore >= 50) threatLevel = 'high';
      else if (totalRiskScore >= 20) threatLevel = 'medium';
      else if (totalRiskScore > 0) threatLevel = 'low';

      return {
        threatLevel,
        threats,
        riskScore: Math.min(totalRiskScore, 100), // 限制在100以内
        timestamp: new Date()
      };

    } catch (error) {
      logger.error('威胁检测失败', {
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * 执行合规性检查
   */
  async performComplianceCheck(standard: string = 'GDPR'): Promise<ComplianceCheckResult> {
    try {
      const checks = [];
      let totalScore = 0;
      let passedChecks = 0;

      switch (standard) {
        case 'GDPR':
          checks.push(...await this.performGDPRChecks());
          break;
        case 'SOX':
          checks.push(...await this.performSOXChecks());
          break;
        case 'HIPAA':
          checks.push(...await this.performHIPAAChecks());
          break;
        case 'PCI_DSS':
          checks.push(...await this.performPCIDSSChecks());
          break;
        default:
          checks.push(...await this.performGeneralSecurityChecks());
      }

      // 计算合规性分数
      checks.forEach(check => {
        if (check.status === 'pass') {
          passedChecks++;
          totalScore += 100;
        } else if (check.status === 'warning') {
          totalScore += 50;
        }
      });

      const score = checks.length > 0 ? totalScore / checks.length : 0;
      const compliant = score >= 80; // 80分以上认为合规

      // 生成建议
      const recommendations = this.generateComplianceRecommendations(checks, standard);

      return {
        standard,
        compliant,
        score,
        checks,
        recommendations,
        timestamp: new Date()
      };

    } catch (error) {
      logger.error('合规性检查失败', {
        error: error instanceof Error ? error.message : String(error),
        standard
      });
      throw error;
    }
  }

  /**
   * 计算风险评分
   */
  private calculateRiskScore(event: AuditEvent): number {
    let score = 0;

    // 基于事件类型的基础分数
    const eventTypeScores = {
      [AuditEventType.LOGIN_FAILED]: 2,
      [AuditEventType.SECURITY_VIOLATION]: 8,
      [AuditEventType.SUSPICIOUS_ACTIVITY]: 5,
      [AuditEventType.BRUTE_FORCE_ATTEMPT]: 10,
      [AuditEventType.PERMISSION_DENIED]: 3,
      [AuditEventType.DATA_DELETE]: 4,
      [AuditEventType.ADMIN_ACTION]: 3
    };

    score += eventTypeScores[event.eventType] || 1;

    // 基于严重级别的乘数
    const severityMultipliers = {
      [AuditSeverity.LOW]: 1,
      [AuditSeverity.MEDIUM]: 2,
      [AuditSeverity.HIGH]: 3,
      [AuditSeverity.CRITICAL]: 5
    };

    score *= severityMultipliers[event.severity];

    // 失败事件增加分数
    if (!event.success) {
      score *= 1.5;
    }

    return Math.min(score, 10); // 限制在10分以内
  }

  /**
   * 确定事件严重级别
   */
  private determineSeverity(eventType: AuditEventType, success: boolean): AuditSeverity {
    if (!success) {
      switch (eventType) {
        case AuditEventType.LOGIN_FAILED:
          return AuditSeverity.MEDIUM;
        case AuditEventType.SECURITY_VIOLATION:
        case AuditEventType.BRUTE_FORCE_ATTEMPT:
          return AuditSeverity.CRITICAL;
        case AuditEventType.PERMISSION_DENIED:
          return AuditSeverity.MEDIUM;
        default:
          return AuditSeverity.LOW;
      }
    }

    switch (eventType) {
      case AuditEventType.LOGIN_SUCCESS:
      case AuditEventType.DATA_ACCESS:
        return AuditSeverity.LOW;
      case AuditEventType.DATA_MODIFY:
      case AuditEventType.PASSWORD_CHANGE:
        return AuditSeverity.MEDIUM;
      case AuditEventType.DATA_DELETE:
      case AuditEventType.ADMIN_ACTION:
      case AuditEventType.SYSTEM_CONFIG_CHANGE:
        return AuditSeverity.HIGH;
      default:
        return AuditSeverity.LOW;
    }
  }

  /**
   * 映射操作到事件类型
   */
  private mapActionToEventType(action: string): AuditEventType {
    const actionMap = {
      'read': AuditEventType.DATA_ACCESS,
      'create': AuditEventType.DATA_MODIFY,
      'update': AuditEventType.DATA_MODIFY,
      'delete': AuditEventType.DATA_DELETE,
      'export': AuditEventType.DATA_EXPORT
    };

    return actionMap[action.toLowerCase()] || AuditEventType.DATA_ACCESS;
  }

  /**
   * 检查实时告警
   */
  private async checkForRealTimeAlerts(event: AuditEvent): Promise<void> {
    // 高风险事件立即告警
    if (event.riskScore >= 8 || event.severity === AuditSeverity.CRITICAL) {
      logger.warn('高风险安全事件检测', {
        eventType: event.eventType,
        severity: event.severity,
        riskScore: event.riskScore,
        userId: event.userId,
        ipAddress: event.ipAddress
      });

      // 这里可以集成告警系统，如发送邮件、Slack通知等
    }
  }

  /**
   * 更新威胁检测缓存
   */
  private async updateThreatDetectionCache(event: AuditEvent): Promise<void> {
    // 更新IP地址相关缓存
    if (event.ipAddress && event.userId) {
      await cacheService.set(
        `last_ip:${event.userId}`,
        event.ipAddress,
        24 * 60 * 60 // 24小时
      );
    }

    // 更新用户代理缓存
    if (event.userAgent && event.userId) {
      await cacheService.set(
        `last_ua:${event.userId}`,
        event.userAgent,
        24 * 60 * 60 // 24小时
      );
    }
  }

  // 辅助方法（简化实现）
  private async getLastKnownIP(userId: string): Promise<string | null> {
    return await cacheService.get(`last_ip:${userId}`);
  }

  private async getLastKnownUserAgent(userId: string): Promise<string | null> {
    return await cacheService.get(`last_ua:${userId}`);
  }

  private async calculateIPDistance(ip1: string, ip2: string): Promise<number> {
    // 简化实现，实际应该使用IP地理位置服务
    return Math.random() * 2000; // 模拟距离
  }

  private calculateUASimilarity(ua1: string, ua2: string): number {
    // 简化的相似度计算
    const words1 = ua1.toLowerCase().split(/\s+/);
    const words2 = ua2.toLowerCase().split(/\s+/);
    const intersection = words1.filter(word => words2.includes(word));
    return intersection.length / Math.max(words1.length, words2.length);
  }

  private async getUserTypicalAccessHours(userId: string): Promise<number[]> {
    // 简化实现，实际应该分析用户历史访问模式
    return [9, 10, 11, 14, 15, 16, 17]; // 模拟典型工作时间
  }

  private async getRecentActionCount(userId: string, action: string, timeWindow: number): Promise<number> {
    const since = new Date(Date.now() - timeWindow);
    const count = await prisma.auditLog.count({
      where: {
        userId,
        action,
        createdAt: { gte: since }
      }
    });
    return count;
  }

  // 威胁检测查询方法
  private async getBruteForceEvents(start: Date, end: Date) {
    return await prisma.auditLog.findMany({
      where: {
        eventType: AuditEventType.BRUTE_FORCE_ATTEMPT,
        createdAt: { gte: start, lte: end }
      }
    });
  }

  private async getSuspiciousEvents(start: Date, end: Date) {
    return await prisma.auditLog.findMany({
      where: {
        eventType: AuditEventType.SUSPICIOUS_ACTIVITY,
        createdAt: { gte: start, lte: end }
      }
    });
  }

  private async getPrivilegeAbuseEvents(start: Date, end: Date) {
    return await prisma.auditLog.findMany({
      where: {
        eventType: AuditEventType.PERMISSION_DENIED,
        createdAt: { gte: start, lte: end }
      }
    });
  }

  private async getDataExportEvents(start: Date, end: Date) {
    return await prisma.auditLog.findMany({
      where: {
        eventType: AuditEventType.DATA_EXPORT,
        createdAt: { gte: start, lte: end }
      }
    });
  }

  // 合规性检查方法（简化实现）
  private async performGDPRChecks() {
    return [
      {
        requirement: '数据处理合法性',
        status: 'pass' as const,
        description: '所有数据处理都有合法依据'
      },
      {
        requirement: '数据主体权利',
        status: 'pass' as const,
        description: '支持数据主体访问、更正、删除权利'
      },
      {
        requirement: '数据保护影响评估',
        status: 'warning' as const,
        description: '需要定期进行DPIA评估'
      }
    ];
  }

  private async performSOXChecks() {
    return [
      {
        requirement: '访问控制',
        status: 'pass' as const,
        description: '实施了适当的访问控制措施'
      },
      {
        requirement: '审计跟踪',
        status: 'pass' as const,
        description: '完整的审计日志记录'
      }
    ];
  }

  private async performHIPAAChecks() {
    return [
      {
        requirement: '访问控制',
        status: 'pass' as const,
        description: '实施了HIPAA要求的访问控制'
      }
    ];
  }

  private async performPCIDSSChecks() {
    return [
      {
        requirement: '网络安全',
        status: 'pass' as const,
        description: '网络安全措施符合PCI DSS要求'
      }
    ];
  }

  private async performGeneralSecurityChecks() {
    return [
      {
        requirement: '密码策略',
        status: 'pass' as const,
        description: '实施了强密码策略'
      },
      {
        requirement: '会话管理',
        status: 'pass' as const,
        description: '安全的会话管理机制'
      }
    ];
  }

  private generateComplianceRecommendations(checks: any[], standard: string): string[] {
    const recommendations = [];
    const failedChecks = checks.filter(check => check.status === 'fail');
    const warningChecks = checks.filter(check => check.status === 'warning');

    if (failedChecks.length > 0) {
      recommendations.push(`修复 ${failedChecks.length} 个不合规项目`);
    }

    if (warningChecks.length > 0) {
      recommendations.push(`关注 ${warningChecks.length} 个警告项目`);
    }

    recommendations.push(`定期进行 ${standard} 合规性审计`);
    recommendations.push('建立合规性监控机制');
    recommendations.push('培训员工合规性要求');

    return recommendations;
  }
}

// 创建单例实例
export const securityAuditService = new SecurityAuditService();
