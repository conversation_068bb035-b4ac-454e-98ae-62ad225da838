/**
 * 设备指纹识别服务
 * 提供设备指纹生成、识别、管理和信任评分功能，支持零信任架构的设备验证
 */

import { Request } from 'express';
import { logger } from '@/config/logger';
import { prisma } from '@/config/database';
import { cacheService } from '@/services/cache.service';
import { metricsCollector } from '@/services/metrics-collector.service';
import crypto from 'crypto';

/**
 * 设备指纹组件接口
 */
interface FingerprintComponents {
  // 基础信息
  userAgent: string;
  acceptLanguage: string;
  acceptEncoding: string;
  acceptCharset: string;
  
  // 屏幕信息
  screenResolution: string;
  screenColorDepth: string;
  screenPixelRatio: string;
  
  // 时区和语言
  timezone: string;
  timezoneOffset: number;
  language: string;
  languages: string[];
  
  // 平台信息
  platform: string;
  cookieEnabled: boolean;
  doNotTrack: string;
  
  // 浏览器特性
  plugins: PluginInfo[];
  fonts: string[];
  
  // Canvas指纹
  canvasFingerprint?: string;
  
  // WebGL指纹
  webglVendor?: string;
  webglRenderer?: string;
  webglFingerprint?: string;
  
  // 音频指纹
  audioFingerprint?: string;
  
  // 网络信息
  connectionType?: string;
  connectionSpeed?: string;
  
  // 硬件信息
  hardwareConcurrency?: number;
  deviceMemory?: number;
  
  // 其他特征
  touchSupport: boolean;
  maxTouchPoints: number;
}

/**
 * 插件信息接口
 */
interface PluginInfo {
  name: string;
  version: string;
  description: string;
  filename: string;
}

/**
 * 设备指纹接口
 */
interface DeviceFingerprint {
  id: string;
  userId?: string;
  fingerprint: string;
  components: FingerprintComponents;
  trustScore: number; // 0-100
  riskScore: number; // 0-100
  confidence: number; // 0-1
  firstSeen: Date;
  lastSeen: Date;
  seenCount: number;
  isVerified: boolean;
  isBlacklisted: boolean;
  tags: string[];
  metadata: Record<string, any>;
}

/**
 * 设备信任评估结果
 */
interface DeviceTrustAssessment {
  deviceId: string;
  trustScore: number;
  riskScore: number;
  confidence: number;
  factors: TrustFactor[];
  recommendations: string[];
  requiresVerification: boolean;
  timestamp: Date;
}

/**
 * 信任因子
 */
interface TrustFactor {
  name: string;
  score: number;
  weight: number;
  description: string;
  evidence: Record<string, any>;
}

/**
 * 设备指纹识别服务
 */
export class DeviceFingerprintService {
  private fingerprintCache = new Map<string, DeviceFingerprint>();
  private trustCache = new Map<string, DeviceTrustAssessment>();

  /**
   * 生成设备指纹
   */
  async generateFingerprint(req: Request, clientData?: Partial<FingerprintComponents>): Promise<DeviceFingerprint> {
    try {
      // 从请求头提取基础信息
      const baseComponents = this.extractBaseComponents(req);
      
      // 合并客户端提供的数据
      const components: FingerprintComponents = {
        ...baseComponents,
        ...clientData
      };

      // 生成指纹哈希
      const fingerprintData = this.normalizeComponents(components);
      const fingerprint = this.generateFingerprintHash(fingerprintData);

      // 检查是否已存在
      const existingFingerprint = await this.getExistingFingerprint(fingerprint);
      if (existingFingerprint) {
        // 更新最后见到时间和计数
        existingFingerprint.lastSeen = new Date();
        existingFingerprint.seenCount++;
        await this.updateFingerprint(existingFingerprint);
        return existingFingerprint;
      }

      // 创建新的设备指纹
      const deviceFingerprint: DeviceFingerprint = {
        id: this.generateDeviceId(),
        fingerprint,
        components,
        trustScore: 50, // 新设备默认中等信任
        riskScore: 30,  // 新设备默认低风险
        confidence: this.calculateInitialConfidence(components),
        firstSeen: new Date(),
        lastSeen: new Date(),
        seenCount: 1,
        isVerified: false,
        isBlacklisted: false,
        tags: this.generateInitialTags(components),
        metadata: {}
      };

      // 保存到数据库
      await this.saveFingerprint(deviceFingerprint);

      // 缓存结果
      this.fingerprintCache.set(fingerprint, deviceFingerprint);

      // 记录指标
      metricsCollector.incrementCounter('device_fingerprints_generated_total', {
        platform: components.platform,
        new_device: 'true'
      });

      logger.info('设备指纹生成完成', {
        deviceId: deviceFingerprint.id,
        fingerprint: fingerprint.substring(0, 8),
        platform: components.platform,
        confidence: deviceFingerprint.confidence
      });

      return deviceFingerprint;

    } catch (error) {
      logger.error('设备指纹生成失败', {
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * 识别设备
   */
  async identifyDevice(req: Request, clientData?: Partial<FingerprintComponents>): Promise<DeviceFingerprint | null> {
    try {
      const components = {
        ...this.extractBaseComponents(req),
        ...clientData
      };

      const fingerprintData = this.normalizeComponents(components);
      const fingerprint = this.generateFingerprintHash(fingerprintData);

      // 尝试精确匹配
      let device = await this.getExistingFingerprint(fingerprint);
      if (device) {
        return device;
      }

      // 尝试模糊匹配
      device = await this.fuzzyMatchDevice(components);
      if (device) {
        logger.info('设备模糊匹配成功', {
          deviceId: device.id,
          similarity: 'high'
        });
        return device;
      }

      return null;

    } catch (error) {
      logger.error('设备识别失败', {
        error: error instanceof Error ? error.message : String(error)
      });
      return null;
    }
  }

  /**
   * 评估设备信任度
   */
  async assessDeviceTrust(deviceFingerprint: DeviceFingerprint, userId?: string): Promise<DeviceTrustAssessment> {
    try {
      const cacheKey = `trust_${deviceFingerprint.id}_${userId || 'anon'}`;
      const cached = this.trustCache.get(cacheKey);
      if (cached && this.isTrustAssessmentValid(cached)) {
        return cached;
      }

      const factors: TrustFactor[] = [];

      // 设备历史因子
      const historyFactor = this.assessDeviceHistory(deviceFingerprint);
      factors.push(historyFactor);

      // 设备一致性因子
      const consistencyFactor = this.assessDeviceConsistency(deviceFingerprint);
      factors.push(consistencyFactor);

      // 用户关联因子
      if (userId) {
        const userAssociationFactor = await this.assessUserAssociation(deviceFingerprint, userId);
        factors.push(userAssociationFactor);
      }

      // 安全特征因子
      const securityFactor = this.assessSecurityFeatures(deviceFingerprint);
      factors.push(securityFactor);

      // 异常检测因子
      const anomalyFactor = this.assessAnomalies(deviceFingerprint);
      factors.push(anomalyFactor);

      // 计算综合信任评分
      const trustScore = this.calculateTrustScore(factors);
      const riskScore = 100 - trustScore;
      const confidence = this.calculateTrustConfidence(factors);

      // 生成建议
      const recommendations = this.generateTrustRecommendations(factors, trustScore);
      const requiresVerification = trustScore < 60 || riskScore > 40;

      const assessment: DeviceTrustAssessment = {
        deviceId: deviceFingerprint.id,
        trustScore,
        riskScore,
        confidence,
        factors,
        recommendations,
        requiresVerification,
        timestamp: new Date()
      };

      // 缓存评估结果
      this.trustCache.set(cacheKey, assessment);
      setTimeout(() => this.trustCache.delete(cacheKey), 10 * 60 * 1000); // 10分钟过期

      // 更新设备信任评分
      deviceFingerprint.trustScore = trustScore;
      deviceFingerprint.riskScore = riskScore;
      await this.updateFingerprint(deviceFingerprint);

      // 记录指标
      metricsCollector.setGauge('device_trust_score', trustScore, {
        device_id: deviceFingerprint.id,
        platform: deviceFingerprint.components.platform
      });

      return assessment;

    } catch (error) {
      logger.error('设备信任评估失败', {
        error: error instanceof Error ? error.message : String(error),
        deviceId: deviceFingerprint.id
      });
      throw error;
    }
  }

  /**
   * 验证设备
   */
  async verifyDevice(deviceId: string, userId: string, verificationMethod: string): Promise<boolean> {
    try {
      const device = await this.getDeviceById(deviceId);
      if (!device) {
        throw new Error('设备不存在');
      }

      // 执行验证逻辑
      const verificationResult = await this.performDeviceVerification(device, userId, verificationMethod);
      
      if (verificationResult.success) {
        // 更新设备状态
        device.isVerified = true;
        device.trustScore = Math.min(device.trustScore + 20, 100);
        device.userId = userId;
        device.tags.push('verified');
        device.metadata.verificationMethod = verificationMethod;
        device.metadata.verifiedAt = new Date();
        device.metadata.verifiedBy = userId;

        await this.updateFingerprint(device);

        logger.info('设备验证成功', {
          deviceId,
          userId,
          method: verificationMethod,
          newTrustScore: device.trustScore
        });

        return true;
      }

      return false;

    } catch (error) {
      logger.error('设备验证失败', {
        error: error instanceof Error ? error.message : String(error),
        deviceId,
        userId
      });
      return false;
    }
  }

  /**
   * 将设备加入黑名单
   */
  async blacklistDevice(deviceId: string, reason: string, operatorId: string): Promise<void> {
    try {
      const device = await this.getDeviceById(deviceId);
      if (!device) {
        throw new Error('设备不存在');
      }

      device.isBlacklisted = true;
      device.trustScore = 0;
      device.riskScore = 100;
      device.tags.push('blacklisted');
      device.metadata.blacklistReason = reason;
      device.metadata.blacklistedAt = new Date();
      device.metadata.blacklistedBy = operatorId;

      await this.updateFingerprint(device);

      logger.warn('设备已加入黑名单', {
        deviceId,
        reason,
        operatorId
      });

      metricsCollector.incrementCounter('devices_blacklisted_total', {
        reason,
        platform: device.components.platform
      });

    } catch (error) {
      logger.error('设备黑名单操作失败', {
        error: error instanceof Error ? error.message : String(error),
        deviceId
      });
      throw error;
    }
  }

  /**
   * 获取用户的设备列表
   */
  async getUserDevices(userId: string): Promise<DeviceFingerprint[]> {
    try {
      const devices = await prisma.deviceFingerprint.findMany({
        where: { userId },
        orderBy: { lastSeen: 'desc' }
      });

      return devices.map(device => this.mapPrismaToFingerprint(device));

    } catch (error) {
      logger.error('获取用户设备列表失败', {
        error: error instanceof Error ? error.message : String(error),
        userId
      });
      return [];
    }
  }

  /**
   * 从请求头提取基础组件
   */
  private extractBaseComponents(req: Request): Partial<FingerprintComponents> {
    return {
      userAgent: req.get('User-Agent') || '',
      acceptLanguage: req.get('Accept-Language') || '',
      acceptEncoding: req.get('Accept-Encoding') || '',
      acceptCharset: req.get('Accept-Charset') || '',
      doNotTrack: req.get('DNT') || '',
      
      // 从自定义头部获取客户端信息
      screenResolution: req.get('X-Screen-Resolution') || '',
      timezone: req.get('X-Timezone') || '',
      platform: this.extractPlatform(req.get('User-Agent') || ''),
      
      // 默认值
      plugins: [],
      fonts: [],
      languages: [],
      touchSupport: false,
      maxTouchPoints: 0,
      cookieEnabled: true,
      timezoneOffset: 0
    };
  }

  /**
   * 标准化组件数据
   */
  private normalizeComponents(components: FingerprintComponents): string {
    // 创建标准化的指纹数据
    const normalized = {
      ua: components.userAgent.toLowerCase(),
      lang: components.acceptLanguage.toLowerCase(),
      screen: components.screenResolution,
      tz: components.timezone,
      platform: components.platform.toLowerCase(),
      plugins: components.plugins.map(p => p.name).sort().join(','),
      fonts: components.fonts.sort().join(','),
      canvas: components.canvasFingerprint || '',
      webgl: components.webglFingerprint || '',
      audio: components.audioFingerprint || ''
    };

    return JSON.stringify(normalized);
  }

  /**
   * 生成指纹哈希
   */
  private generateFingerprintHash(data: string): string {
    return crypto.createHash('sha256').update(data).digest('hex');
  }

  /**
   * 生成设备ID
   */
  private generateDeviceId(): string {
    return `dev_${Date.now()}_${crypto.randomBytes(8).toString('hex')}`;
  }

  /**
   * 计算初始置信度
   */
  private calculateInitialConfidence(components: FingerprintComponents): number {
    let confidence = 0.5; // 基础置信度

    // 基于可用组件数量
    const availableComponents = Object.values(components).filter(v => v && v !== '').length;
    confidence += Math.min(availableComponents * 0.02, 0.3);

    // 特殊组件加分
    if (components.canvasFingerprint) confidence += 0.1;
    if (components.webglFingerprint) confidence += 0.1;
    if (components.audioFingerprint) confidence += 0.05;
    if (components.fonts.length > 10) confidence += 0.05;

    return Math.min(confidence, 1.0);
  }

  /**
   * 生成初始标签
   */
  private generateInitialTags(components: FingerprintComponents): string[] {
    const tags: string[] = ['new'];

    // 平台标签
    tags.push(components.platform.toLowerCase());

    // 浏览器标签
    const ua = components.userAgent.toLowerCase();
    if (ua.includes('chrome')) tags.push('chrome');
    else if (ua.includes('firefox')) tags.push('firefox');
    else if (ua.includes('safari')) tags.push('safari');
    else if (ua.includes('edge')) tags.push('edge');

    // 移动设备标签
    if (ua.includes('mobile') || components.touchSupport) {
      tags.push('mobile');
    } else {
      tags.push('desktop');
    }

    return tags;
  }

  /**
   * 提取平台信息
   */
  private extractPlatform(userAgent: string): string {
    const ua = userAgent.toLowerCase();
    if (ua.includes('windows')) return 'Windows';
    if (ua.includes('mac')) return 'macOS';
    if (ua.includes('linux')) return 'Linux';
    if (ua.includes('android')) return 'Android';
    if (ua.includes('ios') || ua.includes('iphone') || ua.includes('ipad')) return 'iOS';
    return 'Unknown';
  }

  /**
   * 模糊匹配设备
   */
  private async fuzzyMatchDevice(components: FingerprintComponents): Promise<DeviceFingerprint | null> {
    // 简化实现：基于用户代理和屏幕分辨率的模糊匹配
    try {
      const devices = await prisma.deviceFingerprint.findMany({
        where: {
          components: {
            path: ['userAgent'],
            equals: components.userAgent
          }
        },
        take: 10
      });

      for (const device of devices) {
        const deviceComponents = device.components as any;
        const similarity = this.calculateSimilarity(components, deviceComponents);
        if (similarity > 0.8) {
          return this.mapPrismaToFingerprint(device);
        }
      }

      return null;

    } catch (error) {
      logger.warn('模糊匹配失败', { error: error.message });
      return null;
    }
  }

  /**
   * 计算组件相似度
   */
  private calculateSimilarity(components1: FingerprintComponents, components2: FingerprintComponents): number {
    let matches = 0;
    let total = 0;

    const keys: (keyof FingerprintComponents)[] = [
      'userAgent', 'screenResolution', 'timezone', 'platform', 'language'
    ];

    for (const key of keys) {
      total++;
      if (components1[key] === components2[key]) {
        matches++;
      }
    }

    return total > 0 ? matches / total : 0;
  }

  /**
   * 评估设备历史
   */
  private assessDeviceHistory(device: DeviceFingerprint): TrustFactor {
    const daysSinceFirstSeen = (Date.now() - device.firstSeen.getTime()) / (1000 * 60 * 60 * 24);
    const seenFrequency = device.seenCount / Math.max(daysSinceFirstSeen, 1);

    let score = 50; // 基础分数

    // 设备年龄加分
    if (daysSinceFirstSeen > 30) score += 20;
    else if (daysSinceFirstSeen > 7) score += 10;

    // 使用频率加分
    if (seenFrequency > 1) score += 15;
    else if (seenFrequency > 0.5) score += 10;

    // 验证状态加分
    if (device.isVerified) score += 20;

    return {
      name: '设备历史',
      score: Math.min(score, 100),
      weight: 0.3,
      description: '基于设备使用历史的信任评估',
      evidence: {
        daysSinceFirstSeen,
        seenCount: device.seenCount,
        seenFrequency,
        isVerified: device.isVerified
      }
    };
  }

  /**
   * 评估设备一致性
   */
  private assessDeviceConsistency(device: DeviceFingerprint): TrustFactor {
    let score = 80; // 基础分数

    // 检查指纹组件的一致性
    const components = device.components;
    
    // 用户代理一致性
    if (!components.userAgent || components.userAgent.length < 20) {
      score -= 20;
    }

    // 屏幕信息一致性
    if (!components.screenResolution) {
      score -= 10;
    }

    // 时区一致性
    if (!components.timezone) {
      score -= 5;
    }

    return {
      name: '设备一致性',
      score: Math.max(score, 0),
      weight: 0.2,
      description: '设备指纹组件的一致性评估',
      evidence: {
        hasUserAgent: !!components.userAgent,
        hasScreenInfo: !!components.screenResolution,
        hasTimezone: !!components.timezone
      }
    };
  }

  /**
   * 评估用户关联
   */
  private async assessUserAssociation(device: DeviceFingerprint, userId: string): Promise<TrustFactor> {
    let score = 50;

    // 检查设备是否与用户关联
    if (device.userId === userId) {
      score += 30;
    }

    // 检查用户的其他设备
    const userDevices = await this.getUserDevices(userId);
    const verifiedDevices = userDevices.filter(d => d.isVerified).length;
    
    if (verifiedDevices > 0) {
      score += 10;
    }

    return {
      name: '用户关联',
      score: Math.min(score, 100),
      weight: 0.25,
      description: '设备与用户的关联程度评估',
      evidence: {
        isAssociated: device.userId === userId,
        userVerifiedDevices: verifiedDevices
      }
    };
  }

  /**
   * 评估安全特征
   */
  private assessSecurityFeatures(device: DeviceFingerprint): TrustFactor {
    let score = 60;
    const components = device.components;

    // 检查是否支持现代安全特性
    if (components.canvasFingerprint) score += 10;
    if (components.webglFingerprint) score += 10;
    if (components.audioFingerprint) score += 5;

    // 检查浏览器安全性
    const ua = components.userAgent.toLowerCase();
    if (ua.includes('chrome') && this.extractChromeVersion(ua) >= 90) score += 10;
    if (ua.includes('firefox') && this.extractFirefoxVersion(ua) >= 88) score += 10;

    return {
      name: '安全特征',
      score: Math.min(score, 100),
      weight: 0.15,
      description: '设备安全特征评估',
      evidence: {
        hasCanvas: !!components.canvasFingerprint,
        hasWebGL: !!components.webglFingerprint,
        hasAudio: !!components.audioFingerprint,
        userAgent: components.userAgent
      }
    };
  }

  /**
   * 评估异常情况
   */
  private assessAnomalies(device: DeviceFingerprint): TrustFactor {
    let score = 80;
    const anomalies: string[] = [];

    // 检查黑名单状态
    if (device.isBlacklisted) {
      score = 0;
      anomalies.push('设备在黑名单中');
    }

    // 检查指纹异常
    const components = device.components;
    if (!components.userAgent || components.userAgent.length < 10) {
      score -= 20;
      anomalies.push('异常的用户代理');
    }

    if (components.plugins.length === 0 && !components.userAgent.includes('Mobile')) {
      score -= 10;
      anomalies.push('缺少浏览器插件信息');
    }

    return {
      name: '异常检测',
      score: Math.max(score, 0),
      weight: 0.1,
      description: '设备异常情况检测',
      evidence: {
        isBlacklisted: device.isBlacklisted,
        anomalies
      }
    };
  }

  /**
   * 计算信任评分
   */
  private calculateTrustScore(factors: TrustFactor[]): number {
    let weightedSum = 0;
    let totalWeight = 0;

    for (const factor of factors) {
      weightedSum += factor.score * factor.weight;
      totalWeight += factor.weight;
    }

    return totalWeight > 0 ? Math.round(weightedSum / totalWeight) : 50;
  }

  /**
   * 计算信任置信度
   */
  private calculateTrustConfidence(factors: TrustFactor[]): number {
    const avgScore = factors.reduce((sum, f) => sum + f.score, 0) / factors.length;
    const variance = factors.reduce((sum, f) => sum + Math.pow(f.score - avgScore, 2), 0) / factors.length;
    const consistency = Math.max(0, 1 - variance / 1000); // 标准化方差
    
    return Math.min(0.5 + consistency * 0.5, 1.0);
  }

  /**
   * 生成信任建议
   */
  private generateTrustRecommendations(factors: TrustFactor[], trustScore: number): string[] {
    const recommendations: string[] = [];

    if (trustScore < 30) {
      recommendations.push('阻止设备访问');
      recommendations.push('要求设备验证');
    } else if (trustScore < 60) {
      recommendations.push('要求额外身份验证');
      recommendations.push('限制敏感操作');
    } else if (trustScore < 80) {
      recommendations.push('增强监控');
      recommendations.push('定期重新评估');
    }

    // 基于具体因子的建议
    const lowScoreFactors = factors.filter(f => f.score < 50);
    for (const factor of lowScoreFactors) {
      switch (factor.name) {
        case '设备历史':
          recommendations.push('建立设备使用历史');
          break;
        case '用户关联':
          recommendations.push('验证设备所有权');
          break;
        case '安全特征':
          recommendations.push('更新浏览器版本');
          break;
      }
    }

    return [...new Set(recommendations)]; // 去重
  }

  /**
   * 执行设备验证
   */
  private async performDeviceVerification(
    device: DeviceFingerprint,
    userId: string,
    method: string
  ): Promise<{ success: boolean; details?: any }> {
    // 简化的验证逻辑
    switch (method) {
      case 'email':
        // 发送邮件验证
        return { success: true, details: { method: 'email', sentAt: new Date() } };
      case 'sms':
        // 发送短信验证
        return { success: true, details: { method: 'sms', sentAt: new Date() } };
      case 'push':
        // 推送通知验证
        return { success: true, details: { method: 'push', sentAt: new Date() } };
      default:
        return { success: false, details: { error: 'Unknown verification method' } };
    }
  }

  /**
   * 检查信任评估是否有效
   */
  private isTrustAssessmentValid(assessment: DeviceTrustAssessment): boolean {
    const age = Date.now() - assessment.timestamp.getTime();
    return age < 10 * 60 * 1000; // 10分钟有效期
  }

  /**
   * 提取Chrome版本
   */
  private extractChromeVersion(userAgent: string): number {
    const match = userAgent.match(/chrome\/(\d+)/i);
    return match ? parseInt(match[1]) : 0;
  }

  /**
   * 提取Firefox版本
   */
  private extractFirefoxVersion(userAgent: string): number {
    const match = userAgent.match(/firefox\/(\d+)/i);
    return match ? parseInt(match[1]) : 0;
  }

  // 数据库操作方法（简化实现）
  private async getExistingFingerprint(fingerprint: string): Promise<DeviceFingerprint | null> {
    const cached = this.fingerprintCache.get(fingerprint);
    if (cached) return cached;

    try {
      const device = await prisma.deviceFingerprint.findUnique({
        where: { fingerprint }
      });
      return device ? this.mapPrismaToFingerprint(device) : null;
    } catch (error) {
      return null;
    }
  }

  private async getDeviceById(deviceId: string): Promise<DeviceFingerprint | null> {
    try {
      const device = await prisma.deviceFingerprint.findUnique({
        where: { id: deviceId }
      });
      return device ? this.mapPrismaToFingerprint(device) : null;
    } catch (error) {
      return null;
    }
  }

  private async saveFingerprint(device: DeviceFingerprint): Promise<void> {
    try {
      await prisma.deviceFingerprint.create({
        data: {
          id: device.id,
          userId: device.userId,
          fingerprint: device.fingerprint,
          components: device.components as any,
          trustScore: device.trustScore,
          riskScore: device.riskScore,
          confidence: device.confidence,
          firstSeen: device.firstSeen,
          lastSeen: device.lastSeen,
          seenCount: device.seenCount,
          isVerified: device.isVerified,
          isBlacklisted: device.isBlacklisted,
          tags: device.tags,
          metadata: device.metadata as any
        }
      });
    } catch (error) {
      logger.error('保存设备指纹失败', { error: error.message, deviceId: device.id });
    }
  }

  private async updateFingerprint(device: DeviceFingerprint): Promise<void> {
    try {
      await prisma.deviceFingerprint.update({
        where: { id: device.id },
        data: {
          userId: device.userId,
          trustScore: device.trustScore,
          riskScore: device.riskScore,
          lastSeen: device.lastSeen,
          seenCount: device.seenCount,
          isVerified: device.isVerified,
          isBlacklisted: device.isBlacklisted,
          tags: device.tags,
          metadata: device.metadata as any
        }
      });

      // 更新缓存
      this.fingerprintCache.set(device.fingerprint, device);
    } catch (error) {
      logger.error('更新设备指纹失败', { error: error.message, deviceId: device.id });
    }
  }

  private mapPrismaToFingerprint(prismaDevice: any): DeviceFingerprint {
    return {
      id: prismaDevice.id,
      userId: prismaDevice.userId,
      fingerprint: prismaDevice.fingerprint,
      components: prismaDevice.components,
      trustScore: prismaDevice.trustScore,
      riskScore: prismaDevice.riskScore,
      confidence: prismaDevice.confidence,
      firstSeen: prismaDevice.firstSeen,
      lastSeen: prismaDevice.lastSeen,
      seenCount: prismaDevice.seenCount,
      isVerified: prismaDevice.isVerified,
      isBlacklisted: prismaDevice.isBlacklisted,
      tags: prismaDevice.tags,
      metadata: prismaDevice.metadata
    };
  }
}

// 创建单例实例
export const deviceFingerprintService = new DeviceFingerprintService();
