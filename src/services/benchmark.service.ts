/**
 * 基准测试服务
 * 提供系统各组件的基准测试和性能对比
 */

import { logger } from '@/config/logger';
import { prisma } from '@/config/database';
import { redisService } from '@/services/redis.service';
import { performanceTestService, TestType, TestConfig, TestScenario } from '@/services/performance-test.service';
import { metricsCollector } from '@/services/metrics-collector.service';
import axios from 'axios';

/**
 * 基准测试结果接口
 */
export interface BenchmarkResult {
  name: string;
  category: string;
  timestamp: Date;
  environment: {
    nodeVersion: string;
    platform: string;
    arch: string;
    cpus: number;
    memory: number;
  };
  results: {
    [testName: string]: {
      opsPerSecond: number;
      avgLatency: number;
      minLatency: number;
      maxLatency: number;
      p95Latency: number;
      p99Latency: number;
      errorRate: number;
      throughput: number;
      memoryUsage: number;
      cpuUsage: number;
    };
  };
  comparison?: {
    baseline: string;
    improvements: {
      [testName: string]: {
        opsPerSecondChange: number;
        latencyChange: number;
        throughputChange: number;
      };
    };
  };
}

/**
 * 基准测试服务
 */
export class BenchmarkService {
  private baselineResults = new Map<string, BenchmarkResult>();
  private testResults = new Map<string, BenchmarkResult>();

  constructor() {}

  /**
   * 运行完整基准测试套件
   */
  async runFullBenchmarkSuite(): Promise<BenchmarkResult[]> {
    logger.info('开始运行完整基准测试套件');

    const results: BenchmarkResult[] = [];

    try {
      // 1. 数据库基准测试
      const dbBenchmark = await this.runDatabaseBenchmark();
      results.push(dbBenchmark);

      // 2. Redis基准测试
      const redisBenchmark = await this.runRedisBenchmark();
      results.push(redisBenchmark);

      // 3. HTTP API基准测试
      const apiBenchmark = await this.runAPIBenchmark();
      results.push(apiBenchmark);

      // 4. 认证系统基准测试
      const authBenchmark = await this.runAuthBenchmark();
      results.push(authBenchmark);

      // 5. 缓存系统基准测试
      const cacheBenchmark = await this.runCacheBenchmark();
      results.push(cacheBenchmark);

      logger.info('基准测试套件完成', {
        totalTests: results.length,
        categories: results.map(r => r.category)
      });

      return results;

    } catch (error) {
      logger.error('基准测试套件失败', {
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * 数据库基准测试
   */
  async runDatabaseBenchmark(): Promise<BenchmarkResult> {
    logger.info('开始数据库基准测试');

    const results: any = {};

    // 简单查询测试
    results.simpleQuery = await this.benchmarkFunction(
      'Database Simple Query',
      async () => {
        await prisma.user.findFirst();
        return { success: true, duration: 0 };
      },
      { iterations: 1000, concurrency: 10 }
    );

    // 复杂查询测试
    results.complexQuery = await this.benchmarkFunction(
      'Database Complex Query',
      async () => {
        await prisma.user.findMany({
          include: {
            userRoles: {
              include: {
                role: true
              }
            },
            sessions: {
              where: { isActive: true }
            }
          },
          take: 10
        });
        return { success: true, duration: 0 };
      },
      { iterations: 500, concurrency: 5 }
    );

    // 插入测试
    results.insert = await this.benchmarkFunction(
      'Database Insert',
      async () => {
        const testUser = await prisma.user.create({
          data: {
            email: `benchmark-${Date.now()}-${Math.random()}@test.com`,
            nickname: 'Benchmark User',
            hashedPassword: 'hashed-password',
            isEmailVerified: true
          }
        });
        // 清理测试数据
        await prisma.user.delete({ where: { id: testUser.id } });
        return { success: true, duration: 0 };
      },
      { iterations: 200, concurrency: 5 }
    );

    // 更新测试
    results.update = await this.benchmarkFunction(
      'Database Update',
      async () => {
        const user = await prisma.user.findFirst();
        if (user) {
          await prisma.user.update({
            where: { id: user.id },
            data: { lastLoginAt: new Date() }
          });
        }
        return { success: true, duration: 0 };
      },
      { iterations: 300, concurrency: 5 }
    );

    return {
      name: 'Database Benchmark',
      category: 'database',
      timestamp: new Date(),
      environment: this.getEnvironmentInfo(),
      results
    };
  }

  /**
   * Redis基准测试
   */
  async runRedisBenchmark(): Promise<BenchmarkResult> {
    logger.info('开始Redis基准测试');

    const results: any = {};

    // SET操作测试
    results.set = await this.benchmarkFunction(
      'Redis SET',
      async () => {
        const key = `benchmark:${Date.now()}:${Math.random()}`;
        await redisService.set(key, 'test-value', 60);
        return { success: true, duration: 0 };
      },
      { iterations: 2000, concurrency: 20 }
    );

    // GET操作测试
    results.get = await this.benchmarkFunction(
      'Redis GET',
      async () => {
        await redisService.get('benchmark:test-key');
        return { success: true, duration: 0 };
      },
      { iterations: 3000, concurrency: 30 }
    );

    // 哈希操作测试
    results.hash = await this.benchmarkFunction(
      'Redis HASH',
      async () => {
        const key = `benchmark:hash:${Date.now()}`;
        await redisService.hset(key, 'field1', 'value1');
        await redisService.hget(key, 'field1');
        return { success: true, duration: 0 };
      },
      { iterations: 1500, concurrency: 15 }
    );

    // 列表操作测试
    results.list = await this.benchmarkFunction(
      'Redis LIST',
      async () => {
        const key = `benchmark:list:${Date.now()}`;
        await redisService.lpush(key, 'item1', 'item2', 'item3');
        await redisService.lrange(key, 0, -1);
        return { success: true, duration: 0 };
      },
      { iterations: 1000, concurrency: 10 }
    );

    return {
      name: 'Redis Benchmark',
      category: 'cache',
      timestamp: new Date(),
      environment: this.getEnvironmentInfo(),
      results
    };
  }

  /**
   * HTTP API基准测试
   */
  async runAPIBenchmark(): Promise<BenchmarkResult> {
    logger.info('开始HTTP API基准测试');

    const baseURL = process.env.BASE_URL || 'http://localhost:3000';
    const results: any = {};

    // 健康检查测试
    results.healthCheck = await this.benchmarkFunction(
      'API Health Check',
      async () => {
        const response = await axios.get(`${baseURL}/api/v1/monitoring/health`);
        return { 
          success: response.status === 200, 
          duration: 0 
        };
      },
      { iterations: 1000, concurrency: 20 }
    );

    // 获取用户列表测试（需要认证）
    const authToken = await this.getTestAuthToken();
    if (authToken) {
      results.userList = await this.benchmarkFunction(
        'API User List',
        async () => {
          const response = await axios.get(`${baseURL}/api/v1/users`, {
            headers: { Authorization: `Bearer ${authToken}` }
          });
          return { 
            success: response.status === 200, 
            duration: 0 
          };
        },
        { iterations: 500, concurrency: 10 }
      );
    }

    // 静态资源测试
    results.staticResource = await this.benchmarkFunction(
      'API Static Resource',
      async () => {
        const response = await axios.get(`${baseURL}/favicon.ico`);
        return { 
          success: response.status === 200, 
          duration: 0 
        };
      },
      { iterations: 800, concurrency: 15 }
    );

    return {
      name: 'HTTP API Benchmark',
      category: 'api',
      timestamp: new Date(),
      environment: this.getEnvironmentInfo(),
      results
    };
  }

  /**
   * 认证系统基准测试
   */
  async runAuthBenchmark(): Promise<BenchmarkResult> {
    logger.info('开始认证系统基准测试');

    const results: any = {};

    // JWT令牌验证测试
    const testToken = await this.getTestAuthToken();
    if (testToken) {
      results.jwtVerification = await this.benchmarkFunction(
        'JWT Verification',
        async () => {
          // 这里应该调用JWT验证逻辑
          // 简化实现，实际应该导入JWT服务
          return { success: true, duration: 0 };
        },
        { iterations: 2000, concurrency: 25 }
      );
    }

    // 密码哈希测试
    results.passwordHashing = await this.benchmarkFunction(
      'Password Hashing',
      async () => {
        const bcrypt = require('bcrypt');
        await bcrypt.hash('test-password', 10);
        return { success: true, duration: 0 };
      },
      { iterations: 100, concurrency: 5 }
    );

    // 会话验证测试
    results.sessionValidation = await this.benchmarkFunction(
      'Session Validation',
      async () => {
        // 模拟会话验证
        const sessionId = 'test-session-id';
        await redisService.get(`session:${sessionId}`);
        return { success: true, duration: 0 };
      },
      { iterations: 1500, concurrency: 20 }
    );

    return {
      name: 'Authentication Benchmark',
      category: 'auth',
      timestamp: new Date(),
      environment: this.getEnvironmentInfo(),
      results
    };
  }

  /**
   * 缓存系统基准测试
   */
  async runCacheBenchmark(): Promise<BenchmarkResult> {
    logger.info('开始缓存系统基准测试');

    const results: any = {};

    // 缓存命中测试
    results.cacheHit = await this.benchmarkFunction(
      'Cache Hit',
      async () => {
        const key = 'benchmark:cache:hit';
        await redisService.set(key, 'cached-value', 300);
        const value = await redisService.get(key);
        return { success: !!value, duration: 0 };
      },
      { iterations: 2000, concurrency: 25 }
    );

    // 缓存未命中测试
    results.cacheMiss = await this.benchmarkFunction(
      'Cache Miss',
      async () => {
        const key = `benchmark:cache:miss:${Date.now()}:${Math.random()}`;
        const value = await redisService.get(key);
        return { success: value === null, duration: 0 };
      },
      { iterations: 1500, concurrency: 20 }
    );

    // 缓存失效测试
    results.cacheExpiration = await this.benchmarkFunction(
      'Cache Expiration',
      async () => {
        const key = `benchmark:cache:exp:${Date.now()}`;
        await redisService.set(key, 'expiring-value', 1); // 1秒过期
        await new Promise(resolve => setTimeout(resolve, 1100)); // 等待过期
        const value = await redisService.get(key);
        return { success: value === null, duration: 0 };
      },
      { iterations: 100, concurrency: 5 }
    );

    return {
      name: 'Cache System Benchmark',
      category: 'cache',
      timestamp: new Date(),
      environment: this.getEnvironmentInfo(),
      results
    };
  }

  /**
   * 基准测试函数
   */
  private async benchmarkFunction(
    name: string,
    fn: () => Promise<{ success: boolean; duration: number }>,
    options: { iterations: number; concurrency: number }
  ): Promise<any> {
    const { iterations, concurrency } = options;
    const batchSize = Math.ceil(iterations / concurrency);
    
    const startTime = Date.now();
    const results: number[] = [];
    const errors: string[] = [];
    let successCount = 0;
    
    // 记录初始内存使用
    const initialMemory = process.memoryUsage().heapUsed;

    // 并发执行
    const promises: Promise<void>[] = [];
    
    for (let i = 0; i < concurrency; i++) {
      const promise = this.executeBatch(fn, batchSize, results, errors, (success) => {
        if (success) successCount++;
      });
      promises.push(promise);
    }

    await Promise.all(promises);

    const endTime = Date.now();
    const totalDuration = endTime - startTime;
    const finalMemory = process.memoryUsage().heapUsed;
    
    // 计算统计信息
    const sortedResults = results.sort((a, b) => a - b);
    const opsPerSecond = (successCount / totalDuration) * 1000;
    const avgLatency = results.length > 0 
      ? results.reduce((sum, time) => sum + time, 0) / results.length 
      : 0;
    const errorRate = iterations > 0 ? (errors.length / iterations) * 100 : 0;

    logger.debug(`基准测试完成: ${name}`, {
      iterations,
      successCount,
      errorCount: errors.length,
      opsPerSecond: opsPerSecond.toFixed(2),
      avgLatency: avgLatency.toFixed(2)
    });

    return {
      opsPerSecond,
      avgLatency,
      minLatency: sortedResults[0] || 0,
      maxLatency: sortedResults[sortedResults.length - 1] || 0,
      p95Latency: this.calculatePercentile(sortedResults, 95),
      p99Latency: this.calculatePercentile(sortedResults, 99),
      errorRate,
      throughput: opsPerSecond,
      memoryUsage: finalMemory - initialMemory,
      cpuUsage: 0 // 简化实现
    };
  }

  /**
   * 执行批次
   */
  private async executeBatch(
    fn: () => Promise<{ success: boolean; duration: number }>,
    batchSize: number,
    results: number[],
    errors: string[],
    onComplete: (success: boolean) => void
  ): Promise<void> {
    for (let i = 0; i < batchSize; i++) {
      try {
        const startTime = Date.now();
        const result = await fn();
        const duration = Date.now() - startTime;
        
        results.push(duration);
        onComplete(result.success);
        
        if (!result.success) {
          errors.push('Function returned false');
        }
      } catch (error) {
        const duration = Date.now() - Date.now();
        results.push(duration);
        errors.push(error instanceof Error ? error.message : String(error));
        onComplete(false);
      }
    }
  }

  /**
   * 计算百分位数
   */
  private calculatePercentile(sortedArray: number[], percentile: number): number {
    if (sortedArray.length === 0) return 0;
    
    const index = Math.ceil((percentile / 100) * sortedArray.length) - 1;
    return sortedArray[Math.max(0, Math.min(index, sortedArray.length - 1))];
  }

  /**
   * 获取环境信息
   */
  private getEnvironmentInfo() {
    const os = require('os');
    return {
      nodeVersion: process.version,
      platform: process.platform,
      arch: process.arch,
      cpus: os.cpus().length,
      memory: Math.round(os.totalmem() / 1024 / 1024) // MB
    };
  }

  /**
   * 获取测试认证令牌
   */
  private async getTestAuthToken(): Promise<string | null> {
    try {
      // 这里应该实现获取测试令牌的逻辑
      // 简化实现，返回null
      return null;
    } catch (error) {
      logger.warn('获取测试认证令牌失败', { error: error.message });
      return null;
    }
  }

  /**
   * 保存基准测试结果
   */
  saveBaseline(name: string, result: BenchmarkResult): void {
    this.baselineResults.set(name, result);
    logger.info('基准测试结果已保存', { name, category: result.category });
  }

  /**
   * 与基准进行比较
   */
  compareWithBaseline(name: string, result: BenchmarkResult): BenchmarkResult {
    const baseline = this.baselineResults.get(name);
    if (!baseline) {
      return result;
    }

    const improvements: any = {};
    
    for (const [testName, testResult] of Object.entries(result.results)) {
      const baselineResult = baseline.results[testName];
      if (baselineResult) {
        improvements[testName] = {
          opsPerSecondChange: ((testResult.opsPerSecond - baselineResult.opsPerSecond) / baselineResult.opsPerSecond) * 100,
          latencyChange: ((testResult.avgLatency - baselineResult.avgLatency) / baselineResult.avgLatency) * 100,
          throughputChange: ((testResult.throughput - baselineResult.throughput) / baselineResult.throughput) * 100
        };
      }
    }

    return {
      ...result,
      comparison: {
        baseline: baseline.name,
        improvements
      }
    };
  }

  /**
   * 获取所有基准测试结果
   */
  getAllBaselines(): BenchmarkResult[] {
    return Array.from(this.baselineResults.values());
  }

  /**
   * 获取所有测试结果
   */
  getAllResults(): BenchmarkResult[] {
    return Array.from(this.testResults.values());
  }
}

// 创建单例实例
export const benchmarkService = new BenchmarkService();
