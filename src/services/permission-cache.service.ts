/**
 * 权限缓存服务
 *
 * 实现多级缓存架构，提升权限验证性能
 * L1: 内存缓存 (LRU)
 * L2: Redis缓存
 *
 * <AUTHOR> Provider Team
 * @version 1.0.0
 * @since 2025-08-27
 */

import { LRUCache } from 'lru-cache';
import { RedisClientType } from 'redis';
import { logger } from '../utils/logger';
import { PermissionMetadata } from '../types/permission.types';

/**
 * 缓存键前缀
 */
const CACHE_KEYS = {
  USER_PERMISSIONS: 'user:permissions:',
  USER_ROLES: 'user:roles:',
  PERMISSION_METADATA: 'permission:metadata:',
  ROLE_PERMISSIONS: 'role:permissions:',
  APPLICATION_PERMISSIONS: 'app:permissions:',
  PERMISSION_DEPENDENCIES: 'permission:deps:',
  COMPUTED_PERMISSIONS: 'computed:permissions:'
} as const;

/**
 * 缓存TTL配置 (秒)
 */
const CACHE_TTL = {
  USER_PERMISSIONS: 900,      // 15分钟
  USER_ROLES: 1800,          // 30分钟
  PERMISSION_METADATA: 3600,  // 1小时
  ROLE_PERMISSIONS: 3600,     // 1小时
  APPLICATION_PERMISSIONS: 1800, // 30分钟
  PERMISSION_DEPENDENCIES: 7200, // 2小时
  COMPUTED_PERMISSIONS: 600   // 10分钟
} as const;

/**
 * L1缓存配置
 */
const L1_CACHE_CONFIG = {
  USER_PERMISSIONS: { max: 1000, ttl: CACHE_TTL.USER_PERMISSIONS * 1000 },
  USER_ROLES: { max: 1000, ttl: CACHE_TTL.USER_ROLES * 1000 },
  PERMISSION_METADATA: { max: 5000, ttl: CACHE_TTL.PERMISSION_METADATA * 1000 },
  ROLE_PERMISSIONS: { max: 500, ttl: CACHE_TTL.ROLE_PERMISSIONS * 1000 },
  APPLICATION_PERMISSIONS: { max: 2000, ttl: CACHE_TTL.APPLICATION_PERMISSIONS * 1000 }
} as const;

/**
 * 缓存统计接口
 */
interface CacheStats {
  l1Hits: number;
  l1Misses: number;
  l2Hits: number;
  l2Misses: number;
  totalRequests: number;
  hitRate: number;
  l1HitRate: number;
  l2HitRate: number;
}

/**
 * 权限缓存服务类
 */
export class PermissionCacheService {
  private redis: RedisClientType;
  private l1Caches: Map<string, LRUCache<string, any>>;
  private stats: CacheStats;

  constructor(redis: RedisClientType) {
    this.redis = redis;
    this.l1Caches = new Map();
    this.stats = {
      l1Hits: 0,
      l1Misses: 0,
      l2Hits: 0,
      l2Misses: 0,
      totalRequests: 0,
      hitRate: 0,
      l1HitRate: 0,
      l2HitRate: 0
    };

    this.initializeL1Caches();
  }

  /**
   * 初始化L1缓存
   */
  private initializeL1Caches(): void {
    Object.entries(L1_CACHE_CONFIG).forEach(([key, config]) => {
      this.l1Caches.set(key, new LRUCache(config));
    });

    logger.info('L1缓存初始化完成', {
      cacheCount: this.l1Caches.size,
      configs: L1_CACHE_CONFIG
    });
  }

  /**
   * 获取用户权限 (多级缓存)
   */
  async getUserPermissions(userId: string): Promise<string[] | null> {
    const cacheKey = `${CACHE_KEYS.USER_PERMISSIONS}${userId}`;
    this.stats.totalRequests++;

    try {
      // L1缓存检查
      const l1Cache = this.l1Caches.get('USER_PERMISSIONS');
      const l1Result = l1Cache?.get(cacheKey);

      if (l1Result) {
        this.stats.l1Hits++;
        this.updateHitRates();
        logger.debug('L1缓存命中', { userId, cacheKey });
        return l1Result;
      }

      this.stats.l1Misses++;

      // L2缓存检查
      const l2Result = await this.redis.get(cacheKey);

      if (l2Result) {
        this.stats.l2Hits++;
        const permissions = JSON.parse(l2Result);

        // 回填L1缓存
        l1Cache?.set(cacheKey, permissions);

        this.updateHitRates();
        logger.debug('L2缓存命中', { userId, cacheKey });
        return permissions;
      }

      this.stats.l2Misses++;
      this.updateHitRates();
      logger.debug('缓存未命中', { userId, cacheKey });
      return null;
    } catch (error) {
      logger.error('获取用户权限缓存失败', {
        userId,
        error: error instanceof Error ? error.message : String(error)
      });
      return null;
    }
  }

  /**
   * 设置用户权限缓存
   */
  async setUserPermissions(userId: string, permissions: string[]): Promise<void> {
    const cacheKey = `${CACHE_KEYS.USER_PERMISSIONS}${userId}`;

    try {
      // 设置L1缓存
      const l1Cache = this.l1Caches.get('USER_PERMISSIONS');
      l1Cache?.set(cacheKey, permissions);

      // 设置L2缓存
      await this.redis.setEx(
        cacheKey,
        CACHE_TTL.USER_PERMISSIONS,
        JSON.stringify(permissions)
      );

      logger.debug('用户权限缓存设置成功', {
        userId,
        permissionCount: permissions.length
      });
    } catch (error) {
      logger.error('设置用户权限缓存失败', {
        userId,
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  /**
   * 获取权限元数据缓存
   */
  async getPermissionMetadata(permissionId: string): Promise<PermissionMetadata | null> {
    const cacheKey = `${CACHE_KEYS.PERMISSION_METADATA}${permissionId}`;
    this.stats.totalRequests++;

    try {
      // L1缓存检查
      const l1Cache = this.l1Caches.get('PERMISSION_METADATA');
      const l1Result = l1Cache?.get(cacheKey);

      if (l1Result) {
        this.stats.l1Hits++;
        this.updateHitRates();
        return l1Result;
      }

      this.stats.l1Misses++;

      // L2缓存检查
      const l2Result = await this.redis.get(cacheKey);

      if (l2Result) {
        this.stats.l2Hits++;
        const metadata = JSON.parse(l2Result);

        // 回填L1缓存
        l1Cache?.set(cacheKey, metadata);

        this.updateHitRates();
        return metadata;
      }

      this.stats.l2Misses++;
      this.updateHitRates();
      return null;
    } catch (error) {
      logger.error('获取权限元数据缓存失败', {
        permissionId,
        error: error instanceof Error ? error.message : String(error)
      });
      return null;
    }
  }

  /**
   * 设置权限元数据缓存
   */
  async setPermissionMetadata(permissionId: string, metadata: PermissionMetadata): Promise<void> {
    const cacheKey = `${CACHE_KEYS.PERMISSION_METADATA}${permissionId}`;

    try {
      // 设置L1缓存
      const l1Cache = this.l1Caches.get('PERMISSION_METADATA');
      l1Cache?.set(cacheKey, metadata);

      // 设置L2缓存
      await this.redis.setEx(
        cacheKey,
        CACHE_TTL.PERMISSION_METADATA,
        JSON.stringify(metadata)
      );

      logger.debug('权限元数据缓存设置成功', { permissionId });
    } catch (error) {
      logger.error('设置权限元数据缓存失败', {
        permissionId,
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  /**
   * 批量获取权限元数据
   */
  async batchGetPermissionMetadata(permissionIds: string[]): Promise<Map<string, PermissionMetadata>> {
    const result = new Map<string, PermissionMetadata>();
    const uncachedIds: string[] = [];

    // 先从缓存中获取
    for (const permissionId of permissionIds) {
      const cached = await this.getPermissionMetadata(permissionId);
      if (cached) {
        result.set(permissionId, cached);
      } else {
        uncachedIds.push(permissionId);
      }
    }

    logger.debug('批量获取权限元数据', {
      totalRequested: permissionIds.length,
      cachedCount: result.size,
      uncachedCount: uncachedIds.length
    });

    return result;
  }

  /**
   * 获取角色权限缓存
   */
  async getRolePermissions(roleId: string): Promise<string[] | null> {
    const cacheKey = `${CACHE_KEYS.ROLE_PERMISSIONS}${roleId}`;
    this.stats.totalRequests++;

    try {
      // L1缓存检查
      const l1Cache = this.l1Caches.get('ROLE_PERMISSIONS');
      const l1Result = l1Cache?.get(cacheKey);

      if (l1Result) {
        this.stats.l1Hits++;
        this.updateHitRates();
        return l1Result;
      }

      this.stats.l1Misses++;

      // L2缓存检查
      const l2Result = await this.redis.get(cacheKey);

      if (l2Result) {
        this.stats.l2Hits++;
        const permissions = JSON.parse(l2Result);

        // 回填L1缓存
        l1Cache?.set(cacheKey, permissions);

        this.updateHitRates();
        return permissions;
      }

      this.stats.l2Misses++;
      this.updateHitRates();
      return null;
    } catch (error) {
      logger.error('获取角色权限缓存失败', {
        roleId,
        error: error instanceof Error ? error.message : String(error)
      });
      return null;
    }
  }

  /**
   * 设置角色权限缓存
   */
  async setRolePermissions(roleId: string, permissions: string[]): Promise<void> {
    const cacheKey = `${CACHE_KEYS.ROLE_PERMISSIONS}${roleId}`;

    try {
      // 设置L1缓存
      const l1Cache = this.l1Caches.get('ROLE_PERMISSIONS');
      l1Cache?.set(cacheKey, permissions);

      // 设置L2缓存
      await this.redis.setEx(
        cacheKey,
        CACHE_TTL.ROLE_PERMISSIONS,
        JSON.stringify(permissions)
      );

      logger.debug('角色权限缓存设置成功', {
        roleId,
        permissionCount: permissions.length
      });
    } catch (error) {
      logger.error('设置角色权限缓存失败', {
        roleId,
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  /**
   * 清除用户相关缓存
   */
  async clearUserCache(userId: string): Promise<void> {
    try {
      const keys = [
        `${CACHE_KEYS.USER_PERMISSIONS}${userId}`,
        `${CACHE_KEYS.USER_ROLES}${userId}`,
        `${CACHE_KEYS.COMPUTED_PERMISSIONS}${userId}`
      ];

      // 清除L1缓存
      for (const [cacheName, cache] of this.l1Caches) {
        for (const key of keys) {
          cache.delete(key);
        }
      }

      // 清除L2缓存
      await this.redis.del(keys);

      logger.info('用户缓存清除成功', { userId, clearedKeys: keys.length });
    } catch (error) {
      logger.error('清除用户缓存失败', {
        userId,
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  /**
   * 清除权限相关缓存
   */
  async clearPermissionCache(permissionId: string): Promise<void> {
    try {
      const pattern = `*${permissionId}*`;

      // 清除L2缓存中匹配的键
      const keys = await this.redis.keys(pattern);
      if (keys.length > 0) {
        await this.redis.del(keys);
      }

      // 清除L1缓存
      for (const [cacheName, cache] of this.l1Caches) {
        cache.clear(); // 简化处理，清除整个L1缓存
      }

      logger.info('权限缓存清除成功', { permissionId, clearedKeys: keys.length });
    } catch (error) {
      logger.error('清除权限缓存失败', {
        permissionId,
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  /**
   * 更新命中率统计
   */
  private updateHitRates(): void {
    if (this.stats.totalRequests > 0) {
      const totalHits = this.stats.l1Hits + this.stats.l2Hits;
      this.stats.hitRate = totalHits / this.stats.totalRequests;
      this.stats.l1HitRate = this.stats.l1Hits / this.stats.totalRequests;
      this.stats.l2HitRate = this.stats.l2Hits / this.stats.totalRequests;
    }
  }

  /**
   * 获取缓存统计信息
   */
  getCacheStats(): CacheStats {
    this.updateHitRates();
    return { ...this.stats };
  }

  /**
   * 重置缓存统计
   */
  resetCacheStats(): void {
    this.stats = {
      l1Hits: 0,
      l1Misses: 0,
      l2Hits: 0,
      l2Misses: 0,
      totalRequests: 0,
      hitRate: 0,
      l1HitRate: 0,
      l2HitRate: 0
    };
  }

  /**
   * 获取L1缓存信息
   */
  getL1CacheInfo(): Record<string, any> {
    const info: Record<string, any> = {};

    for (const [name, cache] of this.l1Caches) {
      info[name] = {
        size: cache.size,
        max: cache.max,
        calculatedSize: cache.calculatedSize
      };
    }

    return info;
  }

  /**
   * 预热缓存
   */
  async warmupCache(userIds: string[], permissionIds: string[]): Promise<void> {
    logger.info('开始缓存预热', {
      userCount: userIds.length,
      permissionCount: permissionIds.length
    });

    try {
      // 预热用户权限缓存
      const userPromises = userIds.map(async (userId) => {
        // 这里应该从数据库加载用户权限并设置缓存
        // 为了演示，暂时跳过具体实现
      });

      // 预热权限元数据缓存
      const permissionPromises = permissionIds.map(async (permissionId) => {
        // 这里应该从数据库加载权限元数据并设置缓存
        // 为了演示，暂时跳过具体实现
      });

      await Promise.all([...userPromises, ...permissionPromises]);

      logger.info('缓存预热完成');
    } catch (error) {
      logger.error('缓存预热失败', {
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }
}