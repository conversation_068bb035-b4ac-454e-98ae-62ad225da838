/**
 * 性能测试服务
 * 提供全面的性能测试和基准测试功能
 */

import { EventEmitter } from 'events';
import { logger } from '@/config/logger';
import { prisma } from '@/config/database';
import { redisService } from '@/services/redis.service';
import { metricsCollector } from '@/services/metrics-collector.service';

/**
 * 测试类型枚举
 */
export enum TestType {
  LOAD = 'load',           // 负载测试
  STRESS = 'stress',       // 压力测试
  SPIKE = 'spike',         // 峰值测试
  VOLUME = 'volume',       // 容量测试
  ENDURANCE = 'endurance', // 耐久性测试
  BASELINE = 'baseline'    // 基准测试
}

/**
 * 测试配置接口
 */
export interface TestConfig {
  type: TestType;
  name: string;
  description: string;
  duration: number;        // 测试持续时间（毫秒）
  concurrency: number;     // 并发数
  rampUpTime?: number;     // 爬坡时间（毫秒）
  rampDownTime?: number;   // 降压时间（毫秒）
  targetRPS?: number;      // 目标每秒请求数
  maxErrors?: number;      // 最大错误数
  thresholds?: {
    responseTime?: {
      avg?: number;
      p95?: number;
      p99?: number;
    };
    errorRate?: number;
    throughput?: number;
  };
}

/**
 * 测试结果接口
 */
export interface TestResult {
  testId: string;
  config: TestConfig;
  startTime: Date;
  endTime: Date;
  duration: number;
  
  // 请求统计
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  errorRate: number;
  
  // 性能指标
  responseTime: {
    min: number;
    max: number;
    avg: number;
    median: number;
    p95: number;
    p99: number;
  };
  
  // 吞吐量
  throughput: {
    rps: number;           // 每秒请求数
    rpm: number;           // 每分钟请求数
  };
  
  // 资源使用
  resourceUsage: {
    cpu: {
      avg: number;
      max: number;
    };
    memory: {
      avg: number;
      max: number;
    };
    connections: {
      avg: number;
      max: number;
    };
  };
  
  // 错误统计
  errors: Array<{
    type: string;
    count: number;
    percentage: number;
    samples: string[];
  }>;
  
  // 阈值检查
  thresholdResults: {
    passed: boolean;
    failures: string[];
  };
  
  // 详细数据
  timeline: Array<{
    timestamp: Date;
    rps: number;
    responseTime: number;
    errorRate: number;
    activeUsers: number;
  }>;
}

/**
 * 测试场景接口
 */
export interface TestScenario {
  name: string;
  weight: number;          // 权重（0-1）
  execute: () => Promise<{ success: boolean; duration: number; error?: string }>;
}

/**
 * 性能测试服务
 */
export class PerformanceTestService extends EventEmitter {
  private activeTests = new Map<string, { config: TestConfig; startTime: Date; abort: boolean }>();
  private testResults = new Map<string, TestResult>();
  private resourceMonitor?: NodeJS.Timeout;

  constructor() {
    super();
  }

  /**
   * 运行性能测试
   */
  async runTest(config: TestConfig, scenarios: TestScenario[]): Promise<TestResult> {
    const testId = this.generateTestId();
    const startTime = new Date();
    
    logger.info('开始性能测试', {
      testId,
      type: config.type,
      name: config.name,
      concurrency: config.concurrency,
      duration: config.duration
    });

    // 初始化测试状态
    this.activeTests.set(testId, { config, startTime, abort: false });
    
    // 开始资源监控
    this.startResourceMonitoring(testId);

    try {
      const result = await this.executeTest(testId, config, scenarios);
      this.testResults.set(testId, result);
      
      // 发出测试完成事件
      this.emit('testCompleted', result);
      
      logger.info('性能测试完成', {
        testId,
        duration: result.duration,
        totalRequests: result.totalRequests,
        errorRate: result.errorRate,
        avgResponseTime: result.responseTime.avg
      });

      return result;

    } catch (error) {
      logger.error('性能测试失败', {
        testId,
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;

    } finally {
      // 清理测试状态
      this.activeTests.delete(testId);
      this.stopResourceMonitoring();
    }
  }

  /**
   * 执行测试
   */
  private async executeTest(
    testId: string,
    config: TestConfig,
    scenarios: TestScenario[]
  ): Promise<TestResult> {
    const startTime = new Date();
    const endTime = new Date(startTime.getTime() + config.duration);
    
    // 测试统计
    const stats = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      responseTimes: [] as number[],
      errors: new Map<string, { count: number; samples: string[] }>(),
      timeline: [] as any[],
      resourceUsage: {
        cpu: [] as number[],
        memory: [] as number[],
        connections: [] as number[]
      }
    };

    // 并发执行器
    const workers: Promise<void>[] = [];
    const activeUsers = { count: 0 };

    // 创建并发工作器
    for (let i = 0; i < config.concurrency; i++) {
      const worker = this.createWorker(testId, config, scenarios, stats, activeUsers, endTime);
      workers.push(worker);
    }

    // 定期收集时间线数据
    const timelineInterval = setInterval(() => {
      if (this.isTestActive(testId)) {
        stats.timeline.push({
          timestamp: new Date(),
          rps: this.calculateCurrentRPS(stats, 1000), // 最近1秒的RPS
          responseTime: this.calculateRecentAvgResponseTime(stats, 1000),
          errorRate: stats.failedRequests / Math.max(stats.totalRequests, 1) * 100,
          activeUsers: activeUsers.count
        });
      }
    }, 1000);

    try {
      // 等待所有工作器完成
      await Promise.all(workers);
    } finally {
      clearInterval(timelineInterval);
    }

    const actualEndTime = new Date();
    const actualDuration = actualEndTime.getTime() - startTime.getTime();

    // 计算结果
    return this.calculateTestResult(testId, config, startTime, actualEndTime, actualDuration, stats);
  }

  /**
   * 创建工作器
   */
  private async createWorker(
    testId: string,
    config: TestConfig,
    scenarios: TestScenario[],
    stats: any,
    activeUsers: { count: number },
    endTime: Date
  ): Promise<void> {
    while (new Date() < endTime && this.isTestActive(testId)) {
      activeUsers.count++;
      
      try {
        // 选择场景
        const scenario = this.selectScenario(scenarios);
        
        // 执行场景
        const startTime = Date.now();
        const result = await scenario.execute();
        const duration = Date.now() - startTime;

        // 记录统计
        stats.totalRequests++;
        stats.responseTimes.push(duration);

        if (result.success) {
          stats.successfulRequests++;
        } else {
          stats.failedRequests++;
          
          // 记录错误
          const errorType = result.error || 'unknown';
          if (!stats.errors.has(errorType)) {
            stats.errors.set(errorType, { count: 0, samples: [] });
          }
          const errorInfo = stats.errors.get(errorType)!;
          errorInfo.count++;
          if (errorInfo.samples.length < 10) {
            errorInfo.samples.push(result.error || 'unknown error');
          }
        }

        // 记录指标
        metricsCollector.recordHistogram('performance_test_response_time', duration, {
          test_id: testId,
          scenario: scenario.name,
          success: result.success.toString()
        });

        // 检查是否达到最大错误数
        if (config.maxErrors && stats.failedRequests >= config.maxErrors) {
          logger.warn('达到最大错误数，停止测试', {
            testId,
            maxErrors: config.maxErrors,
            currentErrors: stats.failedRequests
          });
          this.abortTest(testId);
          break;
        }

        // 控制请求速率
        if (config.targetRPS) {
          const expectedInterval = 1000 / (config.targetRPS / config.concurrency);
          const actualInterval = duration;
          const delay = Math.max(0, expectedInterval - actualInterval);
          if (delay > 0) {
            await this.sleep(delay);
          }
        }

      } catch (error) {
        stats.failedRequests++;
        logger.error('工作器执行失败', {
          testId,
          error: error instanceof Error ? error.message : String(error)
        });
      } finally {
        activeUsers.count--;
      }
    }
  }

  /**
   * 选择测试场景
   */
  private selectScenario(scenarios: TestScenario[]): TestScenario {
    const random = Math.random();
    let cumulativeWeight = 0;
    
    for (const scenario of scenarios) {
      cumulativeWeight += scenario.weight;
      if (random <= cumulativeWeight) {
        return scenario;
      }
    }
    
    return scenarios[scenarios.length - 1];
  }

  /**
   * 计算测试结果
   */
  private calculateTestResult(
    testId: string,
    config: TestConfig,
    startTime: Date,
    endTime: Date,
    duration: number,
    stats: any
  ): TestResult {
    // 计算响应时间统计
    const sortedResponseTimes = stats.responseTimes.sort((a: number, b: number) => a - b);
    const responseTime = {
      min: sortedResponseTimes[0] || 0,
      max: sortedResponseTimes[sortedResponseTimes.length - 1] || 0,
      avg: sortedResponseTimes.length > 0 
        ? sortedResponseTimes.reduce((sum: number, time: number) => sum + time, 0) / sortedResponseTimes.length 
        : 0,
      median: this.calculatePercentile(sortedResponseTimes, 50),
      p95: this.calculatePercentile(sortedResponseTimes, 95),
      p99: this.calculatePercentile(sortedResponseTimes, 99)
    };

    // 计算吞吐量
    const durationInSeconds = duration / 1000;
    const throughput = {
      rps: durationInSeconds > 0 ? stats.totalRequests / durationInSeconds : 0,
      rpm: durationInSeconds > 0 ? (stats.totalRequests / durationInSeconds) * 60 : 0
    };

    // 计算错误率
    const errorRate = stats.totalRequests > 0 
      ? (stats.failedRequests / stats.totalRequests) * 100 
      : 0;

    // 处理错误统计
    const errors = Array.from(stats.errors.entries()).map(([type, info]) => ({
      type,
      count: info.count,
      percentage: stats.totalRequests > 0 ? (info.count / stats.totalRequests) * 100 : 0,
      samples: info.samples
    }));

    // 计算资源使用
    const resourceUsage = {
      cpu: {
        avg: stats.resourceUsage.cpu.length > 0 
          ? stats.resourceUsage.cpu.reduce((sum: number, val: number) => sum + val, 0) / stats.resourceUsage.cpu.length 
          : 0,
        max: Math.max(...stats.resourceUsage.cpu, 0)
      },
      memory: {
        avg: stats.resourceUsage.memory.length > 0 
          ? stats.resourceUsage.memory.reduce((sum: number, val: number) => sum + val, 0) / stats.resourceUsage.memory.length 
          : 0,
        max: Math.max(...stats.resourceUsage.memory, 0)
      },
      connections: {
        avg: stats.resourceUsage.connections.length > 0 
          ? stats.resourceUsage.connections.reduce((sum: number, val: number) => sum + val, 0) / stats.resourceUsage.connections.length 
          : 0,
        max: Math.max(...stats.resourceUsage.connections, 0)
      }
    };

    // 检查阈值
    const thresholdResults = this.checkThresholds(config, responseTime, errorRate, throughput);

    return {
      testId,
      config,
      startTime,
      endTime,
      duration,
      totalRequests: stats.totalRequests,
      successfulRequests: stats.successfulRequests,
      failedRequests: stats.failedRequests,
      errorRate,
      responseTime,
      throughput,
      resourceUsage,
      errors,
      thresholdResults,
      timeline: stats.timeline
    };
  }

  /**
   * 计算百分位数
   */
  private calculatePercentile(sortedArray: number[], percentile: number): number {
    if (sortedArray.length === 0) return 0;
    
    const index = Math.ceil((percentile / 100) * sortedArray.length) - 1;
    return sortedArray[Math.max(0, Math.min(index, sortedArray.length - 1))];
  }

  /**
   * 检查阈值
   */
  private checkThresholds(
    config: TestConfig,
    responseTime: any,
    errorRate: number,
    throughput: any
  ): { passed: boolean; failures: string[] } {
    const failures: string[] = [];
    
    if (config.thresholds) {
      const { thresholds } = config;
      
      if (thresholds.responseTime) {
        if (thresholds.responseTime.avg && responseTime.avg > thresholds.responseTime.avg) {
          failures.push(`平均响应时间 ${responseTime.avg.toFixed(2)}ms 超过阈值 ${thresholds.responseTime.avg}ms`);
        }
        if (thresholds.responseTime.p95 && responseTime.p95 > thresholds.responseTime.p95) {
          failures.push(`P95响应时间 ${responseTime.p95.toFixed(2)}ms 超过阈值 ${thresholds.responseTime.p95}ms`);
        }
        if (thresholds.responseTime.p99 && responseTime.p99 > thresholds.responseTime.p99) {
          failures.push(`P99响应时间 ${responseTime.p99.toFixed(2)}ms 超过阈值 ${thresholds.responseTime.p99}ms`);
        }
      }
      
      if (thresholds.errorRate && errorRate > thresholds.errorRate) {
        failures.push(`错误率 ${errorRate.toFixed(2)}% 超过阈值 ${thresholds.errorRate}%`);
      }
      
      if (thresholds.throughput && throughput.rps < thresholds.throughput) {
        failures.push(`吞吐量 ${throughput.rps.toFixed(2)} RPS 低于阈值 ${thresholds.throughput} RPS`);
      }
    }
    
    return {
      passed: failures.length === 0,
      failures
    };
  }

  /**
   * 开始资源监控
   */
  private startResourceMonitoring(testId: string): void {
    this.resourceMonitor = setInterval(() => {
      if (this.isTestActive(testId)) {
        const memoryUsage = process.memoryUsage();
        const cpuUsage = process.cpuUsage();
        
        // 记录资源使用指标
        metricsCollector.setGauge('performance_test_memory_usage', memoryUsage.heapUsed, {
          test_id: testId
        });
        
        metricsCollector.setGauge('performance_test_cpu_usage', cpuUsage.user + cpuUsage.system, {
          test_id: testId
        });
      }
    }, 1000);
  }

  /**
   * 停止资源监控
   */
  private stopResourceMonitoring(): void {
    if (this.resourceMonitor) {
      clearInterval(this.resourceMonitor);
      this.resourceMonitor = undefined;
    }
  }

  /**
   * 检查测试是否活跃
   */
  private isTestActive(testId: string): boolean {
    const test = this.activeTests.get(testId);
    return test ? !test.abort : false;
  }

  /**
   * 中止测试
   */
  abortTest(testId: string): void {
    const test = this.activeTests.get(testId);
    if (test) {
      test.abort = true;
      logger.info('测试已中止', { testId });
    }
  }

  /**
   * 计算当前RPS
   */
  private calculateCurrentRPS(stats: any, windowMs: number): number {
    const now = Date.now();
    const windowStart = now - windowMs;
    
    // 这里简化实现，实际应该基于时间戳计算
    return stats.totalRequests / Math.max(1, windowMs / 1000);
  }

  /**
   * 计算最近平均响应时间
   */
  private calculateRecentAvgResponseTime(stats: any, windowMs: number): number {
    const recentTimes = stats.responseTimes.slice(-100); // 最近100个请求
    return recentTimes.length > 0 
      ? recentTimes.reduce((sum: number, time: number) => sum + time, 0) / recentTimes.length 
      : 0;
  }

  /**
   * 生成测试ID
   */
  private generateTestId(): string {
    return `test_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`;
  }

  /**
   * 睡眠函数
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 获取测试结果
   */
  getTestResult(testId: string): TestResult | undefined {
    return this.testResults.get(testId);
  }

  /**
   * 获取所有测试结果
   */
  getAllTestResults(): TestResult[] {
    return Array.from(this.testResults.values());
  }

  /**
   * 获取活跃测试
   */
  getActiveTests(): Array<{ testId: string; config: TestConfig; startTime: Date }> {
    return Array.from(this.activeTests.entries()).map(([testId, test]) => ({
      testId,
      config: test.config,
      startTime: test.startTime
    }));
  }
}

// 创建单例实例
export const performanceTestService = new PerformanceTestService();
