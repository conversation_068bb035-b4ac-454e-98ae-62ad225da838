/**
 * 权限管理服务
 *
 * 提供权限注册、查询、验证等核心功能
 *
 * <AUTHOR> Provider Team
 * @version 1.0.0
 * @since 2025-08-27
 */

import { PrismaClient } from '@prisma/client';
import {
  PermissionMetadata,
  PermissionContext,
  PermissionValidationResult,
  PermissionDependency,
  PermissionChangeRecord,
  ResourceType,
  PermissionOperation
} from '../types/permission.types';
import { PermissionValidator } from '../utils/permission.utils';
import { logger } from '../utils/logger';
import { v4 as uuidv4 } from 'uuid';

/**
 * 权限管理服务类
 */
export class PermissionService {
  private prisma: PrismaClient;

  constructor(prisma: PrismaClient) {
    this.prisma = prisma;
  }

  /**
   * 注册新权限
   */
  async registerPermission(
    permissionData: Omit<PermissionMetadata, 'metadata'>,
    createdBy: string
  ): Promise<PermissionMetadata> {
    try {
      // 验证权限数据
      await this.validatePermissionData(permissionData);

      // 检查权限是否已存在
      const existingPermission = await this.prisma.permission.findUnique({
        where: { id: permissionData.id }
      });

      if (existingPermission) {
        throw new Error(`权限 ${permissionData.id} 已存在`);
      }

      // 验证权限依赖关系
      if (permissionData.dependencies.length > 0) {
        await this.validateDependencies(permissionData.dependencies);
      }

      // 创建权限记录
      const permission = await this.prisma.permission.create({
        data: {
          id: permissionData.id,
          name: permissionData.name,
          description: permissionData.description,
          category: permissionData.category,
          resourceType: permissionData.resourceType,
          operations: permissionData.operations,
          level: permissionData.level,
          dependencies: permissionData.dependencies,
          constraints: permissionData.constraints,
          tags: permissionData.tags,
          isSensitive: permissionData.isSensitive,
          isDelegatable: permissionData.isDelegatable,
          validity: permissionData.validity,
          createdBy,
          updatedBy: createdBy,
          version: '1.0.0',
          sourceApplication: permissionData.metadata?.sourceApplication,
          documentationUrl: permissionData.metadata?.documentationUrl,
          owner: permissionData.metadata?.owner,
          approvalStatus: 'pending'
        }
      });

      // 记录权限变更
      await this.recordPermissionChange({
        permissionId: permission.id,
        changeType: 'created',
        newValue: permission,
        reason: '新权限注册',
        changedBy: createdBy
      });

      logger.info('权限注册成功', {
        permissionId: permission.id,
        name: permission.name,
        createdBy
      });

      return this.mapToPermissionMetadata(permission);
    } catch (error) {
      logger.error('权限注册失败', {
        permissionId: permissionData.id,
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * 更新权限
   */
  async updatePermission(
    permissionId: string,
    updateData: Partial<PermissionMetadata>,
    updatedBy: string
  ): Promise<PermissionMetadata> {
    try {
      // 获取现有权限
      const existingPermission = await this.prisma.permission.findUnique({
        where: { id: permissionId }
      });

      if (!existingPermission) {
        throw new Error(`权限 ${permissionId} 不存在`);
      }

      // 验证更新数据
      if (updateData.dependencies) {
        await this.validateDependencies(updateData.dependencies);
      }

      // 更新权限
      const updatedPermission = await this.prisma.permission.update({
        where: { id: permissionId },
        data: {
          ...updateData,
          updatedBy,
          version: this.incrementVersion(existingPermission.version)
        }
      });

      // 记录权限变更
      await this.recordPermissionChange({
        permissionId,
        changeType: 'updated',
        previousValue: existingPermission,
        newValue: updatedPermission,
        reason: '权限更新',
        changedBy: updatedBy
      });

      logger.info('权限更新成功', {
        permissionId,
        updatedBy
      });

      return this.mapToPermissionMetadata(updatedPermission);
    } catch (error) {
      logger.error('权限更新失败', {
        permissionId,
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * 删除权限
   */
  async deletePermission(permissionId: string, deletedBy: string): Promise<void> {
    try {
      // 检查权限是否存在
      const permission = await this.prisma.permission.findUnique({
        where: { id: permissionId }
      });

      if (!permission) {
        throw new Error(`权限 ${permissionId} 不存在`);
      }

      // 检查是否有依赖此权限的其他权限
      const dependentPermissions = await this.findDependentPermissions(permissionId);
      if (dependentPermissions.length > 0) {
        throw new Error(`无法删除权限 ${permissionId}，存在依赖此权限的其他权限`);
      }

      // 记录权限变更
      await this.recordPermissionChange({
        permissionId,
        changeType: 'deleted',
        previousValue: permission,
        reason: '权限删除',
        changedBy: deletedBy
      });

      // 删除权限
      await this.prisma.permission.delete({
        where: { id: permissionId }
      });

      logger.info('权限删除成功', {
        permissionId,
        deletedBy
      });
    } catch (error) {
      logger.error('权限删除失败', {
        permissionId,
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * 获取权限详情
   */
  async getPermission(permissionId: string): Promise<PermissionMetadata | null> {
    try {
      const permission = await this.prisma.permission.findUnique({
        where: { id: permissionId }
      });

      if (!permission) {
        return null;
      }

      return this.mapToPermissionMetadata(permission);
    } catch (error) {
      logger.error('获取权限详情失败', {
        permissionId,
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * 查询权限列表
   */
  async queryPermissions(filters: {
    category?: string;
    resourceType?: string;
    level?: number;
    isSensitive?: boolean;
    approvalStatus?: string;
    sourceApplication?: string;
    tags?: string[];
    search?: string;
    page?: number;
    limit?: number;
  }): Promise<{
    permissions: PermissionMetadata[];
    total: number;
    page: number;
    limit: number;
  }> {
    try {
      const {
        category,
        resourceType,
        level,
        isSensitive,
        approvalStatus,
        sourceApplication,
        tags,
        search,
        page = 1,
        limit = 20
      } = filters;

      // 构建查询条件
      const where: any = {};

      if (category) where.category = category;
      if (resourceType) where.resourceType = resourceType;
      if (level !== undefined) where.level = level;
      if (isSensitive !== undefined) where.isSensitive = isSensitive;
      if (approvalStatus) where.approvalStatus = approvalStatus;
      if (sourceApplication) where.sourceApplication = sourceApplication;

      if (search) {
        where.OR = [
          { name: { contains: search } },
          { description: { contains: search } },
          { id: { contains: search } }
        ];
      }

      if (tags && tags.length > 0) {
        where.tags = {
          array_contains: tags
        };
      }

      // 计算偏移量
      const offset = (page - 1) * limit;

      // 查询权限列表
      const [permissions, total] = await Promise.all([
        this.prisma.permission.findMany({
          where,
          skip: offset,
          take: limit,
          orderBy: { createdAt: 'desc' }
        }),
        this.prisma.permission.count({ where })
      ]);

      return {
        permissions: permissions.map(p => this.mapToPermissionMetadata(p)),
        total,
        page,
        limit
      };
    } catch (error) {
      logger.error('查询权限列表失败', {
        filters,
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * 验证权限
   */
  async validatePermission(
    permissionId: string,
    context: PermissionContext
  ): Promise<PermissionValidationResult> {
    const startTime = Date.now();

    try {
      // 获取权限定义
      const permission = await this.prisma.permission.findUnique({
        where: { id: permissionId }
      });

      if (!permission) {
        return {
          granted: false,
          permissionId,
          reason: '权限不存在',
          validatedAt: new Date().toISOString(),
          validationTime: Date.now() - startTime
        };
      }

      // 检查权限是否已批准
      if (permission.approvalStatus !== 'approved') {
        return {
          granted: false,
          permissionId,
          reason: '权限未批准',
          validatedAt: new Date().toISOString(),
          validationTime: Date.now() - startTime
        };
      }

      // 获取用户权限
      const userPermissions = await this.getUserPermissions(context.userId);

      // 检查用户是否拥有此权限
      if (!userPermissions.includes(permissionId)) {
        return {
          granted: false,
          permissionId,
          reason: '用户没有此权限',
          validatedAt: new Date().toISOString(),
          validationTime: Date.now() - startTime
        };
      }

      // 检查权限依赖关系
      const dependencyCheck = PermissionValidator.checkDependencies(
        permission.dependencies as PermissionDependency[],
        userPermissions
      );

      if (!dependencyCheck.satisfied) {
        return {
          granted: false,
          permissionId,
          reason: '权限依赖不满足',
          missingDependencies: dependencyCheck.missingDependencies,
          validatedAt: new Date().toISOString(),
          validationTime: Date.now() - startTime
        };
      }

      // 验证权限约束条件
      const constraintCheck = PermissionValidator.validateConstraints(
        permission.constraints as any,
        context
      );

      if (!constraintCheck.valid) {
        return {
          granted: false,
          permissionId,
          reason: '权限约束条件不满足',
          failedConstraints: constraintCheck.failedConstraints,
          validatedAt: new Date().toISOString(),
          validationTime: Date.now() - startTime
        };
      }

      // 记录权限使用日志
      await this.logPermissionUsage({
        userId: context.userId,
        permissionId,
        result: 'granted',
        context
      });

      return {
        granted: true,
        permissionId,
        reason: '权限验证通过',
        validatedAt: new Date().toISOString(),
        validationTime: Date.now() - startTime
      };
    } catch (error) {
      logger.error('权限验证失败', {
        permissionId,
        userId: context.userId,
        error: error instanceof Error ? error.message : String(error)
      });

      // 记录错误日志
      await this.logPermissionUsage({
        userId: context.userId,
        permissionId,
        result: 'error',
        context,
        error: error instanceof Error ? error.message : String(error)
      });

      return {
        granted: false,
        permissionId,
        reason: '权限验证出错',
        validatedAt: new Date().toISOString(),
        validationTime: Date.now() - startTime
      };
    }
  }