/**
 * 风险评估引擎
 * 实现零信任架构的核心组件，提供实时风险评估、威胁检测和自适应认证决策
 */

import { Request } from 'express';
import { logger } from '@/config/logger';
import { cacheService } from '@/services/cache.service';
import { prisma } from '@/config/database';
import { metricsCollector } from '@/services/metrics-collector.service';
import crypto from 'crypto';

/**
 * 风险级别枚举
 */
export enum RiskLevel {
  VERY_LOW = 'very_low',
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  VERY_HIGH = 'very_high',
  CRITICAL = 'critical'
}

/**
 * 风险因子类型
 */
export enum RiskFactorType {
  LOCATION = 'location',
  DEVICE = 'device',
  BEHAVIOR = 'behavior',
  NETWORK = 'network',
  TIME = 'time',
  AUTHENTICATION = 'authentication',
  REPUTATION = 'reputation'
}

/**
 * 风险因子接口
 */
interface RiskFactor {
  type: RiskFactorType;
  name: string;
  score: number; // 0-100
  weight: number; // 权重
  confidence: number; // 置信度 0-1
  details: Record<string, any>;
  timestamp: Date;
}

/**
 * 风险评估结果接口
 */
interface RiskAssessmentResult {
  userId?: string;
  sessionId?: string;
  ipAddress: string;
  userAgent: string;
  overallRiskScore: number; // 0-100
  riskLevel: RiskLevel;
  riskFactors: RiskFactor[];
  recommendations: string[];
  requiredActions: string[];
  confidence: number;
  timestamp: Date;
  expiresAt: Date;
}

/**
 * 设备指纹接口
 */
interface DeviceFingerprint {
  id: string;
  userId?: string;
  fingerprint: string;
  components: {
    userAgent: string;
    screen: string;
    timezone: string;
    language: string;
    platform: string;
    plugins: string[];
    fonts: string[];
    canvas?: string;
    webgl?: string;
  };
  trustScore: number; // 0-100
  firstSeen: Date;
  lastSeen: Date;
  seenCount: number;
  isBlacklisted: boolean;
}

/**
 * 地理位置信息接口
 */
interface GeoLocation {
  ip: string;
  country: string;
  region: string;
  city: string;
  latitude: number;
  longitude: number;
  isp: string;
  organization: string;
  isVpn: boolean;
  isTor: boolean;
  isProxy: boolean;
  threatLevel: number; // 0-100
}

/**
 * 风险评估引擎
 */
export class RiskAssessmentService {
  private riskCache = new Map<string, RiskAssessmentResult>();
  private deviceCache = new Map<string, DeviceFingerprint>();
  private geoCache = new Map<string, GeoLocation>();

  /**
   * 执行综合风险评估
   */
  async assessRisk(
    req: Request,
    userId?: string,
    additionalContext: Record<string, any> = {}
  ): Promise<RiskAssessmentResult> {
    const startTime = Date.now();
    
    try {
      const ipAddress = req.ip || 'unknown';
      const userAgent = req.get('User-Agent') || 'unknown';
      const sessionId = req.sessionID;

      // 生成评估缓存键
      const cacheKey = this.generateCacheKey(userId, ipAddress, userAgent);
      
      // 检查缓存
      const cachedResult = await this.getCachedAssessment(cacheKey);
      if (cachedResult && !this.isExpired(cachedResult)) {
        return cachedResult;
      }

      // 并行收集风险因子
      const [
        locationRisk,
        deviceRisk,
        behaviorRisk,
        networkRisk,
        timeRisk,
        authRisk,
        reputationRisk
      ] = await Promise.all([
        this.assessLocationRisk(ipAddress),
        this.assessDeviceRisk(req, userId),
        this.assessBehaviorRisk(req, userId),
        this.assessNetworkRisk(ipAddress),
        this.assessTimeRisk(userId),
        this.assessAuthenticationRisk(req, userId),
        this.assessReputationRisk(ipAddress, userAgent)
      ]);

      const riskFactors = [
        locationRisk,
        deviceRisk,
        behaviorRisk,
        networkRisk,
        timeRisk,
        authRisk,
        reputationRisk
      ].filter(factor => factor !== null) as RiskFactor[];

      // 计算综合风险评分
      const overallRiskScore = this.calculateOverallRisk(riskFactors);
      const riskLevel = this.determineRiskLevel(overallRiskScore);
      const confidence = this.calculateConfidence(riskFactors);

      // 生成建议和必需操作
      const recommendations = this.generateRecommendations(riskFactors, riskLevel);
      const requiredActions = this.generateRequiredActions(riskLevel, riskFactors);

      const result: RiskAssessmentResult = {
        userId,
        sessionId,
        ipAddress,
        userAgent,
        overallRiskScore,
        riskLevel,
        riskFactors,
        recommendations,
        requiredActions,
        confidence,
        timestamp: new Date(),
        expiresAt: new Date(Date.now() + 5 * 60 * 1000) // 5分钟过期
      };

      // 缓存结果
      await this.cacheAssessment(cacheKey, result);

      // 记录指标
      const duration = Date.now() - startTime;
      metricsCollector.recordHistogram('risk_assessment_duration', duration);
      metricsCollector.incrementCounter('risk_assessments_total', {
        risk_level: riskLevel,
        user_id: userId || 'anonymous'
      });

      logger.info('风险评估完成', {
        userId,
        ipAddress,
        riskScore: overallRiskScore,
        riskLevel,
        duration,
        factorCount: riskFactors.length
      });

      return result;

    } catch (error) {
      logger.error('风险评估失败', {
        error: error instanceof Error ? error.message : String(error),
        userId,
        ipAddress: req.ip
      });

      // 返回高风险结果作为安全默认值
      return this.createFailsafeResult(req, userId);
    }
  }

  /**
   * 评估地理位置风险
   */
  private async assessLocationRisk(ipAddress: string): Promise<RiskFactor | null> {
    try {
      const geoInfo = await this.getGeoLocation(ipAddress);
      let riskScore = 0;
      const details: Record<string, any> = {
        country: geoInfo.country,
        region: geoInfo.region,
        city: geoInfo.city,
        isp: geoInfo.isp
      };

      // 高风险国家/地区
      const highRiskCountries = ['CN', 'RU', 'KP', 'IR'];
      if (highRiskCountries.includes(geoInfo.country)) {
        riskScore += 30;
        details.reason = '来自高风险国家';
      }

      // VPN/代理检测
      if (geoInfo.isVpn || geoInfo.isProxy) {
        riskScore += 25;
        details.vpnProxy = true;
        details.reason = (details.reason || '') + ' 使用VPN/代理';
      }

      // Tor网络检测
      if (geoInfo.isTor) {
        riskScore += 40;
        details.tor = true;
        details.reason = (details.reason || '') + ' 使用Tor网络';
      }

      // 威胁情报评分
      riskScore += geoInfo.threatLevel * 0.3;

      return {
        type: RiskFactorType.LOCATION,
        name: '地理位置风险',
        score: Math.min(riskScore, 100),
        weight: 0.2,
        confidence: 0.8,
        details,
        timestamp: new Date()
      };

    } catch (error) {
      logger.warn('地理位置风险评估失败', { error: error.message, ipAddress });
      return null;
    }
  }

  /**
   * 评估设备风险
   */
  private async assessDeviceRisk(req: Request, userId?: string): Promise<RiskFactor | null> {
    try {
      const deviceFingerprint = await this.generateDeviceFingerprint(req);
      const knownDevice = await this.getKnownDevice(deviceFingerprint.fingerprint, userId);
      
      let riskScore = 0;
      const details: Record<string, any> = {
        fingerprintId: deviceFingerprint.id,
        isKnown: !!knownDevice
      };

      if (knownDevice) {
        // 已知设备，基于信任评分
        riskScore = 100 - knownDevice.trustScore;
        details.trustScore = knownDevice.trustScore;
        details.lastSeen = knownDevice.lastSeen;
        details.seenCount = knownDevice.seenCount;

        if (knownDevice.isBlacklisted) {
          riskScore = 100;
          details.blacklisted = true;
        }
      } else {
        // 新设备，中等风险
        riskScore = 50;
        details.reason = '未知设备';
      }

      // 检查设备指纹异常
      const fingerprintAnomalies = this.detectFingerprintAnomalies(deviceFingerprint);
      if (fingerprintAnomalies.length > 0) {
        riskScore += 20;
        details.anomalies = fingerprintAnomalies;
      }

      return {
        type: RiskFactorType.DEVICE,
        name: '设备风险',
        score: Math.min(riskScore, 100),
        weight: 0.25,
        confidence: knownDevice ? 0.9 : 0.6,
        details,
        timestamp: new Date()
      };

    } catch (error) {
      logger.warn('设备风险评估失败', { error: error.message, userId });
      return null;
    }
  }

  /**
   * 评估行为风险
   */
  private async assessBehaviorRisk(req: Request, userId?: string): Promise<RiskFactor | null> {
    if (!userId) return null;

    try {
      const now = new Date();
      const timeWindow = 24 * 60 * 60 * 1000; // 24小时
      const since = new Date(now.getTime() - timeWindow);

      // 获取用户最近的行为数据
      const [recentLogins, recentActions, failedAttempts] = await Promise.all([
        this.getRecentLogins(userId, since),
        this.getRecentActions(userId, since),
        this.getFailedAttempts(userId, since)
      ]);

      let riskScore = 0;
      const details: Record<string, any> = {
        recentLogins: recentLogins.length,
        recentActions: recentActions.length,
        failedAttempts: failedAttempts.length
      };

      // 异常登录频率
      if (recentLogins.length > 20) {
        riskScore += 15;
        details.highLoginFrequency = true;
      }

      // 异常操作频率
      if (recentActions.length > 1000) {
        riskScore += 20;
        details.highActionFrequency = true;
      }

      // 失败尝试次数
      if (failedAttempts.length > 5) {
        riskScore += failedAttempts.length * 2;
        details.highFailureRate = true;
      }

      // 行为模式分析
      const behaviorPattern = await this.analyzeBehaviorPattern(userId, recentActions);
      if (behaviorPattern.isAnomalous) {
        riskScore += 25;
        details.behaviorAnomaly = behaviorPattern;
      }

      return {
        type: RiskFactorType.BEHAVIOR,
        name: '行为风险',
        score: Math.min(riskScore, 100),
        weight: 0.2,
        confidence: 0.7,
        details,
        timestamp: new Date()
      };

    } catch (error) {
      logger.warn('行为风险评估失败', { error: error.message, userId });
      return null;
    }
  }

  /**
   * 评估网络风险
   */
  private async assessNetworkRisk(ipAddress: string): Promise<RiskFactor | null> {
    try {
      let riskScore = 0;
      const details: Record<string, any> = { ipAddress };

      // 检查IP信誉
      const ipReputation = await this.checkIPReputation(ipAddress);
      riskScore += ipReputation.riskScore;
      details.reputation = ipReputation;

      // 检查是否在黑名单中
      const isBlacklisted = await this.isIPBlacklisted(ipAddress);
      if (isBlacklisted) {
        riskScore = 100;
        details.blacklisted = true;
      }

      // 检查最近的恶意活动
      const recentMaliciousActivity = await this.getRecentMaliciousActivity(ipAddress);
      if (recentMaliciousActivity.length > 0) {
        riskScore += recentMaliciousActivity.length * 10;
        details.maliciousActivity = recentMaliciousActivity;
      }

      return {
        type: RiskFactorType.NETWORK,
        name: '网络风险',
        score: Math.min(riskScore, 100),
        weight: 0.15,
        confidence: 0.8,
        details,
        timestamp: new Date()
      };

    } catch (error) {
      logger.warn('网络风险评估失败', { error: error.message, ipAddress });
      return null;
    }
  }

  /**
   * 评估时间风险
   */
  private async assessTimeRisk(userId?: string): Promise<RiskFactor | null> {
    if (!userId) return null;

    try {
      const now = new Date();
      const hour = now.getHours();
      const dayOfWeek = now.getDay();

      // 获取用户的典型访问模式
      const accessPattern = await this.getUserAccessPattern(userId);
      
      let riskScore = 0;
      const details: Record<string, any> = {
        currentHour: hour,
        currentDay: dayOfWeek,
        accessPattern
      };

      // 检查是否在典型时间范围内
      if (accessPattern.typicalHours && !accessPattern.typicalHours.includes(hour)) {
        riskScore += 20;
        details.unusualHour = true;
      }

      // 检查是否在典型工作日
      if (accessPattern.typicalDays && !accessPattern.typicalDays.includes(dayOfWeek)) {
        riskScore += 15;
        details.unusualDay = true;
      }

      // 深夜访问（凌晨2-6点）
      if (hour >= 2 && hour <= 6) {
        riskScore += 10;
        details.lateNightAccess = true;
      }

      return {
        type: RiskFactorType.TIME,
        name: '时间风险',
        score: Math.min(riskScore, 100),
        weight: 0.1,
        confidence: accessPattern.confidence || 0.5,
        details,
        timestamp: new Date()
      };

    } catch (error) {
      logger.warn('时间风险评估失败', { error: error.message, userId });
      return null;
    }
  }

  /**
   * 评估认证风险
   */
  private async assessAuthenticationRisk(req: Request, userId?: string): Promise<RiskFactor | null> {
    try {
      let riskScore = 0;
      const details: Record<string, any> = {};

      // 检查认证方法强度
      const authMethods = await this.getActiveAuthMethods(userId);
      details.authMethods = authMethods;

      if (!authMethods.includes('mfa')) {
        riskScore += 30;
        details.noMFA = true;
      }

      if (authMethods.includes('password_only')) {
        riskScore += 20;
        details.passwordOnly = true;
      }

      // 检查会话安全性
      const sessionSecurity = this.assessSessionSecurity(req);
      riskScore += sessionSecurity.riskScore;
      details.sessionSecurity = sessionSecurity;

      // 检查最近的认证失败
      if (userId) {
        const recentFailures = await this.getRecentAuthFailures(userId);
        if (recentFailures.length > 3) {
          riskScore += recentFailures.length * 5;
          details.recentFailures = recentFailures.length;
        }
      }

      return {
        type: RiskFactorType.AUTHENTICATION,
        name: '认证风险',
        score: Math.min(riskScore, 100),
        weight: 0.15,
        confidence: 0.9,
        details,
        timestamp: new Date()
      };

    } catch (error) {
      logger.warn('认证风险评估失败', { error: error.message, userId });
      return null;
    }
  }

  /**
   * 评估信誉风险
   */
  private async assessReputationRisk(ipAddress: string, userAgent: string): Promise<RiskFactor | null> {
    try {
      let riskScore = 0;
      const details: Record<string, any> = {};

      // 检查用户代理信誉
      const uaReputation = this.assessUserAgentReputation(userAgent);
      riskScore += uaReputation.riskScore;
      details.userAgentReputation = uaReputation;

      // 检查威胁情报
      const threatIntel = await this.checkThreatIntelligence(ipAddress);
      riskScore += threatIntel.riskScore;
      details.threatIntelligence = threatIntel;

      // 检查历史违规记录
      const violationHistory = await this.getViolationHistory(ipAddress);
      if (violationHistory.length > 0) {
        riskScore += violationHistory.length * 15;
        details.violationHistory = violationHistory;
      }

      return {
        type: RiskFactorType.REPUTATION,
        name: '信誉风险',
        score: Math.min(riskScore, 100),
        weight: 0.1,
        confidence: 0.7,
        details,
        timestamp: new Date()
      };

    } catch (error) {
      logger.warn('信誉风险评估失败', { error: error.message, ipAddress });
      return null;
    }
  }

  /**
   * 计算综合风险评分
   */
  private calculateOverallRisk(riskFactors: RiskFactor[]): number {
    if (riskFactors.length === 0) return 50; // 默认中等风险

    let weightedSum = 0;
    let totalWeight = 0;

    for (const factor of riskFactors) {
      const adjustedScore = factor.score * factor.confidence;
      weightedSum += adjustedScore * factor.weight;
      totalWeight += factor.weight;
    }

    return totalWeight > 0 ? Math.round(weightedSum / totalWeight) : 50;
  }

  /**
   * 确定风险级别
   */
  private determineRiskLevel(riskScore: number): RiskLevel {
    if (riskScore >= 90) return RiskLevel.CRITICAL;
    if (riskScore >= 75) return RiskLevel.VERY_HIGH;
    if (riskScore >= 60) return RiskLevel.HIGH;
    if (riskScore >= 40) return RiskLevel.MEDIUM;
    if (riskScore >= 20) return RiskLevel.LOW;
    return RiskLevel.VERY_LOW;
  }

  /**
   * 计算置信度
   */
  private calculateConfidence(riskFactors: RiskFactor[]): number {
    if (riskFactors.length === 0) return 0.5;

    const avgConfidence = riskFactors.reduce((sum, factor) => sum + factor.confidence, 0) / riskFactors.length;
    const factorBonus = Math.min(riskFactors.length * 0.1, 0.3); // 因子数量奖励
    
    return Math.min(avgConfidence + factorBonus, 1.0);
  }

  /**
   * 生成建议
   */
  private generateRecommendations(riskFactors: RiskFactor[], riskLevel: RiskLevel): string[] {
    const recommendations = new Set<string>();

    // 基于风险级别的通用建议
    if (riskLevel === RiskLevel.CRITICAL || riskLevel === RiskLevel.VERY_HIGH) {
      recommendations.add('立即阻止访问并进行人工审查');
      recommendations.add('启动安全事件响应流程');
    } else if (riskLevel === RiskLevel.HIGH) {
      recommendations.add('要求额外的身份验证');
      recommendations.add('限制敏感操作权限');
    } else if (riskLevel === RiskLevel.MEDIUM) {
      recommendations.add('增强监控和日志记录');
      recommendations.add('考虑要求多因素认证');
    }

    // 基于具体风险因子的建议
    for (const factor of riskFactors) {
      if (factor.score > 60) {
        switch (factor.type) {
          case RiskFactorType.LOCATION:
            recommendations.add('验证用户地理位置变化');
            break;
          case RiskFactorType.DEVICE:
            recommendations.add('要求设备验证或注册');
            break;
          case RiskFactorType.BEHAVIOR:
            recommendations.add('分析用户行为模式异常');
            break;
          case RiskFactorType.NETWORK:
            recommendations.add('检查网络连接安全性');
            break;
          case RiskFactorType.AUTHENTICATION:
            recommendations.add('强化认证要求');
            break;
        }
      }
    }

    return Array.from(recommendations);
  }

  /**
   * 生成必需操作
   */
  private generateRequiredActions(riskLevel: RiskLevel, riskFactors: RiskFactor[]): string[] {
    const actions: string[] = [];

    switch (riskLevel) {
      case RiskLevel.CRITICAL:
        actions.push('BLOCK_ACCESS');
        actions.push('TRIGGER_SECURITY_ALERT');
        actions.push('REQUIRE_ADMIN_REVIEW');
        break;
      case RiskLevel.VERY_HIGH:
        actions.push('REQUIRE_STEP_UP_AUTH');
        actions.push('LIMIT_SESSION_DURATION');
        actions.push('ENHANCED_MONITORING');
        break;
      case RiskLevel.HIGH:
        actions.push('REQUIRE_MFA');
        actions.push('RESTRICT_SENSITIVE_OPERATIONS');
        break;
      case RiskLevel.MEDIUM:
        actions.push('ENHANCED_LOGGING');
        actions.push('PERIODIC_REAUTH');
        break;
    }

    // 基于特定风险因子的操作
    const highRiskFactors = riskFactors.filter(f => f.score > 70);
    if (highRiskFactors.some(f => f.type === RiskFactorType.DEVICE)) {
      actions.push('DEVICE_VERIFICATION');
    }
    if (highRiskFactors.some(f => f.type === RiskFactorType.LOCATION)) {
      actions.push('LOCATION_VERIFICATION');
    }

    return actions;
  }

  /**
   * 生成设备指纹
   */
  private async generateDeviceFingerprint(req: Request): Promise<DeviceFingerprint> {
    const userAgent = req.get('User-Agent') || '';
    const acceptLanguage = req.get('Accept-Language') || '';
    const acceptEncoding = req.get('Accept-Encoding') || '';
    
    // 从请求头提取设备信息
    const components = {
      userAgent,
      screen: req.get('X-Screen-Resolution') || 'unknown',
      timezone: req.get('X-Timezone') || 'unknown',
      language: acceptLanguage,
      platform: this.extractPlatform(userAgent),
      plugins: this.extractPlugins(userAgent),
      fonts: [], // 需要客户端JavaScript支持
      canvas: req.get('X-Canvas-Fingerprint'),
      webgl: req.get('X-WebGL-Fingerprint')
    };

    // 生成指纹哈希
    const fingerprintData = JSON.stringify(components);
    const fingerprint = crypto.createHash('sha256').update(fingerprintData).digest('hex');

    return {
      id: `fp_${Date.now()}_${crypto.randomBytes(4).toString('hex')}`,
      fingerprint,
      components,
      trustScore: 50, // 新设备默认中等信任
      firstSeen: new Date(),
      lastSeen: new Date(),
      seenCount: 1,
      isBlacklisted: false
    };
  }

  /**
   * 创建故障安全结果
   */
  private createFailsafeResult(req: Request, userId?: string): RiskAssessmentResult {
    return {
      userId,
      sessionId: req.sessionID,
      ipAddress: req.ip || 'unknown',
      userAgent: req.get('User-Agent') || 'unknown',
      overallRiskScore: 80, // 高风险作为安全默认值
      riskLevel: RiskLevel.HIGH,
      riskFactors: [],
      recommendations: ['系统错误，建议人工审查'],
      requiredActions: ['REQUIRE_MANUAL_REVIEW'],
      confidence: 0.1,
      timestamp: new Date(),
      expiresAt: new Date(Date.now() + 60 * 1000) // 1分钟过期
    };
  }

  // 辅助方法（简化实现）
  private generateCacheKey(userId?: string, ipAddress?: string, userAgent?: string): string {
    const data = `${userId || 'anon'}_${ipAddress}_${userAgent}`;
    return crypto.createHash('md5').update(data).digest('hex');
  }

  private async getCachedAssessment(key: string): Promise<RiskAssessmentResult | null> {
    return this.riskCache.get(key) || null;
  }

  private async cacheAssessment(key: string, result: RiskAssessmentResult): Promise<void> {
    this.riskCache.set(key, result);
    // 清理过期缓存
    setTimeout(() => this.riskCache.delete(key), 5 * 60 * 1000);
  }

  private isExpired(result: RiskAssessmentResult): boolean {
    return new Date() > result.expiresAt;
  }

  private async getGeoLocation(ipAddress: string): Promise<GeoLocation> {
    // 简化实现，实际应该调用地理位置API
    return {
      ip: ipAddress,
      country: 'US',
      region: 'California',
      city: 'San Francisco',
      latitude: 37.7749,
      longitude: -122.4194,
      isp: 'Example ISP',
      organization: 'Example Org',
      isVpn: false,
      isTor: false,
      isProxy: false,
      threatLevel: 10
    };
  }

  private async getKnownDevice(fingerprint: string, userId?: string): Promise<DeviceFingerprint | null> {
    return this.deviceCache.get(fingerprint) || null;
  }

  private detectFingerprintAnomalies(fingerprint: DeviceFingerprint): string[] {
    const anomalies: string[] = [];
    
    // 检查用户代理异常
    if (!fingerprint.components.userAgent || fingerprint.components.userAgent.length < 10) {
      anomalies.push('异常的用户代理');
    }
    
    // 检查屏幕分辨率异常
    if (fingerprint.components.screen === 'unknown') {
      anomalies.push('缺少屏幕信息');
    }
    
    return anomalies;
  }

  private extractPlatform(userAgent: string): string {
    if (userAgent.includes('Windows')) return 'Windows';
    if (userAgent.includes('Mac')) return 'macOS';
    if (userAgent.includes('Linux')) return 'Linux';
    if (userAgent.includes('Android')) return 'Android';
    if (userAgent.includes('iOS')) return 'iOS';
    return 'Unknown';
  }

  private extractPlugins(userAgent: string): string[] {
    const plugins: string[] = [];
    if (userAgent.includes('Chrome')) plugins.push('Chrome');
    if (userAgent.includes('Firefox')) plugins.push('Firefox');
    if (userAgent.includes('Safari')) plugins.push('Safari');
    return plugins;
  }

  // 数据库查询方法（简化实现）
  private async getRecentLogins(userId: string, since: Date) {
    return await prisma.auditLog.findMany({
      where: {
        userId,
        eventType: 'login_success',
        createdAt: { gte: since }
      }
    });
  }

  private async getRecentActions(userId: string, since: Date) {
    return await prisma.auditLog.findMany({
      where: {
        userId,
        createdAt: { gte: since }
      }
    });
  }

  private async getFailedAttempts(userId: string, since: Date) {
    return await prisma.auditLog.findMany({
      where: {
        userId,
        success: false,
        createdAt: { gte: since }
      }
    });
  }

  private async analyzeBehaviorPattern(userId: string, actions: any[]) {
    // 简化的行为模式分析
    return {
      isAnomalous: actions.length > 500,
      confidence: 0.7,
      details: { actionCount: actions.length }
    };
  }

  private async checkIPReputation(ipAddress: string) {
    // 简化的IP信誉检查
    return {
      riskScore: Math.random() * 30, // 0-30的随机风险评分
      sources: ['internal'],
      lastUpdated: new Date()
    };
  }

  private async isIPBlacklisted(ipAddress: string): Promise<boolean> {
    // 检查IP黑名单
    return false; // 简化实现
  }

  private async getRecentMaliciousActivity(ipAddress: string) {
    return []; // 简化实现
  }

  private async getUserAccessPattern(userId: string) {
    // 简化的用户访问模式分析
    return {
      typicalHours: [9, 10, 11, 14, 15, 16, 17],
      typicalDays: [1, 2, 3, 4, 5], // 工作日
      confidence: 0.8
    };
  }

  private async getActiveAuthMethods(userId?: string): Promise<string[]> {
    if (!userId) return ['password_only'];
    // 简化实现
    return ['password', 'mfa'];
  }

  private assessSessionSecurity(req: Request) {
    let riskScore = 0;
    const details: Record<string, any> = {};

    // 检查HTTPS
    if (!req.secure && req.get('X-Forwarded-Proto') !== 'https') {
      riskScore += 20;
      details.noHttps = true;
    }

    // 检查会话Cookie
    if (!req.sessionID) {
      riskScore += 15;
      details.noSession = true;
    }

    return { riskScore, details };
  }

  private async getRecentAuthFailures(userId: string) {
    return await prisma.auditLog.findMany({
      where: {
        userId,
        eventType: 'login_failed',
        createdAt: { gte: new Date(Date.now() - 24 * 60 * 60 * 1000) }
      }
    });
  }

  private assessUserAgentReputation(userAgent: string) {
    let riskScore = 0;
    const details: Record<string, any> = { userAgent };

    // 检查可疑的用户代理
    const suspiciousPatterns = [
      /bot/i, /crawler/i, /spider/i, /scraper/i,
      /curl/i, /wget/i, /python/i, /java/i
    ];

    if (suspiciousPatterns.some(pattern => pattern.test(userAgent))) {
      riskScore += 30;
      details.suspicious = true;
    }

    // 检查用户代理长度
    if (userAgent.length < 20) {
      riskScore += 15;
      details.tooShort = true;
    }

    return { riskScore, details };
  }

  private async checkThreatIntelligence(ipAddress: string) {
    // 简化的威胁情报检查
    return {
      riskScore: Math.random() * 20,
      sources: ['internal'],
      threats: []
    };
  }

  private async getViolationHistory(ipAddress: string) {
    return await prisma.auditLog.findMany({
      where: {
        ipAddress,
        eventType: 'security_violation',
        createdAt: { gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) } // 30天内
      }
    });
  }
}

// 创建单例实例
export const riskAssessmentService = new RiskAssessmentService();
