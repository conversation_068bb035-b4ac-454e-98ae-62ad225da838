/**
 * 权限监控和审计服务
 * 实现权限使用追踪、异常行为告警和合规性检查
 */

import { prisma } from '@/config/database';
import { logger } from '@/config/logger';
import { enhancedCacheService } from './enhanced-cache.service';
import { realTimeMonitorService } from './real-time-monitor.service';
import { v4 as uuidv4 } from 'uuid';

/**
 * 权限使用记录接口
 */
export interface PermissionUsageRecord {
  id: string;
  userId: string;
  resourceType: string;
  resourceId: string;
  action: string;
  result: 'granted' | 'denied';
  reason: string;
  context: {
    ipAddress?: string;
    userAgent?: string;
    sessionId?: string;
    timestamp: Date;
    location?: string;
  };
  metadata?: Record<string, any>;
  createdAt: Date;
}

/**
 * 权限异常类型枚举
 */
export enum PermissionAnomalyType {
  PRIVILEGE_ESCALATION = 'privilege_escalation',
  UNUSUAL_ACCESS_PATTERN = 'unusual_access_pattern',
  BULK_PERMISSION_CHANGES = 'bulk_permission_changes',
  SUSPICIOUS_LOCATION = 'suspicious_location',
  OFF_HOURS_ACCESS = 'off_hours_access',
  RAPID_PERMISSION_REQUESTS = 'rapid_permission_requests',
  UNAUTHORIZED_RESOURCE_ACCESS = 'unauthorized_resource_access'
}

/**
 * 权限异常接口
 */
export interface PermissionAnomaly {
  id: string;
  type: PermissionAnomalyType;
  severity: 'low' | 'medium' | 'high' | 'critical';
  userId: string;
  description: string;
  evidence: Record<string, any>;
  detectedAt: Date;
  resolvedAt?: Date;
  resolvedBy?: string;
  resolution?: string;
  falsePositive: boolean;
}

/**
 * 合规性检查规则接口
 */
export interface ComplianceRule {
  id: string;
  name: string;
  description: string;
  category: 'access_control' | 'data_protection' | 'audit_trail' | 'segregation_of_duties';
  severity: 'low' | 'medium' | 'high' | 'critical';
  isActive: boolean;
  checkFunction: string; // 检查函数名称
  parameters: Record<string, any>;
  schedule: string; // cron表达式
  lastChecked?: Date;
  nextCheck?: Date;
}

/**
 * 合规性检查结果接口
 */
export interface ComplianceCheckResult {
  id: string;
  ruleId: string;
  status: 'passed' | 'failed' | 'warning';
  message: string;
  details: Record<string, any>;
  checkedAt: Date;
  affectedEntities: string[];
  recommendations?: string[];
}

export class PermissionMonitoringService {
  private anomalyDetectors: Map<PermissionAnomalyType, Function> = new Map();
  private complianceCheckers: Map<string, Function> = new Map();

  constructor() {
    this.initializeAnomalyDetectors();
    this.initializeComplianceCheckers();
  }

  /**
   * 记录权限使用
   */
  async recordPermissionUsage(
    userId: string,
    resourceType: string,
    resourceId: string,
    action: string,
    result: 'granted' | 'denied',
    reason: string,
    context: any
  ): Promise<void> {
    try {
      const record: PermissionUsageRecord = {
        id: uuidv4(),
        userId,
        resourceType,
        resourceId,
        action,
        result,
        reason,
        context: {
          ...context,
          timestamp: new Date()
        },
        createdAt: new Date()
      };

      // 保存到数据库
      await prisma.permissionUsageLog.create({
        data: {
          id: record.id,
          userId: record.userId,
          resourceType: record.resourceType,
          resourceId: record.resourceId,
          action: record.action,
          result: record.result,
          reason: record.reason,
          context: record.context,
          metadata: record.metadata || {}
        }
      });

      // 实时异常检测
      await this.detectAnomalies(record);

      // 更新使用统计
      await this.updateUsageStatistics(record);

      logger.debug('权限使用记录已保存', { 
        userId, 
        resourceType, 
        resourceId, 
        action, 
        result 
      });

    } catch (error) {
      logger.error('记录权限使用失败', { error, userId, resourceType, resourceId });
    }
  }

  /**
   * 检测权限异常
   */
  async detectAnomalies(record: PermissionUsageRecord): Promise<void> {
    try {
      for (const [type, detector] of this.anomalyDetectors) {
        try {
          const anomaly = await detector(record);
          if (anomaly) {
            await this.createAnomaly(type, anomaly);
          }
        } catch (error) {
          logger.error('异常检测器执行失败', { error, type });
        }
      }
    } catch (error) {
      logger.error('权限异常检测失败', { error });
    }
  }

  /**
   * 获取权限使用统计
   */
  async getPermissionUsageStatistics(
    timeRange: { start: Date; end: Date },
    filters?: {
      userId?: string;
      resourceType?: string;
      action?: string;
    }
  ): Promise<any> {
    try {
      const where: any = {
        createdAt: {
          gte: timeRange.start,
          lte: timeRange.end
        }
      };

      if (filters?.userId) {
        where.userId = filters.userId;
      }
      if (filters?.resourceType) {
        where.resourceType = filters.resourceType;
      }
      if (filters?.action) {
        where.action = filters.action;
      }

      const [
        totalUsage,
        grantedUsage,
        deniedUsage,
        userStats,
        resourceStats,
        actionStats
      ] = await Promise.all([
        prisma.permissionUsageLog.count({ where }),
        prisma.permissionUsageLog.count({ where: { ...where, result: 'granted' } }),
        prisma.permissionUsageLog.count({ where: { ...where, result: 'denied' } }),
        prisma.permissionUsageLog.groupBy({
          by: ['userId'],
          where,
          _count: true,
          orderBy: { _count: { userId: 'desc' } },
          take: 10
        }),
        prisma.permissionUsageLog.groupBy({
          by: ['resourceType'],
          where,
          _count: true,
          orderBy: { _count: { resourceType: 'desc' } },
          take: 10
        }),
        prisma.permissionUsageLog.groupBy({
          by: ['action'],
          where,
          _count: true,
          orderBy: { _count: { action: 'desc' } },
          take: 10
        })
      ]);

      return {
        overview: {
          total: totalUsage,
          granted: grantedUsage,
          denied: deniedUsage,
          grantRate: totalUsage > 0 ? (grantedUsage / totalUsage) * 100 : 0
        },
        topUsers: userStats,
        topResources: resourceStats,
        topActions: actionStats
      };

    } catch (error) {
      logger.error('获取权限使用统计失败', { error });
      throw error;
    }
  }

  /**
   * 获取权限异常列表
   */
  async getPermissionAnomalies(
    filters?: {
      type?: PermissionAnomalyType;
      severity?: string;
      userId?: string;
      resolved?: boolean;
      limit?: number;
      offset?: number;
    }
  ): Promise<{ anomalies: PermissionAnomaly[]; total: number }> {
    try {
      const where: any = {};

      if (filters?.type) {
        where.type = filters.type;
      }
      if (filters?.severity) {
        where.severity = filters.severity;
      }
      if (filters?.userId) {
        where.userId = filters.userId;
      }
      if (filters?.resolved !== undefined) {
        if (filters.resolved) {
          where.resolvedAt = { not: null };
        } else {
          where.resolvedAt = null;
        }
      }

      const [anomalies, total] = await Promise.all([
        prisma.permissionAnomaly.findMany({
          where,
          orderBy: { detectedAt: 'desc' },
          take: filters?.limit || 50,
          skip: filters?.offset || 0
        }),
        prisma.permissionAnomaly.count({ where })
      ]);

      return {
        anomalies: anomalies as PermissionAnomaly[],
        total
      };

    } catch (error) {
      logger.error('获取权限异常列表失败', { error });
      throw error;
    }
  }

  /**
   * 解决权限异常
   */
  async resolveAnomaly(
    anomalyId: string,
    resolvedBy: string,
    resolution: string,
    falsePositive: boolean = false
  ): Promise<void> {
    try {
      await prisma.permissionAnomaly.update({
        where: { id: anomalyId },
        data: {
          resolvedAt: new Date(),
          resolvedBy,
          resolution,
          falsePositive
        }
      });

      logger.info('权限异常已解决', { anomalyId, resolvedBy, falsePositive });

    } catch (error) {
      logger.error('解决权限异常失败', { error, anomalyId });
      throw error;
    }
  }

  /**
   * 执行合规性检查
   */
  async runComplianceChecks(ruleIds?: string[]): Promise<ComplianceCheckResult[]> {
    try {
      const where = ruleIds ? { id: { in: ruleIds } } : { isActive: true };
      
      const rules = await prisma.complianceRule.findMany({ where });
      const results: ComplianceCheckResult[] = [];

      for (const rule of rules) {
        try {
          const checker = this.complianceCheckers.get(rule.checkFunction);
          if (!checker) {
            logger.warn('合规性检查器不存在', { checkFunction: rule.checkFunction });
            continue;
          }

          const result = await checker(rule.parameters);
          
          const checkResult: ComplianceCheckResult = {
            id: uuidv4(),
            ruleId: rule.id,
            status: result.status,
            message: result.message,
            details: result.details || {},
            checkedAt: new Date(),
            affectedEntities: result.affectedEntities || [],
            recommendations: result.recommendations
          };

          // 保存检查结果
          await prisma.complianceCheckResult.create({
            data: checkResult
          });

          results.push(checkResult);

          // 更新规则的最后检查时间
          await prisma.complianceRule.update({
            where: { id: rule.id },
            data: { lastChecked: new Date() }
          });

        } catch (error) {
          logger.error('合规性检查执行失败', { error, ruleId: rule.id });
        }
      }

      logger.info('合规性检查完成', { rulesChecked: results.length });
      return results;

    } catch (error) {
      logger.error('执行合规性检查失败', { error });
      throw error;
    }
  }

  /**
   * 获取合规性报告
   */
  async getComplianceReport(timeRange?: { start: Date; end: Date }): Promise<any> {
    try {
      const where = timeRange ? {
        checkedAt: {
          gte: timeRange.start,
          lte: timeRange.end
        }
      } : {};

      const [
        totalChecks,
        passedChecks,
        failedChecks,
        warningChecks,
        ruleResults
      ] = await Promise.all([
        prisma.complianceCheckResult.count({ where }),
        prisma.complianceCheckResult.count({ where: { ...where, status: 'passed' } }),
        prisma.complianceCheckResult.count({ where: { ...where, status: 'failed' } }),
        prisma.complianceCheckResult.count({ where: { ...where, status: 'warning' } }),
        prisma.complianceCheckResult.groupBy({
          by: ['ruleId', 'status'],
          where,
          _count: true
        })
      ]);

      const complianceScore = totalChecks > 0 ? (passedChecks / totalChecks) * 100 : 100;

      return {
        overview: {
          totalChecks,
          passedChecks,
          failedChecks,
          warningChecks,
          complianceScore
        },
        ruleResults,
        timeRange
      };

    } catch (error) {
      logger.error('获取合规性报告失败', { error });
      throw error;
    }
  }

  /**
   * 初始化异常检测器
   */
  private initializeAnomalyDetectors(): void {
    // 权限提升检测
    this.anomalyDetectors.set(
      PermissionAnomalyType.PRIVILEGE_ESCALATION,
      this.detectPrivilegeEscalation.bind(this)
    );

    // 异常访问模式检测
    this.anomalyDetectors.set(
      PermissionAnomalyType.UNUSUAL_ACCESS_PATTERN,
      this.detectUnusualAccessPattern.bind(this)
    );

    // 批量权限变更检测
    this.anomalyDetectors.set(
      PermissionAnomalyType.BULK_PERMISSION_CHANGES,
      this.detectBulkPermissionChanges.bind(this)
    );

    // 可疑位置访问检测
    this.anomalyDetectors.set(
      PermissionAnomalyType.SUSPICIOUS_LOCATION,
      this.detectSuspiciousLocation.bind(this)
    );

    // 非工作时间访问检测
    this.anomalyDetectors.set(
      PermissionAnomalyType.OFF_HOURS_ACCESS,
      this.detectOffHoursAccess.bind(this)
    );
  }

  /**
   * 初始化合规性检查器
   */
  private initializeComplianceCheckers(): void {
    this.complianceCheckers.set('checkAccessControlCompliance', this.checkAccessControlCompliance.bind(this));
    this.complianceCheckers.set('checkDataProtectionCompliance', this.checkDataProtectionCompliance.bind(this));
    this.complianceCheckers.set('checkAuditTrailCompliance', this.checkAuditTrailCompliance.bind(this));
    this.complianceCheckers.set('checkSegregationOfDuties', this.checkSegregationOfDuties.bind(this));
  }

  /**
   * 检测权限提升
   */
  private async detectPrivilegeEscalation(record: PermissionUsageRecord): Promise<any> {
    // 检查用户是否获得了比平时更高的权限
    // 这里实现具体的检测逻辑
    return null;
  }

  /**
   * 检测异常访问模式
   */
  private async detectUnusualAccessPattern(record: PermissionUsageRecord): Promise<any> {
    // 检查用户的访问模式是否异常
    // 这里实现具体的检测逻辑
    return null;
  }

  /**
   * 检测批量权限变更
   */
  private async detectBulkPermissionChanges(record: PermissionUsageRecord): Promise<any> {
    // 检查是否有大量权限变更
    // 这里实现具体的检测逻辑
    return null;
  }

  /**
   * 检测可疑位置访问
   */
  private async detectSuspiciousLocation(record: PermissionUsageRecord): Promise<any> {
    // 检查访问位置是否可疑
    // 这里实现具体的检测逻辑
    return null;
  }

  /**
   * 检测非工作时间访问
   */
  private async detectOffHoursAccess(record: PermissionUsageRecord): Promise<any> {
    // 检查是否在非工作时间访问
    const accessTime = record.context.timestamp;
    const hour = accessTime.getHours();
    
    // 假设工作时间是9:00-18:00
    if (hour < 9 || hour > 18) {
      return {
        severity: 'medium',
        description: '在非工作时间访问系统资源',
        evidence: {
          accessTime: accessTime.toISOString(),
          hour
        }
      };
    }
    
    return null;
  }

  /**
   * 创建异常记录
   */
  private async createAnomaly(type: PermissionAnomalyType, anomalyData: any): Promise<void> {
    try {
      const anomaly: PermissionAnomaly = {
        id: uuidv4(),
        type,
        severity: anomalyData.severity,
        userId: anomalyData.userId,
        description: anomalyData.description,
        evidence: anomalyData.evidence,
        detectedAt: new Date(),
        falsePositive: false
      };

      await prisma.permissionAnomaly.create({
        data: anomaly
      });

      // 发送告警
      await this.sendAnomalyAlert(anomaly);

      logger.warn('检测到权限异常', { anomaly });

    } catch (error) {
      logger.error('创建异常记录失败', { error, type });
    }
  }

  /**
   * 发送异常告警
   */
  private async sendAnomalyAlert(anomaly: PermissionAnomaly): Promise<void> {
    try {
      // 这里可以实现告警通知逻辑
      // 例如：发送邮件、Slack消息、WebSocket通知等
      logger.info('权限异常告警已发送', { anomalyId: anomaly.id });
    } catch (error) {
      logger.error('发送异常告警失败', { error, anomalyId: anomaly.id });
    }
  }

  /**
   * 更新使用统计
   */
  private async updateUsageStatistics(record: PermissionUsageRecord): Promise<void> {
    try {
      const cacheKey = `permission_stats:${record.userId}:${new Date().toDateString()}`;
      
      // 更新用户当日权限使用统计
      const stats = await enhancedCacheService.get(cacheKey) || {
        total: 0,
        granted: 0,
        denied: 0
      };

      stats.total++;
      if (record.result === 'granted') {
        stats.granted++;
      } else {
        stats.denied++;
      }

      await enhancedCacheService.set(cacheKey, stats, 86400); // 缓存24小时

    } catch (error) {
      logger.error('更新使用统计失败', { error });
    }
  }

  /**
   * 访问控制合规性检查
   */
  private async checkAccessControlCompliance(parameters: any): Promise<any> {
    // 实现访问控制合规性检查逻辑
    return {
      status: 'passed',
      message: '访问控制合规性检查通过',
      details: {},
      affectedEntities: []
    };
  }

  /**
   * 数据保护合规性检查
   */
  private async checkDataProtectionCompliance(parameters: any): Promise<any> {
    // 实现数据保护合规性检查逻辑
    return {
      status: 'passed',
      message: '数据保护合规性检查通过',
      details: {},
      affectedEntities: []
    };
  }

  /**
   * 审计跟踪合规性检查
   */
  private async checkAuditTrailCompliance(parameters: any): Promise<any> {
    // 实现审计跟踪合规性检查逻辑
    return {
      status: 'passed',
      message: '审计跟踪合规性检查通过',
      details: {},
      affectedEntities: []
    };
  }

  /**
   * 职责分离合规性检查
   */
  private async checkSegregationOfDuties(parameters: any): Promise<any> {
    // 实现职责分离合规性检查逻辑
    return {
      status: 'passed',
      message: '职责分离合规性检查通过',
      details: {},
      affectedEntities: []
    };
  }
}

// 导出单例实例
export const permissionMonitoringService = new PermissionMonitoringService();
