/**
 * 权限申请工作流服务
 * 实现跨组织权限申请、审批和管理功能
 */

import { prisma } from '@/config/database';
import { logger } from '@/config/logger';
import { cacheService } from './cache.service';
import { organizationService } from './organization.service';
import { organizationPermissionService } from './organization-permission.service';

/**
 * 权限申请接口定义
 */
export interface PermissionRequest {
  id: string;
  requesterId: string;
  targetUserId?: string;
  targetOrganizationId: string;
  requestedPermissions: string[];
  requestType: PermissionRequestType;
  reason: string;
  businessJustification?: string;
  status: PermissionRequestStatus;
  priority: PermissionRequestPriority;
  requestedDuration?: number; // 小时
  expiresAt?: Date;
  approvedBy?: string;
  approvedAt?: Date;
  rejectedBy?: string;
  rejectedAt?: Date;
  rejectionReason?: string;
  createdAt: Date;
  updatedAt: Date;
}

export type PermissionRequestType = 'temporary' | 'project' | 'data_sharing' | 'delegation';
export type PermissionRequestStatus = 'pending' | 'approved' | 'rejected' | 'expired' | 'revoked';
export type PermissionRequestPriority = 'low' | 'normal' | 'high' | 'urgent';

/**
 * 权限申请创建参数
 */
export interface CreatePermissionRequestParams {
  targetUserId?: string;
  targetOrganizationId: string;
  requestedPermissions: string[];
  requestType: PermissionRequestType;
  reason: string;
  businessJustification?: string;
  priority?: PermissionRequestPriority;
  requestedDuration?: number;
}

/**
 * 权限申请审批参数
 */
export interface ApprovePermissionRequestParams {
  requestId: string;
  approverId: string;
  approvedPermissions?: string[]; // 可以部分批准
  approvedDuration?: number;
  conditions?: Record<string, any>;
  comments?: string;
}

/**
 * 权限申请拒绝参数
 */
export interface RejectPermissionRequestParams {
  requestId: string;
  rejectorId: string;
  reason: string;
  comments?: string;
}

/**
 * 权限申请工作流服务类
 */
export class PermissionRequestService {
  private readonly CACHE_TTL = 300; // 5分钟缓存
  private readonly DEFAULT_REQUEST_DURATION = 24 * 7; // 默认7天（小时）
  private readonly MAX_REQUEST_DURATION = 24 * 30; // 最大30天（小时）

  /**
   * 创建权限申请
   */
  async createPermissionRequest(
    requesterId: string,
    params: CreatePermissionRequestParams
  ): Promise<PermissionRequest> {
    try {
      // 验证申请参数
      await this.validatePermissionRequest(requesterId, params);

      // 计算过期时间
      const requestedDuration = Math.min(
        params.requestedDuration || this.DEFAULT_REQUEST_DURATION,
        this.MAX_REQUEST_DURATION
      );
      const expiresAt = new Date();
      expiresAt.setHours(expiresAt.getHours() + requestedDuration);

      // 创建权限申请记录
      const request = await prisma.permissionRequest.create({
        data: {
          requesterId,
          targetUserId: params.targetUserId,
          targetOrganizationId: params.targetOrganizationId,
          requestedPermissions: params.requestedPermissions,
          requestType: params.requestType,
          reason: params.reason,
          businessJustification: params.businessJustification,
          priority: params.priority || 'normal',
          requestedDuration,
          expiresAt,
          status: 'pending'
        },
        include: {
          requester: {
            select: {
              id: true,
              email: true,
              username: true,
              firstName: true,
              lastName: true
            }
          },
          targetOrganization: true,
          targetUser: {
            select: {
              id: true,
              email: true,
              username: true,
              firstName: true,
              lastName: true
            }
          }
        }
      });

      // 发送通知给相关审批者
      await this.notifyApprovers(request);

      logger.info('权限申请创建成功', {
        requestId: request.id,
        requesterId,
        targetOrganizationId: params.targetOrganizationId,
        requestType: params.requestType,
        permissions: params.requestedPermissions
      });

      return request as PermissionRequest;
    } catch (error) {
      logger.error('创建权限申请失败', {
        requesterId,
        params,
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * 验证权限申请
   */
  private async validatePermissionRequest(
    requesterId: string,
    params: CreatePermissionRequestParams
  ): Promise<void> {
    // 检查目标组织是否存在
    const targetOrg = await organizationService.getOrganizationById(params.targetOrganizationId);
    if (!targetOrg) {
      throw new Error('目标组织不存在');
    }

    // 检查申请者是否已经拥有这些权限
    if (!params.targetUserId || params.targetUserId === requesterId) {
      const hasPermissions = await this.checkExistingPermissions(
        requesterId,
        params.targetOrganizationId,
        params.requestedPermissions
      );
      if (hasPermissions) {
        throw new Error('申请者已经拥有所申请的权限');
      }
    }

    // 检查是否有重复的待处理申请
    const existingRequest = await prisma.permissionRequest.findFirst({
      where: {
        requesterId,
        targetOrganizationId: params.targetOrganizationId,
        status: 'pending',
        targetUserId: params.targetUserId
      }
    });

    if (existingRequest) {
      throw new Error('存在相同的待处理权限申请');
    }

    // 验证申请的权限是否有效
    await this.validateRequestedPermissions(params.requestedPermissions);
  }

  /**
   * 检查现有权限
   */
  private async checkExistingPermissions(
    userId: string,
    organizationId: string,
    requestedPermissions: string[]
  ): Promise<boolean> {
    try {
      const organization = await organizationService.getOrganizationById(organizationId);
      if (!organization) return false;

      const effectivePermissions = await organizationPermissionService.resolveUserPermissions(
        userId,
        organization.path
      );

      return requestedPermissions.every(permission => 
        effectivePermissions.permissions.includes(permission)
      );
    } catch (error) {
      logger.error('检查现有权限失败', { userId, organizationId, error });
      return false;
    }
  }

  /**
   * 验证申请的权限
   */
  private async validateRequestedPermissions(permissions: string[]): Promise<void> {
    // 检查权限格式和有效性
    for (const permission of permissions) {
      if (!permission || typeof permission !== 'string') {
        throw new Error(`无效的权限格式: ${permission}`);
      }

      // 检查权限是否存在于系统中
      const existingPermission = await prisma.permission.findFirst({
        where: { name: permission }
      });

      if (!existingPermission) {
        throw new Error(`权限不存在: ${permission}`);
      }
    }
  }

  /**
   * 审批权限申请
   */
  async approvePermissionRequest(
    params: ApprovePermissionRequestParams
  ): Promise<PermissionRequest> {
    try {
      // 获取申请记录
      const request = await this.getPermissionRequestById(params.requestId);
      if (!request) {
        throw new Error('权限申请不存在');
      }

      if (request.status !== 'pending') {
        throw new Error(`权限申请状态无效: ${request.status}`);
      }

      // 验证审批者权限
      await this.validateApproverPermission(params.approverId, request);

      // 确定最终批准的权限
      const approvedPermissions = params.approvedPermissions || request.requestedPermissions;
      const approvedDuration = Math.min(
        params.approvedDuration || request.requestedDuration || this.DEFAULT_REQUEST_DURATION,
        this.MAX_REQUEST_DURATION
      );

      // 更新申请状态
      const updatedRequest = await prisma.permissionRequest.update({
        where: { id: params.requestId },
        data: {
          status: 'approved',
          approvedBy: params.approverId,
          approvedAt: new Date()
        },
        include: {
          requester: true,
          targetOrganization: true,
          targetUser: true
        }
      });

      // 实际授予权限
      await this.grantApprovedPermissions(
        updatedRequest,
        approvedPermissions,
        approvedDuration,
        params.conditions
      );

      // 发送通知
      await this.notifyRequestApproval(updatedRequest, params.approverId);

      logger.info('权限申请审批成功', {
        requestId: params.requestId,
        approverId: params.approverId,
        approvedPermissions,
        approvedDuration
      });

      return updatedRequest as PermissionRequest;
    } catch (error) {
      logger.error('审批权限申请失败', {
        params,
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * 验证审批者权限
   */
  private async validateApproverPermission(
    approverId: string,
    request: any
  ): Promise<void> {
    // 检查审批者是否有权限审批该申请
    const hasPermission = await organizationPermissionService.validatePermission({
      userId: approverId,
      organizationId: request.targetOrganizationId,
      action: 'approve:permission_request'
    });

    if (!hasPermission.granted) {
      throw new Error('审批者没有权限审批该申请');
    }
  }

  /**
   * 授予已批准的权限
   */
  private async grantApprovedPermissions(
    request: any,
    approvedPermissions: string[],
    duration: number,
    conditions?: Record<string, any>
  ): Promise<void> {
    try {
      const targetUserId = request.targetUserId || request.requesterId;
      const expiresAt = new Date();
      expiresAt.setHours(expiresAt.getHours() + duration);

      // 创建权限委托记录
      await prisma.permissionDelegation.create({
        data: {
          delegatorId: request.approvedBy,
          delegateeId: targetUserId,
          organizationId: request.targetOrganizationId,
          delegationType: 'permission',
          delegatedPermissions: approvedPermissions,
          conditions: conditions || {},
          effectiveUntil: expiresAt,
          status: 'active'
        }
      });

      // 清除相关权限缓存
      await this.clearPermissionCache(targetUserId);

    } catch (error) {
      logger.error('授予权限失败', {
        requestId: request.id,
        targetUserId: request.targetUserId || request.requesterId,
        approvedPermissions,
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * 拒绝权限申请
   */
  async rejectPermissionRequest(
    params: RejectPermissionRequestParams
  ): Promise<PermissionRequest> {
    try {
      // 获取申请记录
      const request = await this.getPermissionRequestById(params.requestId);
      if (!request) {
        throw new Error('权限申请不存在');
      }

      if (request.status !== 'pending') {
        throw new Error(`权限申请状态无效: ${request.status}`);
      }

      // 验证拒绝者权限
      await this.validateApproverPermission(params.rejectorId, request);

      // 更新申请状态
      const updatedRequest = await prisma.permissionRequest.update({
        where: { id: params.requestId },
        data: {
          status: 'rejected',
          rejectedBy: params.rejectorId,
          rejectedAt: new Date(),
          rejectionReason: params.reason
        },
        include: {
          requester: true,
          targetOrganization: true,
          targetUser: true
        }
      });

      // 发送通知
      await this.notifyRequestRejection(updatedRequest, params.rejectorId, params.reason);

      logger.info('权限申请拒绝成功', {
        requestId: params.requestId,
        rejectorId: params.rejectorId,
        reason: params.reason
      });

      return updatedRequest as PermissionRequest;
    } catch (error) {
      logger.error('拒绝权限申请失败', {
        params,
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * 获取权限申请详情
   */
  async getPermissionRequestById(requestId: string): Promise<PermissionRequest | null> {
    try {
      const request = await prisma.permissionRequest.findUnique({
        where: { id: requestId },
        include: {
          requester: {
            select: {
              id: true,
              email: true,
              username: true,
              firstName: true,
              lastName: true
            }
          },
          targetOrganization: true,
          targetUser: {
            select: {
              id: true,
              email: true,
              username: true,
              firstName: true,
              lastName: true
            }
          },
          approver: {
            select: {
              id: true,
              email: true,
              username: true,
              firstName: true,
              lastName: true
            }
          }
        }
      });

      return request as PermissionRequest | null;
    } catch (error) {
      logger.error('获取权限申请详情失败', {
        requestId,
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * 获取用户的权限申请列表
   */
  async getUserPermissionRequests(
    userId: string,
    options: {
      status?: PermissionRequestStatus;
      type?: PermissionRequestType;
      page?: number;
      limit?: number;
    } = {}
  ): Promise<{ requests: PermissionRequest[]; total: number }> {
    try {
      const { status, type, page = 1, limit = 20 } = options;
      
      const whereClause: any = {
        OR: [
          { requesterId: userId },
          { targetUserId: userId }
        ]
      };

      if (status) whereClause.status = status;
      if (type) whereClause.requestType = type;

      const [requests, total] = await Promise.all([
        prisma.permissionRequest.findMany({
          where: whereClause,
          include: {
            requester: {
              select: {
                id: true,
                email: true,
                username: true,
                firstName: true,
                lastName: true
              }
            },
            targetOrganization: true,
            targetUser: {
              select: {
                id: true,
                email: true,
                username: true,
                firstName: true,
                lastName: true
              }
            }
          },
          skip: (page - 1) * limit,
          take: limit,
          orderBy: { createdAt: 'desc' }
        }),
        prisma.permissionRequest.count({ where: whereClause })
      ]);

      return {
        requests: requests as PermissionRequest[],
        total
      };
    } catch (error) {
      logger.error('获取用户权限申请列表失败', {
        userId,
        options,
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * 通知审批者
   */
  private async notifyApprovers(request: any): Promise<void> {
    // TODO: 实现通知逻辑（邮件、站内信等）
    logger.info('发送权限申请通知', {
      requestId: request.id,
      targetOrganizationId: request.targetOrganizationId
    });
  }

  /**
   * 通知申请审批结果
   */
  private async notifyRequestApproval(request: any, approverId: string): Promise<void> {
    // TODO: 实现通知逻辑
    logger.info('发送权限申请审批通知', {
      requestId: request.id,
      approverId
    });
  }

  /**
   * 通知申请拒绝结果
   */
  private async notifyRequestRejection(request: any, rejectorId: string, reason: string): Promise<void> {
    // TODO: 实现通知逻辑
    logger.info('发送权限申请拒绝通知', {
      requestId: request.id,
      rejectorId,
      reason
    });
  }

  /**
   * 清除权限缓存
   */
  private async clearPermissionCache(userId: string): Promise<void> {
    const patterns = [
      `user:orgs:${userId}`,
      `user:permissions:${userId}*`,
      `user:effective_permissions:${userId}*`
    ];

    for (const pattern of patterns) {
      await cacheService.del(pattern);
    }
  }
}

export const permissionRequestService = new PermissionRequestService();
