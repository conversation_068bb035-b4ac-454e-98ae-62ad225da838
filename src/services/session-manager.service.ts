/**
 * 会话管理器
 * 专门处理用户会话的创建、验证、更新和清理
 */

import { redisService } from './redis.service';
import { cacheManager } from './cache-manager.service';
import { logger } from '@/config/logger';
import { RedisKeys, RedisTTL } from '@/config/redis';
import crypto from 'crypto';

/**
 * 会话数据接口
 */
export interface SessionData {
  sessionId: string;
  userId: string;
  email: string;
  roles: string[];
  permissions: string[];
  deviceInfo: DeviceInfo;
  ipAddress: string;
  userAgent: string;
  createdAt: Date;
  lastAccessedAt: Date;
  expiresAt: Date;
  isActive: boolean;
  metadata?: Record<string, any>;
}

/**
 * 设备信息接口
 */
export interface DeviceInfo {
  deviceId: string;
  deviceType: 'desktop' | 'mobile' | 'tablet' | 'unknown';
  browser: string;
  os: string;
  fingerprint: string;
  isTrusted: boolean;
}

/**
 * 会话创建选项
 */
export interface SessionCreateOptions {
  ttl?: number;
  rememberMe?: boolean;
  deviceTrust?: boolean;
  metadata?: Record<string, any>;
}

/**
 * 会话查询选项
 */
export interface SessionQueryOptions {
  includeExpired?: boolean;
  sortBy?: 'createdAt' | 'lastAccessedAt';
  sortOrder?: 'asc' | 'desc';
  limit?: number;
  offset?: number;
}

/**
 * 会话统计信息
 */
export interface SessionStats {
  totalSessions: number;
  activeSessions: number;
  expiredSessions: number;
  userSessionCounts: Record<string, number>;
  deviceTypeCounts: Record<string, number>;
  avgSessionDuration: number;
}

/**
 * 会话管理器
 */
export class SessionManager {
  private readonly maxSessionsPerUser = 10; // 每个用户最大会话数
  private readonly sessionCleanupInterval = 60 * 60 * 1000; // 1小时清理一次
  private cleanupTimer?: NodeJS.Timeout;

  constructor() {
    this.startSessionCleanup();
  }

  /**
   * 创建新会话
   */
  async createSession(
    userId: string,
    email: string,
    roles: string[],
    permissions: string[],
    deviceInfo: DeviceInfo,
    ipAddress: string,
    userAgent: string,
    options: SessionCreateOptions = {}
  ): Promise<SessionData> {
    const sessionId = this.generateSessionId();
    const now = new Date();
    const ttl = options.rememberMe ? RedisTTL.USER_SESSIONS : RedisTTL.SESSION;
    const expiresAt = new Date(now.getTime() + ttl * 1000);

    const sessionData: SessionData = {
      sessionId,
      userId,
      email,
      roles,
      permissions,
      deviceInfo,
      ipAddress,
      userAgent,
      createdAt: now,
      lastAccessedAt: now,
      expiresAt,
      isActive: true,
      metadata: options.metadata || {}
    };

    try {
      // 检查用户会话数量限制
      await this.enforceSessionLimit(userId);

      // 存储会话数据
      const sessionKey = RedisKeys.SESSION(sessionId);
      await redisService.set(sessionKey, sessionData, ttl);

      // 添加到用户会话列表
      await this.addToUserSessions(userId, sessionId, ttl);

      // 如果是受信任设备，缓存设备信息
      if (options.deviceTrust && deviceInfo.isTrusted) {
        await this.cacheDeviceInfo(deviceInfo.deviceId, deviceInfo);
      }

      // 记录会话创建事件
      await this.recordSessionEvent(sessionId, 'created', {
        userId,
        deviceType: deviceInfo.deviceType,
        ipAddress
      });

      logger.info('会话创建成功', {
        sessionId,
        userId,
        deviceType: deviceInfo.deviceType,
        rememberMe: options.rememberMe,
        ttl
      });

      return sessionData;

    } catch (error) {
      logger.error('会话创建失败', {
        userId,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * 获取会话数据
   */
  async getSession(sessionId: string): Promise<SessionData | null> {
    try {
      const sessionKey = RedisKeys.SESSION(sessionId);
      const sessionData = await redisService.get<SessionData>(sessionKey);

      if (!sessionData) {
        return null;
      }

      // 检查会话是否过期
      if (new Date() > new Date(sessionData.expiresAt)) {
        await this.destroySession(sessionId);
        return null;
      }

      // 更新最后访问时间
      await this.updateLastAccessed(sessionId);

      return sessionData;

    } catch (error) {
      logger.error('获取会话失败', {
        sessionId,
        error: error.message
      });
      return null;
    }
  }

  /**
   * 验证会话
   */
  async validateSession(sessionId: string, ipAddress?: string): Promise<SessionData | null> {
    const sessionData = await this.getSession(sessionId);

    if (!sessionData) {
      return null;
    }

    // IP地址验证（可选）
    if (ipAddress && sessionData.ipAddress !== ipAddress) {
      logger.warn('会话IP地址不匹配', {
        sessionId,
        expectedIp: sessionData.ipAddress,
        actualIp: ipAddress
      });
      
      // 可以选择是否严格验证IP
      // return null;
    }

    // 检查会话是否活跃
    if (!sessionData.isActive) {
      return null;
    }

    return sessionData;
  }

  /**
   * 更新会话数据
   */
  async updateSession(
    sessionId: string,
    updates: Partial<SessionData>
  ): Promise<boolean> {
    try {
      const sessionData = await this.getSession(sessionId);
      if (!sessionData) {
        return false;
      }

      const updatedSession = {
        ...sessionData,
        ...updates,
        lastAccessedAt: new Date()
      };

      const sessionKey = RedisKeys.SESSION(sessionId);
      const ttl = Math.max(0, Math.floor((new Date(updatedSession.expiresAt).getTime() - Date.now()) / 1000));
      
      await redisService.set(sessionKey, updatedSession, ttl);

      logger.debug('会话更新成功', { sessionId });
      return true;

    } catch (error) {
      logger.error('会话更新失败', {
        sessionId,
        error: error.message
      });
      return false;
    }
  }

  /**
   * 销毁会话
   */
  async destroySession(sessionId: string): Promise<boolean> {
    try {
      const sessionData = await this.getSession(sessionId);
      
      // 从Redis删除会话
      const sessionKey = RedisKeys.SESSION(sessionId);
      await redisService.del(sessionKey);

      // 从用户会话列表中移除
      if (sessionData) {
        await this.removeFromUserSessions(sessionData.userId, sessionId);
        
        // 记录会话销毁事件
        await this.recordSessionEvent(sessionId, 'destroyed', {
          userId: sessionData.userId
        });
      }

      logger.info('会话销毁成功', { sessionId });
      return true;

    } catch (error) {
      logger.error('会话销毁失败', {
        sessionId,
        error: error.message
      });
      return false;
    }
  }

  /**
   * 销毁用户的所有会话
   */
  async destroyUserSessions(userId: string, excludeSessionId?: string): Promise<number> {
    try {
      const userSessionsKey = RedisKeys.USER_SESSIONS(userId);
      const sessionIds = await redisService.smembers(userSessionsKey);

      let destroyedCount = 0;
      for (const sessionId of sessionIds) {
        if (sessionId !== excludeSessionId) {
          const success = await this.destroySession(sessionId);
          if (success) {
            destroyedCount++;
          }
        }
      }

      logger.info('用户会话批量销毁完成', {
        userId,
        destroyedCount,
        excludeSessionId
      });

      return destroyedCount;

    } catch (error) {
      logger.error('用户会话批量销毁失败', {
        userId,
        error: error.message
      });
      return 0;
    }
  }

  /**
   * 获取用户的所有会话
   */
  async getUserSessions(
    userId: string,
    options: SessionQueryOptions = {}
  ): Promise<SessionData[]> {
    try {
      const userSessionsKey = RedisKeys.USER_SESSIONS(userId);
      const sessionIds = await redisService.smembers(userSessionsKey);

      const sessions: SessionData[] = [];
      for (const sessionId of sessionIds) {
        const sessionData = await this.getSession(sessionId);
        if (sessionData && (options.includeExpired || sessionData.isActive)) {
          sessions.push(sessionData);
        }
      }

      // 排序
      if (options.sortBy) {
        sessions.sort((a, b) => {
          const aValue = new Date(a[options.sortBy!]).getTime();
          const bValue = new Date(b[options.sortBy!]).getTime();
          return options.sortOrder === 'desc' ? bValue - aValue : aValue - bValue;
        });
      }

      // 分页
      const start = options.offset || 0;
      const end = options.limit ? start + options.limit : undefined;
      
      return sessions.slice(start, end);

    } catch (error) {
      logger.error('获取用户会话失败', {
        userId,
        error: error.message
      });
      return [];
    }
  }

  /**
   * 获取会话统计信息
   */
  async getSessionStats(): Promise<SessionStats> {
    try {
      const client = redisService.getClient();
      const pattern = RedisKeys.SESSION('*');
      const sessionKeys = await client.keys(pattern);

      let totalSessions = 0;
      let activeSessions = 0;
      let expiredSessions = 0;
      const userSessionCounts: Record<string, number> = {};
      const deviceTypeCounts: Record<string, number> = {};
      let totalDuration = 0;

      for (const key of sessionKeys) {
        const sessionData = await redisService.get<SessionData>(key);
        if (sessionData) {
          totalSessions++;
          
          if (sessionData.isActive && new Date() <= new Date(sessionData.expiresAt)) {
            activeSessions++;
          } else {
            expiredSessions++;
          }

          // 用户会话计数
          userSessionCounts[sessionData.userId] = (userSessionCounts[sessionData.userId] || 0) + 1;

          // 设备类型计数
          const deviceType = sessionData.deviceInfo.deviceType;
          deviceTypeCounts[deviceType] = (deviceTypeCounts[deviceType] || 0) + 1;

          // 会话持续时间
          const duration = new Date(sessionData.lastAccessedAt).getTime() - new Date(sessionData.createdAt).getTime();
          totalDuration += duration;
        }
      }

      const avgSessionDuration = totalSessions > 0 ? totalDuration / totalSessions : 0;

      return {
        totalSessions,
        activeSessions,
        expiredSessions,
        userSessionCounts,
        deviceTypeCounts,
        avgSessionDuration
      };

    } catch (error) {
      logger.error('获取会话统计失败', { error: error.message });
      return {
        totalSessions: 0,
        activeSessions: 0,
        expiredSessions: 0,
        userSessionCounts: {},
        deviceTypeCounts: {},
        avgSessionDuration: 0
      };
    }
  }

  /**
   * 清理过期会话
   */
  async cleanupExpiredSessions(): Promise<number> {
    try {
      const client = redisService.getClient();
      const pattern = RedisKeys.SESSION('*');
      const sessionKeys = await client.keys(pattern);

      let cleanedCount = 0;
      const now = new Date();

      for (const key of sessionKeys) {
        const sessionData = await redisService.get<SessionData>(key);
        if (sessionData && now > new Date(sessionData.expiresAt)) {
          await this.destroySession(sessionData.sessionId);
          cleanedCount++;
        }
      }

      logger.info('过期会话清理完成', { cleanedCount });
      return cleanedCount;

    } catch (error) {
      logger.error('过期会话清理失败', { error: error.message });
      return 0;
    }
  }

  /**
   * 生成会话ID
   */
  private generateSessionId(): string {
    return crypto.randomBytes(32).toString('hex');
  }

  /**
   * 更新最后访问时间
   */
  private async updateLastAccessed(sessionId: string): Promise<void> {
    try {
      const sessionKey = RedisKeys.SESSION(sessionId);
      const sessionData = await redisService.get<SessionData>(sessionKey);
      
      if (sessionData) {
        sessionData.lastAccessedAt = new Date();
        const ttl = Math.max(0, Math.floor((new Date(sessionData.expiresAt).getTime() - Date.now()) / 1000));
        await redisService.set(sessionKey, sessionData, ttl);
      }
    } catch (error) {
      logger.error('更新最后访问时间失败', {
        sessionId,
        error: error.message
      });
    }
  }

  /**
   * 强制执行会话数量限制
   */
  private async enforceSessionLimit(userId: string): Promise<void> {
    const userSessionsKey = RedisKeys.USER_SESSIONS(userId);
    const sessionIds = await redisService.smembers(userSessionsKey);

    if (sessionIds.length >= this.maxSessionsPerUser) {
      // 获取所有会话数据并按最后访问时间排序
      const sessions: Array<{ sessionId: string; lastAccessedAt: Date }> = [];
      
      for (const sessionId of sessionIds) {
        const sessionData = await this.getSession(sessionId);
        if (sessionData) {
          sessions.push({
            sessionId,
            lastAccessedAt: new Date(sessionData.lastAccessedAt)
          });
        }
      }

      // 按最后访问时间升序排序（最旧的在前）
      sessions.sort((a, b) => a.lastAccessedAt.getTime() - b.lastAccessedAt.getTime());

      // 删除最旧的会话
      const sessionsToRemove = sessions.slice(0, sessions.length - this.maxSessionsPerUser + 1);
      for (const session of sessionsToRemove) {
        await this.destroySession(session.sessionId);
      }

      logger.info('强制执行会话限制', {
        userId,
        removedSessions: sessionsToRemove.length,
        maxSessions: this.maxSessionsPerUser
      });
    }
  }

  /**
   * 添加到用户会话列表
   */
  private async addToUserSessions(userId: string, sessionId: string, ttl: number): Promise<void> {
    const userSessionsKey = RedisKeys.USER_SESSIONS(userId);
    await redisService.sadd(userSessionsKey, sessionId);
    await redisService.expire(userSessionsKey, ttl);
  }

  /**
   * 从用户会话列表中移除
   */
  private async removeFromUserSessions(userId: string, sessionId: string): Promise<void> {
    const userSessionsKey = RedisKeys.USER_SESSIONS(userId);
    await redisService.srem(userSessionsKey, sessionId);
  }

  /**
   * 缓存设备信息
   */
  private async cacheDeviceInfo(deviceId: string, deviceInfo: DeviceInfo): Promise<void> {
    const deviceKey = RedisKeys.DEVICE_INFO(deviceId);
    await redisService.set(deviceKey, deviceInfo, RedisTTL.USER_SESSIONS);
  }

  /**
   * 记录会话事件
   */
  private async recordSessionEvent(
    sessionId: string,
    event: string,
    metadata: Record<string, any>
  ): Promise<void> {
    try {
      const eventData = {
        sessionId,
        event,
        timestamp: new Date(),
        metadata
      };

      const eventKey = RedisKeys.SESSION_EVENT(sessionId, event);
      await redisService.set(eventKey, eventData, RedisTTL.STATS_DAILY);

    } catch (error) {
      logger.error('记录会话事件失败', {
        sessionId,
        event,
        error: error.message
      });
    }
  }

  /**
   * 启动会话清理定时器
   */
  private startSessionCleanup(): void {
    this.cleanupTimer = setInterval(async () => {
      await this.cleanupExpiredSessions();
    }, this.sessionCleanupInterval);

    logger.info('会话清理定时器已启动', {
      interval: this.sessionCleanupInterval / 1000 / 60 + '分钟'
    });
  }

  /**
   * 停止会话清理定时器
   */
  stopSessionCleanup(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
      this.cleanupTimer = undefined;
      logger.info('会话清理定时器已停止');
    }
  }

  /**
   * 清理资源
   */
  async cleanup(): Promise<void> {
    this.stopSessionCleanup();
    logger.info('会话管理器清理完成');
  }
}

// 创建单例实例
export const sessionManager = new SessionManager();
