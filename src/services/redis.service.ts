/**
 * Redis服务
 * 管理Redis连接和提供缓存操作接口
 */

import Redis from 'ioredis';
import { logger } from '@/config/logger';
import { getRedisConfig, redisHealthConfig, RedisKeys, RedisTTL } from '@/config/redis';

class RedisService {
  private client: Redis | null = null;
  private isConnected = false;
  private healthCheckInterval: NodeJS.Timeout | null = null;

  /**
   * 初始化Redis连接
   */
  async initialize(): Promise<void> {
    try {
      const config = getRedisConfig();
      
      this.client = new Redis({
        host: config.host,
        port: config.port,
        password: config.password,
        db: config.db,
        keyPrefix: config.keyPrefix,
        maxRetriesPerRequest: config.maxRetriesPerRequest,
        lazyConnect: config.lazyConnect,
        keepAlive: config.keepAlive,
        family: config.family,
        connectTimeout: config.connectTimeout,
        commandTimeout: config.commandTimeout
      });

      // 连接事件监听
      this.client.on('connect', () => {
        logger.info('Redis连接已建立', {
          service: 'redis',
          host: config.host,
          port: config.port,
          db: config.db
        });
      });

      this.client.on('ready', () => {
        this.isConnected = true;
        logger.info('Redis连接就绪', { service: 'redis' });
        this.startHealthCheck();
      });

      this.client.on('error', (error) => {
        this.isConnected = false;
        logger.error('Redis连接错误', {
          service: 'redis',
          error: error.message,
          stack: error.stack
        });
      });

      this.client.on('close', () => {
        this.isConnected = false;
        logger.warn('Redis连接已关闭', { service: 'redis' });
        this.stopHealthCheck();
      });

      this.client.on('reconnecting', () => {
        logger.info('Redis正在重连', { service: 'redis' });
      });

      // 建立连接
      await this.client.connect();
      
      logger.info('Redis服务初始化完成', {
        service: 'redis',
        config: {
          host: config.host,
          port: config.port,
          db: config.db,
          keyPrefix: config.keyPrefix
        }
      });

    } catch (error) {
      logger.error('Redis初始化失败', {
        service: 'redis',
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * 获取Redis客户端实例
   */
  getClient(): Redis {
    if (!this.client) {
      throw new Error('Redis客户端未初始化');
    }
    return this.client;
  }

  /**
   * 检查Redis连接状态
   */
  isReady(): boolean {
    return this.isConnected && this.client?.status === 'ready';
  }

  /**
   * 启动健康检查
   */
  private startHealthCheck(): void {
    if (this.healthCheckInterval) {
      return;
    }

    this.healthCheckInterval = setInterval(async () => {
      try {
        if (this.client) {
          await this.client.ping();
        }
      } catch (error) {
        logger.error('Redis健康检查失败', {
          service: 'redis',
          error: error instanceof Error ? error.message : String(error)
        });
        this.isConnected = false;
      }
    }, redisHealthConfig.checkInterval);
  }

  /**
   * 停止健康检查
   */
  private stopHealthCheck(): void {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
      this.healthCheckInterval = null;
    }
  }

  /**
   * 关闭Redis连接
   */
  async close(): Promise<void> {
    try {
      this.stopHealthCheck();
      
      if (this.client) {
        await this.client.quit();
        this.client = null;
        this.isConnected = false;
        logger.info('Redis连接已关闭', { service: 'redis' });
      }
    } catch (error) {
      logger.error('关闭Redis连接失败', {
        service: 'redis',
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  // ==================== 缓存操作方法 ====================

  /**
   * 设置缓存
   */
  async set(key: string, value: any, ttl?: number): Promise<void> {
    if (!this.isReady()) {
      logger.warn('Redis未就绪，跳过缓存设置', { key });
      return;
    }

    try {
      const serializedValue = JSON.stringify(value);
      
      if (ttl) {
        await this.client!.setex(key, ttl, serializedValue);
      } else {
        await this.client!.set(key, serializedValue);
      }
      
      logger.debug('缓存设置成功', { key, ttl });
    } catch (error) {
      logger.error('缓存设置失败', {
        key,
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  /**
   * 获取缓存
   */
  async get<T = any>(key: string): Promise<T | null> {
    if (!this.isReady()) {
      logger.warn('Redis未就绪，跳过缓存获取', { key });
      return null;
    }

    try {
      const value = await this.client!.get(key);
      
      if (value === null) {
        return null;
      }
      
      const parsedValue = JSON.parse(value);
      logger.debug('缓存获取成功', { key });
      return parsedValue;
    } catch (error) {
      logger.error('缓存获取失败', {
        key,
        error: error instanceof Error ? error.message : String(error)
      });
      return null;
    }
  }

  /**
   * 删除缓存
   */
  async del(key: string | string[]): Promise<number> {
    if (!this.isReady()) {
      logger.warn('Redis未就绪，跳过缓存删除', { key });
      return 0;
    }

    try {
      const result = Array.isArray(key)
        ? await this.client!.del(...key)
        : await this.client!.del(key);
      logger.debug('缓存删除成功', { key, deletedCount: result });
      return result;
    } catch (error) {
      logger.error('缓存删除失败', {
        key,
        error: error instanceof Error ? error.message : String(error)
      });
      return 0;
    }
  }

  /**
   * 检查键是否存在
   */
  async exists(key: string): Promise<boolean> {
    if (!this.isReady()) {
      return false;
    }

    try {
      const result = await this.client!.exists(key);
      return result === 1;
    } catch (error) {
      logger.error('检查键存在失败', {
        key,
        error: error instanceof Error ? error.message : String(error)
      });
      return false;
    }
  }

  /**
   * 设置键过期时间
   */
  async expire(key: string, ttl: number): Promise<boolean> {
    if (!this.isReady()) {
      return false;
    }

    try {
      const result = await this.client!.expire(key, ttl);
      return result === 1;
    } catch (error) {
      logger.error('设置过期时间失败', {
        key,
        ttl,
        error: error instanceof Error ? error.message : String(error)
      });
      return false;
    }
  }

  /**
   * 获取键的剩余过期时间
   */
  async ttl(key: string): Promise<number> {
    if (!this.isReady()) {
      return -1;
    }

    try {
      return await this.client!.ttl(key);
    } catch (error) {
      logger.error('获取过期时间失败', {
        key,
        error: error instanceof Error ? error.message : String(error)
      });
      return -1;
    }
  }

  /**
   * 原子递增
   */
  async incr(key: string): Promise<number> {
    if (!this.isReady()) {
      return 0;
    }

    try {
      return await this.client!.incr(key);
    } catch (error) {
      logger.error('原子递增失败', {
        key,
        error: error instanceof Error ? error.message : String(error)
      });
      return 0;
    }
  }

  /**
   * 原子递增指定值
   */
  async incrby(key: string, increment: number): Promise<number> {
    if (!this.isReady()) {
      return 0;
    }

    try {
      return await this.client!.incrby(key, increment);
    } catch (error) {
      logger.error('原子递增失败', {
        key,
        increment,
        error: error instanceof Error ? error.message : String(error)
      });
      return 0;
    }
  }

  /**
   * 获取匹配模式的所有键
   */
  async keys(pattern: string): Promise<string[]> {
    if (!this.isReady()) {
      return [];
    }

    try {
      return await this.client!.keys(pattern);
    } catch (error) {
      logger.error('获取键列表失败', {
        pattern,
        error: error instanceof Error ? error.message : String(error)
      });
      return [];
    }
  }

  /**
   * 批量删除匹配模式的键
   */
  async deletePattern(pattern: string): Promise<number> {
    if (!this.isReady()) {
      return 0;
    }

    try {
      const keys = await this.keys(pattern);
      if (keys.length === 0) {
        return 0;
      }
      
      return await this.del(keys);
    } catch (error) {
      logger.error('批量删除失败', {
        pattern,
        error: error instanceof Error ? error.message : String(error)
      });
      return 0;
    }
  }

  /**
   * 获取Redis信息
   */
  async getInfo(): Promise<any> {
    if (!this.isReady()) {
      return null;
    }

    try {
      const info = await this.client!.info();
      return this.parseRedisInfo(info);
    } catch (error) {
      logger.error('获取Redis信息失败', {
        error: error instanceof Error ? error.message : String(error)
      });
      return null;
    }
  }

  /**
   * 解析Redis INFO命令输出
   */
  private parseRedisInfo(info: string): any {
    const result: any = {};
    const sections = info.split('\r\n\r\n');
    
    sections.forEach(section => {
      const lines = section.split('\r\n');
      const sectionName = lines[0].replace('# ', '');
      result[sectionName] = {};
      
      lines.slice(1).forEach(line => {
        if (line && line.includes(':')) {
          const [key, value] = line.split(':');
          result[sectionName][key] = value;
        }
      });
    });
    
    return result;
  }
}

// 创建单例实例
export const redisService = new RedisService();

// 导出Redis键和TTL常量
export { RedisKeys, RedisTTL };
