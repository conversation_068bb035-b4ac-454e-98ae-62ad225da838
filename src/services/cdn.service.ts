/**
 * CDN集成服务
 * 提供静态资源CDN加速和管理功能
 */

import { logger } from '@/config/logger';
import { redisService } from './redis.service';
import path from 'path';
import fs from 'fs';
import crypto from 'crypto';

/**
 * CDN配置接口
 */
interface CDNConfig {
  enabled: boolean;
  provider: 'cloudflare' | 'aws' | 'aliyun' | 'custom';
  baseUrl: string;
  apiKey?: string;
  secretKey?: string;
  zoneId?: string;
  bucketName?: string;
  region?: string;
  customHeaders?: Record<string, string>;
  cacheControl?: string;
  maxAge?: number;
}

/**
 * 资源类型配置
 */
interface AssetTypeConfig {
  extensions: string[];
  cacheControl: string;
  maxAge: number;
  compress: boolean;
  versioning: boolean;
}

/**
 * CDN服务类
 */
export class CDNService {
  private config: CDNConfig;
  private assetTypes: Map<string, AssetTypeConfig>;
  private assetManifest: Map<string, string> = new Map();
  private versionCache: Map<string, string> = new Map();

  constructor() {
    this.config = this.loadConfig();
    this.assetTypes = this.initializeAssetTypes();
    this.loadAssetManifest();
  }

  /**
   * 加载CDN配置
   */
  private loadConfig(): CDNConfig {
    return {
      enabled: process.env.CDN_ENABLED === 'true',
      provider: (process.env.CDN_PROVIDER as any) || 'custom',
      baseUrl: process.env.CDN_BASE_URL || '',
      apiKey: process.env.CDN_API_KEY,
      secretKey: process.env.CDN_SECRET_KEY,
      zoneId: process.env.CDN_ZONE_ID,
      bucketName: process.env.CDN_BUCKET_NAME,
      region: process.env.CDN_REGION || 'us-east-1',
      customHeaders: this.parseCustomHeaders(process.env.CDN_CUSTOM_HEADERS),
      cacheControl: process.env.CDN_CACHE_CONTROL || 'public, max-age=31536000',
      maxAge: parseInt(process.env.CDN_MAX_AGE || '31536000') // 1年
    };
  }

  /**
   * 解析自定义头部
   */
  private parseCustomHeaders(headersStr?: string): Record<string, string> {
    if (!headersStr) return {};
    
    try {
      return JSON.parse(headersStr);
    } catch {
      return {};
    }
  }

  /**
   * 初始化资源类型配置
   */
  private initializeAssetTypes(): Map<string, AssetTypeConfig> {
    const types = new Map<string, AssetTypeConfig>();

    // JavaScript文件
    types.set('js', {
      extensions: ['.js', '.mjs'],
      cacheControl: 'public, max-age=31536000, immutable',
      maxAge: 31536000, // 1年
      compress: true,
      versioning: true
    });

    // CSS文件
    types.set('css', {
      extensions: ['.css'],
      cacheControl: 'public, max-age=31536000, immutable',
      maxAge: 31536000,
      compress: true,
      versioning: true
    });

    // 图片文件
    types.set('images', {
      extensions: ['.png', '.jpg', '.jpeg', '.gif', '.svg', '.webp', '.ico'],
      cacheControl: 'public, max-age=2592000', // 30天
      maxAge: 2592000,
      compress: false,
      versioning: false
    });

    // 字体文件
    types.set('fonts', {
      extensions: ['.woff', '.woff2', '.ttf', '.eot'],
      cacheControl: 'public, max-age=31536000, immutable',
      maxAge: 31536000,
      compress: false,
      versioning: false
    });

    // 其他静态资源
    types.set('static', {
      extensions: ['.json', '.xml', '.txt'],
      cacheControl: 'public, max-age=86400', // 1天
      maxAge: 86400,
      compress: true,
      versioning: false
    });

    return types;
  }

  /**
   * 加载资源清单
   */
  private async loadAssetManifest(): Promise<void> {
    try {
      const manifestPath = path.join(process.cwd(), 'dist/frontend/manifest.json');
      
      if (fs.existsSync(manifestPath)) {
        const manifestContent = fs.readFileSync(manifestPath, 'utf-8');
        const manifest = JSON.parse(manifestContent);
        
        // 将清单数据加载到内存
        Object.entries(manifest).forEach(([key, value]) => {
          this.assetManifest.set(key, value as string);
        });

        logger.info('资源清单加载完成', {
          assetsCount: this.assetManifest.size
        });
      }
    } catch (error) {
      logger.warn('加载资源清单失败', {
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  /**
   * 获取资源的CDN URL
   */
  getAssetUrl(assetPath: string): string {
    if (!this.config.enabled || !this.config.baseUrl) {
      return assetPath;
    }

    // 检查是否有版本化的资源
    const versionedPath = this.assetManifest.get(assetPath) || assetPath;
    
    // 构建完整的CDN URL
    const cdnUrl = this.config.baseUrl.replace(/\/$/, '') + '/' + versionedPath.replace(/^\//, '');
    
    return cdnUrl;
  }

  /**
   * 获取资源的缓存控制头
   */
  getCacheControl(assetPath: string): string {
    const extension = path.extname(assetPath).toLowerCase();
    
    for (const [type, config] of this.assetTypes) {
      if (config.extensions.includes(extension)) {
        return config.cacheControl;
      }
    }
    
    return this.config.cacheControl || 'public, max-age=86400';
  }

  /**
   * 检查资源是否应该使用CDN
   */
  shouldUseCDN(assetPath: string): boolean {
    if (!this.config.enabled) {
      return false;
    }

    const extension = path.extname(assetPath).toLowerCase();
    
    // 检查是否是支持的静态资源类型
    for (const config of this.assetTypes.values()) {
      if (config.extensions.includes(extension)) {
        return true;
      }
    }
    
    return false;
  }

  /**
   * 生成资源版本号
   */
  generateAssetVersion(filePath: string): string {
    try {
      if (this.versionCache.has(filePath)) {
        return this.versionCache.get(filePath)!;
      }

      const fullPath = path.join(process.cwd(), 'dist/frontend', filePath);
      
      if (fs.existsSync(fullPath)) {
        const content = fs.readFileSync(fullPath);
        const hash = crypto.createHash('md5').update(content).digest('hex').substring(0, 8);
        
        this.versionCache.set(filePath, hash);
        return hash;
      }
    } catch (error) {
      logger.warn('生成资源版本号失败', {
        filePath,
        error: error instanceof Error ? error.message : String(error)
      });
    }
    
    return Date.now().toString();
  }

  /**
   * 预热CDN缓存
   */
  async warmupCache(assetPaths: string[]): Promise<void> {
    if (!this.config.enabled) {
      logger.info('CDN未启用，跳过缓存预热');
      return;
    }

    logger.info('开始CDN缓存预热', { assetsCount: assetPaths.length });

    const warmupPromises = assetPaths.map(async (assetPath) => {
      try {
        const cdnUrl = this.getAssetUrl(assetPath);
        
        // 发送HEAD请求预热缓存
        const response = await fetch(cdnUrl, { method: 'HEAD' });
        
        if (response.ok) {
          logger.debug('缓存预热成功', { assetPath, cdnUrl });
        } else {
          logger.warn('缓存预热失败', { 
            assetPath, 
            cdnUrl, 
            status: response.status 
          });
        }
      } catch (error) {
        logger.error('缓存预热错误', {
          assetPath,
          error: error instanceof Error ? error.message : String(error)
        });
      }
    });

    await Promise.allSettled(warmupPromises);
    logger.info('CDN缓存预热完成');
  }

  /**
   * 清除CDN缓存
   */
  async purgeCache(assetPaths?: string[]): Promise<void> {
    if (!this.config.enabled) {
      logger.info('CDN未启用，跳过缓存清除');
      return;
    }

    try {
      switch (this.config.provider) {
        case 'cloudflare':
          await this.purgeCloudflareCache(assetPaths);
          break;
        case 'aws':
          await this.purgeAWSCache(assetPaths);
          break;
        case 'aliyun':
          await this.purgeAliyunCache(assetPaths);
          break;
        default:
          logger.warn('不支持的CDN提供商，无法清除缓存', {
            provider: this.config.provider
          });
      }
    } catch (error) {
      logger.error('清除CDN缓存失败', {
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * 清除Cloudflare缓存
   */
  private async purgeCloudflareCache(assetPaths?: string[]): Promise<void> {
    if (!this.config.apiKey || !this.config.zoneId) {
      throw new Error('Cloudflare配置不完整');
    }

    const url = `https://api.cloudflare.com/client/v4/zones/${this.config.zoneId}/purge_cache`;
    
    const body = assetPaths ? {
      files: assetPaths.map(path => this.getAssetUrl(path))
    } : {
      purge_everything: true
    };

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.config.apiKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(body)
    });

    if (!response.ok) {
      throw new Error(`Cloudflare API错误: ${response.status}`);
    }

    logger.info('Cloudflare缓存清除成功', {
      assetPaths: assetPaths?.length || 'all'
    });
  }

  /**
   * 清除AWS CloudFront缓存
   */
  private async purgeAWSCache(assetPaths?: string[]): Promise<void> {
    // TODO: 实现AWS CloudFront缓存清除
    logger.info('AWS CloudFront缓存清除功能待实现');
  }

  /**
   * 清除阿里云CDN缓存
   */
  private async purgeAliyunCache(assetPaths?: string[]): Promise<void> {
    // TODO: 实现阿里云CDN缓存清除
    logger.info('阿里云CDN缓存清除功能待实现');
  }

  /**
   * 获取CDN统计信息
   */
  async getStatistics(): Promise<any> {
    try {
      const stats = {
        enabled: this.config.enabled,
        provider: this.config.provider,
        baseUrl: this.config.baseUrl,
        assetTypes: Array.from(this.assetTypes.keys()),
        manifestSize: this.assetManifest.size,
        versionCacheSize: this.versionCache.size,
        supportedExtensions: this.getAllSupportedExtensions()
      };

      return stats;
    } catch (error) {
      logger.error('获取CDN统计信息失败', {
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * 获取所有支持的文件扩展名
   */
  private getAllSupportedExtensions(): string[] {
    const extensions = new Set<string>();
    
    for (const config of this.assetTypes.values()) {
      config.extensions.forEach(ext => extensions.add(ext));
    }
    
    return Array.from(extensions);
  }

  /**
   * 重新加载配置
   */
  async reloadConfig(): Promise<void> {
    this.config = this.loadConfig();
    await this.loadAssetManifest();
    
    logger.info('CDN配置已重新加载', {
      enabled: this.config.enabled,
      provider: this.config.provider
    });
  }

  /**
   * 健康检查
   */
  async healthCheck(): Promise<boolean> {
    if (!this.config.enabled) {
      return true; // CDN未启用时认为健康
    }

    try {
      // 检查CDN基础URL是否可访问
      const response = await fetch(this.config.baseUrl, { 
        method: 'HEAD',
        timeout: 5000 
      });
      
      return response.ok;
    } catch (error) {
      logger.error('CDN健康检查失败', {
        baseUrl: this.config.baseUrl,
        error: error instanceof Error ? error.message : String(error)
      });
      return false;
    }
  }
}

// 创建服务实例
export const cdnService = new CDNService();
