/**
 * 国际化服务
 * 提供多语言支持、本地化适配、动态语言切换等功能
 */

import fs from 'fs/promises';
import path from 'path';
import { logger } from '@/config/logger';
import { cacheService } from '@/services/cache.service';

/**
 * 支持的语言列表
 */
export enum SupportedLanguage {
  ZH_CN = 'zh-CN',  // 简体中文
  ZH_TW = 'zh-TW',  // 繁体中文
  EN_US = 'en-US',  // 美式英语
  EN_GB = 'en-GB',  // 英式英语
  JA_JP = 'ja-JP',  // 日语
  KO_KR = 'ko-KR',  // 韩语
  FR_FR = 'fr-FR',  // 法语
  DE_DE = 'de-DE',  // 德语
  ES_ES = 'es-ES',  // 西班牙语
  PT_BR = 'pt-BR',  // 巴西葡萄牙语
  RU_RU = 'ru-RU',  // 俄语
  AR_SA = 'ar-SA'   // 阿拉伯语
}

/**
 * 语言信息接口
 */
interface LanguageInfo {
  code: SupportedLanguage;
  name: string;
  nativeName: string;
  direction: 'ltr' | 'rtl';
  region: string;
  flag: string;
  enabled: boolean;
  completeness: number; // 翻译完成度 0-100
}

/**
 * 翻译资源接口
 */
interface TranslationResource {
  [key: string]: string | TranslationResource;
}

/**
 * 翻译上下文接口
 */
interface TranslationContext {
  language: SupportedLanguage;
  namespace?: string;
  variables?: Record<string, any>;
  pluralCount?: number;
  gender?: 'male' | 'female' | 'neutral';
}

/**
 * 本地化配置接口
 */
interface LocalizationConfig {
  dateFormat: string;
  timeFormat: string;
  numberFormat: {
    decimal: string;
    thousands: string;
    currency: string;
  };
  timezone: string;
  firstDayOfWeek: number; // 0=Sunday, 1=Monday
}

/**
 * 国际化服务
 */
export class I18nService {
  private translations = new Map<string, TranslationResource>();
  private languageInfos = new Map<SupportedLanguage, LanguageInfo>();
  private localizationConfigs = new Map<SupportedLanguage, LocalizationConfig>();
  private defaultLanguage: SupportedLanguage = SupportedLanguage.EN_US;
  private fallbackLanguage: SupportedLanguage = SupportedLanguage.EN_US;

  constructor() {
    this.initializeLanguageInfos();
    this.initializeLocalizationConfigs();
  }

  /**
   * 初始化国际化服务
   */
  async initialize(): Promise<void> {
    try {
      logger.info('初始化国际化服务...');

      // 加载所有语言的翻译资源
      await this.loadAllTranslations();

      // 验证翻译完整性
      await this.validateTranslations();

      logger.info('国际化服务初始化完成', {
        supportedLanguages: Array.from(this.languageInfos.keys()),
        loadedTranslations: this.translations.size
      });

    } catch (error) {
      logger.error('国际化服务初始化失败', {
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * 翻译文本
   */
  translate(key: string, context: Partial<TranslationContext> = {}): string {
    const {
      language = this.defaultLanguage,
      namespace = 'common',
      variables = {},
      pluralCount,
      gender
    } = context;

    try {
      // 构建翻译键
      const translationKey = `${language}:${namespace}`;
      const translations = this.translations.get(translationKey);

      if (!translations) {
        return this.getFallbackTranslation(key, context);
      }

      // 获取翻译文本
      let translation = this.getNestedValue(translations, key);

      if (!translation) {
        return this.getFallbackTranslation(key, context);
      }

      // 处理复数形式
      if (pluralCount !== undefined && typeof translation === 'object') {
        translation = this.handlePluralization(translation, pluralCount, language);
      }

      // 处理性别形式
      if (gender && typeof translation === 'object') {
        translation = this.handleGenderization(translation, gender);
      }

      if (typeof translation !== 'string') {
        return this.getFallbackTranslation(key, context);
      }

      // 替换变量
      return this.interpolateVariables(translation, variables);

    } catch (error) {
      logger.warn('翻译失败', {
        key,
        language,
        namespace,
        error: error instanceof Error ? error.message : String(error)
      });
      return this.getFallbackTranslation(key, context);
    }
  }

  /**
   * 批量翻译
   */
  translateBatch(keys: string[], context: Partial<TranslationContext> = {}): Record<string, string> {
    const result: Record<string, string> = {};
    
    for (const key of keys) {
      result[key] = this.translate(key, context);
    }
    
    return result;
  }

  /**
   * 获取支持的语言列表
   */
  getSupportedLanguages(): LanguageInfo[] {
    return Array.from(this.languageInfos.values()).filter(lang => lang.enabled);
  }

  /**
   * 获取语言信息
   */
  getLanguageInfo(language: SupportedLanguage): LanguageInfo | null {
    return this.languageInfos.get(language) || null;
  }

  /**
   * 检测用户语言偏好
   */
  detectUserLanguage(acceptLanguage?: string, userAgent?: string): SupportedLanguage {
    // 从Accept-Language头部检测
    if (acceptLanguage) {
      const languages = this.parseAcceptLanguage(acceptLanguage);
      for (const lang of languages) {
        const supportedLang = this.findSupportedLanguage(lang.code);
        if (supportedLang) {
          return supportedLang;
        }
      }
    }

    // 从User-Agent检测（移动端）
    if (userAgent) {
      const detectedLang = this.detectLanguageFromUserAgent(userAgent);
      if (detectedLang) {
        return detectedLang;
      }
    }

    return this.defaultLanguage;
  }

  /**
   * 获取本地化配置
   */
  getLocalizationConfig(language: SupportedLanguage): LocalizationConfig {
    return this.localizationConfigs.get(language) || this.localizationConfigs.get(this.defaultLanguage)!;
  }

  /**
   * 格式化日期
   */
  formatDate(date: Date, language: SupportedLanguage, format?: string): string {
    const config = this.getLocalizationConfig(language);
    const dateFormat = format || config.dateFormat;

    try {
      return new Intl.DateTimeFormat(language, {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
      }).format(date);
    } catch (error) {
      return date.toLocaleDateString();
    }
  }

  /**
   * 格式化时间
   */
  formatTime(date: Date, language: SupportedLanguage, format?: string): string {
    const config = this.getLocalizationConfig(language);
    const timeFormat = format || config.timeFormat;

    try {
      return new Intl.DateTimeFormat(language, {
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      }).format(date);
    } catch (error) {
      return date.toLocaleTimeString();
    }
  }

  /**
   * 格式化数字
   */
  formatNumber(number: number, language: SupportedLanguage, options?: Intl.NumberFormatOptions): string {
    try {
      return new Intl.NumberFormat(language, options).format(number);
    } catch (error) {
      return number.toString();
    }
  }

  /**
   * 格式化货币
   */
  formatCurrency(amount: number, currency: string, language: SupportedLanguage): string {
    try {
      return new Intl.NumberFormat(language, {
        style: 'currency',
        currency: currency
      }).format(amount);
    } catch (error) {
      return `${currency} ${amount}`;
    }
  }

  /**
   * 获取相对时间
   */
  getRelativeTime(date: Date, language: SupportedLanguage): string {
    try {
      const rtf = new Intl.RelativeTimeFormat(language, { numeric: 'auto' });
      const now = new Date();
      const diffInSeconds = Math.floor((date.getTime() - now.getTime()) / 1000);

      if (Math.abs(diffInSeconds) < 60) {
        return rtf.format(diffInSeconds, 'second');
      } else if (Math.abs(diffInSeconds) < 3600) {
        return rtf.format(Math.floor(diffInSeconds / 60), 'minute');
      } else if (Math.abs(diffInSeconds) < 86400) {
        return rtf.format(Math.floor(diffInSeconds / 3600), 'hour');
      } else {
        return rtf.format(Math.floor(diffInSeconds / 86400), 'day');
      }
    } catch (error) {
      return date.toLocaleDateString();
    }
  }

  /**
   * 加载所有翻译资源
   */
  private async loadAllTranslations(): Promise<void> {
    const translationsDir = path.join(process.cwd(), 'locales');
    
    try {
      const languages = await fs.readdir(translationsDir);
      
      for (const langDir of languages) {
        const langPath = path.join(translationsDir, langDir);
        const stat = await fs.stat(langPath);
        
        if (stat.isDirectory()) {
          const language = langDir as SupportedLanguage;
          if (Object.values(SupportedLanguage).includes(language)) {
            await this.loadLanguageTranslations(language, langPath);
          }
        }
      }
    } catch (error) {
      logger.warn('翻译目录不存在，使用默认翻译', { translationsDir });
      await this.loadDefaultTranslations();
    }
  }

  /**
   * 加载单个语言的翻译资源
   */
  private async loadLanguageTranslations(language: SupportedLanguage, langPath: string): Promise<void> {
    try {
      const files = await fs.readdir(langPath);
      
      for (const file of files) {
        if (file.endsWith('.json')) {
          const namespace = path.basename(file, '.json');
          const filePath = path.join(langPath, file);
          const content = await fs.readFile(filePath, 'utf-8');
          const translations = JSON.parse(content);
          
          const key = `${language}:${namespace}`;
          this.translations.set(key, translations);
        }
      }
      
      logger.debug('加载语言翻译完成', { language, fileCount: files.length });
    } catch (error) {
      logger.error('加载语言翻译失败', {
        language,
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  /**
   * 加载默认翻译（内置）
   */
  private async loadDefaultTranslations(): Promise<void> {
    // 内置的基础翻译
    const defaultTranslations = {
      'en-US:common': {
        'auth.login': 'Login',
        'auth.logout': 'Logout',
        'auth.register': 'Register',
        'auth.forgot_password': 'Forgot Password',
        'auth.reset_password': 'Reset Password',
        'auth.change_password': 'Change Password',
        'common.save': 'Save',
        'common.cancel': 'Cancel',
        'common.delete': 'Delete',
        'common.edit': 'Edit',
        'common.create': 'Create',
        'common.update': 'Update',
        'common.search': 'Search',
        'common.loading': 'Loading...',
        'common.error': 'Error',
        'common.success': 'Success',
        'common.warning': 'Warning',
        'common.info': 'Information'
      },
      'zh-CN:common': {
        'auth.login': '登录',
        'auth.logout': '登出',
        'auth.register': '注册',
        'auth.forgot_password': '忘记密码',
        'auth.reset_password': '重置密码',
        'auth.change_password': '修改密码',
        'common.save': '保存',
        'common.cancel': '取消',
        'common.delete': '删除',
        'common.edit': '编辑',
        'common.create': '创建',
        'common.update': '更新',
        'common.search': '搜索',
        'common.loading': '加载中...',
        'common.error': '错误',
        'common.success': '成功',
        'common.warning': '警告',
        'common.info': '信息'
      }
    };

    for (const [key, translations] of Object.entries(defaultTranslations)) {
      this.translations.set(key, translations);
    }
  }

  /**
   * 初始化语言信息
   */
  private initializeLanguageInfos(): void {
    const languageInfos: LanguageInfo[] = [
      {
        code: SupportedLanguage.ZH_CN,
        name: 'Chinese (Simplified)',
        nativeName: '简体中文',
        direction: 'ltr',
        region: 'China',
        flag: '🇨🇳',
        enabled: true,
        completeness: 100
      },
      {
        code: SupportedLanguage.ZH_TW,
        name: 'Chinese (Traditional)',
        nativeName: '繁體中文',
        direction: 'ltr',
        region: 'Taiwan',
        flag: '🇹🇼',
        enabled: true,
        completeness: 90
      },
      {
        code: SupportedLanguage.EN_US,
        name: 'English (US)',
        nativeName: 'English',
        direction: 'ltr',
        region: 'United States',
        flag: '🇺🇸',
        enabled: true,
        completeness: 100
      },
      {
        code: SupportedLanguage.EN_GB,
        name: 'English (UK)',
        nativeName: 'English',
        direction: 'ltr',
        region: 'United Kingdom',
        flag: '🇬🇧',
        enabled: true,
        completeness: 95
      },
      {
        code: SupportedLanguage.JA_JP,
        name: 'Japanese',
        nativeName: '日本語',
        direction: 'ltr',
        region: 'Japan',
        flag: '🇯🇵',
        enabled: true,
        completeness: 85
      },
      {
        code: SupportedLanguage.KO_KR,
        name: 'Korean',
        nativeName: '한국어',
        direction: 'ltr',
        region: 'South Korea',
        flag: '🇰🇷',
        enabled: true,
        completeness: 80
      },
      {
        code: SupportedLanguage.FR_FR,
        name: 'French',
        nativeName: 'Français',
        direction: 'ltr',
        region: 'France',
        flag: '🇫🇷',
        enabled: true,
        completeness: 75
      },
      {
        code: SupportedLanguage.DE_DE,
        name: 'German',
        nativeName: 'Deutsch',
        direction: 'ltr',
        region: 'Germany',
        flag: '🇩🇪',
        enabled: true,
        completeness: 70
      },
      {
        code: SupportedLanguage.ES_ES,
        name: 'Spanish',
        nativeName: 'Español',
        direction: 'ltr',
        region: 'Spain',
        flag: '🇪🇸',
        enabled: true,
        completeness: 65
      },
      {
        code: SupportedLanguage.PT_BR,
        name: 'Portuguese (Brazil)',
        nativeName: 'Português',
        direction: 'ltr',
        region: 'Brazil',
        flag: '🇧🇷',
        enabled: true,
        completeness: 60
      },
      {
        code: SupportedLanguage.RU_RU,
        name: 'Russian',
        nativeName: 'Русский',
        direction: 'ltr',
        region: 'Russia',
        flag: '🇷🇺',
        enabled: true,
        completeness: 55
      },
      {
        code: SupportedLanguage.AR_SA,
        name: 'Arabic',
        nativeName: 'العربية',
        direction: 'rtl',
        region: 'Saudi Arabia',
        flag: '🇸🇦',
        enabled: true,
        completeness: 50
      }
    ];

    for (const info of languageInfos) {
      this.languageInfos.set(info.code, info);
    }
  }

  /**
   * 初始化本地化配置
   */
  private initializeLocalizationConfigs(): void {
    const configs: Array<[SupportedLanguage, LocalizationConfig]> = [
      [SupportedLanguage.ZH_CN, {
        dateFormat: 'YYYY-MM-DD',
        timeFormat: 'HH:mm:ss',
        numberFormat: { decimal: '.', thousands: ',', currency: '¥' },
        timezone: 'Asia/Shanghai',
        firstDayOfWeek: 1
      }],
      [SupportedLanguage.EN_US, {
        dateFormat: 'MM/DD/YYYY',
        timeFormat: 'h:mm:ss A',
        numberFormat: { decimal: '.', thousands: ',', currency: '$' },
        timezone: 'America/New_York',
        firstDayOfWeek: 0
      }],
      [SupportedLanguage.JA_JP, {
        dateFormat: 'YYYY/MM/DD',
        timeFormat: 'HH:mm:ss',
        numberFormat: { decimal: '.', thousands: ',', currency: '¥' },
        timezone: 'Asia/Tokyo',
        firstDayOfWeek: 0
      }],
      [SupportedLanguage.AR_SA, {
        dateFormat: 'DD/MM/YYYY',
        timeFormat: 'HH:mm:ss',
        numberFormat: { decimal: '.', thousands: ',', currency: 'ر.س' },
        timezone: 'Asia/Riyadh',
        firstDayOfWeek: 6
      }]
    ];

    for (const [language, config] of configs) {
      this.localizationConfigs.set(language, config);
    }
  }

  /**
   * 获取嵌套值
   */
  private getNestedValue(obj: any, key: string): any {
    return key.split('.').reduce((current, prop) => current?.[prop], obj);
  }

  /**
   * 获取回退翻译
   */
  private getFallbackTranslation(key: string, context: Partial<TranslationContext>): string {
    if (context.language !== this.fallbackLanguage) {
      const fallbackTranslation = this.translate(key, {
        ...context,
        language: this.fallbackLanguage
      });
      if (fallbackTranslation !== key) {
        return fallbackTranslation;
      }
    }
    return key; // 返回键名作为最后的回退
  }

  /**
   * 处理复数形式
   */
  private handlePluralization(translation: any, count: number, language: SupportedLanguage): string {
    if (typeof translation !== 'object') return translation;

    // 简化的复数规则
    if (language.startsWith('zh')) {
      // 中文没有复数形式
      return translation.other || translation.one || '';
    } else if (language.startsWith('en')) {
      // 英语复数规则
      return count === 1 ? (translation.one || '') : (translation.other || '');
    } else {
      // 其他语言的默认规则
      return count === 1 ? (translation.one || '') : (translation.other || '');
    }
  }

  /**
   * 处理性别形式
   */
  private handleGenderization(translation: any, gender: string): string {
    if (typeof translation !== 'object') return translation;
    return translation[gender] || translation.neutral || translation.male || '';
  }

  /**
   * 插值变量
   */
  private interpolateVariables(text: string, variables: Record<string, any>): string {
    return text.replace(/\{\{(\w+)\}\}/g, (match, key) => {
      return variables[key] !== undefined ? String(variables[key]) : match;
    });
  }

  /**
   * 解析Accept-Language头部
   */
  private parseAcceptLanguage(acceptLanguage: string): Array<{ code: string; quality: number }> {
    return acceptLanguage
      .split(',')
      .map(lang => {
        const [code, q] = lang.trim().split(';q=');
        return {
          code: code.trim(),
          quality: q ? parseFloat(q) : 1.0
        };
      })
      .sort((a, b) => b.quality - a.quality);
  }

  /**
   * 查找支持的语言
   */
  private findSupportedLanguage(langCode: string): SupportedLanguage | null {
    // 精确匹配
    const exactMatch = Object.values(SupportedLanguage).find(lang => lang === langCode);
    if (exactMatch) return exactMatch;

    // 语言代码匹配（忽略地区）
    const langOnly = langCode.split('-')[0];
    const langMatch = Object.values(SupportedLanguage).find(lang => lang.startsWith(langOnly));
    return langMatch || null;
  }

  /**
   * 从User-Agent检测语言
   */
  private detectLanguageFromUserAgent(userAgent: string): SupportedLanguage | null {
    // 简化的User-Agent语言检测
    if (userAgent.includes('zh-CN')) return SupportedLanguage.ZH_CN;
    if (userAgent.includes('zh-TW')) return SupportedLanguage.ZH_TW;
    if (userAgent.includes('ja')) return SupportedLanguage.JA_JP;
    if (userAgent.includes('ko')) return SupportedLanguage.KO_KR;
    return null;
  }

  /**
   * 验证翻译完整性
   */
  private async validateTranslations(): Promise<void> {
    const baseLanguage = this.defaultLanguage;
    const baseTranslations = this.translations.get(`${baseLanguage}:common`);
    
    if (!baseTranslations) {
      logger.warn('基础语言翻译不存在', { baseLanguage });
      return;
    }

    const baseKeys = this.getAllKeys(baseTranslations);
    
    for (const [key, translations] of this.translations.entries()) {
      if (key.startsWith(baseLanguage)) continue;
      
      const currentKeys = this.getAllKeys(translations);
      const missingKeys = baseKeys.filter(k => !currentKeys.includes(k));
      
      if (missingKeys.length > 0) {
        logger.warn('翻译缺失键', {
          translation: key,
          missingKeys: missingKeys.slice(0, 5), // 只显示前5个
          missingCount: missingKeys.length
        });
      }
    }
  }

  /**
   * 获取所有键
   */
  private getAllKeys(obj: any, prefix = ''): string[] {
    const keys: string[] = [];
    
    for (const [key, value] of Object.entries(obj)) {
      const fullKey = prefix ? `${prefix}.${key}` : key;
      
      if (typeof value === 'object' && value !== null) {
        keys.push(...this.getAllKeys(value, fullKey));
      } else {
        keys.push(fullKey);
      }
    }
    
    return keys;
  }
}

// 创建单例实例
export const i18nService = new I18nService();
