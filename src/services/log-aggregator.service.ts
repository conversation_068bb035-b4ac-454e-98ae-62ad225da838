/**
 * 日志聚合器服务
 * 收集、解析、聚合和分析系统日志
 */

import { EventEmitter } from 'events';
import { logger } from '@/config/logger';
import fs from 'fs/promises';
import path from 'path';
import { Transform } from 'stream';

/**
 * 日志级别枚举
 */
export enum LogLevel {
  ERROR = 'error',
  WARN = 'warn',
  INFO = 'info',
  DEBUG = 'debug',
  VERBOSE = 'verbose'
}

/**
 * 日志条目接口
 */
export interface LogEntry {
  id: string;
  timestamp: Date;
  level: LogLevel;
  message: string;
  service?: string;
  module?: string;
  userId?: string;
  sessionId?: string;
  requestId?: string;
  ipAddress?: string;
  userAgent?: string;
  metadata?: Record<string, any>;
  stack?: string;
  tags?: string[];
}

/**
 * 日志查询选项
 */
export interface LogQueryOptions {
  level?: LogLevel | LogLevel[];
  service?: string;
  module?: string;
  userId?: string;
  startTime?: Date;
  endTime?: Date;
  search?: string;
  tags?: string[];
  limit?: number;
  offset?: number;
  sortBy?: 'timestamp' | 'level';
  sortOrder?: 'asc' | 'desc';
}

/**
 * 日志统计接口
 */
export interface LogStats {
  totalLogs: number;
  levelDistribution: Record<LogLevel, number>;
  serviceDistribution: Record<string, number>;
  moduleDistribution: Record<string, number>;
  errorRate: number;
  timeRange: {
    start: Date;
    end: Date;
  };
  topErrors: Array<{
    message: string;
    count: number;
    lastOccurrence: Date;
  }>;
  topUsers: Array<{
    userId: string;
    logCount: number;
  }>;
}

/**
 * 日志模式接口
 */
export interface LogPattern {
  id: string;
  name: string;
  pattern: RegExp;
  description: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  enabled: boolean;
  matchCount: number;
  lastMatch?: Date;
  actions?: string[];
}

/**
 * 日志告警接口
 */
export interface LogAlert {
  id: string;
  patternId: string;
  patternName: string;
  message: string;
  level: LogLevel;
  timestamp: Date;
  matchedText: string;
  metadata?: Record<string, any>;
  severity: string;
}

/**
 * 日志聚合器服务
 */
export class LogAggregatorService extends EventEmitter {
  private logs: LogEntry[] = [];
  private patterns: Map<string, LogPattern> = new Map();
  private alerts: LogAlert[] = [];
  private readonly maxLogsInMemory = 50000;
  private readonly maxRetentionTime = 7 * 24 * 60 * 60 * 1000; // 7天
  private cleanupInterval?: NodeJS.Timeout;
  private logIdCounter = 0;

  constructor() {
    super();
    this.initializeDefaultPatterns();
    this.startCleanupTimer();
  }

  /**
   * 添加日志条目
   */
  addLog(entry: Omit<LogEntry, 'id' | 'timestamp'>): void {
    const logEntry: LogEntry = {
      id: this.generateLogId(),
      timestamp: new Date(),
      ...entry
    };

    // 添加到内存中
    this.logs.push(logEntry);

    // 限制内存中的日志数量
    if (this.logs.length > this.maxLogsInMemory) {
      this.logs.shift();
    }

    // 检查日志模式
    this.checkLogPatterns(logEntry);

    // 发出日志事件
    this.emit('logAdded', logEntry);

    // 如果是错误日志，发出错误事件
    if (entry.level === LogLevel.ERROR) {
      this.emit('errorLog', logEntry);
    }
  }

  /**
   * 查询日志
   */
  queryLogs(options: LogQueryOptions = {}): LogEntry[] {
    let results = [...this.logs];

    // 按级别过滤
    if (options.level) {
      const levels = Array.isArray(options.level) ? options.level : [options.level];
      results = results.filter(log => levels.includes(log.level));
    }

    // 按服务过滤
    if (options.service) {
      results = results.filter(log => log.service === options.service);
    }

    // 按模块过滤
    if (options.module) {
      results = results.filter(log => log.module === options.module);
    }

    // 按用户过滤
    if (options.userId) {
      results = results.filter(log => log.userId === options.userId);
    }

    // 按时间范围过滤
    if (options.startTime) {
      results = results.filter(log => log.timestamp >= options.startTime!);
    }

    if (options.endTime) {
      results = results.filter(log => log.timestamp <= options.endTime!);
    }

    // 按搜索词过滤
    if (options.search) {
      const searchLower = options.search.toLowerCase();
      results = results.filter(log => 
        log.message.toLowerCase().includes(searchLower) ||
        (log.metadata && JSON.stringify(log.metadata).toLowerCase().includes(searchLower))
      );
    }

    // 按标签过滤
    if (options.tags && options.tags.length > 0) {
      results = results.filter(log => 
        log.tags && options.tags!.every(tag => log.tags!.includes(tag))
      );
    }

    // 排序
    const sortBy = options.sortBy || 'timestamp';
    const sortOrder = options.sortOrder || 'desc';
    
    results.sort((a, b) => {
      let comparison = 0;
      
      if (sortBy === 'timestamp') {
        comparison = a.timestamp.getTime() - b.timestamp.getTime();
      } else if (sortBy === 'level') {
        const levelOrder = { error: 4, warn: 3, info: 2, debug: 1, verbose: 0 };
        comparison = (levelOrder[a.level as keyof typeof levelOrder] || 0) - 
                    (levelOrder[b.level as keyof typeof levelOrder] || 0);
      }
      
      return sortOrder === 'desc' ? -comparison : comparison;
    });

    // 分页
    const offset = options.offset || 0;
    const limit = options.limit || results.length;
    
    return results.slice(offset, offset + limit);
  }

  /**
   * 获取日志统计
   */
  getLogStats(timeRange?: { start: Date; end: Date }): LogStats {
    let logs = this.logs;

    // 应用时间范围过滤
    if (timeRange) {
      logs = logs.filter(log => 
        log.timestamp >= timeRange.start && log.timestamp <= timeRange.end
      );
    }

    // 计算级别分布
    const levelDistribution: Record<LogLevel, number> = {
      [LogLevel.ERROR]: 0,
      [LogLevel.WARN]: 0,
      [LogLevel.INFO]: 0,
      [LogLevel.DEBUG]: 0,
      [LogLevel.VERBOSE]: 0
    };

    const serviceDistribution: Record<string, number> = {};
    const moduleDistribution: Record<string, number> = {};
    const errorMessages: Map<string, { count: number; lastOccurrence: Date }> = new Map();
    const userLogCounts: Map<string, number> = new Map();

    for (const log of logs) {
      // 级别分布
      levelDistribution[log.level]++;

      // 服务分布
      if (log.service) {
        serviceDistribution[log.service] = (serviceDistribution[log.service] || 0) + 1;
      }

      // 模块分布
      if (log.module) {
        moduleDistribution[log.module] = (moduleDistribution[log.module] || 0) + 1;
      }

      // 错误消息统计
      if (log.level === LogLevel.ERROR) {
        const existing = errorMessages.get(log.message);
        if (existing) {
          existing.count++;
          if (log.timestamp > existing.lastOccurrence) {
            existing.lastOccurrence = log.timestamp;
          }
        } else {
          errorMessages.set(log.message, {
            count: 1,
            lastOccurrence: log.timestamp
          });
        }
      }

      // 用户日志统计
      if (log.userId) {
        userLogCounts.set(log.userId, (userLogCounts.get(log.userId) || 0) + 1);
      }
    }

    // 计算错误率
    const totalLogs = logs.length;
    const errorLogs = levelDistribution[LogLevel.ERROR];
    const errorRate = totalLogs > 0 ? (errorLogs / totalLogs) * 100 : 0;

    // 获取时间范围
    const timestamps = logs.map(log => log.timestamp.getTime());
    const timeRangeResult = {
      start: timestamps.length > 0 ? new Date(Math.min(...timestamps)) : new Date(),
      end: timestamps.length > 0 ? new Date(Math.max(...timestamps)) : new Date()
    };

    // 排序并获取前10个错误
    const topErrors = Array.from(errorMessages.entries())
      .map(([message, data]) => ({ message, ...data }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);

    // 排序并获取前10个用户
    const topUsers = Array.from(userLogCounts.entries())
      .map(([userId, logCount]) => ({ userId, logCount }))
      .sort((a, b) => b.logCount - a.logCount)
      .slice(0, 10);

    return {
      totalLogs,
      levelDistribution,
      serviceDistribution,
      moduleDistribution,
      errorRate,
      timeRange: timeRangeResult,
      topErrors,
      topUsers
    };
  }

  /**
   * 添加日志模式
   */
  addLogPattern(pattern: Omit<LogPattern, 'matchCount' | 'lastMatch'>): void {
    const logPattern: LogPattern = {
      ...pattern,
      matchCount: 0
    };

    this.patterns.set(pattern.id, logPattern);
    
    logger.info('日志模式已添加', {
      patternId: pattern.id,
      name: pattern.name,
      severity: pattern.severity
    });
  }

  /**
   * 移除日志模式
   */
  removeLogPattern(patternId: string): boolean {
    const removed = this.patterns.delete(patternId);
    if (removed) {
      logger.info('日志模式已移除', { patternId });
    }
    return removed;
  }

  /**
   * 获取所有日志模式
   */
  getLogPatterns(): LogPattern[] {
    return Array.from(this.patterns.values());
  }

  /**
   * 获取日志告警
   */
  getLogAlerts(limit = 100): LogAlert[] {
    return this.alerts
      .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
      .slice(0, limit);
  }

  /**
   * 检查日志模式
   */
  private checkLogPatterns(logEntry: LogEntry): void {
    const logText = `${logEntry.message} ${JSON.stringify(logEntry.metadata || {})}`;

    for (const pattern of this.patterns.values()) {
      if (!pattern.enabled) continue;

      if (pattern.pattern.test(logText)) {
        pattern.matchCount++;
        pattern.lastMatch = logEntry.timestamp;

        // 创建告警
        const alert: LogAlert = {
          id: this.generateAlertId(),
          patternId: pattern.id,
          patternName: pattern.name,
          message: logEntry.message,
          level: logEntry.level,
          timestamp: logEntry.timestamp,
          matchedText: logText,
          metadata: logEntry.metadata,
          severity: pattern.severity
        };

        this.alerts.push(alert);

        // 限制告警数量
        if (this.alerts.length > 1000) {
          this.alerts.shift();
        }

        // 发出告警事件
        this.emit('logAlert', alert);

        logger.warn('日志模式匹配告警', {
          patternId: pattern.id,
          patternName: pattern.name,
          severity: pattern.severity,
          message: logEntry.message
        });
      }
    }
  }

  /**
   * 初始化默认模式
   */
  private initializeDefaultPatterns(): void {
    // 错误模式
    this.addLogPattern({
      id: 'error_pattern',
      name: '错误日志模式',
      pattern: /error|exception|failed|failure/i,
      description: '检测错误相关的日志',
      severity: 'high',
      enabled: true
    });

    // 安全模式
    this.addLogPattern({
      id: 'security_pattern',
      name: '安全事件模式',
      pattern: /unauthorized|forbidden|authentication|login.*failed|security/i,
      description: '检测安全相关的事件',
      severity: 'critical',
      enabled: true
    });

    // 性能模式
    this.addLogPattern({
      id: 'performance_pattern',
      name: '性能问题模式',
      pattern: /slow|timeout|performance|latency|memory.*high/i,
      description: '检测性能相关的问题',
      severity: 'medium',
      enabled: true
    });

    // 数据库模式
    this.addLogPattern({
      id: 'database_pattern',
      name: '数据库问题模式',
      pattern: /database.*error|connection.*failed|query.*timeout|deadlock/i,
      description: '检测数据库相关的问题',
      severity: 'high',
      enabled: true
    });
  }

  /**
   * 启动清理定时器
   */
  private startCleanupTimer(): void {
    this.cleanupInterval = setInterval(() => {
      this.cleanupOldLogs();
    }, 60 * 60 * 1000); // 每小时清理一次

    logger.info('日志清理定时器已启动');
  }

  /**
   * 清理过期日志
   */
  private cleanupOldLogs(): void {
    const cutoffTime = new Date(Date.now() - this.maxRetentionTime);
    const originalLength = this.logs.length;
    
    this.logs = this.logs.filter(log => log.timestamp > cutoffTime);
    
    const cleanedCount = originalLength - this.logs.length;
    if (cleanedCount > 0) {
      logger.info('过期日志清理完成', {
        cleanedCount,
        remainingCount: this.logs.length,
        cutoffTime: cutoffTime.toISOString()
      });
    }

    // 清理过期告警
    this.alerts = this.alerts.filter(alert => alert.timestamp > cutoffTime);
  }

  /**
   * 生成日志ID
   */
  private generateLogId(): string {
    return `log_${Date.now()}_${++this.logIdCounter}`;
  }

  /**
   * 生成告警ID
   */
  private generateAlertId(): string {
    return `alert_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`;
  }

  /**
   * 导出日志到文件
   */
  async exportLogs(
    filePath: string, 
    options: LogQueryOptions = {},
    format: 'json' | 'csv' = 'json'
  ): Promise<void> {
    const logs = this.queryLogs(options);
    
    try {
      if (format === 'json') {
        await fs.writeFile(filePath, JSON.stringify(logs, null, 2));
      } else if (format === 'csv') {
        const csvContent = this.convertLogsToCSV(logs);
        await fs.writeFile(filePath, csvContent);
      }

      logger.info('日志导出完成', {
        filePath,
        format,
        logCount: logs.length
      });

    } catch (error) {
      logger.error('日志导出失败', {
        filePath,
        format,
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * 转换日志为CSV格式
   */
  private convertLogsToCSV(logs: LogEntry[]): string {
    if (logs.length === 0) return '';

    const headers = ['id', 'timestamp', 'level', 'message', 'service', 'module', 'userId'];
    const csvRows = [headers.join(',')];

    for (const log of logs) {
      const row = [
        log.id,
        log.timestamp.toISOString(),
        log.level,
        `"${log.message.replace(/"/g, '""')}"`,
        log.service || '',
        log.module || '',
        log.userId || ''
      ];
      csvRows.push(row.join(','));
    }

    return csvRows.join('\n');
  }

  /**
   * 清理资源
   */
  cleanup(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }
    
    this.logs = [];
    this.patterns.clear();
    this.alerts = [];
    this.removeAllListeners();
    
    logger.info('日志聚合器清理完成');
  }
}

// 创建单例实例
export const logAggregator = new LogAggregatorService();
