/**
 * OpenID Connect Provider服务
 * 实现完整的OIDC Provider功能
 */

import { v4 as uuidv4 } from 'uuid';
import crypto from 'crypto';
import jwt from 'jsonwebtoken';
import { prisma } from '@/config/database';
import { config, getServerUrl } from '@/config';
import { logger, logAuditEvent } from '@/config/logger';
import { cacheService } from './cache.service';
import { generateAccessToken, generateRefreshToken } from '@/utils/jwt';

export interface OIDCClient {
  id: string;
  name: string;
  clientId: string;
  clientSecret: string;
  redirectUris: string[];
  grantTypes: string[];
  responseTypes: string[];
  scopes: string[];
  tokenEndpointAuthMethod: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface AuthorizationCode {
  code: string;
  clientId: string;
  userId: string;
  redirectUri: string;
  scopes: string[];
  codeChallenge?: string;
  codeChallengeMethod?: string;
  expiresAt: Date;
  used: boolean;
}

export interface TokenResponse {
  access_token: string;
  token_type: string;
  expires_in: number;
  refresh_token?: string;
  id_token?: string;
  scope: string;
}

export interface UserInfo {
  sub: string;
  email?: string;
  email_verified?: boolean;
  name?: string;
  nickname?: string;
  picture?: string;
  given_name?: string;
  family_name?: string;
  updated_at?: number;
}

export class OIDCService {
  
  /**
   * 处理授权请求 (Authorization Code Flow)
   */
  async handleAuthorizationRequest(params: {
    response_type: string;
    client_id: string;
    redirect_uri: string;
    scope: string;
    state?: string;
    nonce?: string;
    code_challenge?: string;
    code_challenge_method?: string;
    prompt?: string;
    max_age?: number;
  }): Promise<{
    authorizationUrl: string;
    state: string;
  }> {
    try {
      // 验证客户端
      const client = await this.validateClient(params.client_id);
      if (!client) {
        throw new Error('无效的客户端ID');
      }

      // 验证重定向URI
      if (!client.redirectUris.includes(params.redirect_uri)) {
        throw new Error('无效的重定向URI');
      }

      // 验证响应类型
      if (!client.responseTypes.includes(params.response_type)) {
        throw new Error('不支持的响应类型');
      }

      // 验证权限范围
      const requestedScopes = params.scope.split(' ');
      const invalidScopes = requestedScopes.filter(scope => !client.scopes.includes(scope));
      if (invalidScopes.length > 0) {
        throw new Error(`无效的权限范围: ${invalidScopes.join(', ')}`);
      }

      // 生成状态参数
      const state = params.state || uuidv4();
      
      // 缓存授权请求信息
      const authRequest = {
        clientId: params.client_id,
        redirectUri: params.redirect_uri,
        scopes: requestedScopes,
        responseType: params.response_type,
        nonce: params.nonce,
        codeChallenge: params.code_challenge,
        codeChallengeMethod: params.code_challenge_method,
        prompt: params.prompt,
        maxAge: params.max_age,
        timestamp: Date.now()
      };

      await cacheService.setOAuthState(state, authRequest);

      // 构建授权URL
      const authorizationUrl = `/oauth2/authorize?${new URLSearchParams({
        response_type: params.response_type,
        client_id: params.client_id,
        redirect_uri: params.redirect_uri,
        scope: params.scope,
        state,
        ...(params.nonce && { nonce: params.nonce }),
        ...(params.code_challenge && { code_challenge: params.code_challenge }),
        ...(params.code_challenge_method && { code_challenge_method: params.code_challenge_method })
      }).toString()}`;

      logger.info('OIDC授权请求处理', {
        clientId: params.client_id,
        scopes: requestedScopes,
        responseType: params.response_type
      });

      return {
        authorizationUrl,
        state
      };

    } catch (error) {
      logger.error('OIDC授权请求失败', {
        error: error instanceof Error ? error.message : String(error),
        clientId: params.client_id
      });
      throw error;
    }
  }

  /**
   * 生成授权码
   */
  async generateAuthorizationCode(
    userId: string,
    clientId: string,
    redirectUri: string,
    scopes: string[],
    codeChallenge?: string,
    codeChallengeMethod?: string
  ): Promise<string> {
    try {
      const code = crypto.randomBytes(32).toString('base64url');
      const expiresAt = new Date(Date.now() + 10 * 60 * 1000); // 10分钟过期

      // 存储授权码
      await prisma.authorizationCode.create({
        data: {
          id: uuidv4(),
          code,
          clientId,
          userId,
          redirectUri,
          scopes: scopes.join(' '),
          codeChallenge,
          codeChallengeMethod,
          expiresAt,
          used: false
        }
      });

      logger.info('授权码生成成功', {
        userId,
        clientId,
        scopes,
        expiresAt
      });

      return code;

    } catch (error) {
      logger.error('授权码生成失败', {
        error: error instanceof Error ? error.message : String(error),
        userId,
        clientId
      });
      throw error;
    }
  }

  /**
   * 交换授权码获取令牌
   */
  async exchangeCodeForTokens(
    code: string,
    clientId: string,
    clientSecret: string,
    redirectUri: string,
    codeVerifier?: string
  ): Promise<TokenResponse> {
    try {
      // 验证客户端
      const client = await this.validateClientCredentials(clientId, clientSecret);
      if (!client) {
        throw new Error('客户端认证失败');
      }

      // 查找授权码
      const authCode = await prisma.authorizationCode.findFirst({
        where: {
          code,
          clientId,
          redirectUri,
          used: false,
          expiresAt: {
            gt: new Date()
          }
        },
        include: {
          user: true
        }
      });

      if (!authCode) {
        throw new Error('无效的授权码');
      }

      // 验证PKCE (如果使用)
      if (authCode.codeChallenge) {
        if (!codeVerifier) {
          throw new Error('缺少code_verifier');
        }

        const challenge = authCode.codeChallengeMethod === 'S256'
          ? crypto.createHash('sha256').update(codeVerifier).digest('base64url')
          : codeVerifier;

        if (challenge !== authCode.codeChallenge) {
          throw new Error('code_verifier验证失败');
        }
      }

      // 标记授权码为已使用
      await prisma.authorizationCode.update({
        where: { id: authCode.id },
        data: { used: true }
      });

      // 创建会话
      const session = await prisma.session.create({
        data: {
          id: uuidv4(),
          userId: authCode.userId,
          sessionToken: crypto.randomBytes(32).toString('hex'),
          authMethod: 'oidc',
          ipAddress: '127.0.0.1', // TODO: 从请求中获取真实IP
          userAgent: 'OIDC Client',
          expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24小时
          lastAccessedAt: new Date()
        }
      });

      // 生成令牌
      const scopes = authCode.scopes.split(' ');
      const accessToken = generateAccessToken(authCode.user, session.id);
      const refreshToken = generateRefreshToken(authCode.user, session.id);

      // 生成ID令牌 (如果请求了openid scope)
      let idToken: string | undefined;
      if (scopes.includes('openid')) {
        idToken = this.generateIdToken(authCode.user, clientId, scopes);
      }

      // 记录审计日志
      logAuditEvent(
        'oidc_token_exchange',
        'user',
        authCode.userId,
        {
          clientId,
          scopes,
          hasIdToken: !!idToken
        }
      );

      return {
        access_token: accessToken,
        token_type: 'Bearer',
        expires_in: 900, // 15分钟
        refresh_token: refreshToken,
        id_token: idToken,
        scope: authCode.scopes
      };

    } catch (error) {
      logger.error('令牌交换失败', {
        error: error instanceof Error ? error.message : String(error),
        clientId,
        code: code.substring(0, 8) + '...'
      });
      throw error;
    }
  }

  /**
   * 生成ID令牌
   */
  private generateIdToken(user: any, clientId: string, scopes: string[]): string {
    const now = Math.floor(Date.now() / 1000);
    
    const payload: any = {
      iss: getServerUrl(),
      sub: user.id,
      aud: clientId,
      exp: now + 3600, // 1小时
      iat: now,
      auth_time: now
    };

    // 根据权限范围添加声明
    if (scopes.includes('email')) {
      payload.email = user.email;
      payload.email_verified = user.emailVerified;
    }

    if (scopes.includes('profile')) {
      payload.name = user.nickname || `${user.firstName} ${user.lastName}`.trim();
      payload.nickname = user.nickname;
      payload.given_name = user.firstName;
      payload.family_name = user.lastName;
      payload.picture = user.avatar;
      payload.updated_at = Math.floor(new Date(user.updatedAt).getTime() / 1000);
    }

    return jwt.sign(payload, config.jwt.secret, { algorithm: 'RS256' });
  }

  /**
   * 刷新令牌流程
   * @param refreshToken 刷新令牌
   * @param clientId 客户端ID
   * @param clientSecret 客户端密钥
   * @param scope 可选的权限范围（用于缩小权限）
   */
  async refreshTokens(
    refreshToken: string,
    clientId: string,
    clientSecret: string,
    scope?: string
  ): Promise<TokenResponse> {
    try {
      // 验证客户端
      const client = await this.validateClient(clientId);
      if (!client || client.clientSecret !== clientSecret) {
        throw new Error('无效的客户端凭据');
      }

      // 验证刷新令牌
      let decoded;
      try {
        decoded = jwt.verify(refreshToken, config.jwt.refreshSecret) as any;
      } catch (error) {
        throw new Error('无效的刷新令牌');
      }

      // 检查令牌类型
      if (decoded.type !== 'refresh') {
        throw new Error('令牌类型错误');
      }

      // 检查令牌是否在黑名单中
      if (decoded.jti) {
        const isBlacklisted = await cacheService.isJWTBlacklisted(decoded.jti);
        if (isBlacklisted) {
          throw new Error('令牌已被撤销');
        }
      }

      // 获取用户信息
      const user = await prisma.user.findUnique({
        where: { id: decoded.userId }
      });

      if (!user || !user.isActive) {
        throw new Error('用户不存在或已被禁用');
      }

      // 处理权限范围
      let requestedScopes = decoded.scopes || ['openid'];
      if (scope) {
        const newScopes = scope.split(' ');
        // 只能缩小权限范围，不能扩大
        requestedScopes = requestedScopes.filter((s: string) => newScopes.includes(s));
      }

      // 撤销旧的刷新令牌（添加到黑名单）
      if (decoded.jti) {
        await cacheService.addJWTToBlacklist(decoded.jti, decoded.exp);
      }

      // 生成新的令牌对
      const newAccessToken = generateAccessToken(user, decoded.sessionId);
      const newRefreshToken = generateRefreshToken(user, decoded.sessionId);

      // 生成新的ID令牌（如果权限范围包含openid）
      let idToken;
      if (requestedScopes.includes('openid')) {
        idToken = this.generateIdToken(user, clientId, requestedScopes);
      }

      logger.info('令牌刷新成功', {
        userId: user.id,
        clientId,
        scopes: requestedScopes
      });

      return {
        access_token: newAccessToken,
        token_type: 'Bearer',
        expires_in: 900, // 15分钟
        refresh_token: newRefreshToken,
        id_token: idToken,
        scope: requestedScopes.join(' ')
      };

    } catch (error) {
      logger.error('令牌刷新失败', {
        error: error instanceof Error ? error.message : String(error),
        clientId
      });
      throw error;
    }
  }

  /**
   * 获取用户信息
   */
  async getUserInfo(accessToken: string): Promise<UserInfo> {
    try {
      // 验证访问令牌
      const decoded = jwt.verify(accessToken, config.jwt.secret) as any;
      
      // 获取用户信息
      const user = await prisma.user.findUnique({
        where: { id: decoded.userId }
      });

      if (!user) {
        throw new Error('用户不存在');
      }

      // 构建用户信息响应
      const userInfo: UserInfo = {
        sub: user.id
      };

      // 根据令牌中的权限范围添加信息
      if (decoded.scopes?.includes('email')) {
        userInfo.email = user.email;
        userInfo.email_verified = user.emailVerified;
      }

      if (decoded.scopes?.includes('profile')) {
        userInfo.name = user.nickname || `${user.firstName} ${user.lastName}`.trim();
        userInfo.nickname = user.nickname;
        userInfo.given_name = user.firstName;
        userInfo.family_name = user.lastName;
        userInfo.picture = user.avatar;
        userInfo.updated_at = Math.floor(new Date(user.updatedAt).getTime() / 1000);
      }

      return userInfo;

    } catch (error) {
      logger.error('获取用户信息失败', {
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * 验证客户端
   */
  async validateClient(clientId: string): Promise<OIDCClient | null> {
    try {
      const client = await prisma.oAuthClient.findUnique({
        where: { clientId }
      });

      return client as OIDCClient | null;
    } catch (error) {
      logger.error('客户端验证失败', { error, clientId });
      return null;
    }
  }

  /**
   * 验证客户端凭据
   */
  private async validateClientCredentials(
    clientId: string,
    clientSecret: string
  ): Promise<OIDCClient | null> {
    try {
      const client = await prisma.oAuthClient.findFirst({
        where: {
          clientId,
          clientSecret,
          isActive: true
        }
      });

      return client as OIDCClient | null;
    } catch (error) {
      logger.error('客户端凭据验证失败', { error, clientId });
      return null;
    }
  }



  /**
   * 客户端凭据流程
   * @param clientId 客户端ID
   * @param clientSecret 客户端密钥
   * @param scope 权限范围
   */
  async clientCredentialsGrant(
    clientId: string,
    clientSecret: string,
    scope?: string
  ): Promise<TokenResponse> {
    try {
      // 验证客户端
      const client = await this.validateClient(clientId);
      if (!client || client.clientSecret !== clientSecret) {
        throw new Error('无效的客户端凭据');
      }

      // 检查客户端是否支持客户端凭据流程
      if (!client.grantTypes.includes('client_credentials')) {
        throw new Error('客户端不支持客户端凭据流程');
      }

      // 处理权限范围
      const requestedScopes = scope ? scope.split(' ') : ['api'];

      // 验证客户端是否有权限访问请求的范围
      const allowedScopes = client.scopes || [];
      const invalidScopes = requestedScopes.filter(s => !allowedScopes.includes(s));
      if (invalidScopes.length > 0) {
        throw new Error(`客户端无权限访问范围: ${invalidScopes.join(', ')}`);
      }

      // 生成访问令牌（客户端凭据流程不生成刷新令牌）
      const payload = {
        userId: clientId, // 使用客户端ID作为主体
        email: `${clientId}@client.local`,
        type: 'access' as const,
        sessionId: uuidv4(),
        jti: uuidv4(),
        roles: ['client'],
        permissions: requestedScopes
      };

      const accessToken = jwt.sign(payload, config.jwt.secret, {
        expiresIn: '15m', // 15分钟
        issuer: getServerUrl(),
        audience: clientId
      });

      logger.info('客户端凭据授权成功', {
        clientId,
        scopes: requestedScopes
      });

      return {
        access_token: accessToken,
        token_type: 'Bearer',
        expires_in: 900, // 15分钟
        scope: requestedScopes.join(' ')
      };

    } catch (error) {
      logger.error('客户端凭据授权失败', {
        error: error instanceof Error ? error.message : String(error),
        clientId
      });
      throw error;
    }
  }

  /**
   * 令牌撤销 (RFC 7009)
   * @param token 要撤销的令牌
   * @param clientId 客户端ID
   * @param clientSecret 客户端密钥
   * @param tokenTypeHint 令牌类型提示
   */
  async revokeToken(
    token: string,
    clientId: string,
    clientSecret: string,
    tokenTypeHint?: string
  ): Promise<void> {
    try {
      // 验证客户端
      const client = await this.validateClient(clientId);
      if (!client || client.clientSecret !== clientSecret) {
        throw new Error('无效的客户端凭据');
      }

      // 尝试解码令牌以获取JTI
      let decoded;
      try {
        // 首先尝试作为访问令牌解码
        decoded = jwt.verify(token, config.jwt.secret) as any;
      } catch (error) {
        try {
          // 如果失败，尝试作为刷新令牌解码
          decoded = jwt.verify(token, config.jwt.refreshSecret) as any;
        } catch (refreshError) {
          // 如果都失败，令牌可能已经无效，但仍然返回成功（RFC 7009要求）
          logger.warn('尝试撤销无效令牌', { clientId });
          return;
        }
      }

      // 将令牌添加到黑名单
      if (decoded.jti) {
        await cacheService.addJWTToBlacklist(decoded.jti, decoded.exp);

        // 如果是刷新令牌，也要撤销相关的访问令牌
        if (decoded.type === 'refresh' && decoded.sessionId) {
          // TODO: 撤销同一会话的所有令牌
        }
      }

      logger.info('令牌撤销成功', {
        clientId,
        tokenType: decoded.type,
        userId: decoded.userId
      });

    } catch (error) {
      logger.error('令牌撤销失败', {
        error: error instanceof Error ? error.message : String(error),
        clientId
      });
      // RFC 7009: 即使撤销失败，也应该返回成功
      // 这里我们记录错误但不抛出异常
    }
  }

  /**
   * 隐式流程支持
   * @param params 授权参数
   * @param userId 用户ID
   */
  async handleImplicitFlow(params: {
    response_type: string;
    client_id: string;
    redirect_uri: string;
    scope: string;
    state?: string;
    nonce?: string;
  }, userId: string): Promise<string> {
    try {
      // 验证客户端
      const client = await this.validateClient(params.client_id);
      if (!client) {
        throw new Error('无效的客户端ID');
      }

      // 验证重定向URI
      if (!client.redirectUris.includes(params.redirect_uri)) {
        throw new Error('无效的重定向URI');
      }

      // 验证响应类型
      if (!client.responseTypes.includes(params.response_type)) {
        throw new Error('不支持的响应类型');
      }

      // 获取用户信息
      const user = await prisma.user.findUnique({
        where: { id: userId }
      });

      if (!user || !user.isActive) {
        throw new Error('用户不存在或已被禁用');
      }

      const scopes = params.scope.split(' ');
      const callbackUrl = new URL(params.redirect_uri);

      // 根据响应类型生成相应的令牌
      if (params.response_type === 'token') {
        // 只返回访问令牌
        const accessToken = generateAccessToken(user);
        callbackUrl.hash = `access_token=${accessToken}&token_type=Bearer&expires_in=900`;
      } else if (params.response_type === 'id_token') {
        // 只返回ID令牌
        const idToken = this.generateIdToken(user, params.client_id, scopes);
        callbackUrl.hash = `id_token=${idToken}`;
      } else if (params.response_type === 'id_token token') {
        // 返回访问令牌和ID令牌
        const accessToken = generateAccessToken(user);
        const idToken = this.generateIdToken(user, params.client_id, scopes);
        callbackUrl.hash = `access_token=${accessToken}&token_type=Bearer&expires_in=900&id_token=${idToken}`;
      }

      // 添加状态参数
      if (params.state) {
        callbackUrl.hash += `&state=${params.state}`;
      }

      logger.info('隐式流程授权成功', {
        userId,
        clientId: params.client_id,
        responseType: params.response_type,
        scopes
      });

      return callbackUrl.toString();

    } catch (error) {
      logger.error('隐式流程失败', {
        error: error instanceof Error ? error.message : String(error),
        clientId: params.client_id,
        userId
      });
      throw error;
    }
  }
}

// 创建单例实例
export const oidcService = new OIDCService();
