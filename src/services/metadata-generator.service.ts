/**
 * 元数据生成服务
 * 实现协议元数据自动生成，支持OIDC和SAML元数据导出
 */

import { config, getServerUrl } from '@/config';
import { logger } from '@/config/logger';
import { oidcProviderService } from './oidc-provider.service';
import { samlIdPService } from './saml-idp.service';
import { prisma } from '@/config/database';
import { create } from 'xmlbuilder2';
import { v4 as uuidv4 } from 'uuid';

/**
 * 元数据类型
 */
export type MetadataType = 'oidc' | 'saml' | 'oauth2' | 'federation';

/**
 * 联邦元数据配置
 */
interface FederationMetadata {
  entityId: string;
  organizationName: string;
  organizationDisplayName: string;
  organizationUrl: string;
  contactPersons: Array<{
    contactType: 'technical' | 'support' | 'administrative' | 'billing' | 'other';
    givenName?: string;
    surname?: string;
    emailAddress?: string;
    telephoneNumber?: string;
  }>;
  protocols: Array<{
    type: 'oidc' | 'saml' | 'oauth2';
    endpoints: Record<string, string>;
    metadata?: any;
  }>;
}

export class MetadataGeneratorService {
  
  /**
   * 生成 OpenID Connect 发现文档
   */
  async generateOIDCMetadata(): Promise<any> {
    try {
      const baseUrl = getServerUrl();
      
      // 获取动态配置
      const jwks = await oidcProviderService.getJWKS();
      
      const metadata = {
        issuer: baseUrl,
        authorization_endpoint: `${baseUrl}/oauth2/authorize`,
        token_endpoint: `${baseUrl}/oauth2/token`,
        userinfo_endpoint: `${baseUrl}/oauth2/userinfo`,
        jwks_uri: `${baseUrl}/.well-known/jwks.json`,
        
        // 撤销和内省端点
        revocation_endpoint: `${baseUrl}/oauth2/revoke`,
        introspection_endpoint: `${baseUrl}/oauth2/introspect`,
        
        // 登出端点
        end_session_endpoint: `${baseUrl}/oauth2/logout`,
        
        // 设备授权端点
        device_authorization_endpoint: `${baseUrl}/oauth2/device/auth`,
        
        // 推送授权请求端点
        pushed_authorization_request_endpoint: `${baseUrl}/oauth2/par`,
        
        // 支持的响应类型
        response_types_supported: [
          'code',
          'id_token',
          'token',
          'code id_token',
          'code token',
          'id_token token',
          'code id_token token'
        ],
        
        // 支持的授权类型
        grant_types_supported: [
          'authorization_code',
          'implicit',
          'refresh_token',
          'client_credentials',
          'urn:ietf:params:oauth:grant-type:device_code'
        ],
        
        // 支持的主题类型
        subject_types_supported: ['public', 'pairwise'],
        
        // 支持的ID令牌签名算法
        id_token_signing_alg_values_supported: [
          'RS256', 'RS384', 'RS512', 'ES256', 'ES384', 'ES512'
        ],
        
        // 支持的令牌端点认证方法
        token_endpoint_auth_methods_supported: [
          'client_secret_basic',
          'client_secret_post',
          'client_secret_jwt',
          'private_key_jwt',
          'none'
        ],
        
        // 支持的声明
        claims_supported: [
          'sub', 'iss', 'aud', 'exp', 'iat', 'auth_time', 'nonce', 'acr', 'amr', 'azp',
          'name', 'family_name', 'given_name', 'middle_name', 'nickname', 'preferred_username',
          'profile', 'picture', 'website', 'gender', 'birthdate', 'zoneinfo', 'locale', 'updated_at',
          'email', 'email_verified', 'phone_number', 'phone_number_verified', 'address'
        ],
        
        // 支持的范围
        scopes_supported: ['openid', 'profile', 'email', 'phone', 'address', 'offline_access'],
        
        // 支持的代码挑战方法
        code_challenge_methods_supported: ['S256'],
        
        // 其他功能
        request_parameter_supported: true,
        request_uri_parameter_supported: true,
        claims_parameter_supported: true,
        
        // 当前JWKS
        jwks: jwks,
        
        // 服务信息
        service_documentation: `${baseUrl}/docs`,
        op_policy_uri: `${baseUrl}/privacy`,
        op_tos_uri: `${baseUrl}/terms`,
        
        // 生成时间戳
        generated_at: new Date().toISOString()
      };

      logger.info('OIDC 元数据生成成功');
      return metadata;

    } catch (error) {
      logger.error('OIDC 元数据生成失败', { error });
      throw error;
    }
  }

  /**
   * 生成 SAML IdP 元数据
   */
  async generateSAMLMetadata(): Promise<string> {
    try {
      const metadata = samlIdPService.generateMetadata();
      
      logger.info('SAML 元数据生成成功');
      return metadata;

    } catch (error) {
      logger.error('SAML 元数据生成失败', { error });
      throw error;
    }
  }

  /**
   * 生成 OAuth 2.0 授权服务器元数据
   */
  async generateOAuth2Metadata(): Promise<any> {
    try {
      const baseUrl = getServerUrl();
      
      const metadata = {
        issuer: baseUrl,
        authorization_endpoint: `${baseUrl}/oauth2/authorize`,
        token_endpoint: `${baseUrl}/oauth2/token`,
        
        // 撤销和内省端点
        revocation_endpoint: `${baseUrl}/oauth2/revoke`,
        introspection_endpoint: `${baseUrl}/oauth2/introspect`,
        
        // 设备授权端点
        device_authorization_endpoint: `${baseUrl}/oauth2/device/auth`,
        
        // 推送授权请求端点
        pushed_authorization_request_endpoint: `${baseUrl}/oauth2/par`,
        
        // 支持的响应类型
        response_types_supported: [
          'code',
          'token'
        ],
        
        // 支持的授权类型
        grant_types_supported: [
          'authorization_code',
          'implicit',
          'refresh_token',
          'client_credentials',
          'urn:ietf:params:oauth:grant-type:device_code'
        ],
        
        // 支持的令牌端点认证方法
        token_endpoint_auth_methods_supported: [
          'client_secret_basic',
          'client_secret_post',
          'client_secret_jwt',
          'private_key_jwt',
          'none'
        ],
        
        // 支持的代码挑战方法
        code_challenge_methods_supported: ['S256'],
        
        // 支持的内省端点认证方法
        introspection_endpoint_auth_methods_supported: [
          'client_secret_basic',
          'client_secret_post',
          'private_key_jwt'
        ],
        
        // 支持的撤销端点认证方法
        revocation_endpoint_auth_methods_supported: [
          'client_secret_basic',
          'client_secret_post',
          'private_key_jwt'
        ],
        
        // 支持的范围
        scopes_supported: ['read', 'write', 'admin'],
        
        // 其他功能
        request_parameter_supported: true,
        request_uri_parameter_supported: true,
        require_request_uri_registration: false,
        require_pushed_authorization_requests: false,
        
        // 生成时间戳
        generated_at: new Date().toISOString()
      };

      logger.info('OAuth 2.0 元数据生成成功');
      return metadata;

    } catch (error) {
      logger.error('OAuth 2.0 元数据生成失败', { error });
      throw error;
    }
  }

  /**
   * 生成联邦元数据
   */
  async generateFederationMetadata(): Promise<string> {
    try {
      const baseUrl = getServerUrl();
      
      const federationConfig: FederationMetadata = {
        entityId: baseUrl,
        organizationName: config.organization?.name || 'Identity Provider',
        organizationDisplayName: config.organization?.displayName || 'Identity Provider',
        organizationUrl: config.organization?.url || baseUrl,
        contactPersons: [
          {
            contactType: 'technical',
            givenName: 'Technical',
            surname: 'Support',
            emailAddress: config.organization?.technicalContact || '<EMAIL>'
          },
          {
            contactType: 'support',
            givenName: 'User',
            surname: 'Support',
            emailAddress: config.organization?.supportContact || '<EMAIL>'
          }
        ],
        protocols: [
          {
            type: 'oidc',
            endpoints: {
              discovery: `${baseUrl}/.well-known/openid-configuration`,
              authorization: `${baseUrl}/oauth2/authorize`,
              token: `${baseUrl}/oauth2/token`,
              userinfo: `${baseUrl}/oauth2/userinfo`,
              jwks: `${baseUrl}/.well-known/jwks.json`
            }
          },
          {
            type: 'saml',
            endpoints: {
              metadata: `${baseUrl}/saml/metadata`,
              sso: `${baseUrl}/saml/sso`,
              slo: `${baseUrl}/saml/slo`
            }
          },
          {
            type: 'oauth2',
            endpoints: {
              authorization: `${baseUrl}/oauth2/authorize`,
              token: `${baseUrl}/oauth2/token`,
              revocation: `${baseUrl}/oauth2/revoke`,
              introspection: `${baseUrl}/oauth2/introspect`
            }
          }
        ]
      };

      // 生成联邦元数据 XML
      const metadata = this.generateFederationXML(federationConfig);
      
      logger.info('联邦元数据生成成功');
      return metadata;

    } catch (error) {
      logger.error('联邦元数据生成失败', { error });
      throw error;
    }
  }

  /**
   * 生成联邦元数据 XML
   */
  private generateFederationXML(config: FederationMetadata): string {
    const now = new Date();
    const validUntil = new Date(now.getTime() + 24 * 60 * 60 * 1000); // 24小时后过期

    const metadata = create({ version: '1.0', encoding: 'UTF-8' })
      .ele('md:EntitiesDescriptor', {
        'xmlns:md': 'urn:oasis:names:tc:SAML:2.0:metadata',
        'xmlns:ds': 'http://www.w3.org/2000/09/xmldsig#',
        'Name': 'Identity Provider Federation',
        'validUntil': validUntil.toISOString(),
        'cacheDuration': 'PT24H'
      })
      .ele('md:EntityDescriptor', {
        'entityID': config.entityId
      })
        .ele('md:Organization')
          .ele('md:OrganizationName', { 'xml:lang': 'en' }).txt(config.organizationName).up()
          .ele('md:OrganizationDisplayName', { 'xml:lang': 'en' }).txt(config.organizationDisplayName).up()
          .ele('md:OrganizationURL', { 'xml:lang': 'en' }).txt(config.organizationUrl).up()
        .up();

    // 添加联系人信息
    config.contactPersons.forEach(contact => {
      const contactElement = metadata.last()
        .ele('md:ContactPerson', { 'contactType': contact.contactType });
      
      if (contact.givenName) {
        contactElement.ele('md:GivenName').txt(contact.givenName);
      }
      if (contact.surname) {
        contactElement.ele('md:SurName').txt(contact.surname);
      }
      if (contact.emailAddress) {
        contactElement.ele('md:EmailAddress').txt(contact.emailAddress);
      }
      if (contact.telephoneNumber) {
        contactElement.ele('md:TelephoneNumber').txt(contact.telephoneNumber);
      }
    });

    return metadata.end({ prettyPrint: true });
  }

  /**
   * 获取所有支持的元数据类型
   */
  getSupportedMetadataTypes(): MetadataType[] {
    return ['oidc', 'saml', 'oauth2', 'federation'];
  }

  /**
   * 根据类型生成元数据
   */
  async generateMetadata(type: MetadataType): Promise<any> {
    switch (type) {
      case 'oidc':
        return await this.generateOIDCMetadata();
      case 'saml':
        return await this.generateSAMLMetadata();
      case 'oauth2':
        return await this.generateOAuth2Metadata();
      case 'federation':
        return await this.generateFederationMetadata();
      default:
        throw new Error(`不支持的元数据类型: ${type}`);
    }
  }
}

// 导出单例实例
export const metadataGeneratorService = new MetadataGeneratorService();
