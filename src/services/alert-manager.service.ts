/**
 * 告警管理服务
 * 统一管理系统告警，支持多种通知渠道和告警策略
 */

import { EventEmitter } from 'events';
import { logger } from '@/config/logger';
import nodemailer from 'nodemailer';

/**
 * 告警级别枚举
 */
export enum AlertSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

/**
 * 告警状态枚举
 */
export enum AlertStatus {
  ACTIVE = 'active',
  ACKNOWLEDGED = 'acknowledged',
  RESOLVED = 'resolved',
  SUPPRESSED = 'suppressed'
}

/**
 * 通知渠道枚举
 */
export enum NotificationChannel {
  EMAIL = 'email',
  WEBHOOK = 'webhook',
  SLACK = 'slack',
  SMS = 'sms',
  CONSOLE = 'console'
}

/**
 * 告警接口
 */
export interface Alert {
  id: string;
  title: string;
  description: string;
  severity: AlertSeverity;
  status: AlertStatus;
  source: string;
  category: string;
  timestamp: Date;
  resolvedAt?: Date;
  acknowledgedAt?: Date;
  acknowledgedBy?: string;
  metadata?: Record<string, any>;
  tags?: string[];
  fingerprint?: string; // 用于去重
}

/**
 * 告警规则接口
 */
export interface AlertRule {
  id: string;
  name: string;
  description: string;
  enabled: boolean;
  severity: AlertSeverity;
  source: string;
  category: string;
  conditions: AlertCondition[];
  actions: AlertAction[];
  cooldownPeriod: number; // 冷却期（毫秒）
  maxOccurrences?: number; // 最大发生次数
  tags?: string[];
}

/**
 * 告警条件接口
 */
export interface AlertCondition {
  type: 'metric' | 'log' | 'event';
  field: string;
  operator: 'gt' | 'lt' | 'eq' | 'ne' | 'contains' | 'regex';
  value: any;
  duration?: number; // 持续时间（毫秒）
}

/**
 * 告警动作接口
 */
export interface AlertAction {
  type: NotificationChannel;
  config: Record<string, any>;
  enabled: boolean;
  conditions?: {
    severity?: AlertSeverity[];
    timeRange?: {
      start: string; // HH:mm
      end: string;   // HH:mm
    };
    days?: number[]; // 0-6 (周日到周六)
  };
}

/**
 * 通知配置接口
 */
export interface NotificationConfig {
  email?: {
    host: string;
    port: number;
    secure: boolean;
    auth: {
      user: string;
      pass: string;
    };
    from: string;
    to: string[];
  };
  webhook?: {
    url: string;
    method: 'POST' | 'PUT';
    headers?: Record<string, string>;
    timeout: number;
  };
  slack?: {
    webhookUrl: string;
    channel: string;
    username: string;
  };
}

/**
 * 告警统计接口
 */
export interface AlertStats {
  total: number;
  active: number;
  resolved: number;
  acknowledged: number;
  suppressed: number;
  bySeverity: Record<AlertSeverity, number>;
  bySource: Record<string, number>;
  byCategory: Record<string, number>;
  recentAlerts: Alert[];
  topSources: Array<{ source: string; count: number }>;
}

/**
 * 告警管理服务
 */
export class AlertManagerService extends EventEmitter {
  private alerts: Map<string, Alert> = new Map();
  private alertRules: Map<string, AlertRule> = new Map();
  private notificationConfig: NotificationConfig = {};
  private alertHistory: Alert[] = [];
  private suppressionRules: Map<string, { until: Date; reason: string }> = new Map();
  private cooldownTracker: Map<string, Date> = new Map();
  private emailTransporter?: nodemailer.Transporter;

  constructor() {
    super();
    this.initializeNotificationChannels();
  }

  /**
   * 创建告警
   */
  createAlert(alertData: Omit<Alert, 'id' | 'timestamp' | 'status'>): Alert {
    const alert: Alert = {
      id: this.generateAlertId(),
      timestamp: new Date(),
      status: AlertStatus.ACTIVE,
      ...alertData
    };

    // 检查是否有相同的告警（去重）
    if (alert.fingerprint) {
      const existingAlert = this.findAlertByFingerprint(alert.fingerprint);
      if (existingAlert && existingAlert.status === AlertStatus.ACTIVE) {
        logger.debug('告警已存在，跳过创建', {
          fingerprint: alert.fingerprint,
          existingId: existingAlert.id
        });
        return existingAlert;
      }
    }

    // 检查抑制规则
    if (this.isAlertSuppressed(alert)) {
      alert.status = AlertStatus.SUPPRESSED;
      logger.info('告警被抑制', {
        alertId: alert.id,
        title: alert.title,
        severity: alert.severity
      });
    }

    // 添加到活跃告警
    this.alerts.set(alert.id, alert);

    // 添加到历史记录
    this.alertHistory.push(alert);
    if (this.alertHistory.length > 10000) {
      this.alertHistory.shift();
    }

    // 发出告警事件
    this.emit('alertCreated', alert);

    // 如果告警未被抑制，发送通知
    if (alert.status === AlertStatus.ACTIVE) {
      this.sendNotifications(alert);
    }

    logger.info('告警已创建', {
      alertId: alert.id,
      title: alert.title,
      severity: alert.severity,
      source: alert.source,
      status: alert.status
    });

    return alert;
  }

  /**
   * 解决告警
   */
  resolveAlert(alertId: string, resolvedBy?: string): boolean {
    const alert = this.alerts.get(alertId);
    if (!alert) {
      return false;
    }

    alert.status = AlertStatus.RESOLVED;
    alert.resolvedAt = new Date();
    
    if (resolvedBy) {
      alert.metadata = { ...alert.metadata, resolvedBy };
    }

    // 从活跃告警中移除
    this.alerts.delete(alertId);

    // 发出解决事件
    this.emit('alertResolved', alert);

    logger.info('告警已解决', {
      alertId: alert.id,
      title: alert.title,
      resolvedBy
    });

    return true;
  }

  /**
   * 确认告警
   */
  acknowledgeAlert(alertId: string, acknowledgedBy: string): boolean {
    const alert = this.alerts.get(alertId);
    if (!alert) {
      return false;
    }

    alert.status = AlertStatus.ACKNOWLEDGED;
    alert.acknowledgedAt = new Date();
    alert.acknowledgedBy = acknowledgedBy;

    // 发出确认事件
    this.emit('alertAcknowledged', alert);

    logger.info('告警已确认', {
      alertId: alert.id,
      title: alert.title,
      acknowledgedBy
    });

    return true;
  }

  /**
   * 抑制告警
   */
  suppressAlert(source: string, duration: number, reason: string): void {
    const until = new Date(Date.now() + duration);
    this.suppressionRules.set(source, { until, reason });

    // 抑制现有的活跃告警
    for (const alert of this.alerts.values()) {
      if (alert.source === source && alert.status === AlertStatus.ACTIVE) {
        alert.status = AlertStatus.SUPPRESSED;
        this.emit('alertSuppressed', alert);
      }
    }

    logger.info('告警源已被抑制', {
      source,
      until: until.toISOString(),
      reason
    });
  }

  /**
   * 添加告警规则
   */
  addAlertRule(rule: AlertRule): void {
    this.alertRules.set(rule.id, rule);
    
    logger.info('告警规则已添加', {
      ruleId: rule.id,
      name: rule.name,
      severity: rule.severity,
      enabled: rule.enabled
    });
  }

  /**
   * 移除告警规则
   */
  removeAlertRule(ruleId: string): boolean {
    const removed = this.alertRules.delete(ruleId);
    if (removed) {
      logger.info('告警规则已移除', { ruleId });
    }
    return removed;
  }

  /**
   * 获取活跃告警
   */
  getActiveAlerts(): Alert[] {
    return Array.from(this.alerts.values())
      .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
  }

  /**
   * 获取告警历史
   */
  getAlertHistory(limit = 100): Alert[] {
    return this.alertHistory
      .slice(-limit)
      .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
  }

  /**
   * 获取告警统计
   */
  getAlertStats(): AlertStats {
    const allAlerts = [...this.alerts.values(), ...this.alertHistory];
    
    const stats: AlertStats = {
      total: allAlerts.length,
      active: 0,
      resolved: 0,
      acknowledged: 0,
      suppressed: 0,
      bySeverity: {
        [AlertSeverity.LOW]: 0,
        [AlertSeverity.MEDIUM]: 0,
        [AlertSeverity.HIGH]: 0,
        [AlertSeverity.CRITICAL]: 0
      },
      bySource: {},
      byCategory: {},
      recentAlerts: [],
      topSources: []
    };

    const sourceCounts: Record<string, number> = {};

    for (const alert of allAlerts) {
      // 按状态统计
      switch (alert.status) {
        case AlertStatus.ACTIVE:
          stats.active++;
          break;
        case AlertStatus.RESOLVED:
          stats.resolved++;
          break;
        case AlertStatus.ACKNOWLEDGED:
          stats.acknowledged++;
          break;
        case AlertStatus.SUPPRESSED:
          stats.suppressed++;
          break;
      }

      // 按严重程度统计
      stats.bySeverity[alert.severity]++;

      // 按来源统计
      stats.bySource[alert.source] = (stats.bySource[alert.source] || 0) + 1;
      sourceCounts[alert.source] = (sourceCounts[alert.source] || 0) + 1;

      // 按类别统计
      stats.byCategory[alert.category] = (stats.byCategory[alert.category] || 0) + 1;
    }

    // 最近的告警
    stats.recentAlerts = allAlerts
      .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
      .slice(0, 10);

    // 最多告警的来源
    stats.topSources = Object.entries(sourceCounts)
      .map(([source, count]) => ({ source, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);

    return stats;
  }

  /**
   * 配置通知渠道
   */
  configureNotifications(config: NotificationConfig): void {
    this.notificationConfig = { ...this.notificationConfig, ...config };
    this.initializeNotificationChannels();
    
    logger.info('通知配置已更新', {
      channels: Object.keys(config)
    });
  }

  /**
   * 发送通知
   */
  private async sendNotifications(alert: Alert): Promise<void> {
    // 检查冷却期
    const cooldownKey = `${alert.source}_${alert.category}`;
    const lastNotification = this.cooldownTracker.get(cooldownKey);
    
    if (lastNotification && Date.now() - lastNotification.getTime() < 300000) { // 5分钟冷却期
      logger.debug('告警在冷却期内，跳过通知', {
        alertId: alert.id,
        cooldownKey
      });
      return;
    }

    // 更新冷却期
    this.cooldownTracker.set(cooldownKey, new Date());

    // 发送到各个通知渠道
    const notifications = [
      this.sendEmailNotification(alert),
      this.sendWebhookNotification(alert),
      this.sendConsoleNotification(alert)
    ];

    try {
      await Promise.allSettled(notifications);
    } catch (error) {
      logger.error('发送通知失败', {
        alertId: alert.id,
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  /**
   * 发送邮件通知
   */
  private async sendEmailNotification(alert: Alert): Promise<void> {
    if (!this.emailTransporter || !this.notificationConfig.email) {
      return;
    }

    try {
      const subject = `[${alert.severity.toUpperCase()}] ${alert.title}`;
      const html = this.generateEmailTemplate(alert);

      await this.emailTransporter.sendMail({
        from: this.notificationConfig.email.from,
        to: this.notificationConfig.email.to,
        subject,
        html
      });

      logger.info('邮件通知已发送', {
        alertId: alert.id,
        recipients: this.notificationConfig.email.to.length
      });

    } catch (error) {
      logger.error('邮件通知发送失败', {
        alertId: alert.id,
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  /**
   * 发送Webhook通知
   */
  private async sendWebhookNotification(alert: Alert): Promise<void> {
    if (!this.notificationConfig.webhook) {
      return;
    }

    try {
      // 使用动态导入避免在Node.js环境中的fetch问题
      const fetch = (await import('node-fetch')).default;

      const response = await fetch(this.notificationConfig.webhook.url, {
        method: this.notificationConfig.webhook.method,
        headers: {
          'Content-Type': 'application/json',
          ...this.notificationConfig.webhook.headers
        },
        body: JSON.stringify({
          alert,
          timestamp: new Date().toISOString()
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      logger.info('Webhook通知已发送', {
        alertId: alert.id,
        url: this.notificationConfig.webhook.url,
        status: response.status
      });

    } catch (error) {
      logger.error('Webhook通知发送失败', {
        alertId: alert.id,
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  /**
   * 发送控制台通知
   */
  private async sendConsoleNotification(alert: Alert): Promise<void> {
    const message = `🚨 [${alert.severity.toUpperCase()}] ${alert.title}`;
    const details = {
      alertId: alert.id,
      source: alert.source,
      category: alert.category,
      description: alert.description,
      timestamp: alert.timestamp.toISOString()
    };

    switch (alert.severity) {
      case AlertSeverity.CRITICAL:
        logger.error(message, details);
        break;
      case AlertSeverity.HIGH:
        logger.error(message, details);
        break;
      case AlertSeverity.MEDIUM:
        logger.warn(message, details);
        break;
      case AlertSeverity.LOW:
        logger.info(message, details);
        break;
    }
  }

  /**
   * 初始化通知渠道
   */
  private initializeNotificationChannels(): void {
    // 初始化邮件传输器
    if (this.notificationConfig.email) {
      this.emailTransporter = nodemailer.createTransporter({
        host: this.notificationConfig.email.host,
        port: this.notificationConfig.email.port,
        secure: this.notificationConfig.email.secure,
        auth: this.notificationConfig.email.auth
      });
    }
  }

  /**
   * 生成邮件模板
   */
  private generateEmailTemplate(alert: Alert): string {
    const severityColor = {
      [AlertSeverity.CRITICAL]: '#dc3545',
      [AlertSeverity.HIGH]: '#fd7e14',
      [AlertSeverity.MEDIUM]: '#ffc107',
      [AlertSeverity.LOW]: '#28a745'
    };

    return `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background-color: ${severityColor[alert.severity]}; color: white; padding: 20px; text-align: center;">
          <h1 style="margin: 0;">${alert.title}</h1>
          <p style="margin: 5px 0 0 0; font-size: 14px;">严重程度: ${alert.severity.toUpperCase()}</p>
        </div>
        <div style="padding: 20px; background-color: #f8f9fa;">
          <p><strong>描述:</strong> ${alert.description}</p>
          <p><strong>来源:</strong> ${alert.source}</p>
          <p><strong>类别:</strong> ${alert.category}</p>
          <p><strong>时间:</strong> ${alert.timestamp.toLocaleString()}</p>
          ${alert.metadata ? `<p><strong>详细信息:</strong> <pre>${JSON.stringify(alert.metadata, null, 2)}</pre></p>` : ''}
        </div>
        <div style="padding: 10px 20px; background-color: #e9ecef; text-align: center; font-size: 12px; color: #6c757d;">
          <p>此邮件由身份提供商系统自动发送</p>
        </div>
      </div>
    `;
  }

  /**
   * 检查告警是否被抑制
   */
  private isAlertSuppressed(alert: Alert): boolean {
    const suppression = this.suppressionRules.get(alert.source);
    if (!suppression) {
      return false;
    }

    if (new Date() > suppression.until) {
      this.suppressionRules.delete(alert.source);
      return false;
    }

    return true;
  }

  /**
   * 根据指纹查找告警
   */
  private findAlertByFingerprint(fingerprint: string): Alert | undefined {
    for (const alert of this.alerts.values()) {
      if (alert.fingerprint === fingerprint) {
        return alert;
      }
    }
    return undefined;
  }

  /**
   * 生成告警ID
   */
  private generateAlertId(): string {
    return `alert_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`;
  }

  /**
   * 清理资源
   */
  cleanup(): void {
    this.alerts.clear();
    this.alertRules.clear();
    this.alertHistory = [];
    this.suppressionRules.clear();
    this.cooldownTracker.clear();
    this.removeAllListeners();
    
    logger.info('告警管理器清理完成');
  }
}

// 创建单例实例
export const alertManager = new AlertManagerService();
