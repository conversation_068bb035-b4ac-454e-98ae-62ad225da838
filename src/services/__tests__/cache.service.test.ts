/**
 * 缓存服务测试
 */

import { cacheService } from '../cache.service';
import { redisService } from '../redis.service';
import { logger } from '@/config/logger';

// Mock Redis服务
jest.mock('../redis.service', () => ({
  redisService: {
    set: jest.fn(),
    get: jest.fn(),
    del: jest.fn(),
    exists: jest.fn(),
    expire: jest.fn(),
    ttl: jest.fn(),
    keys: jest.fn(),
    incr: jest.fn(),
    incrby: jest.fn()
  }
}));

// Mock logger
jest.mock('@/config/logger', () => ({
  logger: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn()
  }
}));

const mockRedisService = redisService as jest.Mocked<typeof redisService>;

describe('CacheService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('基本缓存操作', () => {
    test('应该能够设置缓存', async () => {
      mockRedisService.set.mockResolvedValue(undefined);

      await cacheService.set('test-key', 'test-value', 60);

      expect(mockRedisService.set).toHaveBeenCalledWith('cache:test-key', 'test-value', 60);
    });

    test('应该能够获取缓存', async () => {
      mockRedisService.get.mockResolvedValue('test-value');

      const result = await cacheService.get('test-key');

      expect(mockRedisService.get).toHaveBeenCalledWith('cache:test-key');
      expect(result).toBe('test-value');
    });

    test('应该能够删除缓存', async () => {
      mockRedisService.del.mockResolvedValue(1);

      const result = await cacheService.del('test-key');

      expect(mockRedisService.del).toHaveBeenCalledWith('cache:test-key');
      expect(result).toBe(true);
    });

    test('应该能够检查缓存是否存在', async () => {
      mockRedisService.exists.mockResolvedValue(true);

      const result = await cacheService.exists('test-key');

      expect(mockRedisService.exists).toHaveBeenCalledWith('cache:test-key');
      expect(result).toBe(true);
    });

    test('应该能够设置过期时间', async () => {
      mockRedisService.expire.mockResolvedValue(true);

      const result = await cacheService.expire('test-key', 60);

      expect(mockRedisService.expire).toHaveBeenCalledWith('cache:test-key', 60);
      expect(result).toBe(true);
    });

    test('应该能够获取TTL', async () => {
      mockRedisService.ttl.mockResolvedValue(60);

      const result = await cacheService.ttl('test-key');

      expect(mockRedisService.ttl).toHaveBeenCalledWith('cache:test-key');
      expect(result).toBe(60);
    });
  });

  describe('用户会话缓存', () => {
    test('应该能够设置用户会话', async () => {
      mockRedisService.set.mockResolvedValue(undefined);
      const sessionData = {
        userId: 'user-123',
        sessionId: 'session-456',
        deviceInfo: { browser: 'Chrome' }
      };

      await cacheService.setUserSession('session-456', sessionData, 3600);

      expect(mockRedisService.set).toHaveBeenCalledWith(
        'session:session-456',
        sessionData,
        3600
      );
    });

    test('应该能够获取用户会话', async () => {
      const sessionData = {
        userId: 'user-123',
        sessionId: 'session-456',
        deviceInfo: { browser: 'Chrome' }
      };
      mockRedisService.get.mockResolvedValue(sessionData);

      const result = await cacheService.getUserSession('session-456');

      expect(mockRedisService.get).toHaveBeenCalledWith('session:session-456');
      expect(result).toEqual(sessionData);
    });

    test('应该能够删除用户会话', async () => {
      mockRedisService.del.mockResolvedValue(1);

      const result = await cacheService.deleteUserSession('session-456');

      expect(mockRedisService.del).toHaveBeenCalledWith('session:session-456');
      expect(result).toBe(true);
    });
  });

  describe('JWT黑名单', () => {
    test('应该能够添加JWT到黑名单', async () => {
      mockRedisService.set.mockResolvedValue(undefined);

      await cacheService.addToJWTBlacklist('jwt-token', 3600);

      expect(mockRedisService.set).toHaveBeenCalledWith(
        'jwt_blacklist:jwt-token',
        true,
        3600
      );
    });

    test('应该能够检查JWT是否在黑名单', async () => {
      mockRedisService.exists.mockResolvedValue(true);

      const result = await cacheService.isJWTBlacklisted('jwt-token');

      expect(mockRedisService.exists).toHaveBeenCalledWith('jwt_blacklist:jwt-token');
      expect(result).toBe(true);
    });

    test('应该能够从黑名单移除JWT', async () => {
      mockRedisService.del.mockResolvedValue(1);

      const result = await cacheService.removeFromJWTBlacklist('jwt-token');

      expect(mockRedisService.del).toHaveBeenCalledWith('jwt_blacklist:jwt-token');
      expect(result).toBe(true);
    });
  });

  describe('速率限制', () => {
    test('应该能够检查速率限制', async () => {
      mockRedisService.get.mockResolvedValue('5');
      mockRedisService.ttl.mockResolvedValue(30);

      const result = await cacheService.checkRateLimit('user-123', 10, 60);

      expect(result.allowed).toBe(true);
      expect(result.remaining).toBe(5);
      expect(result.resetTime).toBe(30);
    });

    test('应该能够递增速率限制计数器', async () => {
      mockRedisService.incr.mockResolvedValue(1);
      mockRedisService.expire.mockResolvedValue(true);

      await cacheService.incrementRateLimit('user-123', 60);

      expect(mockRedisService.incr).toHaveBeenCalledWith('rate_limit:user-123');
      expect(mockRedisService.expire).toHaveBeenCalledWith('rate_limit:user-123', 60);
    });

    test('应该拒绝超过限制的请求', async () => {
      mockRedisService.get.mockResolvedValue('10');
      mockRedisService.ttl.mockResolvedValue(30);

      const result = await cacheService.checkRateLimit('user-123', 10, 60);

      expect(result.allowed).toBe(false);
      expect(result.remaining).toBe(0);
    });
  });

  describe('OAuth状态缓存', () => {
    test('应该能够设置OAuth状态', async () => {
      mockRedisService.set.mockResolvedValue(undefined);
      const stateData = {
        clientId: 'client-123',
        redirectUri: 'https://example.com/callback',
        scope: 'read write'
      };

      await cacheService.setOAuthState('state-456', stateData, 600);

      expect(mockRedisService.set).toHaveBeenCalledWith(
        'oauth_state:state-456',
        stateData,
        600
      );
    });

    test('应该能够获取OAuth状态', async () => {
      const stateData = {
        clientId: 'client-123',
        redirectUri: 'https://example.com/callback',
        scope: 'read write'
      };
      mockRedisService.get.mockResolvedValue(stateData);

      const result = await cacheService.getOAuthState('state-456');

      expect(mockRedisService.get).toHaveBeenCalledWith('oauth_state:state-456');
      expect(result).toEqual(stateData);
    });

    test('应该能够删除OAuth状态', async () => {
      mockRedisService.del.mockResolvedValue(1);

      const result = await cacheService.deleteOAuthState('state-456');

      expect(mockRedisService.del).toHaveBeenCalledWith('oauth_state:state-456');
      expect(result).toBe(true);
    });
  });

  describe('批量操作', () => {
    test('应该能够批量删除缓存', async () => {
      mockRedisService.keys.mockResolvedValue(['cache:key1', 'cache:key2', 'cache:key3']);
      mockRedisService.del.mockResolvedValue(3);

      const result = await cacheService.deletePattern('key*');

      expect(mockRedisService.keys).toHaveBeenCalledWith('cache:key*');
      expect(mockRedisService.del).toHaveBeenCalledWith(['cache:key1', 'cache:key2', 'cache:key3']);
      expect(result).toBe(3);
    });

    test('应该能够清空所有缓存', async () => {
      mockRedisService.keys.mockResolvedValue(['cache:key1', 'cache:key2']);
      mockRedisService.del.mockResolvedValue(2);

      const result = await cacheService.clear();

      expect(mockRedisService.keys).toHaveBeenCalledWith('cache:*');
      expect(result).toBe(2);
    });
  });

  describe('缓存统计', () => {
    test('应该能够获取缓存统计', async () => {
      mockRedisService.keys.mockResolvedValue(['cache:key1', 'cache:key2']);

      const stats = await cacheService.getStats();

      expect(stats.totalKeys).toBe(2);
      expect(stats.keysByPrefix).toHaveProperty('cache');
    });

    test('应该能够记录缓存命中', async () => {
      mockRedisService.incr.mockResolvedValue(1);

      await cacheService.recordHit('test-key');

      expect(mockRedisService.incr).toHaveBeenCalledWith('cache_stats:hits:test-key');
    });

    test('应该能够记录缓存未命中', async () => {
      mockRedisService.incr.mockResolvedValue(1);

      await cacheService.recordMiss('test-key');

      expect(mockRedisService.incr).toHaveBeenCalledWith('cache_stats:misses:test-key');
    });
  });

  describe('错误处理', () => {
    test('设置缓存失败时应该记录错误', async () => {
      const error = new Error('Redis connection failed');
      mockRedisService.set.mockRejectedValue(error);

      await expect(cacheService.set('test-key', 'test-value')).rejects.toThrow();
      expect(logger.error).toHaveBeenCalledWith('缓存设置失败', {
        key: 'test-key',
        error: 'Redis connection failed'
      });
    });

    test('获取缓存失败时应该记录错误', async () => {
      const error = new Error('Redis connection failed');
      mockRedisService.get.mockRejectedValue(error);

      await expect(cacheService.get('test-key')).rejects.toThrow();
      expect(logger.error).toHaveBeenCalledWith('缓存获取失败', {
        key: 'test-key',
        error: 'Redis connection failed'
      });
    });
  });

  describe('缓存预热', () => {
    test('应该能够预热缓存', async () => {
      mockRedisService.set.mockResolvedValue(undefined);
      const warmupData = {
        'key1': 'value1',
        'key2': 'value2'
      };

      await cacheService.warmup(warmupData, 3600);

      expect(mockRedisService.set).toHaveBeenCalledTimes(2);
      expect(mockRedisService.set).toHaveBeenCalledWith('cache:key1', 'value1', 3600);
      expect(mockRedisService.set).toHaveBeenCalledWith('cache:key2', 'value2', 3600);
    });
  });
});
