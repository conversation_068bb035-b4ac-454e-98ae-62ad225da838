/**
 * Redis服务测试
 */

import { redisService } from '../redis.service';
import { logger } from '@/config/logger';

// Mock Redis客户端
const mockRedisClient = {
  set: jest.fn(),
  get: jest.fn(),
  del: jest.fn(),
  exists: jest.fn(),
  expire: jest.fn(),
  ttl: jest.fn(),
  incr: jest.fn(),
  incrby: jest.fn(),
  decr: jest.fn(),
  decrby: jest.fn(),
  keys: jest.fn(),
  flushdb: jest.fn(),
  ping: jest.fn(),
  info: jest.fn(),
  quit: jest.fn(),
  disconnect: jest.fn(),
  on: jest.fn(),
  off: jest.fn(),
  removeAllListeners: jest.fn()
};

// Mock logger
jest.mock('@/config/logger', () => ({
  logger: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn()
  }
}));

describe('RedisService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // 设置Redis客户端mock
    (redisService as any).client = mockRedisClient;
  });

  describe('基本操作', () => {
    test('应该能够设置键值对', async () => {
      mockRedisClient.set.mockResolvedValue('OK');
      mockRedisClient.expire.mockResolvedValue(1);

      await redisService.set('test-key', 'test-value', 60);

      expect(mockRedisClient.set).toHaveBeenCalledWith('test-key', '"test-value"');
      expect(mockRedisClient.expire).toHaveBeenCalledWith('test-key', 60);
    });

    test('应该能够获取值', async () => {
      mockRedisClient.get.mockResolvedValue('"test-value"');

      const result = await redisService.get('test-key');

      expect(mockRedisClient.get).toHaveBeenCalledWith('test-key');
      expect(result).toBe('test-value');
    });

    test('应该能够删除键', async () => {
      mockRedisClient.del.mockResolvedValue(1);

      const result = await redisService.del('test-key');

      expect(mockRedisClient.del).toHaveBeenCalledWith('test-key');
      expect(result).toBe(1);
    });

    test('应该能够检查键是否存在', async () => {
      mockRedisClient.exists.mockResolvedValue(1);

      const result = await redisService.exists('test-key');

      expect(mockRedisClient.exists).toHaveBeenCalledWith('test-key');
      expect(result).toBe(true);
    });

    test('应该能够设置过期时间', async () => {
      mockRedisClient.expire.mockResolvedValue(1);

      const result = await redisService.expire('test-key', 60);

      expect(mockRedisClient.expire).toHaveBeenCalledWith('test-key', 60);
      expect(result).toBe(true);
    });

    test('应该能够获取TTL', async () => {
      mockRedisClient.ttl.mockResolvedValue(60);

      const result = await redisService.ttl('test-key');

      expect(mockRedisClient.ttl).toHaveBeenCalledWith('test-key');
      expect(result).toBe(60);
    });
  });

  describe('计数器操作', () => {
    test('应该能够递增计数器', async () => {
      mockRedisClient.incr.mockResolvedValue(1);

      const result = await redisService.incr('counter-key');

      expect(mockRedisClient.incr).toHaveBeenCalledWith('counter-key');
      expect(result).toBe(1);
    });

    test('应该能够按指定值递增', async () => {
      mockRedisClient.incrby.mockResolvedValue(5);

      const result = await redisService.incrby('counter-key', 5);

      expect(mockRedisClient.incrby).toHaveBeenCalledWith('counter-key', 5);
      expect(result).toBe(5);
    });

    test('应该能够递减计数器', async () => {
      mockRedisClient.decr.mockResolvedValue(0);

      const result = await redisService.decr('counter-key');

      expect(mockRedisClient.decr).toHaveBeenCalledWith('counter-key');
      expect(result).toBe(0);
    });

    test('应该能够按指定值递减', async () => {
      mockRedisClient.decrby.mockResolvedValue(5);

      const result = await redisService.decrby('counter-key', 3);

      expect(mockRedisClient.decrby).toHaveBeenCalledWith('counter-key', 3);
      expect(result).toBe(5);
    });
  });

  describe('批量操作', () => {
    test('应该能够获取匹配的键', async () => {
      mockRedisClient.keys.mockResolvedValue(['key1', 'key2', 'key3']);

      const result = await redisService.keys('key*');

      expect(mockRedisClient.keys).toHaveBeenCalledWith('key*');
      expect(result).toEqual(['key1', 'key2', 'key3']);
    });

    test('应该能够清空数据库', async () => {
      mockRedisClient.flushdb.mockResolvedValue('OK');

      await redisService.flushdb();

      expect(mockRedisClient.flushdb).toHaveBeenCalled();
    });
  });

  describe('连接管理', () => {
    test('应该能够ping Redis', async () => {
      mockRedisClient.ping.mockResolvedValue('PONG');

      const result = await redisService.ping();

      expect(mockRedisClient.ping).toHaveBeenCalled();
      expect(result).toBe('PONG');
    });

    test('应该能够获取Redis信息', async () => {
      const mockInfo = 'redis_version:6.2.0\r\nredis_mode:standalone';
      mockRedisClient.info.mockResolvedValue(mockInfo);

      const result = await redisService.info();

      expect(mockRedisClient.info).toHaveBeenCalled();
      expect(result).toBe(mockInfo);
    });

    test('应该能够断开连接', async () => {
      mockRedisClient.quit.mockResolvedValue('OK');

      await redisService.disconnect();

      expect(mockRedisClient.quit).toHaveBeenCalled();
    });
  });

  describe('错误处理', () => {
    test('设置值时应该处理错误', async () => {
      const error = new Error('Redis connection failed');
      mockRedisClient.set.mockRejectedValue(error);

      await expect(redisService.set('test-key', 'test-value')).rejects.toThrow('Redis connection failed');
      expect(logger.error).toHaveBeenCalledWith('Redis设置失败', {
        key: 'test-key',
        error: 'Redis connection failed'
      });
    });

    test('获取值时应该处理错误', async () => {
      const error = new Error('Redis connection failed');
      mockRedisClient.get.mockRejectedValue(error);

      await expect(redisService.get('test-key')).rejects.toThrow('Redis connection failed');
      expect(logger.error).toHaveBeenCalledWith('Redis获取失败', {
        key: 'test-key',
        error: 'Redis connection failed'
      });
    });

    test('删除键时应该处理错误', async () => {
      const error = new Error('Redis connection failed');
      mockRedisClient.del.mockRejectedValue(error);

      await expect(redisService.del('test-key')).rejects.toThrow('Redis connection failed');
      expect(logger.error).toHaveBeenCalledWith('Redis删除失败', {
        key: 'test-key',
        error: 'Redis connection failed'
      });
    });
  });

  describe('JSON序列化', () => {
    test('应该正确序列化对象', async () => {
      mockRedisClient.set.mockResolvedValue('OK');
      const testObject = { name: 'test', value: 123 };

      await redisService.set('test-key', testObject);

      expect(mockRedisClient.set).toHaveBeenCalledWith('test-key', JSON.stringify(testObject));
    });

    test('应该正确反序列化对象', async () => {
      const testObject = { name: 'test', value: 123 };
      mockRedisClient.get.mockResolvedValue(JSON.stringify(testObject));

      const result = await redisService.get('test-key');

      expect(result).toEqual(testObject);
    });

    test('应该处理无效JSON', async () => {
      mockRedisClient.get.mockResolvedValue('invalid-json');

      const result = await redisService.get('test-key');

      expect(result).toBe('invalid-json');
    });

    test('应该处理null值', async () => {
      mockRedisClient.get.mockResolvedValue(null);

      const result = await redisService.get('test-key');

      expect(result).toBeNull();
    });
  });

  describe('事件处理', () => {
    test('应该能够添加事件监听器', () => {
      const handler = jest.fn();
      redisService.on('connect', handler);

      expect(mockRedisClient.on).toHaveBeenCalledWith('connect', handler);
    });

    test('应该能够移除事件监听器', () => {
      const handler = jest.fn();
      redisService.off('connect', handler);

      expect(mockRedisClient.off).toHaveBeenCalledWith('connect', handler);
    });

    test('应该能够移除所有事件监听器', () => {
      redisService.removeAllListeners();

      expect(mockRedisClient.removeAllListeners).toHaveBeenCalled();
    });
  });

  describe('健康检查', () => {
    test('应该返回健康状态', async () => {
      mockRedisClient.ping.mockResolvedValue('PONG');
      mockRedisClient.info.mockResolvedValue('redis_version:6.2.0');

      const health = await redisService.getHealthStatus();

      expect(health.status).toBe('healthy');
      expect(health.connected).toBe(true);
      expect(health.version).toBe('6.2.0');
    });

    test('应该处理连接失败', async () => {
      mockRedisClient.ping.mockRejectedValue(new Error('Connection failed'));

      const health = await redisService.getHealthStatus();

      expect(health.status).toBe('unhealthy');
      expect(health.connected).toBe(false);
      expect(health.error).toBe('Connection failed');
    });
  });
});
