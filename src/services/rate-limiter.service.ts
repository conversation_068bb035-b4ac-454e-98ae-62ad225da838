/**
 * 高级速率限制服务
 * 提供多种速率限制算法和策略
 */

import { redisService } from './redis.service';
import { logger } from '@/config/logger';
import { RedisKeys, RedisTTL } from '@/config/redis';

/**
 * 速率限制算法类型
 */
export enum RateLimitAlgorithm {
  TOKEN_BUCKET = 'token_bucket',       // 令牌桶算法
  LEAKY_BUCKET = 'leaky_bucket',       // 漏桶算法
  FIXED_WINDOW = 'fixed_window',       // 固定窗口算法
  SLIDING_WINDOW = 'sliding_window'    // 滑动窗口算法
}

/**
 * 速率限制配置
 */
export interface RateLimitConfig {
  algorithm: RateLimitAlgorithm;
  limit: number;                       // 限制数量
  windowMs: number;                    // 时间窗口（毫秒）
  keyGenerator?: (req: any) => string; // 自定义键生成器
  skipSuccessfulRequests?: boolean;    // 跳过成功请求
  skipFailedRequests?: boolean;        // 跳过失败请求
  message?: string;                    // 限制消息
  headers?: boolean;                   // 是否返回头部信息
}

/**
 * 速率限制结果
 */
export interface RateLimitResult {
  allowed: boolean;                    // 是否允许
  remaining: number;                   // 剩余次数
  resetTime: number;                   // 重置时间戳
  totalHits: number;                   // 总命中次数
  retryAfter?: number;                 // 重试等待时间（秒）
}

/**
 * 令牌桶状态
 */
interface TokenBucketState {
  tokens: number;                      // 当前令牌数
  lastRefill: number;                  // 上次补充时间
  capacity: number;                    // 桶容量
  refillRate: number;                  // 补充速率（令牌/秒）
}

/**
 * 漏桶状态
 */
interface LeakyBucketState {
  volume: number;                      // 当前容量
  lastLeak: number;                    // 上次漏出时间
  capacity: number;                    // 桶容量
  leakRate: number;                    // 漏出速率（请求/秒）
}

/**
 * 滑动窗口状态
 */
interface SlidingWindowState {
  requests: number[];                  // 请求时间戳数组
  windowMs: number;                    // 窗口大小
  limit: number;                       // 限制数量
}

/**
 * 高级速率限制服务
 */
export class RateLimiterService {
  
  /**
   * 检查速率限制
   */
  async checkRateLimit(
    key: string,
    config: RateLimitConfig
  ): Promise<RateLimitResult> {
    switch (config.algorithm) {
      case RateLimitAlgorithm.TOKEN_BUCKET:
        return await this.checkTokenBucket(key, config);
      
      case RateLimitAlgorithm.LEAKY_BUCKET:
        return await this.checkLeakyBucket(key, config);
      
      case RateLimitAlgorithm.FIXED_WINDOW:
        return await this.checkFixedWindow(key, config);
      
      case RateLimitAlgorithm.SLIDING_WINDOW:
        return await this.checkSlidingWindow(key, config);
      
      default:
        throw new Error(`不支持的速率限制算法: ${config.algorithm}`);
    }
  }

  /**
   * 令牌桶算法
   */
  private async checkTokenBucket(
    key: string,
    config: RateLimitConfig
  ): Promise<RateLimitResult> {
    const bucketKey = RedisKeys.RATE_LIMIT_BUCKET(key);
    const now = Date.now();
    
    try {
      // 获取当前桶状态
      let bucketState = await redisService.get<TokenBucketState>(bucketKey);
      
      if (!bucketState) {
        // 初始化桶状态
        bucketState = {
          tokens: config.limit,
          lastRefill: now,
          capacity: config.limit,
          refillRate: config.limit / (config.windowMs / 1000) // 令牌/秒
        };
      }

      // 计算需要补充的令牌数
      const timePassed = (now - bucketState.lastRefill) / 1000; // 秒
      const tokensToAdd = Math.floor(timePassed * bucketState.refillRate);
      
      // 补充令牌（不超过容量）
      bucketState.tokens = Math.min(bucketState.capacity, bucketState.tokens + tokensToAdd);
      bucketState.lastRefill = now;

      // 检查是否有可用令牌
      if (bucketState.tokens >= 1) {
        bucketState.tokens -= 1;
        
        // 保存状态
        await redisService.set(bucketKey, bucketState, Math.ceil(config.windowMs / 1000));
        
        return {
          allowed: true,
          remaining: bucketState.tokens,
          resetTime: now + (config.windowMs - (bucketState.capacity - bucketState.tokens) * 1000 / bucketState.refillRate),
          totalHits: bucketState.capacity - bucketState.tokens
        };
      } else {
        // 保存状态
        await redisService.set(bucketKey, bucketState, Math.ceil(config.windowMs / 1000));
        
        const retryAfter = Math.ceil((1 - bucketState.tokens) / bucketState.refillRate);
        
        return {
          allowed: false,
          remaining: 0,
          resetTime: now + retryAfter * 1000,
          totalHits: bucketState.capacity,
          retryAfter
        };
      }

    } catch (error) {
      logger.error('令牌桶算法检查失败', { key, error: error.message });
      // 出错时允许请求
      return {
        allowed: true,
        remaining: config.limit,
        resetTime: now + config.windowMs,
        totalHits: 0
      };
    }
  }

  /**
   * 漏桶算法
   */
  private async checkLeakyBucket(
    key: string,
    config: RateLimitConfig
  ): Promise<RateLimitResult> {
    const bucketKey = RedisKeys.RATE_LIMIT_BUCKET(key);
    const now = Date.now();
    
    try {
      // 获取当前桶状态
      let bucketState = await redisService.get<LeakyBucketState>(bucketKey);
      
      if (!bucketState) {
        // 初始化桶状态
        bucketState = {
          volume: 0,
          lastLeak: now,
          capacity: config.limit,
          leakRate: config.limit / (config.windowMs / 1000) // 请求/秒
        };
      }

      // 计算漏出的请求数
      const timePassed = (now - bucketState.lastLeak) / 1000; // 秒
      const leaked = Math.floor(timePassed * bucketState.leakRate);
      
      // 更新桶容量（不低于0）
      bucketState.volume = Math.max(0, bucketState.volume - leaked);
      bucketState.lastLeak = now;

      // 检查是否可以添加新请求
      if (bucketState.volume < bucketState.capacity) {
        bucketState.volume += 1;
        
        // 保存状态
        await redisService.set(bucketKey, bucketState, Math.ceil(config.windowMs / 1000));
        
        return {
          allowed: true,
          remaining: bucketState.capacity - bucketState.volume,
          resetTime: now + (bucketState.volume * 1000 / bucketState.leakRate),
          totalHits: bucketState.volume
        };
      } else {
        // 保存状态
        await redisService.set(bucketKey, bucketState, Math.ceil(config.windowMs / 1000));
        
        const retryAfter = Math.ceil(1 / bucketState.leakRate);
        
        return {
          allowed: false,
          remaining: 0,
          resetTime: now + retryAfter * 1000,
          totalHits: bucketState.capacity,
          retryAfter
        };
      }

    } catch (error) {
      logger.error('漏桶算法检查失败', { key, error: error.message });
      // 出错时允许请求
      return {
        allowed: true,
        remaining: config.limit,
        resetTime: now + config.windowMs,
        totalHits: 0
      };
    }
  }

  /**
   * 固定窗口算法
   */
  private async checkFixedWindow(
    key: string,
    config: RateLimitConfig
  ): Promise<RateLimitResult> {
    const now = Date.now();
    const windowStart = Math.floor(now / config.windowMs) * config.windowMs;
    const windowKey = RedisKeys.RATE_LIMIT_WINDOW(key, windowStart);
    
    try {
      // 获取当前窗口的请求计数
      const currentCount = await redisService.get<number>(windowKey) || 0;
      
      if (currentCount < config.limit) {
        // 增加计数
        const newCount = await redisService.incr(windowKey);
        
        // 设置过期时间
        if (newCount === 1) {
          await redisService.expire(windowKey, Math.ceil(config.windowMs / 1000));
        }
        
        return {
          allowed: true,
          remaining: config.limit - newCount,
          resetTime: windowStart + config.windowMs,
          totalHits: newCount
        };
      } else {
        const retryAfter = Math.ceil((windowStart + config.windowMs - now) / 1000);
        
        return {
          allowed: false,
          remaining: 0,
          resetTime: windowStart + config.windowMs,
          totalHits: currentCount,
          retryAfter
        };
      }

    } catch (error) {
      logger.error('固定窗口算法检查失败', { key, error: error.message });
      // 出错时允许请求
      return {
        allowed: true,
        remaining: config.limit,
        resetTime: now + config.windowMs,
        totalHits: 0
      };
    }
  }

  /**
   * 滑动窗口算法
   */
  private async checkSlidingWindow(
    key: string,
    config: RateLimitConfig
  ): Promise<RateLimitResult> {
    const windowKey = RedisKeys.RATE_LIMIT_SLIDING(key);
    const now = Date.now();
    const windowStart = now - config.windowMs;
    
    try {
      // 获取当前窗口状态
      let windowState = await redisService.get<SlidingWindowState>(windowKey);
      
      if (!windowState) {
        windowState = {
          requests: [],
          windowMs: config.windowMs,
          limit: config.limit
        };
      }

      // 清理过期的请求记录
      windowState.requests = windowState.requests.filter(timestamp => timestamp > windowStart);

      // 检查是否超过限制
      if (windowState.requests.length < config.limit) {
        // 添加当前请求
        windowState.requests.push(now);
        
        // 保存状态
        await redisService.set(windowKey, windowState, Math.ceil(config.windowMs / 1000));
        
        return {
          allowed: true,
          remaining: config.limit - windowState.requests.length,
          resetTime: windowState.requests[0] + config.windowMs,
          totalHits: windowState.requests.length
        };
      } else {
        // 保存状态
        await redisService.set(windowKey, windowState, Math.ceil(config.windowMs / 1000));
        
        const oldestRequest = Math.min(...windowState.requests);
        const retryAfter = Math.ceil((oldestRequest + config.windowMs - now) / 1000);
        
        return {
          allowed: false,
          remaining: 0,
          resetTime: oldestRequest + config.windowMs,
          totalHits: windowState.requests.length,
          retryAfter
        };
      }

    } catch (error) {
      logger.error('滑动窗口算法检查失败', { key, error: error.message });
      // 出错时允许请求
      return {
        allowed: true,
        remaining: config.limit,
        resetTime: now + config.windowMs,
        totalHits: 0
      };
    }
  }

  /**
   * 重置速率限制
   */
  async resetRateLimit(key: string, algorithm: RateLimitAlgorithm): Promise<boolean> {
    try {
      let keysToDelete: string[] = [];
      
      switch (algorithm) {
        case RateLimitAlgorithm.TOKEN_BUCKET:
        case RateLimitAlgorithm.LEAKY_BUCKET:
          keysToDelete.push(RedisKeys.RATE_LIMIT_BUCKET(key));
          break;
        
        case RateLimitAlgorithm.FIXED_WINDOW:
          // 删除所有相关的窗口键
          const client = redisService.getClient();
          const windowPattern = RedisKeys.RATE_LIMIT_WINDOW(key, '*');
          const windowKeys = await client.keys(windowPattern);
          keysToDelete.push(...windowKeys);
          break;
        
        case RateLimitAlgorithm.SLIDING_WINDOW:
          keysToDelete.push(RedisKeys.RATE_LIMIT_SLIDING(key));
          break;
      }

      if (keysToDelete.length > 0) {
        await redisService.del(keysToDelete);
        logger.info('速率限制重置成功', { key, algorithm, deletedKeys: keysToDelete.length });
      }

      return true;

    } catch (error) {
      logger.error('速率限制重置失败', { key, algorithm, error: error.message });
      return false;
    }
  }

  /**
   * 获取速率限制状态
   */
  async getRateLimitStatus(
    key: string,
    algorithm: RateLimitAlgorithm
  ): Promise<any> {
    try {
      switch (algorithm) {
        case RateLimitAlgorithm.TOKEN_BUCKET:
          const bucketKey = RedisKeys.RATE_LIMIT_BUCKET(key);
          return await redisService.get<TokenBucketState>(bucketKey);
        
        case RateLimitAlgorithm.LEAKY_BUCKET:
          const leakyKey = RedisKeys.RATE_LIMIT_BUCKET(key);
          return await redisService.get<LeakyBucketState>(leakyKey);
        
        case RateLimitAlgorithm.SLIDING_WINDOW:
          const slidingKey = RedisKeys.RATE_LIMIT_SLIDING(key);
          return await redisService.get<SlidingWindowState>(slidingKey);
        
        case RateLimitAlgorithm.FIXED_WINDOW:
          // 获取当前窗口的计数
          const now = Date.now();
          const windowMs = 60000; // 默认1分钟窗口
          const windowStart = Math.floor(now / windowMs) * windowMs;
          const windowKey = RedisKeys.RATE_LIMIT_WINDOW(key, windowStart);
          const count = await redisService.get<number>(windowKey);
          return { count, windowStart, windowEnd: windowStart + windowMs };
        
        default:
          return null;
      }

    } catch (error) {
      logger.error('获取速率限制状态失败', { key, algorithm, error: error.message });
      return null;
    }
  }

  /**
   * 批量检查速率限制
   */
  async batchCheckRateLimit(
    requests: Array<{ key: string; config: RateLimitConfig }>
  ): Promise<RateLimitResult[]> {
    const results: RateLimitResult[] = [];
    
    for (const request of requests) {
      try {
        const result = await this.checkRateLimit(request.key, request.config);
        results.push(result);
      } catch (error) {
        logger.error('批量速率限制检查失败', {
          key: request.key,
          error: error.message
        });
        
        // 出错时允许请求
        results.push({
          allowed: true,
          remaining: request.config.limit,
          resetTime: Date.now() + request.config.windowMs,
          totalHits: 0
        });
      }
    }
    
    return results;
  }

  /**
   * 清理过期的速率限制数据
   */
  async cleanupExpiredData(): Promise<number> {
    try {
      const client = redisService.getClient();
      const patterns = [
        RedisKeys.RATE_LIMIT_BUCKET('*'),
        RedisKeys.RATE_LIMIT_WINDOW('*', '*'),
        RedisKeys.RATE_LIMIT_SLIDING('*')
      ];

      let cleanedCount = 0;
      
      for (const pattern of patterns) {
        const keys = await client.keys(pattern);
        
        for (const key of keys) {
          const ttl = await client.ttl(key);
          if (ttl === -1) {
            // 没有过期时间的键，设置默认过期时间
            await client.expire(key, 3600); // 1小时
          } else if (ttl === -2) {
            // 键不存在，跳过
            continue;
          }
        }
      }

      logger.info('速率限制数据清理完成', { cleanedCount });
      return cleanedCount;

    } catch (error) {
      logger.error('速率限制数据清理失败', { error: error.message });
      return 0;
    }
  }
}

// 创建单例实例
export const rateLimiterService = new RateLimiterService();
