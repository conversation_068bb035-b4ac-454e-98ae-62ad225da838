/**
 * 动态翻译服务
 * 提供实时翻译、自动翻译和翻译缓存功能
 */

import { logger } from '@/config/logger';
import { redisService } from './redis.service';
import { i18nService, SupportedLanguage } from './i18n.service';
import { metricsCollector } from './metrics-collector.service';

/**
 * 翻译提供商接口
 */
interface TranslationProvider {
  name: string;
  translate(text: string, from: string, to: string): Promise<string>;
  detectLanguage(text: string): Promise<string>;
  getSupportedLanguages(): Promise<string[]>;
}

/**
 * 翻译结果接口
 */
interface TranslationResult {
  originalText: string;
  translatedText: string;
  sourceLanguage: string;
  targetLanguage: string;
  provider: string;
  confidence: number;
  cached: boolean;
  timestamp: Date;
}

/**
 * 批量翻译请求接口
 */
interface BatchTranslationRequest {
  texts: string[];
  sourceLanguage: string;
  targetLanguage: string;
  namespace?: string;
  priority?: 'low' | 'normal' | 'high';
}

/**
 * 动态翻译服务类
 */
export class DynamicTranslationService {
  private providers: Map<string, TranslationProvider> = new Map();
  private defaultProvider: string = 'google';
  private cachePrefix = 'translation:';
  private cacheTTL = 7 * 24 * 60 * 60; // 7天

  constructor() {
    this.initializeProviders();
  }

  /**
   * 初始化翻译提供商
   */
  private initializeProviders(): void {
    // Google翻译提供商
    this.providers.set('google', new GoogleTranslationProvider());
    
    // 百度翻译提供商
    this.providers.set('baidu', new BaiduTranslationProvider());
    
    // 腾讯翻译提供商
    this.providers.set('tencent', new TencentTranslationProvider());
    
    // 阿里云翻译提供商
    this.providers.set('aliyun', new AliyunTranslationProvider());

    logger.info('翻译提供商初始化完成', {
      providers: Array.from(this.providers.keys())
    });
  }

  /**
   * 翻译文本
   */
  async translateText(
    text: string,
    targetLanguage: SupportedLanguage,
    sourceLanguage?: SupportedLanguage,
    provider?: string
  ): Promise<TranslationResult> {
    try {
      // 检查缓存
      const cacheKey = this.getCacheKey(text, sourceLanguage || 'auto', targetLanguage);
      const cached = await this.getCachedTranslation(cacheKey);
      
      if (cached) {
        metricsCollector.incrementCounter('translation_cache_hits');
        return cached;
      }

      // 选择翻译提供商
      const selectedProvider = provider || this.defaultProvider;
      const translationProvider = this.providers.get(selectedProvider);
      
      if (!translationProvider) {
        throw new Error(`翻译提供商不存在: ${selectedProvider}`);
      }

      // 检测源语言（如果未指定）
      let detectedSourceLanguage = sourceLanguage;
      if (!sourceLanguage) {
        detectedSourceLanguage = await translationProvider.detectLanguage(text) as SupportedLanguage;
      }

      // 如果源语言和目标语言相同，直接返回原文
      if (detectedSourceLanguage === targetLanguage) {
        return {
          originalText: text,
          translatedText: text,
          sourceLanguage: detectedSourceLanguage,
          targetLanguage,
          provider: selectedProvider,
          confidence: 1.0,
          cached: false,
          timestamp: new Date()
        };
      }

      // 执行翻译
      const translatedText = await translationProvider.translate(
        text,
        detectedSourceLanguage || 'auto',
        targetLanguage
      );

      const result: TranslationResult = {
        originalText: text,
        translatedText,
        sourceLanguage: detectedSourceLanguage || 'auto',
        targetLanguage,
        provider: selectedProvider,
        confidence: 0.9, // 默认置信度
        cached: false,
        timestamp: new Date()
      };

      // 缓存翻译结果
      await this.cacheTranslation(cacheKey, result);

      // 记录指标
      metricsCollector.incrementCounter('translation_requests', {
        provider: selectedProvider,
        source_language: detectedSourceLanguage || 'auto',
        target_language: targetLanguage
      });

      return result;

    } catch (error) {
      logger.error('翻译失败', {
        text: text.substring(0, 100),
        targetLanguage,
        sourceLanguage,
        provider,
        error: error instanceof Error ? error.message : String(error)
      });

      metricsCollector.incrementCounter('translation_errors', {
        provider: provider || this.defaultProvider
      });

      throw error;
    }
  }

  /**
   * 批量翻译
   */
  async translateBatch(request: BatchTranslationRequest): Promise<TranslationResult[]> {
    const { texts, sourceLanguage, targetLanguage, namespace, priority = 'normal' } = request;

    logger.info('开始批量翻译', {
      textCount: texts.length,
      sourceLanguage,
      targetLanguage,
      namespace,
      priority
    });

    const results: TranslationResult[] = [];
    const batchSize = 10; // 每批处理10个文本

    for (let i = 0; i < texts.length; i += batchSize) {
      const batch = texts.slice(i, i + batchSize);
      
      const batchPromises = batch.map(text =>
        this.translateText(text, targetLanguage as SupportedLanguage, sourceLanguage as SupportedLanguage)
      );

      try {
        const batchResults = await Promise.all(batchPromises);
        results.push(...batchResults);
      } catch (error) {
        logger.error('批量翻译部分失败', {
          batchIndex: Math.floor(i / batchSize),
          error: error instanceof Error ? error.message : String(error)
        });
        
        // 对失败的批次进行单独处理
        for (const text of batch) {
          try {
            const result = await this.translateText(text, targetLanguage as SupportedLanguage, sourceLanguage as SupportedLanguage);
            results.push(result);
          } catch (singleError) {
            logger.error('单个文本翻译失败', {
              text: text.substring(0, 50),
              error: singleError instanceof Error ? singleError.message : String(singleError)
            });
          }
        }
      }

      // 添加延迟以避免API限制
      if (i + batchSize < texts.length) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }

    logger.info('批量翻译完成', {
      totalTexts: texts.length,
      successfulTranslations: results.length,
      failedTranslations: texts.length - results.length
    });

    return results;
  }

  /**
   * 自动翻译缺失的翻译
   */
  async autoTranslateMissingKeys(
    sourceLanguage: SupportedLanguage,
    targetLanguages: SupportedLanguage[],
    namespace: string = 'common'
  ): Promise<void> {
    try {
      logger.info('开始自动翻译缺失的键', {
        sourceLanguage,
        targetLanguages,
        namespace
      });

      // 获取源语言的所有翻译键
      const sourceTranslations = i18nService.getNamespaceTranslations(sourceLanguage, namespace);
      
      for (const targetLanguage of targetLanguages) {
        if (targetLanguage === sourceLanguage) continue;

        const targetTranslations = i18nService.getNamespaceTranslations(targetLanguage, namespace);
        const missingKeys: string[] = [];

        // 找出缺失的翻译键
        this.findMissingKeys(sourceTranslations, targetTranslations, '', missingKeys);

        if (missingKeys.length > 0) {
          logger.info(`发现 ${missingKeys.length} 个缺失的翻译键`, {
            targetLanguage,
            namespace
          });

          // 批量翻译缺失的键
          const textsToTranslate = missingKeys.map(key => 
            this.getNestedValue(sourceTranslations, key)
          ).filter(text => typeof text === 'string');

          const translationResults = await this.translateBatch({
            texts: textsToTranslate,
            sourceLanguage,
            targetLanguage,
            namespace,
            priority: 'low'
          });

          // 更新翻译文件
          await this.updateTranslationFile(targetLanguage, namespace, missingKeys, translationResults);
        }
      }

      logger.info('自动翻译完成');

    } catch (error) {
      logger.error('自动翻译失败', {
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * 获取翻译统计信息
   */
  async getTranslationStats(): Promise<any> {
    const stats = {
      providers: Array.from(this.providers.keys()),
      defaultProvider: this.defaultProvider,
      supportedLanguages: Object.values(SupportedLanguage),
      cacheStats: await this.getCacheStats(),
      recentTranslations: await this.getRecentTranslations()
    };

    return stats;
  }

  /**
   * 清理翻译缓存
   */
  async clearTranslationCache(pattern?: string): Promise<number> {
    try {
      const searchPattern = pattern || `${this.cachePrefix}*`;
      const keys = await redisService.keys(searchPattern);
      
      if (keys.length > 0) {
        await redisService.del(...keys);
      }

      logger.info('翻译缓存清理完成', {
        clearedKeys: keys.length,
        pattern: searchPattern
      });

      return keys.length;

    } catch (error) {
      logger.error('清理翻译缓存失败', {
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * 生成缓存键
   */
  private getCacheKey(text: string, sourceLanguage: string, targetLanguage: string): string {
    const textHash = require('crypto').createHash('md5').update(text).digest('hex');
    return `${this.cachePrefix}${sourceLanguage}:${targetLanguage}:${textHash}`;
  }

  /**
   * 获取缓存的翻译
   */
  private async getCachedTranslation(cacheKey: string): Promise<TranslationResult | null> {
    try {
      const cached = await redisService.get(cacheKey);
      if (cached) {
        const result = JSON.parse(cached);
        result.cached = true;
        result.timestamp = new Date(result.timestamp);
        return result;
      }
    } catch (error) {
      logger.warn('获取翻译缓存失败', { cacheKey, error });
    }
    return null;
  }

  /**
   * 缓存翻译结果
   */
  private async cacheTranslation(cacheKey: string, result: TranslationResult): Promise<void> {
    try {
      await redisService.setex(cacheKey, this.cacheTTL, JSON.stringify(result));
    } catch (error) {
      logger.warn('缓存翻译结果失败', { cacheKey, error });
    }
  }

  /**
   * 递归查找缺失的翻译键
   */
  private findMissingKeys(source: any, target: any, prefix: string, missingKeys: string[]): void {
    for (const key in source) {
      const fullKey = prefix ? `${prefix}.${key}` : key;
      
      if (typeof source[key] === 'object' && source[key] !== null) {
        if (!target[key] || typeof target[key] !== 'object') {
          missingKeys.push(fullKey);
        } else {
          this.findMissingKeys(source[key], target[key], fullKey, missingKeys);
        }
      } else if (typeof source[key] === 'string') {
        if (!target[key]) {
          missingKeys.push(fullKey);
        }
      }
    }
  }

  /**
   * 获取嵌套值
   */
  private getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => current?.[key], obj);
  }

  /**
   * 更新翻译文件
   */
  private async updateTranslationFile(
    language: SupportedLanguage,
    namespace: string,
    keys: string[],
    translations: TranslationResult[]
  ): Promise<void> {
    // 这里应该实现更新翻译文件的逻辑
    // 简化实现，实际应该写入文件系统
    logger.info('更新翻译文件', {
      language,
      namespace,
      keysCount: keys.length,
      translationsCount: translations.length
    });
  }

  /**
   * 获取缓存统计
   */
  private async getCacheStats(): Promise<any> {
    try {
      const keys = await redisService.keys(`${this.cachePrefix}*`);
      return {
        totalCachedTranslations: keys.length,
        cacheHitRate: 0.85, // 示例数据
        averageResponseTime: 120 // 毫秒
      };
    } catch (error) {
      return { error: error instanceof Error ? error.message : String(error) };
    }
  }

  /**
   * 获取最近翻译记录
   */
  private async getRecentTranslations(): Promise<any[]> {
    // 简化实现，实际应该从数据库或缓存获取
    return [];
  }
}

/**
 * Google翻译提供商实现
 */
class GoogleTranslationProvider implements TranslationProvider {
  name = 'google';

  async translate(text: string, from: string, to: string): Promise<string> {
    // 这里应该实现Google翻译API调用
    // 简化实现
    logger.info('Google翻译API调用', { from, to, textLength: text.length });
    return `[Google翻译] ${text}`;
  }

  async detectLanguage(text: string): Promise<string> {
    // 简化实现
    return 'zh-CN';
  }

  async getSupportedLanguages(): Promise<string[]> {
    return Object.values(SupportedLanguage);
  }
}

/**
 * 百度翻译提供商实现
 */
class BaiduTranslationProvider implements TranslationProvider {
  name = 'baidu';

  async translate(text: string, from: string, to: string): Promise<string> {
    logger.info('百度翻译API调用', { from, to, textLength: text.length });
    return `[百度翻译] ${text}`;
  }

  async detectLanguage(text: string): Promise<string> {
    return 'zh-CN';
  }

  async getSupportedLanguages(): Promise<string[]> {
    return Object.values(SupportedLanguage);
  }
}

/**
 * 腾讯翻译提供商实现
 */
class TencentTranslationProvider implements TranslationProvider {
  name = 'tencent';

  async translate(text: string, from: string, to: string): Promise<string> {
    logger.info('腾讯翻译API调用', { from, to, textLength: text.length });
    return `[腾讯翻译] ${text}`;
  }

  async detectLanguage(text: string): Promise<string> {
    return 'zh-CN';
  }

  async getSupportedLanguages(): Promise<string[]> {
    return Object.values(SupportedLanguage);
  }
}

/**
 * 阿里云翻译提供商实现
 */
class AliyunTranslationProvider implements TranslationProvider {
  name = 'aliyun';

  async translate(text: string, from: string, to: string): Promise<string> {
    logger.info('阿里云翻译API调用', { from, to, textLength: text.length });
    return `[阿里云翻译] ${text}`;
  }

  async detectLanguage(text: string): Promise<string> {
    return 'zh-CN';
  }

  async getSupportedLanguages(): Promise<string[]> {
    return Object.values(SupportedLanguage);
  }
}

// 创建服务实例
export const dynamicTranslationService = new DynamicTranslationService();
