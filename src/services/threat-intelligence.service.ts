/**
 * 威胁情报服务
 * 集成多个威胁情报源，提供实时威胁检测和分析
 */

import axios, { AxiosInstance } from 'axios';
import { logger } from '@/config/logger';
import { redisService } from './redis.service';
import { metricsCollector } from './metrics-collector.service';
import { securityAuditService, AuditEventType, AuditSeverity } from './security-audit.service';

/**
 * 威胁类型枚举
 */
export enum ThreatType {
  MALICIOUS_IP = 'malicious_ip',
  SUSPICIOUS_DOMAIN = 'suspicious_domain',
  KNOWN_MALWARE = 'known_malware',
  PHISHING_URL = 'phishing_url',
  BOTNET = 'botnet',
  TOR_EXIT_NODE = 'tor_exit_node',
  VPN_PROXY = 'vpn_proxy',
  COMPROMISED_CREDENTIAL = 'compromised_credential'
}

/**
 * 威胁级别枚举
 */
export enum ThreatLevel {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

/**
 * 威胁情报接口
 */
export interface ThreatIntelligence {
  id: string;
  type: ThreatType;
  level: ThreatLevel;
  indicator: string;
  source: string;
  description: string;
  confidence: number;
  firstSeen: Date;
  lastSeen: Date;
  tags: string[];
  metadata: Record<string, any>;
}

/**
 * 威胁检测结果接口
 */
export interface ThreatDetectionResult {
  isThreat: boolean;
  threats: ThreatIntelligence[];
  riskScore: number;
  recommendations: string[];
  blockedActions: string[];
}

/**
 * 威胁情报源配置接口
 */
interface ThreatIntelligenceSource {
  name: string;
  enabled: boolean;
  apiKey?: string;
  baseUrl: string;
  rateLimit: number;
  priority: number;
  supportedTypes: ThreatType[];
}

/**
 * 威胁情报服务类
 */
export class ThreatIntelligenceService {
  private sources: Map<string, ThreatIntelligenceSource> = new Map();
  private httpClients: Map<string, AxiosInstance> = new Map();
  private cachePrefix = 'threat_intel:';
  private cacheTTL = 3600; // 1小时缓存

  constructor() {
    this.initializeSources();
  }

  /**
   * 初始化威胁情报源
   */
  private initializeSources(): void {
    // VirusTotal 威胁情报源
    this.sources.set('virustotal', {
      name: 'VirusTotal',
      enabled: !!process.env.VIRUSTOTAL_API_KEY,
      apiKey: process.env.VIRUSTOTAL_API_KEY,
      baseUrl: 'https://www.virustotal.com/vtapi/v2',
      rateLimit: 4, // 每分钟4次请求
      priority: 1,
      supportedTypes: [
        ThreatType.MALICIOUS_IP,
        ThreatType.SUSPICIOUS_DOMAIN,
        ThreatType.KNOWN_MALWARE,
        ThreatType.PHISHING_URL
      ]
    });

    // AbuseIPDB 威胁情报源
    this.sources.set('abuseipdb', {
      name: 'AbuseIPDB',
      enabled: !!process.env.ABUSEIPDB_API_KEY,
      apiKey: process.env.ABUSEIPDB_API_KEY,
      baseUrl: 'https://api.abuseipdb.com/api/v2',
      rateLimit: 1000, // 每日1000次请求
      priority: 2,
      supportedTypes: [ThreatType.MALICIOUS_IP, ThreatType.BOTNET]
    });

    // Shodan 威胁情报源
    this.sources.set('shodan', {
      name: 'Shodan',
      enabled: !!process.env.SHODAN_API_KEY,
      apiKey: process.env.SHODAN_API_KEY,
      baseUrl: 'https://api.shodan.io',
      rateLimit: 100, // 每月100次查询
      priority: 3,
      supportedTypes: [ThreatType.MALICIOUS_IP, ThreatType.BOTNET]
    });

    // ThreatCrowd 威胁情报源（免费）
    this.sources.set('threatcrowd', {
      name: 'ThreatCrowd',
      enabled: true,
      baseUrl: 'https://www.threatcrowd.org/searchApi/v2',
      rateLimit: 10, // 每分钟10次请求
      priority: 4,
      supportedTypes: [
        ThreatType.MALICIOUS_IP,
        ThreatType.SUSPICIOUS_DOMAIN,
        ThreatType.KNOWN_MALWARE
      ]
    });

    // 初始化HTTP客户端
    this.initializeHttpClients();

    logger.info('威胁情报源初始化完成', {
      enabledSources: Array.from(this.sources.entries())
        .filter(([_, source]) => source.enabled)
        .map(([name, _]) => name)
    });
  }

  /**
   * 初始化HTTP客户端
   */
  private initializeHttpClients(): void {
    for (const [name, source] of this.sources) {
      if (!source.enabled) continue;

      const client = axios.create({
        baseURL: source.baseUrl,
        timeout: 10000,
        headers: {
          'User-Agent': 'ID-Provider-Security-Scanner/1.0',
          ...(source.apiKey && { 'X-API-KEY': source.apiKey })
        }
      });

      // 添加请求拦截器用于速率限制
      client.interceptors.request.use(async (config) => {
        await this.checkRateLimit(name);
        return config;
      });

      // 添加响应拦截器用于错误处理
      client.interceptors.response.use(
        (response) => response,
        (error) => {
          logger.error(`威胁情报源 ${name} 请求失败`, {
            error: error.message,
            status: error.response?.status,
            data: error.response?.data
          });
          throw error;
        }
      );

      this.httpClients.set(name, client);
    }
  }

  /**
   * 检查IP地址威胁
   */
  async checkIPThreat(ipAddress: string): Promise<ThreatDetectionResult> {
    try {
      const cacheKey = `${this.cachePrefix}ip:${ipAddress}`;
      
      // 检查缓存
      const cached = await this.getCachedResult(cacheKey);
      if (cached) {
        metricsCollector.incrementCounter('threat_intel_cache_hits');
        return cached;
      }

      const threats: ThreatIntelligence[] = [];
      const promises: Promise<ThreatIntelligence[]>[] = [];

      // 并行查询所有支持IP检查的威胁情报源
      for (const [name, source] of this.sources) {
        if (source.enabled && source.supportedTypes.includes(ThreatType.MALICIOUS_IP)) {
          promises.push(this.queryIPThreat(name, ipAddress));
        }
      }

      const results = await Promise.allSettled(promises);
      
      // 合并结果
      for (const result of results) {
        if (result.status === 'fulfilled') {
          threats.push(...result.value);
        }
      }

      const detectionResult = this.analyzeThreats(threats);
      
      // 缓存结果
      await this.cacheResult(cacheKey, detectionResult);

      // 记录威胁检测事件
      if (detectionResult.isThreat) {
        await securityAuditService.logEvent({
          type: AuditEventType.THREAT_DETECTED,
          severity: this.mapThreatLevelToSeverity(detectionResult.threats[0]?.level),
          details: {
            indicator: ipAddress,
            type: 'ip_address',
            threats: detectionResult.threats.length,
            riskScore: detectionResult.riskScore
          }
        });
      }

      metricsCollector.incrementCounter('threat_intel_queries', {
        type: 'ip',
        result: detectionResult.isThreat ? 'threat' : 'clean'
      });

      return detectionResult;

    } catch (error) {
      logger.error('IP威胁检查失败', {
        ipAddress,
        error: error instanceof Error ? error.message : String(error)
      });

      metricsCollector.incrementCounter('threat_intel_errors');
      
      return {
        isThreat: false,
        threats: [],
        riskScore: 0,
        recommendations: ['威胁检查服务暂时不可用，建议手动审查'],
        blockedActions: []
      };
    }
  }

  /**
   * 检查域名威胁
   */
  async checkDomainThreat(domain: string): Promise<ThreatDetectionResult> {
    try {
      const cacheKey = `${this.cachePrefix}domain:${domain}`;
      
      const cached = await this.getCachedResult(cacheKey);
      if (cached) {
        return cached;
      }

      const threats: ThreatIntelligence[] = [];
      const promises: Promise<ThreatIntelligence[]>[] = [];

      for (const [name, source] of this.sources) {
        if (source.enabled && source.supportedTypes.includes(ThreatType.SUSPICIOUS_DOMAIN)) {
          promises.push(this.queryDomainThreat(name, domain));
        }
      }

      const results = await Promise.allSettled(promises);
      
      for (const result of results) {
        if (result.status === 'fulfilled') {
          threats.push(...result.value);
        }
      }

      const detectionResult = this.analyzeThreats(threats);
      await this.cacheResult(cacheKey, detectionResult);

      if (detectionResult.isThreat) {
        await securityAuditService.logEvent({
          type: AuditEventType.THREAT_DETECTED,
          severity: this.mapThreatLevelToSeverity(detectionResult.threats[0]?.level),
          details: {
            indicator: domain,
            type: 'domain',
            threats: detectionResult.threats.length,
            riskScore: detectionResult.riskScore
          }
        });
      }

      return detectionResult;

    } catch (error) {
      logger.error('域名威胁检查失败', {
        domain,
        error: error instanceof Error ? error.message : String(error)
      });

      return {
        isThreat: false,
        threats: [],
        riskScore: 0,
        recommendations: ['威胁检查服务暂时不可用'],
        blockedActions: []
      };
    }
  }

  /**
   * 检查URL威胁
   */
  async checkURLThreat(url: string): Promise<ThreatDetectionResult> {
    try {
      const cacheKey = `${this.cachePrefix}url:${this.hashString(url)}`;
      
      const cached = await this.getCachedResult(cacheKey);
      if (cached) {
        return cached;
      }

      const threats: ThreatIntelligence[] = [];
      const promises: Promise<ThreatIntelligence[]>[] = [];

      for (const [name, source] of this.sources) {
        if (source.enabled && source.supportedTypes.includes(ThreatType.PHISHING_URL)) {
          promises.push(this.queryURLThreat(name, url));
        }
      }

      const results = await Promise.allSettled(promises);
      
      for (const result of results) {
        if (result.status === 'fulfilled') {
          threats.push(...result.value);
        }
      }

      const detectionResult = this.analyzeThreats(threats);
      await this.cacheResult(cacheKey, detectionResult);

      return detectionResult;

    } catch (error) {
      logger.error('URL威胁检查失败', {
        url: url.substring(0, 100),
        error: error instanceof Error ? error.message : String(error)
      });

      return {
        isThreat: false,
        threats: [],
        riskScore: 0,
        recommendations: [],
        blockedActions: []
      };
    }
  }

  /**
   * 批量威胁检查
   */
  async batchThreatCheck(indicators: Array<{
    type: 'ip' | 'domain' | 'url';
    value: string;
  }>): Promise<Map<string, ThreatDetectionResult>> {
    const results = new Map<string, ThreatDetectionResult>();
    const batchSize = 10;

    for (let i = 0; i < indicators.length; i += batchSize) {
      const batch = indicators.slice(i, i + batchSize);
      const promises = batch.map(async (indicator) => {
        let result: ThreatDetectionResult;
        
        switch (indicator.type) {
          case 'ip':
            result = await this.checkIPThreat(indicator.value);
            break;
          case 'domain':
            result = await this.checkDomainThreat(indicator.value);
            break;
          case 'url':
            result = await this.checkURLThreat(indicator.value);
            break;
          default:
            result = {
              isThreat: false,
              threats: [],
              riskScore: 0,
              recommendations: [],
              blockedActions: []
            };
        }
        
        return { indicator: indicator.value, result };
      });

      const batchResults = await Promise.allSettled(promises);
      
      for (const batchResult of batchResults) {
        if (batchResult.status === 'fulfilled') {
          results.set(batchResult.value.indicator, batchResult.value.result);
        }
      }

      // 批次间延迟，避免API限制
      if (i + batchSize < indicators.length) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }

    return results;
  }

  /**
   * 获取威胁情报统计
   */
  async getThreatIntelligenceStats(): Promise<any> {
    const stats = {
      sources: Array.from(this.sources.entries()).map(([name, source]) => ({
        name,
        enabled: source.enabled,
        priority: source.priority,
        supportedTypes: source.supportedTypes
      })),
      cacheStats: await this.getCacheStats(),
      recentThreats: await this.getRecentThreats()
    };

    return stats;
  }

  // 私有方法

  /**
   * 查询IP威胁（具体实现）
   */
  private async queryIPThreat(sourceName: string, ipAddress: string): Promise<ThreatIntelligence[]> {
    const client = this.httpClients.get(sourceName);
    if (!client) return [];

    try {
      switch (sourceName) {
        case 'abuseipdb':
          return await this.queryAbuseIPDB(client, ipAddress);
        case 'virustotal':
          return await this.queryVirusTotalIP(client, ipAddress);
        case 'shodan':
          return await this.queryShodanIP(client, ipAddress);
        case 'threatcrowd':
          return await this.queryThreatCrowdIP(client, ipAddress);
        default:
          return [];
      }
    } catch (error) {
      logger.warn(`威胁情报源 ${sourceName} 查询失败`, { error });
      return [];
    }
  }

  /**
   * 查询域名威胁（具体实现）
   */
  private async queryDomainThreat(sourceName: string, domain: string): Promise<ThreatIntelligence[]> {
    // 实现各个威胁情报源的域名查询
    return [];
  }

  /**
   * 查询URL威胁（具体实现）
   */
  private async queryURLThreat(sourceName: string, url: string): Promise<ThreatIntelligence[]> {
    // 实现各个威胁情报源的URL查询
    return [];
  }

  /**
   * 分析威胁数据
   */
  private analyzeThreats(threats: ThreatIntelligence[]): ThreatDetectionResult {
    if (threats.length === 0) {
      return {
        isThreat: false,
        threats: [],
        riskScore: 0,
        recommendations: [],
        blockedActions: []
      };
    }

    // 计算风险分数
    const riskScore = this.calculateRiskScore(threats);
    
    // 生成建议
    const recommendations = this.generateRecommendations(threats);
    
    // 确定阻断动作
    const blockedActions = this.determineBlockedActions(threats, riskScore);

    return {
      isThreat: true,
      threats: threats.sort((a, b) => b.confidence - a.confidence),
      riskScore,
      recommendations,
      blockedActions
    };
  }

  /**
   * 计算风险分数
   */
  private calculateRiskScore(threats: ThreatIntelligence[]): number {
    let score = 0;
    
    for (const threat of threats) {
      let threatScore = 0;
      
      // 基于威胁级别的基础分数
      switch (threat.level) {
        case ThreatLevel.LOW:
          threatScore = 25;
          break;
        case ThreatLevel.MEDIUM:
          threatScore = 50;
          break;
        case ThreatLevel.HIGH:
          threatScore = 75;
          break;
        case ThreatLevel.CRITICAL:
          threatScore = 100;
          break;
      }
      
      // 基于置信度调整分数
      threatScore *= (threat.confidence / 100);
      
      // 基于威胁类型调整分数
      if (threat.type === ThreatType.KNOWN_MALWARE || threat.type === ThreatType.BOTNET) {
        threatScore *= 1.2;
      }
      
      score = Math.max(score, threatScore);
    }
    
    return Math.min(100, Math.round(score));
  }

  /**
   * 生成安全建议
   */
  private generateRecommendations(threats: ThreatIntelligence[]): string[] {
    const recommendations: string[] = [];
    const threatTypes = new Set(threats.map(t => t.type));
    
    if (threatTypes.has(ThreatType.MALICIOUS_IP)) {
      recommendations.push('立即阻断来自此IP的所有连接');
      recommendations.push('检查相关日志以确定潜在的入侵范围');
    }
    
    if (threatTypes.has(ThreatType.PHISHING_URL)) {
      recommendations.push('阻止访问此URL');
      recommendations.push('通知用户此链接可能是钓鱼攻击');
    }
    
    if (threatTypes.has(ThreatType.KNOWN_MALWARE)) {
      recommendations.push('立即隔离相关文件或系统');
      recommendations.push('运行完整的恶意软件扫描');
    }
    
    return recommendations;
  }

  /**
   * 确定阻断动作
   */
  private determineBlockedActions(threats: ThreatIntelligence[], riskScore: number): string[] {
    const actions: string[] = [];
    
    if (riskScore >= 80) {
      actions.push('block_all_traffic');
      actions.push('quarantine_source');
    } else if (riskScore >= 60) {
      actions.push('block_suspicious_traffic');
      actions.push('increase_monitoring');
    } else if (riskScore >= 40) {
      actions.push('log_and_monitor');
    }
    
    return actions;
  }

  // 威胁情报源具体实现方法

  private async queryAbuseIPDB(client: AxiosInstance, ipAddress: string): Promise<ThreatIntelligence[]> {
    // AbuseIPDB API实现
    return [];
  }

  private async queryVirusTotalIP(client: AxiosInstance, ipAddress: string): Promise<ThreatIntelligence[]> {
    // VirusTotal API实现
    return [];
  }

  private async queryShodanIP(client: AxiosInstance, ipAddress: string): Promise<ThreatIntelligence[]> {
    // Shodan API实现
    return [];
  }

  private async queryThreatCrowdIP(client: AxiosInstance, ipAddress: string): Promise<ThreatIntelligence[]> {
    // ThreatCrowd API实现
    return [];
  }

  // 辅助方法

  private async checkRateLimit(sourceName: string): Promise<void> {
    // 实现速率限制检查
  }

  private async getCachedResult(cacheKey: string): Promise<ThreatDetectionResult | null> {
    try {
      const cached = await redisService.get(cacheKey);
      return cached ? JSON.parse(cached) : null;
    } catch {
      return null;
    }
  }

  private async cacheResult(cacheKey: string, result: ThreatDetectionResult): Promise<void> {
    try {
      await redisService.setex(cacheKey, this.cacheTTL, JSON.stringify(result));
    } catch (error) {
      logger.warn('缓存威胁检测结果失败', { error });
    }
  }

  private async getCacheStats(): Promise<any> {
    // 实现缓存统计
    return {};
  }

  private async getRecentThreats(): Promise<any[]> {
    // 实现最近威胁获取
    return [];
  }

  private hashString(str: string): string {
    return require('crypto').createHash('md5').update(str).digest('hex');
  }

  private mapThreatLevelToSeverity(level?: ThreatLevel): AuditSeverity {
    switch (level) {
      case ThreatLevel.CRITICAL:
        return AuditSeverity.CRITICAL;
      case ThreatLevel.HIGH:
        return AuditSeverity.HIGH;
      case ThreatLevel.MEDIUM:
        return AuditSeverity.MEDIUM;
      default:
        return AuditSeverity.LOW;
    }
  }
}

// 创建服务实例
export const threatIntelligenceService = new ThreatIntelligenceService();
