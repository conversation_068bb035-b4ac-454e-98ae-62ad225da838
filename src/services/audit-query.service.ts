/**
 * 审计日志查询服务
 * 提供高级审计日志查询、分析和导出功能
 */

import { prisma } from '@/config/database';
import { logger } from '@/config/logger';
import { cacheService } from './cache.service';
import { Parser } from 'json2csv';
import * as XLSX from 'xlsx';

/**
 * 查询参数接口
 */
export interface AuditQueryParams {
  // 基础过滤
  userId?: string;
  action?: string | string[];
  resource?: string | string[];
  level?: string | string[];
  
  // 时间范围
  startDate?: Date;
  endDate?: Date;
  
  // IP和位置
  ipAddress?: string;
  location?: string;
  
  // 文本搜索
  searchText?: string;
  
  // 分页
  page?: number;
  pageSize?: number;
  
  // 排序
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  
  // 高级过滤
  riskScoreMin?: number;
  riskScoreMax?: number;
  hasAnomalies?: boolean;
  complianceFlags?: string[];
}

/**
 * 查询结果接口
 */
export interface AuditQueryResult {
  data: any[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
  aggregations?: {
    actionCounts: Record<string, number>;
    resourceCounts: Record<string, number>;
    userCounts: Record<string, number>;
    timelineCounts: Record<string, number>;
    riskDistribution: Record<string, number>;
  };
}

/**
 * 导出格式枚举
 */
export enum ExportFormat {
  CSV = 'csv',
  XLSX = 'xlsx',
  JSON = 'json'
}

export class AuditQueryService {

  /**
   * 查询审计日志
   */
  async queryAuditLogs(params: AuditQueryParams): Promise<AuditQueryResult> {
    try {
      const {
        page = 1,
        pageSize = 50,
        sortBy = 'createdAt',
        sortOrder = 'desc'
      } = params;

      // 构建查询条件
      const where = this.buildWhereClause(params);
      
      // 构建排序条件
      const orderBy = { [sortBy]: sortOrder };

      // 计算偏移量
      const skip = (page - 1) * pageSize;

      // 执行查询
      const [data, total] = await Promise.all([
        prisma.auditLog.findMany({
          where,
          skip,
          take: pageSize,
          orderBy,
          include: {
            user: {
              select: {
                id: true,
                username: true,
                email: true
              }
            }
          }
        }),
        prisma.auditLog.count({ where })
      ]);

      // 计算总页数
      const totalPages = Math.ceil(total / pageSize);

      // 获取聚合数据
      const aggregations = await this.getAggregations(where);

      logger.info('审计日志查询成功', {
        total,
        page,
        pageSize,
        filters: Object.keys(params).length
      });

      return {
        data,
        total,
        page,
        pageSize,
        totalPages,
        aggregations
      };

    } catch (error) {
      logger.error('查询审计日志失败', { error, params });
      throw error;
    }
  }

  /**
   * 获取审计日志统计信息
   */
  async getAuditStatistics(params: Partial<AuditQueryParams> = {}): Promise<any> {
    try {
      const where = this.buildWhereClause(params);

      // 获取基础统计
      const [
        totalLogs,
        uniqueUsers,
        uniqueActions,
        uniqueResources,
        riskEvents
      ] = await Promise.all([
        prisma.auditLog.count({ where }),
        prisma.auditLog.groupBy({
          by: ['userId'],
          where: { ...where, userId: { not: null } },
          _count: true
        }),
        prisma.auditLog.groupBy({
          by: ['action'],
          where,
          _count: true
        }),
        prisma.auditLog.groupBy({
          by: ['resource'],
          where: { ...where, resource: { not: null } },
          _count: true
        }),
        prisma.auditLog.count({
          where: {
            ...where,
            level: { in: ['warning', 'error', 'critical'] }
          }
        })
      ]);

      // 时间分布统计
      const timeDistribution = await this.getTimeDistribution(where);

      // 风险评分分布
      const riskDistribution = await this.getRiskDistribution(where);

      return {
        overview: {
          totalLogs,
          uniqueUsers: uniqueUsers.length,
          uniqueActions: uniqueActions.length,
          uniqueResources: uniqueResources.length,
          riskEvents
        },
        distributions: {
          actions: uniqueActions.reduce((acc, item) => {
            acc[item.action] = item._count;
            return acc;
          }, {} as Record<string, number>),
          resources: uniqueResources.reduce((acc, item) => {
            acc[item.resource!] = item._count;
            return acc;
          }, {} as Record<string, number>),
          time: timeDistribution,
          risk: riskDistribution
        }
      };

    } catch (error) {
      logger.error('获取审计统计失败', { error, params });
      throw error;
    }
  }

  /**
   * 导出审计日志
   */
  async exportAuditLogs(
    params: AuditQueryParams,
    format: ExportFormat,
    maxRecords: number = 10000
  ): Promise<Buffer> {
    try {
      // 限制导出记录数
      const queryParams = {
        ...params,
        page: 1,
        pageSize: Math.min(maxRecords, 10000)
      };

      const result = await this.queryAuditLogs(queryParams);
      
      // 格式化数据用于导出
      const exportData = result.data.map(log => ({
        时间: log.createdAt.toISOString(),
        用户: log.user?.username || '系统',
        邮箱: log.user?.email || '',
        操作: log.action,
        资源: log.resource || '',
        资源ID: log.resourceId || '',
        IP地址: log.ipAddress || '',
        用户代理: log.userAgent || '',
        级别: log.level || 'info',
        描述: log.description || '',
        风险评分: log.riskScore || 0,
        威胁级别: log.threatLevel || '',
        位置: log.location || '',
        会话ID: log.sessionId || '',
        请求ID: log.requestId || ''
      }));

      switch (format) {
        case ExportFormat.CSV:
          return this.exportToCSV(exportData);
        
        case ExportFormat.XLSX:
          return this.exportToXLSX(exportData);
        
        case ExportFormat.JSON:
          return this.exportToJSON(exportData);
        
        default:
          throw new Error(`不支持的导出格式: ${format}`);
      }

    } catch (error) {
      logger.error('导出审计日志失败', { error, params, format });
      throw error;
    }
  }

  /**
   * 检测异常行为
   */
  async detectAnomalies(params: Partial<AuditQueryParams> = {}): Promise<any[]> {
    try {
      const where = this.buildWhereClause(params);

      // 检测异常登录
      const suspiciousLogins = await this.detectSuspiciousLogins(where);
      
      // 检测权限滥用
      const permissionAbuse = await this.detectPermissionAbuse(where);
      
      // 检测批量操作
      const bulkOperations = await this.detectBulkOperations(where);

      const anomalies = [
        ...suspiciousLogins,
        ...permissionAbuse,
        ...bulkOperations
      ];

      logger.info('异常行为检测完成', { 
        anomaliesCount: anomalies.length 
      });

      return anomalies;

    } catch (error) {
      logger.error('异常行为检测失败', { error });
      throw error;
    }
  }

  /**
   * 构建查询条件
   */
  private buildWhereClause(params: AuditQueryParams): any {
    const where: any = {};

    // 用户过滤
    if (params.userId) {
      where.userId = params.userId;
    }

    // 操作过滤
    if (params.action) {
      if (Array.isArray(params.action)) {
        where.action = { in: params.action };
      } else {
        where.action = params.action;
      }
    }

    // 资源过滤
    if (params.resource) {
      if (Array.isArray(params.resource)) {
        where.resource = { in: params.resource };
      } else {
        where.resource = params.resource;
      }
    }

    // 级别过滤
    if (params.level) {
      if (Array.isArray(params.level)) {
        where.level = { in: params.level };
      } else {
        where.level = params.level;
      }
    }

    // 时间范围过滤
    if (params.startDate || params.endDate) {
      where.createdAt = {};
      if (params.startDate) {
        where.createdAt.gte = params.startDate;
      }
      if (params.endDate) {
        where.createdAt.lte = params.endDate;
      }
    }

    // IP地址过滤
    if (params.ipAddress) {
      where.ipAddress = { contains: params.ipAddress };
    }

    // 位置过滤
    if (params.location) {
      where.location = { contains: params.location };
    }

    // 文本搜索
    if (params.searchText) {
      where.OR = [
        { description: { contains: params.searchText, mode: 'insensitive' } },
        { userAgent: { contains: params.searchText, mode: 'insensitive' } },
        { user: { username: { contains: params.searchText, mode: 'insensitive' } } },
        { user: { email: { contains: params.searchText, mode: 'insensitive' } } }
      ];
    }

    // 风险评分过滤
    if (params.riskScoreMin !== undefined || params.riskScoreMax !== undefined) {
      where.riskScore = {};
      if (params.riskScoreMin !== undefined) {
        where.riskScore.gte = params.riskScoreMin;
      }
      if (params.riskScoreMax !== undefined) {
        where.riskScore.lte = params.riskScoreMax;
      }
    }

    // 异常标记过滤
    if (params.hasAnomalies) {
      where.anomalyScore = { gt: 0 };
    }

    // 合规标记过滤
    if (params.complianceFlags && params.complianceFlags.length > 0) {
      where.complianceFlags = {
        hasSome: params.complianceFlags
      };
    }

    return where;
  }

  /**
   * 获取聚合数据
   */
  private async getAggregations(where: any): Promise<any> {
    try {
      const [actionCounts, resourceCounts, userCounts] = await Promise.all([
        prisma.auditLog.groupBy({
          by: ['action'],
          where,
          _count: true,
          orderBy: { _count: { action: 'desc' } },
          take: 10
        }),
        prisma.auditLog.groupBy({
          by: ['resource'],
          where: { ...where, resource: { not: null } },
          _count: true,
          orderBy: { _count: { resource: 'desc' } },
          take: 10
        }),
        prisma.auditLog.groupBy({
          by: ['userId'],
          where: { ...where, userId: { not: null } },
          _count: true,
          orderBy: { _count: { userId: 'desc' } },
          take: 10
        })
      ]);

      return {
        actionCounts: actionCounts.reduce((acc, item) => {
          acc[item.action] = item._count;
          return acc;
        }, {} as Record<string, number>),
        resourceCounts: resourceCounts.reduce((acc, item) => {
          acc[item.resource!] = item._count;
          return acc;
        }, {} as Record<string, number>),
        userCounts: userCounts.reduce((acc, item) => {
          acc[item.userId!] = item._count;
          return acc;
        }, {} as Record<string, number>)
      };

    } catch (error) {
      logger.error('获取聚合数据失败', { error });
      return {};
    }
  }

  /**
   * 获取时间分布
   */
  private async getTimeDistribution(where: any): Promise<Record<string, number>> {
    // 这里可以实现按小时、天、周等维度的时间分布统计
    // 简化实现，返回空对象
    return {};
  }

  /**
   * 获取风险分布
   */
  private async getRiskDistribution(where: any): Promise<Record<string, number>> {
    // 这里可以实现风险评分的分布统计
    // 简化实现，返回空对象
    return {};
  }

  /**
   * 检测可疑登录
   */
  private async detectSuspiciousLogins(where: any): Promise<any[]> {
    // 这里可以实现可疑登录检测逻辑
    // 例如：异地登录、频繁失败登录等
    return [];
  }

  /**
   * 检测权限滥用
   */
  private async detectPermissionAbuse(where: any): Promise<any[]> {
    // 这里可以实现权限滥用检测逻辑
    // 例如：权限提升、越权访问等
    return [];
  }

  /**
   * 检测批量操作
   */
  private async detectBulkOperations(where: any): Promise<any[]> {
    // 这里可以实现批量操作检测逻辑
    // 例如：短时间内大量操作等
    return [];
  }

  /**
   * 导出为CSV格式
   */
  private exportToCSV(data: any[]): Buffer {
    const parser = new Parser();
    const csv = parser.parse(data);
    return Buffer.from(csv, 'utf8');
  }

  /**
   * 导出为XLSX格式
   */
  private exportToXLSX(data: any[]): Buffer {
    const worksheet = XLSX.utils.json_to_sheet(data);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, '审计日志');
    return XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });
  }

  /**
   * 导出为JSON格式
   */
  private exportToJSON(data: any[]): Buffer {
    return Buffer.from(JSON.stringify(data, null, 2), 'utf8');
  }
}

// 导出单例实例
export const auditQueryService = new AuditQueryService();
