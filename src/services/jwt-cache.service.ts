/**
 * JWT令牌缓存服务
 * 实现JWT黑名单、刷新令牌缓存，支持令牌快速验证和撤销
 */

import { redisService } from './redis.service'
import { CacheKeyBuilder, CacheStrategies } from '@/config/cache-strategy'
import { logger } from '@/config/logger'

export interface TokenInfo {
  jti: string
  userId: string
  type: 'access' | 'refresh'
  issuedAt: Date
  expiresAt: Date
  deviceId?: string
  sessionId?: string
  metadata?: Record<string, any>
}

export interface BlacklistEntry {
  jti: string
  userId: string
  reason: string
  blacklistedAt: Date
  expiresAt: Date
}

/**
 * JWT缓存服务类
 */
class JwtCacheService {
  /**
   * 将令牌添加到黑名单
   */
  async blacklistToken(tokenInfo: TokenInfo, reason: string = 'Manual revocation'): Promise<void> {
    try {
      const blacklistKey = CacheKeyBuilder.jwt.blacklist(tokenInfo.jti)
      
      const blacklistEntry: BlacklistEntry = {
        jti: tokenInfo.jti,
        userId: tokenInfo.userId,
        reason,
        blacklistedAt: new Date(),
        expiresAt: tokenInfo.expiresAt
      }
      
      // 计算TTL（到令牌过期时间的秒数）
      const ttl = Math.max(
        Math.floor((tokenInfo.expiresAt.getTime() - Date.now()) / 1000),
        1 // 至少1秒
      )
      
      await redisService.setex(
        blacklistKey,
        ttl,
        JSON.stringify(blacklistEntry)
      )
      
      logger.info('令牌已添加到黑名单', {
        service: 'jwt-cache',
        jti: tokenInfo.jti,
        userId: tokenInfo.userId,
        reason,
        ttl
      })

    } catch (error) {
      logger.error('添加令牌到黑名单失败', {
        service: 'jwt-cache',
        jti: tokenInfo.jti,
        error: error instanceof Error ? error.message : String(error)
      })
      throw error
    }
  }

  /**
   * 检查令牌是否在黑名单中
   */
  async isTokenBlacklisted(jti: string): Promise<boolean> {
    try {
      const blacklistKey = CacheKeyBuilder.jwt.blacklist(jti)
      const exists = await redisService.exists(blacklistKey)
      
      if (exists) {
        logger.debug('令牌在黑名单中', {
          service: 'jwt-cache',
          jti
        })
      }
      
      return exists > 0

    } catch (error) {
      logger.error('检查令牌黑名单失败', {
        service: 'jwt-cache',
        jti,
        error: error instanceof Error ? error.message : String(error)
      })
      // 出错时为了安全起见，假设令牌被黑名单
      return true
    }
  }

  /**
   * 获取黑名单条目详情
   */
  async getBlacklistEntry(jti: string): Promise<BlacklistEntry | null> {
    try {
      const blacklistKey = CacheKeyBuilder.jwt.blacklist(jti)
      const data = await redisService.get(blacklistKey)
      
      if (!data) {
        return null
      }
      
      return JSON.parse(data) as BlacklistEntry

    } catch (error) {
      logger.error('获取黑名单条目失败', {
        service: 'jwt-cache',
        jti,
        error: error instanceof Error ? error.message : String(error)
      })
      return null
    }
  }

  /**
   * 缓存刷新令牌
   */
  async cacheRefreshToken(tokenInfo: TokenInfo): Promise<void> {
    try {
      if (tokenInfo.type !== 'refresh') {
        throw new Error('只能缓存刷新令牌')
      }
      
      const refreshKey = CacheKeyBuilder.jwt.refreshToken(tokenInfo.jti)
      
      // 计算TTL
      const ttl = Math.max(
        Math.floor((tokenInfo.expiresAt.getTime() - Date.now()) / 1000),
        1
      )
      
      await redisService.setex(
        refreshKey,
        ttl,
        JSON.stringify(tokenInfo)
      )
      
      // 如果有用户ID，建立用户到刷新令牌的映射
      if (tokenInfo.userId) {
        const userRefreshKey = `user:refresh_tokens:${tokenInfo.userId}`
        await redisService.sadd(userRefreshKey, tokenInfo.jti)
        await redisService.expire(userRefreshKey, ttl)
      }
      
      logger.debug('刷新令牌缓存成功', {
        service: 'jwt-cache',
        jti: tokenInfo.jti,
        userId: tokenInfo.userId,
        ttl
      })

    } catch (error) {
      logger.error('缓存刷新令牌失败', {
        service: 'jwt-cache',
        jti: tokenInfo.jti,
        error: error instanceof Error ? error.message : String(error)
      })
      throw error
    }
  }

  /**
   * 获取刷新令牌信息
   */
  async getRefreshToken(jti: string): Promise<TokenInfo | null> {
    try {
      const refreshKey = CacheKeyBuilder.jwt.refreshToken(jti)
      const data = await redisService.get(refreshKey)
      
      if (!data) {
        return null
      }
      
      const tokenInfo = JSON.parse(data) as TokenInfo
      
      // 检查是否过期
      if (new Date() > new Date(tokenInfo.expiresAt)) {
        await this.removeRefreshToken(jti)
        return null
      }
      
      return tokenInfo

    } catch (error) {
      logger.error('获取刷新令牌失败', {
        service: 'jwt-cache',
        jti,
        error: error instanceof Error ? error.message : String(error)
      })
      return null
    }
  }

  /**
   * 移除刷新令牌
   */
  async removeRefreshToken(jti: string): Promise<void> {
    try {
      const refreshKey = CacheKeyBuilder.jwt.refreshToken(jti)
      
      // 先获取令牌信息以便清理用户映射
      const tokenInfo = await this.getRefreshToken(jti)
      
      // 删除令牌缓存
      await redisService.del(refreshKey)
      
      // 清理用户映射
      if (tokenInfo?.userId) {
        const userRefreshKey = `user:refresh_tokens:${tokenInfo.userId}`
        await redisService.srem(userRefreshKey, jti)
      }
      
      logger.debug('刷新令牌移除成功', {
        service: 'jwt-cache',
        jti,
        userId: tokenInfo?.userId
      })

    } catch (error) {
      logger.error('移除刷新令牌失败', {
        service: 'jwt-cache',
        jti,
        error: error instanceof Error ? error.message : String(error)
      })
    }
  }

  /**
   * 获取用户的所有刷新令牌
   */
  async getUserRefreshTokens(userId: string): Promise<TokenInfo[]> {
    try {
      const userRefreshKey = `user:refresh_tokens:${userId}`
      const jtis = await redisService.smembers(userRefreshKey)
      
      const tokens: TokenInfo[] = []
      
      for (const jti of jtis) {
        const tokenInfo = await this.getRefreshToken(jti)
        if (tokenInfo) {
          tokens.push(tokenInfo)
        } else {
          // 清理无效的映射
          await redisService.srem(userRefreshKey, jti)
        }
      }
      
      return tokens

    } catch (error) {
      logger.error('获取用户刷新令牌失败', {
        service: 'jwt-cache',
        userId,
        error: error instanceof Error ? error.message : String(error)
      })
      return []
    }
  }

  /**
   * 撤销用户的所有刷新令牌
   */
  async revokeUserRefreshTokens(userId: string, reason: string = 'User logout'): Promise<number> {
    try {
      const tokens = await this.getUserRefreshTokens(userId)
      let revokedCount = 0
      
      for (const token of tokens) {
        await this.blacklistToken(token, reason)
        await this.removeRefreshToken(token.jti)
        revokedCount++
      }
      
      // 清理用户映射
      const userRefreshKey = `user:refresh_tokens:${userId}`
      await redisService.del(userRefreshKey)
      
      logger.info('用户刷新令牌批量撤销', {
        service: 'jwt-cache',
        userId,
        revokedCount,
        reason
      })
      
      return revokedCount

    } catch (error) {
      logger.error('撤销用户刷新令牌失败', {
        service: 'jwt-cache',
        userId,
        error: error instanceof Error ? error.message : String(error)
      })
      return 0
    }
  }

  /**
   * 缓存访问令牌（用于快速验证）
   */
  async cacheAccessToken(tokenInfo: TokenInfo): Promise<void> {
    try {
      if (tokenInfo.type !== 'access') {
        throw new Error('只能缓存访问令牌')
      }
      
      const accessKey = CacheKeyBuilder.jwt.accessToken(tokenInfo.jti)
      
      // 访问令牌通常生命周期较短，只缓存基本信息
      const cacheData = {
        jti: tokenInfo.jti,
        userId: tokenInfo.userId,
        expiresAt: tokenInfo.expiresAt
      }
      
      const ttl = Math.max(
        Math.floor((tokenInfo.expiresAt.getTime() - Date.now()) / 1000),
        1
      )
      
      await redisService.setex(
        accessKey,
        ttl,
        JSON.stringify(cacheData)
      )
      
      logger.debug('访问令牌缓存成功', {
        service: 'jwt-cache',
        jti: tokenInfo.jti,
        userId: tokenInfo.userId,
        ttl
      })

    } catch (error) {
      logger.error('缓存访问令牌失败', {
        service: 'jwt-cache',
        jti: tokenInfo.jti,
        error: error instanceof Error ? error.message : String(error)
      })
    }
  }

  /**
   * 验证令牌（检查黑名单和缓存）
   */
  async validateToken(jti: string): Promise<{
    valid: boolean
    reason?: string
    tokenInfo?: Partial<TokenInfo>
  }> {
    try {
      // 首先检查黑名单
      const isBlacklisted = await this.isTokenBlacklisted(jti)
      if (isBlacklisted) {
        const blacklistEntry = await this.getBlacklistEntry(jti)
        return {
          valid: false,
          reason: blacklistEntry?.reason || 'Token is blacklisted'
        }
      }
      
      // 尝试从访问令牌缓存获取
      const accessKey = CacheKeyBuilder.jwt.accessToken(jti)
      const accessData = await redisService.get(accessKey)
      
      if (accessData) {
        const tokenInfo = JSON.parse(accessData)
        return {
          valid: true,
          tokenInfo
        }
      }
      
      // 尝试从刷新令牌缓存获取
      const refreshToken = await this.getRefreshToken(jti)
      if (refreshToken) {
        return {
          valid: true,
          tokenInfo: refreshToken
        }
      }
      
      // 令牌不在缓存中，需要进一步验证
      return {
        valid: true,
        reason: 'Token not in cache, requires full validation'
      }

    } catch (error) {
      logger.error('令牌验证失败', {
        service: 'jwt-cache',
        jti,
        error: error instanceof Error ? error.message : String(error)
      })
      
      return {
        valid: false,
        reason: 'Validation error'
      }
    }
  }

  /**
   * 清理过期的令牌缓存
   */
  async cleanupExpiredTokens(): Promise<number> {
    try {
      // Redis会自动清理过期键，这里主要是清理用户映射中的无效引用
      let cleanedCount = 0
      
      // 这里可以实现更复杂的清理逻辑
      // 例如扫描所有用户的刷新令牌映射，清理无效的引用
      
      logger.info('过期令牌清理完成', {
        service: 'jwt-cache',
        cleanedCount
      })
      
      return cleanedCount

    } catch (error) {
      logger.error('清理过期令牌失败', {
        service: 'jwt-cache',
        error: error instanceof Error ? error.message : String(error)
      })
      return 0
    }
  }

  /**
   * 获取令牌缓存统计信息
   */
  async getTokenStats(): Promise<{
    blacklistedTokens: number
    cachedRefreshTokens: number
    cachedAccessTokens: number
  }> {
    try {
      // 这里可以实现更详细的统计逻辑
      // 简化实现，返回基本统计
      return {
        blacklistedTokens: 0,
        cachedRefreshTokens: 0,
        cachedAccessTokens: 0
      }

    } catch (error) {
      logger.error('获取令牌统计失败', {
        service: 'jwt-cache',
        error: error instanceof Error ? error.message : String(error)
      })
      
      return {
        blacklistedTokens: 0,
        cachedRefreshTokens: 0,
        cachedAccessTokens: 0
      }
    }
  }
}

// 导出服务实例
export const jwtCacheService = new JwtCacheService()
export default jwtCacheService
