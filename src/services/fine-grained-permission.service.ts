/**
 * 细粒度权限控制服务
 * 实现资源级别的权限管理和动态权限调整
 */

import { prisma } from '@/config/database';
import { logger } from '@/config/logger';
import { enhancedCacheService } from './enhanced-cache.service';
import { v4 as uuidv4 } from 'uuid';

/**
 * 资源类型枚举
 */
export enum ResourceType {
  USER = 'user',
  APPLICATION = 'application',
  PERMISSION = 'permission',
  ROLE = 'role',
  ORGANIZATION = 'organization',
  API_ENDPOINT = 'api_endpoint',
  DATA_RECORD = 'data_record',
  FILE = 'file',
  CUSTOM = 'custom'
}

/**
 * 操作类型枚举
 */
export enum ActionType {
  CREATE = 'create',
  READ = 'read',
  UPDATE = 'update',
  DELETE = 'delete',
  EXECUTE = 'execute',
  APPROVE = 'approve',
  REJECT = 'reject',
  ASSIGN = 'assign',
  REVOKE = 'revoke',
  CUSTOM = 'custom'
}

/**
 * 权限效果枚举
 */
export enum PermissionEffect {
  ALLOW = 'allow',
  DENY = 'deny'
}

/**
 * 资源权限接口
 */
export interface ResourcePermission {
  id: string;
  resourceType: ResourceType;
  resourceId: string;
  resourcePath?: string;
  action: ActionType;
  effect: PermissionEffect;
  conditions?: PermissionCondition[];
  metadata?: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
  expiresAt?: Date;
}

/**
 * 权限条件接口
 */
export interface PermissionCondition {
  type: 'time' | 'location' | 'attribute' | 'context';
  operator: 'eq' | 'ne' | 'gt' | 'lt' | 'gte' | 'lte' | 'in' | 'not_in' | 'contains' | 'regex';
  field: string;
  value: any;
  description?: string;
}

/**
 * 权限策略接口
 */
export interface PermissionPolicy {
  id: string;
  name: string;
  description: string;
  version: string;
  isActive: boolean;
  rules: PermissionRule[];
  priority: number;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * 权限规则接口
 */
export interface PermissionRule {
  id: string;
  name: string;
  description: string;
  subjects: string[]; // 用户ID、角色ID等
  resources: ResourceSelector[];
  actions: ActionType[];
  effect: PermissionEffect;
  conditions?: PermissionCondition[];
  priority: number;
}

/**
 * 资源选择器接口
 */
export interface ResourceSelector {
  type: ResourceType;
  pattern: string; // 支持通配符和正则表达式
  attributes?: Record<string, any>;
}

/**
 * 权限评估上下文接口
 */
export interface PermissionContext {
  userId: string;
  userRoles: string[];
  userAttributes: Record<string, any>;
  resourceType: ResourceType;
  resourceId: string;
  resourceAttributes?: Record<string, any>;
  action: ActionType;
  environment: {
    timestamp: Date;
    ipAddress?: string;
    userAgent?: string;
    location?: string;
    sessionId?: string;
  };
  additionalContext?: Record<string, any>;
}

/**
 * 权限评估结果接口
 */
export interface PermissionEvaluationResult {
  allowed: boolean;
  effect: PermissionEffect;
  reason: string;
  appliedRules: string[];
  conditions: PermissionCondition[];
  metadata?: Record<string, any>;
  evaluationTime: number;
}

export class FineGrainedPermissionService {

  /**
   * 评估权限
   */
  async evaluatePermission(context: PermissionContext): Promise<PermissionEvaluationResult> {
    const startTime = Date.now();
    
    try {
      // 检查缓存
      const cacheKey = this.generateCacheKey(context);
      const cachedResult = await enhancedCacheService.get<PermissionEvaluationResult>(
        cacheKey, 
        { prefix: 'permission_eval' }
      );

      if (cachedResult) {
        logger.debug('权限评估缓存命中', { userId: context.userId, resourceId: context.resourceId });
        return cachedResult;
      }

      // 获取适用的权限策略
      const applicablePolicies = await this.getApplicablePolicies(context);

      // 评估权限规则
      const result = await this.evaluateRules(context, applicablePolicies);

      // 缓存结果
      await enhancedCacheService.set(
        cacheKey,
        result,
        300, // 5分钟缓存
        { prefix: 'permission_eval' }
      );

      // 记录权限评估日志
      await this.logPermissionEvaluation(context, result);

      return result;

    } catch (error) {
      logger.error('权限评估失败', { error, context });
      
      // 默认拒绝策略
      return {
        allowed: false,
        effect: PermissionEffect.DENY,
        reason: '权限评估失败，默认拒绝访问',
        appliedRules: [],
        conditions: [],
        evaluationTime: Date.now() - startTime
      };
    }
  }

  /**
   * 批量评估权限
   */
  async batchEvaluatePermissions(
    contexts: PermissionContext[]
  ): Promise<PermissionEvaluationResult[]> {
    try {
      const results = await Promise.all(
        contexts.map(context => this.evaluatePermission(context))
      );

      logger.debug('批量权限评估完成', { count: contexts.length });
      return results;

    } catch (error) {
      logger.error('批量权限评估失败', { error });
      throw error;
    }
  }

  /**
   * 创建权限策略
   */
  async createPermissionPolicy(policy: Omit<PermissionPolicy, 'id' | 'createdAt' | 'updatedAt'>): Promise<PermissionPolicy> {
    try {
      const newPolicy: PermissionPolicy = {
        ...policy,
        id: uuidv4(),
        createdAt: new Date(),
        updatedAt: new Date()
      };

      // 保存到数据库
      await prisma.permissionPolicy.create({
        data: {
          id: newPolicy.id,
          name: newPolicy.name,
          description: newPolicy.description,
          version: newPolicy.version,
          isActive: newPolicy.isActive,
          rules: newPolicy.rules,
          priority: newPolicy.priority
        }
      });

      // 清除相关缓存
      await this.clearPermissionCache();

      logger.info('权限策略创建成功', { policyId: newPolicy.id, name: newPolicy.name });
      return newPolicy;

    } catch (error) {
      logger.error('创建权限策略失败', { error, policy });
      throw error;
    }
  }

  /**
   * 更新权限策略
   */
  async updatePermissionPolicy(
    policyId: string, 
    updates: Partial<Omit<PermissionPolicy, 'id' | 'createdAt'>>
  ): Promise<PermissionPolicy> {
    try {
      const updatedPolicy = await prisma.permissionPolicy.update({
        where: { id: policyId },
        data: {
          ...updates,
          updatedAt: new Date()
        }
      });

      // 清除相关缓存
      await this.clearPermissionCache();

      logger.info('权限策略更新成功', { policyId });
      return updatedPolicy as PermissionPolicy;

    } catch (error) {
      logger.error('更新权限策略失败', { error, policyId });
      throw error;
    }
  }

  /**
   * 删除权限策略
   */
  async deletePermissionPolicy(policyId: string): Promise<void> {
    try {
      await prisma.permissionPolicy.delete({
        where: { id: policyId }
      });

      // 清除相关缓存
      await this.clearPermissionCache();

      logger.info('权限策略删除成功', { policyId });

    } catch (error) {
      logger.error('删除权限策略失败', { error, policyId });
      throw error;
    }
  }

  /**
   * 获取用户的资源权限
   */
  async getUserResourcePermissions(
    userId: string,
    resourceType: ResourceType,
    resourceId?: string
  ): Promise<ResourcePermission[]> {
    try {
      const cacheKey = `user_permissions:${userId}:${resourceType}:${resourceId || 'all'}`;
      const cached = await enhancedCacheService.get<ResourcePermission[]>(cacheKey);

      if (cached) {
        return cached;
      }

      // 获取用户角色
      const userRoles = await this.getUserRoles(userId);

      // 构建查询条件
      const where: any = {
        OR: [
          { subjectId: userId, subjectType: 'user' },
          { subjectId: { in: userRoles }, subjectType: 'role' }
        ],
        resourceType
      };

      if (resourceId) {
        where.resourceId = resourceId;
      }

      const permissions = await prisma.resourcePermission.findMany({
        where,
        orderBy: { createdAt: 'desc' }
      });

      // 缓存结果
      await enhancedCacheService.set(cacheKey, permissions, 600); // 10分钟缓存

      return permissions as ResourcePermission[];

    } catch (error) {
      logger.error('获取用户资源权限失败', { error, userId, resourceType, resourceId });
      return [];
    }
  }

  /**
   * 授予资源权限
   */
  async grantResourcePermission(
    subjectId: string,
    subjectType: 'user' | 'role',
    resourceType: ResourceType,
    resourceId: string,
    action: ActionType,
    effect: PermissionEffect = PermissionEffect.ALLOW,
    conditions?: PermissionCondition[],
    expiresAt?: Date
  ): Promise<ResourcePermission> {
    try {
      const permission: ResourcePermission = {
        id: uuidv4(),
        resourceType,
        resourceId,
        action,
        effect,
        conditions,
        createdAt: new Date(),
        updatedAt: new Date(),
        expiresAt
      };

      await prisma.resourcePermission.create({
        data: {
          id: permission.id,
          subjectId,
          subjectType,
          resourceType: permission.resourceType,
          resourceId: permission.resourceId,
          action: permission.action,
          effect: permission.effect,
          conditions: permission.conditions || [],
          expiresAt: permission.expiresAt
        }
      });

      // 清除相关缓存
      await this.clearUserPermissionCache(subjectId, subjectType);

      logger.info('资源权限授予成功', { 
        subjectId, 
        subjectType, 
        resourceType, 
        resourceId, 
        action 
      });

      return permission;

    } catch (error) {
      logger.error('授予资源权限失败', { error, subjectId, resourceType, resourceId });
      throw error;
    }
  }

  /**
   * 撤销资源权限
   */
  async revokeResourcePermission(permissionId: string): Promise<void> {
    try {
      const permission = await prisma.resourcePermission.findUnique({
        where: { id: permissionId }
      });

      if (!permission) {
        throw new Error('权限不存在');
      }

      await prisma.resourcePermission.delete({
        where: { id: permissionId }
      });

      // 清除相关缓存
      await this.clearUserPermissionCache(permission.subjectId, permission.subjectType);

      logger.info('资源权限撤销成功', { permissionId });

    } catch (error) {
      logger.error('撤销资源权限失败', { error, permissionId });
      throw error;
    }
  }

  /**
   * 获取适用的权限策略
   */
  private async getApplicablePolicies(context: PermissionContext): Promise<PermissionPolicy[]> {
    const cacheKey = `applicable_policies:${context.userId}:${context.resourceType}`;
    const cached = await enhancedCacheService.get<PermissionPolicy[]>(cacheKey);

    if (cached) {
      return cached;
    }

    const policies = await prisma.permissionPolicy.findMany({
      where: {
        isActive: true
      },
      orderBy: { priority: 'desc' }
    });

    const applicablePolicies = policies.filter(policy => 
      this.isPolicyApplicable(policy as PermissionPolicy, context)
    );

    // 缓存结果
    await enhancedCacheService.set(cacheKey, applicablePolicies, 300);

    return applicablePolicies as PermissionPolicy[];
  }

  /**
   * 检查策略是否适用
   */
  private isPolicyApplicable(policy: PermissionPolicy, context: PermissionContext): boolean {
    return policy.rules.some(rule => {
      // 检查主体匹配
      const subjectMatch = rule.subjects.some(subject => 
        subject === context.userId || context.userRoles.includes(subject)
      );

      if (!subjectMatch) {
        return false;
      }

      // 检查资源匹配
      const resourceMatch = rule.resources.some(resource => 
        this.isResourceMatch(resource, context)
      );

      if (!resourceMatch) {
        return false;
      }

      // 检查操作匹配
      const actionMatch = rule.actions.includes(context.action);

      return actionMatch;
    });
  }

  /**
   * 检查资源是否匹配
   */
  private isResourceMatch(selector: ResourceSelector, context: PermissionContext): boolean {
    if (selector.type !== context.resourceType) {
      return false;
    }

    // 支持通配符匹配
    if (selector.pattern === '*') {
      return true;
    }

    // 支持正则表达式匹配
    if (selector.pattern.startsWith('/') && selector.pattern.endsWith('/')) {
      const regex = new RegExp(selector.pattern.slice(1, -1));
      return regex.test(context.resourceId);
    }

    // 精确匹配
    return selector.pattern === context.resourceId;
  }

  /**
   * 评估权限规则
   */
  private async evaluateRules(
    context: PermissionContext,
    policies: PermissionPolicy[]
  ): Promise<PermissionEvaluationResult> {
    const startTime = Date.now();
    const appliedRules: string[] = [];
    let finalEffect = PermissionEffect.DENY;
    let reason = '没有匹配的权限规则';

    // 按优先级评估规则
    for (const policy of policies) {
      for (const rule of rule.sort((a, b) => b.priority - a.priority)) {
        if (this.isRuleApplicable(rule, context)) {
          appliedRules.push(rule.id);

          // 检查条件
          if (rule.conditions && !this.evaluateConditions(rule.conditions, context)) {
            continue;
          }

          // 应用规则效果
          if (rule.effect === PermissionEffect.DENY) {
            // 拒绝规则优先级最高
            finalEffect = PermissionEffect.DENY;
            reason = `被规则 ${rule.name} 拒绝`;
            break;
          } else if (rule.effect === PermissionEffect.ALLOW) {
            finalEffect = PermissionEffect.ALLOW;
            reason = `被规则 ${rule.name} 允许`;
          }
        }
      }

      if (finalEffect === PermissionEffect.DENY) {
        break;
      }
    }

    return {
      allowed: finalEffect === PermissionEffect.ALLOW,
      effect: finalEffect,
      reason,
      appliedRules,
      conditions: [],
      evaluationTime: Date.now() - startTime
    };
  }

  /**
   * 检查规则是否适用
   */
  private isRuleApplicable(rule: PermissionRule, context: PermissionContext): boolean {
    // 检查主体
    const subjectMatch = rule.subjects.some(subject => 
      subject === context.userId || context.userRoles.includes(subject)
    );

    if (!subjectMatch) {
      return false;
    }

    // 检查资源
    const resourceMatch = rule.resources.some(resource => 
      this.isResourceMatch(resource, context)
    );

    if (!resourceMatch) {
      return false;
    }

    // 检查操作
    return rule.actions.includes(context.action);
  }

  /**
   * 评估条件
   */
  private evaluateConditions(conditions: PermissionCondition[], context: PermissionContext): boolean {
    return conditions.every(condition => this.evaluateCondition(condition, context));
  }

  /**
   * 评估单个条件
   */
  private evaluateCondition(condition: PermissionCondition, context: PermissionContext): boolean {
    let fieldValue: any;

    // 获取字段值
    switch (condition.type) {
      case 'time':
        fieldValue = context.environment.timestamp;
        break;
      case 'location':
        fieldValue = context.environment.location;
        break;
      case 'attribute':
        fieldValue = context.userAttributes[condition.field] || context.resourceAttributes?.[condition.field];
        break;
      case 'context':
        fieldValue = context.additionalContext?.[condition.field];
        break;
      default:
        return false;
    }

    // 应用操作符
    switch (condition.operator) {
      case 'eq':
        return fieldValue === condition.value;
      case 'ne':
        return fieldValue !== condition.value;
      case 'gt':
        return fieldValue > condition.value;
      case 'lt':
        return fieldValue < condition.value;
      case 'gte':
        return fieldValue >= condition.value;
      case 'lte':
        return fieldValue <= condition.value;
      case 'in':
        return Array.isArray(condition.value) && condition.value.includes(fieldValue);
      case 'not_in':
        return Array.isArray(condition.value) && !condition.value.includes(fieldValue);
      case 'contains':
        return String(fieldValue).includes(String(condition.value));
      case 'regex':
        return new RegExp(condition.value).test(String(fieldValue));
      default:
        return false;
    }
  }

  /**
   * 获取用户角色
   */
  private async getUserRoles(userId: string): Promise<string[]> {
    const userRoles = await prisma.userRole.findMany({
      where: { userId },
      include: { role: true }
    });

    return userRoles.map(ur => ur.role.id);
  }

  /**
   * 生成缓存键
   */
  private generateCacheKey(context: PermissionContext): string {
    const key = `${context.userId}:${context.resourceType}:${context.resourceId}:${context.action}`;
    return Buffer.from(key).toString('base64');
  }

  /**
   * 清除权限缓存
   */
  private async clearPermissionCache(): Promise<void> {
    await enhancedCacheService.delPattern('permission_eval:*');
    await enhancedCacheService.delPattern('applicable_policies:*');
  }

  /**
   * 清除用户权限缓存
   */
  private async clearUserPermissionCache(subjectId: string, subjectType: string): Promise<void> {
    await enhancedCacheService.delPattern(`user_permissions:${subjectId}:*`);
    if (subjectType === 'role') {
      // 如果是角色权限变更，需要清除所有拥有该角色的用户缓存
      // 这里简化处理，清除所有用户权限缓存
      await enhancedCacheService.delPattern('user_permissions:*');
    }
  }

  /**
   * 记录权限评估日志
   */
  private async logPermissionEvaluation(
    context: PermissionContext,
    result: PermissionEvaluationResult
  ): Promise<void> {
    try {
      await prisma.auditLog.create({
        data: {
          id: uuidv4(),
          userId: context.userId,
          action: 'permission_evaluation',
          resource: context.resourceType,
          resourceId: context.resourceId,
          details: {
            action: context.action,
            allowed: result.allowed,
            effect: result.effect,
            reason: result.reason,
            appliedRules: result.appliedRules,
            evaluationTime: result.evaluationTime
          },
          ipAddress: context.environment.ipAddress,
          userAgent: context.environment.userAgent
        }
      });
    } catch (error) {
      logger.error('记录权限评估日志失败', { error });
    }
  }
}

// 导出单例实例
export const fineGrainedPermissionService = new FineGrainedPermissionService();
