/**
 * 管理员数据提供者服务
 * 为React Admin提供数据接口，支持用户、应用、权限等资源的CRUD操作
 */

import { DataProvider } from 'react-admin';
import { prisma } from '@/config/database';
import { logger } from '@/config/logger';

/**
 * 分页参数接口
 */
interface PaginationParams {
  page: number;
  perPage: number;
}

/**
 * 排序参数接口
 */
interface SortParams {
  field: string;
  order: 'ASC' | 'DESC';
}

/**
 * 过滤参数接口
 */
interface FilterParams {
  [key: string]: any;
}

/**
 * React Admin 数据提供者实现
 */
export class AdminDataProviderService implements DataProvider {

  /**
   * 获取资源列表
   */
  async getList(
    resource: string,
    params: {
      pagination: PaginationParams;
      sort: SortParams;
      filter: FilterParams;
    }
  ) {
    try {
      const { pagination, sort, filter } = params;
      const { page, perPage } = pagination;
      const { field, order } = sort;

      // 计算偏移量
      const skip = (page - 1) * perPage;
      const take = perPage;

      // 构建排序条件
      const orderBy = { [field]: order.toLowerCase() };

      // 构建过滤条件
      const where = this.buildWhereClause(resource, filter);

      let data: any[] = [];
      let total = 0;

      switch (resource) {
        case 'users':
          [data, total] = await Promise.all([
            prisma.user.findMany({
              where,
              skip,
              take,
              orderBy,
              include: {
                profile: true
              }
            }),
            prisma.user.count({ where })
          ]);
          break;

        case 'applications':
          [data, total] = await Promise.all([
            prisma.application.findMany({
              where,
              skip,
              take,
              orderBy
            }),
            prisma.application.count({ where })
          ]);
          break;

        case 'permissions':
          [data, total] = await Promise.all([
            prisma.permission.findMany({
              where,
              skip,
              take,
              orderBy
            }),
            prisma.permission.count({ where })
          ]);
          break;

        case 'permission-requests':
          [data, total] = await Promise.all([
            prisma.permissionRequest.findMany({
              where,
              skip,
              take,
              orderBy,
              include: {
                user: {
                  select: {
                    id: true,
                    username: true,
                    email: true
                  }
                },
                sourceApplication: {
                  select: {
                    id: true,
                    name: true
                  }
                },
                targetApplication: {
                  select: {
                    id: true,
                    name: true
                  }
                }
              }
            }),
            prisma.permissionRequest.count({ where })
          ]);
          break;

        case 'audit-logs':
          [data, total] = await Promise.all([
            prisma.auditLog.findMany({
              where,
              skip,
              take,
              orderBy,
              include: {
                user: {
                  select: {
                    id: true,
                    username: true,
                    email: true
                  }
                }
              }
            }),
            prisma.auditLog.count({ where })
          ]);
          break;

        default:
          throw new Error(`未支持的资源类型: ${resource}`);
      }

      logger.info('获取资源列表成功', {
        resource,
        count: data.length,
        total,
        page,
        perPage
      });

      return {
        data: data.map(item => ({ ...item, id: item.id })),
        total
      };

    } catch (error) {
      logger.error('获取资源列表失败', { error, resource, params });
      throw error;
    }
  }

  /**
   * 获取单个资源
   */
  async getOne(resource: string, params: { id: string }) {
    try {
      const { id } = params;
      let data: any = null;

      switch (resource) {
        case 'users':
          data = await prisma.user.findUnique({
            where: { id },
            include: {
              profile: true
            }
          });
          break;

        case 'applications':
          data = await prisma.application.findUnique({
            where: { id }
          });
          break;

        case 'permissions':
          data = await prisma.permission.findUnique({
            where: { id }
          });
          break;

        case 'permission-requests':
          data = await prisma.permissionRequest.findUnique({
            where: { id },
            include: {
              user: {
                select: {
                  id: true,
                  username: true,
                  email: true
                }
              },
              sourceApplication: {
                select: {
                  id: true,
                  name: true
                }
              },
              targetApplication: {
                select: {
                  id: true,
                  name: true
                }
              }
            }
          });
          break;

        case 'audit-logs':
          data = await prisma.auditLog.findUnique({
            where: { id },
            include: {
              user: {
                select: {
                  id: true,
                  username: true,
                  email: true
                }
              }
            }
          });
          break;

        default:
          throw new Error(`未支持的资源类型: ${resource}`);
      }

      if (!data) {
        throw new Error(`资源不存在: ${resource}/${id}`);
      }

      logger.info('获取单个资源成功', { resource, id });

      return { data: { ...data, id: data.id } };

    } catch (error) {
      logger.error('获取单个资源失败', { error, resource, params });
      throw error;
    }
  }

  /**
   * 创建资源
   */
  async create(resource: string, params: { data: any }) {
    try {
      const { data: inputData } = params;
      let data: any = null;

      switch (resource) {
        case 'users':
          data = await prisma.user.create({
            data: {
              ...inputData,
              profile: inputData.profile ? {
                create: inputData.profile
              } : undefined
            },
            include: {
              profile: true
            }
          });
          break;

        case 'applications':
          data = await prisma.application.create({
            data: inputData
          });
          break;

        case 'permissions':
          data = await prisma.permission.create({
            data: inputData
          });
          break;

        default:
          throw new Error(`不支持创建资源类型: ${resource}`);
      }

      logger.info('创建资源成功', { resource, id: data.id });

      return { data: { ...data, id: data.id } };

    } catch (error) {
      logger.error('创建资源失败', { error, resource, params });
      throw error;
    }
  }

  /**
   * 更新资源
   */
  async update(resource: string, params: { id: string; data: any }) {
    try {
      const { id, data: inputData } = params;
      let data: any = null;

      switch (resource) {
        case 'users':
          data = await prisma.user.update({
            where: { id },
            data: {
              ...inputData,
              profile: inputData.profile ? {
                upsert: {
                  create: inputData.profile,
                  update: inputData.profile
                }
              } : undefined
            },
            include: {
              profile: true
            }
          });
          break;

        case 'applications':
          data = await prisma.application.update({
            where: { id },
            data: inputData
          });
          break;

        case 'permissions':
          data = await prisma.permission.update({
            where: { id },
            data: inputData
          });
          break;

        case 'permission-requests':
          data = await prisma.permissionRequest.update({
            where: { id },
            data: inputData
          });
          break;

        default:
          throw new Error(`不支持更新资源类型: ${resource}`);
      }

      logger.info('更新资源成功', { resource, id });

      return { data: { ...data, id: data.id } };

    } catch (error) {
      logger.error('更新资源失败', { error, resource, params });
      throw error;
    }
  }

  /**
   * 删除资源
   */
  async delete(resource: string, params: { id: string }) {
    try {
      const { id } = params;
      let data: any = null;

      switch (resource) {
        case 'users':
          data = await prisma.user.delete({
            where: { id }
          });
          break;

        case 'applications':
          data = await prisma.application.delete({
            where: { id }
          });
          break;

        case 'permissions':
          data = await prisma.permission.delete({
            where: { id }
          });
          break;

        default:
          throw new Error(`不支持删除资源类型: ${resource}`);
      }

      logger.info('删除资源成功', { resource, id });

      return { data: { ...data, id: data.id } };

    } catch (error) {
      logger.error('删除资源失败', { error, resource, params });
      throw error;
    }
  }

  /**
   * 批量删除资源
   */
  async deleteMany(resource: string, params: { ids: string[] }) {
    try {
      const { ids } = params;

      switch (resource) {
        case 'users':
          await prisma.user.deleteMany({
            where: { id: { in: ids } }
          });
          break;

        case 'applications':
          await prisma.application.deleteMany({
            where: { id: { in: ids } }
          });
          break;

        case 'permissions':
          await prisma.permission.deleteMany({
            where: { id: { in: ids } }
          });
          break;

        default:
          throw new Error(`不支持批量删除资源类型: ${resource}`);
      }

      logger.info('批量删除资源成功', { resource, count: ids.length });

      return { data: ids };

    } catch (error) {
      logger.error('批量删除资源失败', { error, resource, params });
      throw error;
    }
  }

  /**
   * 获取多个资源
   */
  async getMany(resource: string, params: { ids: string[] }) {
    try {
      const { ids } = params;
      let data: any[] = [];

      switch (resource) {
        case 'users':
          data = await prisma.user.findMany({
            where: { id: { in: ids } },
            include: {
              profile: true
            }
          });
          break;

        case 'applications':
          data = await prisma.application.findMany({
            where: { id: { in: ids } }
          });
          break;

        case 'permissions':
          data = await prisma.permission.findMany({
            where: { id: { in: ids } }
          });
          break;

        default:
          throw new Error(`未支持的资源类型: ${resource}`);
      }

      logger.info('获取多个资源成功', { resource, count: data.length });

      return { data: data.map(item => ({ ...item, id: item.id })) };

    } catch (error) {
      logger.error('获取多个资源失败', { error, resource, params });
      throw error;
    }
  }

  /**
   * 构建查询条件
   */
  private buildWhereClause(resource: string, filter: FilterParams): any {
    const where: any = {};

    // 通用过滤条件
    if (filter.q) {
      // 全文搜索
      switch (resource) {
        case 'users':
          where.OR = [
            { username: { contains: filter.q, mode: 'insensitive' } },
            { email: { contains: filter.q, mode: 'insensitive' } }
          ];
          break;
        case 'applications':
          where.OR = [
            { name: { contains: filter.q, mode: 'insensitive' } },
            { description: { contains: filter.q, mode: 'insensitive' } }
          ];
          break;
        case 'permissions':
          where.OR = [
            { name: { contains: filter.q, mode: 'insensitive' } },
            { displayName: { contains: filter.q, mode: 'insensitive' } },
            { description: { contains: filter.q, mode: 'insensitive' } }
          ];
          break;
      }
    }

    // 特定字段过滤
    Object.keys(filter).forEach(key => {
      if (key !== 'q' && filter[key] !== undefined && filter[key] !== '') {
        where[key] = filter[key];
      }
    });

    return where;
  }
}

// 导出单例实例
export const adminDataProviderService = new AdminDataProviderService();
