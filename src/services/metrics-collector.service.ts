/**
 * 高级指标收集器服务
 * 收集、聚合和分析系统各种指标数据
 */

import { EventEmitter } from 'events';
import { logger } from '@/config/logger';

/**
 * 指标类型枚举
 */
export enum MetricType {
  COUNTER = 'counter',           // 计数器
  GAUGE = 'gauge',              // 仪表盘
  HISTOGRAM = 'histogram',       // 直方图
  SUMMARY = 'summary',          // 摘要
  TIMER = 'timer'               // 计时器
}

/**
 * 指标数据接口
 */
export interface MetricData {
  name: string;
  type: MetricType;
  value: number;
  timestamp: Date;
  labels?: Record<string, string>;
  tags?: string[];
  metadata?: Record<string, any>;
}

/**
 * 聚合指标接口
 */
export interface AggregatedMetric {
  name: string;
  type: MetricType;
  count: number;
  sum: number;
  min: number;
  max: number;
  avg: number;
  p50: number;
  p95: number;
  p99: number;
  labels?: Record<string, string>;
  timeRange: {
    start: Date;
    end: Date;
  };
}

/**
 * 指标查询选项
 */
export interface MetricQueryOptions {
  name?: string;
  type?: MetricType;
  labels?: Record<string, string>;
  tags?: string[];
  startTime?: Date;
  endTime?: Date;
  limit?: number;
  aggregation?: 'sum' | 'avg' | 'min' | 'max' | 'count';
  groupBy?: string[];
  interval?: number; // 聚合间隔（毫秒）
}

/**
 * 告警规则接口
 */
export interface AlertRule {
  id: string;
  name: string;
  metricName: string;
  condition: 'gt' | 'lt' | 'eq' | 'gte' | 'lte';
  threshold: number;
  duration: number; // 持续时间（毫秒）
  severity: 'low' | 'medium' | 'high' | 'critical';
  enabled: boolean;
  labels?: Record<string, string>;
  description?: string;
  actions?: string[]; // 告警动作
}

/**
 * 告警事件接口
 */
export interface AlertEvent {
  ruleId: string;
  ruleName: string;
  metricName: string;
  currentValue: number;
  threshold: number;
  severity: string;
  timestamp: Date;
  labels?: Record<string, string>;
  description?: string;
}

/**
 * 高级指标收集器服务
 */
export class MetricsCollectorService extends EventEmitter {
  private metrics: Map<string, MetricData[]> = new Map();
  private alertRules: Map<string, AlertRule> = new Map();
  private activeAlerts: Map<string, AlertEvent> = new Map();
  private readonly maxMetricsPerName = 10000;
  private readonly maxRetentionTime = 24 * 60 * 60 * 1000; // 24小时
  private cleanupInterval?: NodeJS.Timeout;

  constructor() {
    super();
    this.startCleanupTimer();
  }

  /**
   * 记录指标
   */
  recordMetric(metric: Omit<MetricData, 'timestamp'>): void {
    const metricData: MetricData = {
      ...metric,
      timestamp: new Date()
    };

    // 获取或创建指标数组
    if (!this.metrics.has(metric.name)) {
      this.metrics.set(metric.name, []);
    }

    const metricArray = this.metrics.get(metric.name)!;
    metricArray.push(metricData);

    // 限制每个指标的数据量
    if (metricArray.length > this.maxMetricsPerName) {
      metricArray.shift();
    }

    // 发出指标事件
    this.emit('metricRecorded', metricData);

    // 检查告警规则
    this.checkAlertRules(metricData);

    logger.debug('指标已记录', {
      name: metric.name,
      type: metric.type,
      value: metric.value,
      labels: metric.labels
    });
  }

  /**
   * 记录计数器指标
   */
  incrementCounter(name: string, value = 1, labels?: Record<string, string>): void {
    this.recordMetric({
      name,
      type: MetricType.COUNTER,
      value,
      labels
    });
  }

  /**
   * 记录仪表盘指标
   */
  setGauge(name: string, value: number, labels?: Record<string, string>): void {
    this.recordMetric({
      name,
      type: MetricType.GAUGE,
      value,
      labels
    });
  }

  /**
   * 记录直方图指标
   */
  recordHistogram(name: string, value: number, labels?: Record<string, string>): void {
    this.recordMetric({
      name,
      type: MetricType.HISTOGRAM,
      value,
      labels
    });
  }

  /**
   * 记录计时器指标
   */
  recordTimer(name: string, duration: number, labels?: Record<string, string>): void {
    this.recordMetric({
      name,
      type: MetricType.TIMER,
      value: duration,
      labels
    });
  }

  /**
   * 查询指标
   */
  queryMetrics(options: MetricQueryOptions = {}): MetricData[] {
    let results: MetricData[] = [];

    // 如果指定了指标名称
    if (options.name) {
      const metrics = this.metrics.get(options.name) || [];
      results = [...metrics];
    } else {
      // 获取所有指标
      for (const metricArray of this.metrics.values()) {
        results.push(...metricArray);
      }
    }

    // 应用过滤条件
    results = this.applyFilters(results, options);

    // 应用限制
    if (options.limit) {
      results = results.slice(-options.limit);
    }

    return results;
  }

  /**
   * 聚合指标
   */
  aggregateMetrics(options: MetricQueryOptions = {}): AggregatedMetric[] {
    const metrics = this.queryMetrics(options);
    const grouped = this.groupMetrics(metrics, options.groupBy || ['name']);
    const aggregated: AggregatedMetric[] = [];

    for (const [key, metricGroup] of grouped) {
      if (metricGroup.length === 0) continue;

      const values = metricGroup.map(m => m.value).sort((a, b) => a - b);
      const sum = values.reduce((acc, val) => acc + val, 0);
      const count = values.length;
      const min = values[0];
      const max = values[values.length - 1];
      const avg = sum / count;

      // 计算百分位数
      const p50Index = Math.floor(count * 0.5);
      const p95Index = Math.floor(count * 0.95);
      const p99Index = Math.floor(count * 0.99);

      const p50 = values[p50Index] || 0;
      const p95 = values[p95Index] || 0;
      const p99 = values[p99Index] || 0;

      const timeRange = {
        start: new Date(Math.min(...metricGroup.map(m => m.timestamp.getTime()))),
        end: new Date(Math.max(...metricGroup.map(m => m.timestamp.getTime())))
      };

      aggregated.push({
        name: metricGroup[0].name,
        type: metricGroup[0].type,
        count,
        sum,
        min,
        max,
        avg,
        p50,
        p95,
        p99,
        labels: metricGroup[0].labels,
        timeRange
      });
    }

    return aggregated;
  }

  /**
   * 添加告警规则
   */
  addAlertRule(rule: AlertRule): void {
    this.alertRules.set(rule.id, rule);
    logger.info('告警规则已添加', {
      ruleId: rule.id,
      name: rule.name,
      metricName: rule.metricName,
      threshold: rule.threshold
    });
  }

  /**
   * 移除告警规则
   */
  removeAlertRule(ruleId: string): boolean {
    const removed = this.alertRules.delete(ruleId);
    if (removed) {
      // 清理相关的活跃告警
      this.activeAlerts.delete(ruleId);
      logger.info('告警规则已移除', { ruleId });
    }
    return removed;
  }

  /**
   * 获取所有告警规则
   */
  getAlertRules(): AlertRule[] {
    return Array.from(this.alertRules.values());
  }

  /**
   * 获取活跃告警
   */
  getActiveAlerts(): AlertEvent[] {
    return Array.from(this.activeAlerts.values());
  }

  /**
   * 检查告警规则
   */
  private checkAlertRules(metric: MetricData): void {
    for (const rule of this.alertRules.values()) {
      if (!rule.enabled || rule.metricName !== metric.name) {
        continue;
      }

      // 检查标签匹配
      if (rule.labels && !this.labelsMatch(metric.labels, rule.labels)) {
        continue;
      }

      // 检查条件
      const conditionMet = this.evaluateCondition(metric.value, rule.condition, rule.threshold);

      if (conditionMet) {
        this.handleAlertConditionMet(rule, metric);
      } else {
        this.handleAlertConditionNotMet(rule);
      }
    }
  }

  /**
   * 处理告警条件满足
   */
  private handleAlertConditionMet(rule: AlertRule, metric: MetricData): void {
    const alertKey = `${rule.id}_${JSON.stringify(metric.labels || {})}`;
    
    if (!this.activeAlerts.has(alertKey)) {
      const alertEvent: AlertEvent = {
        ruleId: rule.id,
        ruleName: rule.name,
        metricName: rule.metricName,
        currentValue: metric.value,
        threshold: rule.threshold,
        severity: rule.severity,
        timestamp: new Date(),
        labels: metric.labels,
        description: rule.description
      };

      this.activeAlerts.set(alertKey, alertEvent);
      this.emit('alertTriggered', alertEvent);

      logger.warn('告警触发', {
        ruleId: rule.id,
        ruleName: rule.name,
        metricName: rule.metricName,
        currentValue: metric.value,
        threshold: rule.threshold,
        severity: rule.severity
      });
    }
  }

  /**
   * 处理告警条件不满足
   */
  private handleAlertConditionNotMet(rule: AlertRule): void {
    const alertsToRemove: string[] = [];
    
    for (const [alertKey, alert] of this.activeAlerts) {
      if (alert.ruleId === rule.id) {
        alertsToRemove.push(alertKey);
        this.emit('alertResolved', alert);
        
        logger.info('告警已解决', {
          ruleId: rule.id,
          ruleName: rule.name,
          metricName: rule.metricName
        });
      }
    }

    alertsToRemove.forEach(key => this.activeAlerts.delete(key));
  }

  /**
   * 评估告警条件
   */
  private evaluateCondition(value: number, condition: string, threshold: number): boolean {
    switch (condition) {
      case 'gt': return value > threshold;
      case 'gte': return value >= threshold;
      case 'lt': return value < threshold;
      case 'lte': return value <= threshold;
      case 'eq': return value === threshold;
      default: return false;
    }
  }

  /**
   * 检查标签匹配
   */
  private labelsMatch(metricLabels?: Record<string, string>, ruleLabels?: Record<string, string>): boolean {
    if (!ruleLabels) return true;
    if (!metricLabels) return false;

    for (const [key, value] of Object.entries(ruleLabels)) {
      if (metricLabels[key] !== value) {
        return false;
      }
    }

    return true;
  }

  /**
   * 应用过滤条件
   */
  private applyFilters(metrics: MetricData[], options: MetricQueryOptions): MetricData[] {
    let filtered = metrics;

    // 按类型过滤
    if (options.type) {
      filtered = filtered.filter(m => m.type === options.type);
    }

    // 按时间范围过滤
    if (options.startTime) {
      filtered = filtered.filter(m => m.timestamp >= options.startTime!);
    }

    if (options.endTime) {
      filtered = filtered.filter(m => m.timestamp <= options.endTime!);
    }

    // 按标签过滤
    if (options.labels) {
      filtered = filtered.filter(m => this.labelsMatch(m.labels, options.labels));
    }

    // 按标签过滤
    if (options.tags) {
      filtered = filtered.filter(m => 
        options.tags!.every(tag => m.tags?.includes(tag))
      );
    }

    return filtered;
  }

  /**
   * 分组指标
   */
  private groupMetrics(metrics: MetricData[], groupBy: string[]): Map<string, MetricData[]> {
    const grouped = new Map<string, MetricData[]>();

    for (const metric of metrics) {
      const key = this.generateGroupKey(metric, groupBy);
      
      if (!grouped.has(key)) {
        grouped.set(key, []);
      }
      
      grouped.get(key)!.push(metric);
    }

    return grouped;
  }

  /**
   * 生成分组键
   */
  private generateGroupKey(metric: MetricData, groupBy: string[]): string {
    const keyParts: string[] = [];

    for (const field of groupBy) {
      if (field === 'name') {
        keyParts.push(metric.name);
      } else if (field === 'type') {
        keyParts.push(metric.type);
      } else if (metric.labels && metric.labels[field]) {
        keyParts.push(`${field}:${metric.labels[field]}`);
      }
    }

    return keyParts.join('|');
  }

  /**
   * 启动清理定时器
   */
  private startCleanupTimer(): void {
    this.cleanupInterval = setInterval(() => {
      this.cleanupOldMetrics();
    }, 60 * 60 * 1000); // 每小时清理一次

    logger.info('指标清理定时器已启动');
  }

  /**
   * 清理过期指标
   */
  private cleanupOldMetrics(): void {
    const cutoffTime = new Date(Date.now() - this.maxRetentionTime);
    let totalCleaned = 0;

    for (const [name, metricArray] of this.metrics) {
      const originalLength = metricArray.length;
      const filtered = metricArray.filter(m => m.timestamp > cutoffTime);
      
      if (filtered.length !== originalLength) {
        this.metrics.set(name, filtered);
        totalCleaned += originalLength - filtered.length;
      }
    }

    if (totalCleaned > 0) {
      logger.info('过期指标清理完成', {
        cleanedCount: totalCleaned,
        cutoffTime: cutoffTime.toISOString()
      });
    }
  }

  /**
   * 获取指标统计信息
   */
  getStats(): {
    totalMetrics: number;
    metricNames: number;
    alertRules: number;
    activeAlerts: number;
    oldestMetric?: Date;
    newestMetric?: Date;
  } {
    let totalMetrics = 0;
    let oldestMetric: Date | undefined;
    let newestMetric: Date | undefined;

    for (const metricArray of this.metrics.values()) {
      totalMetrics += metricArray.length;
      
      if (metricArray.length > 0) {
        const firstMetric = metricArray[0].timestamp;
        const lastMetric = metricArray[metricArray.length - 1].timestamp;
        
        if (!oldestMetric || firstMetric < oldestMetric) {
          oldestMetric = firstMetric;
        }
        
        if (!newestMetric || lastMetric > newestMetric) {
          newestMetric = lastMetric;
        }
      }
    }

    return {
      totalMetrics,
      metricNames: this.metrics.size,
      alertRules: this.alertRules.size,
      activeAlerts: this.activeAlerts.size,
      oldestMetric,
      newestMetric
    };
  }

  /**
   * 清理资源
   */
  cleanup(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }
    
    this.metrics.clear();
    this.alertRules.clear();
    this.activeAlerts.clear();
    this.removeAllListeners();
    
    logger.info('指标收集器清理完成');
  }
}

// 创建单例实例
export const metricsCollector = new MetricsCollectorService();
