/**
 * JWT黑名单管理服务
 * 管理被撤销的JWT令牌，防止已注销的令牌被重复使用
 */

import { redisService } from './redis.service';
import { logger } from '@/config/logger';
import { RedisKeys, RedisTTL } from '@/config/redis';
import { verifyToken } from '@/utils/jwt';

/**
 * 黑名单条目接口
 */
export interface BlacklistEntry {
  jti: string;                    // JWT ID
  userId: string;                 // 用户ID
  tokenType: 'access' | 'refresh'; // 令牌类型
  reason: string;                 // 加入黑名单的原因
  revokedAt: Date;               // 撤销时间
  expiresAt: Date;               // 原始过期时间
  metadata?: Record<string, any>; // 额外元数据
}

/**
 * 撤销原因枚举
 */
export enum RevocationReason {
  USER_LOGOUT = 'user_logout',
  ADMIN_REVOKE = 'admin_revoke',
  SECURITY_BREACH = 'security_breach',
  PASSWORD_CHANGE = 'password_change',
  ACCOUNT_SUSPENDED = 'account_suspended',
  TOKEN_REFRESH = 'token_refresh',
  SUSPICIOUS_ACTIVITY = 'suspicious_activity',
  DEVICE_LOST = 'device_lost',
  MANUAL_REVOKE = 'manual_revoke'
}

/**
 * 黑名单统计信息
 */
export interface BlacklistStats {
  totalEntries: number;
  entriesByReason: Record<string, number>;
  entriesByTokenType: Record<string, number>;
  recentRevocations: number; // 最近24小时的撤销数量
  topUsers: Array<{ userId: string; count: number }>; // 撤销最多的用户
}

/**
 * JWT黑名单管理服务
 */
export class JWTBlacklistService {
  private readonly cleanupInterval = 60 * 60 * 1000; // 1小时清理一次
  private cleanupTimer?: NodeJS.Timeout;

  constructor() {
    this.startCleanupTimer();
  }

  /**
   * 将JWT令牌加入黑名单
   */
  async addToBlacklist(
    token: string,
    reason: RevocationReason,
    metadata?: Record<string, any>
  ): Promise<boolean> {
    try {
      // 解析JWT令牌获取信息
      const payload = await verifyToken(token);
      const jti = payload.jti;
      const userId = payload.sub;
      const exp = payload.exp;
      
      if (!jti || !userId || !exp) {
        logger.error('JWT令牌信息不完整', { jti, userId, exp });
        return false;
      }

      // 计算TTL（令牌剩余有效时间）
      const now = Math.floor(Date.now() / 1000);
      const ttl = Math.max(0, exp - now);

      if (ttl <= 0) {
        logger.debug('JWT令牌已过期，无需加入黑名单', { jti });
        return true;
      }

      // 创建黑名单条目
      const entry: BlacklistEntry = {
        jti,
        userId,
        tokenType: this.determineTokenType(payload),
        reason,
        revokedAt: new Date(),
        expiresAt: new Date(exp * 1000),
        metadata
      };

      // 存储到Redis
      const blacklistKey = RedisKeys.JWT_BLACKLIST(jti);
      await redisService.set(blacklistKey, entry, ttl);

      // 添加到用户黑名单索引
      await this.addToUserBlacklistIndex(userId, jti, ttl);

      // 添加到原因索引
      await this.addToReasonIndex(reason, jti, ttl);

      // 记录统计信息
      await this.updateStats(reason, entry.tokenType);

      logger.info('JWT令牌已加入黑名单', {
        jti,
        userId,
        reason,
        tokenType: entry.tokenType,
        ttl
      });

      return true;

    } catch (error) {
      logger.error('JWT令牌加入黑名单失败', {
        error: error.message,
        reason
      });
      return false;
    }
  }

  /**
   * 检查JWT令牌是否在黑名单中
   */
  async isBlacklisted(token: string): Promise<boolean> {
    try {
      // 解析JWT令牌获取JTI
      const payload = await verifyToken(token);
      const jti = payload.jti;

      if (!jti) {
        logger.warn('JWT令牌缺少JTI', { token: token.substring(0, 20) + '...' });
        return false;
      }

      return await this.isJTIBlacklisted(jti);

    } catch (error) {
      logger.error('检查JWT黑名单失败', {
        error: error.message,
        token: token.substring(0, 20) + '...'
      });
      return false;
    }
  }

  /**
   * 通过JTI检查是否在黑名单中
   */
  async isJTIBlacklisted(jti: string): Promise<boolean> {
    try {
      const blacklistKey = RedisKeys.JWT_BLACKLIST(jti);
      return await redisService.exists(blacklistKey);
    } catch (error) {
      logger.error('检查JTI黑名单失败', { jti, error: error.message });
      return false;
    }
  }

  /**
   * 获取黑名单条目
   */
  async getBlacklistEntry(jti: string): Promise<BlacklistEntry | null> {
    try {
      const blacklistKey = RedisKeys.JWT_BLACKLIST(jti);
      return await redisService.get<BlacklistEntry>(blacklistKey);
    } catch (error) {
      logger.error('获取黑名单条目失败', { jti, error: error.message });
      return null;
    }
  }

  /**
   * 撤销用户的所有令牌
   */
  async revokeAllUserTokens(
    userId: string,
    reason: RevocationReason,
    excludeJti?: string
  ): Promise<number> {
    try {
      const userIndexKey = RedisKeys.USER_BLACKLIST_INDEX(userId);
      const jtis = await redisService.smembers(userIndexKey);

      let revokedCount = 0;
      for (const jti of jtis) {
        if (jti !== excludeJti) {
          // 这里需要获取原始令牌来撤销，实际实现中可能需要不同的策略
          // 暂时直接标记为已撤销
          const entry: BlacklistEntry = {
            jti,
            userId,
            tokenType: 'access', // 默认类型
            reason,
            revokedAt: new Date(),
            expiresAt: new Date(Date.now() + RedisTTL.JWT_BLACKLIST * 1000),
            metadata: { bulkRevocation: true }
          };

          const blacklistKey = RedisKeys.JWT_BLACKLIST(jti);
          await redisService.set(blacklistKey, entry, RedisTTL.JWT_BLACKLIST);
          revokedCount++;
        }
      }

      logger.info('用户令牌批量撤销完成', {
        userId,
        revokedCount,
        reason,
        excludeJti
      });

      return revokedCount;

    } catch (error) {
      logger.error('用户令牌批量撤销失败', {
        userId,
        reason,
        error: error.message
      });
      return 0;
    }
  }

  /**
   * 获取用户的黑名单令牌
   */
  async getUserBlacklistedTokens(userId: string): Promise<BlacklistEntry[]> {
    try {
      const userIndexKey = RedisKeys.USER_BLACKLIST_INDEX(userId);
      const jtis = await redisService.smembers(userIndexKey);

      const entries: BlacklistEntry[] = [];
      for (const jti of jtis) {
        const entry = await this.getBlacklistEntry(jti);
        if (entry) {
          entries.push(entry);
        }
      }

      return entries.sort((a, b) => 
        new Date(b.revokedAt).getTime() - new Date(a.revokedAt).getTime()
      );

    } catch (error) {
      logger.error('获取用户黑名单令牌失败', {
        userId,
        error: error.message
      });
      return [];
    }
  }

  /**
   * 按原因获取黑名单令牌
   */
  async getBlacklistedTokensByReason(reason: RevocationReason): Promise<BlacklistEntry[]> {
    try {
      const reasonIndexKey = RedisKeys.BLACKLIST_REASON_INDEX(reason);
      const jtis = await redisService.smembers(reasonIndexKey);

      const entries: BlacklistEntry[] = [];
      for (const jti of jtis) {
        const entry = await this.getBlacklistEntry(jti);
        if (entry) {
          entries.push(entry);
        }
      }

      return entries.sort((a, b) => 
        new Date(b.revokedAt).getTime() - new Date(a.revokedAt).getTime()
      );

    } catch (error) {
      logger.error('按原因获取黑名单令牌失败', {
        reason,
        error: error.message
      });
      return [];
    }
  }

  /**
   * 获取黑名单统计信息
   */
  async getBlacklistStats(): Promise<BlacklistStats> {
    try {
      const client = redisService.getClient();
      const pattern = RedisKeys.JWT_BLACKLIST('*');
      const blacklistKeys = await client.keys(pattern);

      const stats: BlacklistStats = {
        totalEntries: blacklistKeys.length,
        entriesByReason: {},
        entriesByTokenType: {},
        recentRevocations: 0,
        topUsers: []
      };

      const userCounts: Record<string, number> = {};
      const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);

      for (const key of blacklistKeys) {
        const entry = await redisService.get<BlacklistEntry>(key);
        if (entry) {
          // 按原因统计
          stats.entriesByReason[entry.reason] = (stats.entriesByReason[entry.reason] || 0) + 1;

          // 按令牌类型统计
          stats.entriesByTokenType[entry.tokenType] = (stats.entriesByTokenType[entry.tokenType] || 0) + 1;

          // 最近24小时撤销统计
          if (new Date(entry.revokedAt) > oneDayAgo) {
            stats.recentRevocations++;
          }

          // 用户撤销计数
          userCounts[entry.userId] = (userCounts[entry.userId] || 0) + 1;
        }
      }

      // 排序用户撤销计数
      stats.topUsers = Object.entries(userCounts)
        .map(([userId, count]) => ({ userId, count }))
        .sort((a, b) => b.count - a.count)
        .slice(0, 10);

      return stats;

    } catch (error) {
      logger.error('获取黑名单统计失败', { error: error.message });
      return {
        totalEntries: 0,
        entriesByReason: {},
        entriesByTokenType: {},
        recentRevocations: 0,
        topUsers: []
      };
    }
  }

  /**
   * 清理过期的黑名单条目
   */
  async cleanupExpiredEntries(): Promise<number> {
    try {
      const client = redisService.getClient();
      const pattern = RedisKeys.JWT_BLACKLIST('*');
      const blacklistKeys = await client.keys(pattern);

      let cleanedCount = 0;
      const now = new Date();

      for (const key of blacklistKeys) {
        const entry = await redisService.get<BlacklistEntry>(key);
        if (entry && now > new Date(entry.expiresAt)) {
          await redisService.del(key);
          
          // 从索引中移除
          await this.removeFromUserBlacklistIndex(entry.userId, entry.jti);
          await this.removeFromReasonIndex(entry.reason, entry.jti);
          
          cleanedCount++;
        }
      }

      logger.info('黑名单过期条目清理完成', { cleanedCount });
      return cleanedCount;

    } catch (error) {
      logger.error('黑名单过期条目清理失败', { error: error.message });
      return 0;
    }
  }

  /**
   * 确定令牌类型
   */
  private determineTokenType(payload: any): 'access' | 'refresh' {
    // 根据JWT payload中的信息判断令牌类型
    if (payload.type === 'refresh' || payload.token_type === 'refresh') {
      return 'refresh';
    }
    return 'access';
  }

  /**
   * 添加到用户黑名单索引
   */
  private async addToUserBlacklistIndex(userId: string, jti: string, ttl: number): Promise<void> {
    const userIndexKey = RedisKeys.USER_BLACKLIST_INDEX(userId);
    await redisService.sadd(userIndexKey, jti);
    await redisService.expire(userIndexKey, ttl);
  }

  /**
   * 从用户黑名单索引中移除
   */
  private async removeFromUserBlacklistIndex(userId: string, jti: string): Promise<void> {
    const userIndexKey = RedisKeys.USER_BLACKLIST_INDEX(userId);
    await redisService.srem(userIndexKey, jti);
  }

  /**
   * 添加到原因索引
   */
  private async addToReasonIndex(reason: RevocationReason, jti: string, ttl: number): Promise<void> {
    const reasonIndexKey = RedisKeys.BLACKLIST_REASON_INDEX(reason);
    await redisService.sadd(reasonIndexKey, jti);
    await redisService.expire(reasonIndexKey, ttl);
  }

  /**
   * 从原因索引中移除
   */
  private async removeFromReasonIndex(reason: RevocationReason, jti: string): Promise<void> {
    const reasonIndexKey = RedisKeys.BLACKLIST_REASON_INDEX(reason);
    await redisService.srem(reasonIndexKey, jti);
  }

  /**
   * 更新统计信息
   */
  private async updateStats(reason: RevocationReason, tokenType: string): Promise<void> {
    try {
      const statsKey = RedisKeys.BLACKLIST_STATS();
      const dailyStatsKey = RedisKeys.BLACKLIST_DAILY_STATS(new Date().toISOString().split('T')[0]);

      // 增加总计数
      await redisService.hincrby(statsKey, 'total', 1);
      await redisService.hincrby(statsKey, `reason:${reason}`, 1);
      await redisService.hincrby(statsKey, `type:${tokenType}`, 1);

      // 增加每日计数
      await redisService.hincrby(dailyStatsKey, 'total', 1);
      await redisService.hincrby(dailyStatsKey, `reason:${reason}`, 1);
      await redisService.hincrby(dailyStatsKey, `type:${tokenType}`, 1);
      await redisService.expire(dailyStatsKey, RedisTTL.STATS_DAILY);

    } catch (error) {
      logger.error('更新黑名单统计失败', { error: error.message });
    }
  }

  /**
   * 启动清理定时器
   */
  private startCleanupTimer(): void {
    this.cleanupTimer = setInterval(async () => {
      await this.cleanupExpiredEntries();
    }, this.cleanupInterval);

    logger.info('JWT黑名单清理定时器已启动', {
      interval: this.cleanupInterval / 1000 / 60 + '分钟'
    });
  }

  /**
   * 停止清理定时器
   */
  stopCleanupTimer(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
      this.cleanupTimer = undefined;
      logger.info('JWT黑名单清理定时器已停止');
    }
  }

  /**
   * 清理资源
   */
  async cleanup(): Promise<void> {
    this.stopCleanupTimer();
    logger.info('JWT黑名单服务清理完成');
  }
}

// 创建单例实例
export const jwtBlacklistService = new JWTBlacklistService();
