/**
 * 查询分析器服务
 * 分析数据库查询性能，检测N+1查询问题，提供优化建议
 */

import { prisma } from '@/config/database';
import { logger } from '@/config/logger';
import { EventEmitter } from 'events';

/**
 * 查询类型枚举
 */
export enum QueryType {
  SELECT = 'SELECT',
  INSERT = 'INSERT',
  UPDATE = 'UPDATE',
  DELETE = 'DELETE',
  TRANSACTION = 'TRANSACTION'
}

/**
 * 查询分析结果
 */
export interface QueryAnalysis {
  id: string;
  query: string;
  queryType: QueryType;
  duration: number;
  timestamp: Date;
  
  // 性能指标
  isSlowQuery: boolean;
  severity: 'low' | 'medium' | 'high' | 'critical';
  
  // 问题检测
  hasNPlusOneProblem: boolean;
  hasFullTableScan: boolean;
  hasMissingIndex: boolean;
  hasInefficiientJoin: boolean;
  
  // 优化建议
  suggestions: string[];
  optimizedQuery?: string;
  
  // 上下文信息
  stackTrace?: string;
  requestId?: string;
  userId?: string;
  
  // 统计信息
  affectedRows?: number;
  executionPlan?: any;
}

/**
 * N+1查询检测结果
 */
export interface NPlusOneDetection {
  pattern: string;
  occurrences: number;
  queries: string[];
  suggestions: string[];
  estimatedImpact: 'low' | 'medium' | 'high';
}

/**
 * 查询性能统计
 */
export interface QueryPerformanceStats {
  totalQueries: number;
  slowQueries: number;
  averageDuration: number;
  queryTypeDistribution: Record<QueryType, number>;
  topSlowQueries: QueryAnalysis[];
  nPlusOneProblems: NPlusOneDetection[];
  indexSuggestions: string[];
}

/**
 * 查询分析器服务
 */
export class QueryAnalyzerService extends EventEmitter {
  private queryHistory: QueryAnalysis[] = [];
  private readonly maxHistorySize = 1000;
  private readonly slowQueryThreshold = 100; // 100ms
  private readonly criticalQueryThreshold = 1000; // 1s
  
  // N+1查询检测
  private queryPatterns = new Map<string, number>();
  private readonly nPlusOneWindow = 5000; // 5秒窗口
  private readonly nPlusOneThreshold = 10; // 10次相似查询

  constructor() {
    super();
    this.initializeQueryMonitoring();
  }

  /**
   * 初始化查询监控
   */
  private initializeQueryMonitoring(): void {
    // 监听Prisma查询事件
    prisma.$on('query', (event) => {
      this.analyzeQuery(event);
    });

    // 定期清理历史记录
    setInterval(() => {
      this.cleanupHistory();
    }, 60000); // 每分钟清理一次

    logger.info('查询分析器已初始化');
  }

  /**
   * 分析单个查询
   */
  private async analyzeQuery(event: any): Promise<void> {
    try {
      const analysis: QueryAnalysis = {
        id: this.generateQueryId(),
        query: event.query,
        queryType: this.detectQueryType(event.query),
        duration: event.duration,
        timestamp: new Date(),
        isSlowQuery: event.duration > this.slowQueryThreshold,
        severity: this.calculateSeverity(event.duration),
        hasNPlusOneProblem: false,
        hasFullTableScan: false,
        hasMissingIndex: false,
        hasInefficiientJoin: false,
        suggestions: []
      };

      // 检测各种性能问题
      await this.detectPerformanceIssues(analysis);

      // 生成优化建议
      this.generateOptimizationSuggestions(analysis);

      // 检测N+1查询问题
      this.detectNPlusOneQuery(analysis);

      // 添加到历史记录
      this.addToHistory(analysis);

      // 发出事件
      this.emit('queryAnalyzed', analysis);

      // 如果是慢查询，发出告警
      if (analysis.isSlowQuery) {
        this.emit('slowQuery', analysis);
        logger.warn('慢查询检测', {
          query: analysis.query.substring(0, 200),
          duration: analysis.duration,
          severity: analysis.severity,
          suggestions: analysis.suggestions
        });
      }

    } catch (error) {
      logger.error('查询分析失败', {
        error: error.message,
        query: event.query?.substring(0, 100)
      });
    }
  }

  /**
   * 检测查询类型
   */
  private detectQueryType(query: string): QueryType {
    const normalizedQuery = query.trim().toUpperCase();
    
    if (normalizedQuery.startsWith('SELECT')) return QueryType.SELECT;
    if (normalizedQuery.startsWith('INSERT')) return QueryType.INSERT;
    if (normalizedQuery.startsWith('UPDATE')) return QueryType.UPDATE;
    if (normalizedQuery.startsWith('DELETE')) return QueryType.DELETE;
    if (normalizedQuery.startsWith('BEGIN') || normalizedQuery.startsWith('COMMIT')) {
      return QueryType.TRANSACTION;
    }
    
    return QueryType.SELECT; // 默认
  }

  /**
   * 计算查询严重程度
   */
  private calculateSeverity(duration: number): 'low' | 'medium' | 'high' | 'critical' {
    if (duration > this.criticalQueryThreshold) return 'critical';
    if (duration > 500) return 'high';
    if (duration > 200) return 'medium';
    return 'low';
  }

  /**
   * 检测性能问题
   */
  private async detectPerformanceIssues(analysis: QueryAnalysis): Promise<void> {
    const query = analysis.query.toLowerCase();

    // 检测全表扫描
    if (this.hasFullTableScanPattern(query)) {
      analysis.hasFullTableScan = true;
      analysis.suggestions.push('查询可能进行了全表扫描，考虑添加WHERE条件或索引');
    }

    // 检测缺失索引
    if (this.hasMissingIndexPattern(query, analysis.duration)) {
      analysis.hasMissingIndex = true;
      analysis.suggestions.push('查询性能较慢，可能缺少合适的索引');
    }

    // 检测低效JOIN
    if (this.hasInefficiientJoinPattern(query, analysis.duration)) {
      analysis.hasInefficiientJoin = true;
      analysis.suggestions.push('JOIN操作较慢，检查JOIN条件和相关索引');
    }
  }

  /**
   * 检测全表扫描模式
   */
  private hasFullTableScanPattern(query: string): boolean {
    // 简化的全表扫描检测
    return (
      query.includes('select') &&
      !query.includes('where') &&
      !query.includes('limit') &&
      query.length > 50
    );
  }

  /**
   * 检测缺失索引模式
   */
  private hasMissingIndexPattern(query: string, duration: number): boolean {
    return (
      duration > 200 &&
      (query.includes('where') || query.includes('order by') || query.includes('group by'))
    );
  }

  /**
   * 检测低效JOIN模式
   */
  private hasInefficiientJoinPattern(query: string, duration: number): boolean {
    return (
      duration > 300 &&
      (query.includes('join') || query.includes('inner join') || query.includes('left join'))
    );
  }

  /**
   * 生成优化建议
   */
  private generateOptimizationSuggestions(analysis: QueryAnalysis): void {
    const query = analysis.query.toLowerCase();

    // SELECT * 检测
    if (query.includes('select *')) {
      analysis.suggestions.push('避免使用SELECT *，只选择需要的列');
    }

    // 缺少LIMIT检测
    if (query.includes('select') && !query.includes('limit') && analysis.duration > 100) {
      analysis.suggestions.push('考虑添加LIMIT子句限制返回行数');
    }

    // 子查询优化
    if (query.includes('select') && query.match(/\([^)]*select/g)) {
      analysis.suggestions.push('考虑将子查询重写为JOIN操作');
    }

    // ORDER BY优化
    if (query.includes('order by') && analysis.duration > 200) {
      analysis.suggestions.push('为ORDER BY字段创建索引以提高排序性能');
    }

    // GROUP BY优化
    if (query.includes('group by') && analysis.duration > 200) {
      analysis.suggestions.push('为GROUP BY字段创建索引以提高分组性能');
    }
  }

  /**
   * 检测N+1查询问题
   */
  private detectNPlusOneQuery(analysis: QueryAnalysis): void {
    // 生成查询模式（去除具体参数）
    const pattern = this.normalizeQueryPattern(analysis.query);
    
    // 更新模式计数
    const currentCount = this.queryPatterns.get(pattern) || 0;
    this.queryPatterns.set(pattern, currentCount + 1);

    // 检查是否达到N+1阈值
    if (currentCount + 1 >= this.nPlusOneThreshold) {
      analysis.hasNPlusOneProblem = true;
      analysis.suggestions.push('检测到可能的N+1查询问题，考虑使用include或预加载');
      
      this.emit('nPlusOneDetected', {
        pattern,
        occurrences: currentCount + 1,
        query: analysis.query
      });
    }

    // 清理过期的模式计数
    setTimeout(() => {
      const count = this.queryPatterns.get(pattern) || 0;
      if (count > 1) {
        this.queryPatterns.set(pattern, count - 1);
      } else {
        this.queryPatterns.delete(pattern);
      }
    }, this.nPlusOneWindow);
  }

  /**
   * 规范化查询模式
   */
  private normalizeQueryPattern(query: string): string {
    return query
      .replace(/\$\d+/g, '?')           // 替换参数占位符
      .replace(/\d+/g, 'N')            // 替换数字
      .replace(/'[^']*'/g, "'?'")      // 替换字符串字面量
      .replace(/\s+/g, ' ')            // 规范化空白字符
      .trim()
      .toLowerCase();
  }

  /**
   * 生成查询ID
   */
  private generateQueryId(): string {
    return `query_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`;
  }

  /**
   * 添加到历史记录
   */
  private addToHistory(analysis: QueryAnalysis): void {
    this.queryHistory.push(analysis);
    
    // 保持历史记录大小
    if (this.queryHistory.length > this.maxHistorySize) {
      this.queryHistory.shift();
    }
  }

  /**
   * 清理历史记录
   */
  private cleanupHistory(): void {
    const cutoffTime = new Date(Date.now() - 24 * 60 * 60 * 1000); // 24小时前
    this.queryHistory = this.queryHistory.filter(
      analysis => analysis.timestamp > cutoffTime
    );
  }

  /**
   * 获取查询性能统计
   */
  getPerformanceStats(): QueryPerformanceStats {
    const totalQueries = this.queryHistory.length;
    const slowQueries = this.queryHistory.filter(q => q.isSlowQuery).length;
    const averageDuration = totalQueries > 0 
      ? this.queryHistory.reduce((sum, q) => sum + q.duration, 0) / totalQueries 
      : 0;

    // 查询类型分布
    const queryTypeDistribution = this.queryHistory.reduce((dist, query) => {
      dist[query.queryType] = (dist[query.queryType] || 0) + 1;
      return dist;
    }, {} as Record<QueryType, number>);

    // 最慢的查询
    const topSlowQueries = [...this.queryHistory]
      .filter(q => q.isSlowQuery)
      .sort((a, b) => b.duration - a.duration)
      .slice(0, 10);

    // N+1问题检测
    const nPlusOneProblems = this.detectNPlusOneProblems();

    // 索引建议
    const indexSuggestions = this.generateIndexSuggestions();

    return {
      totalQueries,
      slowQueries,
      averageDuration,
      queryTypeDistribution,
      topSlowQueries,
      nPlusOneProblems,
      indexSuggestions
    };
  }

  /**
   * 检测N+1问题
   */
  private detectNPlusOneProblems(): NPlusOneDetection[] {
    const problems: NPlusOneDetection[] = [];
    const patternGroups = new Map<string, QueryAnalysis[]>();

    // 按模式分组查询
    this.queryHistory.forEach(query => {
      const pattern = this.normalizeQueryPattern(query.query);
      if (!patternGroups.has(pattern)) {
        patternGroups.set(pattern, []);
      }
      patternGroups.get(pattern)!.push(query);
    });

    // 检测重复模式
    patternGroups.forEach((queries, pattern) => {
      if (queries.length >= 5) { // 5次以上重复
        problems.push({
          pattern,
          occurrences: queries.length,
          queries: queries.slice(0, 3).map(q => q.query), // 只显示前3个
          suggestions: [
            '使用Prisma的include选项预加载关联数据',
            '考虑使用findMany替代多次findUnique调用',
            '实施数据加载器(DataLoader)模式'
          ],
          estimatedImpact: queries.length > 20 ? 'high' : queries.length > 10 ? 'medium' : 'low'
        });
      }
    });

    return problems;
  }

  /**
   * 生成索引建议
   */
  private generateIndexSuggestions(): string[] {
    const suggestions: string[] = [];
    const slowQueries = this.queryHistory.filter(q => q.isSlowQuery);

    // 分析慢查询中的WHERE条件
    slowQueries.forEach(query => {
      const whereMatch = query.query.match(/WHERE\s+(\w+)\s*=/i);
      if (whereMatch) {
        suggestions.push(`考虑为字段 ${whereMatch[1]} 创建索引`);
      }

      const orderByMatch = query.query.match(/ORDER BY\s+(\w+)/i);
      if (orderByMatch) {
        suggestions.push(`考虑为排序字段 ${orderByMatch[1]} 创建索引`);
      }
    });

    // 去重
    return [...new Set(suggestions)];
  }

  /**
   * 获取查询历史
   */
  getQueryHistory(limit?: number): QueryAnalysis[] {
    const history = [...this.queryHistory].reverse(); // 最新的在前
    return limit ? history.slice(0, limit) : history;
  }

  /**
   * 获取慢查询
   */
  getSlowQueries(limit = 50): QueryAnalysis[] {
    return this.queryHistory
      .filter(q => q.isSlowQuery)
      .sort((a, b) => b.duration - a.duration)
      .slice(0, limit);
  }

  /**
   * 重置统计数据
   */
  resetStats(): void {
    this.queryHistory = [];
    this.queryPatterns.clear();
    logger.info('查询分析统计数据已重置');
  }

  /**
   * 清理资源
   */
  cleanup(): void {
    this.removeAllListeners();
    this.queryHistory = [];
    this.queryPatterns.clear();
    logger.info('查询分析器清理完成');
  }
}

// 创建单例实例
export const queryAnalyzer = new QueryAnalyzerService();
