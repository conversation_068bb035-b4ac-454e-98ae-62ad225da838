/**
 * 增强缓存服务
 * 使用新的Redis管理器提供高级缓存功能
 */

import { redisManager, RedisKeys, RedisTTL } from '@/config/redis';
import { logger } from '@/config/logger';

export interface CacheOptions {
  ttl?: number;
  prefix?: string;
  serialize?: boolean;
}

export interface CacheStats {
  hits: number;
  misses: number;
  sets: number;
  deletes: number;
  errors: number;
}

export class EnhancedCacheService {
  private stats: CacheStats = {
    hits: 0,
    misses: 0,
    sets: 0,
    deletes: 0,
    errors: 0
  };

  /**
   * 设置缓存值
   */
  async set(key: string, value: any, ttl?: number, options?: CacheOptions): Promise<void> {
    try {
      const redis = redisManager.getClient();
      const finalKey = options?.prefix ? `${options.prefix}:${key}` : key;
      const finalTtl = ttl || options?.ttl || RedisTTL.DEFAULT;
      
      let serializedValue: string;
      if (options?.serialize !== false) {
        serializedValue = JSON.stringify(value);
      } else {
        serializedValue = String(value);
      }

      if (finalTtl > 0) {
        await redis.setex(finalKey, finalTtl, serializedValue);
      } else {
        await redis.set(finalKey, serializedValue);
      }

      this.stats.sets++;
      logger.debug('缓存设置成功', { key: finalKey, ttl: finalTtl });

    } catch (error) {
      this.stats.errors++;
      logger.error('设置缓存失败', { error, key });
      throw error;
    }
  }

  /**
   * 获取缓存值
   */
  async get<T = any>(key: string, options?: CacheOptions): Promise<T | null> {
    try {
      const redis = redisManager.getClient();
      const finalKey = options?.prefix ? `${options.prefix}:${key}` : key;
      
      const value = await redis.get(finalKey);
      
      if (value === null) {
        this.stats.misses++;
        return null;
      }

      this.stats.hits++;
      
      if (options?.serialize !== false) {
        return JSON.parse(value) as T;
      } else {
        return value as T;
      }

    } catch (error) {
      this.stats.errors++;
      logger.error('获取缓存失败', { error, key });
      return null;
    }
  }

  /**
   * 删除缓存
   */
  async del(key: string | string[], options?: CacheOptions): Promise<number> {
    try {
      const redis = redisManager.getClient();
      
      let keys: string[];
      if (Array.isArray(key)) {
        keys = key.map(k => options?.prefix ? `${options.prefix}:${k}` : k);
      } else {
        keys = [options?.prefix ? `${options.prefix}:${key}` : key];
      }

      const result = await redis.del(...keys);
      this.stats.deletes += result;
      
      logger.debug('缓存删除成功', { keys, count: result });
      return result;

    } catch (error) {
      this.stats.errors++;
      logger.error('删除缓存失败', { error, key });
      throw error;
    }
  }

  /**
   * 检查缓存是否存在
   */
  async exists(key: string, options?: CacheOptions): Promise<boolean> {
    try {
      const redis = redisManager.getClient();
      const finalKey = options?.prefix ? `${options.prefix}:${key}` : key;
      
      const result = await redis.exists(finalKey);
      return result === 1;

    } catch (error) {
      this.stats.errors++;
      logger.error('检查缓存存在性失败', { error, key });
      return false;
    }
  }

  /**
   * 设置缓存过期时间
   */
  async expire(key: string, ttl: number, options?: CacheOptions): Promise<boolean> {
    try {
      const redis = redisManager.getClient();
      const finalKey = options?.prefix ? `${options.prefix}:${key}` : key;
      
      const result = await redis.expire(finalKey, ttl);
      return result === 1;

    } catch (error) {
      this.stats.errors++;
      logger.error('设置缓存过期时间失败', { error, key, ttl });
      return false;
    }
  }

  /**
   * 获取缓存剩余过期时间
   */
  async ttl(key: string, options?: CacheOptions): Promise<number> {
    try {
      const redis = redisManager.getClient();
      const finalKey = options?.prefix ? `${options.prefix}:${key}` : key;
      
      return await redis.ttl(finalKey);

    } catch (error) {
      this.stats.errors++;
      logger.error('获取缓存TTL失败', { error, key });
      return -1;
    }
  }

  /**
   * 批量获取缓存
   */
  async mget<T = any>(keys: string[], options?: CacheOptions): Promise<(T | null)[]> {
    try {
      const redis = redisManager.getClient();
      const finalKeys = keys.map(key => 
        options?.prefix ? `${options.prefix}:${key}` : key
      );
      
      const values = await redis.mget(...finalKeys);
      
      return values.map(value => {
        if (value === null) {
          this.stats.misses++;
          return null;
        }
        
        this.stats.hits++;
        
        if (options?.serialize !== false) {
          return JSON.parse(value) as T;
        } else {
          return value as T;
        }
      });

    } catch (error) {
      this.stats.errors++;
      logger.error('批量获取缓存失败', { error, keys });
      return keys.map(() => null);
    }
  }

  /**
   * 批量设置缓存
   */
  async mset(keyValues: Record<string, any>, ttl?: number, options?: CacheOptions): Promise<void> {
    try {
      const redis = redisManager.getClient();
      const pipeline = redis.pipeline();
      
      Object.entries(keyValues).forEach(([key, value]) => {
        const finalKey = options?.prefix ? `${options.prefix}:${key}` : key;
        let serializedValue: string;
        
        if (options?.serialize !== false) {
          serializedValue = JSON.stringify(value);
        } else {
          serializedValue = String(value);
        }

        if (ttl && ttl > 0) {
          pipeline.setex(finalKey, ttl, serializedValue);
        } else {
          pipeline.set(finalKey, serializedValue);
        }
      });

      await pipeline.exec();
      this.stats.sets += Object.keys(keyValues).length;
      
      logger.debug('批量设置缓存成功', { count: Object.keys(keyValues).length });

    } catch (error) {
      this.stats.errors++;
      logger.error('批量设置缓存失败', { error, keyValues });
      throw error;
    }
  }

  /**
   * 模糊匹配删除缓存
   */
  async delPattern(pattern: string, options?: CacheOptions): Promise<number> {
    try {
      const redis = redisManager.getClient();
      const finalPattern = options?.prefix ? `${options.prefix}:${pattern}` : pattern;
      
      const keys = await redis.keys(finalPattern);
      
      if (keys.length === 0) {
        return 0;
      }

      const result = await redis.del(...keys);
      this.stats.deletes += result;
      
      logger.debug('模糊删除缓存成功', { pattern: finalPattern, count: result });
      return result;

    } catch (error) {
      this.stats.errors++;
      logger.error('模糊删除缓存失败', { error, pattern });
      throw error;
    }
  }

  /**
   * 原子递增
   */
  async incr(key: string, options?: CacheOptions): Promise<number> {
    try {
      const redis = redisManager.getClient();
      const finalKey = options?.prefix ? `${options.prefix}:${key}` : key;
      
      return await redis.incr(finalKey);

    } catch (error) {
      this.stats.errors++;
      logger.error('缓存递增失败', { error, key });
      throw error;
    }
  }

  /**
   * 原子递减
   */
  async decr(key: string, options?: CacheOptions): Promise<number> {
    try {
      const redis = redisManager.getClient();
      const finalKey = options?.prefix ? `${options.prefix}:${key}` : key;
      
      return await redis.decr(finalKey);

    } catch (error) {
      this.stats.errors++;
      logger.error('缓存递减失败', { error, key });
      throw error;
    }
  }

  /**
   * 获取缓存统计信息
   */
  getStats(): CacheStats {
    return { ...this.stats };
  }

  /**
   * 重置统计信息
   */
  resetStats(): void {
    this.stats = {
      hits: 0,
      misses: 0,
      sets: 0,
      deletes: 0,
      errors: 0
    };
  }

  /**
   * 获取缓存命中率
   */
  getHitRate(): number {
    const total = this.stats.hits + this.stats.misses;
    return total > 0 ? this.stats.hits / total : 0;
  }

  /**
   * 清空所有缓存（谨慎使用）
   */
  async flushAll(): Promise<void> {
    try {
      const redis = redisManager.getClient();
      await redis.flushdb();
      
      logger.warn('所有缓存已清空');

    } catch (error) {
      this.stats.errors++;
      logger.error('清空缓存失败', { error });
      throw error;
    }
  }

  /**
   * 获取Redis信息
   */
  async getRedisInfo(): Promise<any> {
    try {
      const redis = redisManager.getClient();
      const info = await redis.info();
      
      return this.parseRedisInfo(info);

    } catch (error) {
      logger.error('获取Redis信息失败', { error });
      return null;
    }
  }

  /**
   * 解析Redis信息字符串
   */
  private parseRedisInfo(infoString: string): Record<string, string> {
    const result: Record<string, string> = {};
    
    infoString.split('\r\n').forEach(line => {
      if (line && !line.startsWith('#')) {
        const [key, value] = line.split(':');
        if (key && value) {
          result[key] = value;
        }
      }
    });

    return result;
  }
}

// 导出单例实例
export const enhancedCacheService = new EnhancedCacheService();
