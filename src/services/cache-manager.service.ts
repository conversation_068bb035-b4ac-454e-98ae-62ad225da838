/**
 * 高级缓存管理器
 * 提供缓存策略、分布式锁、缓存预热等高级功能
 */

import { redisService } from './redis.service';
import { logger } from '@/config/logger';
import { RedisKeys, RedisTTL } from '@/config/redis';
import { EventEmitter } from 'events';

/**
 * 缓存策略枚举
 */
export enum CacheStrategy {
  WRITE_THROUGH = 'write_through',     // 写穿透
  WRITE_BEHIND = 'write_behind',       // 写回
  WRITE_AROUND = 'write_around',       // 写绕过
  READ_THROUGH = 'read_through',       // 读穿透
  CACHE_ASIDE = 'cache_aside'          // 缓存旁路
}

/**
 * 缓存配置接口
 */
export interface CacheConfig {
  strategy: CacheStrategy;
  ttl: number;
  maxSize?: number;
  compressionEnabled?: boolean;
  serializationFormat?: 'json' | 'msgpack' | 'protobuf';
  tags?: string[];
  namespace?: string;
}

/**
 * 缓存统计信息
 */
export interface CacheStats {
  hits: number;
  misses: number;
  hitRate: number;
  totalRequests: number;
  totalSize: number;
  keyCount: number;
  avgResponseTime: number;
}

/**
 * 分布式锁配置
 */
export interface LockConfig {
  ttl: number;
  retryDelay: number;
  maxRetries: number;
  autoRelease: boolean;
}

/**
 * 缓存事件类型
 */
export enum CacheEvent {
  HIT = 'cache:hit',
  MISS = 'cache:miss',
  SET = 'cache:set',
  DELETE = 'cache:delete',
  EXPIRE = 'cache:expire',
  EVICT = 'cache:evict'
}

/**
 * 高级缓存管理器
 */
export class CacheManager extends EventEmitter {
  private stats: Map<string, CacheStats> = new Map();
  private locks: Map<string, NodeJS.Timeout> = new Map();
  private compressionEnabled = false;

  constructor() {
    super();
    this.initializeStats();
  }

  /**
   * 初始化统计信息
   */
  private initializeStats(): void {
    setInterval(() => {
      this.updateStats();
    }, 60000); // 每分钟更新一次统计
  }

  /**
   * 获取缓存（带统计）
   */
  async get<T>(key: string, config?: Partial<CacheConfig>): Promise<T | null> {
    const startTime = Date.now();
    const namespace = config?.namespace || 'default';
    
    try {
      const value = await redisService.get<T>(key);
      const responseTime = Date.now() - startTime;
      
      if (value !== null) {
        this.recordHit(namespace, responseTime);
        this.emit(CacheEvent.HIT, { key, namespace, responseTime });
        return value;
      } else {
        this.recordMiss(namespace, responseTime);
        this.emit(CacheEvent.MISS, { key, namespace, responseTime });
        return null;
      }
    } catch (error) {
      this.recordMiss(namespace, Date.now() - startTime);
      logger.error('缓存获取失败', { key, error: error.message });
      return null;
    }
  }

  /**
   * 设置缓存（带策略）
   */
  async set<T>(
    key: string, 
    value: T, 
    config?: Partial<CacheConfig>
  ): Promise<boolean> {
    const startTime = Date.now();
    const namespace = config?.namespace || 'default';
    const ttl = config?.ttl || RedisTTL.USER_PROFILE;
    
    try {
      // 根据策略处理缓存
      switch (config?.strategy) {
        case CacheStrategy.WRITE_THROUGH:
          await this.writeThrough(key, value, ttl);
          break;
        
        case CacheStrategy.WRITE_BEHIND:
          await this.writeBehind(key, value, ttl);
          break;
        
        case CacheStrategy.WRITE_AROUND:
          await this.writeAround(key, value, ttl);
          break;
        
        default:
          await redisService.set(key, value, ttl);
      }

      const responseTime = Date.now() - startTime;
      this.emit(CacheEvent.SET, { key, namespace, responseTime, size: this.getValueSize(value) });
      
      return true;
    } catch (error) {
      logger.error('缓存设置失败', { key, error: error.message });
      return false;
    }
  }

  /**
   * 写穿透策略
   */
  private async writeThrough<T>(key: string, value: T, ttl: number): Promise<void> {
    // 同时写入缓存和数据源
    await Promise.all([
      redisService.set(key, value, ttl),
      this.writeToDataSource(key, value)
    ]);
  }

  /**
   * 写回策略
   */
  private async writeBehind<T>(key: string, value: T, ttl: number): Promise<void> {
    // 先写入缓存，异步写入数据源
    await redisService.set(key, value, ttl);
    
    // 异步写入数据源
    setImmediate(async () => {
      try {
        await this.writeToDataSource(key, value);
      } catch (error) {
        logger.error('写回数据源失败', { key, error: error.message });
      }
    });
  }

  /**
   * 写绕过策略
   */
  private async writeAround<T>(key: string, value: T, ttl: number): Promise<void> {
    // 只写入数据源，不写入缓存
    await this.writeToDataSource(key, value);
  }

  /**
   * 写入数据源（模拟）
   */
  private async writeToDataSource<T>(key: string, value: T): Promise<void> {
    // 这里应该实现实际的数据源写入逻辑
    logger.debug('写入数据源', { key });
  }

  /**
   * 批量获取缓存
   */
  async mget<T>(keys: string[]): Promise<(T | null)[]> {
    try {
      const client = redisService.getClient();
      const values = await client.mget(...keys);
      
      return values.map((value, index) => {
        if (value === null) {
          this.recordMiss('default', 0);
          return null;
        }
        
        try {
          this.recordHit('default', 0);
          return JSON.parse(value);
        } catch (error) {
          logger.error('批量缓存解析失败', { key: keys[index], error: error.message });
          return null;
        }
      });
    } catch (error) {
      logger.error('批量缓存获取失败', { keys, error: error.message });
      return keys.map(() => null);
    }
  }

  /**
   * 批量设置缓存
   */
  async mset<T>(keyValuePairs: Array<{ key: string; value: T; ttl?: number }>): Promise<boolean> {
    try {
      const client = redisService.getClient();
      const pipeline = client.pipeline();
      
      for (const { key, value, ttl } of keyValuePairs) {
        const serializedValue = JSON.stringify(value);
        if (ttl) {
          pipeline.setex(key, ttl, serializedValue);
        } else {
          pipeline.set(key, serializedValue);
        }
      }
      
      await pipeline.exec();
      return true;
    } catch (error) {
      logger.error('批量缓存设置失败', { error: error.message });
      return false;
    }
  }

  /**
   * 获取分布式锁
   */
  async acquireLock(
    lockKey: string, 
    config: Partial<LockConfig> = {}
  ): Promise<string | null> {
    const lockConfig: LockConfig = {
      ttl: config.ttl || 30,
      retryDelay: config.retryDelay || 100,
      maxRetries: config.maxRetries || 10,
      autoRelease: config.autoRelease !== false
    };

    const lockValue = `${Date.now()}-${Math.random()}`;
    const key = RedisKeys.LOCK(lockKey);

    for (let attempt = 0; attempt < lockConfig.maxRetries; attempt++) {
      try {
        const client = redisService.getClient();
        const result = await client.set(key, lockValue, 'EX', lockConfig.ttl, 'NX');
        
        if (result === 'OK') {
          // 设置自动释放
          if (lockConfig.autoRelease) {
            const timeout = setTimeout(() => {
              this.releaseLock(lockKey, lockValue);
            }, lockConfig.ttl * 1000 - 1000); // 提前1秒释放
            
            this.locks.set(lockKey, timeout);
          }
          
          logger.debug('获取分布式锁成功', { lockKey, lockValue });
          return lockValue;
        }
        
        // 等待后重试
        if (attempt < lockConfig.maxRetries - 1) {
          await new Promise(resolve => setTimeout(resolve, lockConfig.retryDelay));
        }
      } catch (error) {
        logger.error('获取分布式锁失败', { lockKey, attempt, error: error.message });
      }
    }

    logger.warn('获取分布式锁超时', { lockKey, maxRetries: lockConfig.maxRetries });
    return null;
  }

  /**
   * 释放分布式锁
   */
  async releaseLock(lockKey: string, lockValue: string): Promise<boolean> {
    try {
      const client = redisService.getClient();
      const key = RedisKeys.LOCK(lockKey);
      
      // 使用Lua脚本确保原子性
      const luaScript = `
        if redis.call("get", KEYS[1]) == ARGV[1] then
          return redis.call("del", KEYS[1])
        else
          return 0
        end
      `;
      
      const result = await client.eval(luaScript, 1, key, lockValue);
      
      // 清理自动释放定时器
      const timeout = this.locks.get(lockKey);
      if (timeout) {
        clearTimeout(timeout);
        this.locks.delete(lockKey);
      }
      
      const released = result === 1;
      logger.debug('释放分布式锁', { lockKey, lockValue, released });
      
      return released;
    } catch (error) {
      logger.error('释放分布式锁失败', { lockKey, lockValue, error: error.message });
      return false;
    }
  }

  /**
   * 缓存预热
   */
  async warmup(keys: string[], loader: (key: string) => Promise<any>): Promise<void> {
    logger.info('开始缓存预热', { keyCount: keys.length });
    
    const batchSize = 10;
    const batches = [];
    
    for (let i = 0; i < keys.length; i += batchSize) {
      batches.push(keys.slice(i, i + batchSize));
    }
    
    for (const batch of batches) {
      const promises = batch.map(async (key) => {
        try {
          const exists = await redisService.exists(key);
          if (!exists) {
            const value = await loader(key);
            if (value !== null && value !== undefined) {
              await redisService.set(key, value, RedisTTL.USER_PROFILE);
              logger.debug('缓存预热成功', { key });
            }
          }
        } catch (error) {
          logger.error('缓存预热失败', { key, error: error.message });
        }
      });
      
      await Promise.all(promises);
    }
    
    logger.info('缓存预热完成', { keyCount: keys.length });
  }

  /**
   * 按标签删除缓存
   */
  async deleteByTag(tag: string): Promise<number> {
    try {
      const client = redisService.getClient();
      const pattern = `*:tag:${tag}:*`;
      const keys = await client.keys(pattern);
      
      if (keys.length > 0) {
        const deletedCount = await client.del(...keys);
        logger.info('按标签删除缓存', { tag, deletedCount });
        return deletedCount;
      }
      
      return 0;
    } catch (error) {
      logger.error('按标签删除缓存失败', { tag, error: error.message });
      return 0;
    }
  }

  /**
   * 获取缓存统计信息
   */
  getStats(namespace = 'default'): CacheStats | null {
    return this.stats.get(namespace) || null;
  }

  /**
   * 获取所有命名空间的统计信息
   */
  getAllStats(): Map<string, CacheStats> {
    return new Map(this.stats);
  }

  /**
   * 重置统计信息
   */
  resetStats(namespace?: string): void {
    if (namespace) {
      this.stats.delete(namespace);
    } else {
      this.stats.clear();
    }
  }

  /**
   * 记录缓存命中
   */
  private recordHit(namespace: string, responseTime: number): void {
    const stats = this.getOrCreateStats(namespace);
    stats.hits++;
    stats.totalRequests++;
    stats.hitRate = stats.hits / stats.totalRequests;
    stats.avgResponseTime = (stats.avgResponseTime + responseTime) / 2;
  }

  /**
   * 记录缓存未命中
   */
  private recordMiss(namespace: string, responseTime: number): void {
    const stats = this.getOrCreateStats(namespace);
    stats.misses++;
    stats.totalRequests++;
    stats.hitRate = stats.hits / stats.totalRequests;
    stats.avgResponseTime = (stats.avgResponseTime + responseTime) / 2;
  }

  /**
   * 获取或创建统计信息
   */
  private getOrCreateStats(namespace: string): CacheStats {
    let stats = this.stats.get(namespace);
    if (!stats) {
      stats = {
        hits: 0,
        misses: 0,
        hitRate: 0,
        totalRequests: 0,
        totalSize: 0,
        keyCount: 0,
        avgResponseTime: 0
      };
      this.stats.set(namespace, stats);
    }
    return stats;
  }

  /**
   * 更新统计信息
   */
  private async updateStats(): Promise<void> {
    try {
      const client = redisService.getClient();
      const info = await client.info('memory');
      
      // 解析Redis内存信息
      const memoryLines = info.split('\r\n');
      for (const line of memoryLines) {
        if (line.startsWith('used_memory:')) {
          const usedMemory = parseInt(line.split(':')[1]);
          // 更新总大小统计
          for (const [namespace, stats] of this.stats) {
            stats.totalSize = usedMemory; // 简化处理
          }
          break;
        }
      }
    } catch (error) {
      logger.error('更新缓存统计失败', { error: error.message });
    }
  }

  /**
   * 获取值的大小（字节）
   */
  private getValueSize(value: any): number {
    try {
      return Buffer.byteLength(JSON.stringify(value), 'utf8');
    } catch {
      return 0;
    }
  }

  /**
   * 清理所有锁
   */
  async cleanup(): Promise<void> {
    // 清理所有定时器
    for (const [lockKey, timeout] of this.locks) {
      clearTimeout(timeout);
    }
    this.locks.clear();
    
    logger.info('缓存管理器清理完成');
  }
}

// 创建单例实例
export const cacheManager = new CacheManager();
