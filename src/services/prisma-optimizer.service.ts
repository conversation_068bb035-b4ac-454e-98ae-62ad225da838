/**
 * Prisma查询优化器服务
 * 提供Prisma特定的查询优化和N+1问题解决方案
 */

import { prisma } from '@/config/database';
import { logger } from '@/config/logger';

/**
 * 查询优化选项
 */
export interface QueryOptimizationOptions {
  enableInclude?: boolean;         // 启用预加载
  enableSelect?: boolean;          // 启用字段选择
  enablePagination?: boolean;      // 启用分页
  enableCaching?: boolean;         // 启用缓存
  maxDepth?: number;              // 最大关联深度
  batchSize?: number;             // 批处理大小
}

/**
 * 批量查询结果
 */
export interface BatchQueryResult<T> {
  data: T[];
  totalCount: number;
  hasMore: boolean;
  nextCursor?: string;
  executionTime: number;
}

/**
 * 预加载配置
 */
export interface IncludeConfig {
  [key: string]: boolean | IncludeConfig;
}

/**
 * Prisma查询优化器服务
 */
export class PrismaOptimizerService {
  
  /**
   * 优化用户查询 - 解决N+1问题
   */
  async findUsersWithRoles(
    where?: any,
    options: QueryOptimizationOptions = {}
  ): Promise<any[]> {
    const startTime = Date.now();
    
    try {
      // 使用include预加载关联数据，避免N+1查询
      const users = await prisma.user.findMany({
        where,
        include: {
          userRoles: {
            include: {
              role: {
                select: {
                  id: true,
                  name: true,
                  description: true
                }
              }
            }
          },
          sessions: options.enableInclude ? {
            where: {
              isActive: true
            },
            select: {
              id: true,
              createdAt: true,
              lastAccessedAt: true,
              ipAddress: true
            }
          } : false,
          mfaDevices: options.enableInclude ? {
            where: {
              isActive: true
            },
            select: {
              id: true,
              type: true,
              name: true,
              isVerified: true
            }
          } : false
        },
        take: options.batchSize || 50
      });

      const executionTime = Date.now() - startTime;
      
      logger.debug('优化用户查询完成', {
        userCount: users.length,
        executionTime,
        includeEnabled: options.enableInclude
      });

      return users;

    } catch (error) {
      logger.error('优化用户查询失败', { error: error.message });
      throw error;
    }
  }

  /**
   * 批量获取用户会话 - 优化大量数据查询
   */
  async findUserSessionsBatch(
    userIds: string[],
    options: QueryOptimizationOptions = {}
  ): Promise<BatchQueryResult<any>> {
    const startTime = Date.now();
    
    try {
      // 使用IN查询批量获取，避免多次单独查询
      const sessions = await prisma.session.findMany({
        where: {
          userId: {
            in: userIds
          },
          isActive: true
        },
        include: {
          user: {
            select: {
              id: true,
              email: true,
              nickname: true
            }
          }
        },
        orderBy: {
          lastAccessedAt: 'desc'
        },
        take: options.batchSize || 100
      });

      // 获取总数（如果需要分页）
      const totalCount = options.enablePagination 
        ? await prisma.session.count({
            where: {
              userId: { in: userIds },
              isActive: true
            }
          })
        : sessions.length;

      const executionTime = Date.now() - startTime;
      
      logger.debug('批量会话查询完成', {
        userIds: userIds.length,
        sessionCount: sessions.length,
        totalCount,
        executionTime
      });

      return {
        data: sessions,
        totalCount,
        hasMore: sessions.length === (options.batchSize || 100),
        executionTime
      };

    } catch (error) {
      logger.error('批量会话查询失败', { error: error.message });
      throw error;
    }
  }

  /**
   * 优化应用查询 - 包含客户端和授权码
   */
  async findApplicationsWithClients(
    where?: any,
    options: QueryOptimizationOptions = {}
  ): Promise<any[]> {
    const startTime = Date.now();
    
    try {
      const applications = await prisma.application.findMany({
        where,
        include: {
          clients: {
            select: {
              id: true,
              clientId: true,
              name: true,
              type: true,
              isActive: true,
              createdAt: true
            }
          },
          // 只在需要时包含授权码统计
          ...(options.enableInclude && {
            authorizationCodes: {
              where: {
                used: false,
                expiresAt: {
                  gt: new Date()
                }
              },
              select: {
                id: true,
                createdAt: true,
                expiresAt: true
              },
              take: 10 // 限制数量
            }
          })
        },
        take: options.batchSize || 20
      });

      const executionTime = Date.now() - startTime;
      
      logger.debug('应用查询完成', {
        applicationCount: applications.length,
        executionTime
      });

      return applications;

    } catch (error) {
      logger.error('应用查询失败', { error: error.message });
      throw error;
    }
  }

  /**
   * 优化审计日志查询 - 分页和过滤
   */
  async findAuditLogsPaginated(
    filters: {
      userId?: string;
      action?: string;
      startDate?: Date;
      endDate?: Date;
    } = {},
    pagination: {
      page: number;
      pageSize: number;
    } = { page: 1, pageSize: 50 }
  ): Promise<BatchQueryResult<any>> {
    const startTime = Date.now();
    
    try {
      const { page, pageSize } = pagination;
      const skip = (page - 1) * pageSize;

      // 构建where条件
      const where: any = {};
      
      if (filters.userId) {
        where.userId = filters.userId;
      }
      
      if (filters.action) {
        where.action = {
          contains: filters.action,
          mode: 'insensitive'
        };
      }
      
      if (filters.startDate || filters.endDate) {
        where.createdAt = {};
        if (filters.startDate) {
          where.createdAt.gte = filters.startDate;
        }
        if (filters.endDate) {
          where.createdAt.lte = filters.endDate;
        }
      }

      // 并行执行查询和计数
      const [auditLogs, totalCount] = await Promise.all([
        prisma.auditLog.findMany({
          where,
          include: {
            user: {
              select: {
                id: true,
                email: true,
                nickname: true
              }
            }
          },
          orderBy: {
            createdAt: 'desc'
          },
          skip,
          take: pageSize
        }),
        prisma.auditLog.count({ where })
      ]);

      const executionTime = Date.now() - startTime;
      
      logger.debug('审计日志分页查询完成', {
        page,
        pageSize,
        resultCount: auditLogs.length,
        totalCount,
        executionTime
      });

      return {
        data: auditLogs,
        totalCount,
        hasMore: skip + auditLogs.length < totalCount,
        executionTime
      };

    } catch (error) {
      logger.error('审计日志查询失败', { error: error.message });
      throw error;
    }
  }

  /**
   * 批量创建用户角色 - 优化批量插入
   */
  async createUserRolesBatch(
    userRoles: Array<{ userId: string; roleId: string }>
  ): Promise<{ created: number; executionTime: number }> {
    const startTime = Date.now();
    
    try {
      // 使用createMany进行批量插入
      const result = await prisma.userRole.createMany({
        data: userRoles,
        skipDuplicates: true // 跳过重复记录
      });

      const executionTime = Date.now() - startTime;
      
      logger.info('批量创建用户角色完成', {
        requested: userRoles.length,
        created: result.count,
        executionTime
      });

      return {
        created: result.count,
        executionTime
      };

    } catch (error) {
      logger.error('批量创建用户角色失败', { error: error.message });
      throw error;
    }
  }

  /**
   * 优化风险评估查询 - 聚合统计
   */
  async getRiskAssessmentStats(
    userId?: string,
    timeRange?: { start: Date; end: Date }
  ): Promise<any> {
    const startTime = Date.now();
    
    try {
      const where: any = {};
      
      if (userId) {
        where.userId = userId;
      }
      
      if (timeRange) {
        where.createdAt = {
          gte: timeRange.start,
          lte: timeRange.end
        };
      }

      // 使用聚合查询获取统计信息
      const [
        totalAssessments,
        riskLevelStats,
        avgRiskScore,
        recentAssessments
      ] = await Promise.all([
        // 总评估数
        prisma.riskAssessment.count({ where }),
        
        // 风险等级分布
        prisma.riskAssessment.groupBy({
          by: ['riskLevel'],
          where,
          _count: {
            id: true
          }
        }),
        
        // 平均风险分数
        prisma.riskAssessment.aggregate({
          where,
          _avg: {
            totalRisk: true
          }
        }),
        
        // 最近的评估记录
        prisma.riskAssessment.findMany({
          where,
          include: {
            user: {
              select: {
                id: true,
                email: true,
                nickname: true
              }
            }
          },
          orderBy: {
            createdAt: 'desc'
          },
          take: 10
        })
      ]);

      const executionTime = Date.now() - startTime;
      
      logger.debug('风险评估统计查询完成', {
        totalAssessments,
        executionTime
      });

      return {
        totalAssessments,
        riskLevelDistribution: riskLevelStats.reduce((acc, item) => {
          acc[item.riskLevel] = item._count.id;
          return acc;
        }, {} as Record<string, number>),
        averageRiskScore: avgRiskScore._avg.totalRisk || 0,
        recentAssessments,
        executionTime
      };

    } catch (error) {
      logger.error('风险评估统计查询失败', { error: error.message });
      throw error;
    }
  }

  /**
   * 清理过期数据 - 批量删除优化
   */
  async cleanupExpiredData(): Promise<{
    deletedSessions: number;
    deletedTokens: number;
    deletedCodes: number;
    executionTime: number;
  }> {
    const startTime = Date.now();
    
    try {
      const now = new Date();
      
      // 并行执行清理操作
      const [
        deletedSessions,
        deletedRefreshTokens,
        deletedAuthCodes,
        deletedPasswordResetTokens,
        deletedEmailVerificationTokens
      ] = await Promise.all([
        // 清理过期会话
        prisma.session.deleteMany({
          where: {
            OR: [
              { expiresAt: { lt: now } },
              { isActive: false }
            ]
          }
        }),
        
        // 清理过期刷新令牌
        prisma.refreshToken.deleteMany({
          where: {
            OR: [
              { expiresAt: { lt: now } },
              { isRevoked: true }
            ]
          }
        }),
        
        // 清理过期授权码
        prisma.authorizationCode.deleteMany({
          where: {
            OR: [
              { expiresAt: { lt: now } },
              { used: true }
            ]
          }
        }),
        
        // 清理过期密码重置令牌
        prisma.passwordResetToken.deleteMany({
          where: {
            OR: [
              { expiresAt: { lt: now } },
              { used: true }
            ]
          }
        }),
        
        // 清理过期邮箱验证令牌
        prisma.emailVerificationToken.deleteMany({
          where: {
            OR: [
              { expiresAt: { lt: now } },
              { used: true }
            ]
          }
        })
      ]);

      const executionTime = Date.now() - startTime;
      
      const totalDeleted = deletedSessions.count + deletedRefreshTokens.count + 
                          deletedAuthCodes.count + deletedPasswordResetTokens.count + 
                          deletedEmailVerificationTokens.count;

      logger.info('过期数据清理完成', {
        deletedSessions: deletedSessions.count,
        deletedRefreshTokens: deletedRefreshTokens.count,
        deletedAuthCodes: deletedAuthCodes.count,
        deletedPasswordResetTokens: deletedPasswordResetTokens.count,
        deletedEmailVerificationTokens: deletedEmailVerificationTokens.count,
        totalDeleted,
        executionTime
      });

      return {
        deletedSessions: deletedSessions.count,
        deletedTokens: deletedRefreshTokens.count + deletedPasswordResetTokens.count + 
                      deletedEmailVerificationTokens.count,
        deletedCodes: deletedAuthCodes.count,
        executionTime
      };

    } catch (error) {
      logger.error('过期数据清理失败', { error: error.message });
      throw error;
    }
  }

  /**
   * 获取数据库统计信息
   */
  async getDatabaseStats(): Promise<any> {
    const startTime = Date.now();
    
    try {
      // 并行获取各表的统计信息
      const [
        userCount,
        sessionCount,
        applicationCount,
        auditLogCount,
        riskAssessmentCount
      ] = await Promise.all([
        prisma.user.count(),
        prisma.session.count({ where: { isActive: true } }),
        prisma.application.count(),
        prisma.auditLog.count(),
        prisma.riskAssessment.count()
      ]);

      const executionTime = Date.now() - startTime;
      
      return {
        tables: {
          users: userCount,
          activeSessions: sessionCount,
          applications: applicationCount,
          auditLogs: auditLogCount,
          riskAssessments: riskAssessmentCount
        },
        totalRecords: userCount + sessionCount + applicationCount + auditLogCount + riskAssessmentCount,
        executionTime
      };

    } catch (error) {
      logger.error('获取数据库统计失败', { error: error.message });
      throw error;
    }
  }
}

// 创建单例实例
export const prismaOptimizer = new PrismaOptimizerService();
