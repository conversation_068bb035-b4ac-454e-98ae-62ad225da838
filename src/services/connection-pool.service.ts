/**
 * 数据库连接池管理服务
 * 优化数据库连接管理，提供连接池监控和自动调优
 */

import { PrismaClient } from '@prisma/client';
import { logger } from '@/config/logger';
import { EventEmitter } from 'events';

/**
 * 连接池配置
 */
export interface ConnectionPoolConfig {
  // 基础配置
  maxConnections: number;          // 最大连接数
  minConnections: number;          // 最小连接数
  acquireTimeoutMs: number;        // 获取连接超时时间
  createTimeoutMs: number;         // 创建连接超时时间
  destroyTimeoutMs: number;        // 销毁连接超时时间
  idleTimeoutMs: number;           // 空闲连接超时时间
  reapIntervalMs: number;          // 清理间隔时间
  
  // 高级配置
  createRetryIntervalMs: number;   // 创建重试间隔
  acquireRetryAttempts: number;    // 获取重试次数
  fifo: boolean;                   // 是否使用FIFO队列
  priorityRange: number;           // 优先级范围
  
  // 监控配置
  enableMetrics: boolean;          // 启用指标收集
  metricsInterval: number;         // 指标收集间隔
  
  // 自动调优配置
  enableAutoTuning: boolean;       // 启用自动调优
  tuningInterval: number;          // 调优检查间隔
  loadThreshold: number;           // 负载阈值
}

/**
 * 连接池统计信息
 */
export interface ConnectionPoolStats {
  // 连接统计
  totalConnections: number;        // 总连接数
  activeConnections: number;       // 活跃连接数
  idleConnections: number;         // 空闲连接数
  pendingRequests: number;         // 等待中的请求数
  
  // 性能统计
  averageAcquireTime: number;      // 平均获取连接时间
  averageCreateTime: number;       // 平均创建连接时间
  averageQueryTime: number;        // 平均查询时间
  
  // 错误统计
  connectionErrors: number;        // 连接错误数
  timeoutErrors: number;           // 超时错误数
  acquisitionErrors: number;       // 获取连接错误数
  
  // 历史统计
  totalAcquisitions: number;       // 总获取次数
  totalCreations: number;          // 总创建次数
  totalDestructions: number;       // 总销毁次数
  
  // 时间统计
  uptime: number;                  // 运行时间
  lastResetTime: Date;             // 上次重置时间
}

/**
 * 连接池健康状态
 */
export enum PoolHealthStatus {
  HEALTHY = 'healthy',
  DEGRADED = 'degraded',
  UNHEALTHY = 'unhealthy',
  CRITICAL = 'critical'
}

/**
 * 连接池健康检查结果
 */
export interface PoolHealthCheck {
  status: PoolHealthStatus;
  timestamp: Date;
  stats: ConnectionPoolStats;
  issues: string[];
  recommendations: string[];
  score: number; // 0-100
}

/**
 * 数据库连接池管理服务
 */
export class ConnectionPoolService extends EventEmitter {
  private config: ConnectionPoolConfig;
  private prismaClients: Map<string, PrismaClient> = new Map();
  private stats: ConnectionPoolStats;
  private startTime: Date;
  private metricsTimer?: NodeJS.Timeout;
  private tuningTimer?: NodeJS.Timeout;
  private healthHistory: PoolHealthCheck[] = [];

  constructor(config: Partial<ConnectionPoolConfig> = {}) {
    super();
    
    this.config = {
      maxConnections: 20,
      minConnections: 2,
      acquireTimeoutMs: 10000,
      createTimeoutMs: 10000,
      destroyTimeoutMs: 5000,
      idleTimeoutMs: 30000,
      reapIntervalMs: 1000,
      createRetryIntervalMs: 200,
      acquireRetryAttempts: 3,
      fifo: true,
      priorityRange: 1,
      enableMetrics: true,
      metricsInterval: 30000,
      enableAutoTuning: true,
      tuningInterval: 60000,
      loadThreshold: 0.8,
      ...config
    };

    this.startTime = new Date();
    this.initializeStats();
    this.startMonitoring();
  }

  /**
   * 初始化统计信息
   */
  private initializeStats(): void {
    this.stats = {
      totalConnections: 0,
      activeConnections: 0,
      idleConnections: 0,
      pendingRequests: 0,
      averageAcquireTime: 0,
      averageCreateTime: 0,
      averageQueryTime: 0,
      connectionErrors: 0,
      timeoutErrors: 0,
      acquisitionErrors: 0,
      totalAcquisitions: 0,
      totalCreations: 0,
      totalDestructions: 0,
      uptime: 0,
      lastResetTime: new Date()
    };
  }

  /**
   * 启动监控
   */
  private startMonitoring(): void {
    if (this.config.enableMetrics) {
      this.metricsTimer = setInterval(() => {
        this.collectMetrics();
      }, this.config.metricsInterval);
    }

    if (this.config.enableAutoTuning) {
      this.tuningTimer = setInterval(() => {
        this.performAutoTuning();
      }, this.config.tuningInterval);
    }

    logger.info('连接池监控已启动', {
      maxConnections: this.config.maxConnections,
      enableMetrics: this.config.enableMetrics,
      enableAutoTuning: this.config.enableAutoTuning
    });
  }

  /**
   * 创建优化的Prisma客户端
   */
  createOptimizedClient(name = 'default'): PrismaClient {
    if (this.prismaClients.has(name)) {
      return this.prismaClients.get(name)!;
    }

    const client = new PrismaClient({
      datasources: {
        db: {
          url: this.buildConnectionUrl()
        }
      },
      log: [
        { emit: 'event', level: 'query' },
        { emit: 'event', level: 'error' },
        { emit: 'event', level: 'info' },
        { emit: 'event', level: 'warn' }
      ]
    });

    // 配置事件监听
    this.setupClientEventListeners(client, name);

    this.prismaClients.set(name, client);
    this.stats.totalCreations++;

    logger.info('Prisma客户端创建成功', { name });
    return client;
  }

  /**
   * 构建连接URL
   */
  private buildConnectionUrl(): string {
    const baseUrl = process.env.DATABASE_URL || '';
    
    // 添加连接池参数
    const poolParams = new URLSearchParams({
      connection_limit: this.config.maxConnections.toString(),
      pool_timeout: Math.floor(this.config.acquireTimeoutMs / 1000).toString(),
      socket_timeout: Math.floor(this.config.createTimeoutMs / 1000).toString()
    });

    const separator = baseUrl.includes('?') ? '&' : '?';
    return `${baseUrl}${separator}${poolParams.toString()}`;
  }

  /**
   * 设置客户端事件监听
   */
  private setupClientEventListeners(client: PrismaClient, name: string): void {
    client.$on('query', (event) => {
      this.stats.averageQueryTime = this.updateAverage(
        this.stats.averageQueryTime,
        event.duration,
        this.stats.totalAcquisitions
      );
      
      this.emit('query', { client: name, event });
    });

    client.$on('error', (event) => {
      this.stats.connectionErrors++;
      logger.error('Prisma客户端错误', { client: name, error: event });
      this.emit('error', { client: name, event });
    });

    client.$on('info', (event) => {
      logger.info('Prisma客户端信息', { client: name, info: event });
    });

    client.$on('warn', (event) => {
      logger.warn('Prisma客户端警告', { client: name, warning: event });
    });
  }

  /**
   * 收集指标
   */
  private async collectMetrics(): Promise<void> {
    try {
      // 更新运行时间
      this.stats.uptime = Date.now() - this.startTime.getTime();

      // 执行健康检查
      const healthCheck = await this.performHealthCheck();
      this.healthHistory.push(healthCheck);

      // 保持历史记录大小
      if (this.healthHistory.length > 100) {
        this.healthHistory.shift();
      }

      // 发出指标事件
      this.emit('metrics', {
        stats: this.stats,
        health: healthCheck
      });

      // 如果状态不健康，发出告警
      if (healthCheck.status !== PoolHealthStatus.HEALTHY) {
        this.emit('healthAlert', healthCheck);
      }

    } catch (error) {
      logger.error('收集连接池指标失败', { error: error.message });
    }
  }

  /**
   * 执行健康检查
   */
  private async performHealthCheck(): Promise<PoolHealthCheck> {
    const issues: string[] = [];
    const recommendations: string[] = [];
    let score = 100;

    // 检查连接数
    if (this.stats.activeConnections > this.config.maxConnections * 0.9) {
      issues.push('活跃连接数接近最大值');
      recommendations.push('考虑增加最大连接数或优化查询');
      score -= 20;
    }

    // 检查等待请求数
    if (this.stats.pendingRequests > 10) {
      issues.push('等待连接的请求过多');
      recommendations.push('检查连接池配置和查询性能');
      score -= 15;
    }

    // 检查平均获取时间
    if (this.stats.averageAcquireTime > 1000) {
      issues.push('获取连接时间过长');
      recommendations.push('优化连接池大小或查询性能');
      score -= 15;
    }

    // 检查错误率
    const errorRate = this.stats.totalAcquisitions > 0 
      ? (this.stats.connectionErrors + this.stats.timeoutErrors) / this.stats.totalAcquisitions 
      : 0;
    
    if (errorRate > 0.05) { // 5%错误率
      issues.push('连接错误率过高');
      recommendations.push('检查数据库连接和网络状况');
      score -= 25;
    }

    // 确定健康状态
    let status: PoolHealthStatus;
    if (score >= 90) status = PoolHealthStatus.HEALTHY;
    else if (score >= 70) status = PoolHealthStatus.DEGRADED;
    else if (score >= 50) status = PoolHealthStatus.UNHEALTHY;
    else status = PoolHealthStatus.CRITICAL;

    return {
      status,
      timestamp: new Date(),
      stats: { ...this.stats },
      issues,
      recommendations,
      score
    };
  }

  /**
   * 执行自动调优
   */
  private async performAutoTuning(): Promise<void> {
    try {
      const healthCheck = await this.performHealthCheck();
      
      // 根据健康状态调整配置
      if (healthCheck.status === PoolHealthStatus.DEGRADED || 
          healthCheck.status === PoolHealthStatus.UNHEALTHY) {
        
        await this.adjustPoolConfiguration(healthCheck);
      }

    } catch (error) {
      logger.error('自动调优失败', { error: error.message });
    }
  }

  /**
   * 调整连接池配置
   */
  private async adjustPoolConfiguration(healthCheck: PoolHealthCheck): Promise<void> {
    const oldConfig = { ...this.config };
    let configChanged = false;

    // 如果活跃连接数过高，增加最大连接数
    if (this.stats.activeConnections > this.config.maxConnections * 0.9) {
      const newMax = Math.min(this.config.maxConnections + 5, 50);
      if (newMax > this.config.maxConnections) {
        this.config.maxConnections = newMax;
        configChanged = true;
        logger.info('自动调优：增加最大连接数', { 
          from: oldConfig.maxConnections, 
          to: newMax 
        });
      }
    }

    // 如果获取时间过长，增加超时时间
    if (this.stats.averageAcquireTime > this.config.acquireTimeoutMs * 0.8) {
      const newTimeout = Math.min(this.config.acquireTimeoutMs + 2000, 30000);
      if (newTimeout > this.config.acquireTimeoutMs) {
        this.config.acquireTimeoutMs = newTimeout;
        configChanged = true;
        logger.info('自动调优：增加获取超时时间', { 
          from: oldConfig.acquireTimeoutMs, 
          to: newTimeout 
        });
      }
    }

    if (configChanged) {
      this.emit('configurationChanged', {
        oldConfig,
        newConfig: { ...this.config },
        reason: 'auto_tuning',
        healthCheck
      });

      // 重新创建客户端以应用新配置
      await this.recreateClients();
    }
  }

  /**
   * 重新创建客户端
   */
  private async recreateClients(): Promise<void> {
    const clientNames = Array.from(this.prismaClients.keys());
    
    for (const name of clientNames) {
      const oldClient = this.prismaClients.get(name);
      if (oldClient) {
        await oldClient.$disconnect();
        this.prismaClients.delete(name);
        this.stats.totalDestructions++;
      }
      
      // 创建新客户端
      this.createOptimizedClient(name);
    }

    logger.info('连接池客户端重新创建完成', { 
      clientCount: clientNames.length 
    });
  }

  /**
   * 更新平均值
   */
  private updateAverage(currentAvg: number, newValue: number, count: number): number {
    if (count === 0) return newValue;
    return (currentAvg * (count - 1) + newValue) / count;
  }

  /**
   * 获取客户端
   */
  getClient(name = 'default'): PrismaClient {
    let client = this.prismaClients.get(name);
    if (!client) {
      client = this.createOptimizedClient(name);
    }
    
    this.stats.totalAcquisitions++;
    return client;
  }

  /**
   * 获取连接池统计
   */
  getStats(): ConnectionPoolStats {
    return { ...this.stats };
  }

  /**
   * 获取健康历史
   */
  getHealthHistory(limit = 50): PoolHealthCheck[] {
    return this.healthHistory.slice(-limit);
  }

  /**
   * 获取当前健康状态
   */
  getCurrentHealth(): PoolHealthCheck | null {
    return this.healthHistory.length > 0 
      ? this.healthHistory[this.healthHistory.length - 1] 
      : null;
  }

  /**
   * 重置统计信息
   */
  resetStats(): void {
    this.initializeStats();
    this.healthHistory = [];
    logger.info('连接池统计信息已重置');
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<ConnectionPoolConfig>): void {
    const oldConfig = { ...this.config };
    this.config = { ...this.config, ...newConfig };
    
    this.emit('configurationChanged', {
      oldConfig,
      newConfig: { ...this.config },
      reason: 'manual_update'
    });

    logger.info('连接池配置已更新', { 
      changes: Object.keys(newConfig) 
    });
  }

  /**
   * 关闭连接池
   */
  async shutdown(): Promise<void> {
    // 停止定时器
    if (this.metricsTimer) {
      clearInterval(this.metricsTimer);
    }
    if (this.tuningTimer) {
      clearInterval(this.tuningTimer);
    }

    // 断开所有客户端连接
    for (const [name, client] of this.prismaClients) {
      try {
        await client.$disconnect();
        logger.info('Prisma客户端已断开', { name });
      } catch (error) {
        logger.error('断开Prisma客户端失败', { name, error: error.message });
      }
    }

    this.prismaClients.clear();
    this.removeAllListeners();
    
    logger.info('连接池服务已关闭');
  }
}

// 创建单例实例
export const connectionPool = new ConnectionPoolService();
