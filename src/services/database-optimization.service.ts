/**
 * 数据库优化服务
 * 提供数据库查询优化、连接池管理、性能监控等功能
 */

import { PrismaClient } from '@prisma/client';
import { logger } from '@/config/logger';
import { metricsCollector } from '@/services/metrics-collector.service';

/**
 * 查询性能统计接口
 */
interface QueryStats {
  query: string;
  duration: number;
  timestamp: Date;
  success: boolean;
  error?: string;
}

/**
 * 连接池状态接口
 */
interface ConnectionPoolStats {
  totalConnections: number;
  activeConnections: number;
  idleConnections: number;
  waitingConnections: number;
  maxConnections: number;
}

/**
 * 数据库优化服务
 */
export class DatabaseOptimizationService {
  private queryStats: QueryStats[] = [];
  private slowQueryThreshold = 1000; // 慢查询阈值（毫秒）
  private maxStatsHistory = 1000; // 最大统计历史记录数

  constructor(private prisma: PrismaClient) {}

  /**
   * 执行优化的查询
   */
  async executeOptimizedQuery<T>(
    queryName: string,
    queryFn: () => Promise<T>,
    options: {
      useCache?: boolean;
      cacheKey?: string;
      cacheTTL?: number;
      timeout?: number;
    } = {}
  ): Promise<T> {
    const startTime = Date.now();
    const { useCache = false, cacheKey, cacheTTL = 300, timeout = 30000 } = options;

    try {
      // 如果启用缓存，先尝试从缓存获取
      if (useCache && cacheKey) {
        const cachedResult = await this.getCachedResult<T>(cacheKey);
        if (cachedResult !== null) {
          this.recordQueryStats(queryName, Date.now() - startTime, true);
          metricsCollector.incrementCounter('database_cache_hits', { query: queryName });
          return cachedResult;
        }
        metricsCollector.incrementCounter('database_cache_misses', { query: queryName });
      }

      // 设置查询超时
      const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => reject(new Error('Query timeout')), timeout);
      });

      // 执行查询
      const result = await Promise.race([queryFn(), timeoutPromise]);
      const duration = Date.now() - startTime;

      // 记录查询统计
      this.recordQueryStats(queryName, duration, true);

      // 记录性能指标
      metricsCollector.recordHistogram('database_query_duration', duration, {
        query: queryName,
        success: 'true'
      });

      // 检查是否为慢查询
      if (duration > this.slowQueryThreshold) {
        logger.warn('检测到慢查询', {
          query: queryName,
          duration,
          threshold: this.slowQueryThreshold
        });
        metricsCollector.incrementCounter('database_slow_queries', { query: queryName });
      }

      // 缓存结果
      if (useCache && cacheKey) {
        await this.setCachedResult(cacheKey, result, cacheTTL);
      }

      return result;

    } catch (error) {
      const duration = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : String(error);

      // 记录错误统计
      this.recordQueryStats(queryName, duration, false, errorMessage);

      // 记录错误指标
      metricsCollector.recordHistogram('database_query_duration', duration, {
        query: queryName,
        success: 'false'
      });
      metricsCollector.incrementCounter('database_query_errors', {
        query: queryName,
        error: errorMessage
      });

      logger.error('数据库查询失败', {
        query: queryName,
        duration,
        error: errorMessage
      });

      throw error;
    }
  }

  /**
   * 获取用户信息（优化版本）
   */
  async getOptimizedUser(userId: string) {
    return this.executeOptimizedQuery(
      'getUser',
      () => this.prisma.user.findUnique({
        where: { id: userId },
        select: {
          id: true,
          email: true,
          nickname: true,
          avatar: true,
          isEmailVerified: true,
          createdAt: true,
          lastLoginAt: true,
          // 只选择需要的字段，避免查询敏感信息
        }
      }),
      {
        useCache: true,
        cacheKey: `user:${userId}`,
        cacheTTL: 300 // 5分钟缓存
      }
    );
  }

  /**
   * 获取用户会话（优化版本）
   */
  async getOptimizedUserSessions(userId: string, limit = 10) {
    return this.executeOptimizedQuery(
      'getUserSessions',
      () => this.prisma.session.findMany({
        where: {
          userId,
          isActive: true
        },
        select: {
          id: true,
          ipAddress: true,
          userAgent: true,
          createdAt: true,
          lastAccessedAt: true,
          expiresAt: true
        },
        orderBy: { lastAccessedAt: 'desc' },
        take: limit
      }),
      {
        useCache: true,
        cacheKey: `user_sessions:${userId}`,
        cacheTTL: 60 // 1分钟缓存
      }
    );
  }

  /**
   * 获取用户角色（优化版本）
   */
  async getOptimizedUserRoles(userId: string) {
    return this.executeOptimizedQuery(
      'getUserRoles',
      () => this.prisma.userRole.findMany({
        where: { userId },
        include: {
          role: {
            select: {
              id: true,
              name: true,
              description: true
            }
          }
        }
      }),
      {
        useCache: true,
        cacheKey: `user_roles:${userId}`,
        cacheTTL: 600 // 10分钟缓存
      }
    );
  }

  /**
   * 批量获取用户信息
   */
  async getBatchUsers(userIds: string[]) {
    if (userIds.length === 0) return [];

    return this.executeOptimizedQuery(
      'getBatchUsers',
      () => this.prisma.user.findMany({
        where: {
          id: { in: userIds }
        },
        select: {
          id: true,
          email: true,
          nickname: true,
          avatar: true,
          isEmailVerified: true,
          createdAt: true
        }
      }),
      { timeout: 10000 }
    );
  }

  /**
   * 获取应用统计信息
   */
  async getApplicationStats(applicationId: string) {
    return this.executeOptimizedQuery(
      'getApplicationStats',
      async () => {
        const [userCount, sessionCount, recentLogins] = await Promise.all([
          // 用户总数
          this.prisma.user.count(),
          
          // 活跃会话数
          this.prisma.session.count({
            where: {
              isActive: true,
              expiresAt: { gt: new Date() }
            }
          }),
          
          // 最近24小时登录数
          this.prisma.auditLog.count({
            where: {
              action: 'login',
              createdAt: {
                gte: new Date(Date.now() - 24 * 60 * 60 * 1000)
              }
            }
          })
        ]);

        return {
          userCount,
          sessionCount,
          recentLogins,
          timestamp: new Date()
        };
      },
      {
        useCache: true,
        cacheKey: `app_stats:${applicationId}`,
        cacheTTL: 300 // 5分钟缓存
      }
    );
  }

  /**
   * 清理过期数据
   */
  async cleanupExpiredData() {
    return this.executeOptimizedQuery(
      'cleanupExpiredData',
      async () => {
        const now = new Date();
        
        const [expiredSessions, expiredTokens] = await Promise.all([
          // 清理过期会话
          this.prisma.session.deleteMany({
            where: {
              OR: [
                { expiresAt: { lt: now } },
                { 
                  isActive: false,
                  updatedAt: { lt: new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000) }
                }
              ]
            }
          }),
          
          // 清理过期的OAuth令牌
          this.prisma.oauthToken.deleteMany({
            where: {
              expiresAt: { lt: now }
            }
          })
        ]);

        logger.info('清理过期数据完成', {
          expiredSessions: expiredSessions.count,
          expiredTokens: expiredTokens.count
        });

        return {
          expiredSessions: expiredSessions.count,
          expiredTokens: expiredTokens.count
        };
      },
      { timeout: 60000 } // 1分钟超时
    );
  }

  /**
   * 记录查询统计
   */
  private recordQueryStats(
    query: string,
    duration: number,
    success: boolean,
    error?: string
  ): void {
    const stat: QueryStats = {
      query,
      duration,
      timestamp: new Date(),
      success,
      error
    };

    this.queryStats.push(stat);

    // 限制历史记录数量
    if (this.queryStats.length > this.maxStatsHistory) {
      this.queryStats = this.queryStats.slice(-this.maxStatsHistory);
    }
  }

  /**
   * 获取查询统计
   */
  getQueryStats(timeRange?: { start: Date; end: Date }) {
    let stats = this.queryStats;

    if (timeRange) {
      stats = stats.filter(stat => 
        stat.timestamp >= timeRange.start && stat.timestamp <= timeRange.end
      );
    }

    const totalQueries = stats.length;
    const successfulQueries = stats.filter(s => s.success).length;
    const failedQueries = totalQueries - successfulQueries;
    const avgDuration = stats.length > 0 
      ? stats.reduce((sum, s) => sum + s.duration, 0) / stats.length 
      : 0;

    const slowQueries = stats.filter(s => s.duration > this.slowQueryThreshold);
    
    const queryBreakdown = stats.reduce((acc, stat) => {
      if (!acc[stat.query]) {
        acc[stat.query] = { count: 0, totalDuration: 0, errors: 0 };
      }
      acc[stat.query].count++;
      acc[stat.query].totalDuration += stat.duration;
      if (!stat.success) {
        acc[stat.query].errors++;
      }
      return acc;
    }, {} as Record<string, { count: number; totalDuration: number; errors: number }>);

    return {
      totalQueries,
      successfulQueries,
      failedQueries,
      successRate: totalQueries > 0 ? (successfulQueries / totalQueries) * 100 : 0,
      avgDuration,
      slowQueries: slowQueries.length,
      slowQueryRate: totalQueries > 0 ? (slowQueries.length / totalQueries) * 100 : 0,
      queryBreakdown: Object.entries(queryBreakdown).map(([query, data]) => ({
        query,
        count: data.count,
        avgDuration: data.totalDuration / data.count,
        errorRate: (data.errors / data.count) * 100
      }))
    };
  }

  /**
   * 获取连接池状态
   */
  async getConnectionPoolStats(): Promise<ConnectionPoolStats> {
    // 注意：这个实现依赖于Prisma的内部API，可能需要根据实际版本调整
    try {
      const metrics = await this.prisma.$metrics.json();
      
      return {
        totalConnections: metrics.counters.find(c => c.key === 'prisma_pool_connections_open')?.value || 0,
        activeConnections: metrics.counters.find(c => c.key === 'prisma_pool_connections_busy')?.value || 0,
        idleConnections: metrics.counters.find(c => c.key === 'prisma_pool_connections_idle')?.value || 0,
        waitingConnections: metrics.counters.find(c => c.key === 'prisma_pool_connections_waiting')?.value || 0,
        maxConnections: parseInt(process.env.DATABASE_CONNECTION_LIMIT || '10')
      };
    } catch (error) {
      logger.warn('无法获取连接池统计信息', { error: error.message });
      return {
        totalConnections: 0,
        activeConnections: 0,
        idleConnections: 0,
        waitingConnections: 0,
        maxConnections: parseInt(process.env.DATABASE_CONNECTION_LIMIT || '10')
      };
    }
  }

  /**
   * 从缓存获取结果
   */
  private async getCachedResult<T>(key: string): Promise<T | null> {
    try {
      const { cacheService } = await import('@/services/cache.service');
      return await cacheService.get(key);
    } catch (error) {
      logger.warn('缓存获取失败', { key, error: error.message });
      return null;
    }
  }

  /**
   * 设置缓存结果
   */
  private async setCachedResult<T>(key: string, value: T, ttl: number): Promise<void> {
    try {
      const { cacheService } = await import('@/services/cache.service');
      await cacheService.set(key, value, ttl);
    } catch (error) {
      logger.warn('缓存设置失败', { key, error: error.message });
    }
  }

  /**
   * 设置慢查询阈值
   */
  setSlowQueryThreshold(threshold: number): void {
    this.slowQueryThreshold = threshold;
    logger.info('慢查询阈值已更新', { threshold });
  }

  /**
   * 获取数据库健康状态
   */
  async getDatabaseHealth() {
    try {
      const startTime = Date.now();
      await this.prisma.$queryRaw`SELECT 1`;
      const responseTime = Date.now() - startTime;

      const connectionStats = await this.getConnectionPoolStats();
      const queryStats = this.getQueryStats({
        start: new Date(Date.now() - 5 * 60 * 1000), // 最近5分钟
        end: new Date()
      });

      return {
        status: 'healthy',
        responseTime,
        connectionPool: connectionStats,
        recentQueries: queryStats,
        timestamp: new Date()
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        error: error instanceof Error ? error.message : String(error),
        timestamp: new Date()
      };
    }
  }
}

// 创建单例实例
export const databaseOptimizationService = new DatabaseOptimizationService(
  new PrismaClient({
    log: [
      { level: 'query', emit: 'event' },
      { level: 'error', emit: 'event' },
      { level: 'warn', emit: 'event' }
    ]
  })
);

// 监听Prisma查询事件
databaseOptimizationService['prisma'].$on('query', (e) => {
  if (e.duration > 1000) { // 记录超过1秒的查询
    logger.warn('慢查询检测', {
      query: e.query,
      duration: e.duration,
      params: e.params
    });
  }
});
