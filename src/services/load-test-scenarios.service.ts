/**
 * 负载测试场景服务
 * 提供各种真实业务场景的负载测试
 */

import { logger } from '@/config/logger';
import { TestScenario } from '@/services/performance-test.service';
import axios from 'axios';
import { prisma } from '@/config/database';
import { redisService } from '@/services/redis.service';

/**
 * 负载测试场景生成器
 */
export class LoadTestScenariosService {
  private baseURL: string;
  private authTokens: Map<string, string> = new Map();

  constructor() {
    this.baseURL = process.env.BASE_URL || 'http://localhost:3000';
  }

  /**
   * 获取用户认证场景
   */
  getUserAuthenticationScenarios(): TestScenario[] {
    return [
      {
        name: 'user_login',
        weight: 0.4,
        execute: async () => {
          try {
            const startTime = Date.now();
            
            const response = await axios.post(`${this.baseURL}/api/v1/auth/login`, {
              email: '<EMAIL>',
              password: 'password123'
            });
            
            const duration = Date.now() - startTime;
            return {
              success: response.status === 200,
              duration
            };
          } catch (error) {
            return {
              success: false,
              duration: 0,
              error: error instanceof Error ? error.message : 'Login failed'
            };
          }
        }
      },
      {
        name: 'user_registration',
        weight: 0.1,
        execute: async () => {
          try {
            const startTime = Date.now();
            const randomId = Math.random().toString(36).substring(7);
            
            const response = await axios.post(`${this.baseURL}/api/v1/auth/register`, {
              email: `test-${randomId}@example.com`,
              password: 'password123',
              nickname: `Test User ${randomId}`
            });
            
            const duration = Date.now() - startTime;
            return {
              success: response.status === 201,
              duration
            };
          } catch (error) {
            return {
              success: false,
              duration: 0,
              error: error instanceof Error ? error.message : 'Registration failed'
            };
          }
        }
      },
      {
        name: 'token_refresh',
        weight: 0.3,
        execute: async () => {
          try {
            const startTime = Date.now();
            
            // 模拟令牌刷新
            const response = await axios.post(`${this.baseURL}/api/v1/auth/refresh`, {
              refreshToken: 'mock-refresh-token'
            });
            
            const duration = Date.now() - startTime;
            return {
              success: response.status === 200,
              duration
            };
          } catch (error) {
            return {
              success: false,
              duration: 0,
              error: error instanceof Error ? error.message : 'Token refresh failed'
            };
          }
        }
      },
      {
        name: 'user_logout',
        weight: 0.2,
        execute: async () => {
          try {
            const startTime = Date.now();
            
            const response = await axios.post(`${this.baseURL}/api/v1/auth/logout`, {}, {
              headers: {
                'Authorization': 'Bearer mock-token'
              }
            });
            
            const duration = Date.now() - startTime;
            return {
              success: response.status === 200,
              duration
            };
          } catch (error) {
            return {
              success: false,
              duration: 0,
              error: error instanceof Error ? error.message : 'Logout failed'
            };
          }
        }
      }
    ];
  }

  /**
   * 获取OAuth认证场景
   */
  getOAuthScenarios(): TestScenario[] {
    return [
      {
        name: 'oauth_google_login',
        weight: 0.4,
        execute: async () => {
          try {
            const startTime = Date.now();
            
            const response = await axios.post(`${this.baseURL}/api/v1/oauth/google/callback`, {
              code: 'mock-oauth-code',
              state: 'mock-state'
            });
            
            const duration = Date.now() - startTime;
            return {
              success: response.status === 200,
              duration
            };
          } catch (error) {
            return {
              success: false,
              duration: 0,
              error: error instanceof Error ? error.message : 'OAuth Google login failed'
            };
          }
        }
      },
      {
        name: 'oauth_github_login',
        weight: 0.3,
        execute: async () => {
          try {
            const startTime = Date.now();
            
            const response = await axios.post(`${this.baseURL}/api/v1/oauth/github/callback`, {
              code: 'mock-oauth-code',
              state: 'mock-state'
            });
            
            const duration = Date.now() - startTime;
            return {
              success: response.status === 200,
              duration
            };
          } catch (error) {
            return {
              success: false,
              duration: 0,
              error: error instanceof Error ? error.message : 'OAuth GitHub login failed'
            };
          }
        }
      },
      {
        name: 'oauth_microsoft_login',
        weight: 0.3,
        execute: async () => {
          try {
            const startTime = Date.now();
            
            const response = await axios.post(`${this.baseURL}/api/v1/oauth/microsoft/callback`, {
              code: 'mock-oauth-code',
              state: 'mock-state'
            });
            
            const duration = Date.now() - startTime;
            return {
              success: response.status === 200,
              duration
            };
          } catch (error) {
            return {
              success: false,
              duration: 0,
              error: error instanceof Error ? error.message : 'OAuth Microsoft login failed'
            };
          }
        }
      }
    ];
  }

  /**
   * 获取API访问场景
   */
  getAPIAccessScenarios(): TestScenario[] {
    return [
      {
        name: 'get_user_profile',
        weight: 0.3,
        execute: async () => {
          try {
            const startTime = Date.now();
            const token = await this.getAuthToken();
            
            const response = await axios.get(`${this.baseURL}/api/v1/users/profile`, {
              headers: {
                'Authorization': `Bearer ${token}`
              }
            });
            
            const duration = Date.now() - startTime;
            return {
              success: response.status === 200,
              duration
            };
          } catch (error) {
            return {
              success: false,
              duration: 0,
              error: error instanceof Error ? error.message : 'Get user profile failed'
            };
          }
        }
      },
      {
        name: 'update_user_profile',
        weight: 0.2,
        execute: async () => {
          try {
            const startTime = Date.now();
            const token = await this.getAuthToken();
            
            const response = await axios.put(`${this.baseURL}/api/v1/users/profile`, {
              nickname: `Updated User ${Date.now()}`
            }, {
              headers: {
                'Authorization': `Bearer ${token}`
              }
            });
            
            const duration = Date.now() - startTime;
            return {
              success: response.status === 200,
              duration
            };
          } catch (error) {
            return {
              success: false,
              duration: 0,
              error: error instanceof Error ? error.message : 'Update user profile failed'
            };
          }
        }
      },
      {
        name: 'get_user_sessions',
        weight: 0.25,
        execute: async () => {
          try {
            const startTime = Date.now();
            const token = await this.getAuthToken();
            
            const response = await axios.get(`${this.baseURL}/api/v1/users/sessions`, {
              headers: {
                'Authorization': `Bearer ${token}`
              }
            });
            
            const duration = Date.now() - startTime;
            return {
              success: response.status === 200,
              duration
            };
          } catch (error) {
            return {
              success: false,
              duration: 0,
              error: error instanceof Error ? error.message : 'Get user sessions failed'
            };
          }
        }
      },
      {
        name: 'health_check',
        weight: 0.25,
        execute: async () => {
          try {
            const startTime = Date.now();
            
            const response = await axios.get(`${this.baseURL}/api/v1/monitoring/health`);
            
            const duration = Date.now() - startTime;
            return {
              success: response.status === 200,
              duration
            };
          } catch (error) {
            return {
              success: false,
              duration: 0,
              error: error instanceof Error ? error.message : 'Health check failed'
            };
          }
        }
      }
    ];
  }

  /**
   * 获取数据库操作场景
   */
  getDatabaseScenarios(): TestScenario[] {
    return [
      {
        name: 'user_query',
        weight: 0.4,
        execute: async () => {
          try {
            const startTime = Date.now();
            
            await prisma.user.findMany({
              take: 10,
              orderBy: { createdAt: 'desc' }
            });
            
            const duration = Date.now() - startTime;
            return {
              success: true,
              duration
            };
          } catch (error) {
            return {
              success: false,
              duration: 0,
              error: error instanceof Error ? error.message : 'User query failed'
            };
          }
        }
      },
      {
        name: 'session_query',
        weight: 0.3,
        execute: async () => {
          try {
            const startTime = Date.now();
            
            await prisma.session.findMany({
              where: { isActive: true },
              include: { user: true },
              take: 20
            });
            
            const duration = Date.now() - startTime;
            return {
              success: true,
              duration
            };
          } catch (error) {
            return {
              success: false,
              duration: 0,
              error: error instanceof Error ? error.message : 'Session query failed'
            };
          }
        }
      },
      {
        name: 'audit_log_insert',
        weight: 0.2,
        execute: async () => {
          try {
            const startTime = Date.now();
            
            await prisma.auditLog.create({
              data: {
                userId: 'test-user-id',
                action: 'test_action',
                resource: 'test_resource',
                details: { test: true },
                ipAddress: '127.0.0.1',
                userAgent: 'test-agent'
              }
            });
            
            const duration = Date.now() - startTime;
            return {
              success: true,
              duration
            };
          } catch (error) {
            return {
              success: false,
              duration: 0,
              error: error instanceof Error ? error.message : 'Audit log insert failed'
            };
          }
        }
      },
      {
        name: 'complex_join_query',
        weight: 0.1,
        execute: async () => {
          try {
            const startTime = Date.now();
            
            await prisma.user.findMany({
              include: {
                userRoles: {
                  include: {
                    role: true
                  }
                },
                sessions: {
                  where: { isActive: true }
                },
                auditLogs: {
                  take: 5,
                  orderBy: { createdAt: 'desc' }
                }
              },
              take: 5
            });
            
            const duration = Date.now() - startTime;
            return {
              success: true,
              duration
            };
          } catch (error) {
            return {
              success: false,
              duration: 0,
              error: error instanceof Error ? error.message : 'Complex join query failed'
            };
          }
        }
      }
    ];
  }

  /**
   * 获取缓存操作场景
   */
  getCacheScenarios(): TestScenario[] {
    return [
      {
        name: 'cache_set',
        weight: 0.3,
        execute: async () => {
          try {
            const startTime = Date.now();
            const key = `test:${Date.now()}:${Math.random()}`;
            
            await redisService.set(key, JSON.stringify({ test: 'data' }), 300);
            
            const duration = Date.now() - startTime;
            return {
              success: true,
              duration
            };
          } catch (error) {
            return {
              success: false,
              duration: 0,
              error: error instanceof Error ? error.message : 'Cache set failed'
            };
          }
        }
      },
      {
        name: 'cache_get',
        weight: 0.4,
        execute: async () => {
          try {
            const startTime = Date.now();
            
            await redisService.get('test:cache:key');
            
            const duration = Date.now() - startTime;
            return {
              success: true,
              duration
            };
          } catch (error) {
            return {
              success: false,
              duration: 0,
              error: error instanceof Error ? error.message : 'Cache get failed'
            };
          }
        }
      },
      {
        name: 'session_cache',
        weight: 0.2,
        execute: async () => {
          try {
            const startTime = Date.now();
            const sessionId = `session:${Date.now()}`;
            
            await redisService.hset(sessionId, 'userId', 'test-user-id');
            await redisService.hset(sessionId, 'createdAt', new Date().toISOString());
            await redisService.expire(sessionId, 3600);
            
            const duration = Date.now() - startTime;
            return {
              success: true,
              duration
            };
          } catch (error) {
            return {
              success: false,
              duration: 0,
              error: error instanceof Error ? error.message : 'Session cache failed'
            };
          }
        }
      },
      {
        name: 'rate_limit_check',
        weight: 0.1,
        execute: async () => {
          try {
            const startTime = Date.now();
            const key = `rate_limit:${Date.now()}`;
            
            await redisService.incr(key);
            await redisService.expire(key, 60);
            
            const duration = Date.now() - startTime;
            return {
              success: true,
              duration
            };
          } catch (error) {
            return {
              success: false,
              duration: 0,
              error: error instanceof Error ? error.message : 'Rate limit check failed'
            };
          }
        }
      }
    ];
  }

  /**
   * 获取混合场景（模拟真实用户行为）
   */
  getMixedScenarios(): TestScenario[] {
    const authScenarios = this.getUserAuthenticationScenarios();
    const apiScenarios = this.getAPIAccessScenarios();
    const cacheScenarios = this.getCacheScenarios();

    return [
      ...authScenarios.map(s => ({ ...s, weight: s.weight * 0.3 })),
      ...apiScenarios.map(s => ({ ...s, weight: s.weight * 0.5 })),
      ...cacheScenarios.map(s => ({ ...s, weight: s.weight * 0.2 }))
    ];
  }

  /**
   * 获取认证令牌（模拟）
   */
  private async getAuthToken(): Promise<string> {
    // 简化实现，返回模拟令牌
    return 'mock-auth-token';
  }

  /**
   * 创建自定义场景
   */
  createCustomScenario(
    name: string,
    weight: number,
    executeFunction: () => Promise<{ success: boolean; duration: number; error?: string }>
  ): TestScenario {
    return {
      name,
      weight,
      execute: executeFunction
    };
  }

  /**
   * 获取所有可用场景类型
   */
  getAllScenarioTypes(): { [key: string]: () => TestScenario[] } {
    return {
      authentication: () => this.getUserAuthenticationScenarios(),
      oauth: () => this.getOAuthScenarios(),
      api: () => this.getAPIAccessScenarios(),
      database: () => this.getDatabaseScenarios(),
      cache: () => this.getCacheScenarios(),
      mixed: () => this.getMixedScenarios()
    };
  }

  /**
   * 根据类型获取场景
   */
  getScenariosByType(type: string): TestScenario[] {
    const scenarioTypes = this.getAllScenarioTypes();
    const scenarioFunction = scenarioTypes[type];
    
    if (!scenarioFunction) {
      throw new Error(`未知的场景类型: ${type}`);
    }
    
    return scenarioFunction();
  }
}

// 创建单例实例
export const loadTestScenariosService = new LoadTestScenariosService();
