/**
 * OIDC客户端管理服务
 * 管理OAuth 2.0/OpenID Connect客户端的注册、配置和生命周期
 */

import { v4 as uuidv4 } from 'uuid';
import crypto from 'crypto';
import { prisma } from '@/config/database';
import { logger, logAuditEvent } from '@/config/logger';
import { validateClientInfo } from '@/utils/oidc-validation';

export interface CreateClientRequest {
  name: string;
  description?: string;
  redirectUris: string[];
  grantTypes?: string[];
  responseTypes?: string[];
  scopes?: string[];
  applicationId?: string;
  tokenEndpointAuthMethod?: string;
  requirePkce?: boolean;
  requireConsent?: boolean;
  accessTokenLifetime?: number;
  refreshTokenLifetime?: number;
  idTokenLifetime?: number;
  logoUri?: string;
  clientUri?: string;
  policyUri?: string;
  tosUri?: string;
}

export interface ClientResponse {
  id: string;
  clientId: string;
  clientSecret: string;
  name: string;
  description?: string;
  redirectUris: string[];
  grantTypes: string[];
  responseTypes: string[];
  scopes: string[];
  tokenEndpointAuthMethod: string;
  requirePkce: boolean;
  requireConsent: boolean;
  accessTokenLifetime: number;
  refreshTokenLifetime: number;
  idTokenLifetime: number;
  logoUri?: string;
  clientUri?: string;
  policyUri?: string;
  tosUri?: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export class OIDCClientService {

  /**
   * 创建新的OIDC客户端
   */
  async createClient(request: CreateClientRequest): Promise<ClientResponse> {
    try {
      // 验证客户端信息
      const validation = validateClientInfo({
        clientId: 'temp', // 临时值，稍后生成
        redirectUris: request.redirectUris,
        grantTypes: request.grantTypes,
        responseTypes: request.responseTypes
      });

      if (!validation.isValid) {
        throw new Error(`客户端信息验证失败: ${validation.errors.join(', ')}`);
      }

      // 生成客户端ID和密钥
      const clientId = this.generateClientId();
      const clientSecret = this.generateClientSecret();

      // 设置默认值
      const grantTypes = request.grantTypes || ['authorization_code', 'refresh_token'];
      const responseTypes = request.responseTypes || ['code'];
      const scopes = request.scopes || ['openid', 'profile', 'email'];

      // 创建客户端
      const client = await prisma.oAuthClient.create({
        data: {
          id: uuidv4(),
          applicationId: request.applicationId,
          clientId,
          clientSecret,
          name: request.name,
          description: request.description,
          redirectUris: request.redirectUris,
          grantTypes,
          responseTypes,
          scopes,
          tokenEndpointAuthMethod: request.tokenEndpointAuthMethod || 'client_secret_basic',
          requirePkce: request.requirePkce || false,
          requireConsent: request.requireConsent !== false, // 默认为true
          accessTokenLifetime: request.accessTokenLifetime || 3600,
          refreshTokenLifetime: request.refreshTokenLifetime || 2592000,
          idTokenLifetime: request.idTokenLifetime || 3600,
          logoUri: request.logoUri,
          clientUri: request.clientUri,
          policyUri: request.policyUri,
          tosUri: request.tosUri,
          isActive: true
        }
      });

      // 记录审计日志
      logAuditEvent(
        'oidc_client_created',
        'system',
        null,
        {
          clientId,
          name: request.name,
          grantTypes,
          responseTypes,
          scopes
        }
      );

      logger.info('OIDC客户端创建成功', {
        clientId,
        name: request.name,
        grantTypes,
        scopes
      });

      return this.mapToClientResponse(client);

    } catch (error) {
      logger.error('OIDC客户端创建失败', {
        error: error instanceof Error ? error.message : String(error),
        name: request.name
      });
      throw error;
    }
  }

  /**
   * 获取客户端信息
   */
  async getClient(clientId: string): Promise<ClientResponse | null> {
    try {
      const client = await prisma.oAuthClient.findUnique({
        where: { clientId }
      });

      if (!client) {
        return null;
      }

      return this.mapToClientResponse(client);

    } catch (error) {
      logger.error('获取OIDC客户端失败', {
        error: error instanceof Error ? error.message : String(error),
        clientId
      });
      throw error;
    }
  }

  /**
   * 更新客户端信息
   */
  async updateClient(
    clientId: string,
    updates: Partial<CreateClientRequest>
  ): Promise<ClientResponse> {
    try {
      // 验证客户端存在
      const existingClient = await prisma.oAuthClient.findUnique({
        where: { clientId }
      });

      if (!existingClient) {
        throw new Error('客户端不存在');
      }

      // 验证更新的信息
      if (updates.redirectUris || updates.grantTypes || updates.responseTypes) {
        const validation = validateClientInfo({
          clientId,
          redirectUris: updates.redirectUris || existingClient.redirectUris as string[],
          grantTypes: updates.grantTypes || existingClient.grantTypes as string[],
          responseTypes: updates.responseTypes || existingClient.responseTypes as string[]
        });

        if (!validation.isValid) {
          throw new Error(`客户端信息验证失败: ${validation.errors.join(', ')}`);
        }
      }

      // 更新客户端
      const updatedClient = await prisma.oAuthClient.update({
        where: { clientId },
        data: {
          ...(updates.name && { name: updates.name }),
          ...(updates.description !== undefined && { description: updates.description }),
          ...(updates.redirectUris && { redirectUris: updates.redirectUris }),
          ...(updates.grantTypes && { grantTypes: updates.grantTypes }),
          ...(updates.responseTypes && { responseTypes: updates.responseTypes }),
          ...(updates.scopes && { scopes: updates.scopes }),
          ...(updates.tokenEndpointAuthMethod && { tokenEndpointAuthMethod: updates.tokenEndpointAuthMethod }),
          ...(updates.requirePkce !== undefined && { requirePkce: updates.requirePkce }),
          ...(updates.requireConsent !== undefined && { requireConsent: updates.requireConsent }),
          ...(updates.accessTokenLifetime && { accessTokenLifetime: updates.accessTokenLifetime }),
          ...(updates.refreshTokenLifetime && { refreshTokenLifetime: updates.refreshTokenLifetime }),
          ...(updates.idTokenLifetime && { idTokenLifetime: updates.idTokenLifetime }),
          ...(updates.logoUri !== undefined && { logoUri: updates.logoUri }),
          ...(updates.clientUri !== undefined && { clientUri: updates.clientUri }),
          ...(updates.policyUri !== undefined && { policyUri: updates.policyUri }),
          ...(updates.tosUri !== undefined && { tosUri: updates.tosUri })
        }
      });

      // 记录审计日志
      logAuditEvent(
        'oidc_client_updated',
        'system',
        null,
        {
          clientId,
          updates: Object.keys(updates)
        }
      );

      logger.info('OIDC客户端更新成功', {
        clientId,
        updates: Object.keys(updates)
      });

      return this.mapToClientResponse(updatedClient);

    } catch (error) {
      logger.error('OIDC客户端更新失败', {
        error: error instanceof Error ? error.message : String(error),
        clientId
      });
      throw error;
    }
  }

  /**
   * 删除客户端
   */
  async deleteClient(clientId: string): Promise<void> {
    try {
      const client = await prisma.oAuthClient.findUnique({
        where: { clientId }
      });

      if (!client) {
        throw new Error('客户端不存在');
      }

      // 删除客户端（级联删除相关的授权码）
      await prisma.oAuthClient.delete({
        where: { clientId }
      });

      // 记录审计日志
      logAuditEvent(
        'oidc_client_deleted',
        'system',
        null,
        {
          clientId,
          name: client.name
        }
      );

      logger.info('OIDC客户端删除成功', {
        clientId,
        name: client.name
      });

    } catch (error) {
      logger.error('OIDC客户端删除失败', {
        error: error instanceof Error ? error.message : String(error),
        clientId
      });
      throw error;
    }
  }

  /**
   * 重新生成客户端密钥
   */
  async regenerateClientSecret(clientId: string): Promise<string> {
    try {
      const client = await prisma.oAuthClient.findUnique({
        where: { clientId }
      });

      if (!client) {
        throw new Error('客户端不存在');
      }

      const newClientSecret = this.generateClientSecret();

      await prisma.oAuthClient.update({
        where: { clientId },
        data: { clientSecret: newClientSecret }
      });

      // 记录审计日志
      logAuditEvent(
        'oidc_client_secret_regenerated',
        'system',
        null,
        {
          clientId,
          name: client.name
        }
      );

      logger.info('OIDC客户端密钥重新生成', {
        clientId,
        name: client.name
      });

      return newClientSecret;

    } catch (error) {
      logger.error('OIDC客户端密钥重新生成失败', {
        error: error instanceof Error ? error.message : String(error),
        clientId
      });
      throw error;
    }
  }

  /**
   * 列出所有客户端
   */
  async listClients(options: {
    applicationId?: string;
    isActive?: boolean;
    limit?: number;
    offset?: number;
  } = {}): Promise<{
    clients: ClientResponse[];
    total: number;
  }> {
    try {
      const where: any = {};
      
      if (options.applicationId) {
        where.applicationId = options.applicationId;
      }
      
      if (options.isActive !== undefined) {
        where.isActive = options.isActive;
      }

      const [clients, total] = await Promise.all([
        prisma.oAuthClient.findMany({
          where,
          take: options.limit || 50,
          skip: options.offset || 0,
          orderBy: { createdAt: 'desc' }
        }),
        prisma.oAuthClient.count({ where })
      ]);

      return {
        clients: clients.map(client => this.mapToClientResponse(client)),
        total
      };

    } catch (error) {
      logger.error('列出OIDC客户端失败', {
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * 生成客户端ID
   */
  private generateClientId(): string {
    return `client_${crypto.randomBytes(16).toString('hex')}`;
  }

  /**
   * 生成客户端密钥
   */
  private generateClientSecret(): string {
    return crypto.randomBytes(32).toString('base64url');
  }

  /**
   * 映射数据库模型到响应对象
   */
  private mapToClientResponse(client: any): ClientResponse {
    return {
      id: client.id,
      clientId: client.clientId,
      clientSecret: client.clientSecret,
      name: client.name,
      description: client.description,
      redirectUris: client.redirectUris as string[],
      grantTypes: client.grantTypes as string[],
      responseTypes: client.responseTypes as string[],
      scopes: client.scopes as string[],
      tokenEndpointAuthMethod: client.tokenEndpointAuthMethod,
      requirePkce: client.requirePkce,
      requireConsent: client.requireConsent,
      accessTokenLifetime: client.accessTokenLifetime,
      refreshTokenLifetime: client.refreshTokenLifetime,
      idTokenLifetime: client.idTokenLifetime,
      logoUri: client.logoUri,
      clientUri: client.clientUri,
      policyUri: client.policyUri,
      tosUri: client.tosUri,
      isActive: client.isActive,
      createdAt: client.createdAt,
      updatedAt: client.updatedAt
    };
  }
}

// 创建单例实例
export const oidcClientService = new OIDCClientService();
