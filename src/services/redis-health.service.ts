/**
 * Redis健康检查服务
 * 监控Redis连接状态和性能指标
 */

import { redisService } from './redis.service';
import { logger } from '@/config/logger';
import { EventEmitter } from 'events';

/**
 * Redis健康状态
 */
export enum RedisHealthStatus {
  HEALTHY = 'healthy',
  DEGRADED = 'degraded',
  UNHEALTHY = 'unhealthy',
  UNKNOWN = 'unknown'
}

/**
 * Redis性能指标
 */
export interface RedisMetrics {
  // 连接信息
  connectedClients: number;
  blockedClients: number;
  totalConnectionsReceived: number;
  
  // 内存信息
  usedMemory: number;
  usedMemoryHuman: string;
  usedMemoryRss: number;
  usedMemoryPeak: number;
  maxMemory: number;
  memoryFragmentationRatio: number;
  
  // 性能信息
  totalCommandsProcessed: number;
  instantaneousOpsPerSec: number;
  instantaneousInputKbps: number;
  instantaneousOutputKbps: number;
  
  // 持久化信息
  rdbChangesSinceLastSave: number;
  rdbLastSaveTime: number;
  aofCurrentSize: number;
  aofBaseSize: number;
  
  // 键空间信息
  totalKeys: number;
  expiredKeys: number;
  evictedKeys: number;
  
  // 网络信息
  totalNetInputBytes: number;
  totalNetOutputBytes: number;
  
  // 复制信息
  role: string;
  connectedSlaves: number;
  
  // 服务器信息
  uptimeInSeconds: number;
  redisVersion: string;
  redisMode: string;
  
  // 自定义指标
  responseTime: number;
  errorRate: number;
  hitRate: number;
}

/**
 * 健康检查结果
 */
export interface HealthCheckResult {
  status: RedisHealthStatus;
  timestamp: Date;
  metrics: RedisMetrics;
  issues: string[];
  recommendations: string[];
}

/**
 * 健康检查配置
 */
export interface HealthCheckConfig {
  checkInterval: number;           // 检查间隔（毫秒）
  responseTimeThreshold: number;   // 响应时间阈值（毫秒）
  memoryUsageThreshold: number;    // 内存使用阈值（百分比）
  errorRateThreshold: number;      // 错误率阈值（百分比）
  hitRateThreshold: number;        // 命中率阈值（百分比）
  enableAlerts: boolean;           // 是否启用告警
  alertCooldown: number;           // 告警冷却时间（毫秒）
}

/**
 * Redis健康检查服务
 */
export class RedisHealthService extends EventEmitter {
  private config: HealthCheckConfig;
  private checkTimer?: NodeJS.Timeout;
  private lastAlertTime = 0;
  private healthHistory: HealthCheckResult[] = [];
  private maxHistorySize = 100;

  // 性能计数器
  private commandCount = 0;
  private errorCount = 0;
  private hitCount = 0;
  private missCount = 0;
  private lastResetTime = Date.now();

  constructor(config: Partial<HealthCheckConfig> = {}) {
    super();
    
    this.config = {
      checkInterval: 30000,           // 30秒
      responseTimeThreshold: 100,     // 100毫秒
      memoryUsageThreshold: 80,       // 80%
      errorRateThreshold: 5,          // 5%
      hitRateThreshold: 70,           // 70%
      enableAlerts: true,
      alertCooldown: 300000,          // 5分钟
      ...config
    };
  }

  /**
   * 启动健康检查
   */
  start(): void {
    if (this.checkTimer) {
      return;
    }

    this.checkTimer = setInterval(async () => {
      await this.performHealthCheck();
    }, this.config.checkInterval);

    logger.info('Redis健康检查服务已启动', {
      checkInterval: this.config.checkInterval / 1000 + '秒'
    });

    // 立即执行一次检查
    setImmediate(() => this.performHealthCheck());
  }

  /**
   * 停止健康检查
   */
  stop(): void {
    if (this.checkTimer) {
      clearInterval(this.checkTimer);
      this.checkTimer = undefined;
      logger.info('Redis健康检查服务已停止');
    }
  }

  /**
   * 执行健康检查
   */
  async performHealthCheck(): Promise<HealthCheckResult> {
    const startTime = Date.now();
    
    try {
      // 检查Redis连接
      if (!redisService.isReady()) {
        const result: HealthCheckResult = {
          status: RedisHealthStatus.UNHEALTHY,
          timestamp: new Date(),
          metrics: {} as RedisMetrics,
          issues: ['Redis连接不可用'],
          recommendations: ['检查Redis服务器状态', '验证网络连接']
        };
        
        this.addToHistory(result);
        this.emitHealthEvent(result);
        return result;
      }

      // 获取Redis信息
      const client = redisService.getClient();
      const info = await client.info();
      const responseTime = Date.now() - startTime;

      // 解析Redis信息
      const metrics = this.parseRedisInfo(info, responseTime);
      
      // 分析健康状态
      const analysis = this.analyzeHealth(metrics);
      
      const result: HealthCheckResult = {
        status: analysis.status,
        timestamp: new Date(),
        metrics,
        issues: analysis.issues,
        recommendations: analysis.recommendations
      };

      this.addToHistory(result);
      this.emitHealthEvent(result);
      
      return result;

    } catch (error) {
      logger.error('Redis健康检查失败', { error: error.message });
      
      const result: HealthCheckResult = {
        status: RedisHealthStatus.UNHEALTHY,
        timestamp: new Date(),
        metrics: {} as RedisMetrics,
        issues: [`健康检查失败: ${error.message}`],
        recommendations: ['检查Redis服务器状态', '查看错误日志']
      };
      
      this.addToHistory(result);
      this.emitHealthEvent(result);
      return result;
    }
  }

  /**
   * 解析Redis信息
   */
  private parseRedisInfo(info: string, responseTime: number): RedisMetrics {
    const lines = info.split('\r\n');
    const data: Record<string, string> = {};
    
    for (const line of lines) {
      if (line.includes(':')) {
        const [key, value] = line.split(':');
        data[key] = value;
      }
    }

    // 计算自定义指标
    const errorRate = this.commandCount > 0 ? (this.errorCount / this.commandCount) * 100 : 0;
    const hitRate = (this.hitCount + this.missCount) > 0 ? (this.hitCount / (this.hitCount + this.missCount)) * 100 : 0;

    return {
      // 连接信息
      connectedClients: parseInt(data.connected_clients) || 0,
      blockedClients: parseInt(data.blocked_clients) || 0,
      totalConnectionsReceived: parseInt(data.total_connections_received) || 0,
      
      // 内存信息
      usedMemory: parseInt(data.used_memory) || 0,
      usedMemoryHuman: data.used_memory_human || '0B',
      usedMemoryRss: parseInt(data.used_memory_rss) || 0,
      usedMemoryPeak: parseInt(data.used_memory_peak) || 0,
      maxMemory: parseInt(data.maxmemory) || 0,
      memoryFragmentationRatio: parseFloat(data.mem_fragmentation_ratio) || 0,
      
      // 性能信息
      totalCommandsProcessed: parseInt(data.total_commands_processed) || 0,
      instantaneousOpsPerSec: parseInt(data.instantaneous_ops_per_sec) || 0,
      instantaneousInputKbps: parseFloat(data.instantaneous_input_kbps) || 0,
      instantaneousOutputKbps: parseFloat(data.instantaneous_output_kbps) || 0,
      
      // 持久化信息
      rdbChangesSinceLastSave: parseInt(data.rdb_changes_since_last_save) || 0,
      rdbLastSaveTime: parseInt(data.rdb_last_save_time) || 0,
      aofCurrentSize: parseInt(data.aof_current_size) || 0,
      aofBaseSize: parseInt(data.aof_base_size) || 0,
      
      // 键空间信息
      totalKeys: this.calculateTotalKeys(data),
      expiredKeys: parseInt(data.expired_keys) || 0,
      evictedKeys: parseInt(data.evicted_keys) || 0,
      
      // 网络信息
      totalNetInputBytes: parseInt(data.total_net_input_bytes) || 0,
      totalNetOutputBytes: parseInt(data.total_net_output_bytes) || 0,
      
      // 复制信息
      role: data.role || 'unknown',
      connectedSlaves: parseInt(data.connected_slaves) || 0,
      
      // 服务器信息
      uptimeInSeconds: parseInt(data.uptime_in_seconds) || 0,
      redisVersion: data.redis_version || 'unknown',
      redisMode: data.redis_mode || 'unknown',
      
      // 自定义指标
      responseTime,
      errorRate,
      hitRate
    };
  }

  /**
   * 计算总键数
   */
  private calculateTotalKeys(data: Record<string, string>): number {
    let totalKeys = 0;
    
    for (const key in data) {
      if (key.startsWith('db')) {
        const dbInfo = data[key];
        const match = dbInfo.match(/keys=(\d+)/);
        if (match) {
          totalKeys += parseInt(match[1]);
        }
      }
    }
    
    return totalKeys;
  }

  /**
   * 分析健康状态
   */
  private analyzeHealth(metrics: RedisMetrics): { 
    status: RedisHealthStatus; 
    issues: string[]; 
    recommendations: string[] 
  } {
    const issues: string[] = [];
    const recommendations: string[] = [];
    let status = RedisHealthStatus.HEALTHY;

    // 检查响应时间
    if (metrics.responseTime > this.config.responseTimeThreshold) {
      issues.push(`响应时间过高: ${metrics.responseTime}ms`);
      recommendations.push('检查网络延迟和Redis服务器负载');
      status = RedisHealthStatus.DEGRADED;
    }

    // 检查内存使用率
    if (metrics.maxMemory > 0) {
      const memoryUsage = (metrics.usedMemory / metrics.maxMemory) * 100;
      if (memoryUsage > this.config.memoryUsageThreshold) {
        issues.push(`内存使用率过高: ${memoryUsage.toFixed(1)}%`);
        recommendations.push('考虑增加内存或优化数据结构');
        status = RedisHealthStatus.DEGRADED;
      }
    }

    // 检查内存碎片率
    if (metrics.memoryFragmentationRatio > 1.5) {
      issues.push(`内存碎片率过高: ${metrics.memoryFragmentationRatio.toFixed(2)}`);
      recommendations.push('考虑重启Redis或使用MEMORY PURGE命令');
    }

    // 检查错误率
    if (metrics.errorRate > this.config.errorRateThreshold) {
      issues.push(`错误率过高: ${metrics.errorRate.toFixed(1)}%`);
      recommendations.push('检查应用程序逻辑和Redis配置');
      status = RedisHealthStatus.DEGRADED;
    }

    // 检查命中率
    if (metrics.hitRate < this.config.hitRateThreshold && metrics.hitRate > 0) {
      issues.push(`缓存命中率过低: ${metrics.hitRate.toFixed(1)}%`);
      recommendations.push('优化缓存策略和TTL设置');
    }

    // 检查连接数
    if (metrics.connectedClients > 1000) {
      issues.push(`连接数过多: ${metrics.connectedClients}`);
      recommendations.push('检查连接池配置和连接泄漏');
    }

    // 检查阻塞客户端
    if (metrics.blockedClients > 10) {
      issues.push(`阻塞客户端过多: ${metrics.blockedClients}`);
      recommendations.push('检查阻塞操作和超时设置');
    }

    // 根据问题数量确定最终状态
    if (issues.length === 0) {
      status = RedisHealthStatus.HEALTHY;
    } else if (issues.length <= 2) {
      status = RedisHealthStatus.DEGRADED;
    } else {
      status = RedisHealthStatus.UNHEALTHY;
    }

    return { status, issues, recommendations };
  }

  /**
   * 添加到历史记录
   */
  private addToHistory(result: HealthCheckResult): void {
    this.healthHistory.push(result);
    
    // 保持历史记录大小
    if (this.healthHistory.length > this.maxHistorySize) {
      this.healthHistory.shift();
    }
  }

  /**
   * 发出健康事件
   */
  private emitHealthEvent(result: HealthCheckResult): void {
    this.emit('healthCheck', result);
    
    // 发出状态变化事件
    if (this.healthHistory.length > 1) {
      const previousStatus = this.healthHistory[this.healthHistory.length - 2].status;
      if (previousStatus !== result.status) {
        this.emit('statusChange', {
          from: previousStatus,
          to: result.status,
          result
        });
      }
    }

    // 发出告警
    if (this.config.enableAlerts && this.shouldAlert(result)) {
      this.emit('alert', result);
      this.lastAlertTime = Date.now();
    }
  }

  /**
   * 判断是否应该发出告警
   */
  private shouldAlert(result: HealthCheckResult): boolean {
    // 检查冷却时间
    if (Date.now() - this.lastAlertTime < this.config.alertCooldown) {
      return false;
    }

    // 只对不健康状态发出告警
    return result.status === RedisHealthStatus.UNHEALTHY || 
           (result.status === RedisHealthStatus.DEGRADED && result.issues.length > 2);
  }

  /**
   * 获取当前健康状态
   */
  getCurrentHealth(): HealthCheckResult | null {
    return this.healthHistory.length > 0 ? this.healthHistory[this.healthHistory.length - 1] : null;
  }

  /**
   * 获取健康历史
   */
  getHealthHistory(limit?: number): HealthCheckResult[] {
    if (limit) {
      return this.healthHistory.slice(-limit);
    }
    return [...this.healthHistory];
  }

  /**
   * 获取健康趋势
   */
  getHealthTrend(minutes: number = 30): {
    avgResponseTime: number;
    avgMemoryUsage: number;
    avgErrorRate: number;
    avgHitRate: number;
    statusDistribution: Record<RedisHealthStatus, number>;
  } {
    const cutoffTime = new Date(Date.now() - minutes * 60 * 1000);
    const recentResults = this.healthHistory.filter(result => result.timestamp > cutoffTime);

    if (recentResults.length === 0) {
      return {
        avgResponseTime: 0,
        avgMemoryUsage: 0,
        avgErrorRate: 0,
        avgHitRate: 0,
        statusDistribution: {
          [RedisHealthStatus.HEALTHY]: 0,
          [RedisHealthStatus.DEGRADED]: 0,
          [RedisHealthStatus.UNHEALTHY]: 0,
          [RedisHealthStatus.UNKNOWN]: 0
        }
      };
    }

    const avgResponseTime = recentResults.reduce((sum, r) => sum + r.metrics.responseTime, 0) / recentResults.length;
    const avgMemoryUsage = recentResults.reduce((sum, r) => {
      return sum + (r.metrics.maxMemory > 0 ? (r.metrics.usedMemory / r.metrics.maxMemory) * 100 : 0);
    }, 0) / recentResults.length;
    const avgErrorRate = recentResults.reduce((sum, r) => sum + r.metrics.errorRate, 0) / recentResults.length;
    const avgHitRate = recentResults.reduce((sum, r) => sum + r.metrics.hitRate, 0) / recentResults.length;

    const statusDistribution = recentResults.reduce((dist, r) => {
      dist[r.status]++;
      return dist;
    }, {
      [RedisHealthStatus.HEALTHY]: 0,
      [RedisHealthStatus.DEGRADED]: 0,
      [RedisHealthStatus.UNHEALTHY]: 0,
      [RedisHealthStatus.UNKNOWN]: 0
    });

    return {
      avgResponseTime,
      avgMemoryUsage,
      avgErrorRate,
      avgHitRate,
      statusDistribution
    };
  }

  /**
   * 记录命令执行
   */
  recordCommand(success: boolean): void {
    this.commandCount++;
    if (!success) {
      this.errorCount++;
    }
  }

  /**
   * 记录缓存命中
   */
  recordCacheHit(): void {
    this.hitCount++;
  }

  /**
   * 记录缓存未命中
   */
  recordCacheMiss(): void {
    this.missCount++;
  }

  /**
   * 重置性能计数器
   */
  resetCounters(): void {
    this.commandCount = 0;
    this.errorCount = 0;
    this.hitCount = 0;
    this.missCount = 0;
    this.lastResetTime = Date.now();
  }

  /**
   * 清理资源
   */
  cleanup(): void {
    this.stop();
    this.removeAllListeners();
    this.healthHistory = [];
    logger.info('Redis健康检查服务清理完成');
  }
}

// 创建单例实例
export const redisHealthService = new RedisHealthService();
