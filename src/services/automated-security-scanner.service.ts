/**
 * 自动化安全扫描服务
 * 实现定时自动化安全漏洞扫描系统
 */

import { logger } from '@/config/logger';
import { securityScannerService } from './security-scanner.service';
import { prisma } from '@/config/database';
import { redisService } from './redis.service';
import cron from 'node-cron';

/**
 * 扫描计划配置
 */
interface ScanSchedule {
  id: string;
  name: string;
  cronExpression: string;
  scanType: 'full' | 'dependency' | 'configuration' | 'code' | 'infrastructure';
  enabled: boolean;
  lastRun?: Date;
  nextRun?: Date;
  notificationThreshold: 'critical' | 'high' | 'medium' | 'low';
}

/**
 * 扫描结果通知配置
 */
interface NotificationConfig {
  email: {
    enabled: boolean;
    recipients: string[];
    template: string;
  };
  webhook: {
    enabled: boolean;
    url: string;
    headers: Record<string, string>;
  };
  slack: {
    enabled: boolean;
    webhookUrl: string;
    channel: string;
  };
}

/**
 * 自动化安全扫描服务类
 */
export class AutomatedSecurityScannerService {
  private schedules: Map<string, ScanSchedule> = new Map();
  private cronJobs: Map<string, cron.ScheduledTask> = new Map();
  private notificationConfig: NotificationConfig;

  constructor() {
    this.notificationConfig = {
      email: {
        enabled: process.env.SECURITY_SCAN_EMAIL_ENABLED === 'true',
        recipients: (process.env.SECURITY_SCAN_EMAIL_RECIPIENTS || '').split(',').filter(Boolean),
        template: 'security-scan-report'
      },
      webhook: {
        enabled: process.env.SECURITY_SCAN_WEBHOOK_ENABLED === 'true',
        url: process.env.SECURITY_SCAN_WEBHOOK_URL || '',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${process.env.SECURITY_SCAN_WEBHOOK_TOKEN || ''}`
        }
      },
      slack: {
        enabled: process.env.SECURITY_SCAN_SLACK_ENABLED === 'true',
        webhookUrl: process.env.SECURITY_SCAN_SLACK_WEBHOOK_URL || '',
        channel: process.env.SECURITY_SCAN_SLACK_CHANNEL || '#security'
      }
    };

    this.initializeDefaultSchedules();
  }

  /**
   * 初始化默认扫描计划
   */
  private initializeDefaultSchedules(): void {
    const defaultSchedules: ScanSchedule[] = [
      {
        id: 'daily-full-scan',
        name: '每日全面安全扫描',
        cronExpression: '0 2 * * *', // 每天凌晨2点
        scanType: 'full',
        enabled: true,
        notificationThreshold: 'medium'
      },
      {
        id: 'hourly-dependency-scan',
        name: '每小时依赖项扫描',
        cronExpression: '0 * * * *', // 每小时
        scanType: 'dependency',
        enabled: true,
        notificationThreshold: 'high'
      },
      {
        id: 'weekly-infrastructure-scan',
        name: '每周基础设施扫描',
        cronExpression: '0 3 * * 0', // 每周日凌晨3点
        scanType: 'infrastructure',
        enabled: true,
        notificationThreshold: 'medium'
      },
      {
        id: 'daily-code-scan',
        name: '每日代码安全扫描',
        cronExpression: '0 4 * * *', // 每天凌晨4点
        scanType: 'code',
        enabled: true,
        notificationThreshold: 'medium'
      }
    ];

    defaultSchedules.forEach(schedule => {
      this.schedules.set(schedule.id, schedule);
    });
  }

  /**
   * 启动自动化扫描服务
   */
  async start(): Promise<void> {
    try {
      logger.info('启动自动化安全扫描服务');

      // 加载保存的扫描计划
      await this.loadSchedulesFromDatabase();

      // 启动所有启用的扫描计划
      for (const [id, schedule] of this.schedules) {
        if (schedule.enabled) {
          await this.startSchedule(id);
        }
      }

      // 启动监控任务
      this.startMonitoringTasks();

      logger.info('自动化安全扫描服务启动成功', {
        activeSchedules: Array.from(this.schedules.values()).filter(s => s.enabled).length,
        totalSchedules: this.schedules.size
      });

    } catch (error) {
      logger.error('启动自动化安全扫描服务失败', {
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * 停止自动化扫描服务
   */
  async stop(): Promise<void> {
    try {
      logger.info('停止自动化安全扫描服务');

      // 停止所有定时任务
      for (const [id, job] of this.cronJobs) {
        job.stop();
        job.destroy();
        this.cronJobs.delete(id);
      }

      logger.info('自动化安全扫描服务已停止');

    } catch (error) {
      logger.error('停止自动化安全扫描服务失败', {
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  /**
   * 启动指定的扫描计划
   */
  private async startSchedule(scheduleId: string): Promise<void> {
    const schedule = this.schedules.get(scheduleId);
    if (!schedule) {
      throw new Error(`扫描计划不存在: ${scheduleId}`);
    }

    try {
      // 停止现有的任务（如果存在）
      const existingJob = this.cronJobs.get(scheduleId);
      if (existingJob) {
        existingJob.stop();
        existingJob.destroy();
      }

      // 创建新的定时任务
      const job = cron.schedule(schedule.cronExpression, async () => {
        await this.executeScan(scheduleId);
      }, {
        scheduled: false,
        timezone: 'Asia/Shanghai'
      });

      // 启动任务
      job.start();
      this.cronJobs.set(scheduleId, job);

      // 更新下次运行时间
      schedule.nextRun = this.getNextRunTime(schedule.cronExpression);
      await this.saveScheduleToDatabase(schedule);

      logger.info('扫描计划已启动', {
        scheduleId,
        name: schedule.name,
        cronExpression: schedule.cronExpression,
        nextRun: schedule.nextRun
      });

    } catch (error) {
      logger.error('启动扫描计划失败', {
        scheduleId,
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * 执行扫描
   */
  private async executeScan(scheduleId: string): Promise<void> {
    const schedule = this.schedules.get(scheduleId);
    if (!schedule) {
      logger.error('扫描计划不存在', { scheduleId });
      return;
    }

    const startTime = new Date();
    logger.info('开始执行自动化安全扫描', {
      scheduleId,
      scanType: schedule.scanType,
      startTime
    });

    try {
      // 执行扫描
      let scanResult;
      switch (schedule.scanType) {
        case 'full':
          scanResult = await securityScannerService.performFullSecurityScan();
          break;
        case 'dependency':
          scanResult = await securityScannerService.scanDependencies();
          break;
        case 'configuration':
          scanResult = await securityScannerService.scanConfiguration();
          break;
        case 'code':
          scanResult = await securityScannerService.scanCode();
          break;
        case 'infrastructure':
          scanResult = await securityScannerService.scanInfrastructure();
          break;
        default:
          throw new Error(`不支持的扫描类型: ${schedule.scanType}`);
      }

      // 更新扫描计划状态
      schedule.lastRun = startTime;
      schedule.nextRun = this.getNextRunTime(schedule.cronExpression);
      await this.saveScheduleToDatabase(schedule);

      // 保存扫描结果到数据库
      await this.saveScanResultToDatabase(scheduleId, scanResult);

      // 检查是否需要发送通知
      await this.checkAndSendNotifications(schedule, scanResult);

      const endTime = new Date();
      const duration = endTime.getTime() - startTime.getTime();

      logger.info('自动化安全扫描完成', {
        scheduleId,
        scanType: schedule.scanType,
        duration,
        vulnerabilitiesFound: scanResult.vulnerabilities?.length || 0,
        status: scanResult.status
      });

    } catch (error) {
      logger.error('自动化安全扫描失败', {
        scheduleId,
        scanType: schedule.scanType,
        error: error instanceof Error ? error.message : String(error)
      });

      // 发送错误通知
      await this.sendErrorNotification(schedule, error);
    }
  }

  /**
   * 检查并发送通知
   */
  private async checkAndSendNotifications(schedule: ScanSchedule, scanResult: any): Promise<void> {
    if (!scanResult.vulnerabilities || scanResult.vulnerabilities.length === 0) {
      return;
    }

    // 根据通知阈值过滤漏洞
    const severityOrder = ['critical', 'high', 'medium', 'low', 'info'];
    const thresholdIndex = severityOrder.indexOf(schedule.notificationThreshold);
    
    const significantVulnerabilities = scanResult.vulnerabilities.filter((vuln: any) => {
      const vulnIndex = severityOrder.indexOf(vuln.severity);
      return vulnIndex <= thresholdIndex;
    });

    if (significantVulnerabilities.length === 0) {
      return;
    }

    // 发送通知
    await this.sendNotifications(schedule, scanResult, significantVulnerabilities);
  }

  /**
   * 发送通知
   */
  private async sendNotifications(schedule: ScanSchedule, scanResult: any, vulnerabilities: any[]): Promise<void> {
    const notificationData = {
      scheduleName: schedule.name,
      scanType: schedule.scanType,
      scanTime: new Date(),
      totalVulnerabilities: vulnerabilities.length,
      criticalCount: vulnerabilities.filter(v => v.severity === 'critical').length,
      highCount: vulnerabilities.filter(v => v.severity === 'high').length,
      mediumCount: vulnerabilities.filter(v => v.severity === 'medium').length,
      vulnerabilities: vulnerabilities.slice(0, 10) // 只包含前10个漏洞
    };

    // 发送邮件通知
    if (this.notificationConfig.email.enabled) {
      await this.sendEmailNotification(notificationData);
    }

    // 发送Webhook通知
    if (this.notificationConfig.webhook.enabled) {
      await this.sendWebhookNotification(notificationData);
    }

    // 发送Slack通知
    if (this.notificationConfig.slack.enabled) {
      await this.sendSlackNotification(notificationData);
    }
  }

  /**
   * 发送邮件通知
   */
  private async sendEmailNotification(data: any): Promise<void> {
    try {
      // TODO: 实现邮件发送逻辑
      logger.info('发送安全扫描邮件通知', {
        recipients: this.notificationConfig.email.recipients,
        vulnerabilities: data.totalVulnerabilities
      });
    } catch (error) {
      logger.error('发送邮件通知失败', {
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  /**
   * 发送Webhook通知
   */
  private async sendWebhookNotification(data: any): Promise<void> {
    try {
      const response = await fetch(this.notificationConfig.webhook.url, {
        method: 'POST',
        headers: this.notificationConfig.webhook.headers,
        body: JSON.stringify(data)
      });

      if (!response.ok) {
        throw new Error(`Webhook响应错误: ${response.status}`);
      }

      logger.info('发送Webhook通知成功', {
        url: this.notificationConfig.webhook.url,
        vulnerabilities: data.totalVulnerabilities
      });

    } catch (error) {
      logger.error('发送Webhook通知失败', {
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  /**
   * 发送Slack通知
   */
  private async sendSlackNotification(data: any): Promise<void> {
    try {
      const message = {
        channel: this.notificationConfig.slack.channel,
        text: `🚨 安全扫描发现 ${data.totalVulnerabilities} 个漏洞`,
        attachments: [
          {
            color: data.criticalCount > 0 ? 'danger' : data.highCount > 0 ? 'warning' : 'good',
            fields: [
              {
                title: '扫描类型',
                value: data.scanType,
                short: true
              },
              {
                title: '扫描时间',
                value: data.scanTime.toLocaleString('zh-CN'),
                short: true
              },
              {
                title: '严重漏洞',
                value: data.criticalCount.toString(),
                short: true
              },
              {
                title: '高危漏洞',
                value: data.highCount.toString(),
                short: true
              }
            ]
          }
        ]
      };

      const response = await fetch(this.notificationConfig.slack.webhookUrl, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(message)
      });

      if (!response.ok) {
        throw new Error(`Slack响应错误: ${response.status}`);
      }

      logger.info('发送Slack通知成功', {
        channel: this.notificationConfig.slack.channel,
        vulnerabilities: data.totalVulnerabilities
      });

    } catch (error) {
      logger.error('发送Slack通知失败', {
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  /**
   * 发送错误通知
   */
  private async sendErrorNotification(schedule: ScanSchedule, error: any): Promise<void> {
    const errorData = {
      scheduleName: schedule.name,
      scanType: schedule.scanType,
      errorTime: new Date(),
      errorMessage: error instanceof Error ? error.message : String(error)
    };

    // 发送Slack错误通知
    if (this.notificationConfig.slack.enabled) {
      try {
        const message = {
          channel: this.notificationConfig.slack.channel,
          text: `❌ 自动化安全扫描失败`,
          attachments: [
            {
              color: 'danger',
              fields: [
                {
                  title: '扫描计划',
                  value: errorData.scheduleName,
                  short: true
                },
                {
                  title: '错误时间',
                  value: errorData.errorTime.toLocaleString('zh-CN'),
                  short: true
                },
                {
                  title: '错误信息',
                  value: errorData.errorMessage,
                  short: false
                }
              ]
            }
          ]
        };

        await fetch(this.notificationConfig.slack.webhookUrl, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(message)
        });

      } catch (notificationError) {
        logger.error('发送错误通知失败', {
          error: notificationError instanceof Error ? notificationError.message : String(notificationError)
        });
      }
    }
  }

  /**
   * 获取下次运行时间
   */
  private getNextRunTime(cronExpression: string): Date {
    // 简化实现，实际应该使用cron解析库
    const now = new Date();
    return new Date(now.getTime() + 24 * 60 * 60 * 1000); // 临时返回24小时后
  }

  /**
   * 从数据库加载扫描计划
   */
  private async loadSchedulesFromDatabase(): Promise<void> {
    try {
      // TODO: 实现从数据库加载扫描计划的逻辑
      logger.info('从数据库加载扫描计划');
    } catch (error) {
      logger.error('从数据库加载扫描计划失败', {
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  /**
   * 保存扫描计划到数据库
   */
  private async saveScheduleToDatabase(schedule: ScanSchedule): Promise<void> {
    try {
      // TODO: 实现保存扫描计划到数据库的逻辑
      logger.debug('保存扫描计划到数据库', { scheduleId: schedule.id });
    } catch (error) {
      logger.error('保存扫描计划失败', {
        scheduleId: schedule.id,
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  /**
   * 保存扫描结果到数据库
   */
  private async saveScanResultToDatabase(scheduleId: string, scanResult: any): Promise<void> {
    try {
      // TODO: 实现保存扫描结果到数据库的逻辑
      logger.debug('保存扫描结果到数据库', { scheduleId });
    } catch (error) {
      logger.error('保存扫描结果失败', {
        scheduleId,
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  /**
   * 启动监控任务
   */
  private startMonitoringTasks(): void {
    // 每小时检查扫描计划状态
    cron.schedule('0 * * * *', async () => {
      await this.monitorScheduleHealth();
    });

    // 每天清理旧的扫描结果
    cron.schedule('0 1 * * *', async () => {
      await this.cleanupOldScanResults();
    });
  }

  /**
   * 监控扫描计划健康状态
   */
  private async monitorScheduleHealth(): Promise<void> {
    try {
      logger.debug('检查扫描计划健康状态');
      
      for (const [id, schedule] of this.schedules) {
        if (schedule.enabled && !this.cronJobs.has(id)) {
          logger.warn('发现未运行的扫描计划，尝试重启', { scheduleId: id });
          await this.startSchedule(id);
        }
      }

    } catch (error) {
      logger.error('监控扫描计划健康状态失败', {
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  /**
   * 清理旧的扫描结果
   */
  private async cleanupOldScanResults(): Promise<void> {
    try {
      logger.info('清理旧的扫描结果');
      
      const retentionDays = 30; // 保留30天的扫描结果
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - retentionDays);

      // TODO: 实现清理逻辑
      
      logger.info('扫描结果清理完成', { retentionDays });

    } catch (error) {
      logger.error('清理扫描结果失败', {
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  /**
   * 获取扫描计划列表
   */
  getSchedules(): ScanSchedule[] {
    return Array.from(this.schedules.values());
  }

  /**
   * 获取扫描计划详情
   */
  getSchedule(scheduleId: string): ScanSchedule | undefined {
    return this.schedules.get(scheduleId);
  }

  /**
   * 启用/禁用扫描计划
   */
  async toggleSchedule(scheduleId: string, enabled: boolean): Promise<void> {
    const schedule = this.schedules.get(scheduleId);
    if (!schedule) {
      throw new Error(`扫描计划不存在: ${scheduleId}`);
    }

    schedule.enabled = enabled;
    
    if (enabled) {
      await this.startSchedule(scheduleId);
    } else {
      const job = this.cronJobs.get(scheduleId);
      if (job) {
        job.stop();
        job.destroy();
        this.cronJobs.delete(scheduleId);
      }
    }

    await this.saveScheduleToDatabase(schedule);
    
    logger.info('扫描计划状态已更新', {
      scheduleId,
      enabled
    });
  }
}

// 创建服务实例
export const automatedSecurityScannerService = new AutomatedSecurityScannerService();
