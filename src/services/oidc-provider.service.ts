/**
 * OpenID Connect Provider 服务
 * 基于 node-oidc-provider 实现完整的 OIDC Provider 功能
 */

import { Provider } from 'oidc-provider';
import { config } from '@/config';
import { prisma } from '@/config/database';
import { logger } from '@/utils/logger';
import { getServerUrl } from '@/utils/url';
import { cacheService } from './cache.service';
import crypto from 'crypto';

/**
 * OIDC Provider 配置
 */
const oidcConfiguration = {
  // 客户端配置 - 从数据库动态加载
  clients: [],

  // 支持的功能
  features: {
    // 开发交互界面（生产环境应禁用）
    devInteractions: { enabled: false },
    
    // 客户端凭据授权
    clientCredentials: { enabled: true },
    
    // 设备授权流程
    deviceFlow: { enabled: true },
    
    // 令牌内省
    introspection: { enabled: true },
    
    // 令牌撤销
    revocation: { enabled: true },
    
    // 用户信息端点
    userinfo: { enabled: true },
    
    // JWT 用户信息
    jwtUserinfo: { enabled: true },
    
    // 推送授权请求
    pushedAuthorizationRequests: { enabled: true },
    
    // 资源指示器
    resourceIndicators: { enabled: true },
    
    // 请求对象
    requestObjects: { enabled: true },
    
    // 后端通道登出
    backchannelLogout: { enabled: true },
    
    // RP 发起的登出
    rpInitiatedLogout: { enabled: true },
    
    // 会话管理
    sessionManagement: { enabled: true },
    
    // DPoP (Demonstration of Proof-of-Possession)
    dPoP: { enabled: true },
    
    // FAPI (Financial-grade API)
    fapi: { enabled: false },
    
    // 加密
    encryption: { enabled: true },
    
    // JWT 响应模式
    jwtResponseModes: { enabled: true }
  },

  // 支持的响应类型
  responseTypes: [
    'code',
    'id_token',
    'token',
    'code id_token',
    'code token',
    'id_token token',
    'code id_token token'
  ],

  // 支持的授权类型
  grantTypes: [
    'authorization_code',
    'implicit',
    'refresh_token',
    'client_credentials',
    'urn:ietf:params:oauth:grant-type:device_code'
  ],

  // 支持的主题类型
  subjectTypes: ['public', 'pairwise'],

  // 支持的签名算法
  idTokenSigningAlgValues: ['RS256', 'RS384', 'RS512', 'ES256', 'ES384', 'ES512'],
  
  // 支持的加密算法
  idTokenEncryptionAlgValues: ['RSA-OAEP', 'RSA-OAEP-256', 'A128KW', 'A192KW', 'A256KW'],
  idTokenEncryptionEncValues: ['A128CBC-HS256', 'A192CBC-HS384', 'A256CBC-HS512', 'A128GCM', 'A192GCM', 'A256GCM'],

  // 支持的客户端认证方法
  tokenEndpointAuthMethods: [
    'client_secret_basic',
    'client_secret_post',
    'client_secret_jwt',
    'private_key_jwt',
    'none'
  ],

  // 支持的 PKCE 代码挑战方法
  pkceMethods: ['S256'],

  // 声明配置
  claims: {
    openid: ['sub'],
    profile: ['name', 'family_name', 'given_name', 'middle_name', 'nickname', 'preferred_username', 'profile', 'picture', 'website', 'gender', 'birthdate', 'zoneinfo', 'locale', 'updated_at'],
    email: ['email', 'email_verified'],
    phone: ['phone_number', 'phone_number_verified'],
    address: ['address']
  },

  // 支持的声明
  claimsSupported: [
    'sub', 'iss', 'aud', 'exp', 'iat', 'auth_time', 'nonce', 'acr', 'amr', 'azp',
    'name', 'family_name', 'given_name', 'middle_name', 'nickname', 'preferred_username',
    'profile', 'picture', 'website', 'gender', 'birthdate', 'zoneinfo', 'locale', 'updated_at',
    'email', 'email_verified', 'phone_number', 'phone_number_verified', 'address'
  ],

  // 支持的范围
  scopes: ['openid', 'profile', 'email', 'phone', 'address', 'offline_access'],

  // TTL 配置
  ttl: {
    AccessToken: 15 * 60, // 15 分钟
    AuthorizationCode: 10 * 60, // 10 分钟
    IdToken: 60 * 60, // 1 小时
    DeviceCode: 10 * 60, // 10 分钟
    RefreshToken: 7 * 24 * 60 * 60, // 7 天
    ClientCredentials: 60 * 60, // 1 小时
    BackchannelAuthenticationRequest: 10 * 60, // 10 分钟
    PushedAuthorizationRequest: 10 * 60 // 10 分钟
  },

  // 路由配置
  routes: {
    authorization: '/oauth2/authorize',
    token: '/oauth2/token',
    userinfo: '/oauth2/userinfo',
    introspection: '/oauth2/introspect',
    revocation: '/oauth2/revoke',
    end_session: '/oauth2/logout',
    jwks: '/.well-known/jwks.json',
    device_authorization: '/oauth2/device/auth',
    code_verification: '/oauth2/device',
    pushed_authorization_request: '/oauth2/par'
  },

  // 交互配置
  interactions: {
    url(ctx: any, interaction: any) {
      return `/interaction/${interaction.uid}`;
    }
  },

  // 查找账户函数
  async findAccount(ctx: any, id: string) {
    try {
      const user = await prisma.user.findUnique({
        where: { id },
        include: {
          profile: true
        }
      });

      if (!user) {
        return undefined;
      }

      return {
        accountId: id,
        async claims(use: string, scope: string, claims: any, rejected: any) {
          const userClaims: any = {
            sub: user.id,
            email: user.email,
            email_verified: user.emailVerified,
            name: user.profile?.displayName || user.username,
            preferred_username: user.username,
            updated_at: Math.floor(user.updatedAt.getTime() / 1000)
          };

          // 根据范围添加声明
          if (scope.includes('profile')) {
            Object.assign(userClaims, {
              name: user.profile?.displayName || user.username,
              family_name: user.profile?.lastName,
              given_name: user.profile?.firstName,
              nickname: user.profile?.nickname,
              picture: user.profile?.avatar,
              website: user.profile?.website,
              gender: user.profile?.gender,
              birthdate: user.profile?.birthDate,
              zoneinfo: user.profile?.timezone,
              locale: user.profile?.locale
            });
          }

          if (scope.includes('phone')) {
            Object.assign(userClaims, {
              phone_number: user.profile?.phoneNumber,
              phone_number_verified: user.profile?.phoneVerified
            });
          }

          if (scope.includes('address')) {
            Object.assign(userClaims, {
              address: {
                formatted: user.profile?.address,
                street_address: user.profile?.streetAddress,
                locality: user.profile?.city,
                region: user.profile?.state,
                postal_code: user.profile?.postalCode,
                country: user.profile?.country
              }
            });
          }

          return userClaims;
        }
      };
    } catch (error) {
      logger.error('查找账户失败', { error, accountId: id });
      return undefined;
    }
  }
};

export class OIDCProviderService {
  private provider: Provider;
  private initialized = false;

  constructor() {
    const issuer = getServerUrl();
    this.provider = new Provider(issuer, oidcConfiguration);
    this.setupEventListeners();
  }

  /**
   * 初始化 OIDC Provider
   */
  async initialize(): Promise<void> {
    try {
      if (this.initialized) {
        return;
      }

      // 加载客户端配置
      await this.loadClients();

      // 生成或加载 JWKS
      await this.setupJWKS();

      this.initialized = true;
      logger.info('OIDC Provider 初始化成功');

    } catch (error) {
      logger.error('OIDC Provider 初始化失败', { error });
      throw error;
    }
  }

  /**
   * 获取 Provider 实例
   */
  getProvider(): Provider {
    if (!this.initialized) {
      throw new Error('OIDC Provider 未初始化');
    }
    return this.provider;
  }

  /**
   * 加载客户端配置
   */
  private async loadClients(): Promise<void> {
    try {
      const applications = await prisma.application.findMany({
        where: {
          isActive: true,
          supportedProtocols: {
            has: 'oidc'
          }
        }
      });

      const clients = applications.map(app => ({
        client_id: app.clientId,
        client_secret: app.clientSecret,
        redirect_uris: app.redirectUris || [],
        response_types: app.responseTypes || ['code'],
        grant_types: app.grantTypes || ['authorization_code'],
        scope: (app.scopes || ['openid']).join(' '),
        token_endpoint_auth_method: app.tokenEndpointAuthMethod || 'client_secret_basic',
        id_token_signed_response_alg: app.idTokenSignedResponseAlg || 'RS256',
        userinfo_signed_response_alg: app.userinfoSignedResponseAlg,
        application_type: app.applicationType || 'web',
        subject_type: app.subjectType || 'public',
        require_auth_time: app.requireAuthTime || false,
        default_max_age: app.defaultMaxAge,
        default_acr_values: app.defaultAcrValues,
        initiate_login_uri: app.initiateLoginUri,
        request_uris: app.requestUris,
        post_logout_redirect_uris: app.postLogoutRedirectUris || [],
        backchannel_logout_uri: app.backchannelLogoutUri,
        backchannel_logout_session_required: app.backchannelLogoutSessionRequired || false
      }));

      // 更新 provider 配置
      (this.provider as any).Client.cacheClear();

      logger.info('客户端配置加载成功', { count: clients.length });

    } catch (error) {
      logger.error('加载客户端配置失败', { error });
      throw error;
    }
  }

  /**
   * 设置 JWKS
   */
  private async setupJWKS(): Promise<void> {
    try {
      // 检查是否已有 JWKS
      let jwks = await cacheService.get('oidc:jwks');

      if (!jwks) {
        // 生成新的 JWKS
        jwks = await this.generateJWKS();
        await cacheService.set('oidc:jwks', jwks, 24 * 60 * 60); // 缓存 24 小时
      }

      // 设置 JWKS
      (this.provider as any).jwks = JSON.parse(jwks);

      logger.info('JWKS 设置成功');

    } catch (error) {
      logger.error('JWKS 设置失败', { error });
      throw error;
    }
  }

  /**
   * 生成 JWKS
   */
  private async generateJWKS(): Promise<string> {
    const { generateKeyPair } = await import('jose');

    // 生成 RS256 密钥对
    const { publicKey, privateKey } = await generateKeyPair('RS256', {
      modulusLength: 2048
    });

    // 导出公钥为 JWK
    const { exportJWK } = await import('jose');
    const publicJwk = await exportJWK(publicKey);
    const privateJwk = await exportJWK(privateKey);

    // 添加密钥 ID
    const kid = crypto.randomBytes(16).toString('hex');
    publicJwk.kid = kid;
    privateJwk.kid = kid;
    publicJwk.use = 'sig';
    privateJwk.use = 'sig';
    publicJwk.alg = 'RS256';
    privateJwk.alg = 'RS256';

    const jwks = {
      keys: [publicJwk]
    };

    // 保存私钥用于签名
    await cacheService.set(`oidc:private_key:${kid}`, JSON.stringify(privateJwk), 24 * 60 * 60);

    return JSON.stringify(jwks);
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    // 授权成功事件
    this.provider.on('authorization.success', (ctx) => {
      logger.info('OIDC 授权成功', {
        clientId: ctx.oidc.client?.clientId,
        accountId: ctx.oidc.account?.accountId,
        scopes: ctx.oidc.params?.scope
      });
    });

    // 授权错误事件
    this.provider.on('authorization.error', (ctx, error) => {
      logger.error('OIDC 授权失败', {
        error: error.message,
        clientId: ctx.oidc.client?.clientId,
        params: ctx.oidc.params
      });
    });

    // 令牌颁发事件
    this.provider.on('grant.success', (ctx) => {
      logger.info('OIDC 令牌颁发成功', {
        clientId: ctx.oidc.client?.clientId,
        accountId: ctx.oidc.account?.accountId,
        grantType: ctx.oidc.params?.grant_type
      });
    });

    // 令牌颁发错误事件
    this.provider.on('grant.error', (ctx, error) => {
      logger.error('OIDC 令牌颁发失败', {
        error: error.message,
        clientId: ctx.oidc.client?.clientId,
        grantType: ctx.oidc.params?.grant_type
      });
    });

    // 用户信息请求事件
    this.provider.on('userinfo.success', (ctx) => {
      logger.info('OIDC 用户信息请求成功', {
        clientId: ctx.oidc.client?.clientId,
        accountId: ctx.oidc.account?.accountId
      });
    });

    // 内省请求事件
    this.provider.on('introspection.success', (ctx) => {
      logger.info('OIDC 令牌内省成功', {
        clientId: ctx.oidc.client?.clientId
      });
    });

    // 撤销请求事件
    this.provider.on('revocation.success', (ctx) => {
      logger.info('OIDC 令牌撤销成功', {
        clientId: ctx.oidc.client?.clientId
      });
    });
  }

  /**
   * 重新加载客户端配置
   */
  async reloadClients(): Promise<void> {
    await this.loadClients();
  }

  /**
   * 获取发现文档
   */
  getDiscoveryDocument(): any {
    return this.provider.issuer;
  }

  /**
   * 获取 JWKS
   */
  async getJWKS(): Promise<any> {
    const jwks = await cacheService.get('oidc:jwks');
    return jwks ? JSON.parse(jwks) : { keys: [] };
  }
}

// 导出单例实例
export const oidcProviderService = new OIDCProviderService();
