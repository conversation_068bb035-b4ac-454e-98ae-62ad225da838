/**
 * 实时分析服务
 * 
 * 功能说明：
 * 1. 实时数据流处理
 * 2. 实时指标计算
 * 3. 异常检测和告警
 * 4. 实时仪表板数据推送
 * 5. 流式数据聚合
 */

import { PrismaClient } from '@prisma/client';
import Redis from 'ioredis';
import { EventEmitter } from 'events';
import { Logger } from 'winston';
import WebSocket from 'ws';

/**
 * 实时事件接口
 */
interface RealTimeEvent {
  id: string;
  type: string;
  userId?: string;
  sessionId?: string;
  timestamp: Date;
  data: Record<string, any>;
  metadata: {
    ip: string;
    userAgent: string;
    country?: string;
    city?: string;
  };
}

/**
 * 实时指标接口
 */
interface RealTimeMetric {
  name: string;
  value: number;
  timestamp: Date;
  dimensions: Record<string, string>;
  type: 'counter' | 'gauge' | 'histogram';
}

/**
 * 异常检测结果接口
 */
interface AnomalyDetection {
  id: string;
  metricName: string;
  anomalyType: 'spike' | 'drop' | 'trend_change';
  severity: 'low' | 'medium' | 'high' | 'critical';
  detectedValue: number;
  expectedValue: number;
  confidenceScore: number;
  detectionAlgorithm: string;
  contextData: Record<string, any>;
  detectedAt: Date;
}

/**
 * 实时分析服务类
 */
export class RealTimeAnalytics extends EventEmitter {
  private prisma: PrismaClient;
  private redis: Redis;
  private logger: Logger;
  private wsServer?: WebSocket.Server;
  private connectedClients: Set<WebSocket> = new Set();
  
  // 滑动窗口配置
  private readonly WINDOW_SIZES = {
    '1m': 60 * 1000,
    '5m': 5 * 60 * 1000,
    '15m': 15 * 60 * 1000,
    '1h': 60 * 60 * 1000,
    '24h': 24 * 60 * 60 * 1000
  };

  // 异常检测阈值
  private readonly ANOMALY_THRESHOLDS = {
    spike: 2.5, // 标准差倍数
    drop: -2.5,
    trend_change: 0.8 // 相关系数阈值
  };

  constructor(
    prisma: PrismaClient,
    redis: Redis,
    logger: Logger
  ) {
    super();
    this.prisma = prisma;
    this.redis = redis;
    this.logger = logger;
    
    this.initializeEventProcessing();
    this.initializeAnomalyDetection();
  }

  /**
   * 初始化WebSocket服务器
   */
  initializeWebSocketServer(port: number = 8080) {
    this.wsServer = new WebSocket.Server({ port });
    
    this.wsServer.on('connection', (ws) => {
      this.connectedClients.add(ws);
      this.logger.info('新的WebSocket连接建立');
      
      ws.on('close', () => {
        this.connectedClients.delete(ws);
        this.logger.info('WebSocket连接关闭');
      });
      
      ws.on('error', (error) => {
        this.logger.error('WebSocket错误', { error });
        this.connectedClients.delete(ws);
      });
    });
    
    this.logger.info(`实时分析WebSocket服务器启动，端口: ${port}`);
  }

  /**
   * 处理实时事件
   */
  async processEvent(event: RealTimeEvent): Promise<void> {
    try {
      // 存储原始事件
      await this.storeEvent(event);
      
      // 更新实时指标
      await this.updateRealTimeMetrics(event);
      
      // 检测异常
      await this.detectAnomalies(event);
      
      // 推送到客户端
      this.broadcastEvent(event);
      
      this.logger.debug('实时事件处理完成', { eventId: event.id, type: event.type });
      
    } catch (error) {
      this.logger.error('处理实时事件失败', { error, event });
    }
  }

  /**
   * 获取实时指标
   */
  async getRealTimeMetrics(
    metricNames: string[],
    timeWindow: keyof typeof this.WINDOW_SIZES = '5m'
  ): Promise<RealTimeMetric[]> {
    const windowMs = this.WINDOW_SIZES[timeWindow];
    const startTime = new Date(Date.now() - windowMs);
    
    try {
      const metrics = await this.prisma.realTimeMetrics.findMany({
        where: {
          metricName: { in: metricNames },
          timestamp: { gte: startTime }
        },
        orderBy: { timestamp: 'desc' }
      });
      
      return metrics.map(metric => ({
        name: metric.metricName,
        value: metric.metricValue.toNumber(),
        timestamp: metric.timestamp,
        dimensions: metric.dimensions as Record<string, string>,
        type: metric.metricType as 'counter' | 'gauge' | 'histogram'
      }));
      
    } catch (error) {
      this.logger.error('获取实时指标失败', { error, metricNames, timeWindow });
      return [];
    }
  }

  /**
   * 获取实时统计数据
   */
  async getRealTimeStats(): Promise<Record<string, any>> {
    try {
      const now = new Date();
      const oneHourAgo = new Date(now.getTime() - this.WINDOW_SIZES['1h']);
      const oneDayAgo = new Date(now.getTime() - this.WINDOW_SIZES['24h']);
      
      // 并行查询多个统计数据
      const [
        activeUsers,
        totalSessions,
        securityEvents,
        avgRiskScore,
        topCountries,
        deviceTypes
      ] = await Promise.all([
        this.getActiveUsersCount(oneHourAgo),
        this.getSessionsCount(oneHourAgo),
        this.getSecurityEventsCount(oneHourAgo),
        this.getAverageRiskScore(oneHourAgo),
        this.getTopCountries(oneDayAgo),
        this.getDeviceTypeDistribution(oneDayAgo)
      ]);
      
      return {
        activeUsers,
        totalSessions,
        securityEvents,
        avgRiskScore,
        topCountries,
        deviceTypes,
        timestamp: now
      };
      
    } catch (error) {
      this.logger.error('获取实时统计数据失败', { error });
      return {};
    }
  }

  /**
   * 订阅实时更新
   */
  subscribeToUpdates(callback: (data: any) => void): () => void {
    const eventHandler = (data: any) => callback(data);
    this.on('realtime-update', eventHandler);
    
    return () => {
      this.off('realtime-update', eventHandler);
    };
  }

  /**
   * 初始化事件处理
   */
  private initializeEventProcessing(): void {
    // 监听Redis事件流
    this.redis.subscribe('analytics:events');
    
    this.redis.on('message', async (channel, message) => {
      if (channel === 'analytics:events') {
        try {
          const event: RealTimeEvent = JSON.parse(message);
          await this.processEvent(event);
        } catch (error) {
          this.logger.error('处理Redis事件消息失败', { error, message });
        }
      }
    });
  }

  /**
   * 初始化异常检测
   */
  private initializeAnomalyDetection(): void {
    // 每分钟运行异常检测
    setInterval(async () => {
      await this.runAnomalyDetection();
    }, 60 * 1000);
  }

  /**
   * 存储事件
   */
  private async storeEvent(event: RealTimeEvent): Promise<void> {
    try {
      await this.prisma.userBehaviorEvents.create({
        data: {
          userId: event.userId,
          sessionId: event.sessionId,
          eventType: event.type,
          eventData: event.data,
          ipAddress: event.metadata.ip,
          userAgent: event.metadata.userAgent,
          country: event.metadata.country,
          city: event.metadata.city,
          timestamp: event.timestamp
        }
      });
    } catch (error) {
      this.logger.error('存储事件失败', { error, eventId: event.id });
    }
  }

  /**
   * 更新实时指标
   */
  private async updateRealTimeMetrics(event: RealTimeEvent): Promise<void> {
    const metrics: RealTimeMetric[] = [];
    
    // 基于事件类型生成指标
    switch (event.type) {
      case 'user_login':
        metrics.push({
          name: 'login_count',
          value: 1,
          timestamp: event.timestamp,
          dimensions: {
            country: event.metadata.country || 'unknown',
            device_type: this.extractDeviceType(event.metadata.userAgent)
          },
          type: 'counter'
        });
        break;
        
      case 'security_event':
        metrics.push({
          name: 'security_event_count',
          value: 1,
          timestamp: event.timestamp,
          dimensions: {
            severity: event.data.severity || 'unknown',
            type: event.data.eventType || 'unknown'
          },
          type: 'counter'
        });
        break;
        
      case 'api_call':
        metrics.push({
          name: 'api_call_count',
          value: 1,
          timestamp: event.timestamp,
          dimensions: {
            endpoint: event.data.endpoint || 'unknown',
            method: event.data.method || 'unknown',
            status: event.data.status || 'unknown'
          },
          type: 'counter'
        });
        
        if (event.data.responseTime) {
          metrics.push({
            name: 'api_response_time',
            value: event.data.responseTime,
            timestamp: event.timestamp,
            dimensions: {
              endpoint: event.data.endpoint || 'unknown'
            },
            type: 'histogram'
          });
        }
        break;
    }
    
    // 批量存储指标
    for (const metric of metrics) {
      await this.storeMetric(metric);
    }
  }

  /**
   * 存储指标
   */
  private async storeMetric(metric: RealTimeMetric): Promise<void> {
    try {
      await this.prisma.realTimeMetrics.create({
        data: {
          metricName: metric.name,
          metricValue: metric.value,
          metricType: metric.type,
          dimensions: metric.dimensions,
          timestamp: metric.timestamp
        }
      });
      
      // 同时更新Redis中的聚合数据
      await this.updateRedisAggregates(metric);
      
    } catch (error) {
      this.logger.error('存储指标失败', { error, metric });
    }
  }

  /**
   * 更新Redis聚合数据
   */
  private async updateRedisAggregates(metric: RealTimeMetric): Promise<void> {
    const pipeline = this.redis.pipeline();
    
    // 更新不同时间窗口的聚合数据
    for (const [window, windowMs] of Object.entries(this.WINDOW_SIZES)) {
      const key = `metrics:${metric.name}:${window}`;
      const timestamp = Math.floor(metric.timestamp.getTime() / windowMs) * windowMs;
      
      switch (metric.type) {
        case 'counter':
          pipeline.hincrby(key, timestamp.toString(), metric.value);
          break;
        case 'gauge':
          pipeline.hset(key, timestamp.toString(), metric.value);
          break;
        case 'histogram':
          // 存储直方图数据
          pipeline.lpush(`${key}:values:${timestamp}`, metric.value);
          pipeline.ltrim(`${key}:values:${timestamp}`, 0, 999); // 保留最近1000个值
          break;
      }
      
      // 设置过期时间
      pipeline.expire(key, windowMs / 1000 * 2); // 保留2倍窗口时间
    }
    
    await pipeline.exec();
  }

  /**
   * 检测异常
   */
  private async detectAnomalies(event: RealTimeEvent): Promise<void> {
    // 基于事件类型进行异常检测
    if (event.type === 'user_login') {
      await this.detectLoginAnomalies(event);
    } else if (event.type === 'security_event') {
      await this.detectSecurityAnomalies(event);
    }
  }

  /**
   * 检测登录异常
   */
  private async detectLoginAnomalies(event: RealTimeEvent): Promise<void> {
    try {
      // 检测登录频率异常
      const recentLogins = await this.getRecentLoginCount(event.metadata.ip, 5 * 60 * 1000); // 5分钟内
      
      if (recentLogins > 10) { // 5分钟内超过10次登录
        const anomaly: AnomalyDetection = {
          id: `anomaly_${Date.now()}_${Math.random()}`,
          metricName: 'login_frequency',
          anomalyType: 'spike',
          severity: 'high',
          detectedValue: recentLogins,
          expectedValue: 2,
          confidenceScore: 0.95,
          detectionAlgorithm: 'threshold_based',
          contextData: {
            ip: event.metadata.ip,
            timeWindow: '5m'
          },
          detectedAt: new Date()
        };
        
        await this.handleAnomaly(anomaly);
      }
      
    } catch (error) {
      this.logger.error('检测登录异常失败', { error, event });
    }
  }

  /**
   * 检测安全事件异常
   */
  private async detectSecurityAnomalies(event: RealTimeEvent): Promise<void> {
    try {
      const severity = event.data.severity;
      
      if (severity === 'critical' || severity === 'high') {
        const anomaly: AnomalyDetection = {
          id: `anomaly_${Date.now()}_${Math.random()}`,
          metricName: 'security_event',
          anomalyType: 'spike',
          severity: severity as 'high' | 'critical',
          detectedValue: 1,
          expectedValue: 0,
          confidenceScore: 1.0,
          detectionAlgorithm: 'rule_based',
          contextData: {
            eventType: event.data.eventType,
            userId: event.userId,
            ip: event.metadata.ip
          },
          detectedAt: new Date()
        };
        
        await this.handleAnomaly(anomaly);
      }
      
    } catch (error) {
      this.logger.error('检测安全事件异常失败', { error, event });
    }
  }

  /**
   * 运行异常检测
   */
  private async runAnomalyDetection(): Promise<void> {
    try {
      // 获取最近的指标数据进行异常检测
      const metrics = await this.getRealTimeMetrics([
        'login_count',
        'api_call_count',
        'security_event_count'
      ], '15m');
      
      // 对每个指标运行统计异常检测
      for (const metricName of ['login_count', 'api_call_count', 'security_event_count']) {
        const metricData = metrics.filter(m => m.name === metricName);
        if (metricData.length > 10) { // 需要足够的数据点
          await this.runStatisticalAnomalyDetection(metricName, metricData);
        }
      }
      
    } catch (error) {
      this.logger.error('运行异常检测失败', { error });
    }
  }

  /**
   * 统计异常检测
   */
  private async runStatisticalAnomalyDetection(
    metricName: string,
    data: RealTimeMetric[]
  ): Promise<void> {
    const values = data.map(d => d.value);
    const mean = values.reduce((a, b) => a + b, 0) / values.length;
    const variance = values.reduce((a, b) => a + Math.pow(b - mean, 2), 0) / values.length;
    const stdDev = Math.sqrt(variance);
    
    const latestValue = values[values.length - 1];
    const zScore = (latestValue - mean) / stdDev;
    
    // 检测异常
    if (Math.abs(zScore) > this.ANOMALY_THRESHOLDS.spike) {
      const anomaly: AnomalyDetection = {
        id: `anomaly_${Date.now()}_${Math.random()}`,
        metricName,
        anomalyType: zScore > 0 ? 'spike' : 'drop',
        severity: Math.abs(zScore) > 3 ? 'critical' : 'high',
        detectedValue: latestValue,
        expectedValue: mean,
        confidenceScore: Math.min(Math.abs(zScore) / 3, 1),
        detectionAlgorithm: 'z_score',
        contextData: {
          zScore,
          mean,
          stdDev,
          dataPoints: values.length
        },
        detectedAt: new Date()
      };
      
      await this.handleAnomaly(anomaly);
    }
  }

  /**
   * 处理异常
   */
  private async handleAnomaly(anomaly: AnomalyDetection): Promise<void> {
    try {
      // 存储异常记录
      await this.prisma.anomalyDetections.create({
        data: {
          metricName: anomaly.metricName,
          anomalyType: anomaly.anomalyType,
          severity: anomaly.severity,
          detectedValue: anomaly.detectedValue,
          expectedValue: anomaly.expectedValue,
          confidenceScore: anomaly.confidenceScore,
          detectionAlgorithm: anomaly.detectionAlgorithm,
          contextData: anomaly.contextData,
          detectedAt: anomaly.detectedAt
        }
      });
      
      // 发送告警
      this.emit('anomaly-detected', anomaly);
      
      // 推送到客户端
      this.broadcastAnomaly(anomaly);
      
      this.logger.warn('检测到异常', {
        metricName: anomaly.metricName,
        severity: anomaly.severity,
        detectedValue: anomaly.detectedValue
      });
      
    } catch (error) {
      this.logger.error('处理异常失败', { error, anomaly });
    }
  }

  /**
   * 广播事件到客户端
   */
  private broadcastEvent(event: RealTimeEvent): void {
    const message = JSON.stringify({
      type: 'event',
      data: event
    });
    
    this.connectedClients.forEach(client => {
      if (client.readyState === WebSocket.OPEN) {
        client.send(message);
      }
    });
  }

  /**
   * 广播异常到客户端
   */
  private broadcastAnomaly(anomaly: AnomalyDetection): void {
    const message = JSON.stringify({
      type: 'anomaly',
      data: anomaly
    });
    
    this.connectedClients.forEach(client => {
      if (client.readyState === WebSocket.OPEN) {
        client.send(message);
      }
    });
  }

  // 辅助方法

  private extractDeviceType(userAgent: string): string {
    if (/Mobile|Android|iPhone|iPad/.test(userAgent)) {
      return 'mobile';
    } else if (/Tablet/.test(userAgent)) {
      return 'tablet';
    } else {
      return 'desktop';
    }
  }

  private async getActiveUsersCount(since: Date): Promise<number> {
    const result = await this.prisma.userBehaviorEvents.groupBy({
      by: ['userId'],
      where: {
        timestamp: { gte: since },
        userId: { not: null }
      }
    });
    return result.length;
  }

  private async getSessionsCount(since: Date): Promise<number> {
    const result = await this.prisma.userBehaviorEvents.groupBy({
      by: ['sessionId'],
      where: {
        timestamp: { gte: since },
        sessionId: { not: null }
      }
    });
    return result.length;
  }

  private async getSecurityEventsCount(since: Date): Promise<number> {
    return await this.prisma.userBehaviorEvents.count({
      where: {
        timestamp: { gte: since },
        eventType: 'security_event'
      }
    });
  }

  private async getAverageRiskScore(since: Date): Promise<number> {
    const result = await this.prisma.userBehaviorEvents.aggregate({
      where: {
        timestamp: { gte: since },
        eventData: {
          path: ['riskScore'],
          not: null
        }
      },
      _avg: {
        // 这里需要根据实际数据结构调整
      }
    });
    return 0.5; // 临时返回值
  }

  private async getTopCountries(since: Date): Promise<Array<{country: string, count: number}>> {
    const result = await this.prisma.userBehaviorEvents.groupBy({
      by: ['country'],
      where: {
        timestamp: { gte: since },
        country: { not: null }
      },
      _count: true,
      orderBy: {
        _count: {
          country: 'desc'
        }
      },
      take: 10
    });
    
    return result.map(r => ({
      country: r.country || 'unknown',
      count: r._count
    }));
  }

  private async getDeviceTypeDistribution(since: Date): Promise<Array<{type: string, count: number}>> {
    // 这里需要根据实际数据结构实现
    return [
      { type: 'desktop', count: 100 },
      { type: 'mobile', count: 80 },
      { type: 'tablet', count: 20 }
    ];
  }

  private async getRecentLoginCount(ip: string, timeWindowMs: number): Promise<number> {
    const since = new Date(Date.now() - timeWindowMs);
    return await this.prisma.userBehaviorEvents.count({
      where: {
        timestamp: { gte: since },
        eventType: 'user_login',
        ipAddress: ip
      }
    });
  }
}
