/**
 * 数据收集服务
 * 
 * 功能说明：
 * 1. 收集用户行为数据
 * 2. 收集系统性能数据
 * 3. 收集安全事件数据
 * 4. 数据预处理和验证
 * 5. 批量数据传输和缓存
 */

import { EventEmitter } from 'events';
import { Request, Response } from 'express';
import { PrismaClient } from '@prisma/client';
import Redis from 'ioredis';
import { v4 as uuidv4 } from 'uuid';

/**
 * 事件类型枚举
 */
export enum EventType {
  // 用户行为事件
  USER_LOGIN = 'user_login',
  USER_LOGOUT = 'user_logout',
  USER_REGISTER = 'user_register',
  USER_PROFILE_UPDATE = 'user_profile_update',
  PASSWORD_CHANGE = 'password_change',
  MFA_SETUP = 'mfa_setup',
  MFA_VERIFY = 'mfa_verify',
  
  // 认证事件
  AUTH_SUCCESS = 'auth_success',
  AUTH_FAILURE = 'auth_failure',
  TOKEN_REFRESH = 'token_refresh',
  TOKEN_REVOKE = 'token_revoke',
  SESSION_EXPIRE = 'session_expire',
  
  // 安全事件
  SUSPICIOUS_LOGIN = 'suspicious_login',
  BRUTE_FORCE_ATTEMPT = 'brute_force_attempt',
  RATE_LIMIT_EXCEEDED = 'rate_limit_exceeded',
  UNAUTHORIZED_ACCESS = 'unauthorized_access',
  SECURITY_VIOLATION = 'security_violation',
  
  // 系统事件
  API_REQUEST = 'api_request',
  API_ERROR = 'api_error',
  SYSTEM_ERROR = 'system_error',
  PERFORMANCE_METRIC = 'performance_metric',
  
  // 业务事件
  APPLICATION_ACCESS = 'application_access',
  PERMISSION_GRANT = 'permission_grant',
  PERMISSION_REVOKE = 'permission_revoke',
  ADMIN_ACTION = 'admin_action'
}

/**
 * 事件数据接口
 */
export interface EventData {
  // 基础字段
  id: string;
  type: EventType;
  timestamp: Date;
  userId?: string;
  sessionId?: string;
  
  // 请求信息
  ip: string;
  userAgent: string;
  referer?: string;
  
  // 地理位置信息
  country?: string;
  region?: string;
  city?: string;
  
  // 设备信息
  deviceType?: 'desktop' | 'mobile' | 'tablet';
  browser?: string;
  os?: string;
  
  // 事件特定数据
  data: Record<string, any>;
  
  // 元数据
  metadata?: {
    source: string;
    version: string;
    environment: string;
  };
}

/**
 * 收集器配置
 */
interface CollectorConfig {
  batchSize: number;
  flushInterval: number;
  maxRetries: number;
  enableGeoLocation: boolean;
  enableDeviceDetection: boolean;
  redisPrefix: string;
}

/**
 * 数据收集器类
 */
export class DataCollector extends EventEmitter {
  private prisma: PrismaClient;
  private redis: Redis;
  private config: CollectorConfig;
  private eventQueue: EventData[] = [];
  private flushTimer?: NodeJS.Timeout;

  constructor(
    prisma: PrismaClient,
    redis: Redis,
    config: Partial<CollectorConfig> = {}
  ) {
    super();
    this.prisma = prisma;
    this.redis = redis;
    this.config = {
      batchSize: 100,
      flushInterval: 5000, // 5秒
      maxRetries: 3,
      enableGeoLocation: true,
      enableDeviceDetection: true,
      redisPrefix: 'analytics:',
      ...config
    };

    this.startFlushTimer();
  }

  /**
   * 收集事件数据
   */
  async collect(
    type: EventType,
    req: Request,
    data: Record<string, any> = {},
    userId?: string
  ): Promise<void> {
    try {
      const eventData = await this.createEventData(type, req, data, userId);
      
      // 添加到队列
      this.eventQueue.push(eventData);
      
      // 如果队列满了，立即刷新
      if (this.eventQueue.length >= this.config.batchSize) {
        await this.flush();
      }
      
      // 发出事件
      this.emit('eventCollected', eventData);
      
      // 实时处理关键事件
      if (this.isCriticalEvent(type)) {
        await this.processCriticalEvent(eventData);
      }
      
    } catch (error) {
      console.error('数据收集失败:', error);
      this.emit('error', error);
    }
  }

  /**
   * 创建事件数据
   */
  private async createEventData(
    type: EventType,
    req: Request,
    data: Record<string, any>,
    userId?: string
  ): Promise<EventData> {
    const eventData: EventData = {
      id: uuidv4(),
      type,
      timestamp: new Date(),
      userId,
      sessionId: req.session?.id,
      ip: this.getClientIP(req),
      userAgent: req.get('User-Agent') || '',
      referer: req.get('Referer'),
      data,
      metadata: {
        source: 'id-provider',
        version: process.env.APP_VERSION || '1.0.0',
        environment: process.env.NODE_ENV || 'development'
      }
    };

    // 地理位置信息
    if (this.config.enableGeoLocation) {
      const geoInfo = await this.getGeoLocation(eventData.ip);
      Object.assign(eventData, geoInfo);
    }

    // 设备信息
    if (this.config.enableDeviceDetection) {
      const deviceInfo = this.parseUserAgent(eventData.userAgent);
      Object.assign(eventData, deviceInfo);
    }

    return eventData;
  }

  /**
   * 获取客户端IP
   */
  private getClientIP(req: Request): string {
    return (
      req.ip ||
      req.connection.remoteAddress ||
      req.socket.remoteAddress ||
      (req.connection as any)?.socket?.remoteAddress ||
      '127.0.0.1'
    );
  }

  /**
   * 获取地理位置信息
   */
  private async getGeoLocation(ip: string): Promise<Partial<EventData>> {
    try {
      // 这里可以集成第三方地理位置服务，如MaxMind GeoIP
      // 暂时返回模拟数据
      if (ip === '127.0.0.1' || ip.startsWith('192.168.')) {
        return {
          country: 'CN',
          region: 'Beijing',
          city: 'Beijing'
        };
      }
      
      // 实际实现中应该调用地理位置API
      return {};
    } catch (error) {
      console.warn('地理位置获取失败:', error);
      return {};
    }
  }

  /**
   * 解析User-Agent
   */
  private parseUserAgent(userAgent: string): Partial<EventData> {
    try {
      // 简单的User-Agent解析
      // 实际实现中可以使用ua-parser-js等库
      const deviceInfo: Partial<EventData> = {};
      
      if (/Mobile|Android|iPhone|iPad/.test(userAgent)) {
        deviceInfo.deviceType = 'mobile';
      } else if (/Tablet|iPad/.test(userAgent)) {
        deviceInfo.deviceType = 'tablet';
      } else {
        deviceInfo.deviceType = 'desktop';
      }
      
      if (/Chrome/.test(userAgent)) {
        deviceInfo.browser = 'Chrome';
      } else if (/Firefox/.test(userAgent)) {
        deviceInfo.browser = 'Firefox';
      } else if (/Safari/.test(userAgent)) {
        deviceInfo.browser = 'Safari';
      }
      
      if (/Windows/.test(userAgent)) {
        deviceInfo.os = 'Windows';
      } else if (/Mac/.test(userAgent)) {
        deviceInfo.os = 'macOS';
      } else if (/Linux/.test(userAgent)) {
        deviceInfo.os = 'Linux';
      }
      
      return deviceInfo;
    } catch (error) {
      console.warn('User-Agent解析失败:', error);
      return {};
    }
  }

  /**
   * 判断是否为关键事件
   */
  private isCriticalEvent(type: EventType): boolean {
    const criticalEvents = [
      EventType.SUSPICIOUS_LOGIN,
      EventType.BRUTE_FORCE_ATTEMPT,
      EventType.UNAUTHORIZED_ACCESS,
      EventType.SECURITY_VIOLATION,
      EventType.SYSTEM_ERROR
    ];
    
    return criticalEvents.includes(type);
  }

  /**
   * 处理关键事件
   */
  private async processCriticalEvent(eventData: EventData): Promise<void> {
    try {
      // 立即存储到Redis用于实时告警
      const key = `${this.config.redisPrefix}critical:${eventData.id}`;
      await this.redis.setex(key, 3600, JSON.stringify(eventData)); // 1小时过期
      
      // 发送实时告警
      this.emit('criticalEvent', eventData);
      
      // 记录到安全日志
      console.warn('关键安全事件:', {
        type: eventData.type,
        userId: eventData.userId,
        ip: eventData.ip,
        timestamp: eventData.timestamp
      });
      
    } catch (error) {
      console.error('关键事件处理失败:', error);
    }
  }

  /**
   * 刷新事件队列到数据库
   */
  async flush(): Promise<void> {
    if (this.eventQueue.length === 0) return;

    const events = [...this.eventQueue];
    this.eventQueue = [];

    try {
      // 批量插入到数据库
      await this.prisma.analyticsEvent.createMany({
        data: events.map(event => ({
          id: event.id,
          type: event.type,
          timestamp: event.timestamp,
          userId: event.userId,
          sessionId: event.sessionId,
          ip: event.ip,
          userAgent: event.userAgent,
          referer: event.referer,
          country: event.country,
          region: event.region,
          city: event.city,
          deviceType: event.deviceType,
          browser: event.browser,
          os: event.os,
          data: event.data,
          metadata: event.metadata
        }))
      });

      // 更新Redis统计
      await this.updateRedisStats(events);

      console.log(`成功刷新 ${events.length} 个事件到数据库`);
      this.emit('flushed', events.length);

    } catch (error) {
      console.error('事件刷新失败:', error);
      
      // 重新加入队列进行重试
      this.eventQueue.unshift(...events);
      this.emit('flushError', error);
    }
  }

  /**
   * 更新Redis统计信息
   */
  private async updateRedisStats(events: EventData[]): Promise<void> {
    const pipeline = this.redis.pipeline();
    const today = new Date().toISOString().split('T')[0];

    for (const event of events) {
      // 按日期统计事件类型
      pipeline.hincrby(`${this.config.redisPrefix}daily:${today}`, event.type, 1);
      
      // 按用户统计
      if (event.userId) {
        pipeline.hincrby(`${this.config.redisPrefix}user:${event.userId}`, event.type, 1);
      }
      
      // 按IP统计
      pipeline.hincrby(`${this.config.redisPrefix}ip:${event.ip}`, event.type, 1);
    }

    await pipeline.exec();
  }

  /**
   * 启动定时刷新
   */
  private startFlushTimer(): void {
    this.flushTimer = setInterval(() => {
      this.flush().catch(console.error);
    }, this.config.flushInterval);
  }

  /**
   * 停止收集器
   */
  async stop(): Promise<void> {
    if (this.flushTimer) {
      clearInterval(this.flushTimer);
    }
    
    // 刷新剩余事件
    await this.flush();
    
    this.emit('stopped');
  }

  /**
   * 获取统计信息
   */
  async getStats(): Promise<{
    queueSize: number;
    totalEvents: number;
    dailyEvents: Record<string, number>;
  }> {
    const today = new Date().toISOString().split('T')[0];
    const dailyEvents = await this.redis.hgetall(`${this.config.redisPrefix}daily:${today}`);
    
    // 转换为数字
    const dailyStats: Record<string, number> = {};
    for (const [key, value] of Object.entries(dailyEvents)) {
      dailyStats[key] = parseInt(value, 10);
    }
    
    const totalEvents = Object.values(dailyStats).reduce((sum, count) => sum + count, 0);
    
    return {
      queueSize: this.eventQueue.length,
      totalEvents,
      dailyEvents: dailyStats
    };
  }
}

export default DataCollector;
