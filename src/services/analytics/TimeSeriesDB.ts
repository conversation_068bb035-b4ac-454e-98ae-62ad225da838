/**
 * 时序数据库服务
 * 
 * 功能说明：
 * 1. 管理时序数据的存储和查询
 * 2. 提供数据聚合和统计功能
 * 3. 支持数据压缩和归档
 * 4. 实现高效的时间范围查询
 * 5. 提供数据导出和备份功能
 */

import { PrismaClient } from '@prisma/client';
import Redis from 'ioredis';

/**
 * 时序数据点接口
 */
export interface TimeSeriesDataPoint {
  timestamp: Date;
  metric: string;
  value: number;
  tags: Record<string, string>;
  fields?: Record<string, any>;
}

/**
 * 查询选项接口
 */
export interface QueryOptions {
  startTime: Date;
  endTime: Date;
  metrics?: string[];
  tags?: Record<string, string>;
  aggregation?: 'avg' | 'sum' | 'min' | 'max' | 'count';
  interval?: string; // '1m', '5m', '1h', '1d'
  limit?: number;
  offset?: number;
}

/**
 * 聚合结果接口
 */
export interface AggregationResult {
  timestamp: Date;
  metric: string;
  value: number;
  count: number;
  tags: Record<string, string>;
}

/**
 * 时序数据库类
 */
export class TimeSeriesDB {
  private prisma: PrismaClient;
  private redis: Redis;
  private readonly CACHE_TTL = 300; // 5分钟缓存
  private readonly BATCH_SIZE = 1000;

  constructor(prisma: PrismaClient, redis: Redis) {
    this.prisma = prisma;
    this.redis = redis;
  }

  /**
   * 写入单个数据点
   */
  async writePoint(dataPoint: TimeSeriesDataPoint): Promise<void> {
    await this.writePoints([dataPoint]);
  }

  /**
   * 批量写入数据点
   */
  async writePoints(dataPoints: TimeSeriesDataPoint[]): Promise<void> {
    if (dataPoints.length === 0) return;

    try {
      // 按批次处理数据
      for (let i = 0; i < dataPoints.length; i += this.BATCH_SIZE) {
        const batch = dataPoints.slice(i, i + this.BATCH_SIZE);
        await this.processBatch(batch);
      }

      // 更新Redis缓存统计
      await this.updateCacheStats(dataPoints);

    } catch (error) {
      console.error('时序数据写入失败:', error);
      throw error;
    }
  }

  /**
   * 处理数据批次
   */
  private async processBatch(batch: TimeSeriesDataPoint[]): Promise<void> {
    const performanceMetrics = batch.map(point => ({
      metricType: point.metric,
      value: point.value,
      unit: point.tags.unit || '',
      timestamp: point.timestamp,
      metadata: {
        tags: point.tags,
        fields: point.fields || {}
      }
    }));

    await this.prisma.performanceMetric.createMany({
      data: performanceMetrics,
      skipDuplicates: true
    });
  }

  /**
   * 查询时序数据
   */
  async query(options: QueryOptions): Promise<AggregationResult[]> {
    const cacheKey = this.generateCacheKey(options);
    
    // 尝试从缓存获取
    const cached = await this.redis.get(cacheKey);
    if (cached) {
      return JSON.parse(cached);
    }

    try {
      const results = await this.executeQuery(options);
      
      // 缓存结果
      await this.redis.setex(cacheKey, this.CACHE_TTL, JSON.stringify(results));
      
      return results;
    } catch (error) {
      console.error('时序数据查询失败:', error);
      throw error;
    }
  }

  /**
   * 执行查询
   */
  private async executeQuery(options: QueryOptions): Promise<AggregationResult[]> {
    const {
      startTime,
      endTime,
      metrics,
      tags,
      aggregation = 'avg',
      interval,
      limit,
      offset
    } = options;

    // 构建查询条件
    const where: any = {
      timestamp: {
        gte: startTime,
        lte: endTime
      }
    };

    if (metrics && metrics.length > 0) {
      where.metricType = { in: metrics };
    }

    if (tags) {
      // 使用JSON查询过滤标签
      for (const [key, value] of Object.entries(tags)) {
        where.metadata = {
          ...where.metadata,
          path: ['tags', key],
          equals: value
        };
      }
    }

    // 如果有时间间隔聚合
    if (interval) {
      return this.queryWithInterval(where, aggregation, interval, limit, offset);
    }

    // 简单聚合查询
    return this.querySimpleAggregation(where, aggregation, limit, offset);
  }

  /**
   * 按时间间隔聚合查询
   */
  private async queryWithInterval(
    where: any,
    aggregation: string,
    interval: string,
    limit?: number,
    offset?: number
  ): Promise<AggregationResult[]> {
    // 将间隔转换为秒数
    const intervalSeconds = this.parseInterval(interval);
    
    // 使用原生SQL进行时间间隔聚合
    const query = `
      SELECT 
        DATE_TRUNC('${this.getPostgresInterval(interval)}', timestamp) as time_bucket,
        metric_type,
        ${this.getAggregationFunction(aggregation)}(value) as value,
        COUNT(*) as count,
        metadata
      FROM performance_metrics 
      WHERE timestamp >= $1 AND timestamp <= $2
      ${where.metricType ? 'AND metric_type = ANY($3)' : ''}
      GROUP BY time_bucket, metric_type, metadata
      ORDER BY time_bucket DESC
      ${limit ? `LIMIT ${limit}` : ''}
      ${offset ? `OFFSET ${offset}` : ''}
    `;

    const params = [where.timestamp.gte, where.timestamp.lte];
    if (where.metricType) {
      params.push(where.metricType.in);
    }

    const results = await this.prisma.$queryRawUnsafe(query, ...params);
    
    return (results as any[]).map(row => ({
      timestamp: new Date(row.time_bucket),
      metric: row.metric_type,
      value: parseFloat(row.value),
      count: parseInt(row.count),
      tags: row.metadata?.tags || {}
    }));
  }

  /**
   * 简单聚合查询
   */
  private async querySimpleAggregation(
    where: any,
    aggregation: string,
    limit?: number,
    offset?: number
  ): Promise<AggregationResult[]> {
    const groupBy = {
      metricType: true,
      metadata: true
    };

    const aggregateFunction = this.getAggregateFunction(aggregation);

    const results = await this.prisma.performanceMetric.groupBy({
      by: ['metricType'],
      where,
      _avg: aggregation === 'avg' ? { value: true } : undefined,
      _sum: aggregation === 'sum' ? { value: true } : undefined,
      _min: aggregation === 'min' ? { value: true } : undefined,
      _max: aggregation === 'max' ? { value: true } : undefined,
      _count: { value: true },
      take: limit,
      skip: offset,
      orderBy: {
        _count: {
          value: 'desc'
        }
      }
    });

    return results.map(result => ({
      timestamp: new Date(), // 对于简单聚合，使用当前时间
      metric: result.metricType,
      value: this.extractAggregateValue(result, aggregation),
      count: result._count.value,
      tags: {}
    }));
  }

  /**
   * 获取聚合函数
   */
  private getAggregationFunction(aggregation: string): string {
    switch (aggregation) {
      case 'avg': return 'AVG';
      case 'sum': return 'SUM';
      case 'min': return 'MIN';
      case 'max': return 'MAX';
      case 'count': return 'COUNT';
      default: return 'AVG';
    }
  }

  /**
   * 获取Prisma聚合函数
   */
  private getAggregateFunction(aggregation: string) {
    return {
      avg: aggregation === 'avg',
      sum: aggregation === 'sum',
      min: aggregation === 'min',
      max: aggregation === 'max',
      count: true
    };
  }

  /**
   * 提取聚合值
   */
  private extractAggregateValue(result: any, aggregation: string): number {
    switch (aggregation) {
      case 'avg': return result._avg?.value || 0;
      case 'sum': return result._sum?.value || 0;
      case 'min': return result._min?.value || 0;
      case 'max': return result._max?.value || 0;
      case 'count': return result._count?.value || 0;
      default: return 0;
    }
  }

  /**
   * 解析时间间隔
   */
  private parseInterval(interval: string): number {
    const match = interval.match(/^(\d+)([smhd])$/);
    if (!match) throw new Error(`无效的时间间隔: ${interval}`);

    const [, amount, unit] = match;
    const multipliers = { s: 1, m: 60, h: 3600, d: 86400 };
    
    return parseInt(amount) * multipliers[unit as keyof typeof multipliers];
  }

  /**
   * 获取PostgreSQL时间间隔
   */
  private getPostgresInterval(interval: string): string {
    const match = interval.match(/^(\d+)([smhd])$/);
    if (!match) return 'hour';

    const [, amount, unit] = match;
    const units = { s: 'second', m: 'minute', h: 'hour', d: 'day' };
    
    return units[unit as keyof typeof units];
  }

  /**
   * 生成缓存键
   */
  private generateCacheKey(options: QueryOptions): string {
    const key = `timeseries:${JSON.stringify(options)}`;
    return key.replace(/[^a-zA-Z0-9:_-]/g, '_');
  }

  /**
   * 更新缓存统计
   */
  private async updateCacheStats(dataPoints: TimeSeriesDataPoint[]): Promise<void> {
    const pipeline = this.redis.pipeline();
    const now = new Date();
    const today = now.toISOString().split('T')[0];

    // 按指标类型统计
    const metricCounts: Record<string, number> = {};
    for (const point of dataPoints) {
      metricCounts[point.metric] = (metricCounts[point.metric] || 0) + 1;
    }

    for (const [metric, count] of Object.entries(metricCounts)) {
      pipeline.hincrby(`timeseries:daily:${today}`, metric, count);
      pipeline.hincrby(`timeseries:total`, metric, count);
    }

    await pipeline.exec();
  }

  /**
   * 数据压缩和归档
   */
  async compressOldData(olderThanDays: number = 30): Promise<void> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);

    try {
      // 删除旧数据
      const result = await this.prisma.performanceMetric.deleteMany({
        where: {
          timestamp: {
            lt: cutoffDate
          }
        }
      });

      console.log(`已压缩 ${result.count} 条旧数据`);
    } catch (error) {
      console.error('数据压缩失败:', error);
      throw error;
    }
  }

  /**
   * 获取数据库统计信息
   */
  async getStats(): Promise<{
    totalPoints: number;
    oldestPoint: Date | null;
    newestPoint: Date | null;
    metricTypes: string[];
  }> {
    const [totalCount, oldest, newest, metrics] = await Promise.all([
      this.prisma.performanceMetric.count(),
      this.prisma.performanceMetric.findFirst({
        orderBy: { timestamp: 'asc' },
        select: { timestamp: true }
      }),
      this.prisma.performanceMetric.findFirst({
        orderBy: { timestamp: 'desc' },
        select: { timestamp: true }
      }),
      this.prisma.performanceMetric.findMany({
        select: { metricType: true },
        distinct: ['metricType']
      })
    ]);

    return {
      totalPoints: totalCount,
      oldestPoint: oldest?.timestamp || null,
      newestPoint: newest?.timestamp || null,
      metricTypes: metrics.map(m => m.metricType)
    };
  }
}

export default TimeSeriesDB;
