/**
 * 数据分析引擎
 * 
 * 功能说明：
 * 1. 用户行为模式分析
 * 2. 异常检测和风险评估
 * 3. 统计报告生成
 * 4. 实时数据处理
 * 5. 预测性分析
 */

import { PrismaClient } from '@prisma/client';
import Redis from 'ioredis';
import { EventType } from './DataCollector';
import { TimeSeriesDB, QueryOptions } from './TimeSeriesDB';

/**
 * 用户行为模式接口
 */
export interface UserBehaviorPattern {
  userId: string;
  patternType: 'login_pattern' | 'usage_pattern' | 'risk_pattern';
  pattern: {
    frequency: number;
    timeOfDay: number[];
    daysOfWeek: number[];
    locations: string[];
    devices: string[];
    averageSessionDuration: number;
    commonActions: string[];
  };
  confidence: number;
  lastUpdated: Date;
}

/**
 * 异常检测结果接口
 */
export interface AnomalyDetectionResult {
  userId: string;
  anomalyType: 'location' | 'time' | 'device' | 'behavior' | 'frequency';
  severity: 'low' | 'medium' | 'high' | 'critical';
  score: number; // 0-1，1表示最异常
  description: string;
  evidence: Record<string, any>;
  timestamp: Date;
}

/**
 * 统计报告接口
 */
export interface AnalyticsReport {
  reportId: string;
  type: 'daily' | 'weekly' | 'monthly' | 'custom';
  period: {
    startDate: Date;
    endDate: Date;
  };
  metrics: {
    totalUsers: number;
    activeUsers: number;
    newUsers: number;
    totalSessions: number;
    averageSessionDuration: number;
    totalLogins: number;
    failedLogins: number;
    securityEvents: number;
    topCountries: Array<{ country: string; count: number }>;
    topDevices: Array<{ device: string; count: number }>;
    hourlyDistribution: Array<{ hour: number; count: number }>;
  };
  generatedAt: Date;
}

/**
 * 数据分析引擎类
 */
export class AnalyticsEngine {
  private prisma: PrismaClient;
  private redis: Redis;
  private timeSeriesDB: TimeSeriesDB;

  constructor(prisma: PrismaClient, redis: Redis, timeSeriesDB: TimeSeriesDB) {
    this.prisma = prisma;
    this.redis = redis;
    this.timeSeriesDB = timeSeriesDB;
  }

  /**
   * 分析用户行为模式
   */
  async analyzeUserBehavior(userId: string, days: number = 30): Promise<UserBehaviorPattern[]> {
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(endDate.getDate() - days);

    try {
      // 获取用户事件数据
      const events = await this.prisma.analyticsEvent.findMany({
        where: {
          userId,
          timestamp: {
            gte: startDate,
            lte: endDate
          }
        },
        orderBy: { timestamp: 'asc' }
      });

      if (events.length === 0) {
        return [];
      }

      const patterns: UserBehaviorPattern[] = [];

      // 分析登录模式
      const loginPattern = this.analyzeLoginPattern(events);
      if (loginPattern) {
        patterns.push(loginPattern);
      }

      // 分析使用模式
      const usagePattern = this.analyzeUsagePattern(events);
      if (usagePattern) {
        patterns.push(usagePattern);
      }

      // 分析风险模式
      const riskPattern = this.analyzeRiskPattern(events);
      if (riskPattern) {
        patterns.push(riskPattern);
      }

      // 保存模式到数据库
      await this.saveUserBehaviorPatterns(patterns);

      return patterns;
    } catch (error) {
      console.error('用户行为分析失败:', error);
      throw error;
    }
  }

  /**
   * 分析登录模式
   */
  private analyzeLoginPattern(events: any[]): UserBehaviorPattern | null {
    const loginEvents = events.filter(e => 
      e.type === EventType.USER_LOGIN || e.type === EventType.AUTH_SUCCESS
    );

    if (loginEvents.length < 5) return null; // 需要足够的数据

    // 分析时间模式
    const timeOfDay = loginEvents.map(e => new Date(e.timestamp).getHours());
    const daysOfWeek = loginEvents.map(e => new Date(e.timestamp).getDay());
    
    // 分析位置模式
    const locations = [...new Set(loginEvents.map(e => e.country).filter(Boolean))];
    
    // 分析设备模式
    const devices = [...new Set(loginEvents.map(e => e.deviceType).filter(Boolean))];

    // 计算频率（每天平均登录次数）
    const daySpan = Math.max(1, (Date.now() - new Date(loginEvents[0].timestamp).getTime()) / (1000 * 60 * 60 * 24));
    const frequency = loginEvents.length / daySpan;

    // 计算置信度
    const confidence = Math.min(1, loginEvents.length / 30); // 30次登录达到最高置信度

    return {
      userId: loginEvents[0].userId,
      patternType: 'login_pattern',
      pattern: {
        frequency,
        timeOfDay: this.getMostCommonValues(timeOfDay),
        daysOfWeek: this.getMostCommonValues(daysOfWeek),
        locations,
        devices,
        averageSessionDuration: 0, // 需要会话数据计算
        commonActions: ['login']
      },
      confidence,
      lastUpdated: new Date()
    };
  }

  /**
   * 分析使用模式
   */
  private analyzeUsagePattern(events: any[]): UserBehaviorPattern | null {
    const usageEvents = events.filter(e => 
      e.type === EventType.API_REQUEST || 
      e.type === EventType.APPLICATION_ACCESS
    );

    if (usageEvents.length < 10) return null;

    // 分析常见操作
    const actions = usageEvents.map(e => e.data?.endpoint || e.type);
    const commonActions = this.getMostCommonValues(actions, 5);

    // 分析使用时间
    const timeOfDay = usageEvents.map(e => new Date(e.timestamp).getHours());
    const daysOfWeek = usageEvents.map(e => new Date(e.timestamp).getDay());

    const daySpan = Math.max(1, (Date.now() - new Date(usageEvents[0].timestamp).getTime()) / (1000 * 60 * 60 * 24));
    const frequency = usageEvents.length / daySpan;

    const confidence = Math.min(1, usageEvents.length / 100);

    return {
      userId: usageEvents[0].userId,
      patternType: 'usage_pattern',
      pattern: {
        frequency,
        timeOfDay: this.getMostCommonValues(timeOfDay),
        daysOfWeek: this.getMostCommonValues(daysOfWeek),
        locations: [],
        devices: [],
        averageSessionDuration: 0,
        commonActions
      },
      confidence,
      lastUpdated: new Date()
    };
  }

  /**
   * 分析风险模式
   */
  private analyzeRiskPattern(events: any[]): UserBehaviorPattern | null {
    const riskEvents = events.filter(e => 
      e.type === EventType.AUTH_FAILURE ||
      e.type === EventType.SUSPICIOUS_LOGIN ||
      e.type === EventType.UNAUTHORIZED_ACCESS
    );

    if (riskEvents.length === 0) return null;

    const timeOfDay = riskEvents.map(e => new Date(e.timestamp).getHours());
    const locations = [...new Set(riskEvents.map(e => e.country).filter(Boolean))];

    const daySpan = Math.max(1, (Date.now() - new Date(riskEvents[0].timestamp).getTime()) / (1000 * 60 * 60 * 24));
    const frequency = riskEvents.length / daySpan;

    // 风险模式的置信度基于事件频率
    const confidence = Math.min(1, riskEvents.length / 10);

    return {
      userId: riskEvents[0].userId,
      patternType: 'risk_pattern',
      pattern: {
        frequency,
        timeOfDay: this.getMostCommonValues(timeOfDay),
        daysOfWeek: [],
        locations,
        devices: [],
        averageSessionDuration: 0,
        commonActions: riskEvents.map(e => e.type)
      },
      confidence,
      lastUpdated: new Date()
    };
  }

  /**
   * 异常检测
   */
  async detectAnomalies(userId: string): Promise<AnomalyDetectionResult[]> {
    try {
      const anomalies: AnomalyDetectionResult[] = [];

      // 获取用户的行为模式
      const patterns = await this.prisma.userBehaviorPattern.findMany({
        where: { userId, isActive: true }
      });

      if (patterns.length === 0) {
        return anomalies; // 没有足够的历史数据
      }

      // 获取最近的事件
      const recentEvents = await this.prisma.analyticsEvent.findMany({
        where: {
          userId,
          timestamp: {
            gte: new Date(Date.now() - 24 * 60 * 60 * 1000) // 最近24小时
          }
        }
      });

      // 检测位置异常
      const locationAnomaly = this.detectLocationAnomaly(patterns, recentEvents);
      if (locationAnomaly) anomalies.push(locationAnomaly);

      // 检测时间异常
      const timeAnomaly = this.detectTimeAnomaly(patterns, recentEvents);
      if (timeAnomaly) anomalies.push(timeAnomaly);

      // 检测设备异常
      const deviceAnomaly = this.detectDeviceAnomaly(patterns, recentEvents);
      if (deviceAnomaly) anomalies.push(deviceAnomaly);

      // 检测频率异常
      const frequencyAnomaly = this.detectFrequencyAnomaly(patterns, recentEvents);
      if (frequencyAnomaly) anomalies.push(frequencyAnomaly);

      return anomalies;
    } catch (error) {
      console.error('异常检测失败:', error);
      throw error;
    }
  }

  /**
   * 检测位置异常
   */
  private detectLocationAnomaly(patterns: any[], recentEvents: any[]): AnomalyDetectionResult | null {
    const loginPattern = patterns.find(p => p.patternType === 'login_pattern');
    if (!loginPattern) return null;

    const knownLocations = loginPattern.pattern.locations || [];
    const recentLocations = [...new Set(recentEvents.map(e => e.country).filter(Boolean))];

    const unknownLocations = recentLocations.filter(loc => !knownLocations.includes(loc));
    
    if (unknownLocations.length > 0) {
      return {
        userId: loginPattern.userId,
        anomalyType: 'location',
        severity: 'medium',
        score: 0.7,
        description: `检测到来自未知位置的访问: ${unknownLocations.join(', ')}`,
        evidence: {
          knownLocations,
          unknownLocations,
          recentEvents: recentEvents.length
        },
        timestamp: new Date()
      };
    }

    return null;
  }

  /**
   * 检测时间异常
   */
  private detectTimeAnomaly(patterns: any[], recentEvents: any[]): AnomalyDetectionResult | null {
    const loginPattern = patterns.find(p => p.patternType === 'login_pattern');
    if (!loginPattern) return null;

    const normalHours = loginPattern.pattern.timeOfDay || [];
    const recentHours = recentEvents.map(e => new Date(e.timestamp).getHours());

    const unusualHours = recentHours.filter(hour => !normalHours.includes(hour));
    
    if (unusualHours.length > recentHours.length * 0.5) { // 超过50%的活动在异常时间
      return {
        userId: loginPattern.userId,
        anomalyType: 'time',
        severity: 'low',
        score: 0.4,
        description: `检测到异常时间段的活动`,
        evidence: {
          normalHours,
          unusualHours,
          recentActivity: recentHours
        },
        timestamp: new Date()
      };
    }

    return null;
  }

  /**
   * 检测设备异常
   */
  private detectDeviceAnomaly(patterns: any[], recentEvents: any[]): AnomalyDetectionResult | null {
    const loginPattern = patterns.find(p => p.patternType === 'login_pattern');
    if (!loginPattern) return null;

    const knownDevices = loginPattern.pattern.devices || [];
    const recentDevices = [...new Set(recentEvents.map(e => e.deviceType).filter(Boolean))];

    const unknownDevices = recentDevices.filter(device => !knownDevices.includes(device));
    
    if (unknownDevices.length > 0) {
      return {
        userId: loginPattern.userId,
        anomalyType: 'device',
        severity: 'medium',
        score: 0.6,
        description: `检测到来自未知设备类型的访问: ${unknownDevices.join(', ')}`,
        evidence: {
          knownDevices,
          unknownDevices
        },
        timestamp: new Date()
      };
    }

    return null;
  }

  /**
   * 检测频率异常
   */
  private detectFrequencyAnomaly(patterns: any[], recentEvents: any[]): AnomalyDetectionResult | null {
    const usagePattern = patterns.find(p => p.patternType === 'usage_pattern');
    if (!usagePattern) return null;

    const normalFrequency = usagePattern.pattern.frequency;
    const recentFrequency = recentEvents.length; // 24小时内的事件数

    // 如果最近频率是正常频率的3倍以上
    if (recentFrequency > normalFrequency * 3) {
      return {
        userId: usagePattern.userId,
        anomalyType: 'frequency',
        severity: 'high',
        score: 0.8,
        description: `检测到异常高频的活动`,
        evidence: {
          normalFrequency,
          recentFrequency,
          ratio: recentFrequency / normalFrequency
        },
        timestamp: new Date()
      };
    }

    return null;
  }

  /**
   * 生成统计报告
   */
  async generateReport(
    type: 'daily' | 'weekly' | 'monthly' | 'custom',
    startDate?: Date,
    endDate?: Date
  ): Promise<AnalyticsReport> {
    const period = this.calculateReportPeriod(type, startDate, endDate);
    
    try {
      const [
        totalUsers,
        activeUsers,
        newUsers,
        sessions,
        logins,
        failedLogins,
        securityEvents,
        topCountries,
        topDevices,
        hourlyDistribution
      ] = await Promise.all([
        this.getTotalUsers(period),
        this.getActiveUsers(period),
        this.getNewUsers(period),
        this.getSessions(period),
        this.getLogins(period),
        this.getFailedLogins(period),
        this.getSecurityEvents(period),
        this.getTopCountries(period),
        this.getTopDevices(period),
        this.getHourlyDistribution(period)
      ]);

      const averageSessionDuration = sessions.totalSessions > 0 
        ? sessions.totalDuration / sessions.totalSessions 
        : 0;

      return {
        reportId: `report_${Date.now()}`,
        type,
        period,
        metrics: {
          totalUsers,
          activeUsers,
          newUsers,
          totalSessions: sessions.totalSessions,
          averageSessionDuration,
          totalLogins: logins,
          failedLogins,
          securityEvents,
          topCountries,
          topDevices,
          hourlyDistribution
        },
        generatedAt: new Date()
      };
    } catch (error) {
      console.error('报告生成失败:', error);
      throw error;
    }
  }

  /**
   * 获取最常见的值
   */
  private getMostCommonValues(values: any[], limit: number = 3): any[] {
    const counts = values.reduce((acc, val) => {
      acc[val] = (acc[val] || 0) + 1;
      return acc;
    }, {});

    return Object.entries(counts)
      .sort(([,a], [,b]) => (b as number) - (a as number))
      .slice(0, limit)
      .map(([val]) => isNaN(Number(val)) ? val : Number(val));
  }

  /**
   * 保存用户行为模式
   */
  private async saveUserBehaviorPatterns(patterns: UserBehaviorPattern[]): Promise<void> {
    for (const pattern of patterns) {
      await this.prisma.userBehaviorPattern.upsert({
        where: {
          userId_patternType: {
            userId: pattern.userId,
            patternType: pattern.patternType
          }
        },
        update: {
          pattern: pattern.pattern,
          confidence: pattern.confidence,
          lastUpdated: pattern.lastUpdated
        },
        create: {
          userId: pattern.userId,
          patternType: pattern.patternType,
          pattern: pattern.pattern,
          confidence: pattern.confidence,
          startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30天前
          endDate: new Date(),
          lastUpdated: pattern.lastUpdated
        }
      });
    }
  }

  /**
   * 计算报告周期
   */
  private calculateReportPeriod(
    type: string,
    startDate?: Date,
    endDate?: Date
  ): { startDate: Date; endDate: Date } {
    const now = new Date();
    
    if (type === 'custom' && startDate && endDate) {
      return { startDate, endDate };
    }

    const end = new Date(now);
    const start = new Date(now);

    switch (type) {
      case 'daily':
        start.setDate(start.getDate() - 1);
        break;
      case 'weekly':
        start.setDate(start.getDate() - 7);
        break;
      case 'monthly':
        start.setMonth(start.getMonth() - 1);
        break;
    }

    return { startDate: start, endDate: end };
  }

  // 报告数据获取方法（简化实现）
  private async getTotalUsers(period: { startDate: Date; endDate: Date }): Promise<number> {
    return this.prisma.user.count();
  }

  private async getActiveUsers(period: { startDate: Date; endDate: Date }): Promise<number> {
    return this.prisma.analyticsEvent.findMany({
      where: {
        timestamp: { gte: period.startDate, lte: period.endDate },
        userId: { not: null }
      },
      distinct: ['userId']
    }).then(events => events.length);
  }

  private async getNewUsers(period: { startDate: Date; endDate: Date }): Promise<number> {
    return this.prisma.user.count({
      where: {
        createdAt: { gte: period.startDate, lte: period.endDate }
      }
    });
  }

  private async getSessions(period: { startDate: Date; endDate: Date }): Promise<{ totalSessions: number; totalDuration: number }> {
    const sessions = await this.prisma.sessionAnalytics.findMany({
      where: {
        startTime: { gte: period.startDate, lte: period.endDate }
      }
    });

    return {
      totalSessions: sessions.length,
      totalDuration: sessions.reduce((sum, s) => sum + (s.duration || 0), 0)
    };
  }

  private async getLogins(period: { startDate: Date; endDate: Date }): Promise<number> {
    return this.prisma.analyticsEvent.count({
      where: {
        type: { in: [EventType.USER_LOGIN, EventType.AUTH_SUCCESS] },
        timestamp: { gte: period.startDate, lte: period.endDate }
      }
    });
  }

  private async getFailedLogins(period: { startDate: Date; endDate: Date }): Promise<number> {
    return this.prisma.analyticsEvent.count({
      where: {
        type: EventType.AUTH_FAILURE,
        timestamp: { gte: period.startDate, lte: period.endDate }
      }
    });
  }

  private async getSecurityEvents(period: { startDate: Date; endDate: Date }): Promise<number> {
    return this.prisma.securityEvent.count({
      where: {
        createdAt: { gte: period.startDate, lte: period.endDate }
      }
    });
  }

  private async getTopCountries(period: { startDate: Date; endDate: Date }): Promise<Array<{ country: string; count: number }>> {
    const results = await this.prisma.analyticsEvent.groupBy({
      by: ['country'],
      where: {
        timestamp: { gte: period.startDate, lte: period.endDate },
        country: { not: null }
      },
      _count: { country: true },
      orderBy: { _count: { country: 'desc' } },
      take: 10
    });

    return results.map(r => ({
      country: r.country || 'Unknown',
      count: r._count.country
    }));
  }

  private async getTopDevices(period: { startDate: Date; endDate: Date }): Promise<Array<{ device: string; count: number }>> {
    const results = await this.prisma.analyticsEvent.groupBy({
      by: ['deviceType'],
      where: {
        timestamp: { gte: period.startDate, lte: period.endDate },
        deviceType: { not: null }
      },
      _count: { deviceType: true },
      orderBy: { _count: { deviceType: 'desc' } },
      take: 10
    });

    return results.map(r => ({
      device: r.deviceType || 'Unknown',
      count: r._count.deviceType
    }));
  }

  private async getHourlyDistribution(period: { startDate: Date; endDate: Date }): Promise<Array<{ hour: number; count: number }>> {
    // 使用原生SQL查询按小时分组
    const results = await this.prisma.$queryRaw`
      SELECT EXTRACT(HOUR FROM timestamp) as hour, COUNT(*) as count
      FROM analytics_events 
      WHERE timestamp >= ${period.startDate} AND timestamp <= ${period.endDate}
      GROUP BY EXTRACT(HOUR FROM timestamp)
      ORDER BY hour
    ` as Array<{ hour: number; count: bigint }>;

    return results.map(r => ({
      hour: Number(r.hour),
      count: Number(r.count)
    }));
  }
}

export default AnalyticsEngine;
