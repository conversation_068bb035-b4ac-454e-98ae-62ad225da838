/**
 * 用户会话缓存服务
 * 实现用户登录会话的Redis缓存，支持多设备会话管理和快速验证
 */

import { redisService } from './redis.service'
import { CacheKeyBuilder, CacheStrategies, CacheSerializer } from '@/config/cache-strategy'
import { logger } from '@/config/logger'

export interface SessionData {
  sessionId: string
  userId: string
  deviceId?: string
  deviceInfo?: {
    userAgent: string
    ip: string
    platform?: string
    browser?: string
  }
  createdAt: Date
  lastAccessedAt: Date
  expiresAt: Date
  isActive: boolean
  metadata?: Record<string, any>
}

export interface UserSessionSummary {
  userId: string
  totalSessions: number
  activeSessions: number
  devices: Array<{
    deviceId: string
    deviceInfo: SessionData['deviceInfo']
    lastAccessed: Date
    sessionCount: number
  }>
}

/**
 * 会话缓存服务类
 */
class SessionCacheService {
  /**
   * 创建会话缓存
   */
  async createSession(sessionData: SessionData): Promise<void> {
    try {
      const sessionKey = CacheKeyBuilder.session.data(sessionData.sessionId)
      const userSessionsKey = CacheKeyBuilder.session.userSessions(sessionData.userId)
      
      // 序列化会话数据
      const serializedData = CacheSerializer.serialize(sessionData)
      
      // 计算TTL（到过期时间的秒数）
      const ttl = Math.floor((sessionData.expiresAt.getTime() - Date.now()) / 1000)
      
      // 存储会话数据
      await redisService.setex(sessionKey, ttl, serializedData)
      
      // 添加到用户会话集合
      await redisService.sadd(userSessionsKey, sessionData.sessionId)
      await redisService.expire(userSessionsKey, CacheStrategies.SESSION_DATA.ttl)
      
      // 如果有设备信息，存储设备会话映射
      if (sessionData.deviceId) {
        const deviceKey = CacheKeyBuilder.session.device(sessionData.deviceId)
        await redisService.sadd(deviceKey, sessionData.sessionId)
        await redisService.expire(deviceKey, CacheStrategies.SESSION_DATA.ttl)
      }
      
      logger.debug('会话缓存创建成功', {
        service: 'session-cache',
        sessionId: sessionData.sessionId,
        userId: sessionData.userId,
        deviceId: sessionData.deviceId,
        ttl
      })

    } catch (error) {
      logger.error('创建会话缓存失败', {
        service: 'session-cache',
        sessionId: sessionData.sessionId,
        error: error instanceof Error ? error.message : String(error)
      })
      throw error
    }
  }

  /**
   * 获取会话数据
   */
  async getSession(sessionId: string): Promise<SessionData | null> {
    try {
      const sessionKey = CacheKeyBuilder.session.data(sessionId)
      const data = await redisService.get(sessionKey)
      
      if (!data) {
        return null
      }
      
      const sessionData = CacheSerializer.deserialize<SessionData>(data)
      
      // 检查会话是否过期
      if (new Date() > new Date(sessionData.expiresAt)) {
        await this.deleteSession(sessionId)
        return null
      }
      
      // 更新最后访问时间
      sessionData.lastAccessedAt = new Date()
      await this.updateSessionActivity(sessionId, sessionData.lastAccessedAt)
      
      logger.debug('会话缓存获取成功', {
        service: 'session-cache',
        sessionId,
        userId: sessionData.userId
      })
      
      return sessionData

    } catch (error) {
      logger.error('获取会话缓存失败', {
        service: 'session-cache',
        sessionId,
        error: error instanceof Error ? error.message : String(error)
      })
      return null
    }
  }

  /**
   * 更新会话活动时间
   */
  async updateSessionActivity(sessionId: string, lastAccessedAt: Date = new Date()): Promise<void> {
    try {
      const sessionKey = CacheKeyBuilder.session.data(sessionId)
      const activityKey = CacheKeyBuilder.session.activity(sessionId)
      
      // 更新活动时间
      await redisService.set(activityKey, lastAccessedAt.toISOString())
      await redisService.expire(activityKey, CacheStrategies.SESSION_DATA.ttl)
      
      // 更新会话数据中的最后访问时间
      const sessionData = await this.getSessionWithoutUpdate(sessionId)
      if (sessionData) {
        sessionData.lastAccessedAt = lastAccessedAt
        const serializedData = CacheSerializer.serialize(sessionData)
        const ttl = Math.floor((sessionData.expiresAt.getTime() - Date.now()) / 1000)
        await redisService.setex(sessionKey, ttl, serializedData)
      }

    } catch (error) {
      logger.error('更新会话活动失败', {
        service: 'session-cache',
        sessionId,
        error: error instanceof Error ? error.message : String(error)
      })
    }
  }

  /**
   * 获取会话数据（不更新活动时间）
   */
  private async getSessionWithoutUpdate(sessionId: string): Promise<SessionData | null> {
    try {
      const sessionKey = CacheKeyBuilder.session.data(sessionId)
      const data = await redisService.get(sessionKey)
      
      if (!data) {
        return null
      }
      
      return CacheSerializer.deserialize<SessionData>(data)

    } catch (error) {
      return null
    }
  }

  /**
   * 删除会话
   */
  async deleteSession(sessionId: string): Promise<void> {
    try {
      const sessionData = await this.getSessionWithoutUpdate(sessionId)
      
      if (sessionData) {
        const sessionKey = CacheKeyBuilder.session.data(sessionId)
        const userSessionsKey = CacheKeyBuilder.session.userSessions(sessionData.userId)
        const activityKey = CacheKeyBuilder.session.activity(sessionId)
        
        // 删除会话数据
        await redisService.del(sessionKey)
        await redisService.del(activityKey)
        
        // 从用户会话集合中移除
        await redisService.srem(userSessionsKey, sessionId)
        
        // 如果有设备信息，从设备会话集合中移除
        if (sessionData.deviceId) {
          const deviceKey = CacheKeyBuilder.session.device(sessionData.deviceId)
          await redisService.srem(deviceKey, sessionId)
        }
        
        logger.debug('会话缓存删除成功', {
          service: 'session-cache',
          sessionId,
          userId: sessionData.userId
        })
      }

    } catch (error) {
      logger.error('删除会话缓存失败', {
        service: 'session-cache',
        sessionId,
        error: error instanceof Error ? error.message : String(error)
      })
      throw error
    }
  }

  /**
   * 获取用户的所有会话
   */
  async getUserSessions(userId: string): Promise<SessionData[]> {
    try {
      const userSessionsKey = CacheKeyBuilder.session.userSessions(userId)
      const sessionIds = await redisService.smembers(userSessionsKey)
      
      const sessions: SessionData[] = []
      
      for (const sessionId of sessionIds) {
        const sessionData = await this.getSessionWithoutUpdate(sessionId)
        if (sessionData && sessionData.isActive) {
          sessions.push(sessionData)
        } else {
          // 清理无效会话
          await redisService.srem(userSessionsKey, sessionId)
        }
      }
      
      // 按最后访问时间排序
      sessions.sort((a, b) => 
        new Date(b.lastAccessedAt).getTime() - new Date(a.lastAccessedAt).getTime()
      )
      
      return sessions

    } catch (error) {
      logger.error('获取用户会话失败', {
        service: 'session-cache',
        userId,
        error: error instanceof Error ? error.message : String(error)
      })
      return []
    }
  }

  /**
   * 获取用户会话摘要
   */
  async getUserSessionSummary(userId: string): Promise<UserSessionSummary> {
    try {
      const sessions = await this.getUserSessions(userId)
      const deviceMap = new Map<string, {
        deviceInfo: SessionData['deviceInfo']
        lastAccessed: Date
        sessionCount: number
      }>()
      
      let activeSessions = 0
      
      for (const session of sessions) {
        if (session.isActive) {
          activeSessions++
        }
        
        if (session.deviceId) {
          const existing = deviceMap.get(session.deviceId)
          if (existing) {
            existing.sessionCount++
            if (session.lastAccessedAt > existing.lastAccessed) {
              existing.lastAccessed = session.lastAccessedAt
            }
          } else {
            deviceMap.set(session.deviceId, {
              deviceInfo: session.deviceInfo,
              lastAccessed: session.lastAccessedAt,
              sessionCount: 1
            })
          }
        }
      }
      
      const devices = Array.from(deviceMap.entries()).map(([deviceId, info]) => ({
        deviceId,
        ...info
      }))
      
      return {
        userId,
        totalSessions: sessions.length,
        activeSessions,
        devices
      }

    } catch (error) {
      logger.error('获取用户会话摘要失败', {
        service: 'session-cache',
        userId,
        error: error instanceof Error ? error.message : String(error)
      })
      
      return {
        userId,
        totalSessions: 0,
        activeSessions: 0,
        devices: []
      }
    }
  }

  /**
   * 终止用户的所有会话（除了当前会话）
   */
  async terminateUserSessions(userId: string, excludeSessionId?: string): Promise<number> {
    try {
      const sessions = await this.getUserSessions(userId)
      let terminatedCount = 0
      
      for (const session of sessions) {
        if (session.sessionId !== excludeSessionId) {
          await this.deleteSession(session.sessionId)
          terminatedCount++
        }
      }
      
      logger.info('用户会话批量终止', {
        service: 'session-cache',
        userId,
        terminatedCount,
        excludeSessionId
      })
      
      return terminatedCount

    } catch (error) {
      logger.error('终止用户会话失败', {
        service: 'session-cache',
        userId,
        error: error instanceof Error ? error.message : String(error)
      })
      return 0
    }
  }

  /**
   * 清理过期会话
   */
  async cleanupExpiredSessions(): Promise<number> {
    try {
      // 这里可以实现定期清理逻辑
      // 由于Redis会自动过期键，主要是清理集合中的引用
      let cleanedCount = 0
      
      logger.info('过期会话清理完成', {
        service: 'session-cache',
        cleanedCount
      })
      
      return cleanedCount

    } catch (error) {
      logger.error('清理过期会话失败', {
        service: 'session-cache',
        error: error instanceof Error ? error.message : String(error)
      })
      return 0
    }
  }

  /**
   * 验证会话是否存在且有效
   */
  async validateSession(sessionId: string): Promise<boolean> {
    try {
      const sessionData = await this.getSession(sessionId)
      return sessionData !== null && sessionData.isActive
    } catch (error) {
      return false
    }
  }
}

// 导出服务实例
export const sessionCacheService = new SessionCacheService()
export default sessionCacheService
