/**
 * 系统健康监控服务
 * 监控系统各组件的健康状态和性能指标
 */

import { EventEmitter } from 'events';
import { logger } from '@/config/logger';
import { prisma } from '@/config/database';
import { redisService } from '@/services/redis.service';
import { metricsCollector } from '@/services/metrics-collector.service';
import { alertManager, AlertSeverity } from '@/services/alert-manager.service';
import os from 'os';
import fs from 'fs/promises';

/**
 * 健康状态枚举
 */
export enum HealthStatus {
  HEALTHY = 'healthy',
  DEGRADED = 'degraded',
  UNHEALTHY = 'unhealthy',
  CRITICAL = 'critical'
}

/**
 * 组件健康检查结果
 */
export interface ComponentHealth {
  name: string;
  status: HealthStatus;
  responseTime: number;
  lastCheck: Date;
  message?: string;
  details?: Record<string, any>;
  error?: string;
}

/**
 * 系统健康检查结果
 */
export interface SystemHealth {
  overall: HealthStatus;
  timestamp: Date;
  uptime: number;
  components: ComponentHealth[];
  metrics: {
    cpu: {
      usage: number;
      loadAverage: number[];
    };
    memory: {
      total: number;
      used: number;
      free: number;
      usage: number;
    };
    disk: {
      total: number;
      used: number;
      free: number;
      usage: number;
    };
    network: {
      connections: number;
    };
  };
  alerts: {
    active: number;
    critical: number;
    high: number;
  };
}

/**
 * 健康检查配置
 */
export interface HealthCheckConfig {
  interval: number;           // 检查间隔（毫秒）
  timeout: number;           // 超时时间（毫秒）
  retries: number;           // 重试次数
  thresholds: {
    cpu: number;             // CPU使用率阈值
    memory: number;          // 内存使用率阈值
    disk: number;            // 磁盘使用率阈值
    responseTime: number;    // 响应时间阈值（毫秒）
  };
  enableAlerts: boolean;     // 是否启用告警
}

/**
 * 系统健康监控服务
 */
export class SystemHealthService extends EventEmitter {
  private config: HealthCheckConfig;
  private healthHistory: SystemHealth[] = [];
  private checkInterval?: NodeJS.Timeout;
  private readonly maxHistorySize = 1000;
  private startTime: Date;

  constructor(config: Partial<HealthCheckConfig> = {}) {
    super();
    
    this.config = {
      interval: 30000,        // 30秒
      timeout: 5000,          // 5秒
      retries: 3,
      thresholds: {
        cpu: 80,              // 80%
        memory: 85,           // 85%
        disk: 90,             // 90%
        responseTime: 1000    // 1秒
      },
      enableAlerts: true,
      ...config
    };

    this.startTime = new Date();
    this.startHealthChecks();
  }

  /**
   * 启动健康检查
   */
  private startHealthChecks(): void {
    this.checkInterval = setInterval(async () => {
      try {
        const health = await this.performHealthCheck();
        this.processHealthResult(health);
      } catch (error) {
        logger.error('健康检查执行失败', {
          error: error instanceof Error ? error.message : String(error)
        });
      }
    }, this.config.interval);

    logger.info('系统健康监控已启动', {
      interval: this.config.interval,
      enableAlerts: this.config.enableAlerts
    });
  }

  /**
   * 执行完整的健康检查
   */
  async performHealthCheck(): Promise<SystemHealth> {
    const startTime = Date.now();
    
    // 并行执行各组件健康检查
    const componentChecks = await Promise.allSettled([
      this.checkDatabase(),
      this.checkRedis(),
      this.checkFileSystem(),
      this.checkMemory(),
      this.checkCPU()
    ]);

    const components: ComponentHealth[] = componentChecks.map((result, index) => {
      const componentNames = ['database', 'redis', 'filesystem', 'memory', 'cpu'];
      
      if (result.status === 'fulfilled') {
        return result.value;
      } else {
        return {
          name: componentNames[index],
          status: HealthStatus.CRITICAL,
          responseTime: Date.now() - startTime,
          lastCheck: new Date(),
          error: result.reason instanceof Error ? result.reason.message : String(result.reason)
        };
      }
    });

    // 获取系统指标
    const metrics = await this.collectSystemMetrics();

    // 获取告警统计
    const alertStats = alertManager.getAlertStats();
    const alerts = {
      active: alertStats.active,
      critical: alertStats.bySeverity.critical || 0,
      high: alertStats.bySeverity.high || 0
    };

    // 计算整体健康状态
    const overall = this.calculateOverallHealth(components, metrics, alerts);

    const health: SystemHealth = {
      overall,
      timestamp: new Date(),
      uptime: Date.now() - this.startTime.getTime(),
      components,
      metrics,
      alerts
    };

    return health;
  }

  /**
   * 检查数据库健康状态
   */
  private async checkDatabase(): Promise<ComponentHealth> {
    const startTime = Date.now();
    
    try {
      // 执行简单查询测试连接
      await prisma.$queryRaw`SELECT 1`;
      
      const responseTime = Date.now() - startTime;
      const status = responseTime > this.config.thresholds.responseTime 
        ? HealthStatus.DEGRADED 
        : HealthStatus.HEALTHY;

      return {
        name: 'database',
        status,
        responseTime,
        lastCheck: new Date(),
        message: status === HealthStatus.HEALTHY ? '数据库连接正常' : '数据库响应较慢',
        details: {
          responseTime: `${responseTime}ms`,
          threshold: `${this.config.thresholds.responseTime}ms`
        }
      };

    } catch (error) {
      return {
        name: 'database',
        status: HealthStatus.CRITICAL,
        responseTime: Date.now() - startTime,
        lastCheck: new Date(),
        message: '数据库连接失败',
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * 检查Redis健康状态
   */
  private async checkRedis(): Promise<ComponentHealth> {
    const startTime = Date.now();
    
    try {
      // 测试Redis连接
      const isReady = redisService.isReady();
      if (!isReady) {
        throw new Error('Redis连接未就绪');
      }

      // 执行ping测试
      await redisService.ping();
      
      const responseTime = Date.now() - startTime;
      const status = responseTime > this.config.thresholds.responseTime 
        ? HealthStatus.DEGRADED 
        : HealthStatus.HEALTHY;

      return {
        name: 'redis',
        status,
        responseTime,
        lastCheck: new Date(),
        message: status === HealthStatus.HEALTHY ? 'Redis连接正常' : 'Redis响应较慢',
        details: {
          responseTime: `${responseTime}ms`,
          isReady
        }
      };

    } catch (error) {
      return {
        name: 'redis',
        status: HealthStatus.CRITICAL,
        responseTime: Date.now() - startTime,
        lastCheck: new Date(),
        message: 'Redis连接失败',
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * 检查文件系统健康状态
   */
  private async checkFileSystem(): Promise<ComponentHealth> {
    const startTime = Date.now();
    
    try {
      // 检查日志目录是否可写
      const testFile = './logs/health-check.tmp';
      await fs.writeFile(testFile, 'health check');
      await fs.unlink(testFile);
      
      const responseTime = Date.now() - startTime;

      return {
        name: 'filesystem',
        status: HealthStatus.HEALTHY,
        responseTime,
        lastCheck: new Date(),
        message: '文件系统正常',
        details: {
          responseTime: `${responseTime}ms`
        }
      };

    } catch (error) {
      return {
        name: 'filesystem',
        status: HealthStatus.CRITICAL,
        responseTime: Date.now() - startTime,
        lastCheck: new Date(),
        message: '文件系统访问失败',
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * 检查内存健康状态
   */
  private async checkMemory(): Promise<ComponentHealth> {
    const startTime = Date.now();
    
    try {
      const memoryUsage = process.memoryUsage();
      const systemMemory = {
        total: os.totalmem(),
        free: os.freemem()
      };
      
      const processMemoryUsage = (memoryUsage.heapUsed / memoryUsage.heapTotal) * 100;
      const systemMemoryUsage = ((systemMemory.total - systemMemory.free) / systemMemory.total) * 100;
      
      let status = HealthStatus.HEALTHY;
      if (systemMemoryUsage > this.config.thresholds.memory) {
        status = HealthStatus.CRITICAL;
      } else if (systemMemoryUsage > this.config.thresholds.memory * 0.8) {
        status = HealthStatus.DEGRADED;
      }

      return {
        name: 'memory',
        status,
        responseTime: Date.now() - startTime,
        lastCheck: new Date(),
        message: `系统内存使用率: ${systemMemoryUsage.toFixed(1)}%`,
        details: {
          processMemoryUsage: `${processMemoryUsage.toFixed(1)}%`,
          systemMemoryUsage: `${systemMemoryUsage.toFixed(1)}%`,
          heapUsed: `${Math.round(memoryUsage.heapUsed / 1024 / 1024)}MB`,
          heapTotal: `${Math.round(memoryUsage.heapTotal / 1024 / 1024)}MB`,
          systemTotal: `${Math.round(systemMemory.total / 1024 / 1024 / 1024)}GB`,
          systemFree: `${Math.round(systemMemory.free / 1024 / 1024 / 1024)}GB`
        }
      };

    } catch (error) {
      return {
        name: 'memory',
        status: HealthStatus.CRITICAL,
        responseTime: Date.now() - startTime,
        lastCheck: new Date(),
        message: '内存检查失败',
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * 检查CPU健康状态
   */
  private async checkCPU(): Promise<ComponentHealth> {
    const startTime = Date.now();
    
    try {
      const cpuUsage = process.cpuUsage();
      const loadAverage = os.loadavg();
      
      // 简化的CPU使用率计算
      const cpuPercent = Math.min(100, (cpuUsage.user + cpuUsage.system) / 1000000 * 100);
      
      let status = HealthStatus.HEALTHY;
      if (cpuPercent > this.config.thresholds.cpu) {
        status = HealthStatus.CRITICAL;
      } else if (cpuPercent > this.config.thresholds.cpu * 0.8) {
        status = HealthStatus.DEGRADED;
      }

      return {
        name: 'cpu',
        status,
        responseTime: Date.now() - startTime,
        lastCheck: new Date(),
        message: `CPU使用率: ${cpuPercent.toFixed(1)}%`,
        details: {
          usage: `${cpuPercent.toFixed(1)}%`,
          loadAverage: loadAverage.map(load => load.toFixed(2)),
          cores: os.cpus().length
        }
      };

    } catch (error) {
      return {
        name: 'cpu',
        status: HealthStatus.CRITICAL,
        responseTime: Date.now() - startTime,
        lastCheck: new Date(),
        message: 'CPU检查失败',
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * 收集系统指标
   */
  private async collectSystemMetrics(): Promise<SystemHealth['metrics']> {
    const memoryUsage = process.memoryUsage();
    const systemMemory = {
      total: os.totalmem(),
      free: os.freemem()
    };
    
    const cpuUsage = process.cpuUsage();
    const loadAverage = os.loadavg();

    // 简化的磁盘使用率（实际应用中可能需要更复杂的实现）
    const diskUsage = {
      total: 100 * 1024 * 1024 * 1024, // 假设100GB
      used: 50 * 1024 * 1024 * 1024,   // 假设50GB
      free: 50 * 1024 * 1024 * 1024    // 假设50GB
    };

    return {
      cpu: {
        usage: Math.min(100, (cpuUsage.user + cpuUsage.system) / 1000000 * 100),
        loadAverage
      },
      memory: {
        total: systemMemory.total,
        used: systemMemory.total - systemMemory.free,
        free: systemMemory.free,
        usage: ((systemMemory.total - systemMemory.free) / systemMemory.total) * 100
      },
      disk: {
        total: diskUsage.total,
        used: diskUsage.used,
        free: diskUsage.free,
        usage: (diskUsage.used / diskUsage.total) * 100
      },
      network: {
        connections: 0 // 简化实现
      }
    };
  }

  /**
   * 计算整体健康状态
   */
  private calculateOverallHealth(
    components: ComponentHealth[], 
    metrics: SystemHealth['metrics'],
    alerts: SystemHealth['alerts']
  ): HealthStatus {
    // 检查是否有关键组件不健康
    const criticalComponents = components.filter(c => c.status === HealthStatus.CRITICAL);
    if (criticalComponents.length > 0) {
      return HealthStatus.CRITICAL;
    }

    // 检查是否有严重告警
    if (alerts.critical > 0) {
      return HealthStatus.CRITICAL;
    }

    // 检查系统资源使用率
    if (metrics.cpu.usage > this.config.thresholds.cpu || 
        metrics.memory.usage > this.config.thresholds.memory ||
        metrics.disk.usage > this.config.thresholds.disk) {
      return HealthStatus.UNHEALTHY;
    }

    // 检查是否有组件性能下降
    const degradedComponents = components.filter(c => c.status === HealthStatus.DEGRADED);
    if (degradedComponents.length > 0 || alerts.high > 5) {
      return HealthStatus.DEGRADED;
    }

    return HealthStatus.HEALTHY;
  }

  /**
   * 处理健康检查结果
   */
  private processHealthResult(health: SystemHealth): void {
    // 添加到历史记录
    this.healthHistory.push(health);
    if (this.healthHistory.length > this.maxHistorySize) {
      this.healthHistory.shift();
    }

    // 记录指标
    metricsCollector.setGauge('system_health_overall', this.healthStatusToNumber(health.overall));
    metricsCollector.setGauge('system_cpu_usage', health.metrics.cpu.usage);
    metricsCollector.setGauge('system_memory_usage', health.metrics.memory.usage);
    metricsCollector.setGauge('system_disk_usage', health.metrics.disk.usage);

    // 发出健康状态事件
    this.emit('healthCheck', health);

    // 如果状态不健康，发出告警
    if (this.config.enableAlerts && health.overall !== HealthStatus.HEALTHY) {
      this.createHealthAlert(health);
    }

    logger.debug('系统健康检查完成', {
      overall: health.overall,
      components: health.components.length,
      unhealthyComponents: health.components.filter(c => c.status !== HealthStatus.HEALTHY).length
    });
  }

  /**
   * 创建健康告警
   */
  private createHealthAlert(health: SystemHealth): void {
    const unhealthyComponents = health.components.filter(c => c.status !== HealthStatus.HEALTHY);
    
    if (unhealthyComponents.length === 0) return;

    const severity = health.overall === HealthStatus.CRITICAL 
      ? AlertSeverity.CRITICAL 
      : health.overall === HealthStatus.UNHEALTHY 
        ? AlertSeverity.HIGH 
        : AlertSeverity.MEDIUM;

    alertManager.createAlert({
      title: `系统健康状态异常: ${health.overall}`,
      description: `检测到${unhealthyComponents.length}个组件状态异常`,
      severity,
      source: 'system-health',
      category: 'infrastructure',
      metadata: {
        overall: health.overall,
        unhealthyComponents: unhealthyComponents.map(c => ({
          name: c.name,
          status: c.status,
          message: c.message,
          error: c.error
        })),
        metrics: health.metrics
      },
      fingerprint: `system_health_${health.overall}`
    });
  }

  /**
   * 健康状态转数字
   */
  private healthStatusToNumber(status: HealthStatus): number {
    switch (status) {
      case HealthStatus.HEALTHY: return 1;
      case HealthStatus.DEGRADED: return 0.7;
      case HealthStatus.UNHEALTHY: return 0.3;
      case HealthStatus.CRITICAL: return 0;
      default: return 0;
    }
  }

  /**
   * 获取当前健康状态
   */
  getCurrentHealth(): SystemHealth | null {
    return this.healthHistory.length > 0 
      ? this.healthHistory[this.healthHistory.length - 1] 
      : null;
  }

  /**
   * 获取健康历史
   */
  getHealthHistory(limit = 100): SystemHealth[] {
    return this.healthHistory.slice(-limit);
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<HealthCheckConfig>): void {
    this.config = { ...this.config, ...newConfig };
    
    // 重启健康检查
    if (this.checkInterval) {
      clearInterval(this.checkInterval);
      this.startHealthChecks();
    }

    logger.info('系统健康监控配置已更新', newConfig);
  }

  /**
   * 停止健康检查
   */
  stop(): void {
    if (this.checkInterval) {
      clearInterval(this.checkInterval);
      this.checkInterval = undefined;
    }
    
    this.removeAllListeners();
    logger.info('系统健康监控已停止');
  }
}

// 创建单例实例
export const systemHealth = new SystemHealthService();
