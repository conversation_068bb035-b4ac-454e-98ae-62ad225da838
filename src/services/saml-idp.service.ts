/**
 * SAML 2.0 Identity Provider 服务
 * 基于 @node-saml/node-saml 实现完整的 SAML IdP 功能
 */

import { SAML } from '@node-saml/node-saml';
import { config, getServerUrl } from '@/config';
import { logger, logAuditEvent } from '@/config/logger';
import { cacheService } from './cache.service';
import { prisma } from '@/config/database';
import crypto from 'crypto';
import { v4 as uuidv4 } from 'uuid';
import fs from 'fs';
import path from 'path';
import * as xml2js from 'xml2js';
import { create } from 'xmlbuilder2';

/**
 * SAML IdP 配置接口
 */
interface SAMLIdPConfig {
  issuer: string;
  ssoServiceUrl: string;
  sloServiceUrl: string;
  metadataUrl: string;
  certificate: string;
  privateKey: string;
  signatureAlgorithm: string;
  digestAlgorithm: string;
  assertionLifetime: number;
  clockSkew: number;
  nameIdFormat: string;
  attributeMapping: Record<string, string>;
}

/**
 * SAML 服务提供商配置
 */
interface SAMLServiceProvider {
  id: string;
  entityId: string;
  name: string;
  description?: string;
  acsUrl: string;
  sloUrl?: string;
  certificate?: string;
  wantAssertionsSigned: boolean;
  wantNameId: boolean;
  signMetadata: boolean;
  requiredAttributes: string[];
  optionalAttributes: string[];
  isActive: boolean;
}

/**
 * SAML 认证请求
 */
interface SAMLAuthRequest {
  id: string;
  issuer: string;
  destination: string;
  acsUrl: string;
  forceAuthn?: boolean;
  isPassive?: boolean;
  nameIdPolicy?: {
    format: string;
    allowCreate: boolean;
  };
  requestedAuthnContext?: {
    authnContextClassRef: string[];
    comparison: string;
  };
}

export class SAMLIdPService {
  private idpConfig: SAMLIdPConfig;
  private samlInstances: Map<string, SAML> = new Map();

  constructor() {
    this.idpConfig = this.initializeConfig();
  }

  /**
   * 初始化 SAML IdP 配置
   */
  private initializeConfig(): SAMLIdPConfig {
    const baseUrl = getServerUrl();
    
    return {
      issuer: config.saml?.issuer || baseUrl,
      ssoServiceUrl: `${baseUrl}/saml/sso`,
      sloServiceUrl: `${baseUrl}/saml/slo`,
      metadataUrl: `${baseUrl}/saml/metadata`,
      
      // 证书配置（生产环境应从安全存储获取）
      certificate: this.loadCertificate(),
      privateKey: this.loadPrivateKey(),
      
      // 签名和加密配置
      signatureAlgorithm: 'http://www.w3.org/2001/04/xmldsig-more#rsa-sha256',
      digestAlgorithm: 'http://www.w3.org/2001/04/xmlenc#sha256',
      
      // 断言配置
      assertionLifetime: 300, // 5分钟
      clockSkew: 300, // 5分钟
      
      // 名称ID格式
      nameIdFormat: 'urn:oasis:names:tc:SAML:2.0:nameid-format:persistent',
      
      // 属性映射
      attributeMapping: {
        'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress': 'email',
        'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name': 'name',
        'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/givenname': 'firstName',
        'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/surname': 'lastName',
        'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/upn': 'username'
      }
    };
  }

  /**
   * 加载证书
   */
  private loadCertificate(): string {
    try {
      const certPath = path.join(process.cwd(), 'certs', 'saml-idp.crt');
      if (fs.existsSync(certPath)) {
        return fs.readFileSync(certPath, 'utf8');
      }
      
      // 如果证书不存在，生成自签名证书（仅用于开发）
      if (config.server.nodeEnv === 'development') {
        return this.generateSelfSignedCertificate().certificate;
      }
      
      throw new Error('SAML IdP 证书未找到');
    } catch (error) {
      logger.error('加载 SAML IdP 证书失败', { error });
      throw error;
    }
  }

  /**
   * 加载私钥
   */
  private loadPrivateKey(): string {
    try {
      const keyPath = path.join(process.cwd(), 'certs', 'saml-idp.key');
      if (fs.existsSync(keyPath)) {
        return fs.readFileSync(keyPath, 'utf8');
      }
      
      // 如果私钥不存在，生成自签名证书（仅用于开发）
      if (config.server.nodeEnv === 'development') {
        return this.generateSelfSignedCertificate().privateKey;
      }
      
      throw new Error('SAML IdP 私钥未找到');
    } catch (error) {
      logger.error('加载 SAML IdP 私钥失败', { error });
      throw error;
    }
  }

  /**
   * 生成自签名证书（仅用于开发环境）
   */
  private generateSelfSignedCertificate(): { certificate: string; privateKey: string } {
    const forge = require('node-forge');
    
    // 生成密钥对
    const keys = forge.pki.rsa.generateKeyPair(2048);
    
    // 创建证书
    const cert = forge.pki.createCertificate();
    cert.publicKey = keys.publicKey;
    cert.serialNumber = '01';
    cert.validity.notBefore = new Date();
    cert.validity.notAfter = new Date();
    cert.validity.notAfter.setFullYear(cert.validity.notBefore.getFullYear() + 1);
    
    const attrs = [{
      name: 'commonName',
      value: 'SAML IdP'
    }, {
      name: 'countryName',
      value: 'US'
    }, {
      shortName: 'ST',
      value: 'Virginia'
    }, {
      name: 'localityName',
      value: 'Blacksburg'
    }, {
      name: 'organizationName',
      value: 'Test'
    }, {
      shortName: 'OU',
      value: 'Test'
    }];
    
    cert.setSubject(attrs);
    cert.setIssuer(attrs);
    cert.sign(keys.privateKey);
    
    const certificate = forge.pki.certificateToPem(cert);
    const privateKey = forge.pki.privateKeyToPem(keys.privateKey);
    
    // 保存到文件系统
    const certsDir = path.join(process.cwd(), 'certs');
    if (!fs.existsSync(certsDir)) {
      fs.mkdirSync(certsDir, { recursive: true });
    }
    
    fs.writeFileSync(path.join(certsDir, 'saml-idp.crt'), certificate);
    fs.writeFileSync(path.join(certsDir, 'saml-idp.key'), privateKey);
    
    logger.info('生成 SAML IdP 自签名证书成功');
    
    return { certificate, privateKey };
  }

  /**
   * 获取或创建 SAML 实例
   */
  private async getSAMLInstance(spEntityId: string): Promise<SAML> {
    if (this.samlInstances.has(spEntityId)) {
      return this.samlInstances.get(spEntityId)!;
    }

    // 获取服务提供商配置
    const sp = await this.getServiceProvider(spEntityId);
    if (!sp) {
      throw new Error(`未找到服务提供商: ${spEntityId}`);
    }

    // 创建 SAML 配置
    const samlConfig = {
      // IdP 配置
      issuer: this.idpConfig.issuer,
      entryPoint: this.idpConfig.ssoServiceUrl,
      logoutUrl: this.idpConfig.sloServiceUrl,

      // SP 配置
      callbackUrl: sp.acsUrl,
      audience: sp.entityId,

      // 证书配置
      idpCert: this.cleanCertificate(this.idpConfig.certificate),
      cert: this.cleanCertificate(this.idpConfig.certificate),
      privateKey: this.idpConfig.privateKey,

      // 签名配置
      signatureAlgorithm: 'sha256' as any,
      digestAlgorithm: 'sha256' as any,

      // 其他配置
      identifierFormat: this.idpConfig.nameIdFormat,
      wantAssertionsSigned: sp.wantAssertionsSigned,
      acceptedClockSkewMs: this.idpConfig.clockSkew * 1000,
      maxAssertionAgeMs: this.idpConfig.assertionLifetime * 1000
    };

    const samlInstance = new SAML(samlConfig);
    this.samlInstances.set(spEntityId, samlInstance);
    
    return samlInstance;
  }

  /**
   * 清理证书格式
   */
  private cleanCertificate(cert: string): string {
    return cert
      .replace(/-----BEGIN CERTIFICATE-----/, '')
      .replace(/-----END CERTIFICATE-----/, '')
      .replace(/\n/g, '')
      .replace(/\r/g, '');
  }

  /**
   * 获取服务提供商配置
   */
  private async getServiceProvider(entityId: string): Promise<SAMLServiceProvider | null> {
    try {
      const application = await prisma.application.findFirst({
        where: {
          clientId: entityId,
          isActive: true
        }
      });

      if (!application || !application.samlConfig) {
        return null;
      }

      const samlConfig = application.samlConfig as any;
      
      return {
        id: application.id,
        entityId: application.clientId,
        name: application.name,
        description: application.description,
        acsUrl: samlConfig.acsUrl,
        sloUrl: samlConfig.sloUrl,
        certificate: samlConfig.certificate,
        wantAssertionsSigned: samlConfig.wantAssertionsSigned || false,
        wantNameId: samlConfig.wantNameId !== false,
        signMetadata: samlConfig.signMetadata || false,
        requiredAttributes: samlConfig.requiredAttributes || [],
        optionalAttributes: samlConfig.optionalAttributes || [],
        isActive: application.isActive
      };

    } catch (error) {
      logger.error('获取服务提供商配置失败', { error, entityId });
      return null;
    }
  }

  /**
   * 处理 SAML 认证请求
   */
  async handleAuthenticationRequest(
    samlRequest: string,
    relayState?: string,
    binding: 'HTTP-POST' | 'HTTP-Redirect' = 'HTTP-POST'
  ): Promise<{ requestId: string; spEntityId: string; acsUrl: string }> {
    try {
      // 解码 SAML 请求
      let decodedRequest: string;
      if (binding === 'HTTP-Redirect') {
        // HTTP-Redirect 绑定使用 deflate 压缩
        const buffer = Buffer.from(samlRequest, 'base64');
        const zlib = require('zlib');
        decodedRequest = zlib.inflateRawSync(buffer).toString('utf8');
      } else {
        // HTTP-POST 绑定使用 base64 编码
        decodedRequest = Buffer.from(samlRequest, 'base64').toString('utf8');
      }

      // 解析 XML
      const parser = new xml2js.Parser({ explicitArray: false });
      const parsed = await parser.parseStringPromise(decodedRequest);
      
      const authnRequest = parsed['samlp:AuthnRequest'] || parsed.AuthnRequest;
      if (!authnRequest) {
        throw new Error('无效的 SAML 认证请求');
      }

      const requestId = authnRequest.$.ID;
      const spEntityId = authnRequest.$.Issuer || authnRequest.Issuer;
      const acsUrl = authnRequest.$.AssertionConsumerServiceURL;

      // 验证服务提供商
      const sp = await this.getServiceProvider(spEntityId);
      if (!sp) {
        throw new Error(`未知的服务提供商: ${spEntityId}`);
      }

      // 缓存请求信息
      await cacheService.setOAuthState(requestId, {
        requestId,
        spEntityId,
        acsUrl: acsUrl || sp.acsUrl,
        relayState,
        binding,
        timestamp: new Date().toISOString()
      });

      logger.info('SAML 认证请求处理成功', {
        requestId,
        spEntityId,
        acsUrl,
        binding
      });

      return {
        requestId,
        spEntityId,
        acsUrl: acsUrl || sp.acsUrl
      };

    } catch (error) {
      logger.error('SAML 认证请求处理失败', { error, samlRequest });
      throw error;
    }
  }

  /**
   * 生成 SAML 认证响应
   */
  async generateAuthenticationResponse(
    requestId: string,
    userId: string,
    attributes?: Record<string, any>
  ): Promise<{ samlResponse: string; relayState?: string }> {
    try {
      // 获取缓存的请求信息
      const requestInfo = await cacheService.getOAuthState(requestId);
      if (!requestInfo) {
        throw new Error('请求信息已过期或不存在');
      }

      // 获取用户信息
      const user = await prisma.user.findUnique({
        where: { id: userId }
      });

      if (!user) {
        throw new Error('用户不存在');
      }

      // 构建用户属性
      const userAttributes = this.buildUserAttributes(user, attributes);

      // 生成断言
      const assertion = this.generateAssertion(
        requestId,
        requestInfo.spEntityId,
        requestInfo.acsUrl,
        user,
        userAttributes
      );

      // 生成 SAML 响应
      const response = this.generateSAMLResponse(
        requestId,
        requestInfo.spEntityId,
        requestInfo.acsUrl,
        assertion
      );

      // 签名响应
      const signedResponse = this.signSAMLResponse(response);

      // 记录审计日志
      logAuditEvent('saml_sso_success', 'authentication', userId, {
        spEntityId: requestInfo.spEntityId,
        requestId,
        acsUrl: requestInfo.acsUrl
      });

      logger.info('SAML 认证响应生成成功', {
        userId,
        spEntityId: requestInfo.spEntityId,
        requestId
      });

      return {
        samlResponse: Buffer.from(signedResponse).toString('base64'),
        relayState: requestInfo.relayState
      };

    } catch (error) {
      logger.error('SAML 认证响应生成失败', { error, requestId, userId });
      throw error;
    }
  }

  /**
   * 构建用户属性
   */
  private buildUserAttributes(user: any, customAttributes?: Record<string, any>): Record<string, string> {
    const attributes: Record<string, string> = {
      'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress': user.email,
      'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name': user.profile?.displayName || user.username,
      'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/upn': user.username
    };

    if (user.profile?.firstName) {
      attributes['http://schemas.xmlsoap.org/ws/2005/05/identity/claims/givenname'] = user.profile.firstName;
    }

    if (user.profile?.lastName) {
      attributes['http://schemas.xmlsoap.org/ws/2005/05/identity/claims/surname'] = user.profile.lastName;
    }

    // 添加自定义属性
    if (customAttributes) {
      Object.assign(attributes, customAttributes);
    }

    return attributes;
  }

  /**
   * 生成 SAML 断言
   */
  private generateAssertion(
    requestId: string,
    spEntityId: string,
    acsUrl: string,
    user: any,
    attributes: Record<string, string>
  ): string {
    const now = new Date();
    const notBefore = new Date(now.getTime() - this.idpConfig.clockSkew * 1000);
    const notOnOrAfter = new Date(now.getTime() + this.idpConfig.assertionLifetime * 1000);
    const assertionId = `_${uuidv4()}`;
    const sessionIndex = uuidv4();

    const assertion = create({ version: '1.0', encoding: 'UTF-8' })
      .ele('saml:Assertion', {
        'xmlns:saml': 'urn:oasis:names:tc:SAML:2.0:assertion',
        'ID': assertionId,
        'IssueInstant': now.toISOString(),
        'Version': '2.0'
      })
      .ele('saml:Issuer').txt(this.idpConfig.issuer).up()
      .ele('saml:Subject')
        .ele('saml:NameID', {
          'Format': this.idpConfig.nameIdFormat
        }).txt(user.id).up()
        .ele('saml:SubjectConfirmation', {
          'Method': 'urn:oasis:names:tc:SAML:2.0:cm:bearer'
        })
          .ele('saml:SubjectConfirmationData', {
            'InResponseTo': requestId,
            'NotOnOrAfter': notOnOrAfter.toISOString(),
            'Recipient': acsUrl
          }).up()
        .up()
      .up()
      .ele('saml:Conditions', {
        'NotBefore': notBefore.toISOString(),
        'NotOnOrAfter': notOnOrAfter.toISOString()
      })
        .ele('saml:AudienceRestriction')
          .ele('saml:Audience').txt(spEntityId).up()
        .up()
      .up()
      .ele('saml:AuthnStatement', {
        'AuthnInstant': now.toISOString(),
        'SessionIndex': sessionIndex
      })
        .ele('saml:AuthnContext')
          .ele('saml:AuthnContextClassRef').txt('urn:oasis:names:tc:SAML:2.0:ac:classes:PasswordProtectedTransport').up()
        .up()
      .up();

    // 添加属性声明
    if (Object.keys(attributes).length > 0) {
      const attributeStatement = assertion.ele('saml:AttributeStatement');

      for (const [name, value] of Object.entries(attributes)) {
        attributeStatement
          .ele('saml:Attribute', {
            'Name': name,
            'NameFormat': 'urn:oasis:names:tc:SAML:2.0:attrname-format:uri'
          })
            .ele('saml:AttributeValue', {
              'xmlns:xsi': 'http://www.w3.org/2001/XMLSchema-instance',
              'xsi:type': 'xs:string'
            }).txt(value).up()
          .up();
      }
    }

    return assertion.end({ prettyPrint: true });
  }

  /**
   * 生成 SAML 响应
   */
  private generateSAMLResponse(
    requestId: string,
    spEntityId: string,
    acsUrl: string,
    assertion: string
  ): string {
    const now = new Date();
    const responseId = `_${uuidv4()}`;

    const response = create({ version: '1.0', encoding: 'UTF-8' })
      .ele('samlp:Response', {
        'xmlns:samlp': 'urn:oasis:names:tc:SAML:2.0:protocol',
        'xmlns:saml': 'urn:oasis:names:tc:SAML:2.0:assertion',
        'ID': responseId,
        'InResponseTo': requestId,
        'IssueInstant': now.toISOString(),
        'Destination': acsUrl,
        'Version': '2.0'
      })
      .ele('saml:Issuer').txt(this.idpConfig.issuer).up()
      .ele('samlp:Status')
        .ele('samlp:StatusCode', {
          'Value': 'urn:oasis:names:tc:SAML:2.0:status:Success'
        }).up()
      .up()
      .ele('saml:Assertion').txt(assertion).up()
      .end({ prettyPrint: true });

    return response;
  }

  /**
   * 签名 SAML 响应
   */
  private signSAMLResponse(response: string): string {
    // 这里应该使用适当的 XML 签名库
    // 为了简化，暂时返回未签名的响应
    // 在生产环境中，必须实现正确的 XML 数字签名

    logger.warn('SAML 响应未签名 - 生产环境中必须实现 XML 数字签名');
    return response;
  }

  /**
   * 生成 IdP 元数据
   */
  generateMetadata(): string {
    const now = new Date();
    const validUntil = new Date(now.getTime() + 24 * 60 * 60 * 1000); // 24小时后过期

    const metadata = create({ version: '1.0', encoding: 'UTF-8' })
      .ele('md:EntityDescriptor', {
        'xmlns:md': 'urn:oasis:names:tc:SAML:2.0:metadata',
        'entityID': this.idpConfig.issuer,
        'validUntil': validUntil.toISOString(),
        'cacheDuration': 'PT24H'
      })
      .ele('md:IDPSSODescriptor', {
        'WantAuthnRequestsSigned': 'false',
        'protocolSupportEnumeration': 'urn:oasis:names:tc:SAML:2.0:protocol'
      })
        .ele('md:KeyDescriptor', { 'use': 'signing' })
          .ele('ds:KeyInfo', { 'xmlns:ds': 'http://www.w3.org/2000/09/xmldsig#' })
            .ele('ds:X509Data')
              .ele('ds:X509Certificate').txt(this.cleanCertificate(this.idpConfig.certificate)).up()
            .up()
          .up()
        .up()
        .ele('md:NameIDFormat').txt(this.idpConfig.nameIdFormat).up()
        .ele('md:SingleSignOnService', {
          'Binding': 'urn:oasis:names:tc:SAML:2.0:bindings:HTTP-POST',
          'Location': this.idpConfig.ssoServiceUrl
        }).up()
        .ele('md:SingleSignOnService', {
          'Binding': 'urn:oasis:names:tc:SAML:2.0:bindings:HTTP-Redirect',
          'Location': this.idpConfig.ssoServiceUrl
        }).up()
        .ele('md:SingleLogoutService', {
          'Binding': 'urn:oasis:names:tc:SAML:2.0:bindings:HTTP-POST',
          'Location': this.idpConfig.sloServiceUrl
        }).up()
        .ele('md:SingleLogoutService', {
          'Binding': 'urn:oasis:names:tc:SAML:2.0:bindings:HTTP-Redirect',
          'Location': this.idpConfig.sloServiceUrl
        }).up()
      .up()
      .end({ prettyPrint: true });

    return metadata;
  }

  /**
   * 处理单点登出请求
   */
  async handleLogoutRequest(
    samlRequest: string,
    relayState?: string,
    binding: 'HTTP-POST' | 'HTTP-Redirect' = 'HTTP-POST'
  ): Promise<{ logoutResponse: string; relayState?: string }> {
    try {
      // 解码 SAML 登出请求
      let decodedRequest: string;
      if (binding === 'HTTP-Redirect') {
        const buffer = Buffer.from(samlRequest, 'base64');
        const zlib = require('zlib');
        decodedRequest = zlib.inflateRawSync(buffer).toString('utf8');
      } else {
        decodedRequest = Buffer.from(samlRequest, 'base64').toString('utf8');
      }

      // 解析 XML
      const parser = new xml2js.Parser({ explicitArray: false });
      const parsed = await parser.parseStringPromise(decodedRequest);

      const logoutRequest = parsed['samlp:LogoutRequest'] || parsed.LogoutRequest;
      if (!logoutRequest) {
        throw new Error('无效的 SAML 登出请求');
      }

      const requestId = logoutRequest.$.ID;
      const spEntityId = logoutRequest.$.Issuer || logoutRequest.Issuer;
      const nameId = logoutRequest['saml:NameID'] || logoutRequest.NameID;

      // 生成登出响应
      const logoutResponse = this.generateLogoutResponse(requestId, spEntityId);

      logger.info('SAML 登出请求处理成功', {
        requestId,
        spEntityId,
        nameId
      });

      return {
        logoutResponse: Buffer.from(logoutResponse).toString('base64'),
        relayState
      };

    } catch (error) {
      logger.error('SAML 登出请求处理失败', { error, samlRequest });
      throw error;
    }
  }

  /**
   * 生成登出响应
   */
  private generateLogoutResponse(requestId: string, spEntityId: string): string {
    const now = new Date();
    const responseId = `_${uuidv4()}`;

    const response = create({ version: '1.0', encoding: 'UTF-8' })
      .ele('samlp:LogoutResponse', {
        'xmlns:samlp': 'urn:oasis:names:tc:SAML:2.0:protocol',
        'xmlns:saml': 'urn:oasis:names:tc:SAML:2.0:assertion',
        'ID': responseId,
        'InResponseTo': requestId,
        'IssueInstant': now.toISOString(),
        'Version': '2.0'
      })
      .ele('saml:Issuer').txt(this.idpConfig.issuer).up()
      .ele('samlp:Status')
        .ele('samlp:StatusCode', {
          'Value': 'urn:oasis:names:tc:SAML:2.0:status:Success'
        }).up()
      .up()
      .end({ prettyPrint: true });

    return response;
  }
}

// 导出单例实例
export const samlIdPService = new SAMLIdPService();
