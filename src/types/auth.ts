/**
 * 认证相关类型定义
 */

import { Request } from 'express';
import { User } from '@prisma/client';

/**
 * 认证用户信息
 */
export interface AuthUser {
  userId: string;
  email: string;
  sessionId?: string;
  roles?: string[];
  permissions?: string[];
}

/**
 * 已认证的请求接口
 */
export interface AuthenticatedRequest extends Request {
  user?: AuthUser;
}

/**
 * 登录请求数据
 */
export interface LoginRequest {
  username: string;
  password: string;
  rememberMe?: boolean;
}

/**
 * 注册请求数据
 */
export interface RegisterRequest {
  email: string;
  password: string;
  nickname?: string;
  firstName?: string;
  lastName?: string;
}

/**
 * 令牌对
 */
export interface TokenPair {
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
}

/**
 * 认证结果
 */
export interface AuthResult {
  user: {
    id: string;
    email: string;
    nickname?: string;
    avatar?: string;
    isEmailVerified: boolean;
  };
  tokens: TokenPair;
  sessionId: string;
  requiresMfa?: boolean;
}

/**
 * MFA验证请求
 */
export interface MfaVerifyRequest {
  code: string;
  deviceId?: string;
  method: 'totp' | 'email' | 'sms';
}

/**
 * 密码重置请求
 */
export interface PasswordResetRequest {
  token: string;
  newPassword: string;
}

/**
 * 会话信息
 */
export interface SessionInfo {
  id: string;
  deviceInfo: {
    userAgent: string;
    platform: string;
    browser: string;
  };
  ipAddress: string;
  lastAccessedAt: Date;
  expiresAt: Date;
  isCurrent: boolean;
}
