/**
 * 数据库相关类型定义
 * 扩展Prisma生成的类型，添加业务逻辑相关的类型
 */

import { 
  User, 
  Application, 
  Session, 
  MFADevice, 
  FederatedIdentity, 
  AuditLog, 
  RiskAssessment, 
  Role, 
  UserRole, 
  RefreshToken, 
  SystemConfig 
} from '@prisma/client';

// 用户相关类型
export type UserWithRoles = User & {
  userRoles: (UserRole & {
    role: Role;
  })[];
};

export type UserWithMFA = User & {
  mfaDevices: MFADevice[];
};

export type UserWithFederatedIdentities = User & {
  federatedIdentities: FederatedIdentity[];
};

export type UserProfile = Omit<User, 'passwordHash' | 'createdAt' | 'updatedAt'>;

// 会话相关类型
export type SessionWithUser = Session & {
  user: User;
  application?: Application;
};

export type ActiveSession = Session & {
  user: Pick<User, 'id' | 'email' | 'nickname'>;
};

// 应用相关类型
export type ApplicationWithSessions = Application & {
  sessions: Session[];
};

// MFA相关类型
export type MFADeviceSecure = Omit<MFADevice, 'secret' | 'backupCodes'>;

export interface TOTPSetup {
  secret: string;
  qrCodeUri: string;
  backupCodes: string[];
}

// 风险评估相关类型
export interface RiskFactors {
  ipRisk: number;
  deviceRisk: number;
  behaviorRisk: number;
  locationRisk: number;
  timeRisk: number;
}

export interface RiskAssessmentResult {
  totalRisk: number;
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
  action: 'allow' | 'mfa_required' | 'deny';
  reason?: string;
  factors: RiskFactors;
}

// 审计日志相关类型
export interface AuditLogCreate {
  userId?: string;
  applicationId?: string;
  action: string;
  resource: string;
  details?: Record<string, any>;
  ipAddress?: string;
  userAgent?: string;
  success: boolean;
  errorMessage?: string;
}

// 系统配置相关类型
export interface PasswordPolicy {
  minLength: number;
  requireUppercase: boolean;
  requireLowercase: boolean;
  requireNumbers: boolean;
  requireSpecialChars: boolean;
  maxAge: number;
  historyCount: number;
}

export interface MFASettings {
  totpIssuer: string;
  totpDigits: number;
  totpPeriod: number;
  backupCodesCount: number;
  emailCodeExpiry: number;
  smsCodeExpiry: number;
}

export interface ZeroTrustConfig {
  enabled: boolean;
  riskThresholds: {
    low: number;
    medium: number;
    high: number;
  };
  factors: {
    ipRisk: { weight: number; enabled: boolean };
    deviceRisk: { weight: number; enabled: boolean };
    behaviorRisk: { weight: number; enabled: boolean };
    locationRisk: { weight: number; enabled: boolean };
    timeRisk: { weight: number; enabled: boolean };
  };
}

export interface SessionConfig {
  defaultTimeout: number;
  rememberMeDays: number;
  maxConcurrentSessions: number;
  extendOnActivity: boolean;
}

export interface EmailTemplates {
  verification: {
    subject: string;
    template: string;
  };
  passwordReset: {
    subject: string;
    template: string;
  };
  mfaCode: {
    subject: string;
    template: string;
  };
}

// 分页相关类型
export interface PaginationParams {
  page: number;
  limit: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface PaginatedResult<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// 搜索和过滤类型
export interface UserFilter {
  email?: string;
  username?: string;
  isActive?: boolean;
  isLocked?: boolean;
  emailVerified?: boolean;
  createdAfter?: Date;
  createdBefore?: Date;
}

export interface ApplicationFilter {
  name?: string;
  isActive?: boolean;
  supportedProtocols?: string[];
  createdAfter?: Date;
  createdBefore?: Date;
}

export interface AuditLogFilter {
  userId?: string;
  applicationId?: string;
  action?: string;
  resource?: string;
  success?: boolean;
  dateFrom?: Date;
  dateTo?: Date;
}

// 设备信息类型
export interface DeviceInfo {
  type: 'desktop' | 'mobile' | 'tablet' | 'unknown';
  os: string;
  browser: string;
  version: string;
  fingerprint: string;
}

// 地理位置信息类型
export interface LocationInfo {
  country: string;
  region: string;
  city: string;
  latitude?: number;
  longitude?: number;
  timezone: string;
}

// 导出所有Prisma类型
export {
  User,
  Application,
  Session,
  MFADevice,
  FederatedIdentity,
  AuditLog,
  RiskAssessment,
  Role,
  UserRole,
  RefreshToken,
  SystemConfig
};
