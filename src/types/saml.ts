/**
 * SAML 2.0 类型定义
 * 定义SAML协议相关的接口和类型
 */

export interface SAMLConfig {
  // 身份提供商配置
  issuer: string;
  ssoServiceUrl: string;
  sloServiceUrl?: string;
  metadataUrl: string;
  
  // 证书和密钥
  certificate: string;
  privateKey: string;
  
  // 签名和加密配置
  signatureAlgorithm: string;
  digestAlgorithm: string;
  encryptionAlgorithm?: string;
  
  // 断言配置
  assertionLifetime: number; // 秒
  clockSkew: number; // 秒
  
  // 属性映射
  attributeMapping: Record<string, string>;
  
  // 名称ID格式
  nameIdFormat: string;
  
  // 绑定类型
  binding: 'HTTP-POST' | 'HTTP-Redirect' | 'HTTP-Artifact';
}

export interface SAMLServiceProvider {
  id: string;
  entityId: string;
  name: string;
  description?: string;
  
  // SP端点
  acsUrl: string; // Assertion Consumer Service URL
  sloUrl?: string; // Single Logout URL
  
  // SP证书（用于加密断言）
  certificate?: string;
  
  // 配置选项
  wantAssertionsSigned: boolean;
  wantNameId: boolean;
  signMetadata: boolean;
  
  // 属性要求
  requiredAttributes: string[];
  optionalAttributes: string[];
  
  // 状态
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface SAMLRequest {
  id: string;
  issuer: string;
  destination: string;
  issueInstant: string;
  protocolBinding: string;
  acsUrl: string;
  nameIdPolicy?: {
    format: string;
    allowCreate: boolean;
  };
  requestedAuthnContext?: {
    comparison: string;
    authnContextClassRef: string[];
  };
  forceAuthn?: boolean;
  isPassive?: boolean;
}

export interface SAMLResponse {
  id: string;
  inResponseTo: string;
  issuer: string;
  destination: string;
  issueInstant: string;
  status: {
    statusCode: string;
    statusMessage?: string;
  };
  assertion?: SAMLAssertion;
}

export interface SAMLAssertion {
  id: string;
  issuer: string;
  subject: {
    nameId: {
      format: string;
      value: string;
    };
    subjectConfirmation: {
      method: string;
      data: {
        recipient: string;
        notOnOrAfter: string;
        inResponseTo: string;
      };
    };
  };
  conditions: {
    notBefore: string;
    notOnOrAfter: string;
    audienceRestriction: {
      audience: string;
    };
  };
  authnStatement: {
    authnInstant: string;
    sessionIndex: string;
    authnContext: {
      authnContextClassRef: string;
    };
  };
  attributeStatement?: {
    attributes: SAMLAttribute[];
  };
}

export interface SAMLAttribute {
  name: string;
  nameFormat?: string;
  friendlyName?: string;
  values: string[];
}

export interface SAMLMetadata {
  entityId: string;
  validUntil: string;
  cacheDuration: string;
  idpSsoDescriptor: {
    wantAuthnRequestsSigned: boolean;
    protocolSupportEnumeration: string;
    keyDescriptor: Array<{
      use: 'signing' | 'encryption';
      certificate: string;
    }>;
    nameIdFormat: string[];
    singleSignOnService: Array<{
      binding: string;
      location: string;
    }>;
    singleLogoutService?: Array<{
      binding: string;
      location: string;
    }>;
    attributeService?: Array<{
      binding: string;
      location: string;
    }>;
  };
}

export interface SAMLLogoutRequest {
  id: string;
  issuer: string;
  destination: string;
  issueInstant: string;
  nameId: {
    format: string;
    value: string;
  };
  sessionIndex?: string;
}

export interface SAMLLogoutResponse {
  id: string;
  inResponseTo: string;
  issuer: string;
  destination: string;
  issueInstant: string;
  status: {
    statusCode: string;
    statusMessage?: string;
  };
}

// SAML状态码常量
export const SAMLStatusCodes = {
  SUCCESS: 'urn:oasis:names:tc:SAML:2.0:status:Success',
  REQUESTER: 'urn:oasis:names:tc:SAML:2.0:status:Requester',
  RESPONDER: 'urn:oasis:names:tc:SAML:2.0:status:Responder',
  VERSION_MISMATCH: 'urn:oasis:names:tc:SAML:2.0:status:VersionMismatch',
  AUTHN_FAILED: 'urn:oasis:names:tc:SAML:2.0:status:AuthnFailed',
  INVALID_ATTR_NAME_OR_VALUE: 'urn:oasis:names:tc:SAML:2.0:status:InvalidAttrNameOrValue',
  INVALID_NAME_ID_POLICY: 'urn:oasis:names:tc:SAML:2.0:status:InvalidNameIDPolicy',
  NO_AUTHN_CONTEXT: 'urn:oasis:names:tc:SAML:2.0:status:NoAuthnContext',
  NO_AVAILABLE_IDP: 'urn:oasis:names:tc:SAML:2.0:status:NoAvailableIDP',
  NO_PASSIVE: 'urn:oasis:names:tc:SAML:2.0:status:NoPassive',
  NO_SUPPORTED_IDP: 'urn:oasis:names:tc:SAML:2.0:status:NoSupportedIDP',
  PARTIAL_LOGOUT: 'urn:oasis:names:tc:SAML:2.0:status:PartialLogout',
  PROXY_COUNT_EXCEEDED: 'urn:oasis:names:tc:SAML:2.0:status:ProxyCountExceeded',
  REQUEST_DENIED: 'urn:oasis:names:tc:SAML:2.0:status:RequestDenied',
  REQUEST_UNSUPPORTED: 'urn:oasis:names:tc:SAML:2.0:status:RequestUnsupported',
  REQUEST_VERSION_DEPRECATED: 'urn:oasis:names:tc:SAML:2.0:status:RequestVersionDeprecated',
  REQUEST_VERSION_TOO_HIGH: 'urn:oasis:names:tc:SAML:2.0:status:RequestVersionTooHigh',
  REQUEST_VERSION_TOO_LOW: 'urn:oasis:names:tc:SAML:2.0:status:RequestVersionTooLow',
  RESOURCE_NOT_RECOGNIZED: 'urn:oasis:names:tc:SAML:2.0:status:ResourceNotRecognized',
  TOO_MANY_RESPONSES: 'urn:oasis:names:tc:SAML:2.0:status:TooManyResponses',
  UNKNOWN_ATTR_PROFILE: 'urn:oasis:names:tc:SAML:2.0:status:UnknownAttrProfile',
  UNKNOWN_PRINCIPAL: 'urn:oasis:names:tc:SAML:2.0:status:UnknownPrincipal',
  UNSUPPORTED_BINDING: 'urn:oasis:names:tc:SAML:2.0:status:UnsupportedBinding'
} as const;

// 名称ID格式常量
export const NameIdFormats = {
  UNSPECIFIED: 'urn:oasis:names:tc:SAML:1.1:nameid-format:unspecified',
  EMAIL: 'urn:oasis:names:tc:SAML:1.1:nameid-format:emailAddress',
  X509_SUBJECT: 'urn:oasis:names:tc:SAML:1.1:nameid-format:X509SubjectName',
  WINDOWS_DOMAIN: 'urn:oasis:names:tc:SAML:1.1:nameid-format:WindowsDomainQualifiedName',
  KERBEROS: 'urn:oasis:names:tc:SAML:2.0:nameid-format:kerberos',
  ENTITY: 'urn:oasis:names:tc:SAML:2.0:nameid-format:entity',
  PERSISTENT: 'urn:oasis:names:tc:SAML:2.0:nameid-format:persistent',
  TRANSIENT: 'urn:oasis:names:tc:SAML:2.0:nameid-format:transient'
} as const;

// 认证上下文类引用常量
export const AuthnContextClassRefs = {
  PASSWORD: 'urn:oasis:names:tc:SAML:2.0:ac:classes:Password',
  PASSWORD_PROTECTED_TRANSPORT: 'urn:oasis:names:tc:SAML:2.0:ac:classes:PasswordProtectedTransport',
  TLS_CLIENT: 'urn:oasis:names:tc:SAML:2.0:ac:classes:TLSClient',
  X509: 'urn:oasis:names:tc:SAML:2.0:ac:classes:X509',
  SMARTCARD: 'urn:oasis:names:tc:SAML:2.0:ac:classes:Smartcard',
  SMARTCARD_PKI: 'urn:oasis:names:tc:SAML:2.0:ac:classes:SmartcardPKI',
  SOFTWARE_PKI: 'urn:oasis:names:tc:SAML:2.0:ac:classes:SoftwarePKI',
  TELEPHONY: 'urn:oasis:names:tc:SAML:2.0:ac:classes:Telephony',
  NOMAD_TELEPHONY: 'urn:oasis:names:tc:SAML:2.0:ac:classes:NomadTelephony',
  PERSONAL_TELEPHONY: 'urn:oasis:names:tc:SAML:2.0:ac:classes:PersonalTelephony',
  AUTHENTICATED_TELEPHONY: 'urn:oasis:names:tc:SAML:2.0:ac:classes:AuthenticatedTelephony',
  SECURE_REMOTE_PASSWORD: 'urn:oasis:names:tc:SAML:2.0:ac:classes:SecureRemotePassword',
  INTERNET_PROTOCOL: 'urn:oasis:names:tc:SAML:2.0:ac:classes:InternetProtocol',
  INTERNET_PROTOCOL_PASSWORD: 'urn:oasis:names:tc:SAML:2.0:ac:classes:InternetProtocolPassword'
} as const;

// 绑定类型常量
export const SAMLBindings = {
  HTTP_POST: 'urn:oasis:names:tc:SAML:2.0:bindings:HTTP-POST',
  HTTP_REDIRECT: 'urn:oasis:names:tc:SAML:2.0:bindings:HTTP-Redirect',
  HTTP_ARTIFACT: 'urn:oasis:names:tc:SAML:2.0:bindings:HTTP-Artifact',
  SOAP: 'urn:oasis:names:tc:SAML:2.0:bindings:SOAP'
} as const;
