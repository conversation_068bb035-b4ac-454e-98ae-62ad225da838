/**
 * 权限元数据类型定义
 *
 * 本文件定义了统一的权限元数据格式，支持权限依赖关系管理和约束规则
 *
 * <AUTHOR> Provider Team
 * @version 1.0.0
 * @since 2025-08-27
 */

/**
 * 权限操作类型枚举
 */
export enum PermissionOperation {
  READ = 'read',
  WRITE = 'write',
  DELETE = 'delete',
  EXECUTE = 'execute',
  ADMIN = 'admin'
}

/**
 * 权限资源类型枚举
 */
export enum ResourceType {
  USER = 'user',
  APPLICATION = 'application',
  ROLE = 'role',
  PERMISSION = 'permission',
  SYSTEM = 'system',
  DATA = 'data',
  API = 'api',
  FILE = 'file'
}

/**
 * 权限约束类型
 */
export interface PermissionConstraints {
  /** 时间限制 - 权限生效的时间范围 */
  timeRestrictions?: {
    startTime?: string; // ISO 8601 格式
    endTime?: string;   // ISO 8601 格式
    allowedHours?: number[]; // 允许的小时数组 [0-23]
    allowedDays?: number[];  // 允许的星期数组 [0-6]
    timezone?: string;       // 时区
  };

  /** IP地址限制 */
  ipRestrictions?: {
    allowedIPs?: string[];    // 允许的IP地址列表
    blockedIPs?: string[];    // 禁止的IP地址列表
    allowedCIDRs?: string[];  // 允许的CIDR网段
  };

  /** 设备限制 */
  deviceRestrictions?: {
    allowedDeviceTypes?: string[]; // 允许的设备类型
    requireMFA?: boolean;          // 是否需要多因子认证
    maxConcurrentSessions?: number; // 最大并发会话数
  };

  /** 地理位置限制 */
  geoRestrictions?: {
    allowedCountries?: string[];   // 允许的国家代码
    allowedRegions?: string[];     // 允许的地区
    blockedCountries?: string[];   // 禁止的国家代码
  };

  /** 数据访问限制 */
  dataRestrictions?: {
    maxRecords?: number;           // 最大记录数
    allowedFields?: string[];      // 允许访问的字段
    sensitivityLevel?: 'public' | 'internal' | 'confidential' | 'secret';
  };
}

/**
 * 权限依赖关系
 */
export interface PermissionDependency {
  /** 依赖的权限ID */
  permissionId: string;

  /** 依赖类型 */
  type: 'requires' | 'conflicts' | 'implies' | 'excludes';

  /** 依赖条件 */
  condition?: string; // 支持表达式，如 "user.role === 'admin'"

  /** 依赖描述 */
  description?: string;
}

/**
 * 权限元数据接口
 */
export interface PermissionMetadata {
  /** 权限唯一标识符 */
  id: string;

  /** 权限名称 */
  name: string;

  /** 权限描述 */
  description: string;

  /** 权限分类 */
  category: string;

  /** 资源类型 */
  resourceType: ResourceType;

  /** 支持的操作 */
  operations: PermissionOperation[];

  /** 权限级别 (1-10, 1为最低，10为最高) */
  level: number;

  /** 权限依赖关系 */
  dependencies: PermissionDependency[];

  /** 权限约束条件 */
  constraints: PermissionConstraints;

  /** 权限标签 */
  tags: string[];

  /** 是否为敏感权限 */
  isSensitive: boolean;

  /** 是否可委托 */
  isDelegatable: boolean;

  /** 权限有效期 */
  validity?: {
    startDate?: string; // ISO 8601 格式
    endDate?: string;   // ISO 8601 格式
    duration?: number;  // 有效期（秒）
  };

  /** 权限元数据 */
  metadata: {
    /** 创建者 */
    createdBy: string;

    /** 创建时间 */
    createdAt: string;

    /** 最后更新者 */
    updatedBy: string;

    /** 最后更新时间 */
    updatedAt: string;

    /** 版本号 */
    version: string;

    /** 权限来源应用 */
    sourceApplication?: string;

    /** 权限文档链接 */
    documentationUrl?: string;

    /** 权限负责人 */
    owner?: string;

    /** 审核状态 */
    approvalStatus?: 'pending' | 'approved' | 'rejected' | 'deprecated';
  };
}

/**
 * 权限验证上下文
 */
export interface PermissionContext {
  /** 用户ID */
  userId: string;

  /** 用户角色 */
  userRoles: string[];

  /** 请求的资源 */
  resource: {
    type: ResourceType;
    id?: string;
    attributes?: Record<string, any>;
  };

  /** 请求的操作 */
  operation: PermissionOperation;

  /** 请求环境信息 */
  environment: {
    ipAddress?: string;
    userAgent?: string;
    deviceType?: string;
    location?: {
      country?: string;
      region?: string;
      city?: string;
    };
    timestamp: string;
    sessionId?: string;
  };

  /** 额外的上下文数据 */
  additionalContext?: Record<string, any>;
}

/**
 * 权限验证结果
 */
export interface PermissionValidationResult {
  /** 是否有权限 */
  granted: boolean;

  /** 验证的权限ID */
  permissionId: string;

  /** 验证原因 */
  reason: string;

  /** 失败的约束条件 */
  failedConstraints?: string[];

  /** 缺少的依赖权限 */
  missingDependencies?: string[];

  /** 权限有效期 */
  expiresAt?: string;

  /** 验证时间戳 */
  validatedAt: string;

  /** 验证耗时（毫秒） */
  validationTime: number;
}

/**
 * 权限表达式接口
 */
export interface PermissionExpression {
  /** 表达式字符串 */
  expression: string;

  /** 表达式类型 */
  type: 'simple' | 'complex' | 'script';

  /** 表达式变量 */
  variables?: Record<string, any>;

  /** 表达式描述 */
  description?: string;
}

/**
 * 权限模板接口
 */
export interface PermissionTemplate {
  /** 模板ID */
  id: string;

  /** 模板名称 */
  name: string;

  /** 模板描述 */
  description: string;

  /** 模板分类 */
  category: string;

  /** 权限列表 */
  permissions: string[];

  /** 模板变量 */
  variables?: Record<string, any>;

  /** 是否为系统模板 */
  isSystemTemplate: boolean;

  /** 创建信息 */
  createdBy: string;
  createdAt: string;
  updatedAt: string;
}

/**
 * 权限变更记录
 */
export interface PermissionChangeRecord {
  /** 变更ID */
  id: string;

  /** 权限ID */
  permissionId: string;

  /** 变更类型 */
  changeType: 'created' | 'updated' | 'deleted' | 'activated' | 'deactivated';

  /** 变更前的值 */
  previousValue?: Partial<PermissionMetadata>;

  /** 变更后的值 */
  newValue?: Partial<PermissionMetadata>;

  /** 变更原因 */
  reason: string;

  /** 变更者 */
  changedBy: string;

  /** 变更时间 */
  changedAt: string;

  /** 审批信息 */
  approval?: {
    required: boolean;
    status: 'pending' | 'approved' | 'rejected';
    approvedBy?: string;
    approvedAt?: string;
    comments?: string;
  };
}