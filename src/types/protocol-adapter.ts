/**
 * 协议适配器类型定义
 * 定义支持非标准应用的协议适配器接口
 */

import { Request, Response } from 'express';
import { User } from '@prisma/client';

/**
 * 认证请求接口
 */
export interface AuthenticationRequest {
  applicationId: string;
  userId?: string;
  clientId: string;
  redirectUri?: string;
  scope?: string[];
  state?: string;
  nonce?: string;
  customParams?: Record<string, any>;
  ipAddress?: string;
  userAgent?: string;
}

/**
 * 认证响应接口
 */
export interface AuthenticationResponse {
  success: boolean;
  redirectUrl?: string;
  tokens?: {
    accessToken?: string;
    refreshToken?: string;
    idToken?: string;
    tokenType?: string;
    expiresIn?: number;
  };
  user?: {
    id: string;
    email: string;
    name?: string;
    attributes?: Record<string, any>;
  };
  error?: {
    code: string;
    message: string;
    description?: string;
  };
  customData?: Record<string, any>;
}

/**
 * 协议配置接口
 */
export interface ProtocolConfig {
  name: string;
  version: string;
  endpoints: {
    authorization?: string;
    token?: string;
    userinfo?: string;
    logout?: string;
    metadata?: string;
    [key: string]: string | undefined;
  };
  supportedGrantTypes?: string[];
  supportedResponseTypes?: string[];
  supportedScopes?: string[];
  customSettings?: Record<string, any>;
}

/**
 * 协议适配器接口
 */
export interface IProtocolAdapter {
  /**
   * 协议名称
   */
  readonly name: string;

  /**
   * 协议版本
   */
  readonly version: string;

  /**
   * 支持的认证方法
   */
  readonly supportedMethods: string[];

  /**
   * 初始化适配器
   */
  initialize(config: ProtocolConfig): Promise<void>;

  /**
   * 处理认证请求
   */
  handleAuthRequest(request: AuthenticationRequest): Promise<AuthenticationResponse>;

  /**
   * 处理令牌请求
   */
  handleTokenRequest(request: Request): Promise<AuthenticationResponse>;

  /**
   * 处理用户信息请求
   */
  handleUserInfoRequest(request: Request): Promise<AuthenticationResponse>;

  /**
   * 处理登出请求
   */
  handleLogoutRequest(request: Request): Promise<AuthenticationResponse>;

  /**
   * 生成元数据
   */
  generateMetadata(): Promise<Record<string, any>>;

  /**
   * 验证配置
   */
  validateConfig(config: ProtocolConfig): Promise<boolean>;

  /**
   * 自定义端点处理器
   */
  handleCustomEndpoint?(endpoint: string, request: Request, response: Response): Promise<void>;
}

/**
 * 协议适配器工厂接口
 */
export interface IProtocolAdapterFactory {
  /**
   * 创建协议适配器实例
   */
  createAdapter(protocolName: string, config: ProtocolConfig): Promise<IProtocolAdapter>;

  /**
   * 获取支持的协议列表
   */
  getSupportedProtocols(): string[];

  /**
   * 注册新的协议适配器
   */
  registerAdapter(protocolName: string, adapterClass: new () => IProtocolAdapter): void;
}

/**
 * 应用协议配置
 */
export interface ApplicationProtocolConfig {
  applicationId: string;
  protocolName: string;
  protocolVersion: string;
  config: ProtocolConfig;
  isActive: boolean;
  customHandlers?: {
    preAuth?: string; // 预认证处理器函数名
    postAuth?: string; // 后认证处理器函数名
    tokenTransform?: string; // 令牌转换器函数名
    userTransform?: string; // 用户信息转换器函数名
  };
  webhooks?: {
    onSuccess?: string; // 成功回调URL
    onError?: string; // 错误回调URL
    onLogout?: string; // 登出回调URL
  };
}

/**
 * 自定义认证流程接口
 */
export interface CustomAuthFlow {
  name: string;
  steps: AuthFlowStep[];
  config: Record<string, any>;
}

/**
 * 认证流程步骤
 */
export interface AuthFlowStep {
  name: string;
  type: 'validation' | 'transformation' | 'external_call' | 'custom';
  config: Record<string, any>;
  handler: string; // 处理器函数名或路径
  onSuccess?: string; // 成功时的下一步
  onError?: string; // 失败时的下一步
  timeout?: number; // 超时时间（毫秒）
}

/**
 * 插件接口
 */
export interface IAuthPlugin {
  /**
   * 插件名称
   */
  readonly name: string;

  /**
   * 插件版本
   */
  readonly version: string;

  /**
   * 插件描述
   */
  readonly description: string;

  /**
   * 初始化插件
   */
  initialize(config: Record<string, any>): Promise<void>;

  /**
   * 销毁插件
   */
  destroy(): Promise<void>;

  /**
   * 获取插件提供的协议适配器
   */
  getProtocolAdapters?(): Record<string, new () => IProtocolAdapter>;

  /**
   * 获取插件提供的自定义处理器
   */
  getCustomHandlers?(): Record<string, Function>;

  /**
   * 插件健康检查
   */
  healthCheck?(): Promise<boolean>;
}

/**
 * 非标准应用类型枚举
 */
export enum NonStandardAppType {
  LEGACY_SYSTEM = 'legacy_system',
  CUSTOM_PROTOCOL = 'custom_protocol',
  API_ONLY = 'api_only',
  WEBHOOK_BASED = 'webhook_based',
  TOKEN_EXCHANGE = 'token_exchange',
  FEDERATED_SSO = 'federated_sso',
  MOBILE_APP = 'mobile_app',
  IOT_DEVICE = 'iot_device'
}

/**
 * 错误类型
 */
export class ProtocolAdapterError extends Error {
  constructor(
    public code: string,
    message: string,
    public details?: Record<string, any>
  ) {
    super(message);
    this.name = 'ProtocolAdapterError';
  }
}
