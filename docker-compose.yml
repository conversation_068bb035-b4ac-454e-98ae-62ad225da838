version: '3.8'

services:
  # PostgreSQL 数据库
  postgres:
    image: postgres:15-alpine
    container_name: idp-postgres
    environment:
      POSTGRES_DB: id_provider
      POSTGRES_USER: idp_user
      POSTGRES_PASSWORD: idp_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    networks:
      - idp-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U idp_user -d id_provider"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis 缓存
  redis:
    image: redis:7-alpine
    container_name: idp-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./config/redis.conf:/usr/local/etc/redis/redis.conf
    command: redis-server /usr/local/etc/redis/redis.conf
    networks:
      - idp-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 身份提供商应用
  idp-app:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    container_name: idp-app
    environment:
      NODE_ENV: production
      DATABASE_URL: ************************************************/id_provider?schema=public
      REDIS_HOST: redis
      REDIS_PORT: 6379
      REDIS_DB: 0
      REDIS_KEY_PREFIX: "idp:prod:"
      JWT_SECRET: ${JWT_SECRET}
      JWT_REFRESH_SECRET: ${JWT_REFRESH_SECRET}
      SMTP_HOST: ${SMTP_HOST}
      SMTP_PORT: ${SMTP_PORT}
      SMTP_USER: ${SMTP_USER}
      SMTP_PASS: ${SMTP_PASS}
      GOOGLE_CLIENT_ID: ${GOOGLE_CLIENT_ID}
      GOOGLE_CLIENT_SECRET: ${GOOGLE_CLIENT_SECRET}
      GITHUB_CLIENT_ID: ${GITHUB_CLIENT_ID}
      GITHUB_CLIENT_SECRET: ${GITHUB_CLIENT_SECRET}
      FRONTEND_URL: ${FRONTEND_URL}
    ports:
      - "3000:3000"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - idp-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    volumes:
      - ./logs:/app/logs

  # 前端应用 (可选)
  idp-frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: idp-frontend
    environment:
      REACT_APP_API_URL: http://localhost:3000/api/v1
      REACT_APP_OAUTH_REDIRECT_URL: http://localhost:3001/auth/callback
    ports:
      - "3001:3000"
    depends_on:
      - idp-app
    networks:
      - idp-network
    restart: unless-stopped

  # Nginx 反向代理 (可选)
  nginx:
    image: nginx:alpine
    container_name: idp-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./config/nginx.conf:/etc/nginx/nginx.conf
      - ./config/ssl:/etc/nginx/ssl
      - ./logs/nginx:/var/log/nginx
    depends_on:
      - idp-app
      - idp-frontend
    networks:
      - idp-network
    restart: unless-stopped

  # Redis Commander (开发环境Redis管理工具)
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: idp-redis-commander
    environment:
      REDIS_HOSTS: local:redis:6379
      HTTP_USER: admin
      HTTP_PASSWORD: admin123
    ports:
      - "8081:8081"
    depends_on:
      - redis
    networks:
      - idp-network
    restart: unless-stopped
    profiles:
      - dev

  # pgAdmin (开发环境数据库管理工具)
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: idp-pgadmin
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin123
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    ports:
      - "8080:80"
    depends_on:
      - postgres
    networks:
      - idp-network
    restart: unless-stopped
    profiles:
      - dev

  # Prometheus 监控 (可选)
  prometheus:
    image: prom/prometheus:latest
    container_name: idp-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./config/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - idp-network
    restart: unless-stopped
    profiles:
      - monitoring

  # Grafana 可视化 (可选)
  grafana:
    image: grafana/grafana:latest
    container_name: idp-grafana
    ports:
      - "3001:3000"
    environment:
      GF_SECURITY_ADMIN_PASSWORD: admin123
    volumes:
      - grafana_data:/var/lib/grafana
      - ./config/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./config/grafana/datasources:/etc/grafana/provisioning/datasources
    depends_on:
      - prometheus
    networks:
      - idp-network
    restart: unless-stopped
    profiles:
      - monitoring

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

networks:
  idp-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
