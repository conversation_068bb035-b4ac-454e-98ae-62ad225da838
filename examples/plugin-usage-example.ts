/**
 * 插件使用示例
 * 演示如何使用插件管理器和协议适配器注册表
 */

import { pluginManager } from '@/services/plugin-manager.service';
import { adapterRegistry } from '@/services/adapter-registry.service';
import { protocolAdapterService } from '@/services/protocol-adapter.service';
import { logger } from '@/config/logger';

/**
 * 插件使用示例类
 */
export class PluginUsageExample {
  
  /**
   * 演示插件安装和使用
   */
  async demonstratePluginUsage(): Promise<void> {
    logger.info('开始演示插件使用');

    try {
      // 1. 安装示例插件
      await this.installExamplePlugin();

      // 2. 创建适配器实例
      await this.createAdapterInstances();

      // 3. 使用自定义处理器
      await this.useCustomHandlers();

      // 4. 演示适配器功能
      await this.demonstrateAdapterFeatures();

      // 5. 监控和管理
      await this.monitorAndManage();

      logger.info('插件使用演示完成');

    } catch (error) {
      logger.error('插件使用演示失败', { error: error.message });
      throw error;
    }
  }

  /**
   * 安装示例插件
   */
  private async installExamplePlugin(): Promise<void> {
    logger.info('安装示例插件');

    try {
      // 安装插件
      await pluginManager.installPlugin(
        'example-auth-plugin',
        './examples/plugins/example-auth-plugin.ts',
        'ExampleAuthPlugin',
        {
          enableLogging: true,
          customConfig: {
            tokenLifetime: 3600,
            apiKeyLifetime: 86400
          }
        },
        'checksum-placeholder' // 在实际使用中应该是真实的校验和
      );

      // 启用插件
      await pluginManager.enablePlugin('example-auth-plugin');

      logger.info('示例插件安装并启用成功');

    } catch (error) {
      logger.error('安装示例插件失败', { error: error.message });
      throw error;
    }
  }

  /**
   * 创建适配器实例
   */
  private async createAdapterInstances(): Promise<void> {
    logger.info('创建适配器实例');

    try {
      // 创建示例令牌适配器实例
      const tokenAdapterInstanceId = await adapterRegistry.createAdapterInstance(
        'example-token',
        {
          name: 'example-token',
          version: '1.0.0',
          endpoints: {
            authentication: '/auth/token',
            userinfo: '/auth/userinfo',
            logout: '/auth/logout'
          },
          customSettings: {
            tokenValidationEnabled: true,
            apiKeyValidationEnabled: true
          }
        }
      );

      logger.info('令牌适配器实例创建成功', { instanceId: tokenAdapterInstanceId });

      // 创建自定义OAuth适配器实例
      const oauthAdapterInstanceId = await adapterRegistry.createAdapterInstance(
        'custom-oauth',
        {
          name: 'custom-oauth',
          version: '2.0.0',
          endpoints: {
            authorization: '/oauth/authorize',
            token: '/oauth/token',
            userinfo: '/oauth/userinfo'
          },
          oauth: {
            authorizationEndpoint: 'https://example.com/oauth/authorize',
            tokenEndpoint: 'https://example.com/oauth/token',
            userinfoEndpoint: 'https://example.com/oauth/userinfo'
          },
          client: {
            clientId: 'example-client',
            redirectUris: ['https://app.example.com/callback'],
            allowedScopes: ['read', 'write', 'admin']
          },
          flows: {
            enabled: ['authorization_code', 'client_credentials']
          }
        }
      );

      logger.info('OAuth适配器实例创建成功', { instanceId: oauthAdapterInstanceId });

    } catch (error) {
      logger.error('创建适配器实例失败', { error: error.message });
      throw error;
    }
  }

  /**
   * 使用自定义处理器
   */
  private async useCustomHandlers(): Promise<void> {
    logger.info('演示自定义处理器使用');

    try {
      // 获取可用的自定义处理器
      const availableHandlers = pluginManager.getAvailableCustomHandlers();
      logger.info('可用的自定义处理器', { handlers: availableHandlers });

      // 使用预认证处理器
      const preAuthResult = await pluginManager.executeCustomHandler(
        'example_pre_auth',
        {
          clientId: 'example-client',
          scope: 'read write',
          redirectUri: 'https://app.example.com/callback'
        }
      );
      logger.info('预认证处理器执行结果', { result: preAuthResult });

      // 使用令牌转换处理器
      const tokenTransformResult = await pluginManager.executeCustomHandler(
        'example_token_transform',
        {
          access_token: 'example-token-123',
          token_type: 'Bearer',
          expires_in: 3600
        }
      );
      logger.info('令牌转换处理器执行结果', { result: tokenTransformResult });

      // 使用用户信息转换处理器
      const userTransformResult = await pluginManager.executeCustomHandler(
        'example_user_transform',
        {
          id: 'user-123',
          username: 'example',
          email: '<EMAIL>'
        }
      );
      logger.info('用户信息转换处理器执行结果', { result: userTransformResult });

      // 使用自定义验证处理器
      const validationResult = await pluginManager.executeCustomHandler(
        'example_custom_validation',
        {
          data: 'test-data',
          type: 'string'
        }
      );
      logger.info('自定义验证处理器执行结果', { result: validationResult });

    } catch (error) {
      logger.error('使用自定义处理器失败', { error: error.message });
      throw error;
    }
  }

  /**
   * 演示适配器功能
   */
  private async demonstrateAdapterFeatures(): Promise<void> {
    logger.info('演示适配器功能');

    try {
      // 获取所有适配器实例
      const instances = adapterRegistry.getAllAdapterInstances();
      logger.info('当前适配器实例', { 
        count: instances.length,
        instances: instances.map(i => ({ id: i.id, name: i.name, isActive: i.isActive }))
      });

      // 获取适配器元数据
      const tokenAdapterMetadata = adapterRegistry.getAdapterMetadata('example-token');
      logger.info('令牌适配器元数据', { metadata: tokenAdapterMetadata });

      // 模拟认证请求
      const authRequest = {
        applicationId: 'app-123',
        clientId: 'example-client',
        authMethod: 'token_auth',
        accessToken: 'example-token-123',
        headers: {
          'authorization': 'Bearer example-token-123'
        }
      };

      // 使用协议适配器服务处理认证
      const authResponse = await protocolAdapterService.handleAuthentication(
        'example-token',
        authRequest as any,
        { clientId: 'example-client', name: 'Example App' }
      );
      logger.info('认证响应', { response: authResponse });

    } catch (error) {
      logger.error('演示适配器功能失败', { error: error.message });
      throw error;
    }
  }

  /**
   * 监控和管理
   */
  private async monitorAndManage(): Promise<void> {
    logger.info('演示监控和管理功能');

    try {
      // 获取插件状态
      const loadedPlugins = pluginManager.getLoadedPlugins();
      logger.info('已加载的插件', { plugins: loadedPlugins });

      // 获取适配器使用统计
      const usageStats = adapterRegistry.getAdapterUsageStats();
      logger.info('适配器使用统计', { stats: usageStats });

      // 执行插件健康检查
      const healthCheckResults = await this.performHealthChecks();
      logger.info('健康检查结果', { results: healthCheckResults });

      // 演示实例管理
      await this.demonstrateInstanceManagement();

    } catch (error) {
      logger.error('监控和管理演示失败', { error: error.message });
      throw error;
    }
  }

  /**
   * 执行健康检查
   */
  private async performHealthChecks(): Promise<Record<string, boolean>> {
    const results: Record<string, boolean> = {};
    const loadedPlugins = pluginManager.getLoadedPlugins();

    for (const pluginName of loadedPlugins) {
      try {
        const plugin = pluginManager.getPlugin(pluginName);
        if (plugin && plugin.healthCheck) {
          results[pluginName] = await plugin.healthCheck();
        } else {
          results[pluginName] = true; // 假设健康
        }
      } catch (error) {
        logger.error('插件健康检查失败', { pluginName, error: error.message });
        results[pluginName] = false;
      }
    }

    return results;
  }

  /**
   * 演示实例管理
   */
  private async demonstrateInstanceManagement(): Promise<void> {
    logger.info('演示实例管理');

    try {
      const instances = adapterRegistry.getAllAdapterInstances();
      
      if (instances.length > 0) {
        const firstInstance = instances[0];
        
        // 记录使用情况
        adapterRegistry.recordAdapterUsage(firstInstance.id);
        logger.info('记录适配器使用', { instanceId: firstInstance.id });

        // 更新配置（示例）
        const newConfig = {
          ...firstInstance.config,
          customSettings: {
            ...firstInstance.config.customSettings,
            updatedAt: new Date().toISOString()
          }
        };

        const updateSuccess = await adapterRegistry.updateAdapterInstanceConfig(
          firstInstance.id,
          newConfig
        );
        logger.info('配置更新结果', { instanceId: firstInstance.id, success: updateSuccess });

        // 重启实例（示例）
        const restartSuccess = await adapterRegistry.restartAdapterInstance(firstInstance.id);
        logger.info('实例重启结果', { instanceId: firstInstance.id, success: restartSuccess });
      }

      // 清理非活跃实例
      await adapterRegistry.cleanupInactiveInstances();
      logger.info('非活跃实例清理完成');

    } catch (error) {
      logger.error('实例管理演示失败', { error: error.message });
      throw error;
    }
  }

  /**
   * 清理演示环境
   */
  async cleanup(): Promise<void> {
    logger.info('清理演示环境');

    try {
      // 停止所有适配器实例
      const instances = adapterRegistry.getAllAdapterInstances();
      for (const instance of instances) {
        await adapterRegistry.stopAdapterInstance(instance.id);
      }

      // 禁用并卸载插件
      await pluginManager.disablePlugin('example-auth-plugin');
      await pluginManager.uninstallPlugin('example-auth-plugin');

      logger.info('演示环境清理完成');

    } catch (error) {
      logger.error('清理演示环境失败', { error: error.message });
      throw error;
    }
  }
}

/**
 * 运行插件使用示例
 */
export async function runPluginUsageExample(): Promise<void> {
  const example = new PluginUsageExample();
  
  try {
    await example.demonstratePluginUsage();
  } finally {
    // 确保清理资源
    await example.cleanup();
  }
}

// 如果直接运行此文件，则执行示例
if (require.main === module) {
  runPluginUsageExample()
    .then(() => {
      logger.info('插件使用示例执行完成');
      process.exit(0);
    })
    .catch((error) => {
      logger.error('插件使用示例执行失败', { error: error.message });
      process.exit(1);
    });
}
