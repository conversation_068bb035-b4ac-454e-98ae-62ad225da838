/**
 * Webhook应用集成示例
 * 演示如何集成基于Webhook的应用
 */

const express = require('express');
const crypto = require('crypto');
const axios = require('axios');

/**
 * Webhook应用示例
 * 这个应用通过Webhook接收认证事件
 */
class WebhookApp {
  constructor(config) {
    this.config = config;
    this.app = express();
    this.setupMiddleware();
    this.setupRoutes();
  }

  setupMiddleware() {
    this.app.use(express.json());
    this.app.use(express.urlencoded({ extended: true }));
  }

  setupRoutes() {
    // 主页
    this.app.get('/', (req, res) => {
      res.send(`
        <h1>Webhook应用示例</h1>
        <p>这是一个基于Webhook的应用集成示例</p>
        <a href="/auth/login">登录</a>
      `);
    });

    // 发起登录
    this.app.get('/auth/login', (req, res) => {
      const authUrl = this.buildAuthUrl();
      res.redirect(authUrl);
    });

    // 认证回调
    this.app.get('/auth/callback', (req, res) => {
      const { code, state, error } = req.query;

      if (error) {
        res.status(400).send(`认证失败: ${error}`);
        return;
      }

      if (!code) {
        res.status(400).send('缺少授权码');
        return;
      }

      // 交换访问令牌
      this.exchangeToken(code)
        .then(tokens => {
          res.send(`
            <h1>认证成功</h1>
            <p>访问令牌: ${tokens.access_token}</p>
            <p>等待Webhook事件...</p>
          `);
        })
        .catch(error => {
          res.status(500).send(`令牌交换失败: ${error.message}`);
        });
    });

    // Webhook端点 - 认证成功
    this.app.post('/webhooks/auth/success', (req, res) => {
      try {
        // 验证Webhook签名
        if (!this.verifyWebhookSignature(req)) {
          res.status(401).send('无效的Webhook签名');
          return;
        }

        const { user, tokens, sessionId, timestamp } = req.body;

        console.log('收到认证成功Webhook:', {
          userId: user.id,
          email: user.email,
          sessionId,
          timestamp
        });

        // 处理认证成功事件
        this.handleAuthSuccess(user, tokens, sessionId);

        res.status(200).json({ received: true });

      } catch (error) {
        console.error('处理认证成功Webhook失败:', error);
        res.status(500).send('Webhook处理失败');
      }
    });

    // Webhook端点 - 认证失败
    this.app.post('/webhooks/auth/error', (req, res) => {
      try {
        if (!this.verifyWebhookSignature(req)) {
          res.status(401).send('无效的Webhook签名');
          return;
        }

        const { error, details, timestamp } = req.body;

        console.log('收到认证失败Webhook:', {
          error,
          details,
          timestamp
        });

        // 处理认证失败事件
        this.handleAuthError(error, details);

        res.status(200).json({ received: true });

      } catch (error) {
        console.error('处理认证失败Webhook失败:', error);
        res.status(500).send('Webhook处理失败');
      }
    });

    // Webhook端点 - 用户登出
    this.app.post('/webhooks/auth/logout', (req, res) => {
      try {
        if (!this.verifyWebhookSignature(req)) {
          res.status(401).send('无效的Webhook签名');
          return;
        }

        const { userId, sessionId, timestamp } = req.body;

        console.log('收到用户登出Webhook:', {
          userId,
          sessionId,
          timestamp
        });

        // 处理用户登出事件
        this.handleUserLogout(userId, sessionId);

        res.status(200).json({ received: true });

      } catch (error) {
        console.error('处理用户登出Webhook失败:', error);
        res.status(500).send('Webhook处理失败');
      }
    });
  }

  /**
   * 构建认证URL
   */
  buildAuthUrl() {
    const params = new URLSearchParams({
      client_id: this.config.clientId,
      redirect_uri: this.config.redirectUri,
      scope: 'profile email',
      state: this.generateState(),
      response_type: 'code'
    });

    return `${this.config.idpBaseUrl}/nsa/auth/${this.config.applicationId}/webhook?${params}`;
  }

  /**
   * 交换访问令牌
   */
  async exchangeToken(code) {
    try {
      const response = await axios.post(
        `${this.config.idpBaseUrl}/nsa/token/${this.config.applicationId}/webhook`,
        new URLSearchParams({
          grant_type: 'authorization_code',
          code,
          client_id: this.config.clientId,
          client_secret: this.config.clientSecret,
          redirect_uri: this.config.redirectUri
        }),
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
          }
        }
      );

      return response.data;

    } catch (error) {
      console.error('令牌交换失败:', error.response?.data || error.message);
      throw error;
    }
  }

  /**
   * 验证Webhook签名
   */
  verifyWebhookSignature(req) {
    const signature = req.headers['x-webhook-signature'];
    if (!signature) {
      return false;
    }

    const payload = JSON.stringify(req.body);
    const expectedSignature = crypto
      .createHmac('sha256', this.config.webhookSecret)
      .update(payload)
      .digest('hex');

    return signature === `sha256=${expectedSignature}`;
  }

  /**
   * 处理认证成功事件
   */
  async handleAuthSuccess(user, tokens, sessionId) {
    console.log('用户认证成功:', user.email);

    // 这里可以实现具体的业务逻辑
    // 例如：创建用户会话、发送欢迎邮件、记录登录日志等

    try {
      // 获取用户详细信息
      const userInfo = await this.getUserInfo(tokens.access_token);
      console.log('用户详细信息:', userInfo);

      // 创建本地用户会话
      await this.createUserSession(user, sessionId);

      // 发送欢迎通知
      await this.sendWelcomeNotification(user);

    } catch (error) {
      console.error('处理认证成功事件失败:', error);
    }
  }

  /**
   * 处理认证失败事件
   */
  async handleAuthError(error, details) {
    console.log('认证失败:', error);

    // 记录失败日志
    await this.logAuthFailure(error, details);

    // 可以发送告警通知
    if (error === 'suspicious_activity') {
      await this.sendSecurityAlert(details);
    }
  }

  /**
   * 处理用户登出事件
   */
  async handleUserLogout(userId, sessionId) {
    console.log('用户登出:', userId);

    // 清理本地会话
    await this.cleanupUserSession(userId, sessionId);

    // 记录登出日志
    await this.logUserLogout(userId, sessionId);
  }

  /**
   * 获取用户信息
   */
  async getUserInfo(accessToken) {
    try {
      const response = await axios.get(
        `${this.config.idpBaseUrl}/nsa/userinfo/${this.config.applicationId}/webhook`,
        {
          headers: {
            'Authorization': `Bearer ${accessToken}`
          }
        }
      );

      return response.data;

    } catch (error) {
      console.error('获取用户信息失败:', error.response?.data || error.message);
      throw error;
    }
  }

  /**
   * 创建用户会话
   */
  async createUserSession(user, sessionId) {
    // 模拟创建用户会话
    console.log(`为用户 ${user.email} 创建会话 ${sessionId}`);
    
    // 这里可以将会话信息存储到数据库或缓存中
    // 例如：Redis、数据库等
  }

  /**
   * 发送欢迎通知
   */
  async sendWelcomeNotification(user) {
    // 模拟发送欢迎通知
    console.log(`向用户 ${user.email} 发送欢迎通知`);
    
    // 这里可以集成邮件服务、短信服务等
  }

  /**
   * 记录认证失败日志
   */
  async logAuthFailure(error, details) {
    console.log('记录认证失败:', { error, details, timestamp: new Date() });
    
    // 这里可以将日志写入文件或数据库
  }

  /**
   * 发送安全告警
   */
  async sendSecurityAlert(details) {
    console.log('发送安全告警:', details);
    
    // 这里可以集成告警系统
  }

  /**
   * 清理用户会话
   */
  async cleanupUserSession(userId, sessionId) {
    console.log(`清理用户 ${userId} 的会话 ${sessionId}`);
    
    // 这里可以从缓存或数据库中删除会话信息
  }

  /**
   * 记录用户登出日志
   */
  async logUserLogout(userId, sessionId) {
    console.log('记录用户登出:', { userId, sessionId, timestamp: new Date() });
  }

  /**
   * 生成状态参数
   */
  generateState() {
    return crypto.randomBytes(16).toString('hex');
  }

  /**
   * 启动应用
   */
  start(port = 3001) {
    this.app.listen(port, () => {
      console.log(`Webhook应用启动在端口 ${port}`);
      console.log(`访问 http://localhost:${port} 开始测试`);
    });
  }
}

// 配置示例
const config = {
  // IdP配置
  idpBaseUrl: 'http://localhost:3000',
  applicationId: 'webhook-app-id',
  clientId: 'webhook-client-id',
  clientSecret: 'webhook-client-secret',
  
  // 应用配置
  redirectUri: 'http://localhost:3001/auth/callback',
  webhookSecret: 'webhook-secret-key'
};

// 创建并启动应用
const webhookApp = new WebhookApp(config);
webhookApp.start();

// 导出供测试使用
module.exports = WebhookApp;
