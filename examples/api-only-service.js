/**
 * API专用服务集成示例
 * 演示如何集成API专用的微服务
 */

const axios = require('axios');
const jwt = require('jsonwebtoken');

/**
 * API专用服务客户端
 * 用于与IdP进行API级别的认证
 */
class ApiOnlyServiceClient {
  constructor(config) {
    this.config = config;
    this.accessToken = null;
    this.tokenExpiresAt = null;
  }

  /**
   * 使用客户端凭据获取访问令牌
   */
  async authenticateWithClientCredentials(scopes = []) {
    try {
      const response = await axios.post(
        `${this.config.idpBaseUrl}/nsa/token/${this.config.applicationId}/custom-oauth`,
        new URLSearchParams({
          grant_type: 'client_credentials',
          client_id: this.config.clientId,
          client_secret: this.config.clientSecret,
          scope: scopes.join(' ')
        }),
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
          }
        }
      );

      const { access_token, expires_in } = response.data;
      
      this.accessToken = access_token;
      this.tokenExpiresAt = new Date(Date.now() + expires_in * 1000);

      console.log('客户端凭据认证成功');
      return access_token;

    } catch (error) {
      console.error('客户端凭据认证失败:', error.response?.data || error.message);
      throw error;
    }
  }

  /**
   * 使用API密钥认证
   */
  async authenticateWithApiKey(apiKey, userId) {
    try {
      const response = await axios.post(
        `${this.config.idpBaseUrl}/nsa/token/${this.config.applicationId}/custom-oauth`,
        new URLSearchParams({
          grant_type: 'custom:api_key',
          api_key: apiKey,
          user_id: userId,
          client_id: this.config.clientId,
          client_secret: this.config.clientSecret
        }),
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
          }
        }
      );

      const { access_token, expires_in } = response.data;
      
      this.accessToken = access_token;
      this.tokenExpiresAt = new Date(Date.now() + expires_in * 1000);

      console.log('API密钥认证成功');
      return access_token;

    } catch (error) {
      console.error('API密钥认证失败:', error.response?.data || error.message);
      throw error;
    }
  }

  /**
   * 检查令牌是否有效
   */
  isTokenValid() {
    return this.accessToken && this.tokenExpiresAt && new Date() < this.tokenExpiresAt;
  }

  /**
   * 确保有有效的访问令牌
   */
  async ensureValidToken() {
    if (!this.isTokenValid()) {
      await this.authenticateWithClientCredentials(['api']);
    }
  }

  /**
   * 发起认证的API请求
   */
  async makeAuthenticatedRequest(method, url, data = null, headers = {}) {
    await this.ensureValidToken();

    const config = {
      method,
      url,
      headers: {
        'Authorization': `Bearer ${this.accessToken}`,
        'Content-Type': 'application/json',
        ...headers
      }
    };

    if (data) {
      config.data = data;
    }

    try {
      const response = await axios(config);
      return response.data;
    } catch (error) {
      if (error.response?.status === 401) {
        // 令牌可能已过期，重新认证
        this.accessToken = null;
        this.tokenExpiresAt = null;
        await this.ensureValidToken();
        
        // 重试请求
        config.headers['Authorization'] = `Bearer ${this.accessToken}`;
        const retryResponse = await axios(config);
        return retryResponse.data;
      }
      throw error;
    }
  }

  /**
   * 获取用户信息
   */
  async getUserInfo() {
    return await this.makeAuthenticatedRequest(
      'GET',
      `${this.config.idpBaseUrl}/nsa/userinfo/${this.config.applicationId}/custom-oauth`
    );
  }

  /**
   * 验证外部令牌
   */
  async validateExternalToken(token) {
    try {
      const response = await axios.post(
        `${this.config.idpBaseUrl}/nsa/token/validate`,
        { token },
        {
          headers: {
            'Authorization': `Bearer ${this.accessToken}`,
            'Content-Type': 'application/json'
          }
        }
      );

      return response.data;

    } catch (error) {
      console.error('令牌验证失败:', error.response?.data || error.message);
      return { valid: false, error: error.message };
    }
  }
}

/**
 * 订单处理微服务示例
 * 演示API专用服务的实际使用
 */
class OrderProcessingService {
  constructor(config) {
    this.apiClient = new ApiOnlyServiceClient(config);
    this.orders = new Map(); // 模拟订单存储
  }

  /**
   * 初始化服务
   */
  async initialize() {
    try {
      // 使用客户端凭据认证
      await this.apiClient.authenticateWithClientCredentials([
        'orders:read',
        'orders:write',
        'inventory:read'
      ]);

      console.log('订单处理服务初始化成功');
    } catch (error) {
      console.error('服务初始化失败:', error);
      throw error;
    }
  }

  /**
   * 创建订单
   */
  async createOrder(orderData, userToken) {
    try {
      // 验证用户令牌
      const tokenValidation = await this.apiClient.validateExternalToken(userToken);
      if (!tokenValidation.valid) {
        throw new Error('无效的用户令牌');
      }

      const userId = tokenValidation.user_id;

      // 检查库存（需要inventory:read权限）
      const inventoryCheck = await this.checkInventory(orderData.items);
      if (!inventoryCheck.available) {
        throw new Error('库存不足');
      }

      // 创建订单
      const orderId = this.generateOrderId();
      const order = {
        id: orderId,
        userId,
        items: orderData.items,
        total: orderData.total,
        status: 'pending',
        createdAt: new Date(),
        updatedAt: new Date()
      };

      this.orders.set(orderId, order);

      // 记录审计日志
      await this.logOrderEvent('order_created', orderId, userId);

      console.log(`订单 ${orderId} 创建成功`);
      return order;

    } catch (error) {
      console.error('创建订单失败:', error);
      throw error;
    }
  }

  /**
   * 获取订单
   */
  async getOrder(orderId, userToken) {
    try {
      // 验证用户令牌
      const tokenValidation = await this.apiClient.validateExternalToken(userToken);
      if (!tokenValidation.valid) {
        throw new Error('无效的用户令牌');
      }

      const order = this.orders.get(orderId);
      if (!order) {
        throw new Error('订单不存在');
      }

      // 检查用户权限
      if (order.userId !== tokenValidation.user_id) {
        throw new Error('无权访问此订单');
      }

      return order;

    } catch (error) {
      console.error('获取订单失败:', error);
      throw error;
    }
  }

  /**
   * 更新订单状态
   */
  async updateOrderStatus(orderId, status, adminToken) {
    try {
      // 验证管理员令牌
      const tokenValidation = await this.apiClient.validateExternalToken(adminToken);
      if (!tokenValidation.valid || !tokenValidation.roles?.includes('admin')) {
        throw new Error('需要管理员权限');
      }

      const order = this.orders.get(orderId);
      if (!order) {
        throw new Error('订单不存在');
      }

      order.status = status;
      order.updatedAt = new Date();

      // 记录审计日志
      await this.logOrderEvent('order_status_updated', orderId, tokenValidation.user_id, { status });

      console.log(`订单 ${orderId} 状态更新为 ${status}`);
      return order;

    } catch (error) {
      console.error('更新订单状态失败:', error);
      throw error;
    }
  }

  /**
   * 检查库存
   */
  async checkInventory(items) {
    // 模拟库存检查
    // 在实际应用中，这里会调用库存服务API
    
    console.log('检查库存:', items);
    
    // 使用认证的API请求调用库存服务
    try {
      const inventoryData = await this.apiClient.makeAuthenticatedRequest(
        'POST',
        `${this.config.inventoryServiceUrl}/check`,
        { items }
      );

      return inventoryData;

    } catch (error) {
      console.error('库存检查失败:', error);
      // 模拟返回
      return { available: true };
    }
  }

  /**
   * 记录审计日志
   */
  async logOrderEvent(action, orderId, userId, details = {}) {
    try {
      await this.apiClient.makeAuthenticatedRequest(
        'POST',
        `${this.config.auditServiceUrl}/log`,
        {
          action,
          resource: 'order',
          resourceId: orderId,
          userId,
          details,
          timestamp: new Date().toISOString()
        }
      );

    } catch (error) {
      console.error('记录审计日志失败:', error);
      // 不抛出错误，避免影响主业务流程
    }
  }

  /**
   * 生成订单ID
   */
  generateOrderId() {
    return `ORD-${Date.now()}-${Math.random().toString(36).substr(2, 6).toUpperCase()}`;
  }

  /**
   * 健康检查
   */
  async healthCheck() {
    try {
      // 检查IdP连接
      const isTokenValid = this.apiClient.isTokenValid();
      if (!isTokenValid) {
        await this.apiClient.authenticateWithClientCredentials(['health']);
      }

      return {
        status: 'healthy',
        timestamp: new Date().toISOString(),
        idp_connection: 'ok',
        orders_count: this.orders.size
      };

    } catch (error) {
      return {
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        error: error.message
      };
    }
  }
}

// 使用示例
async function main() {
  const config = {
    // IdP配置
    idpBaseUrl: 'http://localhost:3000',
    applicationId: 'order-service-app-id',
    clientId: 'order-service-client-id',
    clientSecret: 'order-service-client-secret',
    
    // 其他服务配置
    inventoryServiceUrl: 'http://localhost:3002',
    auditServiceUrl: 'http://localhost:3003'
  };

  const orderService = new OrderProcessingService(config);

  try {
    // 初始化服务
    await orderService.initialize();

    // 模拟创建订单
    const userToken = 'user-jwt-token-here';
    const orderData = {
      items: [
        { productId: 'PROD-001', quantity: 2, price: 29.99 },
        { productId: 'PROD-002', quantity: 1, price: 49.99 }
      ],
      total: 109.97
    };

    const order = await orderService.createOrder(orderData, userToken);
    console.log('订单创建成功:', order);

    // 获取订单
    const retrievedOrder = await orderService.getOrder(order.id, userToken);
    console.log('订单获取成功:', retrievedOrder);

    // 健康检查
    const health = await orderService.healthCheck();
    console.log('健康检查:', health);

  } catch (error) {
    console.error('服务运行失败:', error);
  }
}

// 如果直接运行此文件，执行示例
if (require.main === module) {
  main();
}

module.exports = { ApiOnlyServiceClient, OrderProcessingService };
