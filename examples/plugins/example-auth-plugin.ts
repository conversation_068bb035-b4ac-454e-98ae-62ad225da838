/**
 * 示例认证插件
 * 演示如何创建自定义认证插件
 */

import { IAuthPlugin, IProtocolAdapter } from '@/types/protocol-adapter';
import { BaseProtocolAdapter } from '@/adapters/base-protocol.adapter';
import { 
  AuthenticationRequest, 
  AuthenticationResponse, 
  ProtocolConfig 
} from '@/types/protocol-adapter';
import { Request, Response } from 'express';
import { logger } from '@/config/logger';

/**
 * 示例协议适配器
 * 实现一个简单的基于令牌的认证协议
 */
class ExampleTokenAdapter extends BaseProtocolAdapter {
  readonly name = 'example-token';
  readonly version = '1.0.0';
  readonly supportedMethods = ['token_auth', 'api_key_auth'];

  private validTokens = new Set<string>();
  private apiKeys = new Map<string, any>();

  protected async onInitialize(): Promise<void> {
    // 初始化一些示例令牌和API密钥
    this.validTokens.add('example-token-123');
    this.validTokens.add('example-token-456');
    
    this.apiKeys.set('api-key-abc', {
      userId: 'user-1',
      permissions: ['read', 'write'],
      expiresAt: new Date(Date.now() + 86400000) // 24小时后过期
    });
    
    this.apiKeys.set('api-key-xyz', {
      userId: 'user-2',
      permissions: ['read'],
      expiresAt: new Date(Date.now() + 86400000)
    });

    logger.info('示例令牌适配器初始化完成', {
      tokenCount: this.validTokens.size,
      apiKeyCount: this.apiKeys.size
    });
  }

  protected async performAuthentication(
    request: AuthenticationRequest,
    application: any
  ): Promise<AuthenticationResponse> {
    const authMethod = request.authMethod || 'token_auth';

    switch (authMethod) {
      case 'token_auth':
        return await this.handleTokenAuth(request, application);
      
      case 'api_key_auth':
        return await this.handleApiKeyAuth(request, application);
      
      default:
        return {
          success: false,
          error: 'UNSUPPORTED_METHOD',
          message: `不支持的认证方法: ${authMethod}`
        };
    }
  }

  private async handleTokenAuth(
    request: AuthenticationRequest,
    application: any
  ): Promise<AuthenticationResponse> {
    const token = request.accessToken || request.headers?.authorization?.replace('Bearer ', '');
    
    if (!token) {
      return {
        success: false,
        error: 'MISSING_TOKEN',
        message: '缺少认证令牌'
      };
    }

    if (!this.validTokens.has(token)) {
      return {
        success: false,
        error: 'INVALID_TOKEN',
        message: '无效的认证令牌'
      };
    }

    return {
      success: true,
      data: {
        authenticated: true,
        token,
        user: {
          id: 'example-user',
          username: 'example',
          email: '<EMAIL>'
        }
      }
    };
  }

  private async handleApiKeyAuth(
    request: AuthenticationRequest,
    application: any
  ): Promise<AuthenticationResponse> {
    const apiKey = request.apiKey || request.headers?.['x-api-key'];
    
    if (!apiKey) {
      return {
        success: false,
        error: 'MISSING_API_KEY',
        message: '缺少API密钥'
      };
    }

    const keyData = this.apiKeys.get(apiKey);
    if (!keyData) {
      return {
        success: false,
        error: 'INVALID_API_KEY',
        message: '无效的API密钥'
      };
    }

    // 检查是否过期
    if (keyData.expiresAt < new Date()) {
      return {
        success: false,
        error: 'EXPIRED_API_KEY',
        message: 'API密钥已过期'
      };
    }

    return {
      success: true,
      data: {
        authenticated: true,
        apiKey,
        user: {
          id: keyData.userId,
          permissions: keyData.permissions
        }
      }
    };
  }

  protected async performTokenExchange(request: Request): Promise<AuthenticationResponse> {
    return {
      success: false,
      error: 'NOT_SUPPORTED',
      message: '令牌交换不支持'
    };
  }

  protected async performUserInfoRetrieval(request: Request): Promise<AuthenticationResponse> {
    const token = request.headers.authorization?.replace('Bearer ', '');
    
    if (!token || !this.validTokens.has(token)) {
      return {
        success: false,
        error: 'INVALID_TOKEN',
        message: '无效的令牌'
      };
    }

    return {
      success: true,
      data: {
        sub: 'example-user',
        name: 'Example User',
        email: '<EMAIL>',
        preferred_username: 'example',
        email_verified: true
      }
    };
  }

  protected async performLogout(request: Request): Promise<AuthenticationResponse> {
    const token = request.headers.authorization?.replace('Bearer ', '');
    
    if (token) {
      this.validTokens.delete(token);
      logger.info('令牌已注销', { token: token.substring(0, 10) + '...' });
    }

    return {
      success: true,
      data: {
        message: '注销成功'
      }
    };
  }

  protected async validateCustomConfig(config: ProtocolConfig): Promise<boolean> {
    // 验证配置是否包含必要的端点
    return !!(config.endpoints?.authentication);
  }

  async generateMetadata(): Promise<Record<string, any>> {
    return {
      name: this.name,
      version: this.version,
      supportedMethods: this.supportedMethods,
      endpoints: this.config.endpoints,
      features: ['token_validation', 'api_key_validation', 'user_info'],
      tokenCount: this.validTokens.size,
      apiKeyCount: this.apiKeys.size
    };
  }

  // 插件特定方法
  public addToken(token: string): void {
    this.validTokens.add(token);
    logger.info('添加新令牌', { token: token.substring(0, 10) + '...' });
  }

  public removeToken(token: string): boolean {
    const removed = this.validTokens.delete(token);
    if (removed) {
      logger.info('移除令牌', { token: token.substring(0, 10) + '...' });
    }
    return removed;
  }

  public addApiKey(apiKey: string, userData: any): void {
    this.apiKeys.set(apiKey, userData);
    logger.info('添加新API密钥', { apiKey: apiKey.substring(0, 10) + '...' });
  }

  public removeApiKey(apiKey: string): boolean {
    const removed = this.apiKeys.delete(apiKey);
    if (removed) {
      logger.info('移除API密钥', { apiKey: apiKey.substring(0, 10) + '...' });
    }
    return removed;
  }
}

/**
 * 示例认证插件实现
 */
export class ExampleAuthPlugin implements IAuthPlugin {
  readonly name = 'example-auth-plugin';
  readonly version = '1.0.0';
  readonly description = '示例认证插件，演示插件系统的使用';

  private adapter?: ExampleTokenAdapter;
  private customHandlers = new Map<string, Function>();

  async initialize(config: Record<string, any>): Promise<void> {
    logger.info('初始化示例认证插件', { config });

    // 初始化自定义处理器
    this.setupCustomHandlers();

    logger.info('示例认证插件初始化完成');
  }

  async destroy(): Promise<void> {
    logger.info('销毁示例认证插件');
    
    if (this.adapter) {
      await this.adapter.destroy();
    }
    
    this.customHandlers.clear();
  }

  getProtocolAdapters(): Record<string, new () => IProtocolAdapter> {
    return {
      'example-token': ExampleTokenAdapter
    };
  }

  getCustomHandlers(): Record<string, Function> {
    return Object.fromEntries(this.customHandlers);
  }

  async healthCheck(): Promise<boolean> {
    try {
      // 执行健康检查逻辑
      const isHealthy = this.customHandlers.size > 0;
      
      logger.info('示例插件健康检查', { 
        isHealthy,
        handlerCount: this.customHandlers.size 
      });
      
      return isHealthy;
    } catch (error) {
      logger.error('示例插件健康检查失败', { error: error.message });
      return false;
    }
  }

  private setupCustomHandlers(): void {
    // 预认证处理器
    this.customHandlers.set('example_pre_auth', async (request: any) => {
      logger.info('执行预认证处理', { 
        clientId: request.clientId,
        timestamp: new Date().toISOString()
      });
      
      // 可以在这里添加额外的验证逻辑
      return {
        success: true,
        data: {
          preprocessed: true,
          timestamp: new Date().toISOString()
        }
      };
    });

    // 后认证处理器
    this.customHandlers.set('example_post_auth', async (response: any, request: any) => {
      logger.info('执行后认证处理', { 
        success: response.success,
        clientId: request.clientId 
      });
      
      if (response.success) {
        // 添加额外的用户信息
        response.data = {
          ...response.data,
          postProcessed: true,
          processingTime: new Date().toISOString(),
          pluginVersion: this.version
        };
      }
      
      return response;
    });

    // 令牌转换处理器
    this.customHandlers.set('example_token_transform', async (token: any) => {
      logger.info('执行令牌转换', { tokenType: typeof token });
      
      return {
        ...token,
        transformed: true,
        transformedAt: new Date().toISOString(),
        transformedBy: this.name
      };
    });

    // 用户信息转换处理器
    this.customHandlers.set('example_user_transform', async (userInfo: any) => {
      logger.info('执行用户信息转换', { userId: userInfo.id });
      
      return {
        ...userInfo,
        enhanced: true,
        enhancedAt: new Date().toISOString(),
        enhancedBy: this.name,
        additionalClaims: {
          plugin_processed: true,
          processing_version: this.version
        }
      };
    });

    // 自定义验证处理器
    this.customHandlers.set('example_custom_validation', async (data: any) => {
      logger.info('执行自定义验证', { dataType: typeof data });
      
      // 示例验证逻辑
      const isValid = data && typeof data === 'object';
      
      return {
        isValid,
        validatedAt: new Date().toISOString(),
        validatedBy: this.name,
        validationRules: ['type_check', 'existence_check']
      };
    });

    logger.info('自定义处理器设置完成', { 
      handlerCount: this.customHandlers.size,
      handlers: Array.from(this.customHandlers.keys())
    });
  }

  // 插件特定的公共方法
  public getAdapterInstance(): ExampleTokenAdapter | undefined {
    return this.adapter;
  }

  public addCustomHandler(name: string, handler: Function): void {
    this.customHandlers.set(name, handler);
    logger.info('添加自定义处理器', { name });
  }

  public removeCustomHandler(name: string): boolean {
    const removed = this.customHandlers.delete(name);
    if (removed) {
      logger.info('移除自定义处理器', { name });
    }
    return removed;
  }

  public getHandlerNames(): string[] {
    return Array.from(this.customHandlers.keys());
  }
}

// 导出插件类（用于动态加载）
export default ExampleAuthPlugin;
