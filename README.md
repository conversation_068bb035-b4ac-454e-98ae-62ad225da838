# 身份提供商 (Identity Provider - IdP)

一个现代化的身份提供商系统，提供统一身份认证和授权服务，支持多种认证方式和SSO协议。

## ✨ 特性

### 🔐 身份认证与授权
- **多因素认证(MFA)**: 支持TOTP、邮件验证码、短信验证码、生物识别
- **联合身份认证**: 集成Google、GitHub、微信等第三方OAuth提供商
- **单点登录(SSO)**: 支持OpenID Connect、OAuth 2.0、SAML 2.0
- **零信任模式**: 基于风险评估的自适应认证
- **会话管理**: 智能会话控制和安全策略

### 🛡️ 安全防护
- **自动化安全扫描**: 实时漏洞检测和威胁分析
- **入侵检测系统**: 智能异常行为识别
- **安全审计**: 全面的安全事件记录和分析
- **访问控制**: 基于角色的权限管理(RBAC)
- **数据加密**: 端到端数据保护

### 👥 用户管理
- **用户生命周期管理**: 注册、激活、禁用、删除
- **用户画像**: 智能用户行为分析
- **批量操作**: 支持批量用户管理
- **自助服务**: 用户自助密码重置和资料管理

### 📊 监控与分析
- **实时监控**: 系统健康状态监控
- **性能分析**: 自动化性能测试和基准测试
- **业务指标**: 用户活跃度、登录成功率等关键指标
- **告警系统**: 智能告警和通知机制

### 🌍 国际化与本地化
- **多语言支持**: 中文、英文、日文、韩文等
- **动态翻译**: 实时内容翻译和本地化
- **区域设置**: 时区、日期格式、货币格式本地化
- **RTL支持**: 从右到左语言布局支持

### 🚀 性能与扩展
- **CDN集成**: 静态资源全球加速
- **缓存优化**: 多层缓存策略
- **负载均衡**: 高可用性架构
- **微服务架构**: 模块化设计，易于扩展

### 🧪 测试与质量
- **端到端测试**: 完整的E2E测试套件
- **自动化测试**: 单元测试、集成测试、性能测试
- **代码质量**: ESLint、Prettier、SonarQube集成
- **持续集成**: GitHub Actions自动化流水线

## 🏗️ 技术栈

### 后端技术
- **运行时**: Node.js 18+ + TypeScript
- **Web框架**: Express.js + Helmet + CORS
- **数据库**: PostgreSQL 13+ + Prisma ORM
- **缓存**: Redis + 内存缓存
- **认证**: JWT + Passport.js + OAuth 2.0
- **MFA**: Speakeasy (TOTP) + Nodemailer + Twilio
- **安全**: bcrypt + rate-limiting + CSRF保护

### 前端技术
- **框架**: React 18 + TypeScript
- **构建工具**: Vite + ESBuild
- **UI组件**: Ant Design + 自定义组件
- **状态管理**: React Query + Context API
- **路由**: React Router v6
- **国际化**: react-i18next + 动态翻译

### 测试与质量
- **测试框架**: Jest + Supertest + Playwright
- **E2E测试**: 自动化端到端测试套件
- **性能测试**: 自动化负载测试和基准测试
- **代码质量**: ESLint + Prettier + SonarQube
- **覆盖率**: Istanbul + 自动化报告

### 监控与运维
- **日志**: Winston + 结构化日志
- **监控**: Prometheus + Grafana + 自定义指标
- **告警**: 邮件 + Slack + Webhook通知
- **部署**: Docker + Docker Compose + CI/CD
- **CDN**: Cloudflare + AWS CloudFront支持

### 安全与合规
- **漏洞扫描**: 自动化安全扫描
- **入侵检测**: 实时威胁检测
- **审计日志**: 完整的操作审计
- **数据保护**: 加密存储 + 传输加密
- **合规**: GDPR + SOC2 + ISO27001支持

## 🚀 快速开始

### 环境要求

- **Node.js**: 18.0+ (推荐 LTS 版本)
- **PostgreSQL**: 13+ (推荐 15+)
- **Redis**: 6.0+ (用于缓存和会话存储)
- **npm**: 8.0+ 或 **yarn**: 1.22+
- **Docker**: 20.0+ (可选，用于容器化部署)
- **Git**: 2.30+ (版本控制)

### 安装

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd id-provider
   ```

2. **运行设置脚本**
   ```bash
   chmod +x scripts/setup.sh
   ./scripts/setup.sh
   ```

3. **配置环境变量**

   编辑 `.env` 文件，配置数据库连接和其他必需参数：
   ```bash
   # 数据库配置
   DATABASE_URL="postgresql://username:password@localhost:5432/id_provider"
   REDIS_URL="redis://localhost:6379"

   # JWT密钥
   JWT_SECRET="your-super-secret-jwt-key"
   JWT_REFRESH_SECRET="your-super-secret-refresh-key"

   # 应用配置
   NODE_ENV="development"
   PORT=3000
   FRONTEND_URL="http://localhost:3001"

   # 邮件配置
   SMTP_HOST="smtp.gmail.com"
   SMTP_PORT=587
   SMTP_USER="<EMAIL>"
   SMTP_PASS="your-app-password"

   # OAuth提供商配置 (可选)
   GOOGLE_CLIENT_ID="your-google-client-id"
   GOOGLE_CLIENT_SECRET="your-google-client-secret"
   GITHUB_CLIENT_ID="your-github-client-id"
   GITHUB_CLIENT_SECRET="your-github-client-secret"

   # CDN配置 (可选)
   CDN_ENABLED=true
   CDN_PROVIDER="cloudflare"
   CDN_BASE_URL="https://cdn.example.com"

   # 翻译服务配置 (可选)
   TRANSLATION_PROVIDER="google"
   GOOGLE_TRANSLATE_API_KEY="your-translate-api-key"

   # 安全扫描配置 (可选)
   AUTOMATED_SECURITY_SCANNER_ENABLED=true
   SECURITY_SCAN_INTERVAL="0 2 * * *"
   ```

4. **初始化数据库**
   ```bash
   # 创建数据库
   createdb id_provider

   # 运行迁移
   npm run db:migrate

   # 初始化种子数据
   npm run db:seed
   ```

5. **启动服务**
   ```bash
   # 开发模式
   npm run dev

   # 生产模式
   npm run build
   npm start
   ```

服务将在 `http://localhost:3000` 启动。

### 默认账户

- **管理员账户**: <EMAIL> / admin123456

## 📖 API 文档

### 认证相关

- `POST /api/v1/auth/register` - 用户注册
- `POST /api/v1/auth/login` - 用户登录
- `POST /api/v1/auth/logout` - 用户登出
- `POST /api/v1/auth/refresh-token` - 刷新令牌
- `POST /api/v1/auth/forgot-password` - 忘记密码
- `POST /api/v1/auth/reset-password` - 重置密码

### 用户管理

- `GET /api/v1/me` - 获取当前用户信息
- `PUT /api/v1/me` - 更新用户资料
- `GET /api/v1/me/sessions` - 获取用户会话
- `DELETE /api/v1/me/sessions/:id` - 终止会话

### 多因素认证

- `GET /api/v1/me/mfa` - 获取MFA状态
- `POST /api/v1/me/mfa/enable` - 启用MFA
- `POST /api/v1/me/mfa/verify` - 验证MFA
- `DELETE /api/v1/me/mfa/devices/:id` - 禁用MFA设备

### 安全管理

- `GET /api/v1/security/audit/logs` - 获取安全审计日志
- `GET /api/v1/security/scan/status` - 获取安全扫描状态
- `POST /api/v1/security/scan/trigger` - 触发安全扫描
- `GET /api/v1/security/threats` - 获取威胁检测结果
- `GET /api/v1/security/vulnerabilities` - 获取漏洞扫描结果

### 性能监控

- `GET /api/v1/performance/metrics` - 获取性能指标
- `GET /api/v1/performance/tests` - 获取性能测试结果
- `POST /api/v1/performance/tests/run` - 运行性能测试
- `GET /api/v1/performance/automated/schedules` - 获取自动化测试计划
- `POST /api/v1/performance/automated/trigger/:scheduleId` - 触发自动化测试

### 国际化管理

- `GET /api/v1/i18n/languages` - 获取支持的语言列表
- `POST /api/v1/i18n/switch` - 切换语言
- `GET /api/v1/i18n/translate/:key` - 获取翻译文本
- `POST /api/v1/i18n/translate` - 动态翻译文本
- `POST /api/v1/i18n/translate/batch` - 批量翻译
- `POST /api/v1/i18n/auto-translate` - 自动翻译缺失键

### CDN管理

- `GET /api/v1/cdn/status` - 获取CDN状态
- `GET /api/v1/cdn/asset-url` - 获取资源CDN URL
- `POST /api/v1/cdn/purge` - 清除CDN缓存
- `POST /api/v1/cdn/warmup` - 预热CDN缓存
- `GET /api/v1/cdn/statistics` - 获取CDN统计信息

### 系统监控

- `GET /api/v1/health` - 系统健康检查
- `GET /api/v1/metrics` - 系统指标
- `GET /api/v1/status` - 系统状态
- `GET /api/v1/version` - 系统版本信息

详细的API文档请查看 [docs/api.md](docs/api.md)。

## 🧪 测试

### 单元测试和集成测试
```bash
# 运行所有测试
npm test

# 运行测试并生成覆盖率报告
npm run test:coverage

# 监听模式运行测试
npm run test:watch

# 运行特定测试文件
npm test -- --testPathPattern=auth

# 运行集成测试
npm run test:integration
```

### 端到端测试
```bash
# 运行E2E测试
npm run test:e2e

# 运行E2E测试 (无头模式)
npm run test:e2e:headless

# 运行特定E2E测试
npm run test:e2e -- --grep "登录流程"
```

### 性能测试
```bash
# 运行性能测试
npm run test:performance

# 运行负载测试
npm run test:load

# 运行基准测试
npm run test:benchmark
```

### 安全测试
```bash
# 运行安全扫描
npm run test:security

# 运行漏洞检测
npm run test:vulnerability

# 运行依赖安全检查
npm audit
```

## 📚 文档

> 📖 **完整文档中心**: [docs/README.md](docs/README.md) - 查看所有技术文档的详细索引

### 🏗️ 核心文档
- [系统架构设计](docs/architecture.md) - 整体系统架构和技术选型
- [数据库设计](docs/database.md) - 数据模型和关系设计
- [API接口文档](docs/api.md) - 完整的REST API文档
- [开发指南](docs/development.md) - 开发环境搭建和编码规范

### 🔐 认证与安全
- [身份认证界面系统](docs/authentication-ui-complete.md) - 前端认证界面完整指南
- [多因素认证(MFA)](docs/mfa.md) - MFA配置和使用说明
- [OAuth集成指南](docs/oauth.md) - OAuth 2.0和第三方登录
- [安全加固审计](docs/security-hardening-audit.md) - 安全配置和审计
- [零信任架构](docs/zero-trust-architecture.md) - 零信任安全模型

### 🚀 部署与运维
- [部署文档](docs/deployment/) - 生产环境部署指南
- [监控日志系统](docs/monitoring-logging-system.md) - 系统监控和日志管理
- [性能优化监控](docs/performance-optimization-monitoring.md) - 性能调优指南
- [CDN集成指南](docs/CDN集成指南.md) - CDN配置和使用

### 🌍 国际化与集成
- [国际化完善指南](docs/国际化完善指南.md) - 多语言支持和翻译管理
- [API网关集成指南](docs/API网关集成指南.md) - API网关集成
- [Redis缓存集成指南](docs/Redis缓存集成指南.md) - 缓存系统配置

### 📊 项目状态
- [功能清单](docs/功能清单.md) - 项目功能完成状态
- [项目交付报告](docs/项目交付报告-2025-08-28.md) - 最新项目交付状态

## 🔧 开发

### 项目结构

```
id-provider/
├── src/                          # 后端源代码
│   ├── config/                   # 配置文件
│   │   ├── database.ts           # 数据库配置
│   │   ├── logger.ts             # 日志配置
│   │   └── redis.ts              # Redis配置
│   ├── controllers/              # 控制器层
│   │   ├── auth.controller.ts    # 认证控制器
│   │   ├── user.controller.ts    # 用户管理控制器
│   │   ├── security.controller.ts # 安全管理控制器
│   │   ├── performance.controller.ts # 性能监控控制器
│   │   ├── i18n.controller.ts    # 国际化控制器
│   │   └── cdn.controller.ts     # CDN管理控制器
│   ├── middleware/               # 中间件
│   │   ├── auth.middleware.ts    # 认证中间件
│   │   ├── rbac.middleware.ts    # 权限控制中间件
│   │   ├── security.middleware.ts # 安全中间件
│   │   ├── cdn.middleware.ts     # CDN中间件
│   │   └── i18n.middleware.ts    # 国际化中间件
│   ├── models/                   # 数据模型
│   ├── routes/                   # 路由定义
│   ├── services/                 # 业务逻辑层
│   │   ├── auth.service.ts       # 认证服务
│   │   ├── user.service.ts       # 用户服务
│   │   ├── mfa.service.ts        # MFA服务
│   │   ├── security-audit.service.ts # 安全审计服务
│   │   ├── automated-security-scanner.service.ts # 自动化安全扫描
│   │   ├── performance-test.service.ts # 性能测试服务
│   │   ├── automated-performance-testing.service.ts # 自动化性能测试
│   │   ├── i18n.service.ts       # 国际化服务
│   │   ├── dynamic-translation.service.ts # 动态翻译服务
│   │   └── cdn.service.ts        # CDN服务
│   ├── utils/                    # 工具函数
│   ├── types/                    # TypeScript类型定义
│   └── test/                     # 测试文件
├── frontend/                     # 前端源代码
│   ├── src/
│   │   ├── components/           # React组件
│   │   ├── pages/                # 页面组件
│   │   ├── hooks/                # 自定义Hooks
│   │   ├── services/             # API服务
│   │   ├── utils/                # 工具函数
│   │   └── styles/               # 样式文件
│   ├── public/                   # 静态资源
│   └── dist/                     # 构建输出
├── docs/                         # 项目文档
├── locales/                      # 国际化语言包
├── scripts/                      # 构建和部署脚本
├── prisma/                       # 数据库模式和迁移
├── tests/                        # E2E测试
└── docker/                       # Docker配置文件
```

### 开发命令

```bash
# 开发服务器
npm run dev                    # 启动开发服务器 (后端 + 前端)
npm run dev:backend           # 仅启动后端开发服务器
npm run dev:frontend          # 仅启动前端开发服务器

# 构建
npm run build                 # 构建整个项目
npm run build:backend         # 构建后端
npm run build:frontend        # 构建前端

# 代码质量
npm run lint                  # 代码检查
npm run lint:fix              # 自动修复代码问题
npm run format                # 格式化代码
npm run type-check            # TypeScript类型检查

# 数据库
npm run db:migrate            # 运行数据库迁移
npm run db:generate           # 生成Prisma客户端
npm run db:studio             # 打开Prisma Studio
npm run db:seed               # 填充初始数据
npm run db:reset              # 重置数据库

# 测试
npm run test                  # 运行所有测试
npm run test:unit             # 运行单元测试
npm run test:integration      # 运行集成测试
npm run test:e2e              # 运行E2E测试
npm run test:performance      # 运行性能测试
npm run test:security         # 运行安全测试

# 部署
npm run deploy:staging        # 部署到测试环境
npm run deploy:production     # 部署到生产环境
npm run docker:build          # 构建Docker镜像
npm run docker:run            # 运行Docker容器

# 工具
npm run clean                 # 清理构建文件
npm run docs:generate         # 生成API文档
npm run security:scan         # 运行安全扫描
npm run performance:benchmark # 运行性能基准测试
```

## 🐳 Docker 部署

### 单容器部署
```bash
# 构建镜像
docker build -t id-provider .

# 运行容器
docker run -p 3000:3000 --env-file .env id-provider

# 运行容器 (带数据库)
docker run -d \
  --name id-provider \
  -p 3000:3000 \
  --env-file .env \
  -v $(pwd)/data:/app/data \
  id-provider
```

### Docker Compose 部署
```bash
# 启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down

# 重新构建并启动
docker-compose up -d --build
```

### 生产环境部署
```bash
# 使用生产配置
docker-compose -f docker-compose.prod.yml up -d

# 扩展服务实例
docker-compose up -d --scale app=3

# 滚动更新
docker-compose up -d --no-deps app
```

### 健康检查
```bash
# 检查容器健康状态
docker-compose exec app curl http://localhost:3000/health

# 查看容器资源使用
docker stats

# 查看容器详细信息
docker inspect id-provider
```

## 🤝 贡献

欢迎贡献代码！请查看 [开发指南](docs/development.md) 了解详细信息。

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add some amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🆘 支持

如果您遇到问题或有疑问，请：

1. 查看 [文档](docs/)
2. 搜索 [Issues](../../issues)
3. 创建新的 [Issue](../../issues/new)

## 🗺️ 路线图

- [ ] 联合身份认证实现
- [ ] SSO协议支持
- [ ] 零信任模式
- [ ] 前端管理界面
- [ ] 移动应用支持
- [ ] 高级审计功能
- [ ] 性能优化
- [ ] 国际化支持