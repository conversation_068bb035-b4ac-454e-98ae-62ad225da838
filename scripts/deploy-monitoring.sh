#!/bin/bash

# 监控系统部署脚本
# 部署Prometheus、<PERSON><PERSON>和相关监控组件

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查依赖..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装，请先安装Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose未安装，请先安装Docker Compose"
        exit 1
    fi
    
    log_success "依赖检查通过"
}

# 创建监控目录结构
create_directories() {
    log_info "创建监控目录结构..."
    
    mkdir -p monitoring/{prometheus/{rules,data},grafana/{provisioning/{datasources,dashboards},dashboards,data},alertmanager/data}
    
    # 设置权限
    chmod 755 monitoring/prometheus/data
    chmod 755 monitoring/grafana/data
    chmod 755 monitoring/alertmanager/data
    
    log_success "目录结构创建完成"
}

# 验证配置文件
validate_configs() {
    log_info "验证配置文件..."
    
    # 检查Prometheus配置
    if [ ! -f "monitoring/prometheus/prometheus.yml" ]; then
        log_error "Prometheus配置文件不存在: monitoring/prometheus/prometheus.yml"
        exit 1
    fi
    
    # 检查AlertManager配置
    if [ ! -f "monitoring/alertmanager/alertmanager.yml" ]; then
        log_error "AlertManager配置文件不存在: monitoring/alertmanager/alertmanager.yml"
        exit 1
    fi
    
    # 检查Grafana配置
    if [ ! -f "monitoring/grafana/provisioning/datasources/prometheus.yml" ]; then
        log_error "Grafana数据源配置文件不存在"
        exit 1
    fi
    
    log_success "配置文件验证通过"
}

# 启动监控服务
start_monitoring() {
    log_info "启动监控服务..."
    
    # 停止现有服务
    docker-compose -f docker-compose.monitoring.yml down 2>/dev/null || true
    
    # 启动服务
    docker-compose -f docker-compose.monitoring.yml up -d
    
    log_success "监控服务启动完成"
}

# 等待服务就绪
wait_for_services() {
    log_info "等待服务就绪..."
    
    # 等待Prometheus
    log_info "等待Prometheus启动..."
    for i in {1..30}; do
        if curl -s http://localhost:9090/-/healthy > /dev/null 2>&1; then
            log_success "Prometheus已就绪"
            break
        fi
        if [ $i -eq 30 ]; then
            log_error "Prometheus启动超时"
            exit 1
        fi
        sleep 2
    done
    
    # 等待Grafana
    log_info "等待Grafana启动..."
    for i in {1..30}; do
        if curl -s http://localhost:3001/api/health > /dev/null 2>&1; then
            log_success "Grafana已就绪"
            break
        fi
        if [ $i -eq 30 ]; then
            log_error "Grafana启动超时"
            exit 1
        fi
        sleep 2
    done
    
    # 等待AlertManager
    log_info "等待AlertManager启动..."
    for i in {1..30}; do
        if curl -s http://localhost:9093/-/healthy > /dev/null 2>&1; then
            log_success "AlertManager已就绪"
            break
        fi
        if [ $i -eq 30 ]; then
            log_error "AlertManager启动超时"
            exit 1
        fi
        sleep 2
    done
}

# 验证监控目标
verify_targets() {
    log_info "验证监控目标..."
    
    # 检查ID Provider应用是否可访问
    if curl -s http://localhost:3000/metrics > /dev/null 2>&1; then
        log_success "ID Provider指标端点可访问"
    else
        log_warning "ID Provider指标端点不可访问，请确保应用正在运行"
    fi
    
    # 检查Prometheus目标状态
    sleep 5
    targets_response=$(curl -s http://localhost:9090/api/v1/targets)
    if echo "$targets_response" | grep -q '"health":"up"'; then
        log_success "Prometheus目标状态正常"
    else
        log_warning "部分Prometheus目标可能不健康"
    fi
}

# 显示访问信息
show_access_info() {
    log_info "监控系统访问信息:"
    echo ""
    echo "🔍 Prometheus:     http://localhost:9090"
    echo "📊 Grafana:        http://localhost:3001 (admin/admin123!)"
    echo "🚨 AlertManager:   http://localhost:9093"
    echo "📈 Node Exporter:  http://localhost:9100/metrics"
    echo "💾 Redis Exporter: http://localhost:9121/metrics"
    echo "🗄️  Postgres Exporter: http://localhost:9187/metrics"
    echo "📦 cAdvisor:       http://localhost:8080"
    echo ""
    echo "🎯 ID Provider指标: http://localhost:3000/metrics"
    echo "🏥 健康检查:       http://localhost:3000/health"
    echo ""
}

# 显示使用说明
show_usage_info() {
    log_info "使用说明:"
    echo ""
    echo "1. 访问Grafana仪表板查看监控数据"
    echo "2. 在Prometheus中查询自定义指标"
    echo "3. 配置AlertManager告警规则"
    echo "4. 使用以下命令管理监控服务:"
    echo ""
    echo "   启动: docker-compose -f docker-compose.monitoring.yml up -d"
    echo "   停止: docker-compose -f docker-compose.monitoring.yml down"
    echo "   查看日志: docker-compose -f docker-compose.monitoring.yml logs -f"
    echo "   重启: docker-compose -f docker-compose.monitoring.yml restart"
    echo ""
}

# 清理函数
cleanup() {
    log_info "清理监控服务..."
    docker-compose -f docker-compose.monitoring.yml down
    log_success "监控服务已停止"
}

# 主函数
main() {
    echo "🚀 ID Provider监控系统部署脚本"
    echo "=================================="
    echo ""
    
    case "${1:-deploy}" in
        "deploy")
            check_dependencies
            create_directories
            validate_configs
            start_monitoring
            wait_for_services
            verify_targets
            show_access_info
            show_usage_info
            log_success "监控系统部署完成！"
            ;;
        "start")
            log_info "启动监控服务..."
            docker-compose -f docker-compose.monitoring.yml up -d
            wait_for_services
            show_access_info
            log_success "监控服务启动完成！"
            ;;
        "stop")
            log_info "停止监控服务..."
            docker-compose -f docker-compose.monitoring.yml down
            log_success "监控服务已停止！"
            ;;
        "restart")
            log_info "重启监控服务..."
            docker-compose -f docker-compose.monitoring.yml restart
            wait_for_services
            show_access_info
            log_success "监控服务重启完成！"
            ;;
        "status")
            log_info "监控服务状态:"
            docker-compose -f docker-compose.monitoring.yml ps
            ;;
        "logs")
            log_info "查看监控服务日志:"
            docker-compose -f docker-compose.monitoring.yml logs -f
            ;;
        "cleanup")
            cleanup
            ;;
        "help"|"-h"|"--help")
            echo "用法: $0 [命令]"
            echo ""
            echo "命令:"
            echo "  deploy   - 部署监控系统 (默认)"
            echo "  start    - 启动监控服务"
            echo "  stop     - 停止监控服务"
            echo "  restart  - 重启监控服务"
            echo "  status   - 查看服务状态"
            echo "  logs     - 查看服务日志"
            echo "  cleanup  - 清理监控服务"
            echo "  help     - 显示帮助信息"
            ;;
        *)
            log_error "未知命令: $1"
            echo "使用 '$0 help' 查看可用命令"
            exit 1
            ;;
    esac
}

# 捕获中断信号
trap 'log_warning "脚本被中断"; exit 1' INT TERM

# 执行主函数
main "$@"
