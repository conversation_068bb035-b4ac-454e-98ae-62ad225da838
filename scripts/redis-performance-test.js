/**
 * Redis缓存性能测试脚本
 * 测试Redis缓存的性能和并发处理能力
 */

const Redis = require('ioredis');

// Redis配置
const redisConfig = {
  host: process.env.REDIS_HOST || 'localhost',
  port: parseInt(process.env.REDIS_PORT || '6379'),
  password: process.env.REDIS_PASSWORD,
  db: parseInt(process.env.REDIS_DB || '0'),
  keyPrefix: 'idp:perf:test:',
  retryDelayOnFailover: 100,
  maxRetriesPerRequest: 3,
  lazyConnect: true
};

// 性能测试配置
const testConfig = {
  // 基础性能测试
  basicOps: {
    iterations: 10000,
    concurrency: 100
  },
  
  // 会话缓存测试
  sessionCache: {
    sessions: 1000,
    concurrency: 50
  },
  
  // 速率限制测试
  rateLimit: {
    requests: 5000,
    concurrency: 100,
    windowMs: 60000
  },
  
  // 大数据测试
  largeData: {
    iterations: 1000,
    dataSize: 1024 * 10 // 10KB
  }
};

class RedisPerformanceTester {
  constructor() {
    this.redis = new Redis(redisConfig);
    this.results = {};
  }

  async initialize() {
    try {
      await this.redis.connect();
      console.log('✅ Redis连接成功');
      
      // 清理测试数据
      await this.redis.flushdb();
      console.log('🧹 测试数据库已清理');
    } catch (error) {
      console.error('❌ Redis连接失败:', error.message);
      process.exit(1);
    }
  }

  async cleanup() {
    try {
      await this.redis.flushdb();
      await this.redis.quit();
      console.log('🧹 清理完成');
    } catch (error) {
      console.error('清理失败:', error.message);
    }
  }

  // 基础操作性能测试
  async testBasicOperations() {
    console.log('\n🔧 开始基础操作性能测试...');
    
    const { iterations, concurrency } = testConfig.basicOps;
    const batchSize = Math.ceil(iterations / concurrency);
    
    // SET操作测试
    const setStartTime = Date.now();
    const setPromises = [];
    
    for (let i = 0; i < concurrency; i++) {
      const promise = this.batchSet(i * batchSize, batchSize);
      setPromises.push(promise);
    }
    
    await Promise.all(setPromises);
    const setDuration = Date.now() - setStartTime;
    const setOpsPerSecond = Math.round(iterations / (setDuration / 1000));
    
    console.log(`  SET操作: ${iterations}次, 耗时: ${setDuration}ms, 速率: ${setOpsPerSecond} ops/s`);
    
    // GET操作测试
    const getStartTime = Date.now();
    const getPromises = [];
    
    for (let i = 0; i < concurrency; i++) {
      const promise = this.batchGet(i * batchSize, batchSize);
      getPromises.push(promise);
    }
    
    await Promise.all(getPromises);
    const getDuration = Date.now() - getStartTime;
    const getOpsPerSecond = Math.round(iterations / (getDuration / 1000));
    
    console.log(`  GET操作: ${iterations}次, 耗时: ${getDuration}ms, 速率: ${getOpsPerSecond} ops/s`);
    
    this.results.basicOps = {
      setOpsPerSecond,
      getOpsPerSecond,
      setDuration,
      getDuration
    };
  }

  async batchSet(start, count) {
    const promises = [];
    for (let i = start; i < start + count; i++) {
      const key = `basic:${i}`;
      const value = JSON.stringify({
        id: i,
        data: `test-data-${i}`,
        timestamp: Date.now()
      });
      promises.push(this.redis.set(key, value));
    }
    await Promise.all(promises);
  }

  async batchGet(start, count) {
    const promises = [];
    for (let i = start; i < start + count; i++) {
      const key = `basic:${i}`;
      promises.push(this.redis.get(key));
    }
    await Promise.all(promises);
  }

  // 会话缓存性能测试
  async testSessionCache() {
    console.log('\n👥 开始会话缓存性能测试...');
    
    const { sessions, concurrency } = testConfig.sessionCache;
    const batchSize = Math.ceil(sessions / concurrency);
    
    const startTime = Date.now();
    const promises = [];
    
    for (let i = 0; i < concurrency; i++) {
      const promise = this.batchSessionOperations(i * batchSize, batchSize);
      promises.push(promise);
    }
    
    await Promise.all(promises);
    const duration = Date.now() - startTime;
    const opsPerSecond = Math.round((sessions * 3) / (duration / 1000)); // 每个会话3个操作
    
    console.log(`  会话操作: ${sessions}个会话, 耗时: ${duration}ms, 速率: ${opsPerSecond} ops/s`);
    
    this.results.sessionCache = {
      opsPerSecond,
      duration,
      sessions
    };
  }

  async batchSessionOperations(start, count) {
    for (let i = start; i < start + count; i++) {
      const sessionId = `session:${i}`;
      const userId = `user:${i}`;
      
      const sessionData = {
        userId,
        email: `user${i}@example.com`,
        roles: ['user'],
        permissions: ['read:profile'],
        lastAccessedAt: new Date().toISOString(),
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
      };
      
      // 设置会话
      await this.redis.setex(`session:${sessionId}`, 3600, JSON.stringify(sessionData));
      
      // 添加到用户会话列表
      await this.redis.sadd(`user:${userId}:sessions`, sessionId);
      
      // 获取会话
      await this.redis.get(`session:${sessionId}`);
    }
  }

  // 速率限制性能测试
  async testRateLimit() {
    console.log('\n⚡ 开始速率限制性能测试...');
    
    const { requests, concurrency, windowMs } = testConfig.rateLimit;
    const batchSize = Math.ceil(requests / concurrency);
    
    const startTime = Date.now();
    const promises = [];
    
    for (let i = 0; i < concurrency; i++) {
      const promise = this.batchRateLimitOperations(i, batchSize, windowMs);
      promises.push(promise);
    }
    
    const results = await Promise.all(promises);
    const duration = Date.now() - startTime;
    const opsPerSecond = Math.round(requests / (duration / 1000));
    
    const totalAllowed = results.reduce((sum, result) => sum + result.allowed, 0);
    const totalBlocked = results.reduce((sum, result) => sum + result.blocked, 0);
    
    console.log(`  速率限制检查: ${requests}次, 耗时: ${duration}ms, 速率: ${opsPerSecond} ops/s`);
    console.log(`  允许请求: ${totalAllowed}, 阻止请求: ${totalBlocked}`);
    
    this.results.rateLimit = {
      opsPerSecond,
      duration,
      totalAllowed,
      totalBlocked
    };
  }

  async batchRateLimitOperations(batchId, count, windowMs) {
    let allowed = 0;
    let blocked = 0;
    
    for (let i = 0; i < count; i++) {
      const key = `rate:test:${batchId}`;
      const window = Math.floor(Date.now() / windowMs);
      const rateLimitKey = `${key}:${window}`;
      
      const current = await this.redis.incr(rateLimitKey);
      
      if (current === 1) {
        await this.redis.expire(rateLimitKey, Math.ceil(windowMs / 1000));
      }
      
      if (current <= 100) { // 假设限制为100
        allowed++;
      } else {
        blocked++;
      }
    }
    
    return { allowed, blocked };
  }

  // 大数据性能测试
  async testLargeData() {
    console.log('\n📦 开始大数据性能测试...');
    
    const { iterations, dataSize } = testConfig.largeData;
    const largeData = 'x'.repeat(dataSize);
    
    // 大数据SET测试
    const setStartTime = Date.now();
    for (let i = 0; i < iterations; i++) {
      await this.redis.set(`large:${i}`, largeData);
    }
    const setDuration = Date.now() - setStartTime;
    const setOpsPerSecond = Math.round(iterations / (setDuration / 1000));
    
    console.log(`  大数据SET: ${iterations}次 (${dataSize}字节), 耗时: ${setDuration}ms, 速率: ${setOpsPerSecond} ops/s`);
    
    // 大数据GET测试
    const getStartTime = Date.now();
    for (let i = 0; i < iterations; i++) {
      await this.redis.get(`large:${i}`);
    }
    const getDuration = Date.now() - getStartTime;
    const getOpsPerSecond = Math.round(iterations / (getDuration / 1000));
    
    console.log(`  大数据GET: ${iterations}次 (${dataSize}字节), 耗时: ${getDuration}ms, 速率: ${getOpsPerSecond} ops/s`);
    
    this.results.largeData = {
      setOpsPerSecond,
      getOpsPerSecond,
      setDuration,
      getDuration,
      dataSize
    };
  }

  // 内存使用测试
  async testMemoryUsage() {
    console.log('\n💾 开始内存使用测试...');
    
    const info = await this.redis.info('memory');
    const memoryInfo = this.parseRedisInfo(info);
    
    console.log(`  已用内存: ${this.formatBytes(memoryInfo.used_memory)}`);
    console.log(`  内存峰值: ${this.formatBytes(memoryInfo.used_memory_peak)}`);
    console.log(`  内存碎片率: ${memoryInfo.mem_fragmentation_ratio}`);
    
    this.results.memory = memoryInfo;
  }

  parseRedisInfo(info) {
    const result = {};
    const lines = info.split('\r\n');
    
    lines.forEach(line => {
      if (line && line.includes(':')) {
        const [key, value] = line.split(':');
        result[key] = isNaN(value) ? value : Number(value);
      }
    });
    
    return result;
  }

  formatBytes(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  // 生成性能报告
  generateReport() {
    console.log('\n📊 性能测试报告');
    console.log('='.repeat(50));
    
    if (this.results.basicOps) {
      console.log('\n基础操作性能:');
      console.log(`  SET操作: ${this.results.basicOps.setOpsPerSecond} ops/s`);
      console.log(`  GET操作: ${this.results.basicOps.getOpsPerSecond} ops/s`);
    }
    
    if (this.results.sessionCache) {
      console.log('\n会话缓存性能:');
      console.log(`  会话操作: ${this.results.sessionCache.opsPerSecond} ops/s`);
    }
    
    if (this.results.rateLimit) {
      console.log('\n速率限制性能:');
      console.log(`  限制检查: ${this.results.rateLimit.opsPerSecond} ops/s`);
      console.log(`  允许率: ${((this.results.rateLimit.totalAllowed / (this.results.rateLimit.totalAllowed + this.results.rateLimit.totalBlocked)) * 100).toFixed(2)}%`);
    }
    
    if (this.results.largeData) {
      console.log('\n大数据性能:');
      console.log(`  大数据SET: ${this.results.largeData.setOpsPerSecond} ops/s`);
      console.log(`  大数据GET: ${this.results.largeData.getOpsPerSecond} ops/s`);
    }
    
    if (this.results.memory) {
      console.log('\n内存使用:');
      console.log(`  已用内存: ${this.formatBytes(this.results.memory.used_memory)}`);
      console.log(`  内存碎片率: ${this.results.memory.mem_fragmentation_ratio}`);
    }
    
    console.log('\n✅ 性能测试完成');
  }

  async runAllTests() {
    try {
      await this.initialize();
      
      await this.testBasicOperations();
      await this.testSessionCache();
      await this.testRateLimit();
      await this.testLargeData();
      await this.testMemoryUsage();
      
      this.generateReport();
      
    } catch (error) {
      console.error('❌ 性能测试失败:', error.message);
    } finally {
      await this.cleanup();
    }
  }
}

// 运行性能测试
if (require.main === module) {
  const tester = new RedisPerformanceTester();
  tester.runAllTests().catch(console.error);
}

module.exports = RedisPerformanceTester;
