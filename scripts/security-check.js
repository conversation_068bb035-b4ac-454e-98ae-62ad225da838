/**
 * 安全检查脚本
 * 验证应用的安全配置和防护措施
 */

const http = require('http');
const https = require('https');

const BASE_URL = 'http://localhost:3000';

// 安全测试用例
const securityTests = [
  {
    name: '速率限制测试 - 登录',
    test: async () => {
      console.log('  测试登录速率限制...');
      let blocked = false;
      
      // 快速发送多个登录请求
      for (let i = 0; i < 7; i++) {
        try {
          const response = await makeRequest('/api/v1/auth/login', 'POST', {
            username: '<EMAIL>',
            password: 'wrongpassword'
          });
          
          if (response.statusCode === 429) {
            blocked = true;
            console.log(`    ✅ 第${i + 1}次请求被速率限制阻止`);
            break;
          }
        } catch (error) {
          // 忽略连接错误
        }
        
        // 短暂延迟避免过快请求
        await sleep(100);
      }
      
      return blocked ? '✅ 通过' : '❌ 失败 - 速率限制未生效';
    }
  },
  
  {
    name: '安全头检查',
    test: async () => {
      console.log('  检查安全响应头...');
      const response = await makeRequest('/', 'GET');
      const headers = response.headers;
      
      const requiredHeaders = [
        'x-content-type-options',
        'x-frame-options',
        'x-xss-protection',
        'referrer-policy'
      ];
      
      const missingHeaders = requiredHeaders.filter(header => !headers[header]);
      
      if (missingHeaders.length === 0) {
        console.log('    ✅ 所有必需的安全头都存在');
        return '✅ 通过';
      } else {
        console.log(`    ❌ 缺少安全头: ${missingHeaders.join(', ')}`);
        return '❌ 失败';
      }
    }
  },
  
  {
    name: 'CORS策略检查',
    test: async () => {
      console.log('  测试CORS策略...');
      
      // 测试不允许的源
      const response = await makeRequest('/api/v1/auth/providers', 'GET', null, {
        'Origin': 'https://malicious-site.com'
      });
      
      const corsHeader = response.headers['access-control-allow-origin'];
      
      if (!corsHeader || corsHeader !== 'https://malicious-site.com') {
        console.log('    ✅ CORS正确阻止了不允许的源');
        return '✅ 通过';
      } else {
        console.log('    ❌ CORS允许了不安全的源');
        return '❌ 失败';
      }
    }
  },
  
  {
    name: 'SQL注入防护检查',
    test: async () => {
      console.log('  测试SQL注入防护...');
      
      const maliciousPayload = {
        username: "admin'; DROP TABLE users; --",
        password: "password"
      };
      
      try {
        const response = await makeRequest('/api/v1/auth/login', 'POST', maliciousPayload);
        
        // 应该返回认证失败，而不是服务器错误
        if (response.statusCode === 401 || response.statusCode === 400) {
          console.log('    ✅ SQL注入尝试被正确处理');
          return '✅ 通过';
        } else if (response.statusCode === 500) {
          console.log('    ❌ 可能存在SQL注入漏洞');
          return '❌ 失败';
        } else {
          console.log('    ⚠️  未知响应状态');
          return '⚠️ 未知';
        }
      } catch (error) {
        console.log('    ✅ 请求被正确拒绝');
        return '✅ 通过';
      }
    }
  },
  
  {
    name: 'XSS防护检查',
    test: async () => {
      console.log('  测试XSS防护...');
      
      const xssPayload = '<script>alert("XSS")</script>';
      
      try {
        const response = await makeRequest('/api/v1/auth/register', 'POST', {
          email: `test${xssPayload}@example.com`,
          password: 'password123',
          nickname: xssPayload
        });
        
        // 检查响应是否包含未转义的脚本
        if (response.data && response.data.includes('<script>')) {
          console.log('    ❌ 可能存在XSS漏洞');
          return '❌ 失败';
        } else {
          console.log('    ✅ XSS尝试被正确处理');
          return '✅ 通过';
        }
      } catch (error) {
        console.log('    ✅ 请求被正确拒绝');
        return '✅ 通过';
      }
    }
  },
  
  {
    name: '大请求体防护',
    test: async () => {
      console.log('  测试大请求体防护...');
      
      // 创建一个大的请求体 (超过10MB)
      const largeData = 'x'.repeat(11 * 1024 * 1024); // 11MB
      
      try {
        const response = await makeRequest('/api/v1/auth/register', 'POST', {
          email: '<EMAIL>',
          password: 'password123',
          largeField: largeData
        });
        
        if (response.statusCode === 413) {
          console.log('    ✅ 大请求体被正确拒绝');
          return '✅ 通过';
        } else {
          console.log('    ❌ 大请求体未被拒绝');
          return '❌ 失败';
        }
      } catch (error) {
        if (error.message.includes('ECONNRESET') || error.message.includes('413')) {
          console.log('    ✅ 大请求体被正确拒绝');
          return '✅ 通过';
        } else {
          console.log('    ❌ 未知错误:', error.message);
          return '❌ 失败';
        }
      }
    }
  }
];

// 发送HTTP请求的辅助函数
function makeRequest(path, method = 'GET', data = null, headers = {}) {
  return new Promise((resolve, reject) => {
    const postData = data ? JSON.stringify(data) : null;
    
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: path,
      method: method,
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'Security-Test-Script/1.0',
        ...headers
      }
    };
    
    if (postData) {
      options.headers['Content-Length'] = Buffer.byteLength(postData);
    }

    const req = http.request(options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          headers: res.headers,
          data: responseData
        });
      });
    });

    req.on('error', (err) => {
      reject(err);
    });

    req.setTimeout(10000, () => {
      req.destroy();
      reject(new Error('请求超时'));
    });

    if (postData) {
      req.write(postData);
    }
    
    req.end();
  });
}

// 延迟函数
function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// 主测试函数
async function runSecurityTests() {
  console.log('🔒 开始安全检查...\n');
  
  const results = [];
  
  for (const test of securityTests) {
    console.log(`🧪 ${test.name}`);
    try {
      const result = await test.test();
      results.push({ name: test.name, result });
      console.log(`   ${result}\n`);
    } catch (error) {
      const result = `❌ 测试失败: ${error.message}`;
      results.push({ name: test.name, result });
      console.log(`   ${result}\n`);
    }
  }
  
  // 统计结果
  const passed = results.filter(r => r.result.startsWith('✅')).length;
  const failed = results.filter(r => r.result.startsWith('❌')).length;
  const unknown = results.filter(r => r.result.startsWith('⚠️')).length;
  
  console.log('📊 安全检查结果:');
  console.log(`✅ 通过: ${passed}`);
  console.log(`❌ 失败: ${failed}`);
  console.log(`⚠️  未知: ${unknown}`);
  console.log(`📈 安全评分: ${((passed / results.length) * 100).toFixed(1)}%`);
  
  if (failed === 0) {
    console.log('\n🎉 所有安全检查通过！');
    process.exit(0);
  } else {
    console.log('\n⚠️  发现安全问题，请检查配置');
    process.exit(1);
  }
}

// 等待服务器启动
setTimeout(() => {
  runSecurityTests().catch(console.error);
}, 3000);
