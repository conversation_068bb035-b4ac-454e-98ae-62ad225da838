/**
 * 路由测试脚本
 * 测试前端和后端路由是否正常工作
 */

const http = require('http');

const BASE_URL = 'http://localhost:3000';

// 测试路由列表
const routes = [
  // 前端路由 (应该返回HTML)
  { path: '/', method: 'GET', expectedType: 'html', description: '根路径' },
  { path: '/login', method: 'GET', expectedType: 'html', description: '登录页面' },
  { path: '/register', method: 'GET', expectedType: 'html', description: '注册页面' },
  { path: '/dashboard', method: 'GET', expectedType: 'html', description: '仪表板页面' },
  { path: '/auth/error', method: 'GET', expectedType: 'html', description: 'OAuth错误页面' },
  { path: '/mfa', method: 'GET', expectedType: 'html', description: 'MFA页面' },
  { path: '/forgot-password', method: 'GET', expectedType: 'html', description: '忘记密码页面' },
  { path: '/demo', method: 'GET', expectedType: 'html', description: '演示页面' },
  
  // API路由 (应该返回JSON)
  { path: '/api/v1/health', method: 'GET', expectedType: 'json', description: '健康检查API' },
  { path: '/api/v1/auth/providers', method: 'GET', expectedType: 'json', description: 'OAuth提供商列表' },
  { path: '/health', method: 'GET', expectedType: 'json', description: '系统健康检查' },
  
  // 不存在的路由 (前端应该重定向，API应该返回404)
  { path: '/nonexistent', method: 'GET', expectedType: 'html', description: '不存在的前端路由' },
  { path: '/api/v1/nonexistent', method: 'GET', expectedType: 'json', expectedStatus: 404, description: '不存在的API路由' },
];

// 发送HTTP请求的辅助函数
function makeRequest(path, method = 'GET') {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: path,
      method: method,
      headers: {
        'User-Agent': 'Route-Test-Script/1.0'
      }
    };

    const req = http.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          headers: res.headers,
          data: data,
          contentType: res.headers['content-type'] || ''
        });
      });
    });

    req.on('error', (err) => {
      reject(err);
    });

    req.setTimeout(5000, () => {
      req.destroy();
      reject(new Error('请求超时'));
    });

    req.end();
  });
}

// 测试单个路由
async function testRoute(route) {
  try {
    console.log(`测试: ${route.description} (${route.path})`);
    
    const response = await makeRequest(route.path, route.method);
    const { statusCode, contentType, data } = response;
    
    // 检查状态码
    const expectedStatus = route.expectedStatus || (route.expectedType === 'html' ? 200 : 200);
    if (statusCode !== expectedStatus && statusCode !== 302) { // 302是重定向，对前端路由是正常的
      console.log(`  ❌ 状态码错误: 期望 ${expectedStatus}, 实际 ${statusCode}`);
      return false;
    }
    
    // 检查内容类型
    if (route.expectedType === 'html') {
      if (!contentType.includes('text/html') && statusCode !== 302) {
        console.log(`  ❌ 内容类型错误: 期望 HTML, 实际 ${contentType}`);
        return false;
      }
    } else if (route.expectedType === 'json') {
      if (!contentType.includes('application/json')) {
        console.log(`  ❌ 内容类型错误: 期望 JSON, 实际 ${contentType}`);
        return false;
      }
    }
    
    // 检查响应内容
    if (route.expectedType === 'html' && statusCode === 200) {
      if (!data.includes('<html') && !data.includes('<!DOCTYPE html>')) {
        console.log(`  ❌ HTML内容格式错误`);
        return false;
      }
    } else if (route.expectedType === 'json' && statusCode === 200) {
      try {
        JSON.parse(data);
      } catch (e) {
        console.log(`  ❌ JSON格式错误: ${e.message}`);
        return false;
      }
    }
    
    console.log(`  ✅ 通过 (状态码: ${statusCode}, 类型: ${contentType})`);
    return true;
    
  } catch (error) {
    console.log(`  ❌ 请求失败: ${error.message}`);
    return false;
  }
}

// 主测试函数
async function runTests() {
  console.log('🧪 开始路由测试...\n');
  
  let passed = 0;
  let failed = 0;
  
  for (const route of routes) {
    const result = await testRoute(route);
    if (result) {
      passed++;
    } else {
      failed++;
    }
    console.log(''); // 空行分隔
  }
  
  console.log('📊 测试结果:');
  console.log(`✅ 通过: ${passed}`);
  console.log(`❌ 失败: ${failed}`);
  console.log(`📈 成功率: ${((passed / (passed + failed)) * 100).toFixed(1)}%`);
  
  if (failed === 0) {
    console.log('\n🎉 所有路由测试通过！');
    process.exit(0);
  } else {
    console.log('\n⚠️  部分路由测试失败，请检查配置');
    process.exit(1);
  }
}

// 等待服务器启动
setTimeout(() => {
  runTests().catch(console.error);
}, 2000);
