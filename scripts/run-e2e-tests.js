#!/usr/bin/env node

/**
 * E2E测试运行脚本
 * 自动化运行端到端测试的脚本
 */

const { spawn, exec } = require('child_process');
const path = require('path');
const fs = require('fs');

// 配置
const config = {
  testPort: process.env.E2E_SERVER_PORT || 3001,
  testTimeout: process.env.E2E_TIMEOUT || 60000,
  headless: process.env.E2E_HEADLESS !== 'false',
  browser: process.env.E2E_BROWSER || 'chromium',
  parallel: process.env.E2E_PARALLEL === 'true',
  retries: parseInt(process.env.E2E_RETRIES || '2'),
  baseUrl: process.env.E2E_BASE_URL || `http://localhost:3001`
};

// 颜色输出
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logStep(step, message) {
  log(`[${step}] ${message}`, 'cyan');
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

// 检查依赖
async function checkDependencies() {
  logStep('DEPS', '检查依赖项...');
  
  const dependencies = ['playwright', 'jest'];
  const missing = [];
  
  for (const dep of dependencies) {
    try {
      require.resolve(dep);
    } catch (error) {
      missing.push(dep);
    }
  }
  
  if (missing.length > 0) {
    logError(`缺少依赖项: ${missing.join(', ')}`);
    logStep('INSTALL', '正在安装缺少的依赖项...');
    
    return new Promise((resolve, reject) => {
      const installCmd = `npm install ${missing.join(' ')} --save-dev`;
      exec(installCmd, (error, stdout, stderr) => {
        if (error) {
          logError(`安装依赖失败: ${error.message}`);
          reject(error);
        } else {
          logSuccess('依赖项安装完成');
          resolve();
        }
      });
    });
  } else {
    logSuccess('所有依赖项已安装');
  }
}

// 安装浏览器
async function installBrowsers() {
  logStep('BROWSER', '检查浏览器安装...');
  
  return new Promise((resolve, reject) => {
    exec('npx playwright install', (error, stdout, stderr) => {
      if (error) {
        logWarning(`浏览器安装警告: ${error.message}`);
        // 不阻止测试继续，因为浏览器可能已经安装
        resolve();
      } else {
        logSuccess('浏览器安装检查完成');
        resolve();
      }
    });
  });
}

// 准备测试环境
async function prepareTestEnvironment() {
  logStep('ENV', '准备测试环境...');
  
  // 创建测试结果目录
  const testResultsDir = path.resolve(__dirname, '../test-results');
  const dirs = [
    testResultsDir,
    path.join(testResultsDir, 'screenshots'),
    path.join(testResultsDir, 'videos'),
    path.join(testResultsDir, 'e2e')
  ];
  
  dirs.forEach(dir => {
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
      log(`创建目录: ${dir}`, 'blue');
    }
  });
  
  // 设置环境变量
  process.env.NODE_ENV = 'test';
  process.env.E2E_TEST_MODE = 'true';
  process.env.PORT = config.testPort;
  process.env.E2E_BASE_URL = config.baseUrl;
  process.env.E2E_HEADLESS = config.headless.toString();
  process.env.E2E_BROWSER = config.browser;
  
  logSuccess('测试环境准备完成');
}

// 启动测试服务器
async function startTestServer() {
  logStep('SERVER', `在端口 ${config.testPort} 启动测试服务器...`);
  
  return new Promise((resolve, reject) => {
    const serverProcess = spawn('npm', ['run', 'start:test'], {
      env: {
        ...process.env,
        PORT: config.testPort,
        NODE_ENV: 'test'
      },
      stdio: 'pipe'
    });
    
    let serverReady = false;
    const timeout = setTimeout(() => {
      if (!serverReady) {
        serverProcess.kill();
        reject(new Error('服务器启动超时'));
      }
    }, 30000);
    
    serverProcess.stdout.on('data', (data) => {
      const output = data.toString();
      if (output.includes('服务器启动成功') || output.includes(`端口 ${config.testPort}`)) {
        clearTimeout(timeout);
        serverReady = true;
        logSuccess(`测试服务器已启动 (端口 ${config.testPort})`);
        resolve(serverProcess);
      }
    });
    
    serverProcess.stderr.on('data', (data) => {
      log(`服务器错误: ${data.toString()}`, 'red');
    });
    
    serverProcess.on('error', (error) => {
      clearTimeout(timeout);
      reject(error);
    });
    
    serverProcess.on('exit', (code) => {
      clearTimeout(timeout);
      if (!serverReady) {
        reject(new Error(`服务器异常退出，退出码: ${code}`));
      }
    });
  });
}

// 等待服务器就绪
async function waitForServer() {
  logStep('HEALTH', '等待服务器就绪...');
  
  const maxAttempts = 30;
  let attempts = 0;
  
  while (attempts < maxAttempts) {
    try {
      const response = await fetch(`${config.baseUrl}/health`);
      if (response.ok) {
        logSuccess('服务器已就绪');
        return;
      }
    } catch (error) {
      // 服务器还未就绪，继续等待
    }
    
    attempts++;
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  throw new Error('服务器在预期时间内未就绪');
}

// 运行E2E测试
async function runE2ETests() {
  logStep('TEST', '运行E2E测试...');
  
  const jestArgs = [
    '--config', 'jest.e2e.config.js',
    '--runInBand', // 串行运行E2E测试
    '--forceExit',
    '--detectOpenHandles'
  ];
  
  if (config.retries > 0) {
    jestArgs.push('--testFailureExitCode', '0'); // 允许重试
  }
  
  if (process.argv.includes('--watch')) {
    jestArgs.push('--watch');
  }
  
  if (process.argv.includes('--verbose')) {
    jestArgs.push('--verbose');
  }
  
  // 添加测试文件过滤
  const testFilter = process.argv.find(arg => arg.startsWith('--testNamePattern='));
  if (testFilter) {
    jestArgs.push(testFilter);
  }
  
  return new Promise((resolve, reject) => {
    const testProcess = spawn('npx', ['jest', ...jestArgs], {
      stdio: 'inherit',
      env: {
        ...process.env,
        E2E_BASE_URL: config.baseUrl,
        E2E_HEADLESS: config.headless.toString(),
        E2E_BROWSER: config.browser
      }
    });
    
    testProcess.on('close', (code) => {
      if (code === 0) {
        logSuccess('E2E测试完成');
        resolve();
      } else {
        logError(`E2E测试失败，退出码: ${code}`);
        reject(new Error(`测试失败，退出码: ${code}`));
      }
    });
    
    testProcess.on('error', (error) => {
      logError(`测试进程错误: ${error.message}`);
      reject(error);
    });
  });
}

// 生成测试报告
async function generateReport() {
  logStep('REPORT', '生成测试报告...');
  
  const reportPath = path.resolve(__dirname, '../test-results/e2e/e2e-test-report.html');
  
  if (fs.existsSync(reportPath)) {
    logSuccess(`测试报告已生成: ${reportPath}`);
    log(`在浏览器中打开: file://${reportPath}`, 'blue');
  } else {
    logWarning('未找到测试报告文件');
  }
}

// 清理资源
async function cleanup(serverProcess) {
  logStep('CLEANUP', '清理资源...');
  
  if (serverProcess && !serverProcess.killed) {
    serverProcess.kill('SIGTERM');
    
    // 等待进程退出
    await new Promise((resolve) => {
      serverProcess.on('exit', resolve);
      setTimeout(() => {
        if (!serverProcess.killed) {
          serverProcess.kill('SIGKILL');
        }
        resolve();
      }, 5000);
    });
  }
  
  logSuccess('资源清理完成');
}

// 主函数
async function main() {
  log('🚀 开始E2E测试流程', 'bright');
  log(`配置: ${JSON.stringify(config, null, 2)}`, 'blue');
  
  let serverProcess = null;
  
  try {
    // 1. 检查依赖
    await checkDependencies();
    
    // 2. 安装浏览器
    await installBrowsers();
    
    // 3. 准备测试环境
    await prepareTestEnvironment();
    
    // 4. 启动测试服务器
    serverProcess = await startTestServer();
    
    // 5. 等待服务器就绪
    await waitForServer();
    
    // 6. 运行E2E测试
    await runE2ETests();
    
    // 7. 生成测试报告
    await generateReport();
    
    logSuccess('🎉 E2E测试流程完成');
    process.exit(0);
    
  } catch (error) {
    logError(`E2E测试流程失败: ${error.message}`);
    console.error(error);
    process.exit(1);
    
  } finally {
    // 8. 清理资源
    await cleanup(serverProcess);
  }
}

// 处理进程信号
process.on('SIGINT', async () => {
  log('\n收到中断信号，正在清理...', 'yellow');
  process.exit(1);
});

process.on('SIGTERM', async () => {
  log('\n收到终止信号，正在清理...', 'yellow');
  process.exit(1);
});

// 运行主函数
if (require.main === module) {
  main().catch((error) => {
    logError(`未捕获的错误: ${error.message}`);
    console.error(error);
    process.exit(1);
  });
}

module.exports = {
  main,
  config
};
