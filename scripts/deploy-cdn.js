#!/usr/bin/env node

/**
 * CDN部署脚本
 * 自动化静态资源上传到CDN的脚本
 */

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');
const { execSync } = require('child_process');

// 配置
const config = {
  distPath: path.resolve(__dirname, '../dist/frontend'),
  manifestPath: path.resolve(__dirname, '../dist/frontend/manifest.json'),
  cdnProvider: process.env.CDN_PROVIDER || 'custom',
  cdnBaseUrl: process.env.CDN_BASE_URL || '',
  apiKey: process.env.CDN_API_KEY || '',
  secretKey: process.env.CDN_SECRET_KEY || '',
  bucketName: process.env.CDN_BUCKET_NAME || '',
  region: process.env.CDN_REGION || 'us-east-1',
  dryRun: process.argv.includes('--dry-run'),
  verbose: process.argv.includes('--verbose') || process.argv.includes('-v')
};

// 颜色输出
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logStep(step, message) {
  log(`[${step}] ${message}`, 'cyan');
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

/**
 * 检查前置条件
 */
function checkPrerequisites() {
  logStep('CHECK', '检查前置条件...');

  // 检查构建目录
  if (!fs.existsSync(config.distPath)) {
    logError('前端构建目录不存在，请先运行: npm run build:frontend');
    process.exit(1);
  }

  // 检查CDN配置
  if (!config.cdnBaseUrl) {
    logError('CDN_BASE_URL 环境变量未设置');
    process.exit(1);
  }

  // 检查提供商特定配置
  switch (config.cdnProvider) {
    case 'aws':
      if (!config.apiKey || !config.secretKey || !config.bucketName) {
        logError('AWS配置不完整，需要设置 CDN_API_KEY, CDN_SECRET_KEY, CDN_BUCKET_NAME');
        process.exit(1);
      }
      break;
    case 'cloudflare':
      if (!config.apiKey) {
        logError('Cloudflare配置不完整，需要设置 CDN_API_KEY');
        process.exit(1);
      }
      break;
    case 'aliyun':
      if (!config.apiKey || !config.secretKey) {
        logError('阿里云配置不完整，需要设置 CDN_API_KEY, CDN_SECRET_KEY');
        process.exit(1);
      }
      break;
  }

  logSuccess('前置条件检查通过');
}

/**
 * 扫描静态资源文件
 */
function scanAssets() {
  logStep('SCAN', '扫描静态资源文件...');

  const assets = [];
  
  function scanDirectory(dir, basePath = '') {
    const items = fs.readdirSync(dir);
    
    for (const item of items) {
      const fullPath = path.join(dir, item);
      const relativePath = path.join(basePath, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        scanDirectory(fullPath, relativePath);
      } else {
        // 跳过某些文件
        if (item === 'manifest.json' || item.startsWith('.')) {
          continue;
        }
        
        const ext = path.extname(item).toLowerCase();
        const supportedExts = ['.js', '.css', '.png', '.jpg', '.jpeg', '.gif', '.svg', '.webp', '.ico', '.woff', '.woff2', '.ttf', '.eot', '.json', '.xml', '.txt'];
        
        if (supportedExts.includes(ext)) {
          const content = fs.readFileSync(fullPath);
          const hash = crypto.createHash('md5').update(content).digest('hex').substring(0, 8);
          
          assets.push({
            localPath: fullPath,
            relativePath: relativePath.replace(/\\/g, '/'), // 统一使用正斜杠
            size: stat.size,
            hash,
            mimeType: getMimeType(ext),
            lastModified: stat.mtime
          });
        }
      }
    }
  }
  
  scanDirectory(config.distPath);
  
  logSuccess(`发现 ${assets.length} 个静态资源文件`);
  
  if (config.verbose) {
    assets.forEach(asset => {
      log(`  ${asset.relativePath} (${formatBytes(asset.size)})`, 'blue');
    });
  }
  
  return assets;
}

/**
 * 获取MIME类型
 */
function getMimeType(ext) {
  const mimeTypes = {
    '.js': 'application/javascript',
    '.css': 'text/css',
    '.png': 'image/png',
    '.jpg': 'image/jpeg',
    '.jpeg': 'image/jpeg',
    '.gif': 'image/gif',
    '.svg': 'image/svg+xml',
    '.webp': 'image/webp',
    '.ico': 'image/x-icon',
    '.woff': 'font/woff',
    '.woff2': 'font/woff2',
    '.ttf': 'font/ttf',
    '.eot': 'application/vnd.ms-fontobject',
    '.json': 'application/json',
    '.xml': 'application/xml',
    '.txt': 'text/plain'
  };
  
  return mimeTypes[ext] || 'application/octet-stream';
}

/**
 * 格式化字节数
 */
function formatBytes(bytes) {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * 上传资源到CDN
 */
async function uploadAssets(assets) {
  logStep('UPLOAD', `开始上传 ${assets.length} 个文件到CDN...`);
  
  if (config.dryRun) {
    logWarning('干运行模式，不会实际上传文件');
    assets.forEach(asset => {
      log(`  [DRY-RUN] ${asset.relativePath}`, 'yellow');
    });
    return;
  }
  
  let uploadedCount = 0;
  let skippedCount = 0;
  let errorCount = 0;
  
  for (const asset of assets) {
    try {
      const uploaded = await uploadSingleAsset(asset);
      if (uploaded) {
        uploadedCount++;
        if (config.verbose) {
          logSuccess(`上传成功: ${asset.relativePath}`);
        }
      } else {
        skippedCount++;
        if (config.verbose) {
          log(`跳过: ${asset.relativePath} (已存在)`, 'yellow');
        }
      }
    } catch (error) {
      errorCount++;
      logError(`上传失败: ${asset.relativePath} - ${error.message}`);
    }
  }
  
  logSuccess(`上传完成: ${uploadedCount} 个文件上传，${skippedCount} 个跳过，${errorCount} 个失败`);
}

/**
 * 上传单个资源文件
 */
async function uploadSingleAsset(asset) {
  switch (config.cdnProvider) {
    case 'aws':
      return await uploadToAWS(asset);
    case 'cloudflare':
      return await uploadToCloudflare(asset);
    case 'aliyun':
      return await uploadToAliyun(asset);
    default:
      logWarning(`不支持的CDN提供商: ${config.cdnProvider}`);
      return false;
  }
}

/**
 * 上传到AWS S3/CloudFront
 */
async function uploadToAWS(asset) {
  // 这里需要AWS SDK，简化实现
  logWarning('AWS上传功能需要实现');
  return false;
}

/**
 * 上传到Cloudflare
 */
async function uploadToCloudflare(asset) {
  // 这里需要Cloudflare API，简化实现
  logWarning('Cloudflare上传功能需要实现');
  return false;
}

/**
 * 上传到阿里云CDN
 */
async function uploadToAliyun(asset) {
  // 这里需要阿里云SDK，简化实现
  logWarning('阿里云上传功能需要实现');
  return false;
}

/**
 * 生成资源清单
 */
function generateManifest(assets) {
  logStep('MANIFEST', '生成资源清单...');
  
  const manifest = {
    version: '1.0.0',
    timestamp: new Date().toISOString(),
    cdnBaseUrl: config.cdnBaseUrl,
    assets: {}
  };
  
  assets.forEach(asset => {
    manifest.assets[asset.relativePath] = {
      hash: asset.hash,
      size: asset.size,
      mimeType: asset.mimeType,
      cdnUrl: `${config.cdnBaseUrl}/${asset.relativePath}`,
      lastModified: asset.lastModified.toISOString()
    };
  });
  
  const manifestPath = path.join(config.distPath, 'cdn-manifest.json');
  fs.writeFileSync(manifestPath, JSON.stringify(manifest, null, 2));
  
  logSuccess(`资源清单已生成: ${manifestPath}`);
  return manifest;
}

/**
 * 清除CDN缓存
 */
async function purgeCDNCache(assets) {
  logStep('PURGE', '清除CDN缓存...');
  
  if (config.dryRun) {
    logWarning('干运行模式，不会实际清除缓存');
    return;
  }
  
  const urls = assets.map(asset => `${config.cdnBaseUrl}/${asset.relativePath}`);
  
  try {
    switch (config.cdnProvider) {
      case 'cloudflare':
        await purgeCloudflareCache(urls);
        break;
      case 'aws':
        await purgeAWSCache(urls);
        break;
      case 'aliyun':
        await purgeAliyunCache(urls);
        break;
      default:
        logWarning(`${config.cdnProvider} 不支持自动缓存清除`);
        return;
    }
    
    logSuccess('CDN缓存清除完成');
  } catch (error) {
    logError(`CDN缓存清除失败: ${error.message}`);
  }
}

/**
 * 清除Cloudflare缓存
 */
async function purgeCloudflareCache(urls) {
  logWarning('Cloudflare缓存清除功能需要实现');
}

/**
 * 清除AWS缓存
 */
async function purgeAWSCache(urls) {
  logWarning('AWS缓存清除功能需要实现');
}

/**
 * 清除阿里云缓存
 */
async function purgeAliyunCache(urls) {
  logWarning('阿里云缓存清除功能需要实现');
}

/**
 * 主函数
 */
async function main() {
  log('🚀 开始CDN部署流程', 'cyan');
  log(`配置: ${JSON.stringify({
    provider: config.cdnProvider,
    baseUrl: config.cdnBaseUrl,
    dryRun: config.dryRun
  }, null, 2)}`, 'blue');
  
  try {
    // 1. 检查前置条件
    checkPrerequisites();
    
    // 2. 扫描静态资源
    const assets = scanAssets();
    
    // 3. 上传资源到CDN
    await uploadAssets(assets);
    
    // 4. 生成资源清单
    const manifest = generateManifest(assets);
    
    // 5. 清除CDN缓存
    await purgeCDNCache(assets);
    
    logSuccess('🎉 CDN部署完成');
    
    // 输出统计信息
    const totalSize = assets.reduce((sum, asset) => sum + asset.size, 0);
    log(`\n📊 部署统计:`, 'cyan');
    log(`  文件数量: ${assets.length}`, 'blue');
    log(`  总大小: ${formatBytes(totalSize)}`, 'blue');
    log(`  CDN地址: ${config.cdnBaseUrl}`, 'blue');
    
  } catch (error) {
    logError(`CDN部署失败: ${error.message}`);
    console.error(error);
    process.exit(1);
  }
}

// 处理进程信号
process.on('SIGINT', () => {
  log('\n收到中断信号，正在清理...', 'yellow');
  process.exit(1);
});

process.on('SIGTERM', () => {
  log('\n收到终止信号，正在清理...', 'yellow');
  process.exit(1);
});

// 运行主函数
if (require.main === module) {
  main().catch((error) => {
    logError(`未捕获的错误: ${error.message}`);
    console.error(error);
    process.exit(1);
  });
}

module.exports = {
  main,
  config
};
