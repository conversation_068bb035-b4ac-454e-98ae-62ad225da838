#!/usr/bin/env ts-node

/**
 * 性能测试CLI工具
 * 提供命令行方式运行性能测试和基准测试
 */

import { Command } from 'commander';
import { performanceTestService, TestType, TestConfig } from '../src/services/performance-test.service';
import { benchmarkService } from '../src/services/benchmark.service';
import { loadTestScenariosService } from '../src/services/load-test-scenarios.service';
import { logger } from '../src/config/logger';
import chalk from 'chalk';
import Table from 'cli-table3';

const program = new Command();

/**
 * 格式化持续时间
 */
function formatDuration(ms: number): string {
  const seconds = Math.floor(ms / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  
  if (hours > 0) {
    return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
  } else if (minutes > 0) {
    return `${minutes}m ${seconds % 60}s`;
  } else {
    return `${seconds}s`;
  }
}

/**
 * 格式化数字
 */
function formatNumber(num: number, decimals = 2): string {
  return num.toFixed(decimals);
}

/**
 * 打印测试结果表格
 */
function printTestResults(result: any): void {
  console.log(chalk.blue.bold('\n📊 测试结果摘要'));
  console.log(chalk.gray('─'.repeat(60)));

  const summaryTable = new Table({
    head: [chalk.cyan('指标'), chalk.cyan('值')],
    colWidths: [25, 35]
  });

  summaryTable.push(
    ['测试ID', result.testId],
    ['测试名称', result.config.name],
    ['测试类型', result.config.type],
    ['持续时间', formatDuration(result.duration)],
    ['总请求数', result.totalRequests.toLocaleString()],
    ['成功请求', result.successfulRequests.toLocaleString()],
    ['失败请求', result.failedRequests.toLocaleString()],
    ['错误率', `${formatNumber(result.errorRate)}%`],
    ['平均响应时间', `${formatNumber(result.responseTime.avg)}ms`],
    ['P95响应时间', `${formatNumber(result.responseTime.p95)}ms`],
    ['P99响应时间', `${formatNumber(result.responseTime.p99)}ms`],
    ['吞吐量', `${formatNumber(result.throughput.rps)} RPS`]
  );

  console.log(summaryTable.toString());

  // 阈值检查结果
  if (result.thresholdResults) {
    console.log(chalk.blue.bold('\n🎯 阈值检查结果'));
    console.log(chalk.gray('─'.repeat(60)));
    
    if (result.thresholdResults.passed) {
      console.log(chalk.green('✅ 所有阈值检查通过'));
    } else {
      console.log(chalk.red('❌ 阈值检查失败'));
      result.thresholdResults.failures.forEach((failure: string) => {
        console.log(chalk.red(`  • ${failure}`));
      });
    }
  }

  // 错误统计
  if (result.errors && result.errors.length > 0) {
    console.log(chalk.blue.bold('\n🚨 错误统计'));
    console.log(chalk.gray('─'.repeat(60)));

    const errorTable = new Table({
      head: [chalk.cyan('错误类型'), chalk.cyan('数量'), chalk.cyan('百分比')],
      colWidths: [30, 10, 15]
    });

    result.errors.forEach((error: any) => {
      errorTable.push([
        error.type,
        error.count.toString(),
        `${formatNumber(error.percentage)}%`
      ]);
    });

    console.log(errorTable.toString());
  }

  // 资源使用情况
  console.log(chalk.blue.bold('\n💻 资源使用情况'));
  console.log(chalk.gray('─'.repeat(60)));

  const resourceTable = new Table({
    head: [chalk.cyan('资源'), chalk.cyan('平均值'), chalk.cyan('峰值')],
    colWidths: [15, 20, 20]
  });

  resourceTable.push(
    ['CPU', `${formatNumber(result.resourceUsage.cpu.avg)}%`, `${formatNumber(result.resourceUsage.cpu.max)}%`],
    ['内存', `${Math.round(result.resourceUsage.memory.avg / 1024 / 1024)}MB`, `${Math.round(result.resourceUsage.memory.max / 1024 / 1024)}MB`],
    ['连接数', result.resourceUsage.connections.avg.toString(), result.resourceUsage.connections.max.toString()]
  );

  console.log(resourceTable.toString());
}

/**
 * 打印基准测试结果
 */
function printBenchmarkResults(results: any[]): void {
  console.log(chalk.blue.bold('\n📈 基准测试结果'));
  console.log(chalk.gray('─'.repeat(80)));

  results.forEach(result => {
    console.log(chalk.yellow.bold(`\n${result.name} (${result.category})`));
    console.log(chalk.gray('─'.repeat(40)));

    const benchmarkTable = new Table({
      head: [chalk.cyan('测试'), chalk.cyan('OPS/秒'), chalk.cyan('平均延迟'), chalk.cyan('P95延迟'), chalk.cyan('错误率')],
      colWidths: [20, 12, 12, 12, 10]
    });

    Object.entries(result.results).forEach(([testName, testResult]: [string, any]) => {
      benchmarkTable.push([
        testName,
        formatNumber(testResult.opsPerSecond),
        `${formatNumber(testResult.avgLatency)}ms`,
        `${formatNumber(testResult.p95Latency)}ms`,
        `${formatNumber(testResult.errorRate)}%`
      ]);
    });

    console.log(benchmarkTable.toString());

    // 显示比较结果
    if (result.comparison) {
      console.log(chalk.green.bold(`\n📊 与基准 "${result.comparison.baseline}" 的比较:`));
      Object.entries(result.comparison.improvements).forEach(([testName, improvement]: [string, any]) => {
        const opsChange = improvement.opsPerSecondChange;
        const latencyChange = improvement.latencyChange;
        
        const opsIcon = opsChange > 0 ? '📈' : opsChange < 0 ? '📉' : '➡️';
        const latencyIcon = latencyChange < 0 ? '📈' : latencyChange > 0 ? '📉' : '➡️';
        
        console.log(`  ${testName}:`);
        console.log(`    ${opsIcon} 吞吐量: ${opsChange > 0 ? '+' : ''}${formatNumber(opsChange)}%`);
        console.log(`    ${latencyIcon} 延迟: ${latencyChange > 0 ? '+' : ''}${formatNumber(latencyChange)}%`);
      });
    }
  });

  // 环境信息
  if (results.length > 0) {
    const env = results[0].environment;
    console.log(chalk.blue.bold('\n🖥️  测试环境'));
    console.log(chalk.gray('─'.repeat(40)));
    console.log(`Node.js: ${env.nodeVersion}`);
    console.log(`平台: ${env.platform} ${env.arch}`);
    console.log(`CPU核心: ${env.cpus}`);
    console.log(`内存: ${env.memory}MB`);
  }
}

/**
 * 运行性能测试命令
 */
program
  .command('test')
  .description('运行性能测试')
  .option('-t, --type <type>', '测试类型 (load|stress|spike|volume|endurance|baseline)', 'load')
  .option('-n, --name <name>', '测试名称', 'CLI Performance Test')
  .option('-d, --duration <duration>', '测试持续时间（秒）', '60')
  .option('-c, --concurrency <concurrency>', '并发数', '10')
  .option('-s, --scenario <scenario>', '测试场景类型', 'mixed')
  .option('--target-rps <rps>', '目标RPS')
  .option('--max-errors <errors>', '最大错误数')
  .option('--response-time-threshold <ms>', '响应时间阈值（毫秒）')
  .option('--error-rate-threshold <rate>', '错误率阈值（百分比）')
  .action(async (options) => {
    try {
      console.log(chalk.blue.bold('🚀 开始性能测试'));
      console.log(chalk.gray('─'.repeat(60)));

      const config: TestConfig = {
        type: options.type as TestType,
        name: options.name,
        description: `CLI性能测试 - ${options.type}`,
        duration: parseInt(options.duration) * 1000,
        concurrency: parseInt(options.concurrency),
        targetRPS: options.targetRps ? parseInt(options.targetRps) : undefined,
        maxErrors: options.maxErrors ? parseInt(options.maxErrors) : undefined,
        thresholds: {
          responseTime: options.responseTimeThreshold ? {
            avg: parseInt(options.responseTimeThreshold)
          } : undefined,
          errorRate: options.errorRateThreshold ? parseFloat(options.errorRateThreshold) : undefined
        }
      };

      console.log(`测试名称: ${config.name}`);
      console.log(`测试类型: ${config.type}`);
      console.log(`持续时间: ${formatDuration(config.duration)}`);
      console.log(`并发数: ${config.concurrency}`);
      console.log(`场景类型: ${options.scenario}`);

      const scenarios = loadTestScenariosService.getScenariosByType(options.scenario);
      console.log(`场景数量: ${scenarios.length}`);

      console.log(chalk.yellow('\n⏳ 测试进行中...'));

      const result = await performanceTestService.runTest(config, scenarios);
      printTestResults(result);

    } catch (error) {
      console.error(chalk.red('❌ 测试失败:'), error.message);
      process.exit(1);
    }
  });

/**
 * 运行基准测试命令
 */
program
  .command('benchmark')
  .description('运行基准测试')
  .option('-c, --categories <categories>', '测试类别，逗号分隔 (database,cache,api,auth)', '')
  .option('-s, --save-baseline', '保存为基准')
  .option('-b, --compare-baseline <name>', '与指定基准比较')
  .action(async (options) => {
    try {
      console.log(chalk.blue.bold('📊 开始基准测试'));
      console.log(chalk.gray('─'.repeat(60)));

      let results;
      const categories = options.categories ? options.categories.split(',') : [];

      if (categories.length > 0) {
        console.log(`测试类别: ${categories.join(', ')}`);
        results = [];
        
        for (const category of categories) {
          console.log(chalk.yellow(`\n⏳ 运行 ${category} 基准测试...`));
          
          let result;
          switch (category.trim()) {
            case 'database':
              result = await benchmarkService.runDatabaseBenchmark();
              break;
            case 'cache':
              result = await benchmarkService.runRedisBenchmark();
              break;
            case 'api':
              result = await benchmarkService.runAPIBenchmark();
              break;
            case 'auth':
              result = await benchmarkService.runAuthBenchmark();
              break;
            default:
              console.log(chalk.red(`❌ 未知的测试类别: ${category}`));
              continue;
          }
          
          if (result) {
            results.push(result);
          }
        }
      } else {
        console.log('运行完整基准测试套件');
        console.log(chalk.yellow('\n⏳ 测试进行中...'));
        results = await benchmarkService.runFullBenchmarkSuite();
      }

      // 处理基准保存和比较
      if (options.saveBaseline) {
        results.forEach((result: any) => {
          benchmarkService.saveBaseline(result.name, result);
        });
        console.log(chalk.green('\n✅ 基准已保存'));
      }

      if (options.compareBaseline) {
        results = results.map((result: any) => 
          benchmarkService.compareWithBaseline(options.compareBaseline, result)
        );
      }

      printBenchmarkResults(results);

    } catch (error) {
      console.error(chalk.red('❌ 基准测试失败:'), error.message);
      process.exit(1);
    }
  });

/**
 * 列出可用场景命令
 */
program
  .command('scenarios')
  .description('列出可用的测试场景')
  .action(() => {
    console.log(chalk.blue.bold('📋 可用测试场景'));
    console.log(chalk.gray('─'.repeat(60)));

    const scenarioTypes = loadTestScenariosService.getAllScenarioTypes();
    
    Object.entries(scenarioTypes).forEach(([type, getScenarios]) => {
      console.log(chalk.yellow.bold(`\n${type}:`));
      const scenarios = getScenarios();
      
      scenarios.forEach(scenario => {
        console.log(`  • ${scenario.name} (权重: ${scenario.weight})`);
      });
    });
  });

/**
 * 显示测试历史命令
 */
program
  .command('history')
  .description('显示测试历史')
  .option('-l, --limit <limit>', '显示数量限制', '10')
  .action((options) => {
    console.log(chalk.blue.bold('📚 测试历史'));
    console.log(chalk.gray('─'.repeat(60)));

    const testResults = performanceTestService.getAllTestResults();
    const benchmarkResults = benchmarkService.getAllResults();
    const limit = parseInt(options.limit);

    if (testResults.length > 0) {
      console.log(chalk.yellow.bold('\n性能测试:'));
      const recentTests = testResults
        .sort((a, b) => b.startTime.getTime() - a.startTime.getTime())
        .slice(0, limit);

      const testTable = new Table({
        head: [chalk.cyan('测试ID'), chalk.cyan('名称'), chalk.cyan('类型'), chalk.cyan('时间'), chalk.cyan('RPS'), chalk.cyan('错误率')],
        colWidths: [20, 25, 10, 20, 10, 10]
      });

      recentTests.forEach(result => {
        testTable.push([
          result.testId.substring(0, 18) + '...',
          result.config.name,
          result.config.type,
          result.startTime.toLocaleString(),
          formatNumber(result.throughput.rps),
          `${formatNumber(result.errorRate)}%`
        ]);
      });

      console.log(testTable.toString());
    }

    if (benchmarkResults.length > 0) {
      console.log(chalk.yellow.bold('\n基准测试:'));
      const recentBenchmarks = benchmarkResults
        .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
        .slice(0, limit);

      const benchmarkTable = new Table({
        head: [chalk.cyan('名称'), chalk.cyan('类别'), chalk.cyan('时间'), chalk.cyan('测试数')],
        colWidths: [30, 15, 20, 10]
      });

      recentBenchmarks.forEach(result => {
        benchmarkTable.push([
          result.name,
          result.category,
          result.timestamp.toLocaleString(),
          Object.keys(result.results).length.toString()
        ]);
      });

      console.log(benchmarkTable.toString());
    }

    if (testResults.length === 0 && benchmarkResults.length === 0) {
      console.log(chalk.gray('暂无测试历史'));
    }
  });

// 设置程序信息
program
  .name('performance-test')
  .description('身份提供商系统性能测试CLI工具')
  .version('1.0.0');

// 解析命令行参数
program.parse();

// 如果没有提供命令，显示帮助
if (!process.argv.slice(2).length) {
  program.outputHelp();
}
