#!/usr/bin/env ts-node

/**
 * 测试覆盖率分析和报告生成脚本
 * 提供详细的测试覆盖率分析和改进建议
 */

import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';
import chalk from 'chalk';
import Table from 'cli-table3';

interface CoverageData {
  total: {
    lines: { total: number; covered: number; skipped: number; pct: number };
    functions: { total: number; covered: number; skipped: number; pct: number };
    statements: { total: number; covered: number; skipped: number; pct: number };
    branches: { total: number; covered: number; skipped: number; pct: number };
  };
  [key: string]: any;
}

interface FileStats {
  file: string;
  lines: number;
  functions: number;
  statements: number;
  branches: number;
}

/**
 * 运行测试覆盖率分析
 */
async function runCoverageAnalysis(): Promise<void> {
  console.log(chalk.blue.bold('🧪 开始测试覆盖率分析'));
  console.log(chalk.gray('─'.repeat(60)));

  try {
    // 运行测试并生成覆盖率报告
    console.log(chalk.yellow('⏳ 运行测试套件...'));
    execSync('npm run test:coverage', { stdio: 'inherit' });

    // 读取覆盖率数据
    const coverageData = await loadCoverageData();
    
    if (coverageData) {
      // 显示总体覆盖率
      displayOverallCoverage(coverageData);
      
      // 显示文件级别覆盖率
      displayFileCoverage(coverageData);
      
      // 显示低覆盖率文件
      displayLowCoverageFiles(coverageData);
      
      // 生成改进建议
      generateImprovementSuggestions(coverageData);
      
      // 检查覆盖率阈值
      checkCoverageThresholds(coverageData);
    }

  } catch (error) {
    console.error(chalk.red('❌ 测试覆盖率分析失败:'), error.message);
    process.exit(1);
  }
}

/**
 * 加载覆盖率数据
 */
async function loadCoverageData(): Promise<CoverageData | null> {
  const coverageFile = path.join(process.cwd(), 'coverage', 'coverage-summary.json');
  
  if (!fs.existsSync(coverageFile)) {
    console.warn(chalk.yellow('⚠️  覆盖率数据文件不存在'));
    return null;
  }

  try {
    const data = fs.readFileSync(coverageFile, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    console.error(chalk.red('❌ 读取覆盖率数据失败:'), error.message);
    return null;
  }
}

/**
 * 显示总体覆盖率
 */
function displayOverallCoverage(data: CoverageData): void {
  console.log(chalk.blue.bold('\n📊 总体测试覆盖率'));
  console.log(chalk.gray('─'.repeat(60)));

  const table = new Table({
    head: [chalk.cyan('指标'), chalk.cyan('覆盖数'), chalk.cyan('总数'), chalk.cyan('覆盖率'), chalk.cyan('状态')],
    colWidths: [15, 12, 12, 12, 15]
  });

  const metrics = [
    { name: '语句', key: 'statements' },
    { name: '分支', key: 'branches' },
    { name: '函数', key: 'functions' },
    { name: '行数', key: 'lines' }
  ];

  metrics.forEach(metric => {
    const stats = data.total[metric.key];
    const status = getStatusIcon(stats.pct);
    const pctColor = getCoverageColor(stats.pct);
    
    table.push([
      metric.name,
      stats.covered.toString(),
      stats.total.toString(),
      pctColor(`${stats.pct.toFixed(1)}%`),
      status
    ]);
  });

  console.log(table.toString());
}

/**
 * 显示文件级别覆盖率
 */
function displayFileCoverage(data: CoverageData): void {
  console.log(chalk.blue.bold('\n📁 文件覆盖率详情'));
  console.log(chalk.gray('─'.repeat(80)));

  const files: FileStats[] = [];
  
  Object.keys(data).forEach(key => {
    if (key !== 'total' && data[key].lines) {
      files.push({
        file: key.replace(process.cwd(), '').replace(/^\//, ''),
        lines: data[key].lines.pct,
        functions: data[key].functions.pct,
        statements: data[key].statements.pct,
        branches: data[key].branches.pct
      });
    }
  });

  // 按行覆盖率排序
  files.sort((a, b) => a.lines - b.lines);

  const table = new Table({
    head: [
      chalk.cyan('文件'),
      chalk.cyan('行数'),
      chalk.cyan('函数'),
      chalk.cyan('语句'),
      chalk.cyan('分支')
    ],
    colWidths: [40, 10, 10, 10, 10]
  });

  files.slice(0, 20).forEach(file => {
    table.push([
      file.file.length > 35 ? '...' + file.file.slice(-32) : file.file,
      getCoverageColor(file.lines)(`${file.lines.toFixed(1)}%`),
      getCoverageColor(file.functions)(`${file.functions.toFixed(1)}%`),
      getCoverageColor(file.statements)(`${file.statements.toFixed(1)}%`),
      getCoverageColor(file.branches)(`${file.branches.toFixed(1)}%`)
    ]);
  });

  console.log(table.toString());
  
  if (files.length > 20) {
    console.log(chalk.gray(`... 还有 ${files.length - 20} 个文件`));
  }
}

/**
 * 显示低覆盖率文件
 */
function displayLowCoverageFiles(data: CoverageData): void {
  console.log(chalk.blue.bold('\n⚠️  需要关注的低覆盖率文件'));
  console.log(chalk.gray('─'.repeat(60)));

  const lowCoverageFiles: FileStats[] = [];
  
  Object.keys(data).forEach(key => {
    if (key !== 'total' && data[key].lines) {
      const file = {
        file: key.replace(process.cwd(), '').replace(/^\//, ''),
        lines: data[key].lines.pct,
        functions: data[key].functions.pct,
        statements: data[key].statements.pct,
        branches: data[key].branches.pct
      };
      
      // 筛选低覆盖率文件（任一指标低于70%）
      if (file.lines < 70 || file.functions < 70 || file.statements < 70 || file.branches < 70) {
        lowCoverageFiles.push(file);
      }
    }
  });

  if (lowCoverageFiles.length === 0) {
    console.log(chalk.green('✅ 所有文件的覆盖率都达到了良好水平！'));
    return;
  }

  // 按最低覆盖率排序
  lowCoverageFiles.sort((a, b) => {
    const minA = Math.min(a.lines, a.functions, a.statements, a.branches);
    const minB = Math.min(b.lines, b.functions, b.statements, b.branches);
    return minA - minB;
  });

  const table = new Table({
    head: [
      chalk.cyan('文件'),
      chalk.cyan('最低覆盖率'),
      chalk.cyan('建议优先级')
    ],
    colWidths: [50, 15, 15]
  });

  lowCoverageFiles.forEach(file => {
    const minCoverage = Math.min(file.lines, file.functions, file.statements, file.branches);
    const priority = minCoverage < 50 ? chalk.red('高') : minCoverage < 70 ? chalk.yellow('中') : chalk.blue('低');
    
    table.push([
      file.file.length > 45 ? '...' + file.file.slice(-42) : file.file,
      getCoverageColor(minCoverage)(`${minCoverage.toFixed(1)}%`),
      priority
    ]);
  });

  console.log(table.toString());
}

/**
 * 生成改进建议
 */
function generateImprovementSuggestions(data: CoverageData): void {
  console.log(chalk.blue.bold('\n💡 测试覆盖率改进建议'));
  console.log(chalk.gray('─'.repeat(60)));

  const suggestions: string[] = [];
  const total = data.total;

  // 基于覆盖率数据生成建议
  if (total.lines.pct < 80) {
    suggestions.push('📝 增加更多的单元测试来提高行覆盖率');
  }

  if (total.functions.pct < 80) {
    suggestions.push('🔧 为未测试的函数添加测试用例');
  }

  if (total.branches.pct < 75) {
    suggestions.push('🌿 添加更多的条件分支测试，包括边界情况');
  }

  if (total.statements.pct < 80) {
    suggestions.push('📋 确保所有代码语句都被测试执行');
  }

  // 通用建议
  suggestions.push('🧪 考虑添加集成测试来提高整体覆盖率');
  suggestions.push('🎯 重点关注业务逻辑复杂的模块');
  suggestions.push('🔍 使用测试驱动开发(TDD)方法');
  suggestions.push('📊 定期审查和更新测试用例');

  suggestions.forEach((suggestion, index) => {
    console.log(`${index + 1}. ${suggestion}`);
  });
}

/**
 * 检查覆盖率阈值
 */
function checkCoverageThresholds(data: CoverageData): void {
  console.log(chalk.blue.bold('\n🎯 覆盖率阈值检查'));
  console.log(chalk.gray('─'.repeat(60)));

  const thresholds = {
    lines: 80,
    functions: 80,
    statements: 80,
    branches: 75
  };

  const results: Array<{ metric: string; current: number; threshold: number; passed: boolean }> = [];

  Object.keys(thresholds).forEach(key => {
    const current = data.total[key].pct;
    const threshold = thresholds[key];
    const passed = current >= threshold;
    
    results.push({
      metric: key,
      current,
      threshold,
      passed
    });
  });

  const table = new Table({
    head: [chalk.cyan('指标'), chalk.cyan('当前值'), chalk.cyan('阈值'), chalk.cyan('状态')],
    colWidths: [15, 12, 12, 15]
  });

  results.forEach(result => {
    const status = result.passed ? chalk.green('✅ 通过') : chalk.red('❌ 未达标');
    const currentColor = result.passed ? chalk.green : chalk.red;
    
    table.push([
      result.metric,
      currentColor(`${result.current.toFixed(1)}%`),
      `${result.threshold}%`,
      status
    ]);
  });

  console.log(table.toString());

  const passedCount = results.filter(r => r.passed).length;
  const totalCount = results.length;

  if (passedCount === totalCount) {
    console.log(chalk.green.bold('\n🎉 所有覆盖率阈值检查通过！'));
  } else {
    console.log(chalk.red.bold(`\n⚠️  ${totalCount - passedCount} 个指标未达到阈值要求`));
  }
}

/**
 * 获取覆盖率颜色
 */
function getCoverageColor(percentage: number): (text: string) => string {
  if (percentage >= 90) return chalk.green;
  if (percentage >= 80) return chalk.yellow;
  if (percentage >= 70) return chalk.orange;
  return chalk.red;
}

/**
 * 获取状态图标
 */
function getStatusIcon(percentage: number): string {
  if (percentage >= 90) return chalk.green('🟢 优秀');
  if (percentage >= 80) return chalk.yellow('🟡 良好');
  if (percentage >= 70) return chalk.orange('🟠 一般');
  return chalk.red('🔴 需改进');
}

/**
 * 生成覆盖率徽章
 */
function generateCoverageBadge(data: CoverageData): void {
  const coverage = data.total.lines.pct;
  const color = coverage >= 90 ? 'brightgreen' : 
                coverage >= 80 ? 'yellow' : 
                coverage >= 70 ? 'orange' : 'red';
  
  const badgeUrl = `https://img.shields.io/badge/coverage-${coverage.toFixed(1)}%25-${color}`;
  
  console.log(chalk.blue.bold('\n🏆 覆盖率徽章'));
  console.log(chalk.gray('─'.repeat(40)));
  console.log(`Markdown: ![Coverage](${badgeUrl})`);
  console.log(`URL: ${badgeUrl}`);
}

// 主函数
if (require.main === module) {
  runCoverageAnalysis().catch(error => {
    console.error(chalk.red('❌ 脚本执行失败:'), error);
    process.exit(1);
  });
}
