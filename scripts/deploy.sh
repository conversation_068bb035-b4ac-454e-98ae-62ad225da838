#!/bin/bash

# ID Provider 部署脚本
# 
# 功能说明：
# 1. 自动化部署流程
# 2. 环境检查和配置
# 3. 数据库迁移
# 4. 服务启动和健康检查
# 5. 回滚机制

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
DEPLOY_ENV="${DEPLOY_ENV:-production}"
BACKUP_DIR="${BACKUP_DIR:-/opt/backups/id-provider}"
LOG_FILE="${LOG_FILE:-/var/log/id-provider/deploy.log}"

# 日志函数
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}" | tee -a "$LOG_FILE"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}" | tee -a "$LOG_FILE"
    exit 1
}

info() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] INFO: $1${NC}" | tee -a "$LOG_FILE"
}

# 显示帮助信息
show_help() {
    cat << EOF
ID Provider 部署脚本

用法: $0 [选项]

选项:
    -e, --env ENV           部署环境 (development|staging|production) [默认: production]
    -b, --backup           部署前创建备份
    -r, --rollback         回滚到上一个版本
    -s, --skip-tests       跳过测试
    -f, --force            强制部署（跳过确认）
    -h, --help             显示此帮助信息

环境变量:
    DEPLOY_ENV             部署环境
    BACKUP_DIR             备份目录
    LOG_FILE               日志文件路径
    DATABASE_URL           数据库连接字符串
    REDIS_URL              Redis连接字符串

示例:
    $0 --env production --backup
    $0 --rollback
    DEPLOY_ENV=staging $0 --skip-tests

EOF
}

# 解析命令行参数
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -e|--env)
                DEPLOY_ENV="$2"
                shift 2
                ;;
            -b|--backup)
                CREATE_BACKUP=true
                shift
                ;;
            -r|--rollback)
                ROLLBACK=true
                shift
                ;;
            -s|--skip-tests)
                SKIP_TESTS=true
                shift
                ;;
            -f|--force)
                FORCE_DEPLOY=true
                shift
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            *)
                error "未知参数: $1"
                ;;
        esac
    done
}

# 检查系统要求
check_requirements() {
    log "检查系统要求..."
    
    # 检查必需的命令
    local required_commands=("node" "npm" "docker" "docker-compose" "git" "curl")
    for cmd in "${required_commands[@]}"; do
        if ! command -v "$cmd" &> /dev/null; then
            error "缺少必需的命令: $cmd"
        fi
    done
    
    # 检查Node.js版本
    local node_version=$(node --version | cut -d'v' -f2)
    local required_version="18.0.0"
    if ! printf '%s\n%s\n' "$required_version" "$node_version" | sort -V -C; then
        error "Node.js版本过低。需要 >= $required_version，当前版本: $node_version"
    fi
    
    # 检查磁盘空间
    local available_space=$(df "$PROJECT_DIR" | awk 'NR==2 {print $4}')
    local required_space=1048576  # 1GB in KB
    if [[ $available_space -lt $required_space ]]; then
        error "磁盘空间不足。需要至少1GB可用空间"
    fi
    
    # 检查内存
    local available_memory=$(free -m | awk 'NR==2{print $7}')
    local required_memory=512
    if [[ $available_memory -lt $required_memory ]]; then
        warn "可用内存较低: ${available_memory}MB。建议至少512MB"
    fi
    
    log "系统要求检查完成"
}

# 检查环境配置
check_environment() {
    log "检查环境配置..."
    
    # 检查环境文件
    local env_file="$PROJECT_DIR/.env.$DEPLOY_ENV"
    if [[ ! -f "$env_file" ]]; then
        error "环境配置文件不存在: $env_file"
    fi
    
    # 加载环境变量
    set -a
    source "$env_file"
    set +a
    
    # 检查必需的环境变量
    local required_vars=("DATABASE_URL" "REDIS_URL" "JWT_SECRET" "ENCRYPTION_KEY")
    for var in "${required_vars[@]}"; do
        if [[ -z "${!var}" ]]; then
            error "缺少必需的环境变量: $var"
        fi
    done
    
    # 验证数据库连接
    if ! node -e "
        const { PrismaClient } = require('@prisma/client');
        const prisma = new PrismaClient();
        prisma.\$connect()
            .then(() => { console.log('数据库连接成功'); process.exit(0); })
            .catch((e) => { console.error('数据库连接失败:', e.message); process.exit(1); });
    "; then
        error "数据库连接失败"
    fi
    
    # 验证Redis连接
    if ! node -e "
        const Redis = require('ioredis');
        const redis = new Redis(process.env.REDIS_URL);
        redis.ping()
            .then(() => { console.log('Redis连接成功'); redis.disconnect(); process.exit(0); })
            .catch((e) => { console.error('Redis连接失败:', e.message); process.exit(1); });
    "; then
        error "Redis连接失败"
    fi
    
    log "环境配置检查完成"
}

# 创建备份
create_backup() {
    if [[ "$CREATE_BACKUP" != "true" ]]; then
        return 0
    fi
    
    log "创建备份..."
    
    local backup_timestamp=$(date +%Y%m%d_%H%M%S)
    local backup_path="$BACKUP_DIR/$backup_timestamp"
    
    # 创建备份目录
    mkdir -p "$backup_path"
    
    # 备份数据库
    log "备份数据库..."
    if [[ "$DATABASE_URL" =~ postgresql:// ]]; then
        pg_dump "$DATABASE_URL" | gzip > "$backup_path/database.sql.gz"
    else
        warn "不支持的数据库类型，跳过数据库备份"
    fi
    
    # 备份应用代码
    log "备份应用代码..."
    tar -czf "$backup_path/application.tar.gz" -C "$PROJECT_DIR" \
        --exclude=node_modules \
        --exclude=.git \
        --exclude=logs \
        --exclude=tmp \
        .
    
    # 备份配置文件
    log "备份配置文件..."
    cp "$PROJECT_DIR/.env.$DEPLOY_ENV" "$backup_path/"
    
    # 记录备份信息
    cat > "$backup_path/backup_info.txt" << EOF
备份时间: $(date)
部署环境: $DEPLOY_ENV
Git提交: $(git rev-parse HEAD)
Git分支: $(git rev-parse --abbrev-ref HEAD)
应用版本: $(node -p "require('$PROJECT_DIR/package.json').version")
EOF
    
    # 创建最新备份链接
    ln -sfn "$backup_path" "$BACKUP_DIR/latest"
    
    log "备份创建完成: $backup_path"
}

# 运行测试
run_tests() {
    if [[ "$SKIP_TESTS" == "true" ]]; then
        warn "跳过测试"
        return 0
    fi
    
    log "运行测试..."
    
    cd "$PROJECT_DIR"
    
    # 运行单元测试
    log "运行单元测试..."
    npm run test:unit
    
    # 运行集成测试
    log "运行集成测试..."
    npm run test:integration
    
    # 运行端到端测试
    if [[ "$DEPLOY_ENV" == "production" ]]; then
        log "运行端到端测试..."
        npm run test:e2e
    fi
    
    # 运行安全测试
    log "运行安全测试..."
    npm audit --audit-level=high
    
    log "测试完成"
}

# 构建应用
build_application() {
    log "构建应用..."
    
    cd "$PROJECT_DIR"
    
    # 安装依赖
    log "安装依赖..."
    npm ci --production=false
    
    # 构建后端
    log "构建后端..."
    npm run build
    
    # 构建前端
    log "构建前端..."
    cd frontend
    npm ci
    npm run build
    cd ..
    
    # 构建移动端SDK
    log "构建移动端SDK..."
    cd mobile-sdk/react-native
    npm ci
    npm run build
    cd ../..
    
    log "应用构建完成"
}

# 数据库迁移
migrate_database() {
    log "执行数据库迁移..."
    
    cd "$PROJECT_DIR"
    
    # 生成Prisma客户端
    npx prisma generate
    
    # 执行迁移
    npx prisma migrate deploy
    
    # 更新数据库架构
    if [[ "$DEPLOY_ENV" != "production" ]]; then
        npx prisma db push
    fi
    
    log "数据库迁移完成"
}

# 部署应用
deploy_application() {
    log "部署应用..."
    
    cd "$PROJECT_DIR"
    
    # 停止现有服务
    log "停止现有服务..."
    if command -v pm2 &> /dev/null; then
        pm2 stop id-provider || true
    fi
    
    # 使用Docker Compose部署
    if [[ -f "docker-compose.$DEPLOY_ENV.yml" ]]; then
        log "使用Docker Compose部署..."
        docker-compose -f "docker-compose.$DEPLOY_ENV.yml" down || true
        docker-compose -f "docker-compose.$DEPLOY_ENV.yml" up -d
    else
        # 使用PM2部署
        log "使用PM2部署..."
        pm2 start ecosystem.config.js --env "$DEPLOY_ENV"
    fi
    
    log "应用部署完成"
}

# 健康检查
health_check() {
    log "执行健康检查..."
    
    local max_attempts=30
    local attempt=1
    local health_url="http://localhost:${PORT:-3000}/health"
    
    while [[ $attempt -le $max_attempts ]]; do
        if curl -f -s "$health_url" > /dev/null; then
            log "健康检查通过"
            return 0
        fi
        
        info "健康检查失败，重试 $attempt/$max_attempts..."
        sleep 10
        ((attempt++))
    done
    
    error "健康检查失败，部署可能有问题"
}

# 回滚
rollback() {
    log "开始回滚..."
    
    local latest_backup="$BACKUP_DIR/latest"
    if [[ ! -d "$latest_backup" ]]; then
        error "没有找到备份，无法回滚"
    fi
    
    # 停止当前服务
    log "停止当前服务..."
    if command -v pm2 &> /dev/null; then
        pm2 stop id-provider || true
    fi
    
    if [[ -f "docker-compose.$DEPLOY_ENV.yml" ]]; then
        docker-compose -f "docker-compose.$DEPLOY_ENV.yml" down || true
    fi
    
    # 恢复应用代码
    log "恢复应用代码..."
    cd "$PROJECT_DIR"
    tar -xzf "$latest_backup/application.tar.gz"
    
    # 恢复配置文件
    log "恢复配置文件..."
    cp "$latest_backup/.env.$DEPLOY_ENV" "$PROJECT_DIR/"
    
    # 恢复数据库
    log "恢复数据库..."
    if [[ -f "$latest_backup/database.sql.gz" ]]; then
        gunzip -c "$latest_backup/database.sql.gz" | psql "$DATABASE_URL"
    fi
    
    # 重新启动服务
    deploy_application
    
    # 健康检查
    health_check
    
    log "回滚完成"
}

# 清理
cleanup() {
    log "执行清理..."
    
    # 清理旧的备份（保留最近10个）
    if [[ -d "$BACKUP_DIR" ]]; then
        find "$BACKUP_DIR" -maxdepth 1 -type d -name "20*" | sort -r | tail -n +11 | xargs rm -rf
    fi
    
    # 清理Docker镜像
    if command -v docker &> /dev/null; then
        docker system prune -f
    fi
    
    # 清理日志文件（保留最近30天）
    find /var/log/id-provider -name "*.log" -mtime +30 -delete 2>/dev/null || true
    
    log "清理完成"
}

# 确认部署
confirm_deployment() {
    if [[ "$FORCE_DEPLOY" == "true" ]]; then
        return 0
    fi
    
    echo
    echo "部署信息:"
    echo "  环境: $DEPLOY_ENV"
    echo "  Git分支: $(git rev-parse --abbrev-ref HEAD)"
    echo "  Git提交: $(git rev-parse --short HEAD)"
    echo "  应用版本: $(node -p "require('$PROJECT_DIR/package.json').version")"
    echo "  备份: ${CREATE_BACKUP:-false}"
    echo
    
    read -p "确认部署? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log "部署已取消"
        exit 0
    fi
}

# 主函数
main() {
    log "开始ID Provider部署流程"
    
    # 解析参数
    parse_args "$@"
    
    # 如果是回滚，直接执行回滚
    if [[ "$ROLLBACK" == "true" ]]; then
        rollback
        exit 0
    fi
    
    # 创建日志目录
    mkdir -p "$(dirname "$LOG_FILE")"
    
    # 确认部署
    confirm_deployment
    
    # 检查系统要求
    check_requirements
    
    # 检查环境配置
    check_environment
    
    # 创建备份
    create_backup
    
    # 运行测试
    run_tests
    
    # 构建应用
    build_application
    
    # 数据库迁移
    migrate_database
    
    # 部署应用
    deploy_application
    
    # 健康检查
    health_check
    
    # 清理
    cleanup
    
    log "ID Provider部署完成！"
    log "应用地址: http://localhost:${PORT:-3000}"
    log "健康检查: http://localhost:${PORT:-3000}/health"
    log "管理界面: http://localhost:${PORT:-3000}/admin"
}

# 错误处理
trap 'error "部署过程中发生错误，请检查日志: $LOG_FILE"' ERR

# 执行主函数
main "$@"
