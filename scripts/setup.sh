#!/bin/bash

# 身份提供商(IdP)项目设置脚本
# 用于快速设置开发环境

set -e

echo "🚀 开始设置身份提供商(IdP)项目..."

# 检查Node.js版本
echo "📋 检查Node.js版本..."
if ! command -v node &> /dev/null; then
    echo "❌ Node.js未安装，请先安装Node.js 18+"
    exit 1
fi

NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    echo "❌ Node.js版本过低，需要18+，当前版本: $(node -v)"
    exit 1
fi

echo "✅ Node.js版本检查通过: $(node -v)"

# 检查PostgreSQL
echo "📋 检查PostgreSQL..."
if ! command -v psql &> /dev/null; then
    echo "⚠️  PostgreSQL未安装，请确保已安装PostgreSQL 13+"
fi

# 安装依赖
echo "📦 安装项目依赖..."
npm install

# 复制环境变量文件
if [ ! -f .env ]; then
    echo "📝 创建环境变量文件..."
    cp .env.example .env
    echo "✅ 已创建.env文件，请根据需要修改配置"
else
    echo "✅ .env文件已存在"
fi

# 检查数据库连接
echo "🗄️  检查数据库配置..."
if grep -q "postgresql://username:password@localhost:5432/id_provider" .env; then
    echo "⚠️  请更新.env文件中的数据库连接字符串"
    echo "   DATABASE_URL=\"postgresql://username:password@localhost:5432/id_provider\""
fi

# 生成Prisma客户端
echo "🔧 生成Prisma客户端..."
npm run db:generate

# 提示下一步操作
echo ""
echo "🎉 项目设置完成！"
echo ""
echo "📋 下一步操作："
echo "1. 更新.env文件中的配置（数据库、JWT密钥等）"
echo "2. 创建数据库: createdb id_provider"
echo "3. 运行数据库迁移: npm run db:migrate"
echo "4. 初始化种子数据: npm run db:seed"
echo "5. 启动开发服务器: npm run dev"
echo ""
echo "📚 更多信息请查看docs/development.md"
echo ""
echo "🔐 默认管理员账户："
echo "   邮箱: <EMAIL>"
echo "   密码: admin123456"
echo ""
echo "🌐 服务将在 http://localhost:3000 启动"
