#!/bin/bash

# 身份提供商功能验证测试脚本
# 运行核心业务逻辑的功能验证测试

set -e

echo "🚀 开始运行身份提供商功能验证测试..."
echo "=================================================="

# 检查Node.js环境
echo "📋 检查运行环境..."
node --version
npm --version

# 检查依赖
echo "📦 检查项目依赖..."
if [ ! -d "node_modules" ]; then
    echo "⚠️  node_modules 不存在，正在安装依赖..."
    npm install
fi

# 生成Prisma客户端
echo "🗄️  生成数据库客户端..."
npm run db:generate

# 运行数据库迁移
echo "🔄 运行数据库迁移..."
npm run db:migrate

echo ""
echo "🧪 运行核心功能验证测试..."
echo "=================================================="

# 运行功能验证测试
npm test -- --testPathPattern=functional.test.ts --verbose

echo ""
echo "✅ 功能验证测试完成！"
echo "=================================================="

# 显示测试覆盖的功能模块
echo "📊 已验证的功能模块："
echo "  ✓ 密码加密和验证 (bcrypt)"
echo "  ✓ JWT令牌生成和验证"
echo "  ✓ 邮箱格式验证"
echo "  ✓ 密码强度检查"
echo "  ✓ UUID生成"
echo "  ✓ 时间处理"
echo ""
echo "🎉 身份提供商核心功能验证通过！"
