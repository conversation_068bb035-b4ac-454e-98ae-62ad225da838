/**
 * 管理员API服务
 * 提供管理员控制台所需的API接口
 */

import axios from 'axios'
import { useAuthStore } from '../stores/authStore'

// 创建管理员API实例
const adminApiClient = axios.create({
  baseURL: '/api/v1/admin',
  timeout: 10000,
})

// 请求拦截器 - 添加认证头
adminApiClient.interceptors.request.use(
  (config) => {
    const { accessToken } = useAuthStore.getState()
    if (accessToken) {
      config.headers.Authorization = `Bearer ${accessToken}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器 - 处理错误
adminApiClient.interceptors.response.use(
  (response) => response.data,
  async (error) => {
    if (error.response?.status === 401) {
      // 尝试刷新令牌
      const { refreshAccessToken, clearAuth } = useAuthStore.getState()
      const refreshed = await refreshAccessToken()
      
      if (refreshed) {
        // 重试原请求
        const originalRequest = error.config
        const { accessToken } = useAuthStore.getState()
        originalRequest.headers.Authorization = `Bearer ${accessToken}`
        return adminApiClient.request(originalRequest)
      } else {
        // 刷新失败，清除认证状态
        clearAuth()
        window.location.href = '/login'
      }
    }
    
    return Promise.reject(error)
  }
)

// 系统统计接口
export interface SystemStats {
  totalUsers: number
  activeUsers: number
  totalSessions: number
  oauthClients: number
  apiRequests24h: number
  errorRate: number
  systemHealth: {
    database: boolean
    redis: boolean
    memory: number
    cpu: number
  }
}

// 用户接口
export interface AdminUser {
  id: string
  email: string
  nickname?: string
  firstName?: string
  lastName?: string
  isActive: boolean
  isEmailVerified: boolean
  mfaEnabled: boolean
  lastLoginAt?: string
  createdAt: string
  updatedAt: string
  roles: string[]
  sessions: number
  loginCount: number
}

// OAuth客户端接口
export interface AdminOAuthClient {
  id: string
  clientId: string
  name: string
  description?: string
  redirectUris: string[]
  grantTypes: string[]
  responseTypes: string[]
  scopes: string[]
  isActive: boolean
  requirePkce: boolean
  requireConsent: boolean
  accessTokenLifetime: number
  refreshTokenLifetime: number
  createdAt: string
  updatedAt: string
  usageStats: {
    totalAuthorizations: number
    activeTokens: number
    lastUsed?: string
  }
}

// 审计日志接口
export interface AuditLog {
  id: string
  action: string
  resource: string
  resourceId?: string
  userId?: string
  userEmail?: string
  ipAddress: string
  userAgent: string
  details: Record<string, any>
  createdAt: string
}

// 系统配置接口
export interface SystemConfig {
  id: string
  key: string
  value: any
  description?: string
  category: string
  isPublic: boolean
  updatedAt: string
}

export const adminApi = {
  // 系统统计
  async getSystemStats(): Promise<SystemStats> {
    return adminApiClient.get('/stats')
  },

  // 系统健康检查
  async getSystemHealth(): Promise<any> {
    return adminApiClient.get('/health')
  },

  // 性能监控
  async getPerformanceMetrics(timeWindow?: number): Promise<any> {
    return adminApiClient.get('/metrics', {
      params: { timeWindow }
    })
  },

  // 用户管理
  async getUsers(params?: {
    page?: number
    limit?: number
    search?: string
    isActive?: boolean
    role?: string
  }): Promise<{ users: AdminUser[]; total: number }> {
    return adminApiClient.get('/users', { params })
  },

  async getUser(id: string): Promise<AdminUser> {
    return adminApiClient.get(`/users/${id}`)
  },

  async createUser(data: {
    email: string
    password?: string
    nickname?: string
    firstName?: string
    lastName?: string
    roles?: string[]
    isActive?: boolean
  }): Promise<AdminUser> {
    return adminApiClient.post('/users', data)
  },

  async updateUser(id: string, data: Partial<AdminUser>): Promise<AdminUser> {
    return adminApiClient.put(`/users/${id}`, data)
  },

  async deleteUser(id: string): Promise<void> {
    return adminApiClient.delete(`/users/${id}`)
  },

  async resetUserPassword(id: string): Promise<{ temporaryPassword: string }> {
    return adminApiClient.post(`/users/${id}/reset-password`)
  },

  async toggleUserStatus(id: string): Promise<AdminUser> {
    return adminApiClient.post(`/users/${id}/toggle-status`)
  },

  async getUserSessions(id: string): Promise<any[]> {
    return adminApiClient.get(`/users/${id}/sessions`)
  },

  async revokeUserSessions(id: string): Promise<void> {
    return adminApiClient.delete(`/users/${id}/sessions`)
  },

  // OAuth客户端管理
  async getOAuthClients(params?: {
    page?: number
    limit?: number
    search?: string
    isActive?: boolean
  }): Promise<{ clients: AdminOAuthClient[]; total: number }> {
    return adminApiClient.get('/oauth-clients', { params })
  },

  async getOAuthClient(id: string): Promise<AdminOAuthClient> {
    return adminApiClient.get(`/oauth-clients/${id}`)
  },

  async createOAuthClient(data: {
    name: string
    description?: string
    redirectUris: string[]
    grantTypes?: string[]
    responseTypes?: string[]
    scopes?: string[]
    requirePkce?: boolean
    requireConsent?: boolean
    accessTokenLifetime?: number
    refreshTokenLifetime?: number
  }): Promise<AdminOAuthClient & { clientSecret: string }> {
    return adminApiClient.post('/oauth-clients', data)
  },

  async updateOAuthClient(id: string, data: Partial<AdminOAuthClient>): Promise<AdminOAuthClient> {
    return adminApiClient.put(`/oauth-clients/${id}`, data)
  },

  async deleteOAuthClient(id: string): Promise<void> {
    return adminApiClient.delete(`/oauth-clients/${id}`)
  },

  async regenerateClientSecret(id: string): Promise<{ clientSecret: string }> {
    return adminApiClient.post(`/oauth-clients/${id}/regenerate-secret`)
  },

  async getClientUsageStats(id: string): Promise<any> {
    return adminApiClient.get(`/oauth-clients/${id}/stats`)
  },

  // 审计日志
  async getAuditLogs(params?: {
    page?: number
    limit?: number
    action?: string
    userId?: string
    startDate?: string
    endDate?: string
  }): Promise<{ logs: AuditLog[]; total: number }> {
    return adminApiClient.get('/audit-logs', { params })
  },

  async getAuditLog(id: string): Promise<AuditLog> {
    return adminApiClient.get(`/audit-logs/${id}`)
  },

  // 系统配置
  async getSystemConfigs(category?: string): Promise<SystemConfig[]> {
    return adminApiClient.get('/configs', {
      params: { category }
    })
  },

  async updateSystemConfig(key: string, value: any): Promise<SystemConfig> {
    return adminApiClient.put(`/configs/${key}`, { value })
  },

  async getSystemConfig(key: string): Promise<SystemConfig> {
    return adminApiClient.get(`/configs/${key}`)
  },

  // 安全管理
  async getSecurityEvents(params?: {
    page?: number
    limit?: number
    type?: string
    severity?: string
    startDate?: string
    endDate?: string
  }): Promise<any> {
    return adminApiClient.get('/security/events', { params })
  },

  async getFailedLogins(params?: {
    page?: number
    limit?: number
    timeWindow?: number
  }): Promise<any> {
    return adminApiClient.get('/security/failed-logins', { params })
  },

  async getSuspiciousActivities(): Promise<any> {
    return adminApiClient.get('/security/suspicious-activities')
  },

  async blockIP(ip: string, reason?: string): Promise<void> {
    return adminApiClient.post('/security/block-ip', { ip, reason })
  },

  async unblockIP(ip: string): Promise<void> {
    return adminApiClient.delete(`/security/block-ip/${ip}`)
  },

  async getBlockedIPs(): Promise<any[]> {
    return adminApiClient.get('/security/blocked-ips')
  },

  // 系统维护
  async clearCache(type?: string): Promise<void> {
    return adminApiClient.post('/maintenance/clear-cache', { type })
  },

  async cleanupExpiredTokens(): Promise<{ deletedCount: number }> {
    return adminApiClient.post('/maintenance/cleanup-tokens')
  },

  async cleanupExpiredSessions(): Promise<{ deletedCount: number }> {
    return adminApiClient.post('/maintenance/cleanup-sessions')
  },

  async exportData(type: string, format: 'json' | 'csv' = 'json'): Promise<Blob> {
    const response = await adminApiClient.get(`/export/${type}`, {
      params: { format },
      responseType: 'blob'
    })
    return response
  },

  async importData(type: string, file: File): Promise<{ imported: number; errors: any[] }> {
    const formData = new FormData()
    formData.append('file', file)
    
    return adminApiClient.post(`/import/${type}`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  // 实时监控
  async getRealtimeStats(): Promise<any> {
    return adminApiClient.get('/realtime/stats')
  },

  async getActiveUsers(): Promise<any[]> {
    return adminApiClient.get('/realtime/active-users')
  },

  async getRecentActivities(): Promise<any[]> {
    return adminApiClient.get('/realtime/activities')
  },

  // 系统通知
  async getSystemNotifications(): Promise<any[]> {
    return adminApiClient.get('/notifications')
  },

  async markNotificationRead(id: string): Promise<void> {
    return adminApiClient.put(`/notifications/${id}/read`)
  },

  async createSystemNotification(data: {
    title: string
    message: string
    type: 'info' | 'warning' | 'error' | 'success'
    targetUsers?: string[]
  }): Promise<any> {
    return adminApiClient.post('/notifications', data)
  }
}

export default adminApi
