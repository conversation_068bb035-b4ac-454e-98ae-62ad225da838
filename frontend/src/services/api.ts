import axios, { AxiosInstance, AxiosResponse } from 'axios'
import { LoginCredentials, RegisterData, User, LoginResult } from '../stores/authStore'

// API 基础配置
const API_BASE_URL = '/api/v1'

// 创建 axios 实例
const apiClient: AxiosInstance = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// 请求拦截器 - 添加认证令牌
apiClient.interceptors.request.use(
  (config) => {
    const authStore = JSON.parse(localStorage.getItem('auth-storage') || '{}')
    const accessToken = authStore.state?.accessToken
    
    if (accessToken) {
      config.headers.Authorization = `Bearer ${accessToken}`
    }
    
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器 - 处理错误和令牌刷新
apiClient.interceptors.response.use(
  (response: AxiosResponse) => {
    return response
  },
  async (error) => {
    const originalRequest = error.config
    
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true
      
      try {
        const authStore = JSON.parse(localStorage.getItem('auth-storage') || '{}')
        const refreshToken = authStore.state?.refreshToken
        
        if (refreshToken) {
          const response = await axios.post(`${API_BASE_URL}/auth/refresh-token`, {
            refreshToken,
          })
          
          const { accessToken, refreshToken: newRefreshToken } = response.data
          
          // 更新存储的令牌
          const updatedStore = {
            ...authStore,
            state: {
              ...authStore.state,
              accessToken,
              refreshToken: newRefreshToken || refreshToken,
            },
          }
          localStorage.setItem('auth-storage', JSON.stringify(updatedStore))
          
          // 重试原始请求
          originalRequest.headers.Authorization = `Bearer ${accessToken}`
          return apiClient(originalRequest)
        }
      } catch (refreshError) {
        // 刷新失败，清除认证状态
        localStorage.removeItem('auth-storage')
        window.location.href = '/login'
        return Promise.reject(refreshError)
      }
    }
    
    return Promise.reject(error)
  }
)

// 认证相关 API
export const authApi = {
  // 用户登录
  login: async (credentials: LoginCredentials): Promise<LoginResult> => {
    try {
      const response = await apiClient.post('/auth/login', credentials)
      return response.data
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '登录失败')
    }
  },

  // 用户注册
  register: async (userData: RegisterData): Promise<void> => {
    try {
      await apiClient.post('/auth/register', userData)
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '注册失败')
    }
  },

  // 用户登出
  logout: async (): Promise<void> => {
    try {
      await apiClient.post('/auth/logout')
    } catch (error: any) {
      console.error('登出请求失败:', error)
    }
  },

  // 刷新令牌
  refreshToken: async (refreshToken: string): Promise<{ accessToken: string; refreshToken?: string }> => {
    try {
      const response = await apiClient.post('/auth/refresh-token', { refreshToken })
      return response.data
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '刷新令牌失败')
    }
  },

  // 获取当前用户信息
  getCurrentUser: async (): Promise<User> => {
    try {
      const response = await apiClient.get('/me')
      return response.data
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '获取用户信息失败')
    }
  },

  // 忘记密码
  forgotPassword: async (email: string): Promise<void> => {
    try {
      await apiClient.post('/auth/forgot-password', { email })
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '发送重置邮件失败')
    }
  },

  // 重置密码
  resetPassword: async (token: string, password: string): Promise<void> => {
    try {
      await apiClient.post('/auth/reset-password', { token, password })
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '重置密码失败')
    }
  },

  // 修改密码
  changePassword: async (currentPassword: string, newPassword: string): Promise<void> => {
    try {
      await apiClient.post('/auth/change-password', {
        currentPassword,
        newPassword,
      })
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '修改密码失败')
    }
  },

  // 验证邮箱
  verifyEmail: async (token: string): Promise<void> => {
    try {
      await apiClient.get(`/auth/verify-email?token=${token}`)
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '邮箱验证失败')
    }
  },
}

// MFA 相关 API
export const mfaApi = {
  // 获取 MFA 状态
  getMfaStatus: async (): Promise<any> => {
    try {
      const response = await apiClient.get('/me/mfa')
      return response.data
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '获取MFA状态失败')
    }
  },

  // 启用 MFA
  enableMfa: async (method: string, data: any): Promise<any> => {
    try {
      const response = await apiClient.post('/me/mfa/enable', {
        method,
        ...data,
      })
      return response.data
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '启用MFA失败')
    }
  },

  // 验证 MFA
  verifyMfa: async (method: string, code: string, deviceId?: string): Promise<any> => {
    try {
      const response = await apiClient.post('/me/mfa/verify', {
        method,
        code,
        deviceId,
      })
      return response.data
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'MFA验证失败')
    }
  },

  // 禁用 MFA 设备
  disableMfaDevice: async (deviceId: string): Promise<void> => {
    try {
      await apiClient.delete(`/me/mfa/devices/${deviceId}`)
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '禁用MFA设备失败')
    }
  },
}

// OAuth 相关 API
export const oauthApi = {
  // 获取 OAuth 授权 URL
  getAuthUrl: async (provider: string, redirectUri?: string): Promise<string> => {
    const params = new URLSearchParams()
    if (redirectUri) {
      params.append('redirect_uri', redirectUri)
    }
    
    return `/api/v1/auth/${provider}?${params.toString()}`
  },
}

export default apiClient
