import React, { useEffect, useState } from 'react'
import { useNavigate, useSearchParams } from 'react-router-dom'
import { Result, Button, Alert, Descriptions, Card } from 'antd'
import { ExclamationCircleOutlined, HomeOutlined, ReloadOutlined } from '@ant-design/icons'

interface ErrorDetails {
  provider?: string
  errorType?: string
  errorMessage?: string
  stage?: string
  reason?: string
  timestamp?: string
  details?: string
}

const OAuthErrorPage: React.FC = () => {
  const navigate = useNavigate()
  const [searchParams] = useSearchParams()
  const [errorDetails, setErrorDetails] = useState<ErrorDetails>({})
  
  const error = searchParams.get('error') || 'unknown_error'
  const message = searchParams.get('message') || '登录过程中发生未知错误'
  const timestamp = searchParams.get('timestamp')
  const details = searchParams.get('details')

  useEffect(() => {
    // 解析错误详情
    if (details) {
      try {
        const parsedDetails = JSON.parse(details)
        setErrorDetails(parsedDetails)
      } catch (e) {
        console.warn('无法解析错误详情:', e)
      }
    }
  }, [details])

  // 获取错误类型的友好显示
  const getErrorTitle = (errorCode: string): string => {
    switch (errorCode) {
      case 'oauth_error':
        return '第三方登录失败'
      case 'oauth_denied':
        return '登录被拒绝'
      case 'oauth_callback_error':
        return '登录处理失败'
      case 'invalid_state':
        return '安全验证失败'
      case 'access_denied':
        return '访问被拒绝'
      default:
        return '登录错误'
    }
  }

  // 获取错误的建议解决方案
  const getSuggestions = (errorCode: string, provider?: string): string[] => {
    const suggestions: string[] = []
    
    switch (errorCode) {
      case 'oauth_denied':
        suggestions.push('请确保您同意了授权请求')
        suggestions.push('检查您的第三方账户是否正常')
        break
      case 'oauth_error':
        suggestions.push('请检查网络连接是否正常')
        suggestions.push('稍后重试登录')
        if (provider) {
          suggestions.push(`检查${provider}服务是否可用`)
        }
        break
      case 'oauth_callback_error':
        suggestions.push('登录处理过程中出现问题')
        suggestions.push('请重新尝试登录')
        suggestions.push('如果问题持续存在，请联系技术支持')
        break
      default:
        suggestions.push('请重新尝试登录')
        suggestions.push('如果问题持续存在，请联系技术支持')
    }
    
    return suggestions
  }

  const handleRetry = () => {
    navigate('/login')
  }

  const handleGoHome = () => {
    navigate('/')
  }

  const suggestions = getSuggestions(error, errorDetails.provider)

  return (
    <div className="auth-container">
      <div className="auth-card">
        <Result
          status="error"
          icon={<ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />}
          title={getErrorTitle(error)}
          subTitle={message}
          extra={[
            <Button type="primary" key="retry" icon={<ReloadOutlined />} onClick={handleRetry}>
              重新登录
            </Button>,
            <Button key="home" icon={<HomeOutlined />} onClick={handleGoHome}>
              返回首页
            </Button>,
          ]}
        />

        {/* 错误详情 */}
        {Object.keys(errorDetails).length > 0 && (
          <Card 
            title="错误详情" 
            size="small" 
            style={{ marginTop: 24, textAlign: 'left' }}
          >
            <Descriptions column={1} size="small">
              {errorDetails.provider && (
                <Descriptions.Item label="登录提供商">
                  {errorDetails.provider.toUpperCase()}
                </Descriptions.Item>
              )}
              {errorDetails.errorType && (
                <Descriptions.Item label="错误类型">
                  {errorDetails.errorType}
                </Descriptions.Item>
              )}
              {errorDetails.stage && (
                <Descriptions.Item label="错误阶段">
                  {errorDetails.stage}
                </Descriptions.Item>
              )}
              {timestamp && (
                <Descriptions.Item label="发生时间">
                  {new Date(parseInt(timestamp)).toLocaleString()}
                </Descriptions.Item>
              )}
            </Descriptions>
          </Card>
        )}

        {/* 解决建议 */}
        {suggestions.length > 0 && (
          <Alert
            message="解决建议"
            description={
              <ul style={{ margin: 0, paddingLeft: 20 }}>
                {suggestions.map((suggestion, index) => (
                  <li key={index}>{suggestion}</li>
                ))}
              </ul>
            }
            type="info"
            showIcon
            style={{ marginTop: 24, textAlign: 'left' }}
          />
        )}

        {/* 开发环境显示详细错误信息 */}
        {process.env.NODE_ENV === 'development' && errorDetails.errorMessage && (
          <Alert
            message="开发调试信息"
            description={errorDetails.errorMessage}
            type="warning"
            showIcon
            style={{ marginTop: 16, textAlign: 'left' }}
          />
        )}
      </div>
    </div>
  )
}

export default OAuthErrorPage
