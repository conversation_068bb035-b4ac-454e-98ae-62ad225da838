import React from 'react'
import { Card, Row, Col, Button, Space, Typography, Divider, Tag } from 'antd'
import { 
  LoginOutlined, 
  UserAddOutlined, 
  SafetyOutlined, 
  GoogleOutlined, 
  GithubOutlined,
  MailOutlined,
  MobileOutlined,
  QrcodeOutlined,
  DashboardOutlined
} from '@ant-design/icons'
import { Link } from 'react-router-dom'

const { Title, Paragraph, Text } = Typography

const DemoPage: React.FC = () => {
  const features = [
    {
      title: '用户登录',
      description: '支持用户名/邮箱登录，包含记住我功能和表单验证',
      icon: <LoginOutlined style={{ fontSize: 24, color: '#1890ff' }} />,
      link: '/login',
      tags: ['表单验证', '记住我', '错误处理']
    },
    {
      title: '用户注册',
      description: '完整的注册流程，包含密码强度检查和实时验证',
      icon: <UserAddOutlined style={{ fontSize: 24, color: '#52c41a' }} />,
      link: '/register',
      tags: ['密码强度', '实时验证', '邮箱验证']
    },
    {
      title: 'MFA认证',
      description: '多因素认证支持TOTP、邮件和短信验证',
      icon: <SafetyOutlined style={{ fontSize: 24, color: '#fa8c16' }} />,
      link: '/mfa',
      tags: ['TOTP', '邮件验证', '短信验证']
    },
    {
      title: '用户仪表板',
      description: '用户信息管理、安全设置和会话管理',
      icon: <DashboardOutlined style={{ fontSize: 24, color: '#722ed1' }} />,
      link: '/dashboard',
      tags: ['用户信息', '安全设置', '会话管理']
    }
  ]

  const oauthProviders = [
    {
      name: 'Google',
      icon: <GoogleOutlined style={{ fontSize: 20, color: '#db4437' }} />,
      description: 'Google账户登录'
    },
    {
      name: 'GitHub',
      icon: <GithubOutlined style={{ fontSize: 20, color: '#333' }} />,
      description: 'GitHub账户登录'
    }
  ]

  const mfaMethods = [
    {
      name: 'TOTP认证器',
      icon: <QrcodeOutlined style={{ fontSize: 20, color: '#1890ff' }} />,
      description: 'Google Authenticator、Authy等'
    },
    {
      name: '邮件验证',
      icon: <MailOutlined style={{ fontSize: 20, color: '#52c41a' }} />,
      description: '发送验证码到注册邮箱'
    },
    {
      name: '短信验证',
      icon: <MobileOutlined style={{ fontSize: 20, color: '#fa8c16' }} />,
      description: '发送验证码到手机号'
    }
  ]

  return (
    <div style={{ 
      minHeight: '100vh', 
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      padding: '40px 20px'
    }}>
      <div style={{ maxWidth: 1200, margin: '0 auto' }}>
        {/* 头部 */}
        <div style={{ textAlign: 'center', marginBottom: 40 }}>
          <div style={{
            width: 80,
            height: 80,
            background: 'white',
            borderRadius: '50%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            margin: '0 auto 20px',
            fontSize: 32,
            fontWeight: 'bold',
            color: '#1890ff',
            boxShadow: '0 4px 12px rgba(0,0,0,0.1)'
          }}>
            IdP
          </div>
          <Title level={1} style={{ color: 'white', marginBottom: 16 }}>
            身份提供商认证界面
          </Title>
          <Paragraph style={{ color: 'rgba(255,255,255,0.8)', fontSize: 18, maxWidth: 600, margin: '0 auto' }}>
            完整的身份认证解决方案，包含登录、注册、多因素认证、OAuth集成等功能
          </Paragraph>
        </div>

        {/* 主要功能 */}
        <Title level={2} style={{ color: 'white', textAlign: 'center', marginBottom: 30 }}>
          核心功能
        </Title>
        
        <Row gutter={[24, 24]} style={{ marginBottom: 60 }}>
          {features.map((feature, index) => (
            <Col xs={24} sm={12} lg={6} key={index}>
              <Card
                hoverable
                style={{ 
                  height: '100%',
                  borderRadius: 12,
                  boxShadow: '0 4px 12px rgba(0,0,0,0.1)'
                }}
                bodyStyle={{ padding: 24 }}
              >
                <div style={{ textAlign: 'center', marginBottom: 16 }}>
                  {feature.icon}
                </div>
                <Title level={4} style={{ textAlign: 'center', marginBottom: 12 }}>
                  {feature.title}
                </Title>
                <Paragraph style={{ textAlign: 'center', color: '#666', marginBottom: 16 }}>
                  {feature.description}
                </Paragraph>
                <div style={{ textAlign: 'center', marginBottom: 16 }}>
                  {feature.tags.map(tag => (
                    <Tag key={tag} color="blue" style={{ margin: 2 }}>
                      {tag}
                    </Tag>
                  ))}
                </div>
                <div style={{ textAlign: 'center' }}>
                  <Link to={feature.link}>
                    <Button type="primary" size="small">
                      体验功能
                    </Button>
                  </Link>
                </div>
              </Card>
            </Col>
          ))}
        </Row>

        {/* OAuth集成 */}
        <Row gutter={[40, 40]}>
          <Col xs={24} lg={12}>
            <Card
              title={
                <Title level={3} style={{ margin: 0, color: '#1890ff' }}>
                  OAuth第三方登录
                </Title>
              }
              style={{ 
                borderRadius: 12,
                boxShadow: '0 4px 12px rgba(0,0,0,0.1)'
              }}
            >
              <Space direction="vertical" size="middle" style={{ width: '100%' }}>
                {oauthProviders.map((provider, index) => (
                  <div key={index} style={{ 
                    display: 'flex', 
                    alignItems: 'center',
                    padding: 12,
                    background: '#fafafa',
                    borderRadius: 8
                  }}>
                    <div style={{ marginRight: 12 }}>
                      {provider.icon}
                    </div>
                    <div>
                      <Text strong>{provider.name}</Text>
                      <br />
                      <Text type="secondary" style={{ fontSize: 12 }}>
                        {provider.description}
                      </Text>
                    </div>
                  </div>
                ))}
              </Space>
              <Divider />
              <div style={{ textAlign: 'center' }}>
                <Link to="/login">
                  <Button type="primary">
                    体验OAuth登录
                  </Button>
                </Link>
              </div>
            </Card>
          </Col>

          <Col xs={24} lg={12}>
            <Card
              title={
                <Title level={3} style={{ margin: 0, color: '#fa8c16' }}>
                  多因素认证 (MFA)
                </Title>
              }
              style={{ 
                borderRadius: 12,
                boxShadow: '0 4px 12px rgba(0,0,0,0.1)'
              }}
            >
              <Space direction="vertical" size="middle" style={{ width: '100%' }}>
                {mfaMethods.map((method, index) => (
                  <div key={index} style={{ 
                    display: 'flex', 
                    alignItems: 'center',
                    padding: 12,
                    background: '#fafafa',
                    borderRadius: 8
                  }}>
                    <div style={{ marginRight: 12 }}>
                      {method.icon}
                    </div>
                    <div>
                      <Text strong>{method.name}</Text>
                      <br />
                      <Text type="secondary" style={{ fontSize: 12 }}>
                        {method.description}
                      </Text>
                    </div>
                  </div>
                ))}
              </Space>
              <Divider />
              <div style={{ textAlign: 'center' }}>
                <Link to="/mfa">
                  <Button type="primary">
                    体验MFA认证
                  </Button>
                </Link>
              </div>
            </Card>
          </Col>
        </Row>

        {/* 底部信息 */}
        <div style={{ 
          textAlign: 'center', 
          marginTop: 60,
          padding: 30,
          background: 'rgba(255,255,255,0.1)',
          borderRadius: 12,
          backdropFilter: 'blur(10px)'
        }}>
          <Title level={3} style={{ color: 'white', marginBottom: 16 }}>
            开始使用
          </Title>
          <Paragraph style={{ color: 'rgba(255,255,255,0.8)', marginBottom: 24 }}>
            选择一个功能开始体验，或者查看完整的使用文档
          </Paragraph>
          <Space size="large">
            <Link to="/login">
              <Button type="primary" size="large" icon={<LoginOutlined />}>
                立即登录
              </Button>
            </Link>
            <Link to="/register">
              <Button size="large" icon={<UserAddOutlined />} style={{ 
                background: 'rgba(255,255,255,0.2)', 
                borderColor: 'rgba(255,255,255,0.3)',
                color: 'white'
              }}>
                创建账户
              </Button>
            </Link>
          </Space>
        </div>
      </div>
    </div>
  )
}

export default DemoPage
