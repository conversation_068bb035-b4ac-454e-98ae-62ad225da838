import React, { useState } from 'react'
import { Link } from 'react-router-dom'
import { Form, Input, Button, Al<PERSON>, Result } from 'antd'
import { MailOutlined, ArrowLeftOutlined } from '@ant-design/icons'
import { authApi } from '../services/api'

const ForgotPasswordPage: React.FC = () => {
  const [form] = Form.useForm()
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState(false)
  const [email, setEmail] = useState('')

  const handleSubmit = async (values: { email: string }) => {
    setLoading(true)
    setError(null)

    try {
      await authApi.forgotPassword(values.email)
      setEmail(values.email)
      setSuccess(true)
    } catch (err: any) {
      setError(err.message || '发送重置邮件失败')
    } finally {
      setLoading(false)
    }
  }

  if (success) {
    return (
      <div className="auth-container">
        <div className="auth-card">
          <Result
            status="success"
            title="重置邮件已发送"
            subTitle={`我们已向 ${email} 发送了密码重置链接，请检查您的邮箱。`}
            extra={[
              <Button type="primary" key="login">
                <Link to="/login">返回登录</Link>
              </Button>,
            ]}
          />
        </div>
      </div>
    )
  }

  return (
    <div className="auth-container">
      <div className="auth-card">
        <div className="auth-header">
          <div className="auth-logo">
            <MailOutlined />
          </div>
          <h1 className="auth-title">忘记密码</h1>
          <p className="auth-subtitle">输入您的邮箱地址，我们将发送重置链接</p>
        </div>

        <div className="auth-form">
          {error && (
            <Alert
              message={error}
              type="error"
              showIcon
              closable
              onClose={() => setError(null)}
              style={{ marginBottom: 16 }}
            />
          )}

          <Form
            form={form}
            name="forgot-password"
            onFinish={handleSubmit}
            autoComplete="off"
            size="large"
          >
            <Form.Item
              name="email"
              rules={[
                { required: true, message: '请输入邮箱地址' },
                { type: 'email', message: '请输入有效的邮箱地址' },
              ]}
            >
              <Input
                prefix={<MailOutlined />}
                placeholder="邮箱地址"
                autoComplete="email"
              />
            </Form.Item>

            <Form.Item>
              <Button
                type="primary"
                htmlType="submit"
                loading={loading}
                block
              >
                {loading ? '发送中...' : '发送重置链接'}
              </Button>
            </Form.Item>
          </Form>
        </div>

        <div className="auth-footer">
          <Link to="/login">
            <ArrowLeftOutlined /> 返回登录
          </Link>
        </div>
      </div>
    </div>
  )
}

export default ForgotPasswordPage
