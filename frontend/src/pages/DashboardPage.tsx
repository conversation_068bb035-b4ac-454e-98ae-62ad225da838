import React from 'react'
import { Layout, Card, Button, Avatar, Typography, Space, Divider } from 'antd'
import { UserOutlined, LogoutOutlined, SafetyOutlined, SettingOutlined } from '@ant-design/icons'
import { useAuthStore } from '../stores/authStore'

const { Header, Content } = Layout
const { Title, Text } = Typography

const DashboardPage: React.FC = () => {
  const { user, logout } = useAuthStore()

  const handleLogout = () => {
    logout()
  }

  return (
    <Layout style={{ minHeight: '100vh', background: '#f5f5f5' }}>
      <Header style={{ 
        background: 'white', 
        padding: '0 24px',
        boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between'
      }}>
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <div style={{
            width: 32,
            height: 32,
            background: '#1890ff',
            borderRadius: '50%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            color: 'white',
            fontWeight: 'bold',
            marginRight: 12
          }}>
            IdP
          </div>
          <Title level={4} style={{ margin: 0, color: '#262626' }}>
            身份提供商
          </Title>
        </div>
        
        <Space>
          <Avatar 
            icon={<UserOutlined />} 
            src={user?.avatar}
            style={{ backgroundColor: '#1890ff' }}
          />
          <Text strong>{user?.nickname || user?.firstName || user?.email}</Text>
          <Button 
            type="text" 
            icon={<LogoutOutlined />}
            onClick={handleLogout}
          >
            退出
          </Button>
        </Space>
      </Header>

      <Content style={{ padding: '24px', maxWidth: 1200, margin: '0 auto', width: '100%' }}>
        <div style={{ marginBottom: 24 }}>
          <Title level={2}>欢迎回来！</Title>
          <Text type="secondary">管理您的身份认证设置和安全选项</Text>
        </div>

        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: 24 }}>
          {/* 用户信息卡片 */}
          <Card 
            title={
              <Space>
                <UserOutlined />
                用户信息
              </Space>
            }
            extra={<Button type="link" icon={<SettingOutlined />}>编辑</Button>}
          >
            <Space direction="vertical" style={{ width: '100%' }}>
              <div>
                <Text type="secondary">邮箱地址</Text>
                <br />
                <Text strong>{user?.email}</Text>
                {user?.isEmailVerified && (
                  <Text type="success" style={{ marginLeft: 8 }}>已验证</Text>
                )}
              </div>
              
              {user?.nickname && (
                <div>
                  <Text type="secondary">昵称</Text>
                  <br />
                  <Text strong>{user.nickname}</Text>
                </div>
              )}
              
              {(user?.firstName || user?.lastName) && (
                <div>
                  <Text type="secondary">姓名</Text>
                  <br />
                  <Text strong>{`${user.firstName || ''} ${user.lastName || ''}`.trim()}</Text>
                </div>
              )}
              
              <div>
                <Text type="secondary">用户角色</Text>
                <br />
                <Space wrap>
                  {user?.roles?.map(role => (
                    <Text key={role} code>{role}</Text>
                  ))}
                </Space>
              </div>
            </Space>
          </Card>

          {/* 安全设置卡片 */}
          <Card 
            title={
              <Space>
                <SafetyOutlined />
                安全设置
              </Space>
            }
          >
            <Space direction="vertical" style={{ width: '100%' }}>
              <div>
                <Text type="secondary">多因素认证</Text>
                <br />
                <Space>
                  <Text strong>
                    {user?.mfaEnabled ? '已启用' : '未启用'}
                  </Text>
                  {user?.mfaEnabled ? (
                    <Text type="success">✓</Text>
                  ) : (
                    <Text type="warning">!</Text>
                  )}
                </Space>
              </div>
              
              <Divider style={{ margin: '12px 0' }} />
              
              <Space direction="vertical" style={{ width: '100%' }}>
                <Button type="primary" ghost block>
                  {user?.mfaEnabled ? '管理MFA设备' : '启用多因素认证'}
                </Button>
                <Button block>修改密码</Button>
                <Button block>查看登录历史</Button>
              </Space>
            </Space>
          </Card>

          {/* 应用管理卡片 */}
          <Card 
            title="应用管理"
            extra={<Button type="link">查看全部</Button>}
          >
            <Space direction="vertical" style={{ width: '100%' }}>
              <Text type="secondary">已授权的应用</Text>
              <div style={{ 
                padding: 16, 
                background: '#fafafa', 
                borderRadius: 6,
                textAlign: 'center' 
              }}>
                <Text type="secondary">暂无已授权的应用</Text>
              </div>
              <Button type="dashed" block>
                管理应用授权
              </Button>
            </Space>
          </Card>

          {/* 会话管理卡片 */}
          <Card title="活跃会话">
            <Space direction="vertical" style={{ width: '100%' }}>
              <div>
                <Text type="secondary">当前会话</Text>
                <br />
                <Text strong>此设备 (当前)</Text>
                <br />
                <Text type="secondary" style={{ fontSize: 12 }}>
                  最后活跃: 刚刚
                </Text>
              </div>
              
              <Divider style={{ margin: '12px 0' }} />
              
              <Button type="dashed" block>
                查看所有会话
              </Button>
            </Space>
          </Card>
        </div>
      </Content>
    </Layout>
  )
}

export default DashboardPage
