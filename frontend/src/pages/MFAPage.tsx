import React, { useState, useEffect } from 'react'
import { useNavigate, useLocation } from 'react-router-dom'
import { Form, Input, Button, Alert, Tabs, Card, message, Space, Typography } from 'antd'
import { SafetyOutlined, MailOutlined, MobileOutlined, QrcodeOutlined } from '@ant-design/icons'
import { QRCodeSVG } from 'qrcode.react'
import { useAuthStore } from '../stores/authStore'
import { mfaApi } from '../services/api'

const { Title, Text, Paragraph } = Typography
const { TabPane } = Tabs

interface MFASetupData {
  secret?: string
  qrCodeUri?: string
  backupCodes?: string[]
  deviceId?: string
}

const MFAPage: React.FC = () => {
  const [form] = Form.useForm()
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [mfaStatus, setMfaStatus] = useState<any>(null)
  const [setupData, setSetupData] = useState<MFASetupData | null>(null)
  const [activeTab, setActiveTab] = useState('totp')
  const [isSetupMode, setIsSetupMode] = useState(false)
  
  const navigate = useNavigate()
  const location = useLocation()
  const { mfaRequired, mfaToken, setMfaRequired, setTokens } = useAuthStore()
  
  // 获取重定向路径
  const from = (location.state as any)?.from?.pathname || '/dashboard'

  // 加载MFA状态
  useEffect(() => {
    loadMfaStatus()
  }, [])

  const loadMfaStatus = async () => {
    try {
      const status = await mfaApi.getMfaStatus()
      setMfaStatus(status)
      
      // 如果没有启用任何MFA方法，进入设置模式
      if (!status.enabled || status.devices.length === 0) {
        setIsSetupMode(true)
      }
    } catch (err: any) {
      console.error('加载MFA状态失败:', err)
      if (!mfaRequired) {
        setIsSetupMode(true)
      }
    }
  }

  // 启用MFA方法
  const handleEnableMfa = async (method: string, data: any) => {
    setLoading(true)
    setError(null)

    try {
      const result = await mfaApi.enableMfa(method, data)
      setSetupData(result)
      
      if (method === 'totp') {
        message.success('TOTP已设置，请使用认证器应用扫描二维码')
      } else {
        message.success(`${method === 'email' ? '邮件' : '短信'}MFA已启用`)
      }
    } catch (err: any) {
      setError(err.message || `启用${method}失败`)
    } finally {
      setLoading(false)
    }
  }

  // 验证MFA
  const handleVerifyMfa = async (values: any) => {
    setLoading(true)
    setError(null)

    try {
      const result = await mfaApi.verifyMfa(
        activeTab,
        values.code,
        setupData?.deviceId
      )

      if (result.success) {
        message.success('MFA验证成功')
        
        if (result.accessToken && result.refreshToken) {
          setTokens(result.accessToken, result.refreshToken)
        }
        
        setMfaRequired(false)
        navigate(from, { replace: true })
      }
    } catch (err: any) {
      setError(err.message || 'MFA验证失败')
    } finally {
      setLoading(false)
    }
  }

  // 渲染TOTP设置
  const renderTotpSetup = () => (
    <div className="mfa-container">
      {!setupData ? (
        <div>
          <Paragraph>
            使用认证器应用（如Google Authenticator、Authy等）来生成验证码
          </Paragraph>
          <Button
            type="primary"
            icon={<QrcodeOutlined />}
            onClick={() => handleEnableMfa('totp', { name: '我的认证器' })}
            loading={loading}
            block
          >
            设置TOTP认证器
          </Button>
        </div>
      ) : (
        <div>
          <Title level={4}>扫描二维码</Title>
          <div className="qr-code-container">
            <QRCodeSVG value={setupData.qrCodeUri || ''} size={200} />
          </div>
          
          <Paragraph>
            1. 在您的认证器应用中扫描上方二维码<br/>
            2. 输入认证器显示的6位数字验证码
          </Paragraph>

          {setupData.backupCodes && (
            <div className="backup-codes">
              <div className="backup-codes-title">备用恢复码</div>
              <div className="backup-codes-list">
                {setupData.backupCodes.map((code, index) => (
                  <div key={index}>{code}</div>
                ))}
              </div>
              <Text type="secondary" style={{ fontSize: 12 }}>
                请将这些恢复码保存在安全的地方，当您无法使用认证器时可以使用它们
              </Text>
            </div>
          )}

          <Form form={form} onFinish={handleVerifyMfa} style={{ marginTop: 16 }}>
            <Form.Item
              name="code"
              rules={[
                { required: true, message: '请输入验证码' },
                { pattern: /^\d{6}$/, message: '验证码必须是6位数字' },
              ]}
            >
              <Input
                placeholder="输入6位验证码"
                maxLength={6}
                style={{ textAlign: 'center', fontSize: 18, letterSpacing: 4 }}
              />
            </Form.Item>
            <Form.Item>
              <Button type="primary" htmlType="submit" loading={loading} block>
                验证并完成设置
              </Button>
            </Form.Item>
          </Form>
        </div>
      )}
    </div>
  )

  // 渲染邮件MFA设置
  const renderEmailSetup = () => (
    <div className="mfa-container">
      {!setupData ? (
        <div>
          <Paragraph>
            我们将向您的注册邮箱发送验证码
          </Paragraph>
          <Button
            type="primary"
            icon={<MailOutlined />}
            onClick={() => handleEnableMfa('email', { name: '邮件验证' })}
            loading={loading}
            block
          >
            启用邮件验证
          </Button>
        </div>
      ) : (
        <div>
          <Alert
            message="验证码已发送"
            description="请检查您的邮箱，输入收到的6位验证码"
            type="info"
            showIcon
            style={{ marginBottom: 16 }}
          />
          
          <Form form={form} onFinish={handleVerifyMfa}>
            <Form.Item
              name="code"
              rules={[
                { required: true, message: '请输入验证码' },
                { pattern: /^\d{6}$/, message: '验证码必须是6位数字' },
              ]}
            >
              <Input
                placeholder="输入6位验证码"
                maxLength={6}
                style={{ textAlign: 'center', fontSize: 18, letterSpacing: 4 }}
              />
            </Form.Item>
            <Form.Item>
              <Button type="primary" htmlType="submit" loading={loading} block>
                验证
              </Button>
            </Form.Item>
          </Form>
        </div>
      )}
    </div>
  )

  // 渲染短信MFA设置
  const renderSmsSetup = () => (
    <div className="mfa-container">
      {!setupData ? (
        <Form
          onFinish={(values) => handleEnableMfa('sms', { 
            name: '短信验证',
            phoneNumber: values.phoneNumber 
          })}
        >
          <Form.Item
            name="phoneNumber"
            rules={[
              { required: true, message: '请输入手机号码' },
              { pattern: /^1[3-9]\d{9}$/, message: '请输入有效的手机号码' },
            ]}
          >
            <Input
              placeholder="输入手机号码"
              prefix="+86"
            />
          </Form.Item>
          <Form.Item>
            <Button
              type="primary"
              htmlType="submit"
              icon={<MobileOutlined />}
              loading={loading}
              block
            >
              发送验证码
            </Button>
          </Form.Item>
        </Form>
      ) : (
        <div>
          <Alert
            message="验证码已发送"
            description="请检查您的手机短信，输入收到的6位验证码"
            type="info"
            showIcon
            style={{ marginBottom: 16 }}
          />
          
          <Form form={form} onFinish={handleVerifyMfa}>
            <Form.Item
              name="code"
              rules={[
                { required: true, message: '请输入验证码' },
                { pattern: /^\d{6}$/, message: '验证码必须是6位数字' },
              ]}
            >
              <Input
                placeholder="输入6位验证码"
                maxLength={6}
                style={{ textAlign: 'center', fontSize: 18, letterSpacing: 4 }}
              />
            </Form.Item>
            <Form.Item>
              <Button type="primary" htmlType="submit" loading={loading} block>
                验证
              </Button>
            </Form.Item>
          </Form>
        </div>
      )}
    </div>
  )

  return (
    <div className="auth-container">
      <div className="auth-card" style={{ maxWidth: 500 }}>
        <div className="auth-header">
          <div className="auth-logo">
            <SafetyOutlined />
          </div>
          <h1 className="auth-title">
            {isSetupMode ? '设置多因素认证' : '多因素认证'}
          </h1>
          <p className="auth-subtitle">
            {isSetupMode 
              ? '为您的账户添加额外的安全保护' 
              : '请完成身份验证以继续'
            }
          </p>
        </div>

        <div className="auth-form">
          {error && (
            <Alert
              message={error}
              type="error"
              showIcon
              closable
              onClose={() => setError(null)}
              style={{ marginBottom: 16 }}
            />
          )}

          <Tabs activeKey={activeTab} onChange={setActiveTab}>
            <TabPane tab="认证器应用" key="totp">
              {renderTotpSetup()}
            </TabPane>
            <TabPane tab="邮件验证" key="email">
              {renderEmailSetup()}
            </TabPane>
            <TabPane tab="短信验证" key="sms">
              {renderSmsSetup()}
            </TabPane>
          </Tabs>
        </div>
      </div>
    </div>
  )
}

export default MFAPage
