/**
 * 国际化功能演示页面
 * 
 * 功能说明：
 * 1. 展示多语言切换功能
 * 2. 演示本地化格式化功能
 * 3. 展示RTL语言支持
 * 4. 演示翻译插值和复数形式
 * 5. 展示日期、数字、货币格式化
 */

import React, { useState } from 'react';
import {
  Card,
  Row,
  Col,
  Space,
  Typography,
  Divider,
  Table,
  Tag,
  Button,
  DatePicker,
  InputNumber,
  Select,
  Alert,
  Statistic,
  Timeline,
  Badge
} from 'antd';
import {
  GlobalOutlined,
  CalendarOutlined,
  DollarOutlined,
  ClockCircleOutlined,
  UserOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import LanguageSwitcher from '../components/LanguageSwitcher';
import { useI18n, useFormatters } from '../hooks/useI18n';
import dayjs from 'dayjs';

const { Title, Text, Paragraph } = Typography;
const { Option } = Select;

/**
 * 国际化演示页面
 */
const I18nDemo: React.FC = () => {
  const { t } = useTranslation();
  const { currentLanguage, isRTL, languageInfo } = useI18n();
  const { date, number, currency, relativeTime } = useFormatters();
  
  const [selectedDate, setSelectedDate] = useState(dayjs());
  const [amount, setAmount] = useState(1234.56);
  const [count, setCount] = useState(5);

  // 示例数据
  const sampleData = [
    {
      key: '1',
      name: t('user.full_name'),
      email: '<EMAIL>',
      status: 'active',
      lastLogin: new Date('2024-01-15T10:30:00'),
      loginCount: 42
    },
    {
      key: '2',
      name: t('admin.user_management'),
      email: '<EMAIL>',
      status: 'inactive',
      lastLogin: new Date('2024-01-10T15:45:00'),
      loginCount: 128
    }
  ];

  // 表格列定义
  const columns = [
    {
      title: t('user.full_name'),
      dataIndex: 'name',
      key: 'name'
    },
    {
      title: t('user.email'),
      dataIndex: 'email',
      key: 'email'
    },
    {
      title: t('common.status'),
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Tag color={status === 'active' ? 'green' : 'red'}>
          {t(`common.${status}`)}
        </Tag>
      )
    },
    {
      title: t('user.last_login'),
      dataIndex: 'lastLogin',
      key: 'lastLogin',
      render: (date: Date) => relativeTime(date)
    },
    {
      title: t('security.login_history'),
      dataIndex: 'loginCount',
      key: 'loginCount',
      render: (count: number) => number(count)
    }
  ];

  return (
    <div style={{ padding: '24px', direction: isRTL ? 'rtl' : 'ltr' }}>
      {/* 页面标题 */}
      <Row gutter={[16, 16]}>
        <Col span={24}>
          <Card>
            <Space align="center" size="large">
              <GlobalOutlined style={{ fontSize: '32px', color: '#1890ff' }} />
              <div>
                <Title level={2} style={{ margin: 0 }}>
                  {t('i18n.localization')} {t('common.demo')}
                </Title>
                <Text type="secondary">
                  {t('i18n.current_language')}: {languageInfo.nativeName} {languageInfo.flag}
                </Text>
              </div>
              <LanguageSwitcher mode="dropdown" showFlag showName />
            </Space>
          </Card>
        </Col>
      </Row>

      {/* 语言信息卡片 */}
      <Row gutter={[16, 16]} style={{ marginTop: '16px' }}>
        <Col xs={24} md={12}>
          <Card title={t('i18n.language_settings')} extra={<GlobalOutlined />}>
            <Space direction="vertical" style={{ width: '100%' }}>
              <div>
                <Text strong>{t('i18n.current_language')}: </Text>
                <Tag color="blue">{currentLanguage}</Tag>
              </div>
              <div>
                <Text strong>{t('navigation.name')}: </Text>
                <Text>{languageInfo.name}</Text>
              </div>
              <div>
                <Text strong>{t('i18n.localization')}: </Text>
                <Text>{languageInfo.nativeName}</Text>
              </div>
              <div>
                <Text strong>{t('common.direction')}: </Text>
                <Tag color={isRTL ? 'orange' : 'green'}>
                  {isRTL ? 'RTL' : 'LTR'}
                </Tag>
              </div>
            </Space>
          </Card>
        </Col>

        <Col xs={24} md={12}>
          <Card title={t('i18n.date_format')} extra={<CalendarOutlined />}>
            <Space direction="vertical" style={{ width: '100%' }}>
              <div>
                <Text strong>{t('time.now')}: </Text>
                <Text>{date(new Date())}</Text>
              </div>
              <div>
                <Text strong>{t('i18n.time_format')}: </Text>
                <Text>{date(new Date(), { 
                  hour: '2-digit', 
                  minute: '2-digit',
                  second: '2-digit'
                })}</Text>
              </div>
              <div>
                <Text strong>{t('common.custom')}: </Text>
                <DatePicker 
                  value={selectedDate}
                  onChange={(date) => setSelectedDate(date || dayjs())}
                  style={{ width: '100%' }}
                />
              </div>
              <div>
                <Text strong>{t('common.selected')}: </Text>
                <Text>{date(selectedDate.toDate(), { 
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric'
                })}</Text>
              </div>
            </Space>
          </Card>
        </Col>
      </Row>

      {/* 数字和货币格式化 */}
      <Row gutter={[16, 16]} style={{ marginTop: '16px' }}>
        <Col xs={24} md={12}>
          <Card title={t('i18n.number_format')} extra={<DollarOutlined />}>
            <Space direction="vertical" style={{ width: '100%' }}>
              <div>
                <Text strong>{t('common.amount')}: </Text>
                <InputNumber
                  value={amount}
                  onChange={(value) => setAmount(value || 0)}
                  style={{ width: '100%' }}
                  precision={2}
                />
              </div>
              <Divider />
              <div>
                <Text strong>{t('i18n.number_format')}: </Text>
                <Text>{number(amount)}</Text>
              </div>
              <div>
                <Text strong>{t('i18n.currency_format')} (CNY): </Text>
                <Text>{currency(amount, 'CNY')}</Text>
              </div>
              <div>
                <Text strong>{t('i18n.currency_format')} (USD): </Text>
                <Text>{currency(amount, 'USD')}</Text>
              </div>
              <div>
                <Text strong>{t('i18n.currency_format')} (JPY): </Text>
                <Text>{currency(amount, 'JPY')}</Text>
              </div>
            </Space>
          </Card>
        </Col>

        <Col xs={24} md={12}>
          <Card title={t('common.statistics')} extra={<UserOutlined />}>
            <Row gutter={16}>
              <Col span={12}>
                <Statistic
                  title={t('navigation.users')}
                  value={count}
                  formatter={(value) => number(Number(value))}
                />
              </Col>
              <Col span={12}>
                <Statistic
                  title={t('security.active_sessions')}
                  value={count * 3}
                  formatter={(value) => number(Number(value))}
                />
              </Col>
            </Row>
            <Divider />
            <Space>
              <Text strong>{t('common.count')}: </Text>
              <InputNumber
                min={0}
                max={100}
                value={count}
                onChange={(value) => setCount(value || 0)}
              />
            </Space>
          </Card>
        </Col>
      </Row>

      {/* 翻译插值演示 */}
      <Row gutter={[16, 16]} style={{ marginTop: '16px' }}>
        <Col span={24}>
          <Card title={t('common.examples')}>
            <Alert
              message={t('validation.password_too_short', { min: 8 })}
              type="warning"
              style={{ marginBottom: '16px' }}
            />
            <Alert
              message={t('validation.min_length', { min: 3 })}
              type="info"
              style={{ marginBottom: '16px' }}
            />
            <Alert
              message={t('time.minutes_ago', { count: 5 })}
              type="success"
            />
          </Card>
        </Col>
      </Row>

      {/* 数据表格演示 */}
      <Row gutter={[16, 16]} style={{ marginTop: '16px' }}>
        <Col span={24}>
          <Card title={t('admin.user_management')}>
            <Table
              columns={columns}
              dataSource={sampleData}
              pagination={{
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) => 
                  `${range[0]}-${range[1]} ${t('common.of')} ${total} ${t('common.items')}`
              }}
            />
          </Card>
        </Col>
      </Row>

      {/* 时间线演示 */}
      <Row gutter={[16, 16]} style={{ marginTop: '16px' }}>
        <Col span={24}>
          <Card title={t('security.login_history')} extra={<ClockCircleOutlined />}>
            <Timeline>
              <Timeline.Item color="green">
                <Text strong>{relativeTime(new Date(Date.now() - 1000 * 60 * 5))}</Text>
                <br />
                <Text type="secondary">{t('auth.login_success')}</Text>
              </Timeline.Item>
              <Timeline.Item color="blue">
                <Text strong>{relativeTime(new Date(Date.now() - 1000 * 60 * 60 * 2))}</Text>
                <br />
                <Text type="secondary">{t('user.profile')} {t('common.updated')}</Text>
              </Timeline.Item>
              <Timeline.Item color="orange">
                <Text strong>{relativeTime(new Date(Date.now() - 1000 * 60 * 60 * 24))}</Text>
                <br />
                <Text type="secondary">{t('auth.password_changed')}</Text>
              </Timeline.Item>
            </Timeline>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default I18nDemo;
