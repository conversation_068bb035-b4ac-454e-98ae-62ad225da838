import React, { useState, useEffect } from 'react'
import { Link, useSearchParams, useNavigate } from 'react-router-dom'
import { Form, Input, Button, Alert, Result, Progress } from 'antd'
import { LockOutlined, CheckCircleOutlined } from '@ant-design/icons'
import { authApi } from '../services/api'

// 密码强度检查（复用注册页面的逻辑）
const checkPasswordStrength = (password: string): { score: number; feedback: string[] } => {
  const feedback: string[] = []
  let score = 0

  if (password.length >= 8) {
    score += 20
  } else {
    feedback.push('密码长度至少8个字符')
  }

  if (/[a-z]/.test(password)) {
    score += 20
  } else {
    feedback.push('包含小写字母')
  }

  if (/[A-Z]/.test(password)) {
    score += 20
  } else {
    feedback.push('包含大写字母')
  }

  if (/\d/.test(password)) {
    score += 20
  } else {
    feedback.push('包含数字')
  }

  if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
    score += 20
  } else {
    feedback.push('包含特殊字符')
  }

  return { score, feedback }
}

const ResetPasswordPage: React.FC = () => {
  const [form] = Form.useForm()
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState(false)
  const [passwordStrength, setPasswordStrength] = useState({ score: 0, feedback: [] as string[] })
  const [searchParams] = useSearchParams()
  const navigate = useNavigate()

  const token = searchParams.get('token')

  useEffect(() => {
    if (!token) {
      setError('重置链接无效或已过期')
    }
  }, [token])

  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const password = e.target.value
    const strength = checkPasswordStrength(password)
    setPasswordStrength(strength)
  }

  const getPasswordStrengthColor = (score: number): string => {
    if (score < 40) return '#ff4d4f'
    if (score < 80) return '#faad14'
    return '#52c41a'
  }

  const getPasswordStrengthText = (score: number): string => {
    if (score < 40) return '弱'
    if (score < 80) return '中等'
    return '强'
  }

  const handleSubmit = async (values: { password: string }) => {
    if (!token) {
      setError('重置链接无效')
      return
    }

    setLoading(true)
    setError(null)

    try {
      await authApi.resetPassword(token, values.password)
      setSuccess(true)
    } catch (err: any) {
      setError(err.message || '重置密码失败')
    } finally {
      setLoading(false)
    }
  }

  if (success) {
    return (
      <div className="auth-container">
        <div className="auth-card">
          <Result
            icon={<CheckCircleOutlined style={{ color: '#52c41a' }} />}
            status="success"
            title="密码重置成功"
            subTitle="您的密码已成功重置，现在可以使用新密码登录。"
            extra={[
              <Button type="primary" key="login" onClick={() => navigate('/login')}>
                立即登录
              </Button>,
            ]}
          />
        </div>
      </div>
    )
  }

  return (
    <div className="auth-container">
      <div className="auth-card">
        <div className="auth-header">
          <div className="auth-logo">
            <LockOutlined />
          </div>
          <h1 className="auth-title">重置密码</h1>
          <p className="auth-subtitle">请输入您的新密码</p>
        </div>

        <div className="auth-form">
          {error && (
            <Alert
              message={error}
              type="error"
              showIcon
              closable
              onClose={() => setError(null)}
              style={{ marginBottom: 16 }}
            />
          )}

          {token && (
            <Form
              form={form}
              name="reset-password"
              onFinish={handleSubmit}
              autoComplete="off"
              size="large"
            >
              <Form.Item
                name="password"
                rules={[
                  { required: true, message: '请输入新密码' },
                  { min: 8, message: '密码至少8个字符' },
                  {
                    validator: (_, value) => {
                      if (!value) return Promise.resolve()
                      const { score } = checkPasswordStrength(value)
                      if (score < 60) {
                        return Promise.reject(new Error('密码强度不够，请使用更复杂的密码'))
                      }
                      return Promise.resolve()
                    },
                  },
                ]}
              >
                <Input.Password
                  prefix={<LockOutlined />}
                  placeholder="新密码"
                  autoComplete="new-password"
                  onChange={handlePasswordChange}
                />
              </Form.Item>

              {/* 密码强度指示器 */}
              {passwordStrength.score > 0 && (
                <div style={{ marginBottom: 16 }}>
                  <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 4 }}>
                    <span style={{ fontSize: 12, color: '#8c8c8c' }}>密码强度</span>
                    <span style={{ 
                      fontSize: 12, 
                      color: getPasswordStrengthColor(passwordStrength.score),
                      fontWeight: 500 
                    }}>
                      {getPasswordStrengthText(passwordStrength.score)}
                    </span>
                  </div>
                  <Progress
                    percent={passwordStrength.score}
                    strokeColor={getPasswordStrengthColor(passwordStrength.score)}
                    showInfo={false}
                    size="small"
                  />
                  {passwordStrength.feedback.length > 0 && (
                    <div style={{ fontSize: 12, color: '#8c8c8c', marginTop: 4 }}>
                      建议: {passwordStrength.feedback.join('、')}
                    </div>
                  )}
                </div>
              )}

              <Form.Item
                name="confirmPassword"
                dependencies={['password']}
                rules={[
                  { required: true, message: '请确认新密码' },
                  ({ getFieldValue }) => ({
                    validator(_, value) {
                      if (!value || getFieldValue('password') === value) {
                        return Promise.resolve()
                      }
                      return Promise.reject(new Error('两次输入的密码不一致'))
                    },
                  }),
                ]}
              >
                <Input.Password
                  prefix={<LockOutlined />}
                  placeholder="确认新密码"
                  autoComplete="new-password"
                />
              </Form.Item>

              <Form.Item>
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={loading}
                  block
                >
                  {loading ? '重置中...' : '重置密码'}
                </Button>
              </Form.Item>
            </Form>
          )}
        </div>

        <div className="auth-footer">
          <Link to="/login">返回登录</Link>
        </div>
      </div>
    </div>
  )
}

export default ResetPasswordPage
