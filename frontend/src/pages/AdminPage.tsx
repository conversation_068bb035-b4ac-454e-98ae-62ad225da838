/**
 * 管理员控制台主页面
 * 提供系统管理、用户管理、OAuth客户端管理等功能
 */

import React, { useState, useEffect } from 'react'
import {
  Layout,
  Menu,
  Card,
  Row,
  Col,
  Statistic,
  Table,
  Button,
  Space,
  Typography,
  Tabs,
  Badge,
  Avatar,
  Tag,
  Tooltip,
  Modal,
  Form,
  Input,
  Select,
  Switch,
  message,
  Spin
} from 'antd'
import {
  DashboardOutlined,
  UserOutlined,
  SettingOutlined,
  ApiOutlined,
  MonitorOutlined,
  SecurityScanOutlined,
  TeamOutlined,
  AppstoreOutlined,
  BarChartOutlined,
  BellOutlined,
  ReloadOutlined,
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined
} from '@ant-design/icons'
import { useAuthStore } from '../stores/authStore'
import { adminApi } from '../services/adminApi'
import MonitoringDashboard from '../components/MonitoringDashboard'

const { Header, Sider, Content } = Layout
const { Title, Text } = Typography
const { TabPane } = Tabs

interface SystemStats {
  totalUsers: number
  activeUsers: number
  totalSessions: number
  oauthClients: number
  apiRequests24h: number
  errorRate: number
}

interface User {
  id: string
  email: string
  nickname?: string
  firstName?: string
  lastName?: string
  isActive: boolean
  isEmailVerified: boolean
  lastLoginAt?: string
  createdAt: string
  roles: string[]
}

interface OAuthClient {
  id: string
  clientId: string
  name: string
  description?: string
  redirectUris: string[]
  grantTypes: string[]
  scopes: string[]
  isActive: boolean
  createdAt: string
}

const AdminPage: React.FC = () => {
  const { user } = useAuthStore()
  const [collapsed, setCollapsed] = useState(false)
  const [selectedMenu, setSelectedMenu] = useState('dashboard')
  const [loading, setLoading] = useState(false)
  const [stats, setStats] = useState<SystemStats | null>(null)
  const [users, setUsers] = useState<User[]>([])
  const [oauthClients, setOauthClients] = useState<OAuthClient[]>([])
  const [modalVisible, setModalVisible] = useState(false)
  const [modalType, setModalType] = useState<'user' | 'client'>('user')
  const [editingItem, setEditingItem] = useState<any>(null)
  const [form] = Form.useForm()

  // 检查管理员权限
  const isAdmin = user?.roles?.includes('admin') || user?.roles?.includes('super_admin')

  useEffect(() => {
    if (!isAdmin) {
      message.error('您没有管理员权限')
      return
    }
    loadDashboardData()
  }, [isAdmin])

  const loadDashboardData = async () => {
    setLoading(true)
    try {
      const [statsData, usersData, clientsData] = await Promise.all([
        adminApi.getSystemStats(),
        adminApi.getUsers(),
        adminApi.getOAuthClients()
      ])
      
      setStats(statsData)
      setUsers(usersData)
      setOauthClients(clientsData)
    } catch (error) {
      message.error('加载数据失败')
      console.error('加载管理员数据失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleMenuClick = (key: string) => {
    setSelectedMenu(key)
  }

  const handleCreateUser = () => {
    setModalType('user')
    setEditingItem(null)
    form.resetFields()
    setModalVisible(true)
  }

  const handleCreateClient = () => {
    setModalType('client')
    setEditingItem(null)
    form.resetFields()
    setModalVisible(true)
  }

  const handleEdit = (item: any, type: 'user' | 'client') => {
    setModalType(type)
    setEditingItem(item)
    form.setFieldsValue(item)
    setModalVisible(true)
  }

  const handleDelete = async (id: string, type: 'user' | 'client') => {
    Modal.confirm({
      title: `确认删除${type === 'user' ? '用户' : '客户端'}？`,
      content: '此操作不可恢复',
      onOk: async () => {
        try {
          if (type === 'user') {
            await adminApi.deleteUser(id)
            setUsers(users.filter(u => u.id !== id))
          } else {
            await adminApi.deleteOAuthClient(id)
            setOauthClients(oauthClients.filter(c => c.id !== id))
          }
          message.success('删除成功')
        } catch (error) {
          message.error('删除失败')
        }
      }
    })
  }

  const handleModalOk = async () => {
    try {
      const values = await form.validateFields()
      
      if (modalType === 'user') {
        if (editingItem) {
          await adminApi.updateUser(editingItem.id, values)
          setUsers(users.map(u => u.id === editingItem.id ? { ...u, ...values } : u))
        } else {
          const newUser = await adminApi.createUser(values)
          setUsers([...users, newUser])
        }
      } else {
        if (editingItem) {
          await adminApi.updateOAuthClient(editingItem.id, values)
          setOauthClients(oauthClients.map(c => c.id === editingItem.id ? { ...c, ...values } : c))
        } else {
          const newClient = await adminApi.createOAuthClient(values)
          setOauthClients([...oauthClients, newClient])
        }
      }
      
      setModalVisible(false)
      message.success(`${editingItem ? '更新' : '创建'}成功`)
    } catch (error) {
      message.error(`${editingItem ? '更新' : '创建'}失败`)
    }
  }

  if (!isAdmin) {
    return (
      <div style={{ 
        height: '100vh', 
        display: 'flex', 
        alignItems: 'center', 
        justifyContent: 'center' 
      }}>
        <Card>
          <Title level={3}>访问被拒绝</Title>
          <Text>您没有管理员权限访问此页面</Text>
        </Card>
      </div>
    )
  }

  const menuItems = [
    {
      key: 'dashboard',
      icon: <DashboardOutlined />,
      label: '仪表板'
    },
    {
      key: 'users',
      icon: <TeamOutlined />,
      label: '用户管理'
    },
    {
      key: 'oauth',
      icon: <ApiOutlined />,
      label: 'OAuth客户端'
    },
    {
      key: 'monitoring',
      icon: <MonitorOutlined />,
      label: '系统监控'
    },
    {
      key: 'security',
      icon: <SecurityScanOutlined />,
      label: '安全审计'
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: '系统设置'
    }
  ]

  const userColumns = [
    {
      title: '用户',
      dataIndex: 'email',
      key: 'email',
      render: (email: string, record: User) => (
        <Space>
          <Avatar icon={<UserOutlined />} />
          <div>
            <div>{record.nickname || `${record.firstName} ${record.lastName}`.trim() || email}</div>
            <Text type="secondary" style={{ fontSize: 12 }}>{email}</Text>
          </div>
        </Space>
      )
    },
    {
      title: '状态',
      key: 'status',
      render: (record: User) => (
        <Space>
          <Badge 
            status={record.isActive ? 'success' : 'error'} 
            text={record.isActive ? '活跃' : '禁用'} 
          />
          {record.isEmailVerified && <Tag color="green">已验证</Tag>}
        </Space>
      )
    },
    {
      title: '角色',
      dataIndex: 'roles',
      key: 'roles',
      render: (roles: string[]) => (
        <Space>
          {roles.map(role => (
            <Tag key={role} color="blue">{role}</Tag>
          ))}
        </Space>
      )
    },
    {
      title: '最后登录',
      dataIndex: 'lastLoginAt',
      key: 'lastLoginAt',
      render: (date: string) => date ? new Date(date).toLocaleString() : '从未登录'
    },
    {
      title: '操作',
      key: 'actions',
      render: (record: User) => (
        <Space>
          <Tooltip title="查看详情">
            <Button icon={<EyeOutlined />} size="small" />
          </Tooltip>
          <Tooltip title="编辑">
            <Button 
              icon={<EditOutlined />} 
              size="small" 
              onClick={() => handleEdit(record, 'user')}
            />
          </Tooltip>
          <Tooltip title="删除">
            <Button 
              icon={<DeleteOutlined />} 
              size="small" 
              danger 
              onClick={() => handleDelete(record.id, 'user')}
            />
          </Tooltip>
        </Space>
      )
    }
  ]

  const clientColumns = [
    {
      title: '客户端',
      dataIndex: 'name',
      key: 'name',
      render: (name: string, record: OAuthClient) => (
        <div>
          <div style={{ fontWeight: 500 }}>{name}</div>
          <Text type="secondary" style={{ fontSize: 12 }}>{record.clientId}</Text>
        </div>
      )
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true
    },
    {
      title: '授权类型',
      dataIndex: 'grantTypes',
      key: 'grantTypes',
      render: (types: string[]) => (
        <Space>
          {types.map(type => (
            <Tag key={type}>{type}</Tag>
          ))}
        </Space>
      )
    },
    {
      title: '状态',
      dataIndex: 'isActive',
      key: 'isActive',
      render: (isActive: boolean) => (
        <Badge 
          status={isActive ? 'success' : 'error'} 
          text={isActive ? '启用' : '禁用'} 
        />
      )
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (date: string) => new Date(date).toLocaleString()
    },
    {
      title: '操作',
      key: 'actions',
      render: (record: OAuthClient) => (
        <Space>
          <Tooltip title="查看详情">
            <Button icon={<EyeOutlined />} size="small" />
          </Tooltip>
          <Tooltip title="编辑">
            <Button 
              icon={<EditOutlined />} 
              size="small" 
              onClick={() => handleEdit(record, 'client')}
            />
          </Tooltip>
          <Tooltip title="删除">
            <Button 
              icon={<DeleteOutlined />} 
              size="small" 
              danger 
              onClick={() => handleDelete(record.id, 'client')}
            />
          </Tooltip>
        </Space>
      )
    }
  ]

  const renderContent = () => {
    switch (selectedMenu) {
      case 'dashboard':
        return (
          <div>
            <div style={{ marginBottom: 24, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Title level={2}>系统仪表板</Title>
              <Button icon={<ReloadOutlined />} onClick={loadDashboardData} loading={loading}>
                刷新数据
              </Button>
            </div>
            
            {stats && (
              <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
                <Col xs={24} sm={12} lg={6}>
                  <Card>
                    <Statistic
                      title="总用户数"
                      value={stats.totalUsers}
                      prefix={<UserOutlined />}
                      valueStyle={{ color: '#1890ff' }}
                    />
                  </Card>
                </Col>
                <Col xs={24} sm={12} lg={6}>
                  <Card>
                    <Statistic
                      title="活跃用户"
                      value={stats.activeUsers}
                      prefix={<TeamOutlined />}
                      valueStyle={{ color: '#52c41a' }}
                    />
                  </Card>
                </Col>
                <Col xs={24} sm={12} lg={6}>
                  <Card>
                    <Statistic
                      title="OAuth客户端"
                      value={stats.oauthClients}
                      prefix={<ApiOutlined />}
                      valueStyle={{ color: '#722ed1' }}
                    />
                  </Card>
                </Col>
                <Col xs={24} sm={12} lg={6}>
                  <Card>
                    <Statistic
                      title="24h API请求"
                      value={stats.apiRequests24h}
                      prefix={<BarChartOutlined />}
                      valueStyle={{ color: '#fa8c16' }}
                    />
                  </Card>
                </Col>
              </Row>
            )}
          </div>
        )

      case 'users':
        return (
          <div>
            <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Title level={2}>用户管理</Title>
              <Button type="primary" icon={<PlusOutlined />} onClick={handleCreateUser}>
                创建用户
              </Button>
            </div>
            <Card>
              <Table
                columns={userColumns}
                dataSource={users}
                rowKey="id"
                loading={loading}
                pagination={{ pageSize: 10 }}
              />
            </Card>
          </div>
        )

      case 'oauth':
        return (
          <div>
            <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Title level={2}>OAuth客户端管理</Title>
              <Button type="primary" icon={<PlusOutlined />} onClick={handleCreateClient}>
                创建客户端
              </Button>
            </div>
            <Card>
              <Table
                columns={clientColumns}
                dataSource={oauthClients}
                rowKey="id"
                loading={loading}
                pagination={{ pageSize: 10 }}
              />
            </Card>
          </div>
        )

      case 'monitoring':
        return <MonitoringDashboard />

      default:
        return (
          <Card>
            <Title level={3}>功能开发中</Title>
            <Text>该功能正在开发中，敬请期待...</Text>
          </Card>
        )
    }
  }

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Sider 
        collapsible 
        collapsed={collapsed} 
        onCollapse={setCollapsed}
        theme="light"
        width={250}
      >
        <div style={{ 
          height: 64, 
          display: 'flex', 
          alignItems: 'center', 
          justifyContent: 'center',
          borderBottom: '1px solid #f0f0f0'
        }}>
          <Title level={4} style={{ margin: 0, color: '#1890ff' }}>
            {collapsed ? 'Admin' : '管理控制台'}
          </Title>
        </div>
        <Menu
          mode="inline"
          selectedKeys={[selectedMenu]}
          items={menuItems}
          onClick={({ key }) => handleMenuClick(key)}
          style={{ borderRight: 0 }}
        />
      </Sider>
      
      <Layout>
        <Header style={{ 
          background: '#fff', 
          padding: '0 24px',
          borderBottom: '1px solid #f0f0f0',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between'
        }}>
          <div>
            <Text strong>欢迎，{user?.nickname || user?.email}</Text>
          </div>
          <Space>
            <Badge count={5}>
              <Button icon={<BellOutlined />} />
            </Badge>
            <Avatar icon={<UserOutlined />} />
          </Space>
        </Header>
        
        <Content style={{ margin: '24px', background: '#f0f2f5' }}>
          <Spin spinning={loading}>
            {renderContent()}
          </Spin>
        </Content>
      </Layout>

      {/* 创建/编辑模态框 */}
      <Modal
        title={`${editingItem ? '编辑' : '创建'}${modalType === 'user' ? '用户' : 'OAuth客户端'}`}
        open={modalVisible}
        onOk={handleModalOk}
        onCancel={() => setModalVisible(false)}
        width={600}
      >
        <Form form={form} layout="vertical">
          {modalType === 'user' ? (
            <>
              <Form.Item name="email" label="邮箱" rules={[{ required: true, type: 'email' }]}>
                <Input />
              </Form.Item>
              <Form.Item name="nickname" label="昵称">
                <Input />
              </Form.Item>
              <Form.Item name="firstName" label="名">
                <Input />
              </Form.Item>
              <Form.Item name="lastName" label="姓">
                <Input />
              </Form.Item>
              <Form.Item name="isActive" label="启用状态" valuePropName="checked">
                <Switch />
              </Form.Item>
              <Form.Item name="roles" label="角色">
                <Select mode="multiple" placeholder="选择角色">
                  <Select.Option value="user">用户</Select.Option>
                  <Select.Option value="admin">管理员</Select.Option>
                  <Select.Option value="super_admin">超级管理员</Select.Option>
                </Select>
              </Form.Item>
            </>
          ) : (
            <>
              <Form.Item name="name" label="客户端名称" rules={[{ required: true }]}>
                <Input />
              </Form.Item>
              <Form.Item name="description" label="描述">
                <Input.TextArea />
              </Form.Item>
              <Form.Item name="redirectUris" label="重定向URI" rules={[{ required: true }]}>
                <Select mode="tags" placeholder="输入重定向URI">
                </Select>
              </Form.Item>
              <Form.Item name="grantTypes" label="授权类型" rules={[{ required: true }]}>
                <Select mode="multiple" placeholder="选择授权类型">
                  <Select.Option value="authorization_code">授权码</Select.Option>
                  <Select.Option value="refresh_token">刷新令牌</Select.Option>
                  <Select.Option value="client_credentials">客户端凭据</Select.Option>
                </Select>
              </Form.Item>
              <Form.Item name="scopes" label="权限范围" rules={[{ required: true }]}>
                <Select mode="multiple" placeholder="选择权限范围">
                  <Select.Option value="openid">OpenID</Select.Option>
                  <Select.Option value="profile">用户资料</Select.Option>
                  <Select.Option value="email">邮箱</Select.Option>
                  <Select.Option value="offline_access">离线访问</Select.Option>
                </Select>
              </Form.Item>
              <Form.Item name="isActive" label="启用状态" valuePropName="checked">
                <Switch />
              </Form.Item>
            </>
          )}
        </Form>
      </Modal>
    </Layout>
  )
}

export default AdminPage
