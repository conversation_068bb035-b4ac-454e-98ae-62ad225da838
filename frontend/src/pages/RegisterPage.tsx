import React, { useState } from 'react'
import { Link, useNavigate } from 'react-router-dom'
import { Form, Input, Button, Alert, Progress, message } from 'antd'
import { UserOutlined, MailOutlined, LockOutlined, UserAddOutlined } from '@ant-design/icons'
import { useAuthStore } from '../stores/authStore'

interface RegisterFormData {
  email: string
  password: string
  confirmPassword: string
  firstName?: string
  lastName?: string
  nickname?: string
}

// 密码强度检查
const checkPasswordStrength = (password: string): { score: number; feedback: string[] } => {
  const feedback: string[] = []
  let score = 0

  if (password.length >= 8) {
    score += 20
  } else {
    feedback.push('密码长度至少8个字符')
  }

  if (/[a-z]/.test(password)) {
    score += 20
  } else {
    feedback.push('包含小写字母')
  }

  if (/[A-Z]/.test(password)) {
    score += 20
  } else {
    feedback.push('包含大写字母')
  }

  if (/\d/.test(password)) {
    score += 20
  } else {
    feedback.push('包含数字')
  }

  if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
    score += 20
  } else {
    feedback.push('包含特殊字符')
  }

  return { score, feedback }
}

const RegisterPage: React.FC = () => {
  const [form] = Form.useForm()
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [passwordStrength, setPasswordStrength] = useState({ score: 0, feedback: [] as string[] })
  
  const navigate = useNavigate()
  const { register } = useAuthStore()

  // 处理密码变化
  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const password = e.target.value
    const strength = checkPasswordStrength(password)
    setPasswordStrength(strength)
  }

  // 获取密码强度颜色
  const getPasswordStrengthColor = (score: number): string => {
    if (score < 40) return '#ff4d4f'
    if (score < 80) return '#faad14'
    return '#52c41a'
  }

  // 获取密码强度文本
  const getPasswordStrengthText = (score: number): string => {
    if (score < 40) return '弱'
    if (score < 80) return '中等'
    return '强'
  }

  // 处理表单提交
  const handleSubmit = async (values: RegisterFormData) => {
    setLoading(true)
    setError(null)

    try {
      await register({
        email: values.email,
        password: values.password,
        firstName: values.firstName,
        lastName: values.lastName,
        nickname: values.nickname,
      })

      message.success('注册成功！请检查您的邮箱进行验证')
      navigate('/login', { 
        state: { 
          message: '注册成功，请检查邮箱验证后登录' 
        } 
      })
    } catch (err: any) {
      setError(err.message || '注册失败，请稍后重试')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="auth-container">
      <div className="auth-card">
        {/* 头部 */}
        <div className="auth-header">
          <div className="auth-logo">
            IdP
          </div>
          <h1 className="auth-title">创建账户</h1>
          <p className="auth-subtitle">开始您的身份认证之旅</p>
        </div>

        {/* 表单 */}
        <div className="auth-form">
          {error && (
            <Alert
              message={error}
              type="error"
              showIcon
              closable
              onClose={() => setError(null)}
              style={{ marginBottom: 16 }}
            />
          )}

          <Form
            form={form}
            name="register"
            onFinish={handleSubmit}
            autoComplete="off"
            size="large"
            scrollToFirstError
          >
            <Form.Item
              name="email"
              rules={[
                { required: true, message: '请输入邮箱地址' },
                { type: 'email', message: '请输入有效的邮箱地址' },
              ]}
            >
              <Input
                prefix={<MailOutlined />}
                placeholder="邮箱地址"
                autoComplete="email"
              />
            </Form.Item>

            <Form.Item
              name="nickname"
              rules={[
                { min: 2, message: '昵称至少2个字符' },
                { max: 20, message: '昵称最多20个字符' },
              ]}
            >
              <Input
                prefix={<UserOutlined />}
                placeholder="昵称（可选）"
                autoComplete="nickname"
              />
            </Form.Item>

            <div style={{ display: 'flex', gap: 8 }}>
              <Form.Item
                name="firstName"
                style={{ flex: 1, marginBottom: 16 }}
                rules={[
                  { max: 20, message: '姓名最多20个字符' },
                ]}
              >
                <Input
                  placeholder="名字（可选）"
                  autoComplete="given-name"
                />
              </Form.Item>

              <Form.Item
                name="lastName"
                style={{ flex: 1, marginBottom: 16 }}
                rules={[
                  { max: 20, message: '姓名最多20个字符' },
                ]}
              >
                <Input
                  placeholder="姓氏（可选）"
                  autoComplete="family-name"
                />
              </Form.Item>
            </div>

            <Form.Item
              name="password"
              rules={[
                { required: true, message: '请输入密码' },
                { min: 8, message: '密码至少8个字符' },
                {
                  validator: (_, value) => {
                    if (!value) return Promise.resolve()
                    const { score } = checkPasswordStrength(value)
                    if (score < 60) {
                      return Promise.reject(new Error('密码强度不够，请使用更复杂的密码'))
                    }
                    return Promise.resolve()
                  },
                },
              ]}
            >
              <Input.Password
                prefix={<LockOutlined />}
                placeholder="密码"
                autoComplete="new-password"
                onChange={handlePasswordChange}
              />
            </Form.Item>

            {/* 密码强度指示器 */}
            {passwordStrength.score > 0 && (
              <div style={{ marginBottom: 16 }}>
                <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 4 }}>
                  <span style={{ fontSize: 12, color: '#8c8c8c' }}>密码强度</span>
                  <span style={{ 
                    fontSize: 12, 
                    color: getPasswordStrengthColor(passwordStrength.score),
                    fontWeight: 500 
                  }}>
                    {getPasswordStrengthText(passwordStrength.score)}
                  </span>
                </div>
                <Progress
                  percent={passwordStrength.score}
                  strokeColor={getPasswordStrengthColor(passwordStrength.score)}
                  showInfo={false}
                  size="small"
                />
                {passwordStrength.feedback.length > 0 && (
                  <div style={{ fontSize: 12, color: '#8c8c8c', marginTop: 4 }}>
                    建议: {passwordStrength.feedback.join('、')}
                  </div>
                )}
              </div>
            )}

            <Form.Item
              name="confirmPassword"
              dependencies={['password']}
              rules={[
                { required: true, message: '请确认密码' },
                ({ getFieldValue }) => ({
                  validator(_, value) {
                    if (!value || getFieldValue('password') === value) {
                      return Promise.resolve()
                    }
                    return Promise.reject(new Error('两次输入的密码不一致'))
                  },
                }),
              ]}
            >
              <Input.Password
                prefix={<LockOutlined />}
                placeholder="确认密码"
                autoComplete="new-password"
              />
            </Form.Item>

            <Form.Item>
              <Button
                type="primary"
                htmlType="submit"
                loading={loading}
                block
                icon={<UserAddOutlined />}
              >
                {loading ? '注册中...' : '创建账户'}
              </Button>
            </Form.Item>
          </Form>
        </div>

        {/* 底部 */}
        <div className="auth-footer">
          已有账户？ <Link to="/login">立即登录</Link>
        </div>
      </div>
    </div>
  )
}

export default RegisterPage
