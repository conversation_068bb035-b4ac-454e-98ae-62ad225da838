import React, { useState, useEffect } from 'react'
import { Link, useNavigate, useLocation, useSearchParams } from 'react-router-dom'
import { Form, Input, Button, Checkbox, Alert, Divider, message } from 'antd'
import { UserOutlined, LockOutlined, GoogleOutlined, GithubOutlined } from '@ant-design/icons'
import { useAuthStore } from '../stores/authStore'
import { oauthApi } from '../services/api'

interface LoginFormData {
  username: string
  password: string
  rememberMe: boolean
}

const LoginPage: React.FC = () => {
  const [form] = Form.useForm()
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [oauthError, setOauthError] = useState<string | null>(null)

  const navigate = useNavigate()
  const location = useLocation()
  const [searchParams] = useSearchParams()
  const { login } = useAuthStore()

  // 获取重定向路径
  const from = (location.state as any)?.from?.pathname || '/dashboard'

  // 检查URL参数中的OAuth错误信息
  useEffect(() => {
    const errorParam = searchParams.get('error')
    const messageParam = searchParams.get('message')

    if (errorParam && messageParam) {
      setOauthError(messageParam)
      // 清理URL参数
      const newUrl = window.location.pathname
      window.history.replaceState({}, '', newUrl)
    }
  }, [searchParams])

  // 处理表单提交
  const handleSubmit = async (values: LoginFormData) => {
    setLoading(true)
    setError(null)

    try {
      const result = await login({
        username: values.username,
        password: values.password,
        rememberMe: values.rememberMe,
      })

      if (result.success) {
        if (result.requiresMfa) {
          message.success('登录成功，请完成多因素认证')
          navigate('/mfa', { state: { from: location.state?.from } })
        } else {
          message.success('登录成功')
          navigate(from, { replace: true })
        }
      } else {
        setError(result.message || '登录失败')
      }
    } catch (err: any) {
      setError(err.message || '登录失败，请稍后重试')
    } finally {
      setLoading(false)
    }
  }

  // 处理 OAuth 登录
  const handleOAuthLogin = async (provider: 'google' | 'github') => {
    try {
      const authUrl = await oauthApi.getAuthUrl(provider, window.location.origin + from)
      window.location.href = authUrl
    } catch (err: any) {
      message.error(`${provider} 登录失败: ${err.message}`)
    }
  }

  return (
    <div className="auth-container">
      <div className="auth-card">
        {/* 头部 */}
        <div className="auth-header">
          <div className="auth-logo">
            IdP
          </div>
          <h1 className="auth-title">欢迎回来</h1>
          <p className="auth-subtitle">请登录您的账户</p>
        </div>

        {/* 表单 */}
        <div className="auth-form">
          {/* OAuth错误提示 */}
          {oauthError && (
            <Alert
              message="第三方登录失败"
              description={oauthError}
              type="error"
              showIcon
              closable
              onClose={() => setOauthError(null)}
              style={{ marginBottom: 16 }}
            />
          )}

          {/* 普通登录错误提示 */}
          {error && (
            <Alert
              message={error}
              type="error"
              showIcon
              closable
              onClose={() => setError(null)}
              style={{ marginBottom: 16 }}
            />
          )}

          <Form
            form={form}
            name="login"
            onFinish={handleSubmit}
            autoComplete="off"
            size="large"
          >
            <Form.Item
              name="username"
              rules={[
                { required: true, message: '请输入用户名或邮箱' },
                { min: 2, message: '用户名至少2个字符' },
              ]}
            >
              <Input
                prefix={<UserOutlined />}
                placeholder="用户名或邮箱"
                autoComplete="username"
              />
            </Form.Item>

            <Form.Item
              name="password"
              rules={[
                { required: true, message: '请输入密码' },
                { min: 6, message: '密码至少6个字符' },
              ]}
            >
              <Input.Password
                prefix={<LockOutlined />}
                placeholder="密码"
                autoComplete="current-password"
              />
            </Form.Item>

            <Form.Item>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Form.Item name="rememberMe" valuePropName="checked" noStyle>
                  <Checkbox>记住我</Checkbox>
                </Form.Item>
                <Link to="/forgot-password">忘记密码？</Link>
              </div>
            </Form.Item>

            <Form.Item>
              <Button
                type="primary"
                htmlType="submit"
                loading={loading}
                block
              >
                {loading ? '登录中...' : '登录'}
              </Button>
            </Form.Item>
          </Form>

          {/* OAuth 登录 */}
          <Divider className="auth-divider">
            <span>或使用第三方账户登录</span>
          </Divider>

          <div className="oauth-buttons">
            <div 
              className="oauth-button"
              onClick={() => handleOAuthLogin('google')}
            >
              <GoogleOutlined style={{ fontSize: 18, marginRight: 8 }} />
              Google
            </div>
            <div 
              className="oauth-button"
              onClick={() => handleOAuthLogin('github')}
            >
              <GithubOutlined style={{ fontSize: 18, marginRight: 8 }} />
              GitHub
            </div>
          </div>
        </div>

        {/* 底部 */}
        <div className="auth-footer">
          还没有账户？ <Link to="/register">立即注册</Link>
        </div>
      </div>
    </div>
  )
}

export default LoginPage
