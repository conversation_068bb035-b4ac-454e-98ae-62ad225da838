import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { authApi } from '../services/api'

// 用户信息接口
export interface User {
  id: string
  email: string
  username?: string
  firstName?: string
  lastName?: string
  nickname?: string
  isEmailVerified: boolean
  mfaEnabled: boolean
  avatar?: string
  roles: string[]
}

// 认证状态接口
interface AuthState {
  user: User | null
  accessToken: string | null
  refreshToken: string | null
  isAuthenticated: boolean
  isLoading: boolean
  mfaRequired: boolean
  mfaToken: string | null
}

// 认证操作接口
interface AuthActions {
  login: (credentials: LoginCredentials) => Promise<LoginResult>
  register: (userData: RegisterData) => Promise<void>
  logout: () => void
  refreshAccessToken: () => Promise<boolean>
  setUser: (user: User) => void
  setTokens: (accessToken: string, refreshToken: string) => void
  setMfaRequired: (required: boolean, token?: string) => void
  clearAuth: () => void
  initializeAuth: () => Promise<void>
}

// 登录凭据接口
export interface LoginCredentials {
  username: string
  password: string
  rememberMe?: boolean
}

// 登录结果接口
export interface LoginResult {
  success: boolean
  requiresMfa: boolean
  mfaToken?: string
  user?: User
  accessToken?: string
  refreshToken?: string
  message?: string
}

// 注册数据接口
export interface RegisterData {
  email: string
  password: string
  firstName?: string
  lastName?: string
  nickname?: string
}

// 创建认证状态管理
export const useAuthStore = create<AuthState & AuthActions>()(
  persist(
    (set, get) => ({
      // 初始状态
      user: null,
      accessToken: null,
      refreshToken: null,
      isAuthenticated: false,
      isLoading: true,
      mfaRequired: false,
      mfaToken: null,

      // 登录操作
      login: async (credentials: LoginCredentials): Promise<LoginResult> => {
        try {
          const response = await authApi.login(credentials)
          
          if (response.requiresMfa) {
            set({
              mfaRequired: true,
              mfaToken: response.mfaToken,
            })
            return {
              success: true,
              requiresMfa: true,
              mfaToken: response.mfaToken,
            }
          }

          if (response.user && response.accessToken && response.refreshToken) {
            set({
              user: response.user,
              accessToken: response.accessToken,
              refreshToken: response.refreshToken,
              isAuthenticated: true,
              mfaRequired: false,
              mfaToken: null,
            })
          }

          return {
            success: true,
            requiresMfa: false,
            user: response.user,
            accessToken: response.accessToken,
            refreshToken: response.refreshToken,
          }
        } catch (error: any) {
          console.error('登录失败:', error)
          return {
            success: false,
            requiresMfa: false,
            message: error.message || '登录失败',
          }
        }
      },

      // 注册操作
      register: async (userData: RegisterData): Promise<void> => {
        try {
          await authApi.register(userData)
        } catch (error: any) {
          throw new Error(error.message || '注册失败')
        }
      },

      // 登出操作
      logout: () => {
        const { accessToken } = get()
        if (accessToken) {
          authApi.logout().catch(console.error)
        }
        
        set({
          user: null,
          accessToken: null,
          refreshToken: null,
          isAuthenticated: false,
          mfaRequired: false,
          mfaToken: null,
        })
      },

      // 刷新访问令牌
      refreshAccessToken: async (): Promise<boolean> => {
        try {
          const { refreshToken } = get()
          if (!refreshToken) {
            return false
          }

          const response = await authApi.refreshToken(refreshToken)
          
          set({
            accessToken: response.accessToken,
            refreshToken: response.refreshToken || refreshToken,
          })
          
          return true
        } catch (error) {
          console.error('刷新令牌失败:', error)
          get().clearAuth()
          return false
        }
      },

      // 设置用户信息
      setUser: (user: User) => {
        set({ user })
      },

      // 设置令牌
      setTokens: (accessToken: string, refreshToken: string) => {
        set({
          accessToken,
          refreshToken,
          isAuthenticated: true,
        })
      },

      // 设置MFA状态
      setMfaRequired: (required: boolean, token?: string) => {
        set({
          mfaRequired: required,
          mfaToken: token || null,
        })
      },

      // 清除认证状态
      clearAuth: () => {
        set({
          user: null,
          accessToken: null,
          refreshToken: null,
          isAuthenticated: false,
          mfaRequired: false,
          mfaToken: null,
        })
      },

      // 初始化认证状态
      initializeAuth: async () => {
        set({ isLoading: true })
        
        try {
          const { accessToken, refreshToken } = get()
          
          if (!accessToken) {
            set({ isLoading: false })
            return
          }

          // 验证当前令牌
          try {
            const user = await authApi.getCurrentUser()
            set({
              user,
              isAuthenticated: true,
              isLoading: false,
            })
          } catch (error) {
            // 尝试刷新令牌
            if (refreshToken) {
              const refreshed = await get().refreshAccessToken()
              if (refreshed) {
                const user = await authApi.getCurrentUser()
                set({
                  user,
                  isAuthenticated: true,
                  isLoading: false,
                })
                return
              }
            }
            
            // 清除无效的认证状态
            get().clearAuth()
            set({ isLoading: false })
          }
        } catch (error) {
          console.error('初始化认证状态失败:', error)
          get().clearAuth()
          set({ isLoading: false })
        }
      },
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        accessToken: state.accessToken,
        refreshToken: state.refreshToken,
        user: state.user,
      }),
    }
  )
)

// 初始化认证状态
useAuthStore.getState().initializeAuth()
