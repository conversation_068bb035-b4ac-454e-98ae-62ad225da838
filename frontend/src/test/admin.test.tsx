/**
 * 管理员控制台测试
 * 测试React Admin管理员控制台的基本功能
 */

import React from 'react'
import { render, screen, waitFor } from '@testing-library/react'
import { BrowserRouter } from 'react-router-dom'
import { AdminApp } from '../components/admin/AdminApp'
import { useAuthStore } from '../stores/authStore'

// Mock useAuthStore
jest.mock('../stores/authStore')
const mockUseAuthStore = useAuthStore as jest.MockedFunction<typeof useAuthStore>

// Mock adminApi
jest.mock('../services/adminApi', () => ({
  adminApi: {
    getSystemStats: jest.fn().mockResolvedValue({
      totalUsers: 100,
      activeUsers: 80,
      totalSessions: 50,
      oauthClients: 10,
      apiRequests24h: 1000,
      errorRate: 0.01,
      systemHealth: {
        database: true,
        redis: true,
        memory: 60,
        cpu: 30
      }
    }),
    getUsers: jest.fn().mockResolvedValue({
      users: [
        {
          id: '1',
          email: '<EMAIL>',
          nickname: 'Test User',
          isActive: true,
          isEmailVerified: true,
          roles: ['user'],
          createdAt: new Date().toISOString()
        }
      ],
      total: 1
    }),
    getOAuthClients: jest.fn().mockResolvedValue({
      clients: [
        {
          id: '1',
          clientId: 'test-client',
          name: 'Test Client',
          isActive: true,
          grantTypes: ['authorization_code'],
          scopes: ['openid', 'profile'],
          createdAt: new Date().toISOString()
        }
      ],
      total: 1
    }),
    getAuditLogs: jest.fn().mockResolvedValue({
      logs: [
        {
          id: '1',
          action: 'login',
          resource: 'user',
          userEmail: '<EMAIL>',
          ipAddress: '127.0.0.1',
          createdAt: new Date().toISOString()
        }
      ],
      total: 1
    }),
    getSystemConfigs: jest.fn().mockResolvedValue([
      {
        id: '1',
        key: 'test.config',
        value: 'test value',
        category: 'system',
        isPublic: false,
        updatedAt: new Date().toISOString()
      }
    ])
  }
}))

// Mock React Admin components
jest.mock('react-admin', () => ({
  Admin: ({ children }: any) => <div data-testid="admin-app">{children}</div>,
  Resource: ({ name }: any) => <div data-testid={`resource-${name}`}>{name}</div>,
  Layout: ({ children }: any) => <div data-testid="admin-layout">{children}</div>,
  AppBar: () => <div data-testid="admin-appbar">AppBar</div>,
  Menu: ({ children }: any) => <div data-testid="admin-menu">{children}</div>,
  UserMenu: () => <div data-testid="admin-usermenu">UserMenu</div>
}))

describe('AdminApp', () => {
  beforeEach(() => {
    // Mock authenticated admin user
    mockUseAuthStore.mockReturnValue({
      user: {
        id: '1',
        email: '<EMAIL>',
        roles: ['admin'],
        isActive: true
      },
      accessToken: 'mock-token',
      isAuthenticated: true,
      isLoading: false,
      mfaRequired: false,
      login: jest.fn(),
      logout: jest.fn(),
      refreshAccessToken: jest.fn(),
      clearAuth: jest.fn()
    } as any)
  })

  afterEach(() => {
    jest.clearAllMocks()
  })

  it('应该渲染管理员控制台', async () => {
    render(
      <BrowserRouter>
        <AdminApp />
      </BrowserRouter>
    )

    await waitFor(() => {
      expect(screen.getByTestId('admin-app')).toBeInTheDocument()
    })
  })

  it('应该包含所有资源', async () => {
    render(
      <BrowserRouter>
        <AdminApp />
      </BrowserRouter>
    )

    await waitFor(() => {
      expect(screen.getByTestId('resource-users')).toBeInTheDocument()
      expect(screen.getByTestId('resource-clients')).toBeInTheDocument()
      expect(screen.getByTestId('resource-auditLogs')).toBeInTheDocument()
      expect(screen.getByTestId('resource-systemConfigs')).toBeInTheDocument()
    })
  })
})

describe('数据提供者', () => {
  it('应该正确转换分页参数', () => {
    const { dataProvider } = require('../components/admin/dataProvider')
    
    // 这里可以添加更多的数据提供者测试
    expect(dataProvider).toBeDefined()
  })
})

describe('认证提供者', () => {
  it('应该正确检查管理员权限', async () => {
    const { authProvider } = require('../components/admin/authProvider')
    
    // Mock admin user
    mockUseAuthStore.mockReturnValue({
      user: {
        id: '1',
        email: '<EMAIL>',
        roles: ['admin']
      },
      accessToken: 'mock-token'
    } as any)

    await expect(authProvider.checkAuth()).resolves.toBeUndefined()
  })

  it('应该拒绝非管理员用户', async () => {
    const { authProvider } = require('../components/admin/authProvider')
    
    // Mock regular user
    mockUseAuthStore.mockReturnValue({
      user: {
        id: '1',
        email: '<EMAIL>',
        roles: ['user']
      },
      accessToken: 'mock-token'
    } as any)

    await expect(authProvider.checkAuth()).rejects.toThrow('没有管理员权限')
  })
})
