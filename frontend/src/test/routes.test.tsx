/**
 * 前端路由配置测试
 * 验证所有路由都能正常工作
 */

import React from 'react'
import { render, screen } from '@testing-library/react'
import { <PERSON><PERSON>erRouter } from 'react-router-dom'
import { act } from 'react-dom/test-utils'
import App from '../App'
import { useAuthStore } from '../stores/authStore'

// Mock the auth store
jest.mock('../stores/authStore')
const mockUseAuthStore = useAuthStore as jest.MockedFunction<typeof useAuthStore>

// Mock the API services
jest.mock('../services/api', () => ({
  authApi: {
    login: jest.fn(),
    register: jest.fn(),
    logout: jest.fn(),
    refreshToken: jest.fn(),
    getCurrentUser: jest.fn(),
  },
  oauthApi: {
    getProviders: jest.fn(),
    getAuthUrl: jest.fn(),
  }
}))

// Mock components that might have complex dependencies
jest.mock('../pages/DashboardPage', () => {
  return function MockDashboardPage() {
    return <div data-testid="dashboard-page">仪表板页面</div>
  }
})

jest.mock('../pages/LoginPage', () => {
  return function MockLoginPage() {
    return <div data-testid="login-page">登录页面</div>
  }
})

jest.mock('../pages/RegisterPage', () => {
  return function MockRegisterPage() {
    return <div data-testid="register-page">注册页面</div>
  }
})

jest.mock('../pages/MFAPage', () => {
  return function MockMFAPage() {
    return <div data-testid="mfa-page">MFA验证页面</div>
  }
})

jest.mock('../pages/ForgotPasswordPage', () => {
  return function MockForgotPasswordPage() {
    return <div data-testid="forgot-password-page">忘记密码页面</div>
  }
})

jest.mock('../pages/ResetPasswordPage', () => {
  return function MockResetPasswordPage() {
    return <div data-testid="reset-password-page">重置密码页面</div>
  }
})

jest.mock('../pages/DemoPage', () => {
  return function MockDemoPage() {
    return <div data-testid="demo-page">演示页面</div>
  }
})

jest.mock('../pages/OAuthErrorPage', () => {
  return function MockOAuthErrorPage() {
    return <div data-testid="oauth-error-page">OAuth错误页面</div>
  }
})

const renderWithRouter = (initialEntries = ['/']) => {
  return render(
    <BrowserRouter>
      <App />
    </BrowserRouter>
  )
}

describe('前端路由配置测试', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('未认证用户路由', () => {
    beforeEach(() => {
      mockUseAuthStore.mockReturnValue({
        isAuthenticated: false,
        isLoading: false,
        mfaRequired: false,
        user: null,
        accessToken: null,
        refreshToken: null,
        mfaToken: null,
        login: jest.fn(),
        register: jest.fn(),
        logout: jest.fn(),
        refreshAccessToken: jest.fn(),
        setUser: jest.fn(),
        setTokens: jest.fn(),
        setMfaRequired: jest.fn(),
        clearAuth: jest.fn(),
        initializeAuth: jest.fn(),
      })
    })

    it('应该在根路径重定向到登录页面', () => {
      // 设置初始路径为根路径
      Object.defineProperty(window, 'location', {
        value: { pathname: '/' },
        writable: true
      })

      renderWithRouter()
      
      // 应该显示登录页面
      expect(screen.getByTestId('login-page')).toBeInTheDocument()
    })

    it('应该正确渲染登录页面', () => {
      Object.defineProperty(window, 'location', {
        value: { pathname: '/login' },
        writable: true
      })

      renderWithRouter()
      expect(screen.getByTestId('login-page')).toBeInTheDocument()
    })

    it('应该正确渲染注册页面', () => {
      Object.defineProperty(window, 'location', {
        value: { pathname: '/register' },
        writable: true
      })

      renderWithRouter()
      expect(screen.getByTestId('register-page')).toBeInTheDocument()
    })

    it('应该正确渲染忘记密码页面', () => {
      Object.defineProperty(window, 'location', {
        value: { pathname: '/forgot-password' },
        writable: true
      })

      renderWithRouter()
      expect(screen.getByTestId('forgot-password-page')).toBeInTheDocument()
    })

    it('应该正确渲染OAuth错误页面', () => {
      Object.defineProperty(window, 'location', {
        value: { pathname: '/auth/error' },
        writable: true
      })

      renderWithRouter()
      expect(screen.getByTestId('oauth-error-page')).toBeInTheDocument()
    })
  })

  describe('已认证用户路由', () => {
    beforeEach(() => {
      mockUseAuthStore.mockReturnValue({
        isAuthenticated: true,
        isLoading: false,
        mfaRequired: false,
        user: {
          id: '1',
          email: '<EMAIL>',
          nickname: 'Test User',
          isEmailVerified: true,
          mfaEnabled: false,
          roles: ['user']
        },
        accessToken: 'mock-access-token',
        refreshToken: 'mock-refresh-token',
        mfaToken: null,
        login: jest.fn(),
        register: jest.fn(),
        logout: jest.fn(),
        refreshAccessToken: jest.fn(),
        setUser: jest.fn(),
        setTokens: jest.fn(),
        setMfaRequired: jest.fn(),
        clearAuth: jest.fn(),
        initializeAuth: jest.fn(),
      })
    })

    it('应该在根路径重定向到仪表板', () => {
      Object.defineProperty(window, 'location', {
        value: { pathname: '/' },
        writable: true
      })

      renderWithRouter()
      expect(screen.getByTestId('dashboard-page')).toBeInTheDocument()
    })

    it('应该正确渲染仪表板页面', () => {
      Object.defineProperty(window, 'location', {
        value: { pathname: '/dashboard' },
        writable: true
      })

      renderWithRouter()
      expect(screen.getByTestId('dashboard-page')).toBeInTheDocument()
    })

    it('应该将登录页面重定向到仪表板', () => {
      Object.defineProperty(window, 'location', {
        value: { pathname: '/login' },
        writable: true
      })

      renderWithRouter()
      expect(screen.getByTestId('dashboard-page')).toBeInTheDocument()
    })
  })

  describe('加载状态', () => {
    it('应该在加载时显示加载指示器', () => {
      mockUseAuthStore.mockReturnValue({
        isAuthenticated: false,
        isLoading: true,
        mfaRequired: false,
        user: null,
        accessToken: null,
        refreshToken: null,
        mfaToken: null,
        login: jest.fn(),
        register: jest.fn(),
        logout: jest.fn(),
        refreshAccessToken: jest.fn(),
        setUser: jest.fn(),
        setTokens: jest.fn(),
        setMfaRequired: jest.fn(),
        clearAuth: jest.fn(),
        initializeAuth: jest.fn(),
      })

      renderWithRouter()
      expect(screen.getByText('加载中...')).toBeInTheDocument()
    })
  })

  describe('MFA流程', () => {
    it('应该在需要MFA时重定向到MFA页面', () => {
      mockUseAuthStore.mockReturnValue({
        isAuthenticated: true,
        isLoading: false,
        mfaRequired: true,
        user: {
          id: '1',
          email: '<EMAIL>',
          nickname: 'Test User',
          isEmailVerified: true,
          mfaEnabled: true,
          roles: ['user']
        },
        accessToken: null,
        refreshToken: null,
        mfaToken: 'mock-mfa-token',
        login: jest.fn(),
        register: jest.fn(),
        logout: jest.fn(),
        refreshAccessToken: jest.fn(),
        setUser: jest.fn(),
        setTokens: jest.fn(),
        setMfaRequired: jest.fn(),
        clearAuth: jest.fn(),
        initializeAuth: jest.fn(),
      })

      Object.defineProperty(window, 'location', {
        value: { pathname: '/dashboard' },
        writable: true
      })

      renderWithRouter()
      expect(screen.getByTestId('mfa-page')).toBeInTheDocument()
    })
  })
})
