/**
 * 国际化Hook
 * 
 * 功能说明：
 * 1. 封装react-i18next的使用
 * 2. 提供语言切换功能
 * 3. 提供本地化格式化功能
 * 4. 提供RTL语言支持检测
 * 5. 提供语言加载状态管理
 */

import { useTranslation } from 'react-i18next';
import { useState, useEffect, useCallback } from 'react';
import {
  SupportedLanguage,
  getCurrentLanguage,
  changeLanguage,
  getLanguageInfo,
  isRTLLanguage,
  formatDate,
  formatNumber,
  formatCurrency,
  SUPPORTED_LANGUAGES
} from '../i18n';

/**
 * 国际化Hook返回值类型
 */
interface UseI18nReturn {
  // 翻译函数
  t: (key: string, options?: any) => string;
  
  // 当前语言
  currentLanguage: SupportedLanguage;
  
  // 语言信息
  languageInfo: typeof SUPPORTED_LANGUAGES[SupportedLanguage];
  
  // 是否为RTL语言
  isRTL: boolean;
  
  // 支持的语言列表
  supportedLanguages: typeof SUPPORTED_LANGUAGES;
  
  // 语言切换函数
  switchLanguage: (language: SupportedLanguage) => Promise<void>;
  
  // 语言切换加载状态
  isChangingLanguage: boolean;
  
  // 翻译加载状态
  isLoading: boolean;
  
  // 格式化函数
  formatters: {
    date: (date: Date | string | number, options?: Intl.DateTimeFormatOptions) => string;
    number: (number: number, options?: Intl.NumberFormatOptions) => string;
    currency: (amount: number, currency?: string) => string;
    relativeTime: (date: Date | string | number) => string;
  };
  
  // 工具函数
  utils: {
    getLanguageInfo: (language: SupportedLanguage) => typeof SUPPORTED_LANGUAGES[SupportedLanguage];
    isRTLLanguage: (language?: SupportedLanguage) => boolean;
    getLanguageDirection: () => 'ltr' | 'rtl';
    getBrowserLanguage: () => SupportedLanguage;
  };
}

/**
 * 国际化Hook
 * @param namespace 命名空间，默认为'common'
 */
export const useI18n = (namespace: string = 'common'): UseI18nReturn => {
  const { t, i18n, ready } = useTranslation(namespace);
  const [currentLanguage, setCurrentLanguage] = useState<SupportedLanguage>(getCurrentLanguage());
  const [isChangingLanguage, setIsChangingLanguage] = useState(false);

  // 监听语言变化
  useEffect(() => {
    const handleLanguageChange = () => {
      setCurrentLanguage(getCurrentLanguage());
    };

    // 监听i18next语言变化事件
    i18n.on('languageChanged', handleLanguageChange);
    
    // 监听自定义语言变化事件
    window.addEventListener('languageChanged', handleLanguageChange);

    return () => {
      i18n.off('languageChanged', handleLanguageChange);
      window.removeEventListener('languageChanged', handleLanguageChange);
    };
  }, [i18n]);

  /**
   * 切换语言
   */
  const switchLanguage = useCallback(async (language: SupportedLanguage) => {
    if (language === currentLanguage) return;

    setIsChangingLanguage(true);
    try {
      await changeLanguage(language);
    } catch (error) {
      console.error('语言切换失败:', error);
      throw error;
    } finally {
      setIsChangingLanguage(false);
    }
  }, [currentLanguage]);

  /**
   * 获取相对时间格式化
   */
  const formatRelativeTime = useCallback((date: Date | string | number): string => {
    const now = new Date();
    const targetDate = new Date(date);
    const diffInSeconds = Math.floor((now.getTime() - targetDate.getTime()) / 1000);

    // 使用Intl.RelativeTimeFormat进行相对时间格式化
    const rtf = new Intl.RelativeTimeFormat(currentLanguage, { numeric: 'auto' });

    if (Math.abs(diffInSeconds) < 60) {
      return rtf.format(-diffInSeconds, 'second');
    } else if (Math.abs(diffInSeconds) < 3600) {
      return rtf.format(-Math.floor(diffInSeconds / 60), 'minute');
    } else if (Math.abs(diffInSeconds) < 86400) {
      return rtf.format(-Math.floor(diffInSeconds / 3600), 'hour');
    } else if (Math.abs(diffInSeconds) < 2592000) {
      return rtf.format(-Math.floor(diffInSeconds / 86400), 'day');
    } else if (Math.abs(diffInSeconds) < 31536000) {
      return rtf.format(-Math.floor(diffInSeconds / 2592000), 'month');
    } else {
      return rtf.format(-Math.floor(diffInSeconds / 31536000), 'year');
    }
  }, [currentLanguage]);

  /**
   * 获取浏览器语言
   */
  const getBrowserLanguage = useCallback((): SupportedLanguage => {
    const browserLang = navigator.language || navigator.languages?.[0] || 'zh-CN';
    
    // 检查是否为支持的语言
    if (browserLang in SUPPORTED_LANGUAGES) {
      return browserLang as SupportedLanguage;
    }
    
    // 检查语言前缀
    const langPrefix = browserLang.split('-')[0];
    const matchedLang = Object.keys(SUPPORTED_LANGUAGES).find(lang => 
      lang.startsWith(langPrefix)
    );
    
    return (matchedLang as SupportedLanguage) || 'zh-CN';
  }, []);

  /**
   * 获取语言方向
   */
  const getLanguageDirection = useCallback((): 'ltr' | 'rtl' => {
    return isRTLLanguage(currentLanguage) ? 'rtl' : 'ltr';
  }, [currentLanguage]);

  // 当前语言信息
  const languageInfo = getLanguageInfo(currentLanguage);
  
  // 是否为RTL语言
  const isRTL = isRTLLanguage(currentLanguage);

  return {
    t,
    currentLanguage,
    languageInfo,
    isRTL,
    supportedLanguages: SUPPORTED_LANGUAGES,
    switchLanguage,
    isChangingLanguage,
    isLoading: !ready,
    formatters: {
      date: formatDate,
      number: formatNumber,
      currency: formatCurrency,
      relativeTime: formatRelativeTime
    },
    utils: {
      getLanguageInfo,
      isRTLLanguage,
      getLanguageDirection,
      getBrowserLanguage
    }
  };
};

/**
 * 翻译Hook（简化版）
 * @param namespace 命名空间
 */
export const useTranslate = (namespace?: string) => {
  const { t } = useI18n(namespace);
  return t;
};

/**
 * 语言切换Hook
 */
export const useLanguageSwitcher = () => {
  const { currentLanguage, switchLanguage, isChangingLanguage, supportedLanguages } = useI18n();
  
  return {
    currentLanguage,
    switchLanguage,
    isChangingLanguage,
    supportedLanguages
  };
};

/**
 * 本地化格式化Hook
 */
export const useFormatters = () => {
  const { formatters, currentLanguage } = useI18n();
  
  return {
    ...formatters,
    currentLanguage
  };
};

/**
 * RTL支持Hook
 */
export const useRTL = () => {
  const { isRTL, utils } = useI18n();
  
  return {
    isRTL,
    direction: utils.getLanguageDirection(),
    getDirection: utils.getLanguageDirection
  };
};

export default useI18n;
