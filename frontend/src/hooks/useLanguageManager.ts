/**
 * 语言包管理Hook
 * 
 * 功能说明：
 * 1. 提供语言包动态加载功能
 * 2. 管理加载状态和错误处理
 * 3. 支持语言包预加载和热更新
 * 4. 提供缓存管理功能
 * 5. 集成React组件生命周期
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { languageManager } from '../i18n/LanguageManager';
import { SupportedLanguage } from '../i18n';

/**
 * 加载状态枚举
 */
export enum LoadingState {
  IDLE = 'idle',
  LOADING = 'loading',
  SUCCESS = 'success',
  ERROR = 'error'
}

/**
 * 语言包管理Hook返回值类型
 */
interface UseLanguageManagerReturn {
  // 加载状态
  loadingState: LoadingState;
  
  // 错误信息
  error: string | null;
  
  // 加载进度（0-100）
  progress: number;
  
  // 已加载的语言包列表
  loadedPacks: Array<{
    language: SupportedLanguage;
    namespace: string;
    timestamp: number;
  }>;
  
  // 功能函数
  loadLanguagePack: (language: SupportedLanguage, namespace?: string) => Promise<void>;
  preloadLanguage: (language: SupportedLanguage, namespaces?: string[]) => Promise<void>;
  preloadAllLanguages: (namespaces?: string[]) => Promise<void>;
  hotReload: (language: SupportedLanguage, namespace?: string) => Promise<void>;
  clearCache: (language?: SupportedLanguage, namespace?: string) => void;
  validatePack: (language: SupportedLanguage, namespace?: string) => Promise<boolean>;
  
  // 缓存统计
  cacheStats: {
    size: number;
    totalSize: number;
    validItems: number;
  };
  
  // 工具函数
  utils: {
    isPackLoaded: (language: SupportedLanguage, namespace?: string) => boolean;
    getLoadedNamespaces: (language: SupportedLanguage) => string[];
    refreshCacheStats: () => void;
  };
}

/**
 * 语言包管理Hook
 * @param autoPreload 是否自动预加载当前语言
 * @param defaultNamespaces 默认命名空间列表
 */
export const useLanguageManager = (
  autoPreload: boolean = true,
  defaultNamespaces: string[] = ['common']
): UseLanguageManagerReturn => {
  const { i18n } = useTranslation();
  const [loadingState, setLoadingState] = useState<LoadingState>(LoadingState.IDLE);
  const [error, setError] = useState<string | null>(null);
  const [progress, setProgress] = useState(0);
  const [loadedPacks, setLoadedPacks] = useState<Array<{
    language: SupportedLanguage;
    namespace: string;
    timestamp: number;
  }>>([]);
  const [cacheStats, setCacheStats] = useState({
    size: 0,
    totalSize: 0,
    validItems: 0
  });

  // 使用ref来避免重复加载
  const loadingRef = useRef<Set<string>>(new Set());

  /**
   * 更新缓存统计信息
   */
  const refreshCacheStats = useCallback(() => {
    const stats = languageManager.getCacheStats();
    setCacheStats({
      size: stats.size,
      totalSize: stats.items.reduce((total, item) => total + item.size, 0),
      validItems: stats.items.filter(item => item.valid).length
    });
  }, []);

  /**
   * 检查语言包是否已加载
   */
  const isPackLoaded = useCallback((language: SupportedLanguage, namespace: string = 'common'): boolean => {
    return loadedPacks.some(pack => 
      pack.language === language && pack.namespace === namespace
    );
  }, [loadedPacks]);

  /**
   * 获取已加载的命名空间
   */
  const getLoadedNamespaces = useCallback((language: SupportedLanguage): string[] => {
    return loadedPacks
      .filter(pack => pack.language === language)
      .map(pack => pack.namespace);
  }, [loadedPacks]);

  /**
   * 添加已加载的语言包记录
   */
  const addLoadedPack = useCallback((language: SupportedLanguage, namespace: string) => {
    setLoadedPacks(prev => {
      // 避免重复添加
      const exists = prev.some(pack => 
        pack.language === language && pack.namespace === namespace
      );
      
      if (exists) return prev;
      
      return [...prev, {
        language,
        namespace,
        timestamp: Date.now()
      }];
    });
  }, []);

  /**
   * 移除已加载的语言包记录
   */
  const removeLoadedPack = useCallback((language?: SupportedLanguage, namespace?: string) => {
    setLoadedPacks(prev => {
      if (language && namespace) {
        return prev.filter(pack => 
          !(pack.language === language && pack.namespace === namespace)
        );
      } else if (language) {
        return prev.filter(pack => pack.language !== language);
      } else {
        return [];
      }
    });
  }, []);

  /**
   * 加载语言包
   */
  const loadLanguagePack = useCallback(async (
    language: SupportedLanguage, 
    namespace: string = 'common'
  ): Promise<void> => {
    const loadingKey = `${language}:${namespace}`;
    
    // 避免重复加载
    if (loadingRef.current.has(loadingKey)) {
      return;
    }
    
    loadingRef.current.add(loadingKey);
    setLoadingState(LoadingState.LOADING);
    setError(null);
    setProgress(0);

    try {
      // 模拟进度更新
      const progressInterval = setInterval(() => {
        setProgress(prev => Math.min(prev + 10, 90));
      }, 100);

      await languageManager.loadLanguagePack(language, namespace);
      
      clearInterval(progressInterval);
      setProgress(100);
      setLoadingState(LoadingState.SUCCESS);
      
      // 添加到已加载列表
      addLoadedPack(language, namespace);
      
      // 更新缓存统计
      refreshCacheStats();
      
      console.log(`语言包加载成功: ${language}:${namespace}`);
    } catch (err) {
      setLoadingState(LoadingState.ERROR);
      const errorMessage = err instanceof Error ? err.message : '语言包加载失败';
      setError(errorMessage);
      console.error(`语言包加载失败: ${language}:${namespace}`, err);
    } finally {
      loadingRef.current.delete(loadingKey);
      setTimeout(() => {
        setProgress(0);
        if (loadingState !== LoadingState.ERROR) {
          setLoadingState(LoadingState.IDLE);
        }
      }, 1000);
    }
  }, [loadingState, addLoadedPack, refreshCacheStats]);

  /**
   * 预加载语言
   */
  const preloadLanguage = useCallback(async (
    language: SupportedLanguage,
    namespaces: string[] = defaultNamespaces
  ): Promise<void> => {
    setLoadingState(LoadingState.LOADING);
    setError(null);

    try {
      await languageManager.preloadLanguagePack(language, namespaces);
      
      // 添加到已加载列表
      namespaces.forEach(namespace => {
        addLoadedPack(language, namespace);
      });
      
      setLoadingState(LoadingState.SUCCESS);
      refreshCacheStats();
      
      console.log(`语言预加载成功: ${language}`);
    } catch (err) {
      setLoadingState(LoadingState.ERROR);
      const errorMessage = err instanceof Error ? err.message : '语言预加载失败';
      setError(errorMessage);
      console.error(`语言预加载失败: ${language}`, err);
    }
  }, [defaultNamespaces, addLoadedPack, refreshCacheStats]);

  /**
   * 预加载所有语言
   */
  const preloadAllLanguages = useCallback(async (
    namespaces: string[] = defaultNamespaces
  ): Promise<void> => {
    setLoadingState(LoadingState.LOADING);
    setError(null);

    try {
      await languageManager.preloadAllLanguages(namespaces);
      setLoadingState(LoadingState.SUCCESS);
      refreshCacheStats();
      
      console.log('所有语言预加载成功');
    } catch (err) {
      setLoadingState(LoadingState.ERROR);
      const errorMessage = err instanceof Error ? err.message : '语言预加载失败';
      setError(errorMessage);
      console.error('所有语言预加载失败', err);
    }
  }, [defaultNamespaces, refreshCacheStats]);

  /**
   * 热更新语言包
   */
  const hotReload = useCallback(async (
    language: SupportedLanguage,
    namespace: string = 'common'
  ): Promise<void> => {
    setLoadingState(LoadingState.LOADING);
    setError(null);

    try {
      await languageManager.hotReload(language, namespace);
      setLoadingState(LoadingState.SUCCESS);
      refreshCacheStats();
      
      console.log(`热更新成功: ${language}:${namespace}`);
    } catch (err) {
      setLoadingState(LoadingState.ERROR);
      const errorMessage = err instanceof Error ? err.message : '热更新失败';
      setError(errorMessage);
      console.error(`热更新失败: ${language}:${namespace}`, err);
    }
  }, [refreshCacheStats]);

  /**
   * 清理缓存
   */
  const clearCache = useCallback((language?: SupportedLanguage, namespace?: string) => {
    languageManager.clearCache(language, namespace);
    removeLoadedPack(language, namespace);
    refreshCacheStats();
    
    console.log('缓存清理完成');
  }, [removeLoadedPack, refreshCacheStats]);

  /**
   * 验证语言包
   */
  const validatePack = useCallback(async (
    language: SupportedLanguage,
    namespace: string = 'common'
  ): Promise<boolean> => {
    try {
      return await languageManager.validateLanguagePack(language, namespace);
    } catch (err) {
      console.error(`语言包验证失败: ${language}:${namespace}`, err);
      return false;
    }
  }, []);

  // 自动预加载当前语言
  useEffect(() => {
    if (autoPreload && i18n.language) {
      const currentLang = i18n.language as SupportedLanguage;
      preloadLanguage(currentLang, defaultNamespaces).catch(console.error);
    }
  }, [autoPreload, i18n.language, defaultNamespaces, preloadLanguage]);

  // 初始化缓存统计
  useEffect(() => {
    refreshCacheStats();
  }, [refreshCacheStats]);

  return {
    loadingState,
    error,
    progress,
    loadedPacks,
    loadLanguagePack,
    preloadLanguage,
    preloadAllLanguages,
    hotReload,
    clearCache,
    validatePack,
    cacheStats,
    utils: {
      isPackLoaded,
      getLoadedNamespaces,
      refreshCacheStats
    }
  };
};

export default useLanguageManager;
