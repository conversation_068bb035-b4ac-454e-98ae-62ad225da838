import { useState, useEffect } from 'react'
import axios from 'axios'

export interface UIConfig {
  title: string
  logo?: string
  theme: {
    primaryColor: string
    borderRadius: number
  }
  features: {
    registration: boolean
    socialLogin: boolean
    mfa: boolean
  }
  oauth: {
    google: {
      enabled: boolean
      clientId?: string
    }
    github: {
      enabled: boolean
      clientId?: string
    }
  }
}

const defaultConfig: UIConfig = {
  title: '身份提供商',
  theme: {
    primaryColor: '#1890ff',
    borderRadius: 6,
  },
  features: {
    registration: true,
    socialLogin: true,
    mfa: true,
  },
  oauth: {
    google: {
      enabled: false,
    },
    github: {
      enabled: false,
    },
  },
}

export const useUIConfig = () => {
  const [config, setConfig] = useState<UIConfig>(defaultConfig)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    loadConfig()
  }, [])

  const loadConfig = async () => {
    try {
      setLoading(true)
      setError(null)
      
      const response = await axios.get('/ui/config')
      setConfig({ ...defaultConfig, ...response.data })
    } catch (err: any) {
      console.error('加载UI配置失败:', err)
      setError(err.message || '加载配置失败')
      // 使用默认配置
      setConfig(defaultConfig)
    } finally {
      setLoading(false)
    }
  }

  const updateTheme = (theme: Partial<UIConfig['theme']>) => {
    setConfig(prev => ({
      ...prev,
      theme: { ...prev.theme, ...theme }
    }))
  }

  const updateFeatures = (features: Partial<UIConfig['features']>) => {
    setConfig(prev => ({
      ...prev,
      features: { ...prev.features, ...features }
    }))
  }

  return {
    config,
    loading,
    error,
    loadConfig,
    updateTheme,
    updateFeatures,
  }
}
