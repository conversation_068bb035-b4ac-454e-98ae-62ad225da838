import React, { useEffect } from 'react'
import { ConfigProvider, theme } from 'antd'
import zhCN from 'antd/locale/zh_CN'
import { useUIConfig } from '../hooks/useUIConfig'

interface ThemeProviderProps {
  children: React.ReactNode
}

const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  const { config, loading } = useUIConfig()

  // 动态更新CSS变量
  useEffect(() => {
    if (!loading && config) {
      const root = document.documentElement
      root.style.setProperty('--primary-color', config.theme.primaryColor)
      root.style.setProperty('--border-radius', `${config.theme.borderRadius}px`)
      
      // 更新页面标题
      document.title = config.title
    }
  }, [config, loading])

  if (loading) {
    return (
      <div className="loading-container">
        <div>加载配置中...</div>
      </div>
    )
  }

  const antdConfig = {
    locale: zhCN,
    theme: {
      algorithm: theme.defaultAlgorithm,
      token: {
        colorPrimary: config.theme.primaryColor,
        borderRadius: config.theme.borderRadius,
        fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
      },
      components: {
        Button: {
          borderRadius: config.theme.borderRadius,
        },
        Input: {
          borderRadius: config.theme.borderRadius,
        },
        Card: {
          borderRadius: config.theme.borderRadius,
        },
      },
    },
  }

  return (
    <ConfigProvider {...antdConfig}>
      {children}
    </ConfigProvider>
  )
}

export default ThemeProvider
