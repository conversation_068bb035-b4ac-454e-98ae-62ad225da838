/**
 * 安全事件管理组件
 * 
 * 功能说明：
 * 1. 安全事件列表和详情查看
 * 2. 威胁响应操作界面
 * 3. 事件状态管理和处理
 * 4. 实时事件监控和告警
 * 5. 安全策略配置管理
 */

import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Modal,
  Form,
  Input,
  Select,
  DatePicker,
  Alert,
  Descriptions,
  Timeline,
  Badge,
  Tooltip,
  Drawer,
  Row,
  Col,
  Statistic,
  Progress,
  Typography,
  Divider,
  message
} from 'antd';
import {
  SecurityScanOutlined,
  AlertOutlined,
  ShieldOutlined,
  EyeOutlined,
  EditOutlined,
  DeleteOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  ExclamationCircleOutlined,
  ReloadOutlined,
  FilterOutlined,
  SettingOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import type { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';

const { Title, Text, Paragraph } = Typography;
const { TextArea } = Input;
const { RangePicker } = DatePicker;

/**
 * 安全事件接口
 */
interface SecurityEvent {
  id: string;
  type: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  status: 'open' | 'investigating' | 'resolved' | 'false_positive';
  title: string;
  description: string;
  source: {
    ip: string;
    userAgent?: string;
    userId?: string;
    location?: string;
  };
  indicators: Array<{
    type: string;
    value: string;
    confidence: number;
  }>;
  timeline: Array<{
    timestamp: string;
    action: string;
    user: string;
    details: string;
  }>;
  assignedTo?: string;
  resolvedBy?: string;
  resolution?: string;
  createdAt: string;
  updatedAt: string;
  metadata: Record<string, any>;
}

/**
 * 威胁响应动作接口
 */
interface ResponseAction {
  id: string;
  name: string;
  description: string;
  type: 'block_ip' | 'block_user' | 'require_mfa' | 'rate_limit' | 'quarantine';
  severity: string[];
  autoExecute: boolean;
}

/**
 * 安全事件管理组件
 */
const SecurityEventManager: React.FC = () => {
  const { t } = useTranslation();
  const [events, setEvents] = useState<SecurityEvent[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedEvent, setSelectedEvent] = useState<SecurityEvent | null>(null);
  const [detailsVisible, setDetailsVisible] = useState(false);
  const [responseVisible, setResponseVisible] = useState(false);
  const [filters, setFilters] = useState({
    severity: '',
    status: '',
    type: '',
    dateRange: null as [dayjs.Dayjs, dayjs.Dayjs] | null
  });

  // 模拟数据
  const mockEvents: SecurityEvent[] = [
    {
      id: 'evt_001',
      type: '暴力破解',
      severity: 'high',
      status: 'open',
      title: '检测到暴力破解攻击',
      description: '来自IP *************的多次登录失败尝试',
      source: {
        ip: '*************',
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)',
        location: '北京, 中国'
      },
      indicators: [
        { type: 'ip', value: '*************', confidence: 0.9 },
        { type: 'pattern', value: 'brute_force', confidence: 0.85 }
      ],
      timeline: [
        {
          timestamp: '2024-01-15 14:30:25',
          action: '事件创建',
          user: 'system',
          details: '自动检测到暴力破解模式'
        },
        {
          timestamp: '2024-01-15 14:31:00',
          action: '自动响应',
          user: 'system',
          details: '应用IP限制措施'
        }
      ],
      createdAt: '2024-01-15 14:30:25',
      updatedAt: '2024-01-15 14:31:00',
      metadata: {
        attemptCount: 15,
        timeWindow: '5分钟',
        targetUsers: ['<EMAIL>', '<EMAIL>']
      }
    },
    {
      id: 'evt_002',
      type: '可疑登录',
      severity: 'medium',
      status: 'investigating',
      title: '异常地理位置登录',
      description: '用户从异常地理位置登录',
      source: {
        ip: '************',
        userId: '<EMAIL>',
        location: '东京, 日本'
      },
      indicators: [
        { type: 'location', value: 'impossible_travel', confidence: 0.7 },
        { type: 'user', value: '<EMAIL>', confidence: 0.8 }
      ],
      timeline: [
        {
          timestamp: '2024-01-15 13:45:12',
          action: '事件创建',
          user: 'system',
          details: '检测到异常地理位置'
        },
        {
          timestamp: '2024-01-15 13:50:00',
          action: '分配处理',
          user: 'admin',
          details: '分配给安全团队调查'
        }
      ],
      assignedTo: 'security_team',
      createdAt: '2024-01-15 13:45:12',
      updatedAt: '2024-01-15 13:50:00',
      metadata: {
        previousLocation: '北京, 中国',
        travelTime: '2小时',
        distance: '2100公里'
      }
    }
  ];

  const responseActions: ResponseAction[] = [
    {
      id: 'block_ip',
      name: '阻止IP地址',
      description: '将源IP地址加入黑名单',
      type: 'block_ip',
      severity: ['high', 'critical'],
      autoExecute: true
    },
    {
      id: 'block_user',
      name: '阻止用户账户',
      description: '暂时禁用用户账户',
      type: 'block_user',
      severity: ['high', 'critical'],
      autoExecute: false
    },
    {
      id: 'require_mfa',
      name: '要求多因素认证',
      description: '强制用户进行多因素认证',
      type: 'require_mfa',
      severity: ['medium', 'high'],
      autoExecute: true
    },
    {
      id: 'rate_limit',
      name: '应用速率限制',
      description: '限制来源的请求频率',
      type: 'rate_limit',
      severity: ['low', 'medium'],
      autoExecute: true
    }
  ];

  useEffect(() => {
    loadSecurityEvents();
  }, [filters]);

  /**
   * 加载安全事件
   */
  const loadSecurityEvents = async () => {
    setLoading(true);
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      let filteredEvents = [...mockEvents];
      
      // 应用过滤器
      if (filters.severity) {
        filteredEvents = filteredEvents.filter(e => e.severity === filters.severity);
      }
      if (filters.status) {
        filteredEvents = filteredEvents.filter(e => e.status === filters.status);
      }
      if (filters.type) {
        filteredEvents = filteredEvents.filter(e => e.type.includes(filters.type));
      }
      
      setEvents(filteredEvents);
    } catch (error) {
      message.error('加载安全事件失败');
    } finally {
      setLoading(false);
    }
  };

  /**
   * 获取严重程度标签
   */
  const getSeverityTag = (severity: string) => {
    const config = {
      low: { color: 'green', text: '低' },
      medium: { color: 'orange', text: '中' },
      high: { color: 'red', text: '高' },
      critical: { color: 'purple', text: '严重' }
    };
    const { color, text } = config[severity as keyof typeof config];
    return <Tag color={color}>{text}</Tag>;
  };

  /**
   * 获取状态标签
   */
  const getStatusTag = (status: string) => {
    const config = {
      open: { color: 'red', text: '待处理', icon: <ExclamationCircleOutlined /> },
      investigating: { color: 'orange', text: '调查中', icon: <EyeOutlined /> },
      resolved: { color: 'green', text: '已解决', icon: <CheckCircleOutlined /> },
      false_positive: { color: 'gray', text: '误报', icon: <CloseCircleOutlined /> }
    };
    const { color, text, icon } = config[status as keyof typeof config];
    return <Tag color={color} icon={icon}>{text}</Tag>;
  };

  /**
   * 处理事件响应
   */
  const handleEventResponse = async (eventId: string, action: ResponseAction) => {
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      message.success(`已执行响应动作: ${action.name}`);
      
      // 更新事件时间线
      const updatedEvents = events.map(event => {
        if (event.id === eventId) {
          return {
            ...event,
            timeline: [
              ...event.timeline,
              {
                timestamp: dayjs().format('YYYY-MM-DD HH:mm:ss'),
                action: '响应动作',
                user: 'current_user',
                details: `执行了${action.name}`
              }
            ]
          };
        }
        return event;
      });
      
      setEvents(updatedEvents);
      setResponseVisible(false);
    } catch (error) {
      message.error('执行响应动作失败');
    }
  };

  /**
   * 更新事件状态
   */
  const updateEventStatus = async (eventId: string, status: string, resolution?: string) => {
    try {
      const updatedEvents = events.map(event => {
        if (event.id === eventId) {
          return {
            ...event,
            status: status as any,
            resolution,
            resolvedBy: status === 'resolved' ? 'current_user' : undefined,
            updatedAt: dayjs().format('YYYY-MM-DD HH:mm:ss'),
            timeline: [
              ...event.timeline,
              {
                timestamp: dayjs().format('YYYY-MM-DD HH:mm:ss'),
                action: '状态更新',
                user: 'current_user',
                details: `状态更改为: ${status}${resolution ? `, 解决方案: ${resolution}` : ''}`
              }
            ]
          };
        }
        return event;
      });
      
      setEvents(updatedEvents);
      message.success('事件状态已更新');
    } catch (error) {
      message.error('更新事件状态失败');
    }
  };

  // 表格列定义
  const columns: ColumnsType<SecurityEvent> = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 100,
      render: (id) => <Text code>{id}</Text>
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      width: 120
    },
    {
      title: '严重程度',
      dataIndex: 'severity',
      key: 'severity',
      width: 100,
      render: getSeverityTag
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 120,
      render: getStatusTag
    },
    {
      title: '标题',
      dataIndex: 'title',
      key: 'title',
      ellipsis: true
    },
    {
      title: '来源IP',
      dataIndex: ['source', 'ip'],
      key: 'sourceIp',
      width: 140,
      render: (ip) => <Text code>{ip}</Text>
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 160,
      render: (time) => dayjs(time).format('YYYY-MM-DD HH:mm:ss')
    },
    {
      title: '操作',
      key: 'actions',
      width: 200,
      render: (_, record) => (
        <Space>
          <Tooltip title="查看详情">
            <Button
              size="small"
              icon={<EyeOutlined />}
              onClick={() => {
                setSelectedEvent(record);
                setDetailsVisible(true);
              }}
            />
          </Tooltip>
          <Tooltip title="威胁响应">
            <Button
              size="small"
              icon={<ShieldOutlined />}
              onClick={() => {
                setSelectedEvent(record);
                setResponseVisible(true);
              }}
              disabled={record.status === 'resolved'}
            />
          </Tooltip>
          <Tooltip title="标记已解决">
            <Button
              size="small"
              icon={<CheckCircleOutlined />}
              onClick={() => updateEventStatus(record.id, 'resolved')}
              disabled={record.status === 'resolved'}
            />
          </Tooltip>
        </Space>
      )
    }
  ];

  return (
    <div style={{ padding: '24px' }}>
      {/* 页面标题 */}
      <Row justify="space-between" align="middle" style={{ marginBottom: '24px' }}>
        <Col>
          <Title level={2}>
            <SecurityScanOutlined /> 安全事件管理
          </Title>
        </Col>
        <Col>
          <Space>
            <Button icon={<ReloadOutlined />} onClick={loadSecurityEvents}>
              刷新
            </Button>
            <Button icon={<SettingOutlined />}>
              配置
            </Button>
          </Space>
        </Col>
      </Row>

      {/* 统计概览 */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="总事件数"
              value={events.length}
              prefix={<AlertOutlined />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="待处理"
              value={events.filter(e => e.status === 'open').length}
              valueStyle={{ color: '#cf1322' }}
              prefix={<ExclamationCircleOutlined />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="调查中"
              value={events.filter(e => e.status === 'investigating').length}
              valueStyle={{ color: '#fa8c16' }}
              prefix={<EyeOutlined />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="已解决"
              value={events.filter(e => e.status === 'resolved').length}
              valueStyle={{ color: '#3f8600' }}
              prefix={<CheckCircleOutlined />}
            />
          </Card>
        </Col>
      </Row>

      {/* 过滤器 */}
      <Card style={{ marginBottom: '24px' }}>
        <Row gutter={[16, 16]} align="middle">
          <Col>
            <Text strong>过滤条件:</Text>
          </Col>
          <Col>
            <Select
              placeholder="严重程度"
              style={{ width: 120 }}
              value={filters.severity}
              onChange={(value) => setFilters({ ...filters, severity: value })}
              allowClear
            >
              <Select.Option value="low">低</Select.Option>
              <Select.Option value="medium">中</Select.Option>
              <Select.Option value="high">高</Select.Option>
              <Select.Option value="critical">严重</Select.Option>
            </Select>
          </Col>
          <Col>
            <Select
              placeholder="状态"
              style={{ width: 120 }}
              value={filters.status}
              onChange={(value) => setFilters({ ...filters, status: value })}
              allowClear
            >
              <Select.Option value="open">待处理</Select.Option>
              <Select.Option value="investigating">调查中</Select.Option>
              <Select.Option value="resolved">已解决</Select.Option>
              <Select.Option value="false_positive">误报</Select.Option>
            </Select>
          </Col>
          <Col>
            <Input
              placeholder="事件类型"
              style={{ width: 150 }}
              value={filters.type}
              onChange={(e) => setFilters({ ...filters, type: e.target.value })}
              allowClear
            />
          </Col>
          <Col>
            <RangePicker
              value={filters.dateRange}
              onChange={(dates) => setFilters({ ...filters, dateRange: dates })}
            />
          </Col>
        </Row>
      </Card>

      {/* 事件列表 */}
      <Card>
        <Table
          columns={columns}
          dataSource={events}
          rowKey="id"
          loading={loading}
          pagination={{
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `${range[0]}-${range[1]} 共 ${total} 条`
          }}
        />
      </Card>

      {/* 事件详情抽屉 */}
      <Drawer
        title="事件详情"
        width={720}
        open={detailsVisible}
        onClose={() => setDetailsVisible(false)}
      >
        {selectedEvent && (
          <div>
            <Descriptions title="基本信息" bordered column={2}>
              <Descriptions.Item label="事件ID">{selectedEvent.id}</Descriptions.Item>
              <Descriptions.Item label="类型">{selectedEvent.type}</Descriptions.Item>
              <Descriptions.Item label="严重程度">
                {getSeverityTag(selectedEvent.severity)}
              </Descriptions.Item>
              <Descriptions.Item label="状态">
                {getStatusTag(selectedEvent.status)}
              </Descriptions.Item>
              <Descriptions.Item label="来源IP" span={2}>
                <Text code>{selectedEvent.source.ip}</Text>
              </Descriptions.Item>
              <Descriptions.Item label="创建时间" span={2}>
                {selectedEvent.createdAt}
              </Descriptions.Item>
            </Descriptions>

            <Divider />

            <Title level={4}>威胁指标</Title>
            <Table
              size="small"
              dataSource={selectedEvent.indicators}
              pagination={false}
              columns={[
                { title: '类型', dataIndex: 'type', key: 'type' },
                { title: '值', dataIndex: 'value', key: 'value' },
                {
                  title: '置信度',
                  dataIndex: 'confidence',
                  key: 'confidence',
                  render: (confidence) => (
                    <Progress percent={confidence * 100} size="small" />
                  )
                }
              ]}
            />

            <Divider />

            <Title level={4}>处理时间线</Title>
            <Timeline>
              {selectedEvent.timeline.map((item, index) => (
                <Timeline.Item key={index}>
                  <div>
                    <Text strong>{item.action}</Text>
                    <Text type="secondary" style={{ marginLeft: 8 }}>
                      {item.timestamp}
                    </Text>
                  </div>
                  <div>
                    <Text>操作人: {item.user}</Text>
                  </div>
                  <div>
                    <Text type="secondary">{item.details}</Text>
                  </div>
                </Timeline.Item>
              ))}
            </Timeline>
          </div>
        )}
      </Drawer>

      {/* 威胁响应模态框 */}
      <Modal
        title="威胁响应"
        open={responseVisible}
        onCancel={() => setResponseVisible(false)}
        footer={null}
        width={600}
      >
        {selectedEvent && (
          <div>
            <Alert
              message={`正在处理事件: ${selectedEvent.title}`}
              description={selectedEvent.description}
              type="warning"
              style={{ marginBottom: 16 }}
            />
            
            <Title level={4}>可用响应动作</Title>
            <Space direction="vertical" style={{ width: '100%' }}>
              {responseActions
                .filter(action => action.severity.includes(selectedEvent.severity))
                .map(action => (
                  <Card key={action.id} size="small">
                    <Row justify="space-between" align="middle">
                      <Col span={18}>
                        <div>
                          <Text strong>{action.name}</Text>
                          {action.autoExecute && (
                            <Tag color="blue" style={{ marginLeft: 8 }}>自动</Tag>
                          )}
                        </div>
                        <div>
                          <Text type="secondary">{action.description}</Text>
                        </div>
                      </Col>
                      <Col span={6} style={{ textAlign: 'right' }}>
                        <Button
                          type="primary"
                          onClick={() => handleEventResponse(selectedEvent.id, action)}
                        >
                          执行
                        </Button>
                      </Col>
                    </Row>
                  </Card>
                ))}
            </Space>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default SecurityEventManager;
