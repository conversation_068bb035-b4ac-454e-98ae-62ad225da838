/**
 * 语言包管理控制面板组件
 * 
 * 功能说明：
 * 1. 可视化语言包加载状态
 * 2. 提供语言包管理操作界面
 * 3. 显示缓存统计信息
 * 4. 支持语言包热更新和验证
 * 5. 提供调试和诊断工具
 */

import React, { useState } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Progress,
  Alert,
  Statistic,
  Row,
  Col,
  Modal,
  Select,
  message,
  Tooltip,
  Typography,
  Divider,
  Badge
} from 'antd';
import {
  ReloadOutlined,
  DeleteOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  InfoCircleOutlined,
  DownloadOutlined,
  CloudSyncOutlined,
  BugOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useLanguageManager, LoadingState } from '../hooks/useLanguageManager';
import { SUPPORTED_LANGUAGES, SupportedLanguage } from '../i18n';
import { formatDate, formatNumber } from '../i18n';

const { Title, Text } = Typography;
const { Option } = Select;

/**
 * 语言包管理控制面板
 */
const LanguagePackManager: React.FC = () => {
  const { t } = useTranslation();
  const {
    loadingState,
    error,
    progress,
    loadedPacks,
    loadLanguagePack,
    preloadLanguage,
    preloadAllLanguages,
    hotReload,
    clearCache,
    validatePack,
    cacheStats,
    utils
  } = useLanguageManager();

  const [selectedLanguage, setSelectedLanguage] = useState<SupportedLanguage>('zh-CN');
  const [selectedNamespace, setSelectedNamespace] = useState('common');
  const [validationResults, setValidationResults] = useState<Record<string, boolean>>({});
  const [showDebugModal, setShowDebugModal] = useState(false);

  // 可用的命名空间
  const availableNamespaces = ['common', 'auth', 'admin', 'security', 'validation', 'error'];

  /**
   * 处理语言包加载
   */
  const handleLoadPack = async () => {
    try {
      await loadLanguagePack(selectedLanguage, selectedNamespace);
      message.success(t('success.loaded'));
    } catch (err) {
      message.error(t('error.general'));
    }
  };

  /**
   * 处理语言预加载
   */
  const handlePreloadLanguage = async () => {
    try {
      await preloadLanguage(selectedLanguage, availableNamespaces);
      message.success(t('success.loaded'));
    } catch (err) {
      message.error(t('error.general'));
    }
  };

  /**
   * 处理所有语言预加载
   */
  const handlePreloadAll = async () => {
    Modal.confirm({
      title: '预加载所有语言',
      content: '这将加载所有支持语言的语言包，可能需要一些时间。是否继续？',
      onOk: async () => {
        try {
          await preloadAllLanguages(availableNamespaces);
          message.success('所有语言预加载完成');
        } catch (err) {
          message.error('预加载失败');
        }
      }
    });
  };

  /**
   * 处理热更新
   */
  const handleHotReload = async () => {
    try {
      await hotReload(selectedLanguage, selectedNamespace);
      message.success('热更新完成');
    } catch (err) {
      message.error('热更新失败');
    }
  };

  /**
   * 处理缓存清理
   */
  const handleClearCache = (language?: SupportedLanguage, namespace?: string) => {
    Modal.confirm({
      title: '清理缓存',
      content: language 
        ? `确定要清理 ${language}${namespace ? `:${namespace}` : ''} 的缓存吗？`
        : '确定要清理所有缓存吗？',
      onOk: () => {
        clearCache(language, namespace);
        message.success('缓存清理完成');
      }
    });
  };

  /**
   * 处理语言包验证
   */
  const handleValidatePack = async (language: SupportedLanguage, namespace: string) => {
    const key = `${language}:${namespace}`;
    try {
      const isValid = await validatePack(language, namespace);
      setValidationResults(prev => ({ ...prev, [key]: isValid }));
      
      if (isValid) {
        message.success(`${key} 验证通过`);
      } else {
        message.warning(`${key} 验证失败`);
      }
    } catch (err) {
      message.error(`${key} 验证出错`);
    }
  };

  // 表格列定义
  const columns = [
    {
      title: '语言',
      dataIndex: 'language',
      key: 'language',
      render: (language: SupportedLanguage) => (
        <Space>
          <span>{SUPPORTED_LANGUAGES[language].flag}</span>
          <span>{SUPPORTED_LANGUAGES[language].nativeName}</span>
          <Tag size="small">{language}</Tag>
        </Space>
      )
    },
    {
      title: '命名空间',
      dataIndex: 'namespace',
      key: 'namespace',
      render: (namespace: string) => <Tag color="blue">{namespace}</Tag>
    },
    {
      title: '加载时间',
      dataIndex: 'timestamp',
      key: 'timestamp',
      render: (timestamp: number) => formatDate(new Date(timestamp), {
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
    },
    {
      title: '状态',
      key: 'status',
      render: (record: any) => {
        const key = `${record.language}:${record.namespace}`;
        const isValid = validationResults[key];
        
        return (
          <Space>
            <Badge status="success" text="已加载" />
            {isValid !== undefined && (
              <Tag color={isValid ? 'green' : 'red'}>
                {isValid ? '验证通过' : '验证失败'}
              </Tag>
            )}
          </Space>
        );
      }
    },
    {
      title: '操作',
      key: 'actions',
      render: (record: any) => (
        <Space>
          <Tooltip title="热更新">
            <Button
              size="small"
              icon={<ReloadOutlined />}
              onClick={() => hotReload(record.language, record.namespace)}
            />
          </Tooltip>
          <Tooltip title="验证">
            <Button
              size="small"
              icon={<CheckCircleOutlined />}
              onClick={() => handleValidatePack(record.language, record.namespace)}
            />
          </Tooltip>
          <Tooltip title="清理缓存">
            <Button
              size="small"
              icon={<DeleteOutlined />}
              onClick={() => handleClearCache(record.language, record.namespace)}
            />
          </Tooltip>
        </Space>
      )
    }
  ];

  return (
    <div style={{ padding: '24px' }}>
      {/* 标题和状态 */}
      <Row gutter={[16, 16]}>
        <Col span={24}>
          <Card>
            <Space align="center" size="large">
              <CloudSyncOutlined style={{ fontSize: '32px', color: '#1890ff' }} />
              <div>
                <Title level={3} style={{ margin: 0 }}>
                  语言包管理器
                </Title>
                <Text type="secondary">
                  管理和监控应用程序的语言包加载状态
                </Text>
              </div>
              <Button
                icon={<BugOutlined />}
                onClick={() => setShowDebugModal(true)}
              >
                调试信息
              </Button>
            </Space>
          </Card>
        </Col>
      </Row>

      {/* 加载状态和错误信息 */}
      {loadingState === LoadingState.LOADING && (
        <Row gutter={[16, 16]} style={{ marginTop: '16px' }}>
          <Col span={24}>
            <Alert
              message="正在加载语言包..."
              type="info"
              showIcon
              action={
                <Progress
                  percent={progress}
                  size="small"
                  style={{ width: '200px' }}
                />
              }
            />
          </Col>
        </Row>
      )}

      {error && (
        <Row gutter={[16, 16]} style={{ marginTop: '16px' }}>
          <Col span={24}>
            <Alert
              message="加载失败"
              description={error}
              type="error"
              showIcon
              closable
            />
          </Col>
        </Row>
      )}

      {/* 统计信息 */}
      <Row gutter={[16, 16]} style={{ marginTop: '16px' }}>
        <Col xs={24} sm={8}>
          <Card>
            <Statistic
              title="已加载语言包"
              value={loadedPacks.length}
              prefix={<DownloadOutlined />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={8}>
          <Card>
            <Statistic
              title="缓存项目"
              value={cacheStats.size}
              prefix={<InfoCircleOutlined />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={8}>
          <Card>
            <Statistic
              title="缓存大小"
              value={formatNumber(cacheStats.totalSize)}
              suffix="字节"
              prefix={<CloudSyncOutlined />}
            />
          </Card>
        </Col>
      </Row>

      {/* 操作控制面板 */}
      <Row gutter={[16, 16]} style={{ marginTop: '16px' }}>
        <Col span={24}>
          <Card title="语言包操作" extra={
            <Button
              type="primary"
              icon={<DownloadOutlined />}
              onClick={handlePreloadAll}
            >
              预加载所有语言
            </Button>
          }>
            <Space wrap>
              <Select
                value={selectedLanguage}
                onChange={setSelectedLanguage}
                style={{ width: 200 }}
              >
                {Object.entries(SUPPORTED_LANGUAGES).map(([code, info]) => (
                  <Option key={code} value={code}>
                    {info.flag} {info.nativeName}
                  </Option>
                ))}
              </Select>
              
              <Select
                value={selectedNamespace}
                onChange={setSelectedNamespace}
                style={{ width: 150 }}
              >
                {availableNamespaces.map(ns => (
                  <Option key={ns} value={ns}>{ns}</Option>
                ))}
              </Select>
              
              <Button
                icon={<DownloadOutlined />}
                onClick={handleLoadPack}
                loading={loadingState === LoadingState.LOADING}
              >
                加载语言包
              </Button>
              
              <Button
                icon={<CloudSyncOutlined />}
                onClick={handlePreloadLanguage}
              >
                预加载语言
              </Button>
              
              <Button
                icon={<ReloadOutlined />}
                onClick={handleHotReload}
              >
                热更新
              </Button>
              
              <Button
                icon={<DeleteOutlined />}
                onClick={() => handleClearCache()}
                danger
              >
                清理所有缓存
              </Button>
            </Space>
          </Card>
        </Col>
      </Row>

      {/* 已加载语言包列表 */}
      <Row gutter={[16, 16]} style={{ marginTop: '16px' }}>
        <Col span={24}>
          <Card title="已加载的语言包">
            <Table
              columns={columns}
              dataSource={loadedPacks}
              rowKey={(record) => `${record.language}:${record.namespace}`}
              pagination={{
                pageSize: 10,
                showSizeChanger: true,
                showQuickJumper: true
              }}
            />
          </Card>
        </Col>
      </Row>

      {/* 调试信息模态框 */}
      <Modal
        title="调试信息"
        open={showDebugModal}
        onCancel={() => setShowDebugModal(false)}
        footer={null}
        width={800}
      >
        <Space direction="vertical" style={{ width: '100%' }}>
          <Card size="small" title="缓存统计">
            <pre>{JSON.stringify(cacheStats, null, 2)}</pre>
          </Card>
          
          <Card size="small" title="已加载语言包">
            <pre>{JSON.stringify(loadedPacks, null, 2)}</pre>
          </Card>
          
          <Card size="small" title="验证结果">
            <pre>{JSON.stringify(validationResults, null, 2)}</pre>
          </Card>
        </Space>
      </Modal>
    </div>
  );
};

export default LanguagePackManager;
