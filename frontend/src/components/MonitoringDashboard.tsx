/**
 * 系统监控仪表板组件
 * 实时显示系统性能指标和健康状态
 */

import React, { useState, useEffect, useRef } from 'react'
import {
  Card,
  Row,
  Col,
  Statistic,
  Progress,
  Table,
  Tag,
  Typography,
  Space,
  Button,
  Alert,
  Spin,
  Tooltip,
  Badge
} from 'antd'
import {
  ReloadOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  CloseCircleOutlined,
  LineChartOutlined,
  DatabaseOutlined,
  CloudServerOutlined,
  ApiOutlined
} from '@ant-design/icons'
import { Line, Area, Column } from '@ant-design/plots'
import { adminApi } from '../services/adminApi'

const { Title, Text } = Typography

interface PerformanceMetrics {
  timestamp: string
  api?: {
    count: number
    avg: number
    min: number
    max: number
    p95: number
    p99: number
  }
  database?: {
    totalQueries: number
    recentStats?: {
      count: number
      avg: number
      min: number
      max: number
    }
  }
  cache?: {
    hitCount: number
    missCount: number
    total: number
    hitRate: string
  }
  system?: {
    memory: {
      rss: string
      heapUsed: string
      heapTotal: string
    }
    cpu: {
      user: string
      system: string
    }
    uptime: string
  }
  redis?: {
    status: string
    memory?: any
    stats?: any
  }
}

interface SystemHealth {
  status: string
  timestamp: string
  uptime: string
  responseTime: string
  components: {
    database: {
      healthy: boolean
      responseTime: string
      status: string
    }
    redis: {
      healthy: boolean
      responseTime: string
      status: string
    }
    memory: {
      healthy: boolean
      heapUsed: string
      heapTotal: string
      rss: string
    }
  }
  version: string
  environment: string
}

const MonitoringDashboard: React.FC = () => {
  const [loading, setLoading] = useState(false)
  const [metrics, setMetrics] = useState<PerformanceMetrics | null>(null)
  const [health, setHealth] = useState<SystemHealth | null>(null)
  const [historicalData, setHistoricalData] = useState<any[]>([])
  const [autoRefresh, setAutoRefresh] = useState(true)
  const intervalRef = useRef<NodeJS.Timeout>()

  useEffect(() => {
    loadData()
    
    if (autoRefresh) {
      intervalRef.current = setInterval(loadData, 5000) // 每5秒刷新
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
      }
    }
  }, [autoRefresh])

  const loadData = async () => {
    if (!autoRefresh) setLoading(true)
    
    try {
      const [metricsData, healthData] = await Promise.all([
        adminApi.getPerformanceMetrics(300), // 5分钟窗口
        adminApi.getSystemHealth()
      ])

      setMetrics(metricsData)
      setHealth(healthData)

      // 更新历史数据
      if (metricsData.api) {
        setHistoricalData(prev => {
          const newData = [...prev, {
            time: new Date().toLocaleTimeString(),
            responseTime: metricsData.api?.avg || 0,
            requests: metricsData.api?.count || 0,
            memory: parseFloat(metricsData.system?.memory.heapUsed?.replace(' MB', '') || '0')
          }]
          
          // 保持最近50个数据点
          return newData.slice(-50)
        })
      }

    } catch (error) {
      console.error('加载监控数据失败:', error)
    } finally {
      if (!autoRefresh) setLoading(false)
    }
  }

  const handleRefresh = () => {
    loadData()
  }

  const toggleAutoRefresh = () => {
    setAutoRefresh(!autoRefresh)
  }

  const getHealthStatus = (healthy: boolean) => {
    return healthy ? (
      <Badge status="success" text="健康" />
    ) : (
      <Badge status="error" text="异常" />
    )
  }

  const getHealthIcon = (healthy: boolean) => {
    return healthy ? (
      <CheckCircleOutlined style={{ color: '#52c41a' }} />
    ) : (
      <CloseCircleOutlined style={{ color: '#ff4d4f' }} />
    )
  }

  const responseTimeConfig = {
    data: historicalData,
    xField: 'time',
    yField: 'responseTime',
    smooth: true,
    color: '#1890ff',
    point: {
      size: 3,
      shape: 'circle'
    },
    tooltip: {
      formatter: (datum: any) => ({
        name: '响应时间',
        value: `${datum.responseTime.toFixed(2)}ms`
      })
    }
  }

  const memoryConfig = {
    data: historicalData,
    xField: 'time',
    yField: 'memory',
    smooth: true,
    color: '#52c41a',
    areaStyle: {
      fill: 'l(270) 0:#ffffff 0.5:#7ec2f3 1:#1890ff'
    },
    tooltip: {
      formatter: (datum: any) => ({
        name: '内存使用',
        value: `${datum.memory.toFixed(2)}MB`
      })
    }
  }

  const requestsConfig = {
    data: historicalData.slice(-20), // 最近20个数据点
    xField: 'time',
    yField: 'requests',
    color: '#722ed1',
    tooltip: {
      formatter: (datum: any) => ({
        name: '请求数量',
        value: `${datum.requests}`
      })
    }
  }

  return (
    <div>
      <div style={{ 
        marginBottom: 24, 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center' 
      }}>
        <Title level={2}>系统监控</Title>
        <Space>
          <Button 
            type={autoRefresh ? 'primary' : 'default'}
            onClick={toggleAutoRefresh}
          >
            {autoRefresh ? '停止自动刷新' : '开启自动刷新'}
          </Button>
          <Button 
            icon={<ReloadOutlined />} 
            onClick={handleRefresh} 
            loading={loading}
          >
            手动刷新
          </Button>
        </Space>
      </div>

      {/* 系统健康状态 */}
      {health && (
        <Alert
          message={
            <Space>
              {getHealthIcon(health.status === 'healthy')}
              <Text strong>
                系统状态: {health.status === 'healthy' ? '健康' : '异常'}
              </Text>
              <Text type="secondary">
                运行时间: {health.uptime} | 响应时间: {health.responseTime}
              </Text>
            </Space>
          }
          type={health.status === 'healthy' ? 'success' : 'error'}
          style={{ marginBottom: 24 }}
        />
      )}

      {/* 核心指标卡片 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="API响应时间"
              value={metrics?.api?.avg || 0}
              precision={2}
              suffix="ms"
              prefix={<ApiOutlined />}
              valueStyle={{ 
                color: (metrics?.api?.avg || 0) > 1000 ? '#ff4d4f' : '#52c41a' 
              }}
            />
            <div style={{ marginTop: 8 }}>
              <Text type="secondary" style={{ fontSize: 12 }}>
                P95: {metrics?.api?.p95 || 0}ms | P99: {metrics?.api?.p99 || 0}ms
              </Text>
            </div>
          </Card>
        </Col>

        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="数据库查询"
              value={metrics?.database?.recentStats?.avg || 0}
              precision={2}
              suffix="ms"
              prefix={<DatabaseOutlined />}
              valueStyle={{ 
                color: (metrics?.database?.recentStats?.avg || 0) > 100 ? '#ff4d4f' : '#52c41a' 
              }}
            />
            <div style={{ marginTop: 8 }}>
              <Text type="secondary" style={{ fontSize: 12 }}>
                总查询: {metrics?.database?.totalQueries || 0}
              </Text>
            </div>
          </Card>
        </Col>

        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="缓存命中率"
              value={parseFloat(metrics?.cache?.hitRate?.replace('%', '') || '0')}
              precision={2}
              suffix="%"
              prefix={<CloudServerOutlined />}
              valueStyle={{ 
                color: parseFloat(metrics?.cache?.hitRate?.replace('%', '') || '0') > 80 ? '#52c41a' : '#ff4d4f' 
              }}
            />
            <div style={{ marginTop: 8 }}>
              <Text type="secondary" style={{ fontSize: 12 }}>
                命中: {metrics?.cache?.hitCount || 0} | 未命中: {metrics?.cache?.missCount || 0}
              </Text>
            </div>
          </Card>
        </Col>

        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="内存使用"
              value={parseFloat(metrics?.system?.memory.heapUsed?.replace(' MB', '') || '0')}
              precision={2}
              suffix="MB"
              prefix={<LineChartOutlined />}
              valueStyle={{ 
                color: parseFloat(metrics?.system?.memory.heapUsed?.replace(' MB', '') || '0') > 500 ? '#ff4d4f' : '#52c41a' 
              }}
            />
            <div style={{ marginTop: 8 }}>
              <Text type="secondary" style={{ fontSize: 12 }}>
                总计: {metrics?.system?.memory.heapTotal || '0 MB'}
              </Text>
            </div>
          </Card>
        </Col>
      </Row>

      {/* 组件健康状态 */}
      {health && (
        <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
          <Col xs={24} lg={8}>
            <Card title="数据库状态" size="small">
              <Space direction="vertical" style={{ width: '100%' }}>
                {getHealthStatus(health.components.database.healthy)}
                <Text type="secondary">
                  响应时间: {health.components.database.responseTime}
                </Text>
                <Text type="secondary">
                  状态: {health.components.database.status}
                </Text>
              </Space>
            </Card>
          </Col>

          <Col xs={24} lg={8}>
            <Card title="Redis状态" size="small">
              <Space direction="vertical" style={{ width: '100%' }}>
                {getHealthStatus(health.components.redis.healthy)}
                <Text type="secondary">
                  响应时间: {health.components.redis.responseTime}
                </Text>
                <Text type="secondary">
                  状态: {health.components.redis.status}
                </Text>
              </Space>
            </Card>
          </Col>

          <Col xs={24} lg={8}>
            <Card title="内存状态" size="small">
              <Space direction="vertical" style={{ width: '100%' }}>
                {getHealthStatus(health.components.memory.healthy)}
                <Text type="secondary">
                  堆内存: {health.components.memory.heapUsed}
                </Text>
                <Text type="secondary">
                  RSS: {health.components.memory.rss}
                </Text>
              </Space>
            </Card>
          </Col>
        </Row>
      )}

      {/* 性能图表 */}
      <Row gutter={[16, 16]}>
        <Col xs={24} lg={12}>
          <Card title="API响应时间趋势" size="small">
            {historicalData.length > 0 ? (
              <Line {...responseTimeConfig} height={200} />
            ) : (
              <div style={{ height: 200, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                <Text type="secondary">暂无数据</Text>
              </div>
            )}
          </Card>
        </Col>

        <Col xs={24} lg={12}>
          <Card title="内存使用趋势" size="small">
            {historicalData.length > 0 ? (
              <Area {...memoryConfig} height={200} />
            ) : (
              <div style={{ height: 200, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                <Text type="secondary">暂无数据</Text>
              </div>
            )}
          </Card>
        </Col>

        <Col xs={24}>
          <Card title="请求量统计" size="small">
            {historicalData.length > 0 ? (
              <Column {...requestsConfig} height={200} />
            ) : (
              <div style={{ height: 200, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                <Text type="secondary">暂无数据</Text>
              </div>
            )}
          </Card>
        </Col>
      </Row>
    </div>
  )
}

export default MonitoringDashboard
