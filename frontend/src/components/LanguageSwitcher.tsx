/**
 * 语言切换组件
 * 
 * 功能说明：
 * 1. 提供语言选择下拉菜单
 * 2. 显示当前选中的语言
 * 3. 支持语言图标和本地化名称
 * 4. 响应式设计，适配移动端
 * 5. 支持键盘导航和无障碍访问
 */

import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Select, Button, Dropdown, Space, Typography } from 'antd';
import { GlobalOutlined, DownOutlined } from '@ant-design/icons';
import type { MenuProps } from 'antd';
import { 
  SUPPORTED_LANGUAGES, 
  SupportedLanguage, 
  changeLanguage, 
  getCurrentLanguage,
  getLanguageInfo 
} from '../i18n';

const { Text } = Typography;

interface LanguageSwitcherProps {
  /**
   * 显示模式
   * - 'dropdown': 下拉菜单模式（默认）
   * - 'select': 选择框模式
   * - 'button': 按钮模式
   */
  mode?: 'dropdown' | 'select' | 'button';
  
  /**
   * 尺寸
   */
  size?: 'small' | 'middle' | 'large';
  
  /**
   * 是否显示语言名称
   */
  showName?: boolean;
  
  /**
   * 是否显示国旗图标
   */
  showFlag?: boolean;
  
  /**
   * 是否显示本地化名称
   */
  showNativeName?: boolean;
  
  /**
   * 自定义样式类名
   */
  className?: string;
  
  /**
   * 语言切换回调
   */
  onChange?: (language: SupportedLanguage) => void;
}

/**
 * 语言切换组件
 */
const LanguageSwitcher: React.FC<LanguageSwitcherProps> = ({
  mode = 'dropdown',
  size = 'middle',
  showName = true,
  showFlag = true,
  showNativeName = false,
  className,
  onChange
}) => {
  const { t } = useTranslation();
  const [currentLang, setCurrentLang] = useState<SupportedLanguage>(getCurrentLanguage());
  const [loading, setLoading] = useState(false);

  // 监听语言变化
  useEffect(() => {
    const handleLanguageChange = () => {
      setCurrentLang(getCurrentLanguage());
    };

    window.addEventListener('languageChanged', handleLanguageChange);
    return () => {
      window.removeEventListener('languageChanged', handleLanguageChange);
    };
  }, []);

  /**
   * 处理语言切换
   */
  const handleLanguageChange = async (language: SupportedLanguage) => {
    if (language === currentLang) return;

    setLoading(true);
    try {
      await changeLanguage(language);
      setCurrentLang(language);
      onChange?.(language);
      
      // 显示切换成功提示
      console.log(`语言已切换为: ${getLanguageInfo(language).nativeName}`);
    } catch (error) {
      console.error('语言切换失败:', error);
    } finally {
      setLoading(false);
    }
  };

  /**
   * 获取语言显示文本
   */
  const getLanguageText = (lang: SupportedLanguage, includeNative = false) => {
    const info = getLanguageInfo(lang);
    const parts = [];
    
    if (showFlag) {
      parts.push(info.flag);
    }
    
    if (showName) {
      parts.push(info.name);
    }
    
    if (includeNative && showNativeName && info.nativeName !== info.name) {
      parts.push(`(${info.nativeName})`);
    }
    
    return parts.join(' ');
  };

  /**
   * 渲染选择框模式
   */
  const renderSelectMode = () => {
    const options = Object.entries(SUPPORTED_LANGUAGES).map(([code, info]) => ({
      label: getLanguageText(code as SupportedLanguage, true),
      value: code,
      key: code
    }));

    return (
      <Select
        value={currentLang}
        onChange={handleLanguageChange}
        options={options}
        size={size}
        className={className}
        loading={loading}
        suffixIcon={<GlobalOutlined />}
        placeholder={t('i18n.select_language')}
        aria-label={t('i18n.language_settings')}
      />
    );
  };

  /**
   * 渲染下拉菜单模式
   */
  const renderDropdownMode = () => {
    const menuItems: MenuProps['items'] = Object.entries(SUPPORTED_LANGUAGES).map(([code, info]) => ({
      key: code,
      label: (
        <Space>
          {showFlag && <span>{info.flag}</span>}
          <span>
            {showName && info.name}
            {showNativeName && info.nativeName !== info.name && (
              <Text type="secondary" style={{ marginLeft: 4 }}>
                ({info.nativeName})
              </Text>
            )}
          </span>
        </Space>
      ),
      onClick: () => handleLanguageChange(code as SupportedLanguage)
    }));

    const menu: MenuProps = {
      items: menuItems,
      selectedKeys: [currentLang]
    };

    return (
      <Dropdown 
        menu={menu} 
        trigger={['click']} 
        placement="bottomRight"
        className={className}
      >
        <Button 
          size={size} 
          loading={loading}
          icon={<GlobalOutlined />}
          aria-label={t('i18n.language_settings')}
        >
          <Space>
            {getLanguageText(currentLang)}
            <DownOutlined />
          </Space>
        </Button>
      </Dropdown>
    );
  };

  /**
   * 渲染按钮模式
   */
  const renderButtonMode = () => {
    return (
      <Space.Compact className={className}>
        {Object.entries(SUPPORTED_LANGUAGES).map(([code, info]) => (
          <Button
            key={code}
            size={size}
            type={code === currentLang ? 'primary' : 'default'}
            loading={loading && code === currentLang}
            onClick={() => handleLanguageChange(code as SupportedLanguage)}
            aria-label={`${t('i18n.change_language')}: ${info.nativeName}`}
          >
            {showFlag && info.flag}
            {showName && (
              <span style={{ marginLeft: showFlag ? 4 : 0 }}>
                {showNativeName ? info.nativeName : info.name}
              </span>
            )}
          </Button>
        ))}
      </Space.Compact>
    );
  };

  // 根据模式渲染对应组件
  switch (mode) {
    case 'select':
      return renderSelectMode();
    case 'button':
      return renderButtonMode();
    case 'dropdown':
    default:
      return renderDropdownMode();
  }
};

export default LanguageSwitcher;
