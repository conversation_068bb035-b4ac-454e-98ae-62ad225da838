/**
 * 响应式布局样式
 * 提供桌面和移动端的统一样式设计
 */

/* 主布局容器 */
.responsive-layout {
  min-height: 100vh;
  background: #f0f2f5;
}

.main-layout {
  background: #f0f2f5;
}

/* 头部样式 */
.responsive-header {
  background: #ffffff;
  padding: 0 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: sticky;
  top: 0;
  z-index: 100;
  height: 64px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.header-right {
  display: flex;
  align-items: center;
}

/* 菜单触发按钮 */
.menu-trigger,
.mobile-menu-trigger {
  font-size: 18px;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
  transition: all 0.2s;
}

.menu-trigger:hover,
.mobile-menu-trigger:hover {
  background: rgba(0, 0, 0, 0.06);
}

/* Logo 样式 */
.logo {
  display: flex;
  align-items: center;
  height: 32px;
}

.logo-image {
  height: 32px;
  width: auto;
  object-fit: contain;
}

.logo-text {
  font-size: 18px;
  font-weight: 600;
  color: #1890ff;
  white-space: nowrap;
}

/* 用户信息区域 */
.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
}

.user-info:hover {
  background: rgba(0, 0, 0, 0.06);
}

.user-avatar {
  flex-shrink: 0;
}

.user-name {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.85);
  max-width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 通知按钮 */
.notification-btn {
  font-size: 16px;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
  transition: all 0.2s;
}

.notification-btn:hover {
  background: rgba(0, 0, 0, 0.06);
}

/* 桌面端侧边栏 */
.desktop-sider {
  background: #ffffff;
  border-right: 1px solid #f0f0f0;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.06);
  position: relative;
  z-index: 10;
}

.sider-content {
  height: 100%;
  padding-top: 16px;
}

/* 主内容区域 */
.main-content {
  margin: 0;
  padding: 0;
  background: #f0f2f5;
  min-height: calc(100vh - 64px);
  overflow: auto;
}

.content-wrapper {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

/* 菜单样式优化 */
.ant-menu-light {
  background: transparent;
}

.ant-menu-item {
  margin: 4px 8px;
  border-radius: 6px;
  height: 40px;
  line-height: 40px;
  display: flex;
  align-items: center;
}

.ant-menu-item-selected {
  background: #e6f7ff !important;
  color: #1890ff !important;
}

.ant-menu-item-selected::after {
  display: none;
}

.ant-menu-item:hover {
  background: rgba(0, 0, 0, 0.06);
  color: #1890ff;
}

.ant-menu-item-icon {
  font-size: 16px;
  margin-right: 12px;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .responsive-header {
    padding: 0 12px;
  }
  
  .header-left {
    gap: 12px;
  }
  
  .logo-text {
    font-size: 16px;
  }
  
  .content-wrapper {
    padding: 16px;
  }
  
  .user-name {
    display: none;
  }
}

@media (max-width: 480px) {
  .responsive-header {
    padding: 0 8px;
  }
  
  .header-left {
    gap: 8px;
  }
  
  .logo-text {
    font-size: 14px;
    max-width: 120px;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  
  .content-wrapper {
    padding: 12px;
  }
}

/* 抽屉菜单样式 */
.ant-drawer-body {
  padding: 0;
}

.ant-drawer-header {
  border-bottom: 1px solid #f0f0f0;
  padding: 16px 24px;
}

.ant-drawer-title {
  font-size: 16px;
  font-weight: 600;
  color: #1890ff;
}

/* 加载状态 */
.layout-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background: #f0f2f5;
}

.layout-loading .ant-spin {
  font-size: 24px;
}

/* 暗色主题支持 */
@media (prefers-color-scheme: dark) {
  .responsive-layout {
    background: #141414;
  }
  
  .main-layout {
    background: #141414;
  }
  
  .responsive-header {
    background: #1f1f1f;
    border-bottom-color: #303030;
  }
  
  .desktop-sider {
    background: #1f1f1f;
    border-right-color: #303030;
  }
  
  .main-content {
    background: #141414;
  }
  
  .logo-text {
    color: #1890ff;
  }
  
  .user-name {
    color: rgba(255, 255, 255, 0.85);
  }
  
  .menu-trigger:hover,
  .mobile-menu-trigger:hover,
  .notification-btn:hover,
  .user-info:hover {
    background: rgba(255, 255, 255, 0.08);
  }
  
  .ant-menu-item:hover {
    background: rgba(255, 255, 255, 0.08);
  }
}

/* 动画效果 */
.responsive-layout * {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 滚动条样式 */
.main-content::-webkit-scrollbar {
  width: 6px;
}

.main-content::-webkit-scrollbar-track {
  background: transparent;
}

.main-content::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

.main-content::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

/* 焦点可访问性 */
.menu-trigger:focus,
.mobile-menu-trigger:focus,
.notification-btn:focus,
.user-info:focus {
  outline: 2px solid #1890ff;
  outline-offset: 2px;
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .responsive-header {
    border-bottom: 2px solid #000000;
  }
  
  .desktop-sider {
    border-right: 2px solid #000000;
  }
  
  .ant-menu-item-selected {
    background: #000000 !important;
    color: #ffffff !important;
  }
}
