/**
 * 响应式布局组件
 * 提供适配桌面和移动端的统一布局框架
 */

import React, { useState, useEffect } from 'react'
import { Layout, Menu, Drawer, Button, Avatar, Dropdown, Space, Badge } from 'antd'
import {
  MenuOutlined,
  UserOutlined,
  SettingOutlined,
  LogoutOutlined,
  BellOutlined,
  DashboardOutlined,
  SecurityScanOutlined,
  KeyOutlined
} from '@ant-design/icons'
import { useNavigate, useLocation } from 'react-router-dom'
import { useAuthStore } from '../../stores/authStore'
import { useUIConfig } from '../../hooks/useUIConfig'
import './ResponsiveLayout.css'

const { Header, Sider, Content } = Layout

interface ResponsiveLayoutProps {
  children: React.ReactNode
}

interface MenuItem {
  key: string
  icon: React.ReactNode
  label: string
  path: string
  adminOnly?: boolean
}

/**
 * 响应式布局组件
 */
export const ResponsiveLayout: React.FC<ResponsiveLayoutProps> = ({ children }) => {
  const [collapsed, setCollapsed] = useState(false)
  const [mobileDrawerVisible, setMobileDrawerVisible] = useState(false)
  const [isMobile, setIsMobile] = useState(false)
  
  const navigate = useNavigate()
  const location = useLocation()
  const { user, logout } = useAuthStore()
  const { config } = useUIConfig()

  // 检测屏幕尺寸
  useEffect(() => {
    const checkScreenSize = () => {
      const mobile = window.innerWidth < 768
      setIsMobile(mobile)
      if (mobile) {
        setCollapsed(true)
      }
    }

    checkScreenSize()
    window.addEventListener('resize', checkScreenSize)
    return () => window.removeEventListener('resize', checkScreenSize)
  }, [])

  // 菜单项配置
  const menuItems: MenuItem[] = [
    {
      key: 'dashboard',
      icon: <DashboardOutlined />,
      label: '仪表板',
      path: '/dashboard'
    },
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: '个人资料',
      path: '/profile'
    },
    {
      key: 'security',
      icon: <SecurityScanOutlined />,
      label: '安全设置',
      path: '/security'
    },
    {
      key: 'admin',
      icon: <KeyOutlined />,
      label: '管理控制台',
      path: '/admin',
      adminOnly: true
    }
  ]

  // 过滤菜单项（根据用户权限）
  const filteredMenuItems = menuItems.filter(item => {
    if (item.adminOnly) {
      return user?.roles?.includes('admin') || user?.roles?.includes('super_admin')
    }
    return true
  })

  // 获取当前选中的菜单项
  const selectedKey = filteredMenuItems.find(item => 
    location.pathname.startsWith(item.path)
  )?.key || 'dashboard'

  // 处理菜单点击
  const handleMenuClick = (key: string) => {
    const item = menuItems.find(item => item.key === key)
    if (item) {
      navigate(item.path)
      if (isMobile) {
        setMobileDrawerVisible(false)
      }
    }
  }

  // 用户下拉菜单
  const userMenuItems = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: '个人资料',
      onClick: () => navigate('/profile')
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: '设置',
      onClick: () => navigate('/settings')
    },
    {
      type: 'divider' as const
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
      onClick: () => {
        logout()
        navigate('/login')
      }
    }
  ]

  // 渲染侧边栏菜单
  const renderMenu = () => (
    <Menu
      theme="light"
      mode="inline"
      selectedKeys={[selectedKey]}
      style={{ borderRight: 0 }}
      items={filteredMenuItems.map(item => ({
        key: item.key,
        icon: item.icon,
        label: item.label,
        onClick: () => handleMenuClick(item.key)
      }))}
    />
  )

  // 渲染头部
  const renderHeader = () => (
    <Header className="responsive-header">
      <div className="header-left">
        {isMobile ? (
          <Button
            type="text"
            icon={<MenuOutlined />}
            onClick={() => setMobileDrawerVisible(true)}
            className="mobile-menu-trigger"
          />
        ) : (
          <Button
            type="text"
            icon={<MenuOutlined />}
            onClick={() => setCollapsed(!collapsed)}
            className="menu-trigger"
          />
        )}
        
        <div className="logo">
          {config?.logo ? (
            <img src={config.logo} alt={config.title} className="logo-image" />
          ) : (
            <span className="logo-text">{config?.title || '身份提供商'}</span>
          )}
        </div>
      </div>

      <div className="header-right">
        <Space size="middle">
          {/* 通知铃铛 */}
          <Badge count={0} size="small">
            <Button
              type="text"
              icon={<BellOutlined />}
              className="notification-btn"
            />
          </Badge>

          {/* 用户头像和下拉菜单 */}
          <Dropdown
            menu={{ items: userMenuItems }}
            placement="bottomRight"
            trigger={['click']}
          >
            <div className="user-info">
              <Avatar
                size="small"
                icon={<UserOutlined />}
                src={user?.avatar}
                className="user-avatar"
              />
              {!isMobile && (
                <span className="user-name">
                  {user?.nickname || user?.firstName || user?.email}
                </span>
              )}
            </div>
          </Dropdown>
        </Space>
      </div>
    </Header>
  )

  return (
    <Layout className="responsive-layout">
      {/* 桌面端侧边栏 */}
      {!isMobile && (
        <Sider
          trigger={null}
          collapsible
          collapsed={collapsed}
          width={240}
          collapsedWidth={80}
          className="desktop-sider"
        >
          <div className="sider-content">
            {renderMenu()}
          </div>
        </Sider>
      )}

      {/* 移动端抽屉菜单 */}
      {isMobile && (
        <Drawer
          title={config?.title || '身份提供商'}
          placement="left"
          onClose={() => setMobileDrawerVisible(false)}
          open={mobileDrawerVisible}
          bodyStyle={{ padding: 0 }}
          width={280}
        >
          {renderMenu()}
        </Drawer>
      )}

      <Layout className="main-layout">
        {renderHeader()}
        
        <Content className="main-content">
          <div className="content-wrapper">
            {children}
          </div>
        </Content>
      </Layout>
    </Layout>
  )
}

export default ResponsiveLayout
