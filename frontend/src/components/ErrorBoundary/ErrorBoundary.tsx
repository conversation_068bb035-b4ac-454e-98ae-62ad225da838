/**
 * 错误边界组件
 * 捕获和处理React组件错误，提供友好的错误界面
 */

import React, { Component, ErrorInfo, ReactNode } from 'react'
import { Result, Button, Typography, Collapse, Space, Alert } from 'antd'
import { 
  BugOutlined, 
  ReloadOutlined, 
  HomeOutlined,
  WarningOutlined,
  InfoCircleOutlined
} from '@ant-design/icons'
import './ErrorBoundary.css'

const { Text, Paragraph } = Typography
const { Panel } = Collapse

interface Props {
  children: ReactNode
  fallback?: ReactNode
  onError?: (error: Error, errorInfo: ErrorInfo) => void
}

interface State {
  hasError: boolean
  error: Error | null
  errorInfo: ErrorInfo | null
  errorId: string
}

/**
 * 错误边界组件
 */
export class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props)
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: ''
    }
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    // 生成错误ID用于追踪
    const errorId = `ERR_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    
    return {
      hasError: true,
      error,
      errorId
    }
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // 更新错误信息
    this.setState({
      error,
      errorInfo
    })

    // 调用外部错误处理函数
    if (this.props.onError) {
      this.props.onError(error, errorInfo)
    }

    // 记录错误到控制台
    console.error('ErrorBoundary caught an error:', error, errorInfo)

    // 发送错误报告到监控服务
    this.reportError(error, errorInfo)
  }

  /**
   * 发送错误报告
   */
  private reportError = (error: Error, errorInfo: ErrorInfo) => {
    try {
      // 这里可以集成错误监控服务，如 Sentry
      const errorReport = {
        errorId: this.state.errorId,
        message: error.message,
        stack: error.stack,
        componentStack: errorInfo.componentStack,
        timestamp: new Date().toISOString(),
        userAgent: navigator.userAgent,
        url: window.location.href
      }

      // 发送到错误监控服务
      // 示例：Sentry.captureException(error, { extra: errorReport })
      
      console.log('Error report:', errorReport)
    } catch (reportError) {
      console.error('Failed to report error:', reportError)
    }
  }

  /**
   * 重置错误状态
   */
  private handleReset = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: ''
    })
  }

  /**
   * 刷新页面
   */
  private handleReload = () => {
    window.location.reload()
  }

  /**
   * 返回首页
   */
  private handleGoHome = () => {
    window.location.href = '/'
  }

  /**
   * 复制错误信息
   */
  private handleCopyError = () => {
    const { error, errorInfo, errorId } = this.state
    const errorText = `
错误ID: ${errorId}
错误信息: ${error?.message}
错误堆栈: ${error?.stack}
组件堆栈: ${errorInfo?.componentStack}
时间: ${new Date().toISOString()}
URL: ${window.location.href}
用户代理: ${navigator.userAgent}
    `.trim()

    navigator.clipboard.writeText(errorText).then(() => {
      console.log('错误信息已复制到剪贴板')
    }).catch(() => {
      console.error('复制失败')
    })
  }

  render() {
    if (this.state.hasError) {
      // 如果提供了自定义fallback，使用它
      if (this.props.fallback) {
        return this.props.fallback
      }

      const { error, errorInfo, errorId } = this.state
      const isDevelopment = process.env.NODE_ENV === 'development'

      return (
        <div className="error-boundary">
          <Result
            status="error"
            icon={<BugOutlined />}
            title="页面出现错误"
            subTitle={`抱歉，页面遇到了意外错误。错误ID: ${errorId}`}
            extra={
              <Space direction="vertical" size="middle">
                <Space wrap>
                  <Button 
                    type="primary" 
                    icon={<ReloadOutlined />}
                    onClick={this.handleReset}
                  >
                    重试
                  </Button>
                  <Button 
                    icon={<ReloadOutlined />}
                    onClick={this.handleReload}
                  >
                    刷新页面
                  </Button>
                  <Button 
                    icon={<HomeOutlined />}
                    onClick={this.handleGoHome}
                  >
                    返回首页
                  </Button>
                </Space>

                {/* 开发环境显示详细错误信息 */}
                {isDevelopment && error && (
                  <div className="error-details">
                    <Alert
                      message="开发环境错误详情"
                      description="以下信息仅在开发环境显示"
                      type="warning"
                      icon={<WarningOutlined />}
                      showIcon
                    />
                    
                    <Collapse ghost>
                      <Panel 
                        header={
                          <Space>
                            <InfoCircleOutlined />
                            <Text strong>错误详情</Text>
                          </Space>
                        } 
                        key="error-details"
                      >
                        <div className="error-content">
                          <div className="error-section">
                            <Text strong>错误消息:</Text>
                            <Paragraph code copyable>
                              {error.message}
                            </Paragraph>
                          </div>

                          <div className="error-section">
                            <Text strong>错误堆栈:</Text>
                            <Paragraph code copyable>
                              <pre>{error.stack}</pre>
                            </Paragraph>
                          </div>

                          {errorInfo && (
                            <div className="error-section">
                              <Text strong>组件堆栈:</Text>
                              <Paragraph code copyable>
                                <pre>{errorInfo.componentStack}</pre>
                              </Paragraph>
                            </div>
                          )}

                          <div className="error-section">
                            <Button 
                              type="dashed" 
                              size="small"
                              onClick={this.handleCopyError}
                            >
                              复制完整错误信息
                            </Button>
                          </div>
                        </div>
                      </Panel>
                    </Collapse>
                  </div>
                )}
              </Space>
            }
          />
        </div>
      )
    }

    return this.props.children
  }
}

/**
 * 简化的错误边界Hook
 */
export const withErrorBoundary = <P extends object>(
  Component: React.ComponentType<P>,
  fallback?: ReactNode,
  onError?: (error: Error, errorInfo: ErrorInfo) => void
) => {
  const WrappedComponent = (props: P) => (
    <ErrorBoundary fallback={fallback} onError={onError}>
      <Component {...props} />
    </ErrorBoundary>
  )

  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`
  
  return WrappedComponent
}

export default ErrorBoundary
