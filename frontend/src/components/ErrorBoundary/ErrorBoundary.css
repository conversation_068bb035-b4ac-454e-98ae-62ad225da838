/**
 * 错误边界组件样式
 */

.error-boundary {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f0f2f5;
  padding: 20px;
}

.error-details {
  max-width: 800px;
  width: 100%;
  margin-top: 24px;
}

.error-content {
  background: #fafafa;
  border-radius: 6px;
  padding: 16px;
  border: 1px solid #d9d9d9;
}

.error-section {
  margin-bottom: 16px;
}

.error-section:last-child {
  margin-bottom: 0;
}

.error-section pre {
  background: #f5f5f5;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  padding: 12px;
  font-size: 12px;
  line-height: 1.4;
  overflow-x: auto;
  white-space: pre-wrap;
  word-break: break-word;
  max-height: 300px;
  overflow-y: auto;
}

/* Result组件自定义样式 */
.error-boundary .ant-result {
  padding: 48px 32px;
}

.error-boundary .ant-result-icon {
  margin-bottom: 24px;
}

.error-boundary .ant-result-icon .anticon {
  font-size: 72px;
  color: #ff4d4f;
}

.error-boundary .ant-result-title {
  color: rgba(0, 0, 0, 0.85);
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 16px;
}

.error-boundary .ant-result-subtitle {
  color: rgba(0, 0, 0, 0.65);
  font-size: 14px;
  line-height: 1.6;
  margin-bottom: 24px;
}

/* 按钮组样式 */
.error-boundary .ant-space-vertical {
  width: 100%;
}

.error-boundary .ant-btn {
  height: 40px;
  border-radius: 6px;
  font-weight: 400;
}

.error-boundary .ant-btn-primary {
  background: #1890ff;
  border-color: #1890ff;
}

.error-boundary .ant-btn-primary:hover {
  background: #40a9ff;
  border-color: #40a9ff;
}

/* 折叠面板样式 */
.error-boundary .ant-collapse {
  background: transparent;
  border: none;
}

.error-boundary .ant-collapse-item {
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  margin-bottom: 16px;
}

.error-boundary .ant-collapse-header {
  padding: 12px 16px;
  background: #fafafa;
  border-radius: 6px 6px 0 0;
}

.error-boundary .ant-collapse-content {
  border-top: 1px solid #d9d9d9;
}

.error-boundary .ant-collapse-content-box {
  padding: 16px;
}

/* 警告提示样式 */
.error-boundary .ant-alert {
  margin-bottom: 16px;
  border-radius: 6px;
}

.error-boundary .ant-alert-warning {
  background: #fffbe6;
  border: 1px solid #ffe58f;
}

/* 代码块样式 */
.error-boundary .ant-typography-copy {
  margin-left: 8px;
}

.error-boundary .ant-typography code {
  background: #f5f5f5;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  padding: 2px 6px;
  font-size: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .error-boundary {
    padding: 16px;
  }
  
  .error-boundary .ant-result {
    padding: 32px 16px;
  }
  
  .error-boundary .ant-result-icon .anticon {
    font-size: 48px;
  }
  
  .error-boundary .ant-result-title {
    font-size: 20px;
  }
  
  .error-boundary .ant-result-subtitle {
    font-size: 13px;
  }
  
  .error-details {
    margin-top: 16px;
  }
  
  .error-content {
    padding: 12px;
  }
  
  .error-section pre {
    font-size: 11px;
    padding: 8px;
    max-height: 200px;
  }
}

@media (max-width: 480px) {
  .error-boundary {
    padding: 12px;
  }
  
  .error-boundary .ant-result {
    padding: 24px 12px;
  }
  
  .error-boundary .ant-result-icon .anticon {
    font-size: 36px;
  }
  
  .error-boundary .ant-result-title {
    font-size: 18px;
  }
  
  .error-boundary .ant-result-subtitle {
    font-size: 12px;
  }
  
  .error-boundary .ant-btn {
    height: 36px;
    font-size: 13px;
  }
  
  .error-boundary .ant-space-item {
    width: 100%;
  }
  
  .error-boundary .ant-btn {
    width: 100%;
  }
}

/* 暗色主题支持 */
@media (prefers-color-scheme: dark) {
  .error-boundary {
    background: #141414;
  }
  
  .error-content {
    background: #1f1f1f;
    border-color: #434343;
  }
  
  .error-section pre {
    background: #262626;
    border-color: #434343;
    color: #ffffff;
  }
  
  .error-boundary .ant-result-title {
    color: rgba(255, 255, 255, 0.85);
  }
  
  .error-boundary .ant-result-subtitle {
    color: rgba(255, 255, 255, 0.65);
  }
  
  .error-boundary .ant-collapse-item {
    border-color: #434343;
  }
  
  .error-boundary .ant-collapse-header {
    background: #262626;
  }
  
  .error-boundary .ant-collapse-content {
    border-color: #434343;
  }
  
  .error-boundary .ant-alert-warning {
    background: #2d2a1f;
    border-color: #594214;
  }
  
  .error-boundary .ant-typography code {
    background: #262626;
    border-color: #434343;
    color: #ffffff;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .error-boundary .ant-result-icon .anticon {
    color: #d32f2f;
  }
  
  .error-boundary .ant-result-title {
    color: #000000;
  }
  
  .error-boundary .ant-result-subtitle {
    color: #424242;
  }
  
  .error-content {
    border: 2px solid #000000;
  }
  
  .error-section pre {
    border: 2px solid #000000;
    background: #ffffff;
    color: #000000;
  }
  
  .error-boundary .ant-btn {
    border: 2px solid #000000;
  }
  
  .error-boundary .ant-btn-primary {
    background: #000000;
    border-color: #000000;
    color: #ffffff;
  }
}

/* 减少动画效果 */
@media (prefers-reduced-motion: reduce) {
  .error-boundary * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* 打印样式 */
@media print {
  .error-boundary {
    background: white;
    color: black;
  }
  
  .error-boundary .ant-btn {
    display: none;
  }
  
  .error-section pre {
    background: white;
    border: 1px solid black;
    color: black;
  }
}
