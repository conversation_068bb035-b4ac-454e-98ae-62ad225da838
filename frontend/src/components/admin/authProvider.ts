/**
 * React Admin 认证提供者
 * 集成现有的认证系统，提供React Admin所需的认证接口
 */

import { AuthProvider } from 'react-admin'
import { useAuthStore } from '../../stores/authStore'

/**
 * React Admin 认证提供者实现
 */
export const authProvider: AuthProvider = {
  /**
   * 登录处理
   * React Admin会调用此方法进行登录
   */
  login: async ({ username, password }) => {
    try {
      // 使用现有的认证store进行登录
      const { login } = useAuthStore.getState()
      const result = await login(username, password)
      
      if (result.success) {
        return Promise.resolve()
      } else {
        return Promise.reject(new Error(result.error || '登录失败'))
      }
    } catch (error) {
      return Promise.reject(error)
    }
  },

  /**
   * 登出处理
   */
  logout: async () => {
    try {
      const { logout } = useAuthStore.getState()
      await logout()
      return Promise.resolve()
    } catch (error) {
      return Promise.reject(error)
    }
  },

  /**
   * 检查认证状态
   */
  checkAuth: async () => {
    try {
      const { user, accessToken } = useAuthStore.getState()
      
      // 检查是否已登录且有管理员权限
      if (!user || !accessToken) {
        return Promise.reject(new Error('未登录'))
      }
      
      // 检查管理员权限
      const isAdmin = user.roles?.includes('admin') || user.roles?.includes('super_admin')
      if (!isAdmin) {
        return Promise.reject(new Error('没有管理员权限'))
      }
      
      return Promise.resolve()
    } catch (error) {
      return Promise.reject(error)
    }
  },

  /**
   * 检查错误状态
   * 当API返回401或403时，React Admin会调用此方法
   */
  checkError: async (error) => {
    const status = error.status
    
    if (status === 401 || status === 403) {
      // 尝试刷新令牌
      const { refreshAccessToken, clearAuth } = useAuthStore.getState()
      const refreshed = await refreshAccessToken()
      
      if (!refreshed) {
        // 刷新失败，清除认证状态
        clearAuth()
        return Promise.reject(new Error('认证已过期，请重新登录'))
      }
      
      return Promise.resolve()
    }
    
    return Promise.resolve()
  },

  /**
   * 获取权限信息
   */
  getPermissions: async () => {
    try {
      const { user } = useAuthStore.getState()
      
      if (!user) {
        return Promise.reject(new Error('未登录'))
      }
      
      // 返回用户角色作为权限
      return Promise.resolve(user.roles || [])
    } catch (error) {
      return Promise.reject(error)
    }
  },

  /**
   * 获取用户身份信息
   */
  getIdentity: async () => {
    try {
      const { user } = useAuthStore.getState()
      
      if (!user) {
        return Promise.reject(new Error('未登录'))
      }
      
      return Promise.resolve({
        id: user.id,
        fullName: user.nickname || `${user.firstName || ''} ${user.lastName || ''}`.trim() || user.email,
        avatar: user.avatar || undefined
      })
    } catch (error) {
      return Promise.reject(error)
    }
  }
}

export default authProvider
