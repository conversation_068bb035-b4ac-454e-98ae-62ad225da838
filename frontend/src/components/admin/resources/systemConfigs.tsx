/**
 * 系统配置资源组件
 * 提供系统配置的列表和编辑功能
 */

import React from 'react'
import {
  List,
  Datagrid,
  TextField,
  BooleanField,
  DateField,
  EditButton,
  Edit,
  SimpleForm,
  TextInput,
  BooleanInput,
  NumberInput,
  SelectInput,
  required,
  Toolbar,
  SaveButton,
  ListButton,
  TopToolbar,
  FilterButton,
  SearchInput,
  SelectColumnsButton,
  ChipField,
  JsonField
} from 'react-admin'
import {
  Card,
  CardContent,
  Typography,
  Chip,
  Box,
  Avatar
} from '@mui/material'
import {
  Settings,
  Security,
  Email,
  Storage,
  Speed
} from '@mui/icons-material'

/**
 * 系统配置列表过滤器
 */
const configFilters = [
  <SearchInput source="q" placeholder="搜索配置..." alwaysOn />,
  <SelectInput
    source="category"
    label="分类"
    choices={[
      { id: 'security', name: '安全设置' },
      { id: 'email', name: '邮件配置' },
      { id: 'oauth', name: '<PERSON>Auth配置' },
      { id: 'system', name: '系统设置' },
      { id: 'performance', name: '性能配置' }
    ]}
    allowEmpty
  />,
  <SelectInput
    source="isPublic"
    label="可见性"
    choices={[
      { id: true, name: '公开' },
      { id: false, name: '私有' }
    ]}
    allowEmpty
  />
]

/**
 * 系统配置列表工具栏
 */
const ConfigListActions = () => (
  <TopToolbar>
    <FilterButton />
    <SelectColumnsButton />
  </TopToolbar>
)

/**
 * 配置分类字段
 */
const CategoryField = ({ record }: any) => {
  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'security':
        return <Security fontSize="small" />
      case 'email':
        return <Email fontSize="small" />
      case 'system':
        return <Settings fontSize="small" />
      case 'performance':
        return <Speed fontSize="small" />
      default:
        return <Storage fontSize="small" />
    }
  }

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'security':
        return 'error'
      case 'email':
        return 'info'
      case 'oauth':
        return 'secondary'
      case 'system':
        return 'primary'
      case 'performance':
        return 'warning'
      default:
        return 'default'
    }
  }

  const getCategoryLabel = (category: string) => {
    const labels: Record<string, string> = {
      security: '安全设置',
      email: '邮件配置',
      oauth: 'OAuth配置',
      system: '系统设置',
      performance: '性能配置'
    }
    return labels[category] || category
  }

  return (
    <Box display="flex" alignItems="center">
      <Avatar sx={{ width: 24, height: 24, mr: 1, bgcolor: `${getCategoryColor(record?.category)}.main` }}>
        {getCategoryIcon(record?.category)}
      </Avatar>
      <Chip
        label={getCategoryLabel(record?.category)}
        color={getCategoryColor(record?.category)}
        size="small"
      />
    </Box>
  )
}

/**
 * 配置键字段
 */
const ConfigKeyField = ({ record }: any) => (
  <Box>
    <Typography variant="body2" fontWeight="medium">
      {record?.key}
    </Typography>
    {record?.description && (
      <Typography variant="caption" color="textSecondary">
        {record.description}
      </Typography>
    )}
  </Box>
)

/**
 * 配置值字段
 */
const ConfigValueField = ({ record }: any) => {
  const value = record?.value
  
  if (typeof value === 'boolean') {
    return (
      <Chip
        label={value ? '是' : '否'}
        color={value ? 'success' : 'error'}
        size="small"
      />
    )
  }
  
  if (typeof value === 'number') {
    return <Typography variant="body2">{value}</Typography>
  }
  
  if (typeof value === 'string') {
    return (
      <Typography 
        variant="body2" 
        sx={{ 
          maxWidth: 200,
          overflow: 'hidden',
          textOverflow: 'ellipsis',
          whiteSpace: 'nowrap'
        }}
      >
        {value}
      </Typography>
    )
  }
  
  if (typeof value === 'object') {
    return (
      <Chip
        label="JSON对象"
        color="info"
        size="small"
      />
    )
  }
  
  return <Typography variant="body2">-</Typography>
}

/**
 * 系统配置列表组件
 */
export const SystemConfigList = () => (
  <List
    filters={configFilters}
    actions={<ConfigListActions />}
    perPage={25}
    sort={{ field: 'category', order: 'ASC' }}
  >
    <Datagrid rowClick="edit" bulkActionButtons={false}>
      <CategoryField source="category" label="分类" />
      <ConfigKeyField source="key" label="配置键" />
      <ConfigValueField source="value" label="配置值" />
      <BooleanField source="isPublic" label="公开" />
      <DateField source="updatedAt" label="更新时间" showTime />
      <EditButton />
    </Datagrid>
  </List>
)

/**
 * 配置编辑表单工具栏
 */
const ConfigEditToolbar = () => (
  <Toolbar>
    <SaveButton />
    <ListButton />
  </Toolbar>
)

/**
 * 动态值输入组件
 */
const DynamicValueInput = ({ record }: any) => {
  const value = record?.value
  
  if (typeof value === 'boolean') {
    return <BooleanInput source="value" label="配置值" />
  }
  
  if (typeof value === 'number') {
    return <NumberInput source="value" label="配置值" />
  }
  
  if (typeof value === 'object') {
    return (
      <TextInput 
        source="value" 
        label="配置值 (JSON)" 
        multiline 
        rows={6} 
        fullWidth
        format={(value) => JSON.stringify(value, null, 2)}
        parse={(value) => {
          try {
            return JSON.parse(value)
          } catch {
            return value
          }
        }}
      />
    )
  }
  
  return <TextInput source="value" label="配置值" fullWidth />
}

/**
 * 系统配置编辑组件
 */
export const SystemConfigEdit = () => (
  <Edit>
    <SimpleForm toolbar={<ConfigEditToolbar />}>
      <Card sx={{ mb: 2, p: 2 }}>
        <Typography variant="h6" gutterBottom>
          配置信息
        </Typography>
        <TextField source="key" label="配置键" />
        <TextField source="description" label="描述" />
        <CategoryField source="category" label="分类" />
      </Card>
      
      <Typography variant="h6" gutterBottom>
        配置值
      </Typography>
      <DynamicValueInput />
      
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        其他设置
      </Typography>
      <BooleanInput source="isPublic" label="公开配置" />
    </SimpleForm>
  </Edit>
)
