/**
 * OAuth客户端管理资源组件
 * 提供OAuth客户端的列表、创建、编辑、查看功能
 */

import React from 'react'
import {
  List,
  Datagrid,
  TextField,
  BooleanField,
  DateField,
  EditButton,
  ShowButton,
  DeleteButton,
  Create,
  Edit,
  Show,
  SimpleForm,
  SimpleShowLayout,
  TextInput,
  BooleanInput,
  SelectInput,
  ArrayInput,
  SimpleFormIterator,
  required,
  Toolbar,
  SaveButton,
  ListButton,
  TopToolbar,
  CreateButton,
  ExportButton,
  FilterButton,
  SearchInput,
  SelectColumnsButton,
  ChipField,
  ArrayField,
  SingleFieldList,
  NumberInput,
  NumberField
} from 'react-admin'
import {
  Card,
  CardContent,
  Typography,
  Chip,
  Box,
  Avatar
} from '@mui/material'
import {
  Api,
  Security,
  Schedule,
  Settings
} from '@mui/icons-material'

/**
 * OAuth客户端列表过滤器
 */
const clientFilters = [
  <SearchInput source="q" placeholder="搜索客户端..." alwaysOn />,
  <SelectInput
    source="isActive"
    label="状态"
    choices={[
      { id: true, name: '启用' },
      { id: false, name: '禁用' }
    ]}
    allowEmpty
  />
]

/**
 * 客户端列表工具栏
 */
const ClientListActions = () => (
  <TopToolbar>
    <FilterButton />
    <CreateButton />
    <ExportButton />
    <SelectColumnsButton />
  </TopToolbar>
)

/**
 * 自定义客户端信息字段
 */
const ClientInfoField = ({ record }: any) => (
  <Box display="flex" alignItems="center">
    <Avatar sx={{ width: 32, height: 32, mr: 1, bgcolor: 'secondary.main' }}>
      <Api />
    </Avatar>
    <Box>
      <Typography variant="body2" fontWeight="medium">
        {record?.name}
      </Typography>
      <Typography variant="caption" color="textSecondary">
        {record?.clientId}
      </Typography>
    </Box>
  </Box>
)

/**
 * 客户端状态字段
 */
const ClientStatusField = ({ record }: any) => (
  <Box>
    <Chip
      label={record?.isActive ? '启用' : '禁用'}
      color={record?.isActive ? 'success' : 'error'}
      size="small"
      sx={{ mb: 0.5 }}
    />
    {record?.requirePkce && (
      <Chip
        label="PKCE"
        color="primary"
        size="small"
        variant="outlined"
        sx={{ ml: 0.5 }}
      />
    )}
  </Box>
)

/**
 * 授权类型字段
 */
const GrantTypesField = ({ record }: any) => (
  <Box>
    {record?.grantTypes?.map((type: string) => (
      <Chip
        key={type}
        label={type}
        color="secondary"
        size="small"
        sx={{ mr: 0.5, mb: 0.5 }}
      />
    ))}
  </Box>
)

/**
 * OAuth客户端列表组件
 */
export const ClientList = () => (
  <List
    filters={clientFilters}
    actions={<ClientListActions />}
    perPage={25}
    sort={{ field: 'createdAt', order: 'DESC' }}
  >
    <Datagrid rowClick="show" bulkActionButtons={false}>
      <ClientInfoField source="name" label="客户端" />
      <TextField source="description" label="描述" />
      <ClientStatusField source="isActive" label="状态" />
      <GrantTypesField source="grantTypes" label="授权类型" />
      <DateField source="createdAt" label="创建时间" showTime />
      <EditButton />
      <ShowButton />
      <DeleteButton />
    </Datagrid>
  </List>
)

/**
 * 客户端创建表单工具栏
 */
const ClientCreateToolbar = () => (
  <Toolbar>
    <SaveButton />
  </Toolbar>
)

/**
 * OAuth客户端创建组件
 */
export const ClientCreate = () => (
  <Create>
    <SimpleForm toolbar={<ClientCreateToolbar />}>
      <TextInput source="name" label="客户端名称" validate={required()} fullWidth />
      <TextInput source="description" label="描述" multiline rows={3} fullWidth />
      
      <ArrayInput source="redirectUris" label="重定向URI">
        <SimpleFormIterator>
          <TextInput source="" label="URI" validate={required()} fullWidth />
        </SimpleFormIterator>
      </ArrayInput>
      
      <SelectInput
        source="grantTypes"
        label="授权类型"
        choices={[
          { id: 'authorization_code', name: '授权码' },
          { id: 'refresh_token', name: '刷新令牌' },
          { id: 'client_credentials', name: '客户端凭据' }
        ]}
        multiple
        validate={required()}
        defaultValue={['authorization_code', 'refresh_token']}
      />
      
      <SelectInput
        source="responseTypes"
        label="响应类型"
        choices={[
          { id: 'code', name: 'code' },
          { id: 'token', name: 'token' },
          { id: 'id_token', name: 'id_token' }
        ]}
        multiple
        defaultValue={['code']}
      />
      
      <SelectInput
        source="scopes"
        label="权限范围"
        choices={[
          { id: 'openid', name: 'OpenID' },
          { id: 'profile', name: '用户资料' },
          { id: 'email', name: '邮箱' },
          { id: 'offline_access', name: '离线访问' }
        ]}
        multiple
        validate={required()}
        defaultValue={['openid', 'profile', 'email']}
      />
      
      <BooleanInput source="requirePkce" label="要求PKCE" defaultValue={true} />
      <BooleanInput source="requireConsent" label="要求用户同意" defaultValue={true} />
      <BooleanInput source="isActive" label="启用状态" defaultValue={true} />
      
      <NumberInput source="accessTokenLifetime" label="访问令牌生命周期(秒)" defaultValue={3600} />
      <NumberInput source="refreshTokenLifetime" label="刷新令牌生命周期(秒)" defaultValue={604800} />
    </SimpleForm>
  </Create>
)

/**
 * 客户端编辑表单工具栏
 */
const ClientEditToolbar = () => (
  <Toolbar>
    <SaveButton />
    <ListButton />
  </Toolbar>
)

/**
 * OAuth客户端编辑组件
 */
export const ClientEdit = () => (
  <Edit>
    <SimpleForm toolbar={<ClientEditToolbar />}>
      <TextInput source="name" label="客户端名称" validate={required()} fullWidth />
      <TextInput source="description" label="描述" multiline rows={3} fullWidth />
      
      <ArrayInput source="redirectUris" label="重定向URI">
        <SimpleFormIterator>
          <TextInput source="" label="URI" validate={required()} fullWidth />
        </SimpleFormIterator>
      </ArrayInput>
      
      <SelectInput
        source="grantTypes"
        label="授权类型"
        choices={[
          { id: 'authorization_code', name: '授权码' },
          { id: 'refresh_token', name: '刷新令牌' },
          { id: 'client_credentials', name: '客户端凭据' }
        ]}
        multiple
        validate={required()}
      />
      
      <SelectInput
        source="responseTypes"
        label="响应类型"
        choices={[
          { id: 'code', name: 'code' },
          { id: 'token', name: 'token' },
          { id: 'id_token', name: 'id_token' }
        ]}
        multiple
      />
      
      <SelectInput
        source="scopes"
        label="权限范围"
        choices={[
          { id: 'openid', name: 'OpenID' },
          { id: 'profile', name: '用户资料' },
          { id: 'email', name: '邮箱' },
          { id: 'offline_access', name: '离线访问' }
        ]}
        multiple
        validate={required()}
      />
      
      <BooleanInput source="requirePkce" label="要求PKCE" />
      <BooleanInput source="requireConsent" label="要求用户同意" />
      <BooleanInput source="isActive" label="启用状态" />
      
      <NumberInput source="accessTokenLifetime" label="访问令牌生命周期(秒)" />
      <NumberInput source="refreshTokenLifetime" label="刷新令牌生命周期(秒)" />
    </SimpleForm>
  </Edit>
)

/**
 * 客户端详情页面工具栏
 */
const ClientShowActions = () => (
  <TopToolbar>
    <EditButton />
    <ListButton />
  </TopToolbar>
)

/**
 * OAuth客户端详情组件
 */
export const ClientShow = () => (
  <Show actions={<ClientShowActions />}>
    <SimpleShowLayout>
      <Card sx={{ mb: 2 }}>
        <CardContent>
          <Box display="flex" alignItems="center" mb={2}>
            <Avatar sx={{ width: 64, height: 64, mr: 2, bgcolor: 'secondary.main' }}>
              <Api fontSize="large" />
            </Avatar>
            <Box>
              <Typography variant="h5">
                <TextField source="name" />
              </Typography>
              <Typography variant="body2" color="textSecondary">
                客户端ID: <TextField source="clientId" />
              </Typography>
            </Box>
          </Box>
          
          <Box display="flex" gap={1} mb={2}>
            <ClientStatusField />
          </Box>
        </CardContent>
      </Card>
      
      <TextField source="description" label="描述" />
      <ArrayField source="redirectUris" label="重定向URI">
        <SingleFieldList>
          <ChipField source="" />
        </SingleFieldList>
      </ArrayField>
      <GrantTypesField source="grantTypes" label="授权类型" />
      <ArrayField source="responseTypes" label="响应类型">
        <SingleFieldList>
          <ChipField source="" />
        </SingleFieldList>
      </ArrayField>
      <ArrayField source="scopes" label="权限范围">
        <SingleFieldList>
          <ChipField source="" />
        </SingleFieldList>
      </ArrayField>
      <BooleanField source="requirePkce" label="要求PKCE" />
      <BooleanField source="requireConsent" label="要求用户同意" />
      <BooleanField source="isActive" label="启用状态" />
      <NumberField source="accessTokenLifetime" label="访问令牌生命周期(秒)" />
      <NumberField source="refreshTokenLifetime" label="刷新令牌生命周期(秒)" />
      <DateField source="createdAt" label="创建时间" showTime />
      <DateField source="updatedAt" label="更新时间" showTime />
    </SimpleShowLayout>
  </Show>
)
