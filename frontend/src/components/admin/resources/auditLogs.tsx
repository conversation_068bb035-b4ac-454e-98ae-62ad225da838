/**
 * 审计日志资源组件
 * 提供审计日志的列表和查看功能
 */

import React from 'react'
import {
  List,
  Datagrid,
  TextField,
  DateField,
  ShowButton,
  Show,
  SimpleShowLayout,
  TopToolbar,
  ListButton,
  FilterButton,
  SearchInput,
  SelectInput,
  DateInput,
  SelectColumnsButton,
  ChipField,
  JsonField
} from 'react-admin'
import {
  Card,
  CardContent,
  Typography,
  Chip,
  Box,
  Avatar
} from '@mui/material'
import {
  Security,
  Person,
  Computer,
  Event
} from '@mui/icons-material'

/**
 * 审计日志列表过滤器
 */
const auditLogFilters = [
  <SearchInput source="q" placeholder="搜索日志..." alwaysOn />,
  <SelectInput
    source="action"
    label="操作类型"
    choices={[
      { id: 'login', name: '登录' },
      { id: 'logout', name: '登出' },
      { id: 'register', name: '注册' },
      { id: 'password_reset', name: '密码重置' },
      { id: 'mfa_setup', name: 'M<PERSON>设置' },
      { id: 'oauth_authorize', name: '<PERSON>A<PERSON>授权' },
      { id: 'user_create', name: '创建用户' },
      { id: 'user_update', name: '更新用户' },
      { id: 'user_delete', name: '删除用户' },
      { id: 'client_create', name: '创建客户端' },
      { id: 'client_update', name: '更新客户端' },
      { id: 'client_delete', name: '删除客户端' }
    ]}
    allowEmpty
  />,
  <DateInput source="startDate" label="开始日期" />,
  <DateInput source="endDate" label="结束日期" />
]

/**
 * 审计日志列表工具栏
 */
const AuditLogListActions = () => (
  <TopToolbar>
    <FilterButton />
    <SelectColumnsButton />
  </TopToolbar>
)

/**
 * 操作类型字段
 */
const ActionField = ({ record }: any) => {
  const getActionColor = (action: string) => {
    switch (action) {
      case 'login':
      case 'register':
        return 'success'
      case 'logout':
        return 'info'
      case 'password_reset':
      case 'mfa_setup':
        return 'warning'
      case 'user_delete':
      case 'client_delete':
        return 'error'
      default:
        return 'primary'
    }
  }

  const getActionLabel = (action: string) => {
    const labels: Record<string, string> = {
      login: '登录',
      logout: '登出',
      register: '注册',
      password_reset: '密码重置',
      mfa_setup: 'MFA设置',
      oauth_authorize: 'OAuth授权',
      user_create: '创建用户',
      user_update: '更新用户',
      user_delete: '删除用户',
      client_create: '创建客户端',
      client_update: '更新客户端',
      client_delete: '删除客户端'
    }
    return labels[action] || action
  }

  return (
    <Chip
      label={getActionLabel(record?.action)}
      color={getActionColor(record?.action)}
      size="small"
    />
  )
}

/**
 * 用户信息字段
 */
const UserInfoField = ({ record }: any) => (
  <Box display="flex" alignItems="center">
    <Avatar sx={{ width: 24, height: 24, mr: 1 }}>
      <Person fontSize="small" />
    </Avatar>
    <Box>
      <Typography variant="body2">
        {record?.userEmail || '系统'}
      </Typography>
      {record?.userId && (
        <Typography variant="caption" color="textSecondary">
          ID: {record.userId}
        </Typography>
      )}
    </Box>
  </Box>
)

/**
 * 资源信息字段
 */
const ResourceField = ({ record }: any) => (
  <Box>
    <Typography variant="body2" fontWeight="medium">
      {record?.resource}
    </Typography>
    {record?.resourceId && (
      <Typography variant="caption" color="textSecondary">
        ID: {record.resourceId}
      </Typography>
    )}
  </Box>
)

/**
 * IP地址和用户代理字段
 */
const ClientInfoField = ({ record }: any) => (
  <Box>
    <Typography variant="body2">
      {record?.ipAddress}
    </Typography>
    <Typography variant="caption" color="textSecondary" sx={{ 
      display: 'block',
      maxWidth: 200,
      overflow: 'hidden',
      textOverflow: 'ellipsis',
      whiteSpace: 'nowrap'
    }}>
      {record?.userAgent}
    </Typography>
  </Box>
)

/**
 * 审计日志列表组件
 */
export const AuditLogList = () => (
  <List
    filters={auditLogFilters}
    actions={<AuditLogListActions />}
    perPage={50}
    sort={{ field: 'createdAt', order: 'DESC' }}
  >
    <Datagrid rowClick="show" bulkActionButtons={false}>
      <ActionField source="action" label="操作" />
      <UserInfoField source="userEmail" label="用户" />
      <ResourceField source="resource" label="资源" />
      <ClientInfoField source="ipAddress" label="客户端信息" />
      <DateField source="createdAt" label="时间" showTime />
      <ShowButton />
    </Datagrid>
  </List>
)

/**
 * 审计日志详情页面工具栏
 */
const AuditLogShowActions = () => (
  <TopToolbar>
    <ListButton />
  </TopToolbar>
)

/**
 * 审计日志详情组件
 */
export const AuditLogShow = () => (
  <Show actions={<AuditLogShowActions />}>
    <SimpleShowLayout>
      <Card sx={{ mb: 2 }}>
        <CardContent>
          <Box display="flex" alignItems="center" mb={2}>
            <Avatar sx={{ width: 64, height: 64, mr: 2, bgcolor: 'warning.main' }}>
              <Security fontSize="large" />
            </Avatar>
            <Box>
              <Typography variant="h5">
                审计日志详情
              </Typography>
              <Typography variant="body2" color="textSecondary">
                <DateField source="createdAt" showTime />
              </Typography>
            </Box>
          </Box>
        </CardContent>
      </Card>
      
      <Typography variant="h6" gutterBottom>
        基本信息
      </Typography>
      <ActionField source="action" label="操作类型" />
      <TextField source="resource" label="资源类型" />
      <TextField source="resourceId" label="资源ID" />
      
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        用户信息
      </Typography>
      <TextField source="userId" label="用户ID" />
      <TextField source="userEmail" label="用户邮箱" />
      
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        客户端信息
      </Typography>
      <TextField source="ipAddress" label="IP地址" />
      <TextField source="userAgent" label="用户代理" />
      
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        详细信息
      </Typography>
      <JsonField source="details" label="操作详情" />
      
      <DateField source="createdAt" label="创建时间" showTime />
    </SimpleShowLayout>
  </Show>
)
