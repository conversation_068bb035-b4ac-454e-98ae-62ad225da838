/**
 * 用户管理资源组件
 * 提供用户的列表、创建、编辑、查看功能
 */

import React from 'react'
import {
  List,
  Datagrid,
  TextField,
  EmailField,
  BooleanField,
  DateField,
  EditButton,
  ShowButton,
  DeleteButton,
  Create,
  Edit,
  Show,
  SimpleForm,
  SimpleShowLayout,
  TextInput,
  BooleanInput,
  SelectInput,
  required,
  email,
  Toolbar,
  SaveButton,
  ListButton,
  TopToolbar,
  CreateButton,
  ExportButton,
  FilterButton,
  SearchInput,
  SelectColumnsButton,
  ChipField,
  ArrayField,
  SingleFieldList,
  ReferenceArrayField
} from 'react-admin'
import {
  Card,
  CardContent,
  Typography,
  Chip,
  Box,
  Avatar
} from '@mui/material'
import {
  Person,
  Email,
  Security,
  Schedule
} from '@mui/icons-material'

/**
 * 用户列表过滤器
 */
const userFilters = [
  <SearchInput source="q" placeholder="搜索用户..." alwaysOn />,
  <SelectInput
    source="isActive"
    label="状态"
    choices={[
      { id: true, name: '活跃' },
      { id: false, name: '禁用' }
    ]}
    allowEmpty
  />,
  <SelectInput
    source="role"
    label="角色"
    choices={[
      { id: 'user', name: '普通用户' },
      { id: 'admin', name: '管理员' },
      { id: 'super_admin', name: '超级管理员' }
    ]}
    allowEmpty
  />
]

/**
 * 用户列表工具栏
 */
const UserListActions = () => (
  <TopToolbar>
    <FilterButton />
    <CreateButton />
    <ExportButton />
    <SelectColumnsButton />
  </TopToolbar>
)

/**
 * 自定义用户头像字段
 */
const UserAvatarField = ({ record }: any) => (
  <Box display="flex" alignItems="center">
    <Avatar sx={{ width: 32, height: 32, mr: 1 }}>
      <Person />
    </Avatar>
    <Box>
      <Typography variant="body2" fontWeight="medium">
        {record?.nickname || `${record?.firstName || ''} ${record?.lastName || ''}`.trim() || record?.email}
      </Typography>
      <Typography variant="caption" color="textSecondary">
        {record?.email}
      </Typography>
    </Box>
  </Box>
)

/**
 * 用户状态字段
 */
const UserStatusField = ({ record }: any) => (
  <Box>
    <Chip
      label={record?.isActive ? '活跃' : '禁用'}
      color={record?.isActive ? 'success' : 'error'}
      size="small"
      sx={{ mb: 0.5 }}
    />
    {record?.isEmailVerified && (
      <Chip
        label="已验证"
        color="primary"
        size="small"
        variant="outlined"
      />
    )}
  </Box>
)

/**
 * 用户角色字段
 */
const UserRolesField = ({ record }: any) => (
  <Box>
    {record?.roles?.map((role: string) => (
      <Chip
        key={role}
        label={role === 'super_admin' ? '超级管理员' : role === 'admin' ? '管理员' : '用户'}
        color="secondary"
        size="small"
        sx={{ mr: 0.5, mb: 0.5 }}
      />
    ))}
  </Box>
)

/**
 * 用户列表组件
 */
export const UserList = () => (
  <List
    filters={userFilters}
    actions={<UserListActions />}
    perPage={25}
    sort={{ field: 'createdAt', order: 'DESC' }}
  >
    <Datagrid rowClick="show" bulkActionButtons={false}>
      <UserAvatarField source="email" label="用户" />
      <UserStatusField source="isActive" label="状态" />
      <UserRolesField source="roles" label="角色" />
      <DateField source="lastLoginAt" label="最后登录" showTime />
      <DateField source="createdAt" label="创建时间" showTime />
      <EditButton />
      <ShowButton />
      <DeleteButton />
    </Datagrid>
  </List>
)

/**
 * 用户创建表单工具栏
 */
const UserCreateToolbar = () => (
  <Toolbar>
    <SaveButton />
  </Toolbar>
)

/**
 * 用户创建组件
 */
export const UserCreate = () => (
  <Create>
    <SimpleForm toolbar={<UserCreateToolbar />}>
      <TextInput source="email" label="邮箱" validate={[required(), email()]} fullWidth />
      <TextInput source="nickname" label="昵称" fullWidth />
      <TextInput source="firstName" label="名" fullWidth />
      <TextInput source="lastName" label="姓" fullWidth />
      <TextInput source="password" label="密码" type="password" validate={required()} fullWidth />
      <BooleanInput source="isActive" label="启用状态" defaultValue={true} />
      <SelectInput
        source="roles"
        label="角色"
        choices={[
          { id: 'user', name: '普通用户' },
          { id: 'admin', name: '管理员' },
          { id: 'super_admin', name: '超级管理员' }
        ]}
        multiple
        defaultValue={['user']}
      />
    </SimpleForm>
  </Create>
)

/**
 * 用户编辑表单工具栏
 */
const UserEditToolbar = () => (
  <Toolbar>
    <SaveButton />
    <ListButton />
  </Toolbar>
)

/**
 * 用户编辑组件
 */
export const UserEdit = () => (
  <Edit>
    <SimpleForm toolbar={<UserEditToolbar />}>
      <TextInput source="email" label="邮箱" validate={[required(), email()]} fullWidth />
      <TextInput source="nickname" label="昵称" fullWidth />
      <TextInput source="firstName" label="名" fullWidth />
      <TextInput source="lastName" label="姓" fullWidth />
      <BooleanInput source="isActive" label="启用状态" />
      <BooleanInput source="isEmailVerified" label="邮箱已验证" />
      <BooleanInput source="mfaEnabled" label="启用MFA" />
      <SelectInput
        source="roles"
        label="角色"
        choices={[
          { id: 'user', name: '普通用户' },
          { id: 'admin', name: '管理员' },
          { id: 'super_admin', name: '超级管理员' }
        ]}
        multiple
      />
    </SimpleForm>
  </Edit>
)

/**
 * 用户详情页面工具栏
 */
const UserShowActions = () => (
  <TopToolbar>
    <EditButton />
    <ListButton />
  </TopToolbar>
)

/**
 * 用户详情组件
 */
export const UserShow = () => (
  <Show actions={<UserShowActions />}>
    <SimpleShowLayout>
      <Card sx={{ mb: 2 }}>
        <CardContent>
          <Box display="flex" alignItems="center" mb={2}>
            <Avatar sx={{ width: 64, height: 64, mr: 2 }}>
              <Person fontSize="large" />
            </Avatar>
            <Box>
              <Typography variant="h5">
                <TextField source="nickname" />
              </Typography>
              <Typography variant="body2" color="textSecondary">
                <EmailField source="email" />
              </Typography>
            </Box>
          </Box>
          
          <Box display="flex" gap={1} mb={2}>
            <UserStatusField />
            <UserRolesField />
          </Box>
        </CardContent>
      </Card>
      
      <TextField source="firstName" label="名" />
      <TextField source="lastName" label="姓" />
      <BooleanField source="isActive" label="启用状态" />
      <BooleanField source="isEmailVerified" label="邮箱已验证" />
      <BooleanField source="mfaEnabled" label="启用MFA" />
      <TextField source="sessions" label="活跃会话数" />
      <TextField source="loginCount" label="登录次数" />
      <DateField source="lastLoginAt" label="最后登录时间" showTime />
      <DateField source="createdAt" label="创建时间" showTime />
      <DateField source="updatedAt" label="更新时间" showTime />
    </SimpleShowLayout>
  </Show>
)
