/**
 * 基于React Admin的管理员控制台主应用
 * 提供完整的管理员功能，包括用户管理、OAuth客户端管理、系统配置等
 */

import React from 'react'
import { Admin, Resource, Layout, AppBar, Menu, UserMenu } from 'react-admin'
import { dataProvider } from './dataProvider'
import { authProvider } from './authProvider'
import { theme } from './theme'

// 导入各个资源的组件
import { UserList, UserEdit, UserCreate, UserShow } from './resources/users'
import { ClientList, ClientEdit, ClientCreate, ClientShow } from './resources/clients'
import { AuditLogList, AuditLogShow } from './resources/auditLogs'
import { SystemConfigList, SystemConfigEdit } from './resources/systemConfigs'
import { Dashboard } from './Dashboard'

// 导入图标
import {
  UserOutlined,
  ApiOutlined,
  SettingOutlined,
  AuditOutlined,
  DashboardOutlined,
  SecurityScanOutlined
} from '@ant-design/icons'

/**
 * 自定义应用栏
 */
const CustomAppBar = () => (
  <AppBar
    sx={{
      '& .RaAppBar-title': {
        flex: 1,
        textOverflow: 'ellipsis',
        whiteSpace: 'nowrap',
        overflow: 'hidden',
      },
    }}
    userMenu={<UserMenu />}
  />
)

/**
 * 自定义菜单
 */
const CustomMenu = () => (
  <Menu>
    <Menu.DashboardItem />
    <Menu.ResourceItem name="users" />
    <Menu.ResourceItem name="clients" />
    <Menu.ResourceItem name="auditLogs" />
    <Menu.ResourceItem name="systemConfigs" />
  </Menu>
)

/**
 * 自定义布局
 */
const CustomLayout = (props: any) => (
  <Layout
    {...props}
    appBar={CustomAppBar}
    menu={CustomMenu}
  />
)

/**
 * 管理员控制台主应用组件
 */
export const AdminApp: React.FC = () => {
  return (
    <Admin
      dataProvider={dataProvider}
      authProvider={authProvider}
      dashboard={Dashboard}
      layout={CustomLayout}
      theme={theme}
      title="身份提供商管理控制台"
    >
      {/* 用户管理 */}
      <Resource
        name="users"
        list={UserList}
        edit={UserEdit}
        create={UserCreate}
        show={UserShow}
        icon={UserOutlined}
        options={{ label: '用户管理' }}
      />

      {/* OAuth客户端管理 */}
      <Resource
        name="clients"
        list={ClientList}
        edit={ClientEdit}
        create={ClientCreate}
        show={ClientShow}
        icon={ApiOutlined}
        options={{ label: 'OAuth客户端' }}
      />

      {/* 审计日志 */}
      <Resource
        name="auditLogs"
        list={AuditLogList}
        show={AuditLogShow}
        icon={AuditOutlined}
        options={{ label: '审计日志' }}
      />

      {/* 系统配置 */}
      <Resource
        name="systemConfigs"
        list={SystemConfigList}
        edit={SystemConfigEdit}
        icon={SettingOutlined}
        options={{ label: '系统配置' }}
      />
    </Admin>
  )
}

export default AdminApp
