/**
 * React Admin 主题配置
 * 定义管理员控制台的视觉样式和主题
 */

import { createTheme } from '@mui/material/styles'
import { zhCN } from '@mui/material/locale'

/**
 * 自定义主题配置
 */
export const theme = createTheme(
  {
    palette: {
      mode: 'light',
      primary: {
        main: '#1890ff', // Ant Design 主色
        light: '#40a9ff',
        dark: '#096dd9',
        contrastText: '#ffffff'
      },
      secondary: {
        main: '#722ed1', // 紫色
        light: '#9254de',
        dark: '#531dab',
        contrastText: '#ffffff'
      },
      success: {
        main: '#52c41a',
        light: '#73d13d',
        dark: '#389e0d',
        contrastText: '#ffffff'
      },
      warning: {
        main: '#fa8c16',
        light: '#ffa940',
        dark: '#d46b08',
        contrastText: '#ffffff'
      },
      error: {
        main: '#ff4d4f',
        light: '#ff7875',
        dark: '#cf1322',
        contrastText: '#ffffff'
      },
      background: {
        default: '#f0f2f5', // Ant Design 背景色
        paper: '#ffffff'
      },
      text: {
        primary: 'rgba(0, 0, 0, 0.85)', // Ant Design 主文本色
        secondary: 'rgba(0, 0, 0, 0.65)' // Ant Design 次文本色
      }
    },
    typography: {
      fontFamily: [
        '-apple-system',
        'BlinkMacSystemFont',
        'Segoe UI',
        'Roboto',
        'Helvetica Neue',
        'Arial',
        'Noto Sans',
        'sans-serif',
        'Apple Color Emoji',
        'Segoe UI Emoji',
        'Segoe UI Symbol',
        'Noto Color Emoji'
      ].join(','),
      h1: {
        fontSize: '2.125rem',
        fontWeight: 600,
        lineHeight: 1.2
      },
      h2: {
        fontSize: '1.75rem',
        fontWeight: 600,
        lineHeight: 1.3
      },
      h3: {
        fontSize: '1.5rem',
        fontWeight: 600,
        lineHeight: 1.4
      },
      h4: {
        fontSize: '1.25rem',
        fontWeight: 600,
        lineHeight: 1.4
      },
      h5: {
        fontSize: '1.125rem',
        fontWeight: 600,
        lineHeight: 1.5
      },
      h6: {
        fontSize: '1rem',
        fontWeight: 600,
        lineHeight: 1.5
      },
      body1: {
        fontSize: '0.875rem',
        lineHeight: 1.5
      },
      body2: {
        fontSize: '0.75rem',
        lineHeight: 1.5
      }
    },
    shape: {
      borderRadius: 6 // Ant Design 圆角
    },
    spacing: 8, // 8px 基础间距
    components: {
      // 自定义组件样式
      MuiAppBar: {
        styleOverrides: {
          root: {
            backgroundColor: '#ffffff',
            color: 'rgba(0, 0, 0, 0.85)',
            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15)',
            borderBottom: '1px solid #f0f0f0'
          }
        }
      },
      MuiDrawer: {
        styleOverrides: {
          paper: {
            backgroundColor: '#ffffff',
            borderRight: '1px solid #f0f0f0'
          }
        }
      },
      MuiCard: {
        styleOverrides: {
          root: {
            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.09)',
            border: '1px solid #f0f0f0',
            borderRadius: 6
          }
        }
      },
      MuiButton: {
        styleOverrides: {
          root: {
            textTransform: 'none', // 不转换大小写
            borderRadius: 6,
            fontWeight: 400
          },
          contained: {
            boxShadow: '0 2px 0 rgba(0, 0, 0, 0.045)',
            '&:hover': {
              boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)'
            }
          }
        }
      },
      MuiTextField: {
        styleOverrides: {
          root: {
            '& .MuiOutlinedInput-root': {
              borderRadius: 6,
              '& fieldset': {
                borderColor: '#d9d9d9'
              },
              '&:hover fieldset': {
                borderColor: '#40a9ff'
              },
              '&.Mui-focused fieldset': {
                borderColor: '#1890ff',
                borderWidth: 2
              }
            }
          }
        }
      },
      MuiTableCell: {
        styleOverrides: {
          root: {
            borderBottom: '1px solid #f0f0f0',
            padding: '12px 16px'
          },
          head: {
            backgroundColor: '#fafafa',
            fontWeight: 600,
            color: 'rgba(0, 0, 0, 0.85)'
          }
        }
      },
      MuiChip: {
        styleOverrides: {
          root: {
            borderRadius: 4,
            height: 24,
            fontSize: '0.75rem'
          }
        }
      },
      MuiPaper: {
        styleOverrides: {
          root: {
            backgroundImage: 'none' // 移除默认渐变背景
          }
        }
      }
    }
  },
  zhCN // 中文本地化
)

export default theme
