/**
 * 管理员控制台仪表板
 * 显示系统概览、统计信息和关键指标
 */

import React, { useState, useEffect } from 'react'
import {
  Card,
  CardContent,
  Typography,
  Grid,
  Box,
  CircularProgress,
  Alert,
  Chip,
  LinearProgress
} from '@mui/material'
import {
  TrendingUp,
  People,
  Security,
  Api,
  Storage,
  Memory,
  Speed
} from '@mui/icons-material'
import { adminApi, SystemStats } from '../../services/adminApi'

/**
 * 统计卡片组件
 */
interface StatCardProps {
  title: string
  value: string | number
  icon: React.ReactNode
  color: 'primary' | 'secondary' | 'success' | 'warning' | 'error'
  trend?: string
}

const StatCard: React.FC<StatCardProps> = ({ title, value, icon, color, trend }) => (
  <Card sx={{ height: '100%' }}>
    <CardContent>
      <Box display="flex" alignItems="center" justifyContent="space-between">
        <Box>
          <Typography color="textSecondary" gutterBottom variant="body2">
            {title}
          </Typography>
          <Typography variant="h4" component="div" color={`${color}.main`}>
            {value}
          </Typography>
          {trend && (
            <Box display="flex" alignItems="center" mt={1}>
              <TrendingUp fontSize="small" color="success" />
              <Typography variant="body2" color="success.main" ml={0.5}>
                {trend}
              </Typography>
            </Box>
          )}
        </Box>
        <Box color={`${color}.main`}>
          {icon}
        </Box>
      </Box>
    </CardContent>
  </Card>
)

/**
 * 系统健康状态组件
 */
interface SystemHealthProps {
  health: {
    database: boolean
    redis: boolean
    memory: number
    cpu: number
  }
}

const SystemHealth: React.FC<SystemHealthProps> = ({ health }) => (
  <Card>
    <CardContent>
      <Typography variant="h6" gutterBottom>
        系统健康状态
      </Typography>
      
      <Box mb={2}>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
          <Typography variant="body2">数据库</Typography>
          <Chip
            label={health.database ? '正常' : '异常'}
            color={health.database ? 'success' : 'error'}
            size="small"
          />
        </Box>
        
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
          <Typography variant="body2">Redis缓存</Typography>
          <Chip
            label={health.redis ? '正常' : '异常'}
            color={health.redis ? 'success' : 'error'}
            size="small"
          />
        </Box>
      </Box>
      
      <Box mb={2}>
        <Typography variant="body2" gutterBottom>
          内存使用率: {health.memory}%
        </Typography>
        <LinearProgress
          variant="determinate"
          value={health.memory}
          color={health.memory > 80 ? 'error' : health.memory > 60 ? 'warning' : 'success'}
        />
      </Box>
      
      <Box>
        <Typography variant="body2" gutterBottom>
          CPU使用率: {health.cpu}%
        </Typography>
        <LinearProgress
          variant="determinate"
          value={health.cpu}
          color={health.cpu > 80 ? 'error' : health.cpu > 60 ? 'warning' : 'success'}
        />
      </Box>
    </CardContent>
  </Card>
)

/**
 * 仪表板主组件
 */
export const Dashboard: React.FC = () => {
  const [stats, setStats] = useState<SystemStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    loadStats()
  }, [])

  const loadStats = async () => {
    try {
      setLoading(true)
      setError(null)
      const data = await adminApi.getSystemStats()
      setStats(data)
    } catch (err) {
      setError('加载统计数据失败')
      console.error('加载统计数据失败:', err)
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    )
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ mb: 2 }}>
        {error}
      </Alert>
    )
  }

  if (!stats) {
    return (
      <Alert severity="warning" sx={{ mb: 2 }}>
        暂无统计数据
      </Alert>
    )
  }

  return (
    <Box p={3}>
      <Typography variant="h4" gutterBottom>
        系统仪表板
      </Typography>
      
      <Grid container spacing={3}>
        {/* 统计卡片 */}
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="总用户数"
            value={stats.totalUsers.toLocaleString()}
            icon={<People fontSize="large" />}
            color="primary"
            trend="+5.2%"
          />
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="活跃用户"
            value={stats.activeUsers.toLocaleString()}
            icon={<TrendingUp fontSize="large" />}
            color="success"
            trend="+8.7%"
          />
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="OAuth客户端"
            value={stats.oauthClients.toLocaleString()}
            icon={<Api fontSize="large" />}
            color="secondary"
            trend="+2.1%"
          />
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="24h API请求"
            value={stats.apiRequests24h.toLocaleString()}
            icon={<Speed fontSize="large" />}
            color="warning"
            trend="+12.3%"
          />
        </Grid>
        
        {/* 系统健康状态 */}
        <Grid item xs={12} md={6}>
          <SystemHealth health={stats.systemHealth} />
        </Grid>
        
        {/* 快速操作 */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                快速操作
              </Typography>
              
              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <Box
                    p={2}
                    border={1}
                    borderColor="divider"
                    borderRadius={1}
                    textAlign="center"
                    sx={{ cursor: 'pointer', '&:hover': { bgcolor: 'action.hover' } }}
                  >
                    <People color="primary" />
                    <Typography variant="body2" mt={1}>
                      用户管理
                    </Typography>
                  </Box>
                </Grid>
                
                <Grid item xs={6}>
                  <Box
                    p={2}
                    border={1}
                    borderColor="divider"
                    borderRadius={1}
                    textAlign="center"
                    sx={{ cursor: 'pointer', '&:hover': { bgcolor: 'action.hover' } }}
                  >
                    <Api color="secondary" />
                    <Typography variant="body2" mt={1}>
                      OAuth客户端
                    </Typography>
                  </Box>
                </Grid>
                
                <Grid item xs={6}>
                  <Box
                    p={2}
                    border={1}
                    borderColor="divider"
                    borderRadius={1}
                    textAlign="center"
                    sx={{ cursor: 'pointer', '&:hover': { bgcolor: 'action.hover' } }}
                  >
                    <Security color="warning" />
                    <Typography variant="body2" mt={1}>
                      审计日志
                    </Typography>
                  </Box>
                </Grid>
                
                <Grid item xs={6}>
                  <Box
                    p={2}
                    border={1}
                    borderColor="divider"
                    borderRadius={1}
                    textAlign="center"
                    sx={{ cursor: 'pointer', '&:hover': { bgcolor: 'action.hover' } }}
                  >
                    <Storage color="success" />
                    <Typography variant="body2" mt={1}>
                      系统配置
                    </Typography>
                  </Box>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  )
}

export default Dashboard
