/**
 * 国际化上下文提供者组件
 * 
 * 功能说明：
 * 1. 提供全局的国际化上下文
 * 2. 管理语言切换状态
 * 3. 提供RTL布局支持
 * 4. 处理语言加载错误
 * 5. 提供语言偏好设置持久化
 */

import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { ConfigProvider, message } from 'antd';
import { useTranslation } from 'react-i18next';
import zhCN from 'antd/locale/zh_CN';
import enUS from 'antd/locale/en_US';
import jaJP from 'antd/locale/ja_JP';
import {
  SupportedLanguage,
  getCurrentLanguage,
  changeLanguage,
  isRTLLanguage,
  SUPPORTED_LANGUAGES
} from '../i18n';

// Ant Design 语言包映射
const antdLocales = {
  'zh-CN': zhCN,
  'en-US': enUS,
  'ja-JP': jaJP
};

/**
 * 国际化上下文类型
 */
interface I18nContextType {
  currentLanguage: SupportedLanguage;
  isRTL: boolean;
  isLoading: boolean;
  error: string | null;
  switchLanguage: (language: SupportedLanguage) => Promise<void>;
  supportedLanguages: typeof SUPPORTED_LANGUAGES;
}

/**
 * 国际化上下文
 */
const I18nContext = createContext<I18nContextType | undefined>(undefined);

/**
 * 国际化提供者组件属性
 */
interface I18nProviderProps {
  children: ReactNode;
  /**
   * 默认语言
   */
  defaultLanguage?: SupportedLanguage;
  /**
   * 是否启用语言检测
   */
  enableDetection?: boolean;
  /**
   * 错误处理回调
   */
  onError?: (error: Error) => void;
}

/**
 * 国际化提供者组件
 */
export const I18nProvider: React.FC<I18nProviderProps> = ({
  children,
  defaultLanguage,
  enableDetection = true,
  onError
}) => {
  const { i18n } = useTranslation();
  const [currentLanguage, setCurrentLanguage] = useState<SupportedLanguage>(
    defaultLanguage || getCurrentLanguage()
  );
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 初始化语言设置
  useEffect(() => {
    const initializeLanguage = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // 如果启用了语言检测且没有指定默认语言
        if (enableDetection && !defaultLanguage) {
          const detectedLanguage = getCurrentLanguage();
          if (detectedLanguage !== currentLanguage) {
            await changeLanguage(detectedLanguage);
            setCurrentLanguage(detectedLanguage);
          }
        }

        // 设置HTML属性
        updateDocumentAttributes(currentLanguage);
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : '语言初始化失败';
        setError(errorMessage);
        onError?.(err instanceof Error ? err : new Error(errorMessage));
        console.error('语言初始化失败:', err);
      } finally {
        setIsLoading(false);
      }
    };

    initializeLanguage();
  }, [defaultLanguage, enableDetection, onError]);

  // 监听语言变化
  useEffect(() => {
    const handleLanguageChange = (lng: string) => {
      const newLanguage = lng as SupportedLanguage;
      if (newLanguage !== currentLanguage && newLanguage in SUPPORTED_LANGUAGES) {
        setCurrentLanguage(newLanguage);
        updateDocumentAttributes(newLanguage);
      }
    };

    // 监听i18next语言变化
    i18n.on('languageChanged', handleLanguageChange);

    // 监听自定义语言变化事件
    const handleCustomLanguageChange = (event: CustomEvent) => {
      const { language } = event.detail;
      if (language !== currentLanguage) {
        setCurrentLanguage(language);
      }
    };

    window.addEventListener('languageChanged', handleCustomLanguageChange as EventListener);

    return () => {
      i18n.off('languageChanged', handleLanguageChange);
      window.removeEventListener('languageChanged', handleCustomLanguageChange as EventListener);
    };
  }, [i18n, currentLanguage]);

  /**
   * 更新文档属性
   */
  const updateDocumentAttributes = (language: SupportedLanguage) => {
    // 设置HTML lang属性
    document.documentElement.lang = language;
    
    // 设置文档方向
    const isRTL = isRTLLanguage(language);
    document.documentElement.dir = isRTL ? 'rtl' : 'ltr';
    
    // 设置CSS变量用于样式调整
    document.documentElement.style.setProperty('--text-direction', isRTL ? 'rtl' : 'ltr');
    
    // 添加语言类名
    document.documentElement.className = document.documentElement.className
      .replace(/\blang-\w+\b/g, '') + ` lang-${language}`;
  };

  /**
   * 切换语言
   */
  const switchLanguage = async (language: SupportedLanguage): Promise<void> => {
    if (language === currentLanguage) return;

    try {
      setIsLoading(true);
      setError(null);

      await changeLanguage(language);
      setCurrentLanguage(language);
      updateDocumentAttributes(language);

      // 显示成功消息
      message.success(`语言已切换为 ${SUPPORTED_LANGUAGES[language].nativeName}`);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '语言切换失败';
      setError(errorMessage);
      onError?.(err instanceof Error ? err : new Error(errorMessage));
      
      // 显示错误消息
      message.error(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  // 上下文值
  const contextValue: I18nContextType = {
    currentLanguage,
    isRTL: isRTLLanguage(currentLanguage),
    isLoading,
    error,
    switchLanguage,
    supportedLanguages: SUPPORTED_LANGUAGES
  };

  // 获取当前Ant Design语言包
  const antdLocale = antdLocales[currentLanguage];

  return (
    <I18nContext.Provider value={contextValue}>
      <ConfigProvider 
        locale={antdLocale}
        direction={isRTLLanguage(currentLanguage) ? 'rtl' : 'ltr'}
        theme={{
          token: {
            // 根据语言调整主题
            fontFamily: currentLanguage === 'ja-JP' 
              ? '"Hiragino Sans", "Hiragino Kaku Gothic ProN", "Noto Sans JP", sans-serif'
              : currentLanguage === 'zh-CN'
              ? '"PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", sans-serif'
              : '"Segoe UI", "Roboto", "Helvetica Neue", sans-serif'
          }
        }}
      >
        {children}
      </ConfigProvider>
    </I18nContext.Provider>
  );
};

/**
 * 使用国际化上下文的Hook
 */
export const useI18nContext = (): I18nContextType => {
  const context = useContext(I18nContext);
  if (!context) {
    throw new Error('useI18nContext must be used within an I18nProvider');
  }
  return context;
};

export default I18nProvider;
