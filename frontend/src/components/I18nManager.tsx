/**
 * 国际化管理组件
 * 提供语言切换、翻译管理和本地化配置的用户界面
 */

import React, { useState, useEffect } from 'react';
import {
  Card,
  Select,
  Button,
  Table,
  Modal,
  Form,
  Input,
  TextArea,
  Space,
  Tag,
  Progress,
  Tabs,
  Row,
  Col,
  Statistic,
  Alert,
  Tooltip,
  Switch,
  Divider,
  message,
  Upload,
  Typography
} from 'antd';
import {
  GlobalOutlined,
  TranslationOutlined,
  DownloadOutlined,
  UploadOutlined,
  ReloadOutlined,
  SettingOutlined,
  DeleteOutlined,
  EditOutlined,
  PlusOutlined,
  ExportOutlined,
  ImportOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useI18n } from '../hooks/useI18n';
import { i18nApi } from '../services/api';

const { Option } = Select;
const { TabPane } = Tabs;
const { Title, Text } = Typography;

interface TranslationItem {
  key: string;
  namespace: string;
  translations: Record<string, string>;
  lastModified: string;
  status: 'complete' | 'partial' | 'missing';
}

interface LanguageStats {
  language: string;
  completionRate: number;
  totalKeys: number;
  translatedKeys: number;
  missingKeys: number;
}

export const I18nManager: React.FC = () => {
  const { t } = useTranslation();
  const { currentLanguage, supportedLanguages, switchLanguage } = useI18n();
  
  const [loading, setLoading] = useState(false);
  const [translations, setTranslations] = useState<TranslationItem[]>([]);
  const [languageStats, setLanguageStats] = useState<LanguageStats[]>([]);
  const [selectedLanguage, setSelectedLanguage] = useState(currentLanguage);
  const [translateModalVisible, setTranslateModalVisible] = useState(false);
  const [batchTranslateModalVisible, setBatchTranslateModalVisible] = useState(false);
  const [autoTranslateModalVisible, setAutoTranslateModalVisible] = useState(false);
  
  const [translateForm] = Form.useForm();
  const [batchTranslateForm] = Form.useForm();
  const [autoTranslateForm] = Form.useForm();

  useEffect(() => {
    loadTranslations();
    loadLanguageStats();
  }, [selectedLanguage]);

  /**
   * 加载翻译数据
   */
  const loadTranslations = async () => {
    try {
      setLoading(true);
      const response = await i18nApi.getTranslations(selectedLanguage);
      setTranslations(response.data);
    } catch (error) {
      message.error(t('errors.load_failed'));
    } finally {
      setLoading(false);
    }
  };

  /**
   * 加载语言统计
   */
  const loadLanguageStats = async () => {
    try {
      const response = await i18nApi.getLanguageStats();
      setLanguageStats(response.data);
    } catch (error) {
      message.error(t('errors.load_failed'));
    }
  };

  /**
   * 切换语言
   */
  const handleLanguageChange = async (language: string) => {
    try {
      await switchLanguage(language);
      setSelectedLanguage(language);
      message.success(t('i18n.preference_updated'));
    } catch (error) {
      message.error(t('errors.language_switch_failed'));
    }
  };

  /**
   * 动态翻译
   */
  const handleTranslate = async (values: any) => {
    try {
      setLoading(true);
      const response = await i18nApi.translateText({
        text: values.text,
        targetLanguage: values.targetLanguage,
        sourceLanguage: values.sourceLanguage,
        provider: values.provider
      });
      
      message.success(t('success.translated'));
      translateForm.setFieldsValue({
        translatedText: response.data.translatedText
      });
    } catch (error) {
      message.error(t('errors.translation_failed'));
    } finally {
      setLoading(false);
    }
  };

  /**
   * 批量翻译
   */
  const handleBatchTranslate = async (values: any) => {
    try {
      setLoading(true);
      const texts = values.texts.split('\n').filter((text: string) => text.trim());
      
      const response = await i18nApi.translateBatch({
        texts,
        sourceLanguage: values.sourceLanguage,
        targetLanguage: values.targetLanguage,
        namespace: values.namespace,
        priority: values.priority
      });
      
      message.success(t('success.batch_translated', { 
        count: response.data.summary.successfulTranslations 
      }));
      setBatchTranslateModalVisible(false);
      loadTranslations();
    } catch (error) {
      message.error(t('errors.batch_translation_failed'));
    } finally {
      setLoading(false);
    }
  };

  /**
   * 自动翻译缺失的键
   */
  const handleAutoTranslate = async (values: any) => {
    try {
      setLoading(true);
      await i18nApi.autoTranslate({
        sourceLanguage: values.sourceLanguage,
        targetLanguages: values.targetLanguages,
        namespace: values.namespace
      });
      
      message.success(t('success.auto_translate_started'));
      setAutoTranslateModalVisible(false);
      
      // 延迟重新加载数据
      setTimeout(() => {
        loadTranslations();
        loadLanguageStats();
      }, 2000);
    } catch (error) {
      message.error(t('errors.auto_translate_failed'));
    } finally {
      setLoading(false);
    }
  };

  /**
   * 导出翻译
   */
  const handleExportTranslations = async () => {
    try {
      const response = await i18nApi.exportTranslations(selectedLanguage);
      const blob = new Blob([JSON.stringify(response.data, null, 2)], {
        type: 'application/json'
      });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `translations-${selectedLanguage}.json`;
      a.click();
      URL.revokeObjectURL(url);
      
      message.success(t('success.exported'));
    } catch (error) {
      message.error(t('errors.export_failed'));
    }
  };

  /**
   * 清理翻译缓存
   */
  const handleClearCache = async () => {
    try {
      await i18nApi.clearCache();
      message.success(t('success.cache_cleared'));
      loadTranslations();
    } catch (error) {
      message.error(t('errors.cache_clear_failed'));
    }
  };

  const translationColumns = [
    {
      title: t('common.key'),
      dataIndex: 'key',
      key: 'key',
      width: 200,
      ellipsis: true
    },
    {
      title: t('common.namespace'),
      dataIndex: 'namespace',
      key: 'namespace',
      width: 120,
      render: (namespace: string) => <Tag color="blue">{namespace}</Tag>
    },
    {
      title: t('i18n.translation'),
      dataIndex: 'translations',
      key: 'translations',
      render: (translations: Record<string, string>) => (
        <Text ellipsis={{ tooltip: translations[selectedLanguage] }}>
          {translations[selectedLanguage] || <Text type="secondary">{t('i18n.translation_missing')}</Text>}
        </Text>
      )
    },
    {
      title: t('common.status'),
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string) => {
        const colors = {
          complete: 'green',
          partial: 'orange',
          missing: 'red'
        };
        return <Tag color={colors[status as keyof typeof colors]}>{t(`status.${status}`)}</Tag>;
      }
    },
    {
      title: t('common.actions'),
      key: 'actions',
      width: 120,
      render: (record: TranslationItem) => (
        <Space>
          <Tooltip title={t('common.edit')}>
            <Button type="text" icon={<EditOutlined />} size="small" />
          </Tooltip>
          <Tooltip title={t('common.delete')}>
            <Button type="text" danger icon={<DeleteOutlined />} size="small" />
          </Tooltip>
        </Space>
      )
    }
  ];

  return (
    <div className="i18n-manager">
      <Card>
        <Row gutter={[16, 16]}>
          <Col span={24}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Title level={3}>
                <GlobalOutlined /> {t('i18n.language_settings')}
              </Title>
              <Space>
                <Select
                  value={selectedLanguage}
                  onChange={setSelectedLanguage}
                  style={{ width: 150 }}
                >
                  {supportedLanguages.map(lang => (
                    <Option key={lang.code} value={lang.code}>
                      {lang.name}
                    </Option>
                  ))}
                </Select>
                <Button
                  type="primary"
                  onClick={() => handleLanguageChange(selectedLanguage)}
                  disabled={selectedLanguage === currentLanguage}
                >
                  {t('i18n.change_language')}
                </Button>
              </Space>
            </div>
          </Col>
        </Row>

        <Divider />

        <Tabs defaultActiveKey="overview">
          <TabPane tab={t('common.overview')} key="overview">
            <Row gutter={[16, 16]}>
              {languageStats.map(stat => (
                <Col span={6} key={stat.language}>
                  <Card size="small">
                    <Statistic
                      title={stat.language}
                      value={stat.completionRate}
                      suffix="%"
                      valueStyle={{ color: stat.completionRate >= 90 ? '#3f8600' : '#cf1322' }}
                    />
                    <Progress
                      percent={stat.completionRate}
                      size="small"
                      status={stat.completionRate >= 90 ? 'success' : 'exception'}
                    />
                    <Text type="secondary">
                      {stat.translatedKeys} / {stat.totalKeys} {t('i18n.keys')}
                    </Text>
                  </Card>
                </Col>
              ))}
            </Row>

            <Divider />

            <Alert
              message={t('i18n.translation_statistics')}
              description={t('i18n.completion_rate_description')}
              type="info"
              showIcon
              style={{ marginBottom: 16 }}
            />
          </TabPane>

          <TabPane tab={t('i18n.translations')} key="translations">
            <div style={{ marginBottom: 16 }}>
              <Space>
                <Button
                  type="primary"
                  icon={<TranslationOutlined />}
                  onClick={() => setTranslateModalVisible(true)}
                >
                  {t('i18n.translate')}
                </Button>
                <Button
                  icon={<PlusOutlined />}
                  onClick={() => setBatchTranslateModalVisible(true)}
                >
                  {t('i18n.batch_translate')}
                </Button>
                <Button
                  icon={<SettingOutlined />}
                  onClick={() => setAutoTranslateModalVisible(true)}
                >
                  {t('i18n.auto_translate')}
                </Button>
                <Button
                  icon={<ExportOutlined />}
                  onClick={handleExportTranslations}
                >
                  {t('common.export')}
                </Button>
                <Button
                  icon={<ReloadOutlined />}
                  onClick={handleClearCache}
                >
                  {t('i18n.clear_cache')}
                </Button>
              </Space>
            </div>

            <Table
              columns={translationColumns}
              dataSource={translations}
              loading={loading}
              rowKey="key"
              pagination={{
                pageSize: 20,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => t('common.total_items', { total })
              }}
            />
          </TabPane>

          <TabPane tab={t('common.settings')} key="settings">
            <Card title={t('i18n.localization_settings')}>
              <Form layout="vertical">
                <Row gutter={16}>
                  <Col span={12}>
                    <Form.Item label={t('i18n.date_format')}>
                      <Select defaultValue="YYYY-MM-DD">
                        <Option value="YYYY-MM-DD">YYYY-MM-DD</Option>
                        <Option value="MM/DD/YYYY">MM/DD/YYYY</Option>
                        <Option value="DD/MM/YYYY">DD/MM/YYYY</Option>
                      </Select>
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item label={t('i18n.time_format')}>
                      <Select defaultValue="24h">
                        <Option value="24h">24小时制</Option>
                        <Option value="12h">12小时制</Option>
                      </Select>
                    </Form.Item>
                  </Col>
                </Row>
                <Row gutter={16}>
                  <Col span={12}>
                    <Form.Item label={t('i18n.number_format')}>
                      <Select defaultValue="1,234.56">
                        <Option value="1,234.56">1,234.56</Option>
                        <Option value="1.234,56">1.234,56</Option>
                        <Option value="1 234,56">1 234,56</Option>
                      </Select>
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item label={t('i18n.currency_format')}>
                      <Select defaultValue="$1,234.56">
                        <Option value="$1,234.56">$1,234.56</Option>
                        <Option value="1,234.56$">1,234.56$</Option>
                        <Option value="¥1,234.56">¥1,234.56</Option>
                      </Select>
                    </Form.Item>
                  </Col>
                </Row>
                <Form.Item>
                  <Button type="primary">{t('common.save')}</Button>
                </Form.Item>
              </Form>
            </Card>
          </TabPane>
        </Tabs>
      </Card>

      {/* 翻译模态框 */}
      <Modal
        title={t('i18n.translate_text')}
        open={translateModalVisible}
        onCancel={() => setTranslateModalVisible(false)}
        footer={null}
        width={600}
      >
        <Form
          form={translateForm}
          layout="vertical"
          onFinish={handleTranslate}
        >
          <Form.Item
            name="text"
            label={t('i18n.source_text')}
            rules={[{ required: true, message: t('validation.required') }]}
          >
            <TextArea rows={4} placeholder={t('i18n.enter_text_to_translate')} />
          </Form.Item>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="sourceLanguage"
                label={t('i18n.source_language')}
              >
                <Select placeholder={t('i18n.auto_detect')}>
                  {supportedLanguages.map(lang => (
                    <Option key={lang.code} value={lang.code}>
                      {lang.name}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="targetLanguage"
                label={t('i18n.target_language')}
                rules={[{ required: true, message: t('validation.required') }]}
              >
                <Select>
                  {supportedLanguages.map(lang => (
                    <Option key={lang.code} value={lang.code}>
                      {lang.name}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>
          <Form.Item
            name="provider"
            label={t('i18n.translation_provider')}
          >
            <Select defaultValue="google">
              <Option value="google">Google Translate</Option>
              <Option value="baidu">百度翻译</Option>
              <Option value="tencent">腾讯翻译</Option>
              <Option value="aliyun">阿里云翻译</Option>
            </Select>
          </Form.Item>
          <Form.Item
            name="translatedText"
            label={t('i18n.translated_text')}
          >
            <TextArea rows={4} readOnly />
          </Form.Item>
          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit" loading={loading}>
                {t('i18n.translate')}
              </Button>
              <Button onClick={() => setTranslateModalVisible(false)}>
                {t('common.cancel')}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 批量翻译模态框 */}
      <Modal
        title={t('i18n.batch_translate')}
        open={batchTranslateModalVisible}
        onCancel={() => setBatchTranslateModalVisible(false)}
        footer={null}
        width={700}
      >
        <Form
          form={batchTranslateForm}
          layout="vertical"
          onFinish={handleBatchTranslate}
        >
          <Form.Item
            name="texts"
            label={t('i18n.texts_to_translate')}
            rules={[{ required: true, message: t('validation.required') }]}
          >
            <TextArea
              rows={6}
              placeholder={t('i18n.enter_texts_one_per_line')}
            />
          </Form.Item>
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="sourceLanguage"
                label={t('i18n.source_language')}
                rules={[{ required: true, message: t('validation.required') }]}
              >
                <Select>
                  {supportedLanguages.map(lang => (
                    <Option key={lang.code} value={lang.code}>
                      {lang.name}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="targetLanguage"
                label={t('i18n.target_language')}
                rules={[{ required: true, message: t('validation.required') }]}
              >
                <Select>
                  {supportedLanguages.map(lang => (
                    <Option key={lang.code} value={lang.code}>
                      {lang.name}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="priority"
                label={t('common.priority')}
                initialValue="normal"
              >
                <Select>
                  <Option value="low">{t('priority.low')}</Option>
                  <Option value="normal">{t('priority.normal')}</Option>
                  <Option value="high">{t('priority.high')}</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
          <Form.Item
            name="namespace"
            label={t('common.namespace')}
            initialValue="common"
          >
            <Input placeholder="common" />
          </Form.Item>
          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit" loading={loading}>
                {t('i18n.start_translation')}
              </Button>
              <Button onClick={() => setBatchTranslateModalVisible(false)}>
                {t('common.cancel')}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 自动翻译模态框 */}
      <Modal
        title={t('i18n.auto_translate_missing')}
        open={autoTranslateModalVisible}
        onCancel={() => setAutoTranslateModalVisible(false)}
        footer={null}
        width={600}
      >
        <Form
          form={autoTranslateForm}
          layout="vertical"
          onFinish={handleAutoTranslate}
        >
          <Form.Item
            name="sourceLanguage"
            label={t('i18n.source_language')}
            rules={[{ required: true, message: t('validation.required') }]}
          >
            <Select>
              {supportedLanguages.map(lang => (
                <Option key={lang.code} value={lang.code}>
                  {lang.name}
                </Option>
              ))}
            </Select>
          </Form.Item>
          <Form.Item
            name="targetLanguages"
            label={t('i18n.target_languages')}
            rules={[{ required: true, message: t('validation.required') }]}
          >
            <Select mode="multiple">
              {supportedLanguages.map(lang => (
                <Option key={lang.code} value={lang.code}>
                  {lang.name}
                </Option>
              ))}
            </Select>
          </Form.Item>
          <Form.Item
            name="namespace"
            label={t('common.namespace')}
            initialValue="common"
          >
            <Input placeholder="common" />
          </Form.Item>
          <Alert
            message={t('i18n.auto_translate_warning')}
            description={t('i18n.auto_translate_description')}
            type="warning"
            showIcon
            style={{ marginBottom: 16 }}
          />
          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit" loading={loading}>
                {t('i18n.start_auto_translate')}
              </Button>
              <Button onClick={() => setAutoTranslateModalVisible(false)}>
                {t('common.cancel')}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default I18nManager;
