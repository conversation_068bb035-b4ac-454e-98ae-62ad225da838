/**
 * 加载组件样式
 */

/* 加载容器 */
.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.loading-container.centered {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(2px);
  z-index: 1000;
}

.loading-container.small {
  padding: 10px;
}

.loading-container.large {
  padding: 40px;
}

/* 点状加载器 */
.dots-loader {
  display: flex;
  align-items: center;
  gap: 4px;
}

.dots-loader .dot {
  background: #1890ff;
  border-radius: 50%;
  animation: dots-bounce 1.4s ease-in-out infinite both;
}

@keyframes dots-bounce {
  0%, 80%, 100% {
    transform: scale(0);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 骨架屏加载器 */
.skeleton-loader {
  width: 100%;
  max-width: 400px;
  padding: 16px;
}

.skeleton-line {
  height: 16px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e6e6e6 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 4px;
  margin-bottom: 12px;
}

.skeleton-line.skeleton-title {
  height: 20px;
  width: 60%;
}

.skeleton-line.skeleton-text {
  width: 100%;
}

.skeleton-line.skeleton-text.short {
  width: 75%;
}

@keyframes skeleton-loading {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* 内容占位符 */
.content-placeholder {
  padding: 16px;
}

.placeholder-row {
  margin-bottom: 12px;
}

.placeholder-line {
  height: 14px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e6e6e6 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 4px;
}

/* 自定义 Spin 组件样式 */
.ant-spin-dot {
  font-size: 20px;
}

.ant-spin-dot-item {
  background-color: #1890ff;
}

.ant-spin-text {
  color: rgba(0, 0, 0, 0.65);
  font-size: 14px;
  margin-top: 8px;
}

/* 大小变体 */
.loading-container.small .ant-spin-dot {
  font-size: 16px;
}

.loading-container.small .ant-spin-text {
  font-size: 12px;
  margin-top: 6px;
}

.loading-container.large .ant-spin-dot {
  font-size: 24px;
}

.loading-container.large .ant-spin-text {
  font-size: 16px;
  margin-top: 12px;
}

/* 进度圆环自定义样式 */
.ant-progress-circle .ant-progress-text {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.85);
}

/* 暗色主题支持 */
@media (prefers-color-scheme: dark) {
  .loading-container.centered {
    background: rgba(0, 0, 0, 0.8);
  }
  
  .skeleton-line {
    background: linear-gradient(90deg, #262626 25%, #1f1f1f 50%, #262626 75%);
    background-size: 200% 100%;
  }
  
  .placeholder-line {
    background: linear-gradient(90deg, #262626 25%, #1f1f1f 50%, #262626 75%);
    background-size: 200% 100%;
  }
  
  .ant-spin-text {
    color: rgba(255, 255, 255, 0.65);
  }
  
  .ant-progress-circle .ant-progress-text {
    color: rgba(255, 255, 255, 0.85);
  }
}

/* 减少动画效果（用户偏好） */
@media (prefers-reduced-motion: reduce) {
  .dots-loader .dot {
    animation: none;
  }
  
  .skeleton-line,
  .placeholder-line {
    animation: none;
    background: #f0f0f0;
  }
  
  @media (prefers-color-scheme: dark) {
    .skeleton-line,
    .placeholder-line {
      background: #262626;
    }
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .dots-loader .dot {
    background: #000000;
  }
  
  .skeleton-line,
  .placeholder-line {
    background: #cccccc;
    border: 1px solid #000000;
  }
  
  .ant-spin-dot-item {
    background-color: #000000;
  }
  
  .ant-spin-text {
    color: #000000;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .loading-container {
    padding: 16px;
  }
  
  .loading-container.large {
    padding: 24px;
  }
  
  .skeleton-loader {
    padding: 12px;
  }
  
  .content-placeholder {
    padding: 12px;
  }
}

@media (max-width: 480px) {
  .loading-container {
    padding: 12px;
  }
  
  .loading-container.large {
    padding: 20px;
  }
  
  .skeleton-loader {
    padding: 8px;
  }
  
  .content-placeholder {
    padding: 8px;
  }
  
  .ant-spin-text {
    font-size: 12px;
  }
}
