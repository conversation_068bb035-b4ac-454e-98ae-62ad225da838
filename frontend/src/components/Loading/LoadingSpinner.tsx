/**
 * 增强的加载组件
 * 提供多种加载状态和动画效果
 */

import React from 'react'
import { Spin, Progress, Typography, Space } from 'antd'
import { LoadingOutlined } from '@ant-design/icons'
import './LoadingSpinner.css'

const { Text } = Typography

export interface LoadingSpinnerProps {
  /** 加载状态 */
  loading?: boolean
  /** 加载文本 */
  text?: string
  /** 加载类型 */
  type?: 'spinner' | 'progress' | 'skeleton' | 'dots'
  /** 大小 */
  size?: 'small' | 'default' | 'large'
  /** 进度百分比（仅在 type='progress' 时有效） */
  percent?: number
  /** 是否显示在页面中心 */
  centered?: boolean
  /** 自定义样式 */
  style?: React.CSSProperties
  /** 子组件（包装模式） */
  children?: React.ReactNode
}

/**
 * 旋转加载图标
 */
const SpinIcon = <LoadingOutlined style={{ fontSize: 24 }} spin />

/**
 * 点状加载动画
 */
const DotsLoader: React.FC<{ size?: string }> = ({ size = 'default' }) => {
  const dotSize = size === 'small' ? 6 : size === 'large' ? 10 : 8
  
  return (
    <div className="dots-loader">
      {[1, 2, 3].map(i => (
        <div
          key={i}
          className="dot"
          style={{
            width: dotSize,
            height: dotSize,
            animationDelay: `${(i - 1) * 0.2}s`
          }}
        />
      ))}
    </div>
  )
}

/**
 * 骨架屏加载
 */
const SkeletonLoader: React.FC = () => (
  <div className="skeleton-loader">
    <div className="skeleton-line skeleton-title" />
    <div className="skeleton-line skeleton-text" />
    <div className="skeleton-line skeleton-text short" />
    <div className="skeleton-line skeleton-text" />
  </div>
)

/**
 * 加载组件
 */
export const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  loading = true,
  text = '加载中...',
  type = 'spinner',
  size = 'default',
  percent = 0,
  centered = false,
  style,
  children
}) => {
  // 如果有子组件且不在加载状态，直接返回子组件
  if (children && !loading) {
    return <>{children}</>
  }

  // 渲染不同类型的加载器
  const renderLoader = () => {
    switch (type) {
      case 'progress':
        return (
          <Space direction="vertical" align="center" size="middle">
            <Progress
              type="circle"
              percent={percent}
              size={size === 'small' ? 60 : size === 'large' ? 120 : 80}
              strokeColor={{
                '0%': '#108ee9',
                '100%': '#87d068',
              }}
            />
            <Text type="secondary">{text}</Text>
          </Space>
        )

      case 'skeleton':
        return <SkeletonLoader />

      case 'dots':
        return (
          <Space direction="vertical" align="center" size="small">
            <DotsLoader size={size} />
            <Text type="secondary" style={{ fontSize: size === 'small' ? 12 : 14 }}>
              {text}
            </Text>
          </Space>
        )

      case 'spinner':
      default:
        return (
          <Spin
            indicator={SpinIcon}
            size={size}
            tip={text}
          />
        )
    }
  }

  // 包装模式
  if (children) {
    return (
      <Spin
        spinning={loading}
        indicator={SpinIcon}
        tip={text}
        size={size}
      >
        {children}
      </Spin>
    )
  }

  // 独立加载器
  const loaderContent = renderLoader()

  if (centered) {
    return (
      <div className={`loading-container centered ${size}`} style={style}>
        {loaderContent}
      </div>
    )
  }

  return (
    <div className={`loading-container ${size}`} style={style}>
      {loaderContent}
    </div>
  )
}

/**
 * 页面级加载组件
 */
export const PageLoading: React.FC<{
  text?: string
  type?: LoadingSpinnerProps['type']
}> = ({ text = '页面加载中...', type = 'spinner' }) => (
  <LoadingSpinner
    text={text}
    type={type}
    size="large"
    centered
    style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      background: 'rgba(255, 255, 255, 0.8)',
      zIndex: 9999
    }}
  />
)

/**
 * 按钮加载状态
 */
export const ButtonLoading: React.FC<{
  loading?: boolean
  children: React.ReactNode
}> = ({ loading = false, children }) => (
  <Space size="small">
    {loading && <LoadingOutlined />}
    {children}
  </Space>
)

/**
 * 内容加载占位符
 */
export const ContentPlaceholder: React.FC<{
  loading?: boolean
  children: React.ReactNode
  rows?: number
}> = ({ loading = true, children, rows = 4 }) => {
  if (!loading) {
    return <>{children}</>
  }

  return (
    <div className="content-placeholder">
      {Array.from({ length: rows }, (_, i) => (
        <div key={i} className="placeholder-row">
          <div className="placeholder-line" style={{ width: `${Math.random() * 40 + 60}%` }} />
        </div>
      ))}
    </div>
  )
}

export default LoadingSpinner
