/**
 * 分析数据可视化仪表板
 * 
 * 功能说明：
 * 1. 实时数据展示和监控
 * 2. 交互式图表和统计
 * 3. 安全事件分析
 * 4. 用户行为分析
 * 5. 威胁检测可视化
 */

import React, { useState, useEffect } from 'react';
import {
  Card,
  Row,
  Col,
  Statistic,
  Table,
  Select,
  DatePicker,
  Button,
  Alert,
  Tag,
  Progress,
  Timeline,
  Tabs,
  Space,
  Typography,
  Tooltip,
  Badge
} from 'antd';
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  Legend,
  ResponsiveContainer
} from 'recharts';
import {
  DashboardOutlined,
  SecurityScanOutlined,
  UserOutlined,
  GlobalOutlined,
  AlertOutlined,
  TrendingUpOutlined,
  ShieldOutlined,
  EyeOutlined,
  ClockCircleOutlined,
  WarningOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import dayjs from 'dayjs';

const { Title, Text } = Typography;
const { RangePicker } = DatePicker;
const { TabPane } = Tabs;

/**
 * 仪表板数据接口
 */
interface DashboardData {
  overview: {
    totalUsers: number;
    activeUsers: number;
    totalSessions: number;
    securityEvents: number;
    threatLevel: 'low' | 'medium' | 'high' | 'critical';
  };
  userActivity: Array<{
    time: string;
    logins: number;
    registrations: number;
    failures: number;
  }>;
  securityMetrics: Array<{
    type: string;
    count: number;
    severity: string;
  }>;
  geographicData: Array<{
    country: string;
    users: number;
    threats: number;
  }>;
  deviceStats: Array<{
    device: string;
    count: number;
    percentage: number;
  }>;
  recentEvents: Array<{
    id: string;
    type: string;
    severity: string;
    description: string;
    timestamp: string;
    user?: string;
    ip?: string;
  }>;
}

/**
 * 分析仪表板组件
 */
const AnalyticsDashboard: React.FC = () => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<DashboardData | null>(null);
  const [timeRange, setTimeRange] = useState<[dayjs.Dayjs, dayjs.Dayjs]>([
    dayjs().subtract(7, 'day'),
    dayjs()
  ]);
  const [refreshInterval, setRefreshInterval] = useState<number>(30000); // 30秒

  // 模拟数据
  const mockData: DashboardData = {
    overview: {
      totalUsers: 12543,
      activeUsers: 3421,
      totalSessions: 8765,
      securityEvents: 23,
      threatLevel: 'medium'
    },
    userActivity: [
      { time: '00:00', logins: 45, registrations: 12, failures: 3 },
      { time: '04:00', logins: 23, registrations: 5, failures: 1 },
      { time: '08:00', logins: 156, registrations: 34, failures: 8 },
      { time: '12:00', logins: 234, registrations: 45, failures: 12 },
      { time: '16:00', logins: 189, registrations: 28, failures: 6 },
      { time: '20:00', logins: 98, registrations: 15, failures: 4 }
    ],
    securityMetrics: [
      { type: '暴力破解', count: 12, severity: 'high' },
      { type: '可疑登录', count: 8, severity: 'medium' },
      { type: '异常位置', count: 5, severity: 'low' },
      { type: '恶意IP', count: 3, severity: 'critical' }
    ],
    geographicData: [
      { country: '中国', users: 5432, threats: 8 },
      { country: '美国', users: 3210, threats: 12 },
      { country: '日本', users: 1876, threats: 3 },
      { country: '德国', users: 987, threats: 2 },
      { country: '英国', users: 654, threats: 1 }
    ],
    deviceStats: [
      { device: '桌面端', count: 6543, percentage: 52.1 },
      { device: '移动端', count: 4321, percentage: 34.4 },
      { device: '平板端', count: 1679, percentage: 13.4 }
    ],
    recentEvents: [
      {
        id: '1',
        type: '暴力破解',
        severity: 'high',
        description: '检测到来自 ************* 的暴力破解尝试',
        timestamp: '2024-01-15 14:30:25',
        ip: '*************'
      },
      {
        id: '2',
        type: '可疑登录',
        severity: 'medium',
        description: '用户从新设备登录',
        timestamp: '2024-01-15 14:25:12',
        user: '<EMAIL>'
      },
      {
        id: '3',
        type: '异常位置',
        severity: 'low',
        description: '检测到异常地理位置访问',
        timestamp: '2024-01-15 14:20:08',
        user: '<EMAIL>'
      }
    ]
  };

  useEffect(() => {
    loadDashboardData();
    
    // 设置自动刷新
    const interval = setInterval(loadDashboardData, refreshInterval);
    return () => clearInterval(interval);
  }, [timeRange, refreshInterval]);

  /**
   * 加载仪表板数据
   */
  const loadDashboardData = async () => {
    setLoading(true);
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));
      setData(mockData);
    } catch (error) {
      console.error('加载仪表板数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  /**
   * 获取威胁级别颜色
   */
  const getThreatLevelColor = (level: string) => {
    const colors = {
      low: '#52c41a',
      medium: '#faad14',
      high: '#ff7875',
      critical: '#ff4d4f'
    };
    return colors[level as keyof typeof colors] || '#d9d9d9';
  };

  /**
   * 获取严重程度标签
   */
  const getSeverityTag = (severity: string) => {
    const config = {
      low: { color: 'green', text: '低' },
      medium: { color: 'orange', text: '中' },
      high: { color: 'red', text: '高' },
      critical: { color: 'purple', text: '严重' }
    };
    const { color, text } = config[severity as keyof typeof config] || { color: 'default', text: severity };
    return <Tag color={color}>{text}</Tag>;
  };

  if (!data) {
    return <div>加载中...</div>;
  }

  return (
    <div style={{ padding: '24px' }}>
      {/* 页面标题和控制 */}
      <Row justify="space-between" align="middle" style={{ marginBottom: '24px' }}>
        <Col>
          <Title level={2}>
            <DashboardOutlined /> 分析仪表板
          </Title>
        </Col>
        <Col>
          <Space>
            <RangePicker
              value={timeRange}
              onChange={(dates) => dates && setTimeRange(dates)}
              format="YYYY-MM-DD"
            />
            <Select
              value={refreshInterval}
              onChange={setRefreshInterval}
              style={{ width: 120 }}
            >
              <Select.Option value={10000}>10秒</Select.Option>
              <Select.Option value={30000}>30秒</Select.Option>
              <Select.Option value={60000}>1分钟</Select.Option>
              <Select.Option value={300000}>5分钟</Select.Option>
            </Select>
            <Button onClick={loadDashboardData} loading={loading}>
              刷新
            </Button>
          </Space>
        </Col>
      </Row>

      {/* 威胁级别警告 */}
      {data.overview.threatLevel !== 'low' && (
        <Alert
          message={`当前威胁级别：${data.overview.threatLevel.toUpperCase()}`}
          description="检测到安全威胁，请及时关注安全事件"
          type={data.overview.threatLevel === 'critical' ? 'error' : 'warning'}
          showIcon
          style={{ marginBottom: '24px' }}
        />
      )}

      {/* 概览统计 */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="总用户数"
              value={data.overview.totalUsers}
              prefix={<UserOutlined />}
              valueStyle={{ color: '#3f8600' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="活跃用户"
              value={data.overview.activeUsers}
              prefix={<EyeOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="活跃会话"
              value={data.overview.totalSessions}
              prefix={<ClockCircleOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="安全事件"
              value={data.overview.securityEvents}
              prefix={<AlertOutlined />}
              valueStyle={{ color: '#cf1322' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 主要图表区域 */}
      <Tabs defaultActiveKey="activity" style={{ marginBottom: '24px' }}>
        <TabPane tab="用户活动" key="activity">
          <Card title="用户活动趋势" extra={<TrendingUpOutlined />}>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={data.userActivity}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="time" />
                <YAxis />
                <RechartsTooltip />
                <Legend />
                <Line type="monotone" dataKey="logins" stroke="#1890ff" name="登录" />
                <Line type="monotone" dataKey="registrations" stroke="#52c41a" name="注册" />
                <Line type="monotone" dataKey="failures" stroke="#ff4d4f" name="失败" />
              </LineChart>
            </ResponsiveContainer>
          </Card>
        </TabPane>

        <TabPane tab="安全分析" key="security">
          <Row gutter={[16, 16]}>
            <Col xs={24} md={12}>
              <Card title="安全事件分布" extra={<SecurityScanOutlined />}>
                <ResponsiveContainer width="100%" height={250}>
                  <BarChart data={data.securityMetrics}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="type" />
                    <YAxis />
                    <RechartsTooltip />
                    <Bar dataKey="count" fill="#ff7875" />
                  </BarChart>
                </ResponsiveContainer>
              </Card>
            </Col>
            <Col xs={24} md={12}>
              <Card title="威胁级别分布" extra={<ShieldOutlined />}>
                <ResponsiveContainer width="100%" height={250}>
                  <PieChart>
                    <Pie
                      data={data.securityMetrics}
                      cx="50%"
                      cy="50%"
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="count"
                      label={({ type, count }) => `${type}: ${count}`}
                    >
                      {data.securityMetrics.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={getThreatLevelColor(entry.severity)} />
                      ))}
                    </Pie>
                    <RechartsTooltip />
                  </PieChart>
                </ResponsiveContainer>
              </Card>
            </Col>
          </Row>
        </TabPane>

        <TabPane tab="地理分析" key="geographic">
          <Card title="地理分布" extra={<GlobalOutlined />}>
            <Table
              dataSource={data.geographicData}
              pagination={false}
              size="small"
              columns={[
                {
                  title: '国家/地区',
                  dataIndex: 'country',
                  key: 'country'
                },
                {
                  title: '用户数',
                  dataIndex: 'users',
                  key: 'users',
                  render: (value) => value.toLocaleString()
                },
                {
                  title: '威胁数',
                  dataIndex: 'threats',
                  key: 'threats',
                  render: (value) => (
                    <Badge count={value} style={{ backgroundColor: value > 5 ? '#ff4d4f' : '#52c41a' }} />
                  )
                },
                {
                  title: '风险比例',
                  key: 'risk',
                  render: (_, record) => {
                    const ratio = (record.threats / record.users) * 100;
                    return (
                      <Progress
                        percent={Math.min(ratio * 10, 100)}
                        size="small"
                        status={ratio > 1 ? 'exception' : 'normal'}
                        format={() => `${ratio.toFixed(2)}%`}
                      />
                    );
                  }
                }
              ]}
            />
          </Card>
        </TabPane>

        <TabPane tab="设备分析" key="device">
          <Row gutter={[16, 16]}>
            <Col xs={24} md={12}>
              <Card title="设备类型分布">
                <ResponsiveContainer width="100%" height={250}>
                  <PieChart>
                    <Pie
                      data={data.deviceStats}
                      cx="50%"
                      cy="50%"
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="count"
                      label={({ device, percentage }) => `${device}: ${percentage}%`}
                    >
                      {data.deviceStats.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={['#1890ff', '#52c41a', '#faad14'][index]} />
                      ))}
                    </Pie>
                    <RechartsTooltip />
                  </PieChart>
                </ResponsiveContainer>
              </Card>
            </Col>
            <Col xs={24} md={12}>
              <Card title="设备统计详情">
                <Table
                  dataSource={data.deviceStats}
                  pagination={false}
                  size="small"
                  columns={[
                    {
                      title: '设备类型',
                      dataIndex: 'device',
                      key: 'device'
                    },
                    {
                      title: '数量',
                      dataIndex: 'count',
                      key: 'count',
                      render: (value) => value.toLocaleString()
                    },
                    {
                      title: '占比',
                      dataIndex: 'percentage',
                      key: 'percentage',
                      render: (value) => `${value}%`
                    }
                  ]}
                />
              </Card>
            </Col>
          </Row>
        </TabPane>
      </Tabs>

      {/* 最近事件 */}
      <Card title="最近安全事件" extra={<WarningOutlined />}>
        <Timeline>
          {data.recentEvents.map((event) => (
            <Timeline.Item
              key={event.id}
              color={getThreatLevelColor(event.severity)}
              dot={<AlertOutlined />}
            >
              <div>
                <Space>
                  {getSeverityTag(event.severity)}
                  <Text strong>{event.type}</Text>
                  <Text type="secondary">{event.timestamp}</Text>
                </Space>
                <div style={{ marginTop: '4px' }}>
                  <Text>{event.description}</Text>
                </div>
                {(event.user || event.ip) && (
                  <div style={{ marginTop: '4px' }}>
                    {event.user && <Tag>用户: {event.user}</Tag>}
                    {event.ip && <Tag>IP: {event.ip}</Tag>}
                  </div>
                )}
              </div>
            </Timeline.Item>
          ))}
        </Timeline>
      </Card>
    </div>
  );
};

export default AnalyticsDashboard;
