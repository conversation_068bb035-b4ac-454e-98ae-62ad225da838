/**
 * 高级分析仪表板组件
 * 
 * 功能说明：
 * 1. 实时数据可视化
 * 2. 多维度分析图表
 * 3. 交互式数据探索
 * 4. 自定义报告生成
 * 5. 异常检测和告警
 */

import React, { useState, useEffect, useMemo } from 'react';
import {
  Card,
  Row,
  Col,
  Statistic,
  Select,
  DatePicker,
  Button,
  Table,
  Alert,
  Spin,
  Tabs,
  Space,
  Tag,
  Progress,
  Tooltip,
  Badge
} from 'antd';
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  Legend,
  ResponsiveContainer,
  Scatter<PERSON>hart,
  Scatter,
  Heatmap
} from 'recharts';
import {
  TrendingUpOutlined,
  TrendingDownOutlined,
  WarningOutlined,
  SafetyOutlined,
  UserOutlined,
  GlobalOutlined,
  MobileOutlined,
  DesktopOutlined,
  AlertOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  FireOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useAnalytics } from '../../hooks/useAnalytics';
import { formatNumber, formatPercentage, formatDuration } from '../../utils/formatters';

const { RangePicker } = DatePicker;
const { Option } = Select;
const { TabPane } = Tabs;

/**
 * 仪表板数据接口
 */
interface DashboardData {
  overview: {
    totalUsers: number;
    activeUsers: number;
    totalSessions: number;
    securityEvents: number;
    riskScore: number;
    threatLevel: string;
  };
  userActivity: Array<{
    time: string;
    logins: number;
    registrations: number;
    failures: number;
  }>;
  riskAnalysis: Array<{
    time: string;
    lowRisk: number;
    mediumRisk: number;
    highRisk: number;
    criticalRisk: number;
  }>;
  geographicData: Array<{
    country: string;
    users: number;
    sessions: number;
    riskScore: number;
  }>;
  deviceAnalysis: Array<{
    type: string;
    count: number;
    percentage: number;
  }>;
  securityEvents: Array<{
    id: string;
    type: string;
    severity: string;
    timestamp: string;
    description: string;
    status: string;
  }>;
  anomalies: Array<{
    id: string;
    metric: string;
    type: string;
    severity: string;
    detectedAt: string;
    value: number;
    expectedValue: number;
    confidence: number;
  }>;
}

/**
 * 高级分析仪表板组件
 */
export const AdvancedDashboard: React.FC = () => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(true);
  const [data, setData] = useState<DashboardData | null>(null);
  const [timeRange, setTimeRange] = useState<[string, string]>(['7d', 'now']);
  const [refreshInterval, setRefreshInterval] = useState(30000); // 30秒
  const [selectedMetrics, setSelectedMetrics] = useState<string[]>(['users', 'sessions', 'security']);

  const { fetchDashboardData, subscribeToRealTimeUpdates } = useAnalytics();

  // 加载仪表板数据
  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      try {
        const dashboardData = await fetchDashboardData({
          timeRange,
          metrics: selectedMetrics
        });
        setData(dashboardData);
      } catch (error) {
        console.error('加载仪表板数据失败:', error);
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [timeRange, selectedMetrics]);

  // 实时数据更新
  useEffect(() => {
    const unsubscribe = subscribeToRealTimeUpdates((updates) => {
      setData(prevData => {
        if (!prevData) return prevData;
        return {
          ...prevData,
          ...updates
        };
      });
    });

    return unsubscribe;
  }, []);

  // 自动刷新
  useEffect(() => {
    const interval = setInterval(() => {
      if (data) {
        fetchDashboardData({ timeRange, metrics: selectedMetrics })
          .then(setData)
          .catch(console.error);
      }
    }, refreshInterval);

    return () => clearInterval(interval);
  }, [refreshInterval, timeRange, selectedMetrics, data]);

  // 计算趋势
  const trends = useMemo(() => {
    if (!data?.userActivity || data.userActivity.length < 2) return {};

    const latest = data.userActivity[data.userActivity.length - 1];
    const previous = data.userActivity[data.userActivity.length - 2];

    return {
      logins: ((latest.logins - previous.logins) / previous.logins) * 100,
      registrations: ((latest.registrations - previous.registrations) / previous.registrations) * 100,
      failures: ((latest.failures - previous.failures) / previous.failures) * 100
    };
  }, [data?.userActivity]);

  // 风险等级颜色映射
  const getRiskColor = (level: string) => {
    const colors = {
      low: '#52c41a',
      medium: '#faad14',
      high: '#ff7875',
      critical: '#ff4d4f'
    };
    return colors[level as keyof typeof colors] || '#d9d9d9';
  };

  // 威胁等级图标
  const getThreatIcon = (level: string) => {
    switch (level) {
      case 'low': return <SafetyOutlined style={{ color: '#52c41a' }} />;
      case 'medium': return <WarningOutlined style={{ color: '#faad14' }} />;
      case 'high': return <AlertOutlined style={{ color: '#ff7875' }} />;
      case 'critical': return <FireOutlined style={{ color: '#ff4d4f' }} />;
      default: return <CheckCircleOutlined style={{ color: '#d9d9d9' }} />;
    }
  };

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
        <p style={{ marginTop: 16 }}>{t('dashboard.loading')}</p>
      </div>
    );
  }

  if (!data) {
    return (
      <Alert
        message={t('dashboard.error.title')}
        description={t('dashboard.error.description')}
        type="error"
        showIcon
      />
    );
  }

  return (
    <div className="advanced-dashboard">
      {/* 控制面板 */}
      <Card style={{ marginBottom: 24 }}>
        <Row gutter={16} align="middle">
          <Col span={8}>
            <Space>
              <span>{t('dashboard.timeRange')}:</span>
              <Select
                value={timeRange[0]}
                onChange={(value) => setTimeRange([value, 'now'])}
                style={{ width: 120 }}
              >
                <Option value="1h">{t('dashboard.time.1hour')}</Option>
                <Option value="24h">{t('dashboard.time.24hours')}</Option>
                <Option value="7d">{t('dashboard.time.7days')}</Option>
                <Option value="30d">{t('dashboard.time.30days')}</Option>
              </Select>
            </Space>
          </Col>
          <Col span={8}>
            <Space>
              <span>{t('dashboard.refreshInterval')}:</span>
              <Select
                value={refreshInterval}
                onChange={setRefreshInterval}
                style={{ width: 120 }}
              >
                <Option value={10000}>10s</Option>
                <Option value={30000}>30s</Option>
                <Option value={60000}>1min</Option>
                <Option value={300000}>5min</Option>
              </Select>
            </Space>
          </Col>
          <Col span={8}>
            <Space>
              <Button type="primary" onClick={() => window.location.reload()}>
                {t('dashboard.refresh')}
              </Button>
              <Button>{t('dashboard.export')}</Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* 概览统计 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={4}>
          <Card>
            <Statistic
              title={t('dashboard.overview.totalUsers')}
              value={data.overview.totalUsers}
              prefix={<UserOutlined />}
              suffix={
                trends.logins !== undefined && (
                  <span style={{ fontSize: 12, color: trends.logins > 0 ? '#52c41a' : '#ff4d4f' }}>
                    {trends.logins > 0 ? <TrendingUpOutlined /> : <TrendingDownOutlined />}
                    {formatPercentage(Math.abs(trends.logins))}
                  </span>
                )
              }
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title={t('dashboard.overview.activeUsers')}
              value={data.overview.activeUsers}
              prefix={<UserOutlined />}
              valueStyle={{ color: '#3f8600' }}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title={t('dashboard.overview.totalSessions')}
              value={data.overview.totalSessions}
              prefix={<GlobalOutlined />}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title={t('dashboard.overview.securityEvents')}
              value={data.overview.securityEvents}
              prefix={<AlertOutlined />}
              valueStyle={{ color: data.overview.securityEvents > 10 ? '#cf1322' : '#3f8600' }}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <div style={{ textAlign: 'center' }}>
              <div style={{ fontSize: 14, color: '#666', marginBottom: 8 }}>
                {t('dashboard.overview.riskScore')}
              </div>
              <Progress
                type="circle"
                percent={data.overview.riskScore * 100}
                strokeColor={getRiskColor(data.overview.threatLevel)}
                size={80}
                format={(percent) => `${percent?.toFixed(0)}%`}
              />
            </div>
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <div style={{ textAlign: 'center' }}>
              <div style={{ fontSize: 14, color: '#666', marginBottom: 8 }}>
                {t('dashboard.overview.threatLevel')}
              </div>
              <div style={{ fontSize: 24, marginBottom: 8 }}>
                {getThreatIcon(data.overview.threatLevel)}
              </div>
              <Tag color={getRiskColor(data.overview.threatLevel)}>
                {t(`dashboard.threatLevel.${data.overview.threatLevel}`)}
              </Tag>
            </div>
          </Card>
        </Col>
      </Row>

      {/* 异常检测告警 */}
      {data.anomalies.length > 0 && (
        <Alert
          message={t('dashboard.anomalies.detected', { count: data.anomalies.length })}
          description={
            <div>
              {data.anomalies.slice(0, 3).map(anomaly => (
                <div key={anomaly.id} style={{ marginBottom: 4 }}>
                  <Badge status="error" />
                  <span>{anomaly.metric}: </span>
                  <span style={{ fontWeight: 'bold' }}>{formatNumber(anomaly.value)}</span>
                  <span> (预期: {formatNumber(anomaly.expectedValue)})</span>
                  <span style={{ color: '#666', marginLeft: 8 }}>
                    置信度: {formatPercentage(anomaly.confidence)}
                  </span>
                </div>
              ))}
              {data.anomalies.length > 3 && (
                <div style={{ color: '#1890ff', cursor: 'pointer' }}>
                  查看全部 {data.anomalies.length} 个异常...
                </div>
              )}
            </div>
          }
          type="warning"
          showIcon
          style={{ marginBottom: 24 }}
        />
      )}

      {/* 主要图表区域 */}
      <Tabs defaultActiveKey="activity" style={{ marginBottom: 24 }}>
        <TabPane tab={t('dashboard.tabs.userActivity')} key="activity">
          <Card title={t('dashboard.charts.userActivity')}>
            <ResponsiveContainer width="100%" height={400}>
              <LineChart data={data.userActivity}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="time" />
                <YAxis />
                <RechartsTooltip />
                <Legend />
                <Line
                  type="monotone"
                  dataKey="logins"
                  stroke="#1890ff"
                  strokeWidth={2}
                  name={t('dashboard.metrics.logins')}
                />
                <Line
                  type="monotone"
                  dataKey="registrations"
                  stroke="#52c41a"
                  strokeWidth={2}
                  name={t('dashboard.metrics.registrations')}
                />
                <Line
                  type="monotone"
                  dataKey="failures"
                  stroke="#ff4d4f"
                  strokeWidth={2}
                  name={t('dashboard.metrics.failures')}
                />
              </LineChart>
            </ResponsiveContainer>
          </Card>
        </TabPane>

        <TabPane tab={t('dashboard.tabs.riskAnalysis')} key="risk">
          <Card title={t('dashboard.charts.riskAnalysis')}>
            <ResponsiveContainer width="100%" height={400}>
              <AreaChart data={data.riskAnalysis}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="time" />
                <YAxis />
                <RechartsTooltip />
                <Legend />
                <Area
                  type="monotone"
                  dataKey="lowRisk"
                  stackId="1"
                  stroke="#52c41a"
                  fill="#52c41a"
                  name={t('dashboard.risk.low')}
                />
                <Area
                  type="monotone"
                  dataKey="mediumRisk"
                  stackId="1"
                  stroke="#faad14"
                  fill="#faad14"
                  name={t('dashboard.risk.medium')}
                />
                <Area
                  type="monotone"
                  dataKey="highRisk"
                  stackId="1"
                  stroke="#ff7875"
                  fill="#ff7875"
                  name={t('dashboard.risk.high')}
                />
                <Area
                  type="monotone"
                  dataKey="criticalRisk"
                  stackId="1"
                  stroke="#ff4d4f"
                  fill="#ff4d4f"
                  name={t('dashboard.risk.critical')}
                />
              </AreaChart>
            </ResponsiveContainer>
          </Card>
        </TabPane>

        <TabPane tab={t('dashboard.tabs.geographic')} key="geographic">
          <Row gutter={16}>
            <Col span={16}>
              <Card title={t('dashboard.charts.geographicDistribution')}>
                <ResponsiveContainer width="100%" height={400}>
                  <BarChart data={data.geographicData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="country" />
                    <YAxis />
                    <RechartsTooltip />
                    <Legend />
                    <Bar dataKey="users" fill="#1890ff" name={t('dashboard.metrics.users')} />
                    <Bar dataKey="sessions" fill="#52c41a" name={t('dashboard.metrics.sessions')} />
                  </BarChart>
                </ResponsiveContainer>
              </Card>
            </Col>
            <Col span={8}>
              <Card title={t('dashboard.charts.deviceTypes')}>
                <ResponsiveContainer width="100%" height={400}>
                  <PieChart>
                    <Pie
                      data={data.deviceAnalysis}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ type, percentage }) => `${type} ${percentage}%`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="count"
                    >
                      {data.deviceAnalysis.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={['#1890ff', '#52c41a', '#faad14'][index % 3]} />
                      ))}
                    </Pie>
                    <RechartsTooltip />
                  </PieChart>
                </ResponsiveContainer>
              </Card>
            </Col>
          </Row>
        </TabPane>

        <TabPane tab={t('dashboard.tabs.security')} key="security">
          <Card title={t('dashboard.charts.securityEvents')}>
            <Table
              dataSource={data.securityEvents}
              columns={[
                {
                  title: t('dashboard.table.type'),
                  dataIndex: 'type',
                  key: 'type',
                  render: (type) => (
                    <Tag color="blue">{t(`dashboard.eventType.${type}`)}</Tag>
                  )
                },
                {
                  title: t('dashboard.table.severity'),
                  dataIndex: 'severity',
                  key: 'severity',
                  render: (severity) => (
                    <Tag color={getRiskColor(severity)}>
                      {t(`dashboard.severity.${severity}`)}
                    </Tag>
                  )
                },
                {
                  title: t('dashboard.table.timestamp'),
                  dataIndex: 'timestamp',
                  key: 'timestamp',
                  render: (timestamp) => new Date(timestamp).toLocaleString()
                },
                {
                  title: t('dashboard.table.description'),
                  dataIndex: 'description',
                  key: 'description',
                  ellipsis: true
                },
                {
                  title: t('dashboard.table.status'),
                  dataIndex: 'status',
                  key: 'status',
                  render: (status) => (
                    <Badge
                      status={status === 'resolved' ? 'success' : 'processing'}
                      text={t(`dashboard.status.${status}`)}
                    />
                  )
                }
              ]}
              pagination={{ pageSize: 10 }}
              size="small"
            />
          </Card>
        </TabPane>
      </Tabs>
    </div>
  );
};

export default AdvancedDashboard;
