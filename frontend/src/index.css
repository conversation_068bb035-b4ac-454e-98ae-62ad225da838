/* 全局样式重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB',
    'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif, 'Apple Color Emoji',
    'Segoe UI Emoji', 'Segoe UI Symbol';
  font-size: 14px;
  line-height: 1.5715;
  color: rgba(0, 0, 0, 0.85);
  background-color: #f5f5f5;
}

#root {
  height: 100%;
}

/* 认证页面容器样式 */
.auth-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.auth-card {
  width: 100%;
  max-width: 400px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  overflow: hidden;
}

.auth-header {
  text-align: center;
  padding: 32px 24px 24px;
  background: white;
}

.auth-logo {
  width: 64px;
  height: 64px;
  margin: 0 auto 16px;
  background: #1890ff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24px;
  font-weight: bold;
}

.auth-title {
  font-size: 24px;
  font-weight: 600;
  color: #262626;
  margin-bottom: 8px;
}

.auth-subtitle {
  color: #8c8c8c;
  font-size: 14px;
}

.auth-form {
  padding: 0 24px 32px;
}

.auth-form .ant-form-item {
  margin-bottom: 16px;
}

.auth-form .ant-btn {
  width: 100%;
  height: 40px;
  font-size: 16px;
}

.auth-divider {
  margin: 24px 0;
  text-align: center;
  position: relative;
}

.auth-divider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background: #f0f0f0;
}

.auth-divider span {
  background: white;
  padding: 0 16px;
  color: #8c8c8c;
  font-size: 14px;
}

.oauth-buttons {
  display: flex;
  gap: 12px;
}

.oauth-button {
  flex: 1;
  height: 40px;
  border: 1px solid #d9d9d9;
  background: white;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s;
}

.oauth-button:hover {
  border-color: #1890ff;
  color: #1890ff;
}

.auth-footer {
  text-align: center;
  padding: 16px 24px;
  background: #fafafa;
  border-top: 1px solid #f0f0f0;
}

.auth-footer a {
  color: #1890ff;
  text-decoration: none;
}

.auth-footer a:hover {
  text-decoration: underline;
}

/* MFA 相关样式 */
.mfa-container {
  text-align: center;
}

.qr-code-container {
  padding: 20px;
  background: #fafafa;
  border-radius: 8px;
  margin: 16px 0;
}

.backup-codes {
  background: #f6f8fa;
  border: 1px solid #e1e4e8;
  border-radius: 6px;
  padding: 16px;
  margin: 16px 0;
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
  font-size: 12px;
  line-height: 1.5;
}

.backup-codes-title {
  font-weight: 600;
  margin-bottom: 8px;
  color: #24292e;
}

.backup-codes-list {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4px;
  color: #586069;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .auth-container {
    padding: 12px;
  }
  
  .auth-card {
    max-width: none;
  }
  
  .oauth-buttons {
    flex-direction: column;
  }
  
  .oauth-button {
    flex: none;
  }
}

/* 加载动画 */
.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
}

/* 错误提示样式 */
.error-message {
  color: #ff4d4f;
  font-size: 14px;
  margin-top: 8px;
}

/* 成功提示样式 */
.success-message {
  color: #52c41a;
  font-size: 14px;
  margin-top: 8px;
}
