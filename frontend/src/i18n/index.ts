/**
 * 国际化配置文件
 * 
 * 功能说明：
 * 1. 配置i18next框架，支持多语言切换
 * 2. 自动检测浏览器语言
 * 3. 支持动态语言包加载
 * 4. 提供语言切换和本地化功能
 * 
 * 支持的语言：
 * - zh-CN: 简体中文
 * - en-US: 英语（美国）
 * - ja-JP: 日语
 */

import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';
import Backend from 'i18next-http-backend';

// 支持的语言列表
export const SUPPORTED_LANGUAGES = {
  'zh-CN': {
    name: '简体中文',
    nativeName: '简体中文',
    flag: '🇨🇳',
    rtl: false
  },
  'en-US': {
    name: 'English (US)',
    nativeName: 'English (US)',
    flag: '🇺🇸',
    rtl: false
  },
  'ja-JP': {
    name: 'Japanese',
    nativeName: '日本語',
    flag: '🇯🇵',
    rtl: false
  }
} as const;

export type SupportedLanguage = keyof typeof SUPPORTED_LANGUAGES;

// 默认语言
export const DEFAULT_LANGUAGE: SupportedLanguage = 'zh-CN';

// 语言检测配置
const languageDetectorOptions = {
  // 检测顺序：localStorage -> cookie -> navigator -> htmlTag -> path -> subdomain
  order: ['localStorage', 'cookie', 'navigator', 'htmlTag'],
  
  // 在localStorage中的键名
  lookupLocalStorage: 'i18nextLng',
  
  // 在cookie中的键名
  lookupCookie: 'i18next',
  
  // 缓存用户语言选择
  caches: ['localStorage', 'cookie'],
  
  // 排除的路径
  excludeCacheFor: ['cimode'],
  
  // 检查白名单
  checkWhitelist: true
};

// 后端加载配置
const backendOptions = {
  // 语言包加载路径
  loadPath: '/locales/{{lng}}/{{ns}}.json',
  
  // 允许跨域
  crossDomain: false,
  
  // 请求超时时间
  requestOptions: {
    cache: 'default'
  }
};

// i18n初始化配置
i18n
  // 加载后端语言包
  .use(Backend)
  // 检测用户语言
  .use(LanguageDetector)
  // 传递给react-i18next
  .use(initReactI18next)
  // 初始化i18next
  .init({
    // 默认语言
    lng: DEFAULT_LANGUAGE,
    
    // 回退语言
    fallbackLng: DEFAULT_LANGUAGE,
    
    // 支持的语言列表
    supportedLngs: Object.keys(SUPPORTED_LANGUAGES),
    
    // 非生产环境显示调试信息
    debug: process.env.NODE_ENV === 'development',
    
    // 命名空间
    defaultNS: 'common',
    ns: ['common'],
    
    // 语言检测配置
    detection: languageDetectorOptions,
    
    // 后端配置
    backend: backendOptions,
    
    // 插值配置
    interpolation: {
      // React已经做了XSS防护，不需要转义
      escapeValue: false,
      
      // 格式化函数
      format: (value: any, format: string, lng: string) => {
        // 日期格式化
        if (format === 'date') {
          return new Intl.DateTimeFormat(lng).format(new Date(value));
        }
        
        // 时间格式化
        if (format === 'time') {
          return new Intl.DateTimeFormat(lng, {
            hour: '2-digit',
            minute: '2-digit'
          }).format(new Date(value));
        }
        
        // 数字格式化
        if (format === 'number') {
          return new Intl.NumberFormat(lng).format(value);
        }
        
        // 货币格式化
        if (format === 'currency') {
          return new Intl.NumberFormat(lng, {
            style: 'currency',
            currency: 'CNY' // 默认人民币，可根据地区调整
          }).format(value);
        }
        
        return value;
      }
    },
    
    // React配置
    react: {
      // 使用Suspense等待翻译加载
      useSuspense: true,
      
      // 绑定事件
      bindI18n: 'languageChanged',
      bindI18nStore: '',
      
      // 事务模式
      transEmptyNodeValue: '',
      transSupportBasicHtmlNodes: true,
      transKeepBasicHtmlNodesFor: ['br', 'strong', 'i', 'em']
    },
    
    // 资源配置
    resources: undefined, // 使用后端加载
    
    // 键分隔符
    keySeparator: '.',
    
    // 命名空间分隔符
    nsSeparator: ':',
    
    // 复数规则
    pluralSeparator: '_',
    
    // 上下文分隔符
    contextSeparator: '_',
    
    // 后处理器
    postProcess: false,
    
    // 返回对象而不是字符串
    returnObjects: false,
    
    // 返回详细信息
    returnDetails: false,
    
    // 连接超时
    loadTimeout: 5000,
    
    // 分区超时
    partialBundledLanguages: false,
    
    // 清理代码
    cleanCode: true,
    
    // 非严格模式
    nonExplicitSupportedLngs: false
  });

// 导出i18n实例
export default i18n;

/**
 * 获取当前语言
 */
export const getCurrentLanguage = (): SupportedLanguage => {
  return i18n.language as SupportedLanguage || DEFAULT_LANGUAGE;
};

/**
 * 切换语言
 * @param language 目标语言
 */
export const changeLanguage = async (language: SupportedLanguage): Promise<void> => {
  await i18n.changeLanguage(language);
  
  // 更新HTML lang属性
  document.documentElement.lang = language;
  
  // 更新页面方向（RTL支持）
  const isRTL = SUPPORTED_LANGUAGES[language].rtl;
  document.documentElement.dir = isRTL ? 'rtl' : 'ltr';
  
  // 触发自定义事件
  window.dispatchEvent(new CustomEvent('languageChanged', {
    detail: { language, isRTL }
  }));
};

/**
 * 获取语言信息
 * @param language 语言代码
 */
export const getLanguageInfo = (language: SupportedLanguage) => {
  return SUPPORTED_LANGUAGES[language];
};

/**
 * 检查是否为RTL语言
 * @param language 语言代码
 */
export const isRTLLanguage = (language?: SupportedLanguage): boolean => {
  const lang = language || getCurrentLanguage();
  return SUPPORTED_LANGUAGES[lang]?.rtl || false;
};

/**
 * 格式化日期
 * @param date 日期
 * @param options 格式化选项
 */
export const formatDate = (
  date: Date | string | number,
  options?: Intl.DateTimeFormatOptions
): string => {
  const currentLang = getCurrentLanguage();
  return new Intl.DateTimeFormat(currentLang, options).format(new Date(date));
};

/**
 * 格式化数字
 * @param number 数字
 * @param options 格式化选项
 */
export const formatNumber = (
  number: number,
  options?: Intl.NumberFormatOptions
): string => {
  const currentLang = getCurrentLanguage();
  return new Intl.NumberFormat(currentLang, options).format(number);
};

/**
 * 格式化货币
 * @param amount 金额
 * @param currency 货币代码
 */
export const formatCurrency = (
  amount: number,
  currency: string = 'CNY'
): string => {
  const currentLang = getCurrentLanguage();
  return new Intl.NumberFormat(currentLang, {
    style: 'currency',
    currency
  }).format(amount);
};
