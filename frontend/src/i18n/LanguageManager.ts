/**
 * 语言包管理器
 * 
 * 功能说明：
 * 1. 动态加载语言包
 * 2. 缓存管理和版本控制
 * 3. 语言包热更新
 * 4. 错误处理和回退机制
 * 5. 语言包完整性检查
 */

import i18n from 'i18next';
import { SupportedLanguage, SUPPORTED_LANGUAGES } from './index';

/**
 * 语言包元数据接口
 */
interface LanguagePackMeta {
  version: string;
  lastModified: string;
  checksum?: string;
  size: number;
  namespaces: string[];
}

/**
 * 语言包缓存项
 */
interface CacheItem {
  data: Record<string, any>;
  meta: LanguagePackMeta;
  timestamp: number;
  expires: number;
}

/**
 * 语言包加载选项
 */
interface LoadOptions {
  force?: boolean; // 强制重新加载
  timeout?: number; // 超时时间
  retries?: number; // 重试次数
  fallback?: boolean; // 是否使用回退语言
}

/**
 * 语言包管理器类
 */
class LanguageManager {
  private cache = new Map<string, CacheItem>();
  private loadingPromises = new Map<string, Promise<any>>();
  private readonly CACHE_DURATION = 1000 * 60 * 30; // 30分钟缓存
  private readonly DEFAULT_TIMEOUT = 10000; // 10秒超时
  private readonly DEFAULT_RETRIES = 3; // 默认重试3次

  /**
   * 获取缓存键
   */
  private getCacheKey(language: SupportedLanguage, namespace: string): string {
    return `${language}:${namespace}`;
  }

  /**
   * 检查缓存是否有效
   */
  private isCacheValid(cacheItem: CacheItem): boolean {
    return Date.now() < cacheItem.expires;
  }

  /**
   * 从缓存获取语言包
   */
  private getFromCache(language: SupportedLanguage, namespace: string): Record<string, any> | null {
    const key = this.getCacheKey(language, namespace);
    const cacheItem = this.cache.get(key);
    
    if (cacheItem && this.isCacheValid(cacheItem)) {
      return cacheItem.data;
    }
    
    // 清理过期缓存
    if (cacheItem) {
      this.cache.delete(key);
    }
    
    return null;
  }

  /**
   * 保存到缓存
   */
  private saveToCache(
    language: SupportedLanguage, 
    namespace: string, 
    data: Record<string, any>,
    meta: LanguagePackMeta
  ): void {
    const key = this.getCacheKey(language, namespace);
    const cacheItem: CacheItem = {
      data,
      meta,
      timestamp: Date.now(),
      expires: Date.now() + this.CACHE_DURATION
    };
    
    this.cache.set(key, cacheItem);
  }

  /**
   * 获取语言包元数据
   */
  private async fetchMeta(language: SupportedLanguage, namespace: string): Promise<LanguagePackMeta> {
    const response = await fetch(`/locales/${language}/${namespace}.meta.json`);
    
    if (!response.ok) {
      // 如果没有元数据文件，返回默认元数据
      return {
        version: '1.0.0',
        lastModified: new Date().toISOString(),
        size: 0,
        namespaces: [namespace]
      };
    }
    
    return response.json();
  }

  /**
   * 获取语言包数据
   */
  private async fetchLanguagePack(
    language: SupportedLanguage, 
    namespace: string,
    options: LoadOptions = {}
  ): Promise<Record<string, any>> {
    const { timeout = this.DEFAULT_TIMEOUT } = options;
    
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);
    
    try {
      const response = await fetch(`/locales/${language}/${namespace}.json`, {
        signal: controller.signal,
        headers: {
          'Cache-Control': options.force ? 'no-cache' : 'max-age=300'
        }
      });
      
      clearTimeout(timeoutId);
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      return await response.json();
    } catch (error) {
      clearTimeout(timeoutId);
      throw error;
    }
  }

  /**
   * 加载语言包（带重试机制）
   */
  private async loadWithRetry(
    language: SupportedLanguage,
    namespace: string,
    options: LoadOptions = {}
  ): Promise<Record<string, any>> {
    const { retries = this.DEFAULT_RETRIES, fallback = true } = options;
    let lastError: Error;
    
    for (let attempt = 0; attempt <= retries; attempt++) {
      try {
        const [meta, data] = await Promise.all([
          this.fetchMeta(language, namespace),
          this.fetchLanguagePack(language, namespace, options)
        ]);
        
        // 保存到缓存
        this.saveToCache(language, namespace, data, meta);
        
        return data;
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));
        
        if (attempt < retries) {
          // 指数退避重试
          const delay = Math.pow(2, attempt) * 1000;
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }
    
    // 如果启用回退且不是默认语言，尝试加载默认语言
    if (fallback && language !== 'zh-CN') {
      console.warn(`加载 ${language}:${namespace} 失败，回退到默认语言`);
      return this.loadWithRetry('zh-CN', namespace, { ...options, fallback: false });
    }
    
    throw lastError!;
  }

  /**
   * 加载语言包
   */
  async loadLanguagePack(
    language: SupportedLanguage,
    namespace: string = 'common',
    options: LoadOptions = {}
  ): Promise<Record<string, any>> {
    // 检查是否为支持的语言
    if (!(language in SUPPORTED_LANGUAGES)) {
      throw new Error(`不支持的语言: ${language}`);
    }
    
    // 如果不强制重新加载，先检查缓存
    if (!options.force) {
      const cached = this.getFromCache(language, namespace);
      if (cached) {
        return cached;
      }
    }
    
    // 检查是否已在加载中
    const loadingKey = this.getCacheKey(language, namespace);
    if (this.loadingPromises.has(loadingKey)) {
      return this.loadingPromises.get(loadingKey)!;
    }
    
    // 开始加载
    const loadingPromise = this.loadWithRetry(language, namespace, options)
      .finally(() => {
        this.loadingPromises.delete(loadingKey);
      });
    
    this.loadingPromises.set(loadingKey, loadingPromise);
    
    return loadingPromise;
  }

  /**
   * 预加载语言包
   */
  async preloadLanguagePack(language: SupportedLanguage, namespaces: string[] = ['common']): Promise<void> {
    const promises = namespaces.map(namespace => 
      this.loadLanguagePack(language, namespace).catch(error => {
        console.warn(`预加载 ${language}:${namespace} 失败:`, error);
      })
    );
    
    await Promise.allSettled(promises);
  }

  /**
   * 预加载所有支持的语言
   */
  async preloadAllLanguages(namespaces: string[] = ['common']): Promise<void> {
    const languages = Object.keys(SUPPORTED_LANGUAGES) as SupportedLanguage[];
    const promises = languages.map(language => 
      this.preloadLanguagePack(language, namespaces)
    );
    
    await Promise.allSettled(promises);
  }

  /**
   * 热更新语言包
   */
  async hotReload(language: SupportedLanguage, namespace: string = 'common'): Promise<void> {
    try {
      const data = await this.loadLanguagePack(language, namespace, { force: true });
      
      // 更新i18next资源
      i18n.addResourceBundle(language, namespace, data, true, true);
      
      console.log(`热更新完成: ${language}:${namespace}`);
    } catch (error) {
      console.error(`热更新失败: ${language}:${namespace}`, error);
      throw error;
    }
  }

  /**
   * 清理缓存
   */
  clearCache(language?: SupportedLanguage, namespace?: string): void {
    if (language && namespace) {
      // 清理特定语言包缓存
      const key = this.getCacheKey(language, namespace);
      this.cache.delete(key);
    } else if (language) {
      // 清理特定语言的所有缓存
      for (const key of this.cache.keys()) {
        if (key.startsWith(`${language}:`)) {
          this.cache.delete(key);
        }
      }
    } else {
      // 清理所有缓存
      this.cache.clear();
    }
  }

  /**
   * 获取缓存统计信息
   */
  getCacheStats(): {
    size: number;
    items: Array<{
      key: string;
      size: number;
      timestamp: number;
      expires: number;
      valid: boolean;
    }>;
  } {
    const items = Array.from(this.cache.entries()).map(([key, item]) => ({
      key,
      size: JSON.stringify(item.data).length,
      timestamp: item.timestamp,
      expires: item.expires,
      valid: this.isCacheValid(item)
    }));
    
    return {
      size: this.cache.size,
      items
    };
  }

  /**
   * 检查语言包完整性
   */
  async validateLanguagePack(language: SupportedLanguage, namespace: string = 'common'): Promise<boolean> {
    try {
      const data = await this.loadLanguagePack(language, namespace);
      
      // 基本完整性检查
      if (!data || typeof data !== 'object') {
        return false;
      }
      
      // 检查必要的键是否存在
      const requiredKeys = ['common', 'auth', 'navigation'];
      for (const key of requiredKeys) {
        if (!(key in data)) {
          console.warn(`语言包 ${language}:${namespace} 缺少必要键: ${key}`);
        }
      }
      
      return true;
    } catch (error) {
      console.error(`语言包验证失败: ${language}:${namespace}`, error);
      return false;
    }
  }
}

// 创建单例实例
export const languageManager = new LanguageManager();

export default LanguageManager;
