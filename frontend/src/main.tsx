import React, { Suspense } from 'react'
import ReactDOM from 'react-dom/client'
import { <PERSON><PERSON>erRouter } from 'react-router-dom'
import { Spin } from 'antd'
import ThemeProvider from './components/ThemeProvider'
import App from './App'
import './index.css'
import './i18n' // 导入i18n配置

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <Suspense fallback={
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh'
      }}>
        <Spin size="large" tip="加载中..." />
      </div>
    }>
      <ThemeProvider>
        <BrowserRouter>
          <App />
        </BrowserRouter>
      </ThemeProvider>
    </Suspense>
  </React.StrictMode>,
)
