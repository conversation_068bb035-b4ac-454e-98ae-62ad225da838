import React from 'react'
import { Routes, Route, Navigate } from 'react-router-dom'
import { useAuthStore } from './stores/authStore'
import LoginPage from './pages/LoginPage'
import RegisterPage from './pages/RegisterPage'
import MFAPage from './pages/MFAPage'
import DashboardPage from './pages/DashboardPage'
import ForgotPasswordPage from './pages/ForgotPasswordPage'
import ResetPasswordPage from './pages/ResetPasswordPage'
import DemoPage from './pages/DemoPage'
import OAuthErrorPage from './pages/OAuthErrorPage'
import I18nDemo from './pages/I18nDemo'
import ProtectedRoute from './components/ProtectedRoute'
import AdminApp from './components/admin/AdminApp'
import I18nProvider from './components/I18nProvider'

const App: React.FC = () => {
  const { isAuthenticated, isLoading } = useAuthStore()

  if (isLoading) {
    return (
      <div className="loading-container">
        <div>加载中...</div>
      </div>
    )
  }

  return (
    <I18nProvider>
      <Routes>
        {/* 公开路由 */}
        <Route
          path="/login"
          element={
            isAuthenticated ? <Navigate to="/dashboard" replace /> : <LoginPage />
          }
        />
        <Route
          path="/register"
          element={
            isAuthenticated ? <Navigate to="/dashboard" replace /> : <RegisterPage />
          }
        />
        <Route path="/forgot-password" element={<ForgotPasswordPage />} />
        <Route path="/reset-password" element={<ResetPasswordPage />} />
        <Route path="/mfa" element={<MFAPage />} />
        <Route path="/demo" element={<DemoPage />} />
        <Route path="/i18n-demo" element={<I18nDemo />} />
        <Route path="/auth/error" element={<OAuthErrorPage />} />
      
      {/* 受保护的路由 */}
      <Route
        path="/dashboard"
        element={
          <ProtectedRoute>
            <DashboardPage />
          </ProtectedRoute>
        }
      />

      {/* 管理员控制台 */}
      <Route
        path="/admin/*"
        element={
          <ProtectedRoute requireAdmin>
            <AdminApp />
          </ProtectedRoute>
        }
      />
      
      {/* 默认重定向 */}
      <Route 
        path="/" 
        element={
          <Navigate to={isAuthenticated ? "/dashboard" : "/login"} replace />
        } 
      />
      
      {/* 404 页面 */}
      <Route path="*" element={<Navigate to="/" replace />} />
    </Routes>
    </I18nProvider>
  )
}

export default App
