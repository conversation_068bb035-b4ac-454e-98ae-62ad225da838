# Grafana数据源配置
# 自动配置Prometheus数据源

apiVersion: 1

# 数据源列表
datasources:
  # Prometheus主数据源
  - name: Prometheus
    type: prometheus
    access: proxy
    url: http://prometheus:9090
    isDefault: true
    editable: true
    
    # 基础配置
    basicAuth: false
    withCredentials: false
    
    # JSON数据配置
    jsonData:
      # 查询配置
      timeInterval: "15s"
      queryTimeout: "60s"
      defaultRegion: "default"
      
      # HTTP配置
      httpMethod: "POST"
      keepCookies: []
      
      # 自定义查询参数
      customQueryParameters: ""
      
      # Prometheus特定配置
      prometheusType: "Prometheus"
      prometheusVersion: "2.45.0"
      
      # 示例查询
      exemplarTraceIdDestinations:
        - name: "trace_id"
          datasourceUid: "jaeger"
          url: "http://jaeger:16686/trace/$${__value.raw}"
    
    # 安全数据（加密存储）
    secureJsonData: {}
    
    # 版本
    version: 1
    
    # 只读模式
    readOnly: false

  # AlertManager数据源
  - name: AlertManager
    type: alertmanager
    access: proxy
    url: http://alertmanager:9093
    isDefault: false
    editable: true
    
    # 基础配置
    basicAuth: false
    withCredentials: false
    
    # JSON数据配置
    jsonData:
      # AlertManager特定配置
      implementation: "prometheus"
      
      # HTTP配置
      httpMethod: "GET"
      
      # 超时配置
      timeout: 60
    
    # 版本
    version: 1
    
    # 只读模式
    readOnly: false
