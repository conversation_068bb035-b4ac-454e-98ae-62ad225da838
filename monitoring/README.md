# ID Provider 监控系统

本文档介绍如何部署和使用ID Provider的监控系统，包括Prometheus、Graf<PERSON>、AlertManager等组件。

## 🏗️ 架构概览

监控系统包含以下组件：

- **Prometheus** - 指标收集和存储
- **Grafana** - 数据可视化和仪表板
- **AlertManager** - 告警管理和通知
- **Node Exporter** - 系统指标收集
- **Redis Exporter** - Redis指标收集
- **Postgres Exporter** - PostgreSQL指标收集
- **cAdvisor** - 容器指标收集

## 🚀 快速开始

### 1. 部署监控系统

```bash
# 使用部署脚本（推荐）
./scripts/deploy-monitoring.sh

# 或手动部署
docker-compose -f docker-compose.monitoring.yml up -d
```

### 2. 访问监控界面

- **Grafana**: http://localhost:3001 (admin/admin123!)
- **Prometheus**: http://localhost:9090
- **AlertManager**: http://localhost:9093

### 3. 验证指标收集

```bash
# 检查ID Provider指标
curl http://localhost:3000/metrics

# 检查Prometheus目标状态
curl http://localhost:9090/api/v1/targets
```

## 📊 监控指标

### HTTP请求指标

- `idp_http_requests_total` - HTTP请求总数
- `idp_http_request_duration_seconds` - HTTP请求持续时间
- `idp_http_requests_in_flight` - 正在处理的HTTP请求数

### 认证指标

- `idp_auth_attempts_total` - 认证尝试总数
- `idp_auth_success_total` - 认证成功总数
- `idp_active_sessions_total` - 活跃会话数

### 数据库指标

- `idp_database_queries_total` - 数据库查询总数
- `idp_database_query_duration_seconds` - 数据库查询持续时间
- `idp_database_connections_active` - 活跃数据库连接数

### Redis指标

- `idp_redis_commands_total` - Redis命令总数
- `idp_redis_command_duration_seconds` - Redis命令持续时间
- `idp_redis_connections_active` - 活跃Redis连接数

### 系统指标

- `idp_memory_usage_bytes` - 内存使用量
- `idp_cpu_usage_percent` - CPU使用率

## 🎯 Grafana仪表板

### 预配置仪表板

1. **ID Provider Overview** - 应用程序总览
   - HTTP请求速率
   - 响应时间分布
   - 内存使用趋势
   - 活跃会话数

### 自定义仪表板

可以根据需要创建自定义仪表板：

1. 登录Grafana
2. 点击"+"按钮 → "Dashboard"
3. 添加面板并配置查询
4. 保存仪表板

## 🚨 告警配置

### 预配置告警规则

- **HighErrorRate** - 错误率过高 (>5%)
- **HighResponseTime** - 响应时间过慢 (>2s)
- **HighAuthFailureRate** - 认证失败率过高 (>20%)
- **HighMemoryUsage** - 内存使用过高 (>80%)
- **ServiceDown** - 服务不可用

### 告警通知

告警通知支持多种方式：

- **邮件** - 发送到指定邮箱
- **Slack** - 发送到Slack频道
- **Webhook** - 自定义HTTP回调

### 配置告警接收器

编辑 `monitoring/alertmanager/alertmanager.yml`：

```yaml
receivers:
  - name: 'email-alerts'
    email_configs:
      - to: '<EMAIL>'
        subject: '[ID Provider] {{ .GroupLabels.alertname }}'
```

## 🔧 配置管理

### Prometheus配置

主配置文件：`monitoring/prometheus/prometheus.yml`

```yaml
scrape_configs:
  - job_name: 'id-provider'
    static_configs:
      - targets: ['host.docker.internal:3000']
    scrape_interval: 15s
    metrics_path: /metrics
```

### Grafana配置

数据源配置：`monitoring/grafana/provisioning/datasources/prometheus.yml`

```yaml
datasources:
  - name: Prometheus
    type: prometheus
    url: http://prometheus:9090
    isDefault: true
```

## 📈 性能优化

### 指标保留策略

Prometheus默认保留30天的数据：

```yaml
storage:
  tsdb:
    retention.time: 30d
    retention.size: 10GB
```

### 查询优化

- 使用适当的时间范围
- 避免高基数标签
- 使用recording rules预计算复杂查询

## 🛠️ 故障排除

### 常见问题

1. **Prometheus无法抓取指标**
   - 检查ID Provider应用是否运行
   - 验证指标端点可访问性
   - 检查防火墙设置

2. **Grafana无法连接Prometheus**
   - 检查数据源配置
   - 验证网络连接
   - 查看容器日志

3. **告警不工作**
   - 检查告警规则语法
   - 验证AlertManager配置
   - 测试通知渠道

### 日志查看

```bash
# 查看所有服务日志
docker-compose -f docker-compose.monitoring.yml logs -f

# 查看特定服务日志
docker-compose -f docker-compose.monitoring.yml logs -f prometheus
docker-compose -f docker-compose.monitoring.yml logs -f grafana
```

### 服务管理

```bash
# 启动服务
./scripts/deploy-monitoring.sh start

# 停止服务
./scripts/deploy-monitoring.sh stop

# 重启服务
./scripts/deploy-monitoring.sh restart

# 查看状态
./scripts/deploy-monitoring.sh status
```

## 🔒 安全考虑

### 访问控制

1. **Grafana认证**
   - 修改默认管理员密码
   - 配置LDAP/OAuth认证
   - 设置用户权限

2. **网络安全**
   - 使用防火墙限制访问
   - 配置TLS加密
   - 设置VPN访问

### 数据保护

- 定期备份监控数据
- 加密敏感配置
- 审计访问日志

## 📚 参考资料

- [Prometheus官方文档](https://prometheus.io/docs/)
- [Grafana官方文档](https://grafana.com/docs/)
- [AlertManager官方文档](https://prometheus.io/docs/alerting/latest/alertmanager/)
- [PromQL查询语言](https://prometheus.io/docs/prometheus/latest/querying/basics/)

## 🤝 贡献

欢迎提交问题和改进建议：

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 创建Pull Request

## 📄 许可证

本项目采用MIT许可证 - 查看[LICENSE](../LICENSE)文件了解详情。
