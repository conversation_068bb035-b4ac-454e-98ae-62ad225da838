# AlertManager配置文件
# 定义告警路由、接收器和抑制规则

global:
  # 全局配置
  smtp_smarthost: 'localhost:587'
  smtp_from: '<EMAIL>'
  smtp_auth_username: '<EMAIL>'
  smtp_auth_password: 'your-email-password'
  
  # 默认接收器
  smtp_require_tls: true
  
  # Slack配置
  slack_api_url: 'https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK'

# 模板配置
templates:
  - '/etc/alertmanager/templates/*.tmpl'

# 路由配置
route:
  # 默认接收器
  receiver: 'default-receiver'
  
  # 分组配置
  group_by: ['alertname', 'cluster', 'service']
  group_wait: 10s          # 等待时间
  group_interval: 10s      # 分组间隔
  repeat_interval: 1h      # 重复间隔
  
  # 子路由
  routes:
    # 关键告警 - 立即通知
    - match:
        severity: critical
      receiver: 'critical-alerts'
      group_wait: 0s
      group_interval: 5s
      repeat_interval: 15m
      
    # 安全告警 - 发送到安全团队
    - match:
        category: security
      receiver: 'security-team'
      group_wait: 5s
      group_interval: 10s
      repeat_interval: 30m
      
    # 数据库告警 - 发送到DBA团队
    - match:
        category: database
      receiver: 'dba-team'
      group_wait: 10s
      group_interval: 15s
      repeat_interval: 1h
      
    # 基础设施告警 - 发送到运维团队
    - match:
        category: infrastructure
      receiver: 'ops-team'
      group_wait: 10s
      group_interval: 15s
      repeat_interval: 1h
      
    # 应用程序告警 - 发送到开发团队
    - match:
        category: application
      receiver: 'dev-team'
      group_wait: 15s
      group_interval: 30s
      repeat_interval: 2h

# 抑制规则
inhibit_rules:
  # 服务宕机时抑制其他告警
  - source_match:
      alertname: ServiceDown
    target_match:
      service: id-provider
    equal: ['service', 'instance']
    
  # 高错误率时抑制响应时间告警
  - source_match:
      alertname: HighErrorRate
    target_match:
      alertname: HighResponseTime
    equal: ['service']
    
  # 内存告警时抑制CPU告警
  - source_match:
      alertname: HighSystemMemoryUsage
    target_match:
      alertname: HighCPUUsage
    equal: ['instance']

# 接收器配置
receivers:
  # 默认接收器
  - name: 'default-receiver'
    email_configs:
      - to: '<EMAIL>'
        subject: '[ID Provider] {{ .GroupLabels.alertname }} - {{ .Status | toUpper }}'
        body: |
          {{ range .Alerts }}
          告警: {{ .Annotations.summary }}
          描述: {{ .Annotations.description }}
          标签: {{ range .Labels.SortedPairs }}{{ .Name }}={{ .Value }} {{ end }}
          时间: {{ .StartsAt.Format "2006-01-02 15:04:05" }}
          {{ end }}

  # 关键告警接收器
  - name: 'critical-alerts'
    email_configs:
      - to: '<EMAIL>'
        subject: '🚨 [CRITICAL] {{ .GroupLabels.alertname }}'
        body: |
          ⚠️ 关键告警触发！
          
          {{ range .Alerts }}
          告警: {{ .Annotations.summary }}
          描述: {{ .Annotations.description }}
          服务: {{ .Labels.service }}
          严重程度: {{ .Labels.severity }}
          时间: {{ .StartsAt.Format "2006-01-02 15:04:05" }}
          运行手册: {{ .Annotations.runbook_url }}
          {{ end }}
    
    slack_configs:
      - api_url: 'https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK'
        channel: '#critical-alerts'
        title: '🚨 关键告警'
        text: |
          {{ range .Alerts }}
          *{{ .Annotations.summary }}*
          {{ .Annotations.description }}
          服务: {{ .Labels.service }}
          时间: {{ .StartsAt.Format "15:04:05" }}
          {{ end }}
        color: 'danger'

  # 安全团队接收器
  - name: 'security-team'
    email_configs:
      - to: '<EMAIL>'
        subject: '🔒 [SECURITY] {{ .GroupLabels.alertname }}'
        body: |
          🔒 安全告警触发！
          
          {{ range .Alerts }}
          告警: {{ .Annotations.summary }}
          描述: {{ .Annotations.description }}
          服务: {{ .Labels.service }}
          类别: {{ .Labels.category }}
          时间: {{ .StartsAt.Format "2006-01-02 15:04:05" }}
          {{ end }}
    
    slack_configs:
      - api_url: 'https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK'
        channel: '#security-alerts'
        title: '🔒 安全告警'
        text: |
          {{ range .Alerts }}
          *{{ .Annotations.summary }}*
          {{ .Annotations.description }}
          {{ end }}
        color: 'warning'

  # DBA团队接收器
  - name: 'dba-team'
    email_configs:
      - to: '<EMAIL>'
        subject: '🗄️ [DATABASE] {{ .GroupLabels.alertname }}'
        body: |
          🗄️ 数据库告警触发！
          
          {{ range .Alerts }}
          告警: {{ .Annotations.summary }}
          描述: {{ .Annotations.description }}
          服务: {{ .Labels.service }}
          时间: {{ .StartsAt.Format "2006-01-02 15:04:05" }}
          {{ end }}

  # 运维团队接收器
  - name: 'ops-team'
    email_configs:
      - to: '<EMAIL>'
        subject: '⚙️ [INFRASTRUCTURE] {{ .GroupLabels.alertname }}'
        body: |
          ⚙️ 基础设施告警触发！
          
          {{ range .Alerts }}
          告警: {{ .Annotations.summary }}
          描述: {{ .Annotations.description }}
          实例: {{ .Labels.instance }}
          时间: {{ .StartsAt.Format "2006-01-02 15:04:05" }}
          {{ end }}
    
    slack_configs:
      - api_url: 'https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK'
        channel: '#ops-alerts'
        title: '⚙️ 基础设施告警'
        text: |
          {{ range .Alerts }}
          *{{ .Annotations.summary }}*
          {{ .Annotations.description }}
          {{ end }}

  # 开发团队接收器
  - name: 'dev-team'
    email_configs:
      - to: '<EMAIL>'
        subject: '💻 [APPLICATION] {{ .GroupLabels.alertname }}'
        body: |
          💻 应用程序告警触发！
          
          {{ range .Alerts }}
          告警: {{ .Annotations.summary }}
          描述: {{ .Annotations.description }}
          服务: {{ .Labels.service }}
          时间: {{ .StartsAt.Format "2006-01-02 15:04:05" }}
          运行手册: {{ .Annotations.runbook_url }}
          {{ end }}
    
    slack_configs:
      - api_url: 'https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK'
        channel: '#dev-alerts'
        title: '💻 应用告警'
        text: |
          {{ range .Alerts }}
          *{{ .Annotations.summary }}*
          {{ .Annotations.description }}
          {{ end }}
        color: 'good'
