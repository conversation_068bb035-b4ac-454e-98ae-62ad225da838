# ID Provider告警规则
# 定义应用程序相关的告警条件和阈值

groups:
  # 应用程序告警组
  - name: id-provider-app
    interval: 30s
    rules:
      # 高错误率告警
      - alert: HighErrorRate
        expr: |
          (
            rate(idp_http_requests_total{status_code=~"5.."}[5m]) /
            rate(idp_http_requests_total[5m])
          ) * 100 > 5
        for: 2m
        labels:
          severity: warning
          service: id-provider
          category: application
        annotations:
          summary: "ID Provider高错误率"
          description: "ID Provider在过去5分钟内的错误率为 {{ $value | humanizePercentage }}，超过5%阈值"
          runbook_url: "https://docs.example.com/runbooks/high-error-rate"

      # 响应时间过慢告警
      - alert: HighResponseTime
        expr: |
          histogram_quantile(0.95, 
            rate(idp_http_request_duration_seconds_bucket[5m])
          ) > 2
        for: 3m
        labels:
          severity: warning
          service: id-provider
          category: performance
        annotations:
          summary: "ID Provider响应时间过慢"
          description: "ID Provider的95%响应时间为 {{ $value | humanizeDuration }}，超过2秒阈值"
          runbook_url: "https://docs.example.com/runbooks/high-response-time"

      # 认证失败率过高告警
      - alert: HighAuthFailureRate
        expr: |
          (
            rate(idp_auth_attempts_total{status="failure"}[5m]) /
            rate(idp_auth_attempts_total[5m])
          ) * 100 > 20
        for: 2m
        labels:
          severity: warning
          service: id-provider
          category: security
        annotations:
          summary: "ID Provider认证失败率过高"
          description: "ID Provider在过去5分钟内的认证失败率为 {{ $value | humanizePercentage }}，超过20%阈值"
          runbook_url: "https://docs.example.com/runbooks/high-auth-failure-rate"

      # 内存使用过高告警
      - alert: HighMemoryUsage
        expr: |
          (idp_memory_usage_bytes{type="heap_used"} / idp_memory_usage_bytes{type="heap_total"}) * 100 > 80
        for: 5m
        labels:
          severity: warning
          service: id-provider
          category: resource
        annotations:
          summary: "ID Provider内存使用过高"
          description: "ID Provider的堆内存使用率为 {{ $value | humanizePercentage }}，超过80%阈值"
          runbook_url: "https://docs.example.com/runbooks/high-memory-usage"

      # 数据库查询时间过长告警
      - alert: SlowDatabaseQueries
        expr: |
          histogram_quantile(0.95,
            rate(idp_database_query_duration_seconds_bucket[5m])
          ) > 0.5
        for: 3m
        labels:
          severity: warning
          service: id-provider
          category: database
        annotations:
          summary: "ID Provider数据库查询缓慢"
          description: "ID Provider的95%数据库查询时间为 {{ $value | humanizeDuration }}，超过500ms阈值"
          runbook_url: "https://docs.example.com/runbooks/slow-database-queries"

      # Redis连接失败告警
      - alert: RedisConnectionDown
        expr: idp_redis_connections_active < 1
        for: 1m
        labels:
          severity: critical
          service: id-provider
          category: infrastructure
        annotations:
          summary: "ID Provider Redis连接断开"
          description: "ID Provider无法连接到Redis服务器"
          runbook_url: "https://docs.example.com/runbooks/redis-connection-down"

      # 活跃会话数异常告警
      - alert: UnusualActiveSessionCount
        expr: |
          (
            idp_active_sessions_total > 
            (avg_over_time(idp_active_sessions_total[1h]) * 2)
          ) or (
            idp_active_sessions_total < 
            (avg_over_time(idp_active_sessions_total[1h]) * 0.5)
          )
        for: 5m
        labels:
          severity: warning
          service: id-provider
          category: business
        annotations:
          summary: "ID Provider活跃会话数异常"
          description: "ID Provider当前活跃会话数为 {{ $value }}，与过去1小时平均值相比异常"
          runbook_url: "https://docs.example.com/runbooks/unusual-session-count"

  # 基础设施告警组
  - name: id-provider-infrastructure
    interval: 30s
    rules:
      # 服务不可用告警
      - alert: ServiceDown
        expr: up{job="id-provider"} == 0
        for: 1m
        labels:
          severity: critical
          service: id-provider
          category: availability
        annotations:
          summary: "ID Provider服务不可用"
          description: "ID Provider服务已停止响应超过1分钟"
          runbook_url: "https://docs.example.com/runbooks/service-down"

      # 系统CPU使用率过高告警
      - alert: HighCPUUsage
        expr: |
          (
            100 - (avg by (instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100)
          ) > 80
        for: 5m
        labels:
          severity: warning
          service: id-provider
          category: resource
        annotations:
          summary: "系统CPU使用率过高"
          description: "系统CPU使用率为 {{ $value | humanizePercentage }}，超过80%阈值"
          runbook_url: "https://docs.example.com/runbooks/high-cpu-usage"

      # 系统内存使用率过高告警
      - alert: HighSystemMemoryUsage
        expr: |
          (
            (node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes) / 
            node_memory_MemTotal_bytes
          ) * 100 > 85
        for: 5m
        labels:
          severity: warning
          service: id-provider
          category: resource
        annotations:
          summary: "系统内存使用率过高"
          description: "系统内存使用率为 {{ $value | humanizePercentage }}，超过85%阈值"
          runbook_url: "https://docs.example.com/runbooks/high-system-memory-usage"

      # 磁盘空间不足告警
      - alert: LowDiskSpace
        expr: |
          (
            (node_filesystem_size_bytes - node_filesystem_free_bytes) /
            node_filesystem_size_bytes
          ) * 100 > 85
        for: 5m
        labels:
          severity: warning
          service: id-provider
          category: resource
        annotations:
          summary: "磁盘空间不足"
          description: "磁盘 {{ $labels.mountpoint }} 使用率为 {{ $value | humanizePercentage }}，超过85%阈值"
          runbook_url: "https://docs.example.com/runbooks/low-disk-space"

  # 数据库告警组
  - name: id-provider-database
    interval: 30s
    rules:
      # PostgreSQL连接数过多告警
      - alert: HighPostgreSQLConnections
        expr: |
          pg_stat_database_numbackends / pg_settings_max_connections * 100 > 80
        for: 3m
        labels:
          severity: warning
          service: id-provider
          category: database
        annotations:
          summary: "PostgreSQL连接数过多"
          description: "PostgreSQL连接使用率为 {{ $value | humanizePercentage }}，超过80%阈值"
          runbook_url: "https://docs.example.com/runbooks/high-postgresql-connections"

      # PostgreSQL慢查询告警
      - alert: PostgreSQLSlowQueries
        expr: |
          rate(pg_stat_database_tup_returned[5m]) / rate(pg_stat_database_tup_fetched[5m]) < 0.1
        for: 5m
        labels:
          severity: warning
          service: id-provider
          category: database
        annotations:
          summary: "PostgreSQL存在慢查询"
          description: "PostgreSQL查询效率较低，可能存在慢查询问题"
          runbook_url: "https://docs.example.com/runbooks/postgresql-slow-queries"

  # Redis告警组
  - name: id-provider-redis
    interval: 30s
    rules:
      # Redis内存使用率过高告警
      - alert: HighRedisMemoryUsage
        expr: |
          (redis_memory_used_bytes / redis_memory_max_bytes) * 100 > 80
        for: 3m
        labels:
          severity: warning
          service: id-provider
          category: cache
        annotations:
          summary: "Redis内存使用率过高"
          description: "Redis内存使用率为 {{ $value | humanizePercentage }}，超过80%阈值"
          runbook_url: "https://docs.example.com/runbooks/high-redis-memory-usage"

      # Redis连接数过多告警
      - alert: HighRedisConnections
        expr: redis_connected_clients > 100
        for: 3m
        labels:
          severity: warning
          service: id-provider
          category: cache
        annotations:
          summary: "Redis连接数过多"
          description: "Redis当前连接数为 {{ $value }}，超过100个连接"
          runbook_url: "https://docs.example.com/runbooks/high-redis-connections"

      # Redis缓存命中率过低告警
      - alert: LowRedisCacheHitRate
        expr: |
          (
            rate(redis_keyspace_hits_total[5m]) /
            (rate(redis_keyspace_hits_total[5m]) + rate(redis_keyspace_misses_total[5m]))
          ) * 100 < 80
        for: 5m
        labels:
          severity: warning
          service: id-provider
          category: cache
        annotations:
          summary: "Redis缓存命中率过低"
          description: "Redis缓存命中率为 {{ $value | humanizePercentage }}，低于80%阈值"
          runbook_url: "https://docs.example.com/runbooks/low-redis-cache-hit-rate"
