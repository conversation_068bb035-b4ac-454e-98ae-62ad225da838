# Prometheus配置文件
# 定义数据收集目标、规则和告警配置

global:
  # 全局配置
  scrape_interval: 15s          # 默认抓取间隔
  evaluation_interval: 15s      # 规则评估间隔
  scrape_timeout: 10s           # 抓取超时时间
  
  # 外部标签 - 添加到所有时间序列
  external_labels:
    cluster: 'id-provider'
    environment: 'production'

# 规则文件配置
rule_files:
  - "rules/*.yml"

# 告警管理器配置
alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

# 抓取配置
scrape_configs:
  # Prometheus自身监控
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']
    scrape_interval: 30s
    metrics_path: /metrics

  # ID Provider应用监控
  - job_name: 'id-provider'
    static_configs:
      - targets: ['host.docker.internal:3000']
    scrape_interval: 15s
    metrics_path: /metrics
    scrape_timeout: 10s
    honor_labels: true
    params:
      format: ['prometheus']
    relabel_configs:
      # 添加实例标签
      - source_labels: [__address__]
        target_label: instance
        replacement: 'id-provider-app'
      # 添加服务标签
      - target_label: service
        replacement: 'id-provider'

  # Node Exporter - 系统指标
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']
    scrape_interval: 30s
    metrics_path: /metrics
    relabel_configs:
      - source_labels: [__address__]
        target_label: instance
        replacement: 'id-provider-host'

  # Redis Exporter - Redis指标
  - job_name: 'redis-exporter'
    static_configs:
      - targets: ['redis-exporter:9121']
    scrape_interval: 30s
    metrics_path: /metrics
    relabel_configs:
      - source_labels: [__address__]
        target_label: instance
        replacement: 'id-provider-redis'

  # Postgres Exporter - PostgreSQL指标
  - job_name: 'postgres-exporter'
    static_configs:
      - targets: ['postgres-exporter:9187']
    scrape_interval: 30s
    metrics_path: /metrics
    relabel_configs:
      - source_labels: [__address__]
        target_label: instance
        replacement: 'id-provider-postgres'

  # cAdvisor - 容器指标
  - job_name: 'cadvisor'
    static_configs:
      - targets: ['cadvisor:8080']
    scrape_interval: 30s
    metrics_path: /metrics
    relabel_configs:
      - source_labels: [__address__]
        target_label: instance
        replacement: 'id-provider-containers'

  # AlertManager监控
  - job_name: 'alertmanager'
    static_configs:
      - targets: ['alertmanager:9093']
    scrape_interval: 30s
    metrics_path: /metrics

# 存储配置
storage:
  tsdb:
    # 数据保留期
    retention.time: 30d
    retention.size: 10GB
    
    # 压缩配置
    min-block-duration: 2h
    max-block-duration: 25h
    
    # WAL配置
    wal-compression: true

# 远程写入配置（可选）
# remote_write:
#   - url: "https://your-remote-storage/api/v1/write"
#     basic_auth:
#       username: "your-username"
#       password: "your-password"

# 远程读取配置（可选）
# remote_read:
#   - url: "https://your-remote-storage/api/v1/read"
#     basic_auth:
#       username: "your-username"
#       password: "your-password"
