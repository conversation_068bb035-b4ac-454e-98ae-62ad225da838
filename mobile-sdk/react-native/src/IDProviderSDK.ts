/**
 * ID Provider React Native SDK
 * 
 * 功能说明：
 * 1. 跨平台身份认证功能
 * 2. 统一的API接口
 * 3. 生物识别集成
 * 4. 安全存储管理
 * 5. 设备指纹收集
 */

import { NativeModules, Platform, NativeEventEmitter } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { NetworkInfo } from '@react-native-community/netinfo';
import DeviceInfo from 'react-native-device-info';

// 原生模块接口
interface IDProviderNativeModule {
  initialize(config: IDProviderConfig): Promise<void>;
  login(username: string, password: string): Promise<AuthResult>;
  loginWithBiometric(): Promise<AuthResult>;
  verifyMFA(sessionId: string, method: string, code: string): Promise<AuthResult>;
  logout(): Promise<void>;
  refreshToken(): Promise<string>;
  getCurrentUser(): Promise<UserInfo>;
  isAuthenticated(): Promise<boolean>;
  getAccessToken(): Promise<string | null>;
  enableBiometric(): Promise<void>;
  getDeviceInfo(): Promise<DeviceInfo>;
  generateDeviceFingerprint(): Promise<string>;
  checkBiometricAvailability(): Promise<BiometricAvailability>;
}

/**
 * SDK配置接口
 */
export interface IDProviderConfig {
  baseURL: string;
  clientId: string;
  clientSecret: string;
  biometricEnabled?: boolean;
  deviceFingerprintEnabled?: boolean;
  debugMode?: boolean;
  timeout?: number;
  retryAttempts?: number;
}

/**
 * 认证结果类型
 */
export type AuthResult = 
  | { success: true; token: string; refreshToken: string; user: UserInfo }
  | { success: false; requiresMFA: true; sessionId: string; methods: MFAMethod[] }
  | { success: false; requiresBiometric: true; sessionId: string }
  | { success: false; blocked: true; reason: string }
  | { success: false; error: string };

/**
 * MFA方法枚举
 */
export enum MFAMethod {
  SMS = 'sms',
  TOTP = 'totp',
  PUSH = 'push',
  BIOMETRIC = 'biometric'
}

/**
 * 用户信息接口
 */
export interface UserInfo {
  id: string;
  email: string;
  name: string;
  roles: string[];
  permissions: string[];
  lastLoginAt?: Date;
  createdAt: Date;
}

/**
 * 设备信息接口
 */
export interface DeviceInfo {
  deviceId: string;
  model: string;
  systemVersion: string;
  appVersion: string;
  fingerprint: string;
  trustLevel: number;
  isRooted: boolean;
  isSimulator: boolean;
}

/**
 * 生物识别可用性接口
 */
export interface BiometricAvailability {
  isAvailable: boolean;
  biometryType: 'TouchID' | 'FaceID' | 'Fingerprint' | 'Face' | 'None';
  error?: string;
}

/**
 * SDK错误类型
 */
export class IDProviderError extends Error {
  constructor(
    message: string,
    public code: string,
    public details?: any
  ) {
    super(message);
    this.name = 'IDProviderError';
  }
}

/**
 * ID Provider React Native SDK主类
 */
export class IDProviderSDK {
  private static instance: IDProviderSDK;
  private nativeModule: IDProviderNativeModule;
  private eventEmitter: NativeEventEmitter;
  private config?: IDProviderConfig;
  private isInitialized = false;

  // 存储键常量
  private static readonly STORAGE_KEYS = {
    ACCESS_TOKEN: 'IDProvider_AccessToken',
    REFRESH_TOKEN: 'IDProvider_RefreshToken',
    USER_INFO: 'IDProvider_UserInfo',
    DEVICE_ID: 'IDProvider_DeviceId',
    CONFIG: 'IDProvider_Config'
  };

  private constructor() {
    this.nativeModule = NativeModules.IDProviderSDK;
    this.eventEmitter = new NativeEventEmitter(this.nativeModule);
    
    if (!this.nativeModule) {
      throw new IDProviderError(
        'ID Provider native module not found. Please ensure the SDK is properly installed.',
        'NATIVE_MODULE_NOT_FOUND'
      );
    }
  }

  /**
   * 获取SDK单例实例
   */
  public static getInstance(): IDProviderSDK {
    if (!IDProviderSDK.instance) {
      IDProviderSDK.instance = new IDProviderSDK();
    }
    return IDProviderSDK.instance;
  }

  /**
   * 初始化SDK
   */
  public async initialize(config: IDProviderConfig): Promise<void> {
    try {
      this.config = {
        biometricEnabled: true,
        deviceFingerprintEnabled: true,
        debugMode: false,
        timeout: 30000,
        retryAttempts: 3,
        ...config
      };

      // 保存配置到本地存储
      await AsyncStorage.setItem(
        IDProviderSDK.STORAGE_KEYS.CONFIG,
        JSON.stringify(this.config)
      );

      // 初始化原生模块
      await this.nativeModule.initialize(this.config);

      this.isInitialized = true;

      if (this.config.debugMode) {
        console.log('[IDProviderSDK] SDK initialized successfully', this.config);
      }

    } catch (error) {
      throw new IDProviderError(
        'Failed to initialize SDK',
        'INITIALIZATION_FAILED',
        error
      );
    }
  }

  /**
   * 用户名密码登录
   */
  public async login(username: string, password: string): Promise<AuthResult> {
    this.ensureInitialized();

    try {
      const result = await this.nativeModule.login(username, password);
      
      if (result.success) {
        // 保存令牌和用户信息
        await this.saveTokens(result.token, result.refreshToken);
        await this.saveUserInfo(result.user);
      }

      return result;

    } catch (error) {
      throw new IDProviderError(
        'Login failed',
        'LOGIN_FAILED',
        error
      );
    }
  }

  /**
   * 生物识别登录
   */
  public async loginWithBiometric(): Promise<AuthResult> {
    this.ensureInitialized();

    try {
      // 检查生物识别可用性
      const availability = await this.checkBiometricAvailability();
      if (!availability.isAvailable) {
        return {
          success: false,
          error: availability.error || 'Biometric authentication not available'
        };
      }

      const result = await this.nativeModule.loginWithBiometric();
      
      if (result.success) {
        await this.saveTokens(result.token, result.refreshToken);
        await this.saveUserInfo(result.user);
      }

      return result;

    } catch (error) {
      throw new IDProviderError(
        'Biometric login failed',
        'BIOMETRIC_LOGIN_FAILED',
        error
      );
    }
  }

  /**
   * MFA验证
   */
  public async verifyMFA(
    sessionId: string,
    method: MFAMethod,
    code: string
  ): Promise<AuthResult> {
    this.ensureInitialized();

    try {
      const result = await this.nativeModule.verifyMFA(sessionId, method, code);
      
      if (result.success) {
        await this.saveTokens(result.token, result.refreshToken);
        await this.saveUserInfo(result.user);
      }

      return result;

    } catch (error) {
      throw new IDProviderError(
        'MFA verification failed',
        'MFA_VERIFICATION_FAILED',
        error
      );
    }
  }

  /**
   * 刷新访问令牌
   */
  public async refreshToken(): Promise<string> {
    this.ensureInitialized();

    try {
      const newToken = await this.nativeModule.refreshToken();
      await AsyncStorage.setItem(IDProviderSDK.STORAGE_KEYS.ACCESS_TOKEN, newToken);
      return newToken;

    } catch (error) {
      // 刷新失败，清除本地令牌
      await this.clearTokens();
      throw new IDProviderError(
        'Token refresh failed',
        'TOKEN_REFRESH_FAILED',
        error
      );
    }
  }

  /**
   * 用户登出
   */
  public async logout(): Promise<void> {
    this.ensureInitialized();

    try {
      await this.nativeModule.logout();
      await this.clearLocalData();

    } catch (error) {
      // 即使服务器登出失败，也要清除本地数据
      await this.clearLocalData();
      throw new IDProviderError(
        'Logout failed',
        'LOGOUT_FAILED',
        error
      );
    }
  }

  /**
   * 获取当前用户信息
   */
  public async getCurrentUser(): Promise<UserInfo | null> {
    this.ensureInitialized();

    try {
      // 先尝试从本地存储获取
      const cachedUser = await this.getCachedUserInfo();
      if (cachedUser) {
        return cachedUser;
      }

      // 从服务器获取最新信息
      const user = await this.nativeModule.getCurrentUser();
      await this.saveUserInfo(user);
      return user;

    } catch (error) {
      throw new IDProviderError(
        'Failed to get current user',
        'GET_USER_FAILED',
        error
      );
    }
  }

  /**
   * 检查认证状态
   */
  public async isAuthenticated(): Promise<boolean> {
    this.ensureInitialized();

    try {
      const hasToken = await this.getAccessToken() !== null;
      if (!hasToken) return false;

      return await this.nativeModule.isAuthenticated();

    } catch (error) {
      return false;
    }
  }

  /**
   * 获取访问令牌
   */
  public async getAccessToken(): Promise<string | null> {
    try {
      return await AsyncStorage.getItem(IDProviderSDK.STORAGE_KEYS.ACCESS_TOKEN);
    } catch (error) {
      return null;
    }
  }

  /**
   * 启用生物识别
   */
  public async enableBiometric(): Promise<void> {
    this.ensureInitialized();

    try {
      const availability = await this.checkBiometricAvailability();
      if (!availability.isAvailable) {
        throw new IDProviderError(
          availability.error || 'Biometric authentication not available',
          'BIOMETRIC_NOT_AVAILABLE'
        );
      }

      await this.nativeModule.enableBiometric();

    } catch (error) {
      throw new IDProviderError(
        'Failed to enable biometric authentication',
        'ENABLE_BIOMETRIC_FAILED',
        error
      );
    }
  }

  /**
   * 检查生物识别可用性
   */
  public async checkBiometricAvailability(): Promise<BiometricAvailability> {
    this.ensureInitialized();

    try {
      return await this.nativeModule.checkBiometricAvailability();
    } catch (error) {
      return {
        isAvailable: false,
        biometryType: 'None',
        error: error.message
      };
    }
  }

  /**
   * 获取设备信息
   */
  public async getDeviceInfo(): Promise<DeviceInfo> {
    this.ensureInitialized();

    try {
      // 合并原生设备信息和React Native设备信息
      const [nativeDeviceInfo, rnDeviceInfo] = await Promise.all([
        this.nativeModule.getDeviceInfo(),
        this.collectReactNativeDeviceInfo()
      ]);

      return {
        ...nativeDeviceInfo,
        ...rnDeviceInfo
      };

    } catch (error) {
      throw new IDProviderError(
        'Failed to get device info',
        'GET_DEVICE_INFO_FAILED',
        error
      );
    }
  }

  /**
   * 生成设备指纹
   */
  public async generateDeviceFingerprint(): Promise<string> {
    this.ensureInitialized();

    try {
      return await this.nativeModule.generateDeviceFingerprint();
    } catch (error) {
      throw new IDProviderError(
        'Failed to generate device fingerprint',
        'GENERATE_FINGERPRINT_FAILED',
        error
      );
    }
  }

  /**
   * 监听SDK事件
   */
  public addEventListener(
    eventType: 'tokenExpired' | 'biometricChanged' | 'securityEvent',
    listener: (event: any) => void
  ): () => void {
    const subscription = this.eventEmitter.addListener(eventType, listener);
    return () => subscription.remove();
  }

  // 私有方法

  private ensureInitialized(): void {
    if (!this.isInitialized) {
      throw new IDProviderError(
        'SDK not initialized. Call initialize() first.',
        'SDK_NOT_INITIALIZED'
      );
    }
  }

  private async saveTokens(accessToken: string, refreshToken: string): Promise<void> {
    await Promise.all([
      AsyncStorage.setItem(IDProviderSDK.STORAGE_KEYS.ACCESS_TOKEN, accessToken),
      AsyncStorage.setItem(IDProviderSDK.STORAGE_KEYS.REFRESH_TOKEN, refreshToken)
    ]);
  }

  private async saveUserInfo(user: UserInfo): Promise<void> {
    await AsyncStorage.setItem(
      IDProviderSDK.STORAGE_KEYS.USER_INFO,
      JSON.stringify(user)
    );
  }

  private async getCachedUserInfo(): Promise<UserInfo | null> {
    try {
      const userJson = await AsyncStorage.getItem(IDProviderSDK.STORAGE_KEYS.USER_INFO);
      return userJson ? JSON.parse(userJson) : null;
    } catch (error) {
      return null;
    }
  }

  private async clearTokens(): Promise<void> {
    await Promise.all([
      AsyncStorage.removeItem(IDProviderSDK.STORAGE_KEYS.ACCESS_TOKEN),
      AsyncStorage.removeItem(IDProviderSDK.STORAGE_KEYS.REFRESH_TOKEN)
    ]);
  }

  private async clearLocalData(): Promise<void> {
    await Promise.all([
      AsyncStorage.removeItem(IDProviderSDK.STORAGE_KEYS.ACCESS_TOKEN),
      AsyncStorage.removeItem(IDProviderSDK.STORAGE_KEYS.REFRESH_TOKEN),
      AsyncStorage.removeItem(IDProviderSDK.STORAGE_KEYS.USER_INFO)
    ]);
  }

  private async collectReactNativeDeviceInfo(): Promise<Partial<DeviceInfo>> {
    try {
      const [
        deviceId,
        model,
        systemVersion,
        appVersion,
        isEmulator
      ] = await Promise.all([
        DeviceInfo.getUniqueId(),
        DeviceInfo.getModel(),
        DeviceInfo.getSystemVersion(),
        DeviceInfo.getVersion(),
        DeviceInfo.isEmulator()
      ]);

      return {
        deviceId,
        model,
        systemVersion,
        appVersion,
        isSimulator: isEmulator
      };

    } catch (error) {
      console.warn('[IDProviderSDK] Failed to collect React Native device info:', error);
      return {};
    }
  }
}

// 导出默认实例
export default IDProviderSDK.getInstance();
