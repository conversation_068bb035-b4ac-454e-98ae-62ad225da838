/**
 * ID Provider Flutter SDK
 * 
 * 功能说明：
 * 1. Flutter跨平台身份认证
 * 2. 统一的Dart API接口
 * 3. 生物识别集成
 * 4. 安全存储管理
 * 5. 设备指纹收集
 */

import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:local_auth/local_auth.dart';
import 'package:crypto/crypto.dart';

/// SDK配置类
class IDProviderConfig {
  final String baseURL;
  final String clientId;
  final String clientSecret;
  final bool biometricEnabled;
  final bool deviceFingerprintEnabled;
  final bool debugMode;
  final int timeout;
  final int retryAttempts;

  const IDProviderConfig({
    required this.baseURL,
    required this.clientId,
    required this.clientSecret,
    this.biometricEnabled = true,
    this.deviceFingerprintEnabled = true,
    this.debugMode = false,
    this.timeout = 30000,
    this.retryAttempts = 3,
  });

  Map<String, dynamic> toMap() {
    return {
      'baseURL': baseURL,
      'clientId': clientId,
      'clientSecret': clientSecret,
      'biometricEnabled': biometricEnabled,
      'deviceFingerprintEnabled': deviceFingerprintEnabled,
      'debugMode': debugMode,
      'timeout': timeout,
      'retryAttempts': retryAttempts,
    };
  }
}

/// MFA方法枚举
enum MFAMethod {
  sms('sms', '短信验证'),
  totp('totp', 'TOTP验证'),
  push('push', '推送验证'),
  biometric('biometric', '生物识别');

  const MFAMethod(this.value, this.displayName);
  final String value;
  final String displayName;
}

/// 认证结果类
abstract class AuthResult {
  const AuthResult();
}

class AuthSuccess extends AuthResult {
  final String token;
  final String refreshToken;
  final UserInfo user;

  const AuthSuccess({
    required this.token,
    required this.refreshToken,
    required this.user,
  });
}

class AuthRequiresMFA extends AuthResult {
  final String sessionId;
  final List<MFAMethod> methods;

  const AuthRequiresMFA({
    required this.sessionId,
    required this.methods,
  });
}

class AuthRequiresBiometric extends AuthResult {
  final String sessionId;

  const AuthRequiresBiometric({required this.sessionId});
}

class AuthBlocked extends AuthResult {
  final String reason;

  const AuthBlocked({required this.reason});
}

class AuthFailed extends AuthResult {
  final String error;
  final String? code;

  const AuthFailed({required this.error, this.code});
}

/// 用户信息类
class UserInfo {
  final String id;
  final String email;
  final String name;
  final List<String> roles;
  final List<String> permissions;
  final DateTime? lastLoginAt;
  final DateTime createdAt;

  const UserInfo({
    required this.id,
    required this.email,
    required this.name,
    required this.roles,
    required this.permissions,
    this.lastLoginAt,
    required this.createdAt,
  });

  factory UserInfo.fromMap(Map<String, dynamic> map) {
    return UserInfo(
      id: map['id'],
      email: map['email'],
      name: map['name'],
      roles: List<String>.from(map['roles']),
      permissions: List<String>.from(map['permissions']),
      lastLoginAt: map['lastLoginAt'] != null 
          ? DateTime.parse(map['lastLoginAt']) 
          : null,
      createdAt: DateTime.parse(map['createdAt']),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'email': email,
      'name': name,
      'roles': roles,
      'permissions': permissions,
      'lastLoginAt': lastLoginAt?.toIso8601String(),
      'createdAt': createdAt.toIso8601String(),
    };
  }
}

/// 设备信息类
class DeviceInfo {
  final String deviceId;
  final String model;
  final String systemVersion;
  final String appVersion;
  final String fingerprint;
  final double trustLevel;
  final bool isRooted;
  final bool isSimulator;

  const DeviceInfo({
    required this.deviceId,
    required this.model,
    required this.systemVersion,
    required this.appVersion,
    required this.fingerprint,
    required this.trustLevel,
    required this.isRooted,
    required this.isSimulator,
  });

  factory DeviceInfo.fromMap(Map<String, dynamic> map) {
    return DeviceInfo(
      deviceId: map['deviceId'],
      model: map['model'],
      systemVersion: map['systemVersion'],
      appVersion: map['appVersion'],
      fingerprint: map['fingerprint'],
      trustLevel: map['trustLevel'].toDouble(),
      isRooted: map['isRooted'],
      isSimulator: map['isSimulator'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'deviceId': deviceId,
      'model': model,
      'systemVersion': systemVersion,
      'appVersion': appVersion,
      'fingerprint': fingerprint,
      'trustLevel': trustLevel,
      'isRooted': isRooted,
      'isSimulator': isSimulator,
    };
  }
}

/// 生物识别可用性类
class BiometricAvailability {
  final bool isAvailable;
  final BiometricType biometryType;
  final String? error;

  const BiometricAvailability({
    required this.isAvailable,
    required this.biometryType,
    this.error,
  });
}

/// SDK异常类
class IDProviderException implements Exception {
  final String message;
  final String code;
  final dynamic details;

  const IDProviderException(this.message, this.code, [this.details]);

  @override
  String toString() => 'IDProviderException: $message (code: $code)';
}

/// ID Provider Flutter SDK主类
class IDProviderSDK {
  static IDProviderSDK? _instance;
  static const MethodChannel _channel = MethodChannel('id_provider_sdk');
  static const EventChannel _eventChannel = EventChannel('id_provider_sdk_events');

  IDProviderConfig? _config;
  bool _isInitialized = false;
  StreamSubscription? _eventSubscription;
  final LocalAuthentication _localAuth = LocalAuthentication();
  final DeviceInfoPlugin _deviceInfoPlugin = DeviceInfoPlugin();

  // 存储键常量
  static const String _accessTokenKey = 'IDProvider_AccessToken';
  static const String _refreshTokenKey = 'IDProvider_RefreshToken';
  static const String _userInfoKey = 'IDProvider_UserInfo';
  static const String _deviceIdKey = 'IDProvider_DeviceId';
  static const String _configKey = 'IDProvider_Config';

  IDProviderSDK._();

  /// 获取SDK单例实例
  static IDProviderSDK get instance {
    _instance ??= IDProviderSDK._();
    return _instance!;
  }

  /// 初始化SDK
  Future<void> initialize(IDProviderConfig config) async {
    try {
      _config = config;

      // 保存配置到本地存储
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_configKey, jsonEncode(config.toMap()));

      // 初始化原生模块
      await _channel.invokeMethod('initialize', config.toMap());

      // 监听事件
      _eventSubscription = _eventChannel.receiveBroadcastStream().listen(
        _handleEvent,
        onError: _handleEventError,
      );

      _isInitialized = true;

      if (config.debugMode) {
        debugPrint('[IDProviderSDK] SDK initialized successfully');
      }
    } catch (e) {
      throw IDProviderException(
        'Failed to initialize SDK',
        'INITIALIZATION_FAILED',
        e,
      );
    }
  }

  /// 用户名密码登录
  Future<AuthResult> login(String username, String password) async {
    _ensureInitialized();

    try {
      final result = await _channel.invokeMethod('login', {
        'username': username,
        'password': password,
      });

      final authResult = _parseAuthResult(result);
      
      if (authResult is AuthSuccess) {
        await _saveTokens(authResult.token, authResult.refreshToken);
        await _saveUserInfo(authResult.user);
      }

      return authResult;
    } catch (e) {
      throw IDProviderException('Login failed', 'LOGIN_FAILED', e);
    }
  }

  /// 生物识别登录
  Future<AuthResult> loginWithBiometric() async {
    _ensureInitialized();

    try {
      // 检查生物识别可用性
      final availability = await checkBiometricAvailability();
      if (!availability.isAvailable) {
        return AuthFailed(
          error: availability.error ?? 'Biometric authentication not available',
          code: 'BIOMETRIC_NOT_AVAILABLE',
        );
      }

      // 执行生物识别验证
      final isAuthenticated = await _localAuth.authenticate(
        localizedReason: '请验证身份以登录',
        options: const AuthenticationOptions(
          biometricOnly: true,
          stickyAuth: true,
        ),
      );

      if (!isAuthenticated) {
        return const AuthFailed(
          error: 'Biometric authentication failed',
          code: 'BIOMETRIC_FAILED',
        );
      }

      final result = await _channel.invokeMethod('loginWithBiometric');
      final authResult = _parseAuthResult(result);
      
      if (authResult is AuthSuccess) {
        await _saveTokens(authResult.token, authResult.refreshToken);
        await _saveUserInfo(authResult.user);
      }

      return authResult;
    } catch (e) {
      throw IDProviderException(
        'Biometric login failed',
        'BIOMETRIC_LOGIN_FAILED',
        e,
      );
    }
  }

  /// MFA验证
  Future<AuthResult> verifyMFA(
    String sessionId,
    MFAMethod method,
    String code,
  ) async {
    _ensureInitialized();

    try {
      final result = await _channel.invokeMethod('verifyMFA', {
        'sessionId': sessionId,
        'method': method.value,
        'code': code,
      });

      final authResult = _parseAuthResult(result);
      
      if (authResult is AuthSuccess) {
        await _saveTokens(authResult.token, authResult.refreshToken);
        await _saveUserInfo(authResult.user);
      }

      return authResult;
    } catch (e) {
      throw IDProviderException(
        'MFA verification failed',
        'MFA_VERIFICATION_FAILED',
        e,
      );
    }
  }

  /// 刷新访问令牌
  Future<String> refreshToken() async {
    _ensureInitialized();

    try {
      final newToken = await _channel.invokeMethod('refreshToken');
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_accessTokenKey, newToken);
      return newToken;
    } catch (e) {
      // 刷新失败，清除本地令牌
      await _clearTokens();
      throw IDProviderException(
        'Token refresh failed',
        'TOKEN_REFRESH_FAILED',
        e,
      );
    }
  }

  /// 用户登出
  Future<void> logout() async {
    _ensureInitialized();

    try {
      await _channel.invokeMethod('logout');
      await _clearLocalData();
    } catch (e) {
      // 即使服务器登出失败，也要清除本地数据
      await _clearLocalData();
      throw IDProviderException('Logout failed', 'LOGOUT_FAILED', e);
    }
  }

  /// 获取当前用户信息
  Future<UserInfo?> getCurrentUser() async {
    _ensureInitialized();

    try {
      // 先尝试从本地存储获取
      final cachedUser = await _getCachedUserInfo();
      if (cachedUser != null) {
        return cachedUser;
      }

      // 从服务器获取最新信息
      final result = await _channel.invokeMethod('getCurrentUser');
      final user = UserInfo.fromMap(Map<String, dynamic>.from(result));
      await _saveUserInfo(user);
      return user;
    } catch (e) {
      throw IDProviderException(
        'Failed to get current user',
        'GET_USER_FAILED',
        e,
      );
    }
  }

  /// 检查认证状态
  Future<bool> isAuthenticated() async {
    _ensureInitialized();

    try {
      final hasToken = await getAccessToken() != null;
      if (!hasToken) return false;

      return await _channel.invokeMethod('isAuthenticated');
    } catch (e) {
      return false;
    }
  }

  /// 获取访问令牌
  Future<String?> getAccessToken() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getString(_accessTokenKey);
    } catch (e) {
      return null;
    }
  }

  /// 启用生物识别
  Future<void> enableBiometric() async {
    _ensureInitialized();

    try {
      final availability = await checkBiometricAvailability();
      if (!availability.isAvailable) {
        throw IDProviderException(
          availability.error ?? 'Biometric authentication not available',
          'BIOMETRIC_NOT_AVAILABLE',
        );
      }

      await _channel.invokeMethod('enableBiometric');
    } catch (e) {
      throw IDProviderException(
        'Failed to enable biometric authentication',
        'ENABLE_BIOMETRIC_FAILED',
        e,
      );
    }
  }

  /// 检查生物识别可用性
  Future<BiometricAvailability> checkBiometricAvailability() async {
    try {
      final isAvailable = await _localAuth.canCheckBiometrics;
      final availableBiometrics = await _localAuth.getAvailableBiometrics();
      
      if (!isAvailable || availableBiometrics.isEmpty) {
        return const BiometricAvailability(
          isAvailable: false,
          biometryType: BiometricType.none,
          error: 'Biometric authentication not available',
        );
      }

      BiometricType biometryType = BiometricType.none;
      if (availableBiometrics.contains(BiometricType.face)) {
        biometryType = BiometricType.face;
      } else if (availableBiometrics.contains(BiometricType.fingerprint)) {
        biometryType = BiometricType.fingerprint;
      } else if (availableBiometrics.contains(BiometricType.iris)) {
        biometryType = BiometricType.iris;
      }

      return BiometricAvailability(
        isAvailable: true,
        biometryType: biometryType,
      );
    } catch (e) {
      return BiometricAvailability(
        isAvailable: false,
        biometryType: BiometricType.none,
        error: e.toString(),
      );
    }
  }

  /// 获取设备信息
  Future<DeviceInfo> getDeviceInfo() async {
    _ensureInitialized();

    try {
      // 合并原生设备信息和Flutter设备信息
      final nativeDeviceInfo = await _channel.invokeMethod('getDeviceInfo');
      final flutterDeviceInfo = await _collectFlutterDeviceInfo();

      final combinedInfo = Map<String, dynamic>.from(nativeDeviceInfo);
      combinedInfo.addAll(flutterDeviceInfo);

      return DeviceInfo.fromMap(combinedInfo);
    } catch (e) {
      throw IDProviderException(
        'Failed to get device info',
        'GET_DEVICE_INFO_FAILED',
        e,
      );
    }
  }

  /// 生成设备指纹
  Future<String> generateDeviceFingerprint() async {
    _ensureInitialized();

    try {
      return await _channel.invokeMethod('generateDeviceFingerprint');
    } catch (e) {
      throw IDProviderException(
        'Failed to generate device fingerprint',
        'GENERATE_FINGERPRINT_FAILED',
        e,
      );
    }
  }

  /// 销毁SDK实例
  void dispose() {
    _eventSubscription?.cancel();
    _eventSubscription = null;
    _isInitialized = false;
  }

  // 私有方法

  void _ensureInitialized() {
    if (!_isInitialized) {
      throw const IDProviderException(
        'SDK not initialized. Call initialize() first.',
        'SDK_NOT_INITIALIZED',
      );
    }
  }

  AuthResult _parseAuthResult(dynamic result) {
    final map = Map<String, dynamic>.from(result);
    
    if (map['success'] == true) {
      return AuthSuccess(
        token: map['token'],
        refreshToken: map['refreshToken'],
        user: UserInfo.fromMap(Map<String, dynamic>.from(map['user'])),
      );
    } else if (map['requiresMFA'] == true) {
      final methods = (map['methods'] as List)
          .map((m) => MFAMethod.values.firstWhere((method) => method.value == m))
          .toList();
      return AuthRequiresMFA(
        sessionId: map['sessionId'],
        methods: methods,
      );
    } else if (map['requiresBiometric'] == true) {
      return AuthRequiresBiometric(sessionId: map['sessionId']);
    } else if (map['blocked'] == true) {
      return AuthBlocked(reason: map['reason']);
    } else {
      return AuthFailed(
        error: map['error'] ?? 'Unknown error',
        code: map['code'],
      );
    }
  }

  Future<void> _saveTokens(String accessToken, String refreshToken) async {
    final prefs = await SharedPreferences.getInstance();
    await Future.wait([
      prefs.setString(_accessTokenKey, accessToken),
      prefs.setString(_refreshTokenKey, refreshToken),
    ]);
  }

  Future<void> _saveUserInfo(UserInfo user) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_userInfoKey, jsonEncode(user.toMap()));
  }

  Future<UserInfo?> _getCachedUserInfo() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userJson = prefs.getString(_userInfoKey);
      if (userJson != null) {
        final userMap = jsonDecode(userJson) as Map<String, dynamic>;
        return UserInfo.fromMap(userMap);
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  Future<void> _clearTokens() async {
    final prefs = await SharedPreferences.getInstance();
    await Future.wait([
      prefs.remove(_accessTokenKey),
      prefs.remove(_refreshTokenKey),
    ]);
  }

  Future<void> _clearLocalData() async {
    final prefs = await SharedPreferences.getInstance();
    await Future.wait([
      prefs.remove(_accessTokenKey),
      prefs.remove(_refreshTokenKey),
      prefs.remove(_userInfoKey),
    ]);
  }

  Future<Map<String, dynamic>> _collectFlutterDeviceInfo() async {
    try {
      final deviceInfo = <String, dynamic>{};
      
      if (Platform.isAndroid) {
        final androidInfo = await _deviceInfoPlugin.androidInfo;
        deviceInfo.addAll({
          'model': androidInfo.model,
          'systemVersion': androidInfo.version.release,
          'isSimulator': !androidInfo.isPhysicalDevice,
        });
      } else if (Platform.isIOS) {
        final iosInfo = await _deviceInfoPlugin.iosInfo;
        deviceInfo.addAll({
          'model': iosInfo.model,
          'systemVersion': iosInfo.systemVersion,
          'isSimulator': !iosInfo.isPhysicalDevice,
        });
      }

      return deviceInfo;
    } catch (e) {
      debugPrint('[IDProviderSDK] Failed to collect Flutter device info: $e');
      return {};
    }
  }

  void _handleEvent(dynamic event) {
    if (_config?.debugMode == true) {
      debugPrint('[IDProviderSDK] Received event: $event');
    }
    // 处理来自原生模块的事件
  }

  void _handleEventError(dynamic error) {
    debugPrint('[IDProviderSDK] Event error: $error');
  }
}
