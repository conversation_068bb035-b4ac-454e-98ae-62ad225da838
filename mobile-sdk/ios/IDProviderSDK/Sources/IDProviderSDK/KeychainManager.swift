/**
 * 钥匙串管理器
 * 
 * 功能说明：
 * 1. 安全存储敏感数据（令牌、密码等）
 * 2. 支持生物识别保护的存储
 * 3. 数据加密和解密
 * 4. 钥匙串项目管理
 * 5. 跨应用数据共享支持
 */

import Foundation
import Security
import LocalAuthentication

/**
 * 钥匙串错误枚举
 */
public enum KeychainError: Error, LocalizedError {
    case itemNotFound
    case duplicateItem
    case invalidData
    case authenticationRequired
    case operationFailed(OSStatus)
    case biometricNotAvailable
    case userCancel
    case unknown
    
    public var errorDescription: String? {
        switch self {
        case .itemNotFound:
            return "钥匙串项目未找到"
        case .duplicateItem:
            return "钥匙串项目已存在"
        case .invalidData:
            return "无效的数据格式"
        case .authenticationRequired:
            return "需要身份验证"
        case .operationFailed(let status):
            return "钥匙串操作失败: \(status)"
        case .biometricNotAvailable:
            return "生物识别不可用"
        case .userCancel:
            return "用户取消操作"
        case .unknown:
            return "未知错误"
        }
    }
}

/**
 * 钥匙串访问级别枚举
 */
public enum KeychainAccessLevel {
    case whenUnlocked
    case whenUnlockedThisDeviceOnly
    case afterFirstUnlock
    case afterFirstUnlockThisDeviceOnly
    case whenPasscodeSetThisDeviceOnly
    case biometricAny
    case biometricCurrentSet
    
    var secAttrAccessible: CFString {
        switch self {
        case .whenUnlocked:
            return kSecAttrAccessibleWhenUnlocked
        case .whenUnlockedThisDeviceOnly:
            return kSecAttrAccessibleWhenUnlockedThisDeviceOnly
        case .afterFirstUnlock:
            return kSecAttrAccessibleAfterFirstUnlock
        case .afterFirstUnlockThisDeviceOnly:
            return kSecAttrAccessibleAfterFirstUnlockThisDeviceOnly
        case .whenPasscodeSetThisDeviceOnly:
            return kSecAttrAccessibleWhenPasscodeSetThisDeviceOnly
        case .biometricAny:
            return kSecAttrAccessibleBiometryAny
        case .biometricCurrentSet:
            return kSecAttrAccessibleBiometryCurrentSet
        }
    }
}

/**
 * 钥匙串项目配置
 */
public struct KeychainItemConfig {
    let accessLevel: KeychainAccessLevel
    let requireBiometric: Bool
    let synchronizable: Bool
    let accessGroup: String?
    
    public init(
        accessLevel: KeychainAccessLevel = .whenUnlockedThisDeviceOnly,
        requireBiometric: Bool = false,
        synchronizable: Bool = false,
        accessGroup: String? = nil
    ) {
        self.accessLevel = accessLevel
        self.requireBiometric = requireBiometric
        self.synchronizable = synchronizable
        self.accessGroup = accessGroup
    }
    
    public static let `default` = KeychainItemConfig()
    public static let biometric = KeychainItemConfig(
        accessLevel: .biometricCurrentSet,
        requireBiometric: true
    )
    public static let secure = KeychainItemConfig(
        accessLevel: .whenPasscodeSetThisDeviceOnly
    )
}

/**
 * 钥匙串管理器类
 */
public class KeychainManager {
    
    // MARK: - 私有属性
    private let serviceIdentifier: String
    private let accessGroup: String?
    
    // MARK: - 初始化
    public init(
        serviceIdentifier: String? = nil,
        accessGroup: String? = nil
    ) {
        self.serviceIdentifier = serviceIdentifier ?? Bundle.main.bundleIdentifier ?? "IDProviderSDK"
        self.accessGroup = accessGroup
    }
    
    // MARK: - 公共方法
    
    /**
     * 存储字符串值
     */
    public func set(
        key: String,
        value: String,
        config: KeychainItemConfig = .default
    ) -> Bool {
        guard let data = value.data(using: .utf8) else { return false }
        return setData(key: key, data: data, config: config)
    }
    
    /**
     * 获取字符串值
     */
    public func get(key: String) -> String? {
        guard let data = getData(key: key) else { return nil }
        return String(data: data, encoding: .utf8)
    }
    
    /**
     * 存储数据
     */
    public func setData(
        key: String,
        data: Data,
        config: KeychainItemConfig = .default
    ) -> Bool {
        // 先删除已存在的项目
        delete(key: key)
        
        // 构建查询字典
        var query = buildBaseQuery(key: key, config: config)
        query[kSecValueData] = data
        
        // 执行添加操作
        let status = SecItemAdd(query as CFDictionary, nil)
        
        return status == errSecSuccess
    }
    
    /**
     * 获取数据
     */
    public func getData(key: String) -> Data? {
        var query = buildBaseQuery(key: key)
        query[kSecReturnData] = true
        query[kSecMatchLimit] = kSecMatchLimitOne
        
        var result: AnyObject?
        let status = SecItemCopyMatching(query as CFDictionary, &result)
        
        guard status == errSecSuccess else { return nil }
        return result as? Data
    }
    
    /**
     * 使用生物识别获取数据
     */
    public func getDataWithBiometric(
        key: String,
        prompt: String = "请验证身份以访问数据",
        completion: @escaping (Result<Data?, KeychainError>) -> Void
    ) {
        var query = buildBaseQuery(key: key)
        query[kSecReturnData] = true
        query[kSecMatchLimit] = kSecMatchLimitOne
        
        // 添加生物识别要求
        let context = LAContext()
        context.localizedFallbackTitle = "使用密码"
        query[kSecUseAuthenticationContext] = context
        query[kSecUseAuthenticationUI] = kSecUseAuthenticationUIAllow
        
        DispatchQueue.global(qos: .userInitiated).async {
            var result: AnyObject?
            let status = SecItemCopyMatching(query as CFDictionary, &result)
            
            DispatchQueue.main.async {
                switch status {
                case errSecSuccess:
                    completion(.success(result as? Data))
                case errSecItemNotFound:
                    completion(.failure(.itemNotFound))
                case errSecUserCancel:
                    completion(.failure(.userCancel))
                case errSecAuthFailed:
                    completion(.failure(.authenticationRequired))
                default:
                    completion(.failure(.operationFailed(status)))
                }
            }
        }
    }
    
    /**
     * 删除项目
     */
    public func delete(key: String) -> Bool {
        let query = buildBaseQuery(key: key)
        let status = SecItemDelete(query as CFDictionary)
        
        return status == errSecSuccess || status == errSecItemNotFound
    }
    
    /**
     * 检查项目是否存在
     */
    public func exists(key: String) -> Bool {
        var query = buildBaseQuery(key: key)
        query[kSecReturnData] = false
        query[kSecMatchLimit] = kSecMatchLimitOne
        
        let status = SecItemCopyMatching(query as CFDictionary, nil)
        return status == errSecSuccess
    }
    
    /**
     * 获取所有键
     */
    public func getAllKeys() -> [String] {
        var query: [CFString: Any] = [
            kSecClass: kSecClassGenericPassword,
            kSecAttrService: serviceIdentifier,
            kSecReturnAttributes: true,
            kSecMatchLimit: kSecMatchLimitAll
        ]
        
        if let accessGroup = accessGroup {
            query[kSecAttrAccessGroup] = accessGroup
        }
        
        var result: AnyObject?
        let status = SecItemCopyMatching(query as CFDictionary, &result)
        
        guard status == errSecSuccess,
              let items = result as? [[CFString: Any]] else {
            return []
        }
        
        return items.compactMap { item in
            item[kSecAttrAccount] as? String
        }
    }
    
    /**
     * 清除所有数据
     */
    public func clearAll() -> Bool {
        var query: [CFString: Any] = [
            kSecClass: kSecClassGenericPassword,
            kSecAttrService: serviceIdentifier
        ]
        
        if let accessGroup = accessGroup {
            query[kSecAttrAccessGroup] = accessGroup
        }
        
        let status = SecItemDelete(query as CFDictionary)
        return status == errSecSuccess || status == errSecItemNotFound
    }
    
    /**
     * 更新项目
     */
    public func update(
        key: String,
        value: String,
        config: KeychainItemConfig = .default
    ) -> Bool {
        guard let data = value.data(using: .utf8) else { return false }
        return updateData(key: key, data: data, config: config)
    }
    
    /**
     * 更新数据
     */
    public func updateData(
        key: String,
        data: Data,
        config: KeychainItemConfig = .default
    ) -> Bool {
        let query = buildBaseQuery(key: key)
        
        var attributesToUpdate: [CFString: Any] = [
            kSecValueData: data,
            kSecAttrAccessible: config.accessLevel.secAttrAccessible
        ]
        
        if config.synchronizable {
            attributesToUpdate[kSecAttrSynchronizable] = true
        }
        
        let status = SecItemUpdate(query as CFDictionary, attributesToUpdate as CFDictionary)
        
        // 如果项目不存在，则创建新项目
        if status == errSecItemNotFound {
            return setData(key: key, data: data, config: config)
        }
        
        return status == errSecSuccess
    }
    
    /**
     * 获取项目属性
     */
    public func getItemAttributes(key: String) -> [String: Any]? {
        var query = buildBaseQuery(key: key)
        query[kSecReturnAttributes] = true
        query[kSecMatchLimit] = kSecMatchLimitOne
        
        var result: AnyObject?
        let status = SecItemCopyMatching(query as CFDictionary, &result)
        
        guard status == errSecSuccess,
              let attributes = result as? [CFString: Any] else {
            return nil
        }
        
        // 转换CFString键为String键
        var stringAttributes: [String: Any] = [:]
        for (key, value) in attributes {
            stringAttributes[String(key)] = value
        }
        
        return stringAttributes
    }
    
    // MARK: - 私有方法
    
    /**
     * 构建基础查询字典
     */
    private func buildBaseQuery(
        key: String,
        config: KeychainItemConfig = .default
    ) -> [CFString: Any] {
        var query: [CFString: Any] = [
            kSecClass: kSecClassGenericPassword,
            kSecAttrService: serviceIdentifier,
            kSecAttrAccount: key
        ]
        
        // 设置访问级别
        query[kSecAttrAccessible] = config.accessLevel.secAttrAccessible
        
        // 设置同步选项
        if config.synchronizable {
            query[kSecAttrSynchronizable] = true
        }
        
        // 设置访问组
        if let accessGroup = config.accessGroup ?? self.accessGroup {
            query[kSecAttrAccessGroup] = accessGroup
        }
        
        // 设置生物识别要求
        if config.requireBiometric {
            let access = SecAccessControlCreateWithFlags(
                nil,
                config.accessLevel.secAttrAccessible,
                .biometryAny,
                nil
            )
            if let access = access {
                query[kSecAttrAccessControl] = access
            }
        }
        
        return query
    }
}

// MARK: - 便利方法扩展

extension KeychainManager {
    
    /**
     * 存储令牌
     */
    public func setToken(_ token: String, for key: String = "access_token") -> Bool {
        return set(key: key, value: token, config: .secure)
    }
    
    /**
     * 获取令牌
     */
    public func getToken(for key: String = "access_token") -> String? {
        return get(key: key)
    }
    
    /**
     * 删除令牌
     */
    public func deleteToken(for key: String = "access_token") -> Bool {
        return delete(key: key)
    }
    
    /**
     * 存储用户凭据
     */
    public func setCredentials(username: String, password: String) -> Bool {
        let success1 = set(key: "username", value: username, config: .default)
        let success2 = set(key: "password", value: password, config: .biometric)
        return success1 && success2
    }
    
    /**
     * 获取用户凭据
     */
    public func getCredentials(completion: @escaping (Result<(username: String, password: String), KeychainError>) -> Void) {
        guard let username = get(key: "username") else {
            completion(.failure(.itemNotFound))
            return
        }
        
        getDataWithBiometric(key: "password") { result in
            switch result {
            case .success(let data):
                if let data = data, let password = String(data: data, encoding: .utf8) {
                    completion(.success((username: username, password: password)))
                } else {
                    completion(.failure(.invalidData))
                }
            case .failure(let error):
                completion(.failure(error))
            }
        }
    }
    
    /**
     * 删除用户凭据
     */
    public func deleteCredentials() -> Bool {
        let success1 = delete(key: "username")
        let success2 = delete(key: "password")
        return success1 && success2
    }
}

// MARK: - 调试扩展

#if DEBUG
extension KeychainManager {
    
    /**
     * 获取钥匙串状态信息
     */
    public func getDebugInfo() -> [String: Any] {
        let allKeys = getAllKeys()
        
        var info: [String: Any] = [
            "service_identifier": serviceIdentifier,
            "access_group": accessGroup ?? "None",
            "total_items": allKeys.count,
            "keys": allKeys
        ]
        
        // 获取每个项目的属性
        var itemsInfo: [String: [String: Any]] = [:]
        for key in allKeys {
            if let attributes = getItemAttributes(key: key) {
                itemsInfo[key] = attributes
            }
        }
        info["items_attributes"] = itemsInfo
        
        return info
    }
    
    /**
     * 打印钥匙串调试信息
     */
    public func printDebugInfo() {
        let info = getDebugInfo()
        print("=== KeychainManager Debug Info ===")
        for (key, value) in info {
            if key == "items_attributes" {
                print("\(key):")
                if let items = value as? [String: [String: Any]] {
                    for (itemKey, attributes) in items {
                        print("  \(itemKey):")
                        for (attrKey, attrValue) in attributes {
                            print("    \(attrKey): \(attrValue)")
                        }
                    }
                }
            } else {
                print("\(key): \(value)")
            }
        }
        print("==================================")
    }
    
    /**
     * 测试钥匙串操作
     */
    public func runTests() -> [String: Bool] {
        var results: [String: Bool] = [:]
        
        // 测试基本存储和获取
        let testKey = "test_key"
        let testValue = "test_value"
        
        results["set_string"] = set(key: testKey, value: testValue)
        results["get_string"] = get(key: testKey) == testValue
        results["exists"] = exists(key: testKey)
        results["delete"] = delete(key: testKey)
        results["not_exists_after_delete"] = !exists(key: testKey)
        
        // 测试数据存储
        let testData = "test_data".data(using: .utf8)!
        results["set_data"] = setData(key: testKey, data: testData)
        results["get_data"] = getData(key: testKey) == testData
        
        // 清理测试数据
        _ = delete(key: testKey)
        
        return results
    }
}
#endif
