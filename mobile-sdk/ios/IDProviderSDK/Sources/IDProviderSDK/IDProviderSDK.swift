/**
 * ID Provider iOS SDK
 * 
 * 功能说明：
 * 1. 提供iOS平台的身份认证功能
 * 2. 集成生物识别认证（Face ID/Touch ID）
 * 3. 安全令牌管理和存储
 * 4. 设备指纹收集
 * 5. 零信任架构支持
 */

import Foundation
import Security
import LocalAuthentication
import UIKit
import CryptoKit

/**
 * SDK配置类
 */
public struct IDProviderConfig {
    /// 服务器基础URL
    public let baseURL: String
    
    /// 客户端ID
    public let clientId: String
    
    /// 客户端密钥
    public let clientSecret: String
    
    /// 是否启用生物识别
    public let biometricEnabled: Bool
    
    /// 是否启用设备指纹
    public let deviceFingerprintEnabled: Bool
    
    /// 调试模式
    public let debugMode: Bool
    
    public init(
        baseURL: String,
        clientId: String,
        clientSecret: String,
        biometricEnabled: Bool = true,
        deviceFingerprintEnabled: Bool = true,
        debugMode: Bool = false
    ) {
        self.baseURL = baseURL
        self.clientId = clientId
        self.clientSecret = clientSecret
        self.biometricEnabled = biometricEnabled
        self.deviceFingerprintEnabled = deviceFingerprintEnabled
        self.debugMode = debugMode
    }
}

/**
 * 认证结果枚举
 */
public enum AuthResult {
    case success(token: String, refreshToken: String)
    case requiresMFA(sessionId: String, methods: [MFAMethod])
    case requiresBiometric(sessionId: String)
    case blocked(reason: String)
    case failed(error: IDProviderError)
}

/**
 * MFA方法枚举
 */
public enum MFAMethod: String, CaseIterable {
    case sms = "sms"
    case totp = "totp"
    case push = "push"
    case biometric = "biometric"
    
    public var displayName: String {
        switch self {
        case .sms: return "短信验证"
        case .totp: return "TOTP验证"
        case .push: return "推送验证"
        case .biometric: return "生物识别"
        }
    }
}

/**
 * SDK错误类型
 */
public enum IDProviderError: Error, LocalizedError {
    case invalidConfiguration
    case networkError(Error)
    case authenticationFailed(String)
    case biometricNotAvailable
    case biometricNotEnrolled
    case biometricFailed
    case tokenExpired
    case invalidToken
    case deviceNotTrusted
    case userBlocked
    case unknownError(String)
    
    public var errorDescription: String? {
        switch self {
        case .invalidConfiguration:
            return "SDK配置无效"
        case .networkError(let error):
            return "网络错误: \(error.localizedDescription)"
        case .authenticationFailed(let reason):
            return "认证失败: \(reason)"
        case .biometricNotAvailable:
            return "生物识别不可用"
        case .biometricNotEnrolled:
            return "未设置生物识别"
        case .biometricFailed:
            return "生物识别验证失败"
        case .tokenExpired:
            return "令牌已过期"
        case .invalidToken:
            return "无效令牌"
        case .deviceNotTrusted:
            return "设备不受信任"
        case .userBlocked:
            return "用户被阻止"
        case .unknownError(let message):
            return "未知错误: \(message)"
        }
    }
}

/**
 * 用户信息结构
 */
public struct UserInfo {
    public let id: String
    public let email: String
    public let name: String
    public let roles: [String]
    public let permissions: [String]
    public let lastLoginAt: Date?
    public let createdAt: Date
}

/**
 * 设备信息结构
 */
public struct DeviceInfo {
    public let deviceId: String
    public let model: String
    public let systemVersion: String
    public let appVersion: String
    public let fingerprint: String
    public let trustLevel: Double
    public let isJailbroken: Bool
}

/**
 * ID Provider SDK主类
 */
public class IDProviderSDK {
    
    // MARK: - 单例
    public static let shared = IDProviderSDK()
    
    // MARK: - 私有属性
    private var config: IDProviderConfig?
    private let keychain = KeychainManager()
    private let biometric = BiometricManager()
    private let deviceFingerprint = DeviceFingerprintManager()
    private let networkManager = NetworkManager()
    
    // MARK: - 常量
    private struct Constants {
        static let accessTokenKey = "IDProvider_AccessToken"
        static let refreshTokenKey = "IDProvider_RefreshToken"
        static let userInfoKey = "IDProvider_UserInfo"
        static let deviceIdKey = "IDProvider_DeviceId"
    }
    
    private init() {}
    
    // MARK: - 公共方法
    
    /**
     * 初始化SDK
     */
    public func initialize(config: IDProviderConfig) {
        self.config = config
        self.networkManager.configure(baseURL: config.baseURL, debugMode: config.debugMode)
        
        if config.debugMode {
            print("[IDProviderSDK] SDK已初始化，配置: \(config)")
        }
    }
    
    /**
     * 用户名密码登录
     */
    public func login(
        username: String,
        password: String,
        completion: @escaping (AuthResult) -> Void
    ) {
        guard let config = config else {
            completion(.failed(.invalidConfiguration))
            return
        }
        
        // 收集设备信息
        let deviceInfo = collectDeviceInfo()
        
        let parameters: [String: Any] = [
            "username": username,
            "password": password,
            "client_id": config.clientId,
            "client_secret": config.clientSecret,
            "device_info": deviceInfo.toDictionary()
        ]
        
        networkManager.post(endpoint: "/auth/login", parameters: parameters) { [weak self] result in
            DispatchQueue.main.async {
                self?.handleAuthResponse(result: result, completion: completion)
            }
        }
    }
    
    /**
     * 生物识别登录
     */
    public func loginWithBiometric(completion: @escaping (AuthResult) -> Void) {
        guard let config = config, config.biometricEnabled else {
            completion(.failed(.biometricNotAvailable))
            return
        }
        
        // 检查生物识别可用性
        biometric.checkAvailability { [weak self] available, error in
            if !available {
                completion(.failed(.biometricNotAvailable))
                return
            }
            
            // 执行生物识别验证
            self?.biometric.authenticate(reason: "使用生物识别登录") { success, error in
                if success {
                    // 生物识别成功，使用存储的令牌
                    self?.loginWithStoredCredentials(completion: completion)
                } else {
                    completion(.failed(.biometricFailed))
                }
            }
        }
    }
    
    /**
     * MFA验证
     */
    public func verifyMFA(
        sessionId: String,
        method: MFAMethod,
        code: String,
        completion: @escaping (AuthResult) -> Void
    ) {
        let parameters: [String: Any] = [
            "session_id": sessionId,
            "method": method.rawValue,
            "code": code
        ]
        
        networkManager.post(endpoint: "/auth/mfa/verify", parameters: parameters) { [weak self] result in
            DispatchQueue.main.async {
                self?.handleAuthResponse(result: result, completion: completion)
            }
        }
    }
    
    /**
     * 刷新令牌
     */
    public func refreshToken(completion: @escaping (Result<String, IDProviderError>) -> Void) {
        guard let refreshToken = keychain.get(key: Constants.refreshTokenKey) else {
            completion(.failure(.tokenExpired))
            return
        }
        
        let parameters: [String: Any] = [
            "refresh_token": refreshToken,
            "client_id": config?.clientId ?? ""
        ]
        
        networkManager.post(endpoint: "/auth/refresh", parameters: parameters) { [weak self] result in
            DispatchQueue.main.async {
                switch result {
                case .success(let data):
                    if let accessToken = data["access_token"] as? String {
                        self?.keychain.set(key: Constants.accessTokenKey, value: accessToken)
                        if let newRefreshToken = data["refresh_token"] as? String {
                            self?.keychain.set(key: Constants.refreshTokenKey, value: newRefreshToken)
                        }
                        completion(.success(accessToken))
                    } else {
                        completion(.failure(.invalidToken))
                    }
                case .failure(let error):
                    completion(.failure(.networkError(error)))
                }
            }
        }
    }
    
    /**
     * 登出
     */
    public func logout(completion: @escaping (Result<Void, IDProviderError>) -> Void) {
        guard let accessToken = keychain.get(key: Constants.accessTokenKey) else {
            // 清理本地数据
            clearLocalData()
            completion(.success(()))
            return
        }
        
        let parameters: [String: Any] = [
            "token": accessToken
        ]
        
        networkManager.post(endpoint: "/auth/logout", parameters: parameters) { [weak self] result in
            DispatchQueue.main.async {
                // 无论服务器响应如何，都清理本地数据
                self?.clearLocalData()
                
                switch result {
                case .success:
                    completion(.success(()))
                case .failure(let error):
                    completion(.failure(.networkError(error)))
                }
            }
        }
    }
    
    /**
     * 获取当前用户信息
     */
    public func getCurrentUser(completion: @escaping (Result<UserInfo, IDProviderError>) -> Void) {
        guard let accessToken = keychain.get(key: Constants.accessTokenKey) else {
            completion(.failure(.tokenExpired))
            return
        }
        
        networkManager.get(
            endpoint: "/user/profile",
            headers: ["Authorization": "Bearer \(accessToken)"]
        ) { result in
            DispatchQueue.main.async {
                switch result {
                case .success(let data):
                    if let userInfo = UserInfo.from(dictionary: data) {
                        completion(.success(userInfo))
                    } else {
                        completion(.failure(.unknownError("无法解析用户信息")))
                    }
                case .failure(let error):
                    completion(.failure(.networkError(error)))
                }
            }
        }
    }
    
    /**
     * 检查认证状态
     */
    public func isAuthenticated() -> Bool {
        return keychain.get(key: Constants.accessTokenKey) != nil
    }
    
    /**
     * 获取访问令牌
     */
    public func getAccessToken() -> String? {
        return keychain.get(key: Constants.accessTokenKey)
    }
    
    /**
     * 启用生物识别
     */
    public func enableBiometric(completion: @escaping (Result<Void, IDProviderError>) -> Void) {
        biometric.checkAvailability { [weak self] available, error in
            if !available {
                completion(.failure(.biometricNotAvailable))
                return
            }
            
            self?.biometric.authenticate(reason: "启用生物识别登录") { success, error in
                if success {
                    // 生物识别验证成功，保存当前令牌用于后续生物识别登录
                    completion(.success(()))
                } else {
                    completion(.failure(.biometricFailed))
                }
            }
        }
    }
    
    /**
     * 获取设备信息
     */
    public func getDeviceInfo() -> DeviceInfo {
        return collectDeviceInfo()
    }
    
    // MARK: - 私有方法
    
    /**
     * 处理认证响应
     */
    private func handleAuthResponse(
        result: Result<[String: Any], Error>,
        completion: @escaping (AuthResult) -> Void
    ) {
        switch result {
        case .success(let data):
            if let accessToken = data["access_token"] as? String,
               let refreshToken = data["refresh_token"] as? String {
                // 认证成功
                keychain.set(key: Constants.accessTokenKey, value: accessToken)
                keychain.set(key: Constants.refreshTokenKey, value: refreshToken)
                completion(.success(token: accessToken, refreshToken: refreshToken))
                
            } else if let sessionId = data["session_id"] as? String,
                      let methodsArray = data["required_methods"] as? [String] {
                // 需要MFA
                let methods = methodsArray.compactMap { MFAMethod(rawValue: $0) }
                completion(.requiresMFA(sessionId: sessionId, methods: methods))
                
            } else if let sessionId = data["session_id"] as? String,
                      data["requires_biometric"] as? Bool == true {
                // 需要生物识别
                completion(.requiresBiometric(sessionId: sessionId))
                
            } else if let blocked = data["blocked"] as? Bool, blocked,
                      let reason = data["reason"] as? String {
                // 用户被阻止
                completion(.blocked(reason: reason))
                
            } else {
                completion(.failed(.unknownError("未知的服务器响应")))
            }
            
        case .failure(let error):
            completion(.failed(.networkError(error)))
        }
    }
    
    /**
     * 使用存储的凭据登录
     */
    private func loginWithStoredCredentials(completion: @escaping (AuthResult) -> Void) {
        guard let accessToken = keychain.get(key: Constants.accessTokenKey) else {
            completion(.failed(.tokenExpired))
            return
        }
        
        // 验证令牌有效性
        networkManager.get(
            endpoint: "/auth/validate",
            headers: ["Authorization": "Bearer \(accessToken)"]
        ) { [weak self] result in
            DispatchQueue.main.async {
                switch result {
                case .success:
                    if let refreshToken = self?.keychain.get(key: Constants.refreshTokenKey) {
                        completion(.success(token: accessToken, refreshToken: refreshToken))
                    } else {
                        completion(.failed(.tokenExpired))
                    }
                case .failure:
                    // 令牌无效，尝试刷新
                    self?.refreshToken { refreshResult in
                        switch refreshResult {
                        case .success(let newToken):
                            if let refreshToken = self?.keychain.get(key: Constants.refreshTokenKey) {
                                completion(.success(token: newToken, refreshToken: refreshToken))
                            } else {
                                completion(.failed(.tokenExpired))
                            }
                        case .failure(let error):
                            completion(.failed(error))
                        }
                    }
                }
            }
        }
    }
    
    /**
     * 收集设备信息
     */
    private func collectDeviceInfo() -> DeviceInfo {
        let device = UIDevice.current
        let deviceId = getOrCreateDeviceId()
        let fingerprint = config?.deviceFingerprintEnabled == true ? 
            deviceFingerprint.generateFingerprint() : ""
        
        return DeviceInfo(
            deviceId: deviceId,
            model: device.model,
            systemVersion: device.systemVersion,
            appVersion: Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "Unknown",
            fingerprint: fingerprint,
            trustLevel: calculateTrustLevel(),
            isJailbroken: deviceFingerprint.isJailbroken()
        )
    }
    
    /**
     * 获取或创建设备ID
     */
    private func getOrCreateDeviceId() -> String {
        if let existingId = keychain.get(key: Constants.deviceIdKey) {
            return existingId
        }
        
        let newId = UUID().uuidString
        keychain.set(key: Constants.deviceIdKey, value: newId)
        return newId
    }
    
    /**
     * 计算设备信任级别
     */
    private func calculateTrustLevel() -> Double {
        var trustLevel = 1.0
        
        // 如果设备越狱，降低信任级别
        if deviceFingerprint.isJailbroken() {
            trustLevel -= 0.5
        }
        
        // 如果生物识别不可用，降低信任级别
        if !biometric.isAvailable() {
            trustLevel -= 0.2
        }
        
        return max(0.0, trustLevel)
    }
    
    /**
     * 清理本地数据
     */
    private func clearLocalData() {
        keychain.delete(key: Constants.accessTokenKey)
        keychain.delete(key: Constants.refreshTokenKey)
        keychain.delete(key: Constants.userInfoKey)
    }
}

// MARK: - 扩展

extension DeviceInfo {
    func toDictionary() -> [String: Any] {
        return [
            "device_id": deviceId,
            "model": model,
            "system_version": systemVersion,
            "app_version": appVersion,
            "fingerprint": fingerprint,
            "trust_level": trustLevel,
            "is_jailbroken": isJailbroken
        ]
    }
}

extension UserInfo {
    static func from(dictionary: [String: Any]) -> UserInfo? {
        guard let id = dictionary["id"] as? String,
              let email = dictionary["email"] as? String,
              let name = dictionary["name"] as? String,
              let roles = dictionary["roles"] as? [String],
              let permissions = dictionary["permissions"] as? [String],
              let createdAtString = dictionary["created_at"] as? String else {
            return nil
        }
        
        let dateFormatter = ISO8601DateFormatter()
        guard let createdAt = dateFormatter.date(from: createdAtString) else {
            return nil
        }
        
        let lastLoginAt: Date?
        if let lastLoginString = dictionary["last_login_at"] as? String {
            lastLoginAt = dateFormatter.date(from: lastLoginString)
        } else {
            lastLoginAt = nil
        }
        
        return UserInfo(
            id: id,
            email: email,
            name: name,
            roles: roles,
            permissions: permissions,
            lastLoginAt: lastLoginAt,
            createdAt: createdAt
        )
    }
}
