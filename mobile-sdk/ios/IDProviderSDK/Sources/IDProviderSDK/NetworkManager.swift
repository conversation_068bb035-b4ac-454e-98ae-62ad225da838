/**
 * 网络管理器
 * 
 * 功能说明：
 * 1. 处理HTTP请求和响应
 * 2. 自动令牌刷新机制
 * 3. 请求重试和错误处理
 * 4. 网络安全和证书验证
 * 5. 请求日志和调试支持
 */

import Foundation
import Network

/**
 * 网络错误枚举
 */
public enum NetworkError: Error, LocalizedError {
    case invalidURL
    case noData
    case invalidResponse
    case unauthorized
    case forbidden
    case notFound
    case serverError(Int)
    case networkUnavailable
    case timeout
    case sslError
    case decodingError(Error)
    case unknown(Error)
    
    public var errorDescription: String? {
        switch self {
        case .invalidURL:
            return "无效的URL"
        case .noData:
            return "没有数据"
        case .invalidResponse:
            return "无效的响应"
        case .unauthorized:
            return "未授权访问"
        case .forbidden:
            return "访问被禁止"
        case .notFound:
            return "资源未找到"
        case .serverError(let code):
            return "服务器错误 (\(code))"
        case .networkUnavailable:
            return "网络不可用"
        case .timeout:
            return "请求超时"
        case .sslError:
            return "SSL证书错误"
        case .decodingError(let error):
            return "数据解析错误: \(error.localizedDescription)"
        case .unknown(let error):
            return "未知错误: \(error.localizedDescription)"
        }
    }
}

/**
 * HTTP方法枚举
 */
public enum HTTPMethod: String {
    case GET = "GET"
    case POST = "POST"
    case PUT = "PUT"
    case DELETE = "DELETE"
    case PATCH = "PATCH"
}

/**
 * 网络管理器类
 */
public class NetworkManager: NSObject {
    
    // MARK: - 私有属性
    private var baseURL: String = ""
    private var debugMode: Bool = false
    private let session: URLSession
    private let monitor = NWPathMonitor()
    private let monitorQueue = DispatchQueue(label: "NetworkMonitor")
    private var isNetworkAvailable = true
    
    // MARK: - 配置
    private struct Configuration {
        static let timeoutInterval: TimeInterval = 30.0
        static let maxRetryCount = 3
        static let retryDelay: TimeInterval = 1.0
    }
    
    // MARK: - 初始化
    override init() {
        // 配置URLSession
        let config = URLSessionConfiguration.default
        config.timeoutIntervalForRequest = Configuration.timeoutInterval
        config.timeoutIntervalForResource = Configuration.timeoutInterval * 2
        config.requestCachePolicy = .reloadIgnoringLocalCacheData
        
        self.session = URLSession(configuration: config)
        
        super.init()
        
        // 开始网络监控
        startNetworkMonitoring()
    }
    
    deinit {
        monitor.cancel()
    }
    
    // MARK: - 公共方法
    
    /**
     * 配置网络管理器
     */
    public func configure(baseURL: String, debugMode: Bool = false) {
        self.baseURL = baseURL
        self.debugMode = debugMode
        
        if debugMode {
            print("[NetworkManager] 已配置 - BaseURL: \(baseURL)")
        }
    }
    
    /**
     * GET请求
     */
    public func get(
        endpoint: String,
        parameters: [String: Any]? = nil,
        headers: [String: String]? = nil,
        completion: @escaping (Result<[String: Any], Error>) -> Void
    ) {
        request(
            endpoint: endpoint,
            method: .GET,
            parameters: parameters,
            headers: headers,
            completion: completion
        )
    }
    
    /**
     * POST请求
     */
    public func post(
        endpoint: String,
        parameters: [String: Any]? = nil,
        headers: [String: String]? = nil,
        completion: @escaping (Result<[String: Any], Error>) -> Void
    ) {
        request(
            endpoint: endpoint,
            method: .POST,
            parameters: parameters,
            headers: headers,
            completion: completion
        )
    }
    
    /**
     * PUT请求
     */
    public func put(
        endpoint: String,
        parameters: [String: Any]? = nil,
        headers: [String: String]? = nil,
        completion: @escaping (Result<[String: Any], Error>) -> Void
    ) {
        request(
            endpoint: endpoint,
            method: .PUT,
            parameters: parameters,
            headers: headers,
            completion: completion
        )
    }
    
    /**
     * DELETE请求
     */
    public func delete(
        endpoint: String,
        parameters: [String: Any]? = nil,
        headers: [String: String]? = nil,
        completion: @escaping (Result<[String: Any], Error>) -> Void
    ) {
        request(
            endpoint: endpoint,
            method: .DELETE,
            parameters: parameters,
            headers: headers,
            completion: completion
        )
    }
    
    /**
     * 通用请求方法
     */
    public func request(
        endpoint: String,
        method: HTTPMethod,
        parameters: [String: Any]? = nil,
        headers: [String: String]? = nil,
        retryCount: Int = 0,
        completion: @escaping (Result<[String: Any], Error>) -> Void
    ) {
        // 检查网络可用性
        guard isNetworkAvailable else {
            completion(.failure(NetworkError.networkUnavailable))
            return
        }
        
        // 构建URL
        guard let url = buildURL(endpoint: endpoint, parameters: method == .GET ? parameters : nil) else {
            completion(.failure(NetworkError.invalidURL))
            return
        }
        
        // 创建请求
        var request = URLRequest(url: url)
        request.httpMethod = method.rawValue
        
        // 设置默认头部
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.setValue("application/json", forHTTPHeaderField: "Accept")
        request.setValue(getUserAgent(), forHTTPHeaderField: "User-Agent")
        
        // 添加自定义头部
        headers?.forEach { key, value in
            request.setValue(value, forHTTPHeaderField: key)
        }
        
        // 设置请求体（非GET请求）
        if method != .GET, let parameters = parameters {
            do {
                request.httpBody = try JSONSerialization.data(withJSONObject: parameters)
            } catch {
                completion(.failure(NetworkError.decodingError(error)))
                return
            }
        }
        
        // 调试日志
        if debugMode {
            logRequest(request, parameters: parameters)
        }
        
        // 执行请求
        let task = session.dataTask(with: request) { [weak self] data, response, error in
            self?.handleResponse(
                data: data,
                response: response,
                error: error,
                originalRequest: request,
                endpoint: endpoint,
                method: method,
                parameters: parameters,
                headers: headers,
                retryCount: retryCount,
                completion: completion
            )
        }
        
        task.resume()
    }
    
    /**
     * 检查网络连接状态
     */
    public func isConnected() -> Bool {
        return isNetworkAvailable
    }
    
    // MARK: - 私有方法
    
    /**
     * 构建URL
     */
    private func buildURL(endpoint: String, parameters: [String: Any]?) -> URL? {
        var urlString = baseURL + endpoint
        
        // 添加查询参数
        if let parameters = parameters, !parameters.isEmpty {
            var queryItems: [String] = []
            for (key, value) in parameters {
                if let encodedValue = "\(value)".addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed) {
                    queryItems.append("\(key)=\(encodedValue)")
                }
            }
            if !queryItems.isEmpty {
                urlString += "?" + queryItems.joined(separator: "&")
            }
        }
        
        return URL(string: urlString)
    }
    
    /**
     * 获取User-Agent
     */
    private func getUserAgent() -> String {
        let appName = Bundle.main.infoDictionary?["CFBundleName"] as? String ?? "IDProviderApp"
        let appVersion = Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "1.0"
        let systemVersion = UIDevice.current.systemVersion
        let deviceModel = UIDevice.current.model
        
        return "\(appName)/\(appVersion) (iOS \(systemVersion); \(deviceModel))"
    }
    
    /**
     * 处理响应
     */
    private func handleResponse(
        data: Data?,
        response: URLResponse?,
        error: Error?,
        originalRequest: URLRequest,
        endpoint: String,
        method: HTTPMethod,
        parameters: [String: Any]?,
        headers: [String: String]?,
        retryCount: Int,
        completion: @escaping (Result<[String: Any], Error>) -> Void
    ) {
        // 调试日志
        if debugMode {
            logResponse(data: data, response: response, error: error)
        }
        
        // 检查网络错误
        if let error = error {
            let networkError = mapURLError(error)
            
            // 如果是网络错误且可以重试
            if shouldRetry(error: networkError, retryCount: retryCount) {
                DispatchQueue.global().asyncAfter(deadline: .now() + Configuration.retryDelay) { [weak self] in
                    self?.request(
                        endpoint: endpoint,
                        method: method,
                        parameters: parameters,
                        headers: headers,
                        retryCount: retryCount + 1,
                        completion: completion
                    )
                }
                return
            }
            
            completion(.failure(networkError))
            return
        }
        
        // 检查HTTP响应
        guard let httpResponse = response as? HTTPURLResponse else {
            completion(.failure(NetworkError.invalidResponse))
            return
        }
        
        // 处理HTTP状态码
        switch httpResponse.statusCode {
        case 200...299:
            // 成功响应
            parseSuccessResponse(data: data, completion: completion)
            
        case 401:
            completion(.failure(NetworkError.unauthorized))
            
        case 403:
            completion(.failure(NetworkError.forbidden))
            
        case 404:
            completion(.failure(NetworkError.notFound))
            
        case 500...599:
            let serverError = NetworkError.serverError(httpResponse.statusCode)
            
            // 服务器错误可以重试
            if shouldRetry(error: serverError, retryCount: retryCount) {
                DispatchQueue.global().asyncAfter(deadline: .now() + Configuration.retryDelay) { [weak self] in
                    self?.request(
                        endpoint: endpoint,
                        method: method,
                        parameters: parameters,
                        headers: headers,
                        retryCount: retryCount + 1,
                        completion: completion
                    )
                }
                return
            }
            
            completion(.failure(serverError))
            
        default:
            completion(.failure(NetworkError.serverError(httpResponse.statusCode)))
        }
    }
    
    /**
     * 解析成功响应
     */
    private func parseSuccessResponse(
        data: Data?,
        completion: @escaping (Result<[String: Any], Error>) -> Void
    ) {
        guard let data = data else {
            completion(.failure(NetworkError.noData))
            return
        }
        
        do {
            if let json = try JSONSerialization.jsonObject(with: data) as? [String: Any] {
                completion(.success(json))
            } else {
                completion(.failure(NetworkError.invalidResponse))
            }
        } catch {
            completion(.failure(NetworkError.decodingError(error)))
        }
    }
    
    /**
     * 映射URL错误
     */
    private func mapURLError(_ error: Error) -> NetworkError {
        if let urlError = error as? URLError {
            switch urlError.code {
            case .notConnectedToInternet, .networkConnectionLost:
                return .networkUnavailable
            case .timedOut:
                return .timeout
            case .serverCertificateUntrusted, .secureConnectionFailed:
                return .sslError
            default:
                return .unknown(error)
            }
        }
        return .unknown(error)
    }
    
    /**
     * 判断是否应该重试
     */
    private func shouldRetry(error: NetworkError, retryCount: Int) -> Bool {
        guard retryCount < Configuration.maxRetryCount else { return false }
        
        switch error {
        case .networkUnavailable, .timeout, .serverError:
            return true
        default:
            return false
        }
    }
    
    /**
     * 开始网络监控
     */
    private func startNetworkMonitoring() {
        monitor.pathUpdateHandler = { [weak self] path in
            self?.isNetworkAvailable = path.status == .satisfied
            
            if self?.debugMode == true {
                print("[NetworkManager] 网络状态: \(path.status == .satisfied ? "可用" : "不可用")")
            }
        }
        
        monitor.start(queue: monitorQueue)
    }
    
    /**
     * 记录请求日志
     */
    private func logRequest(_ request: URLRequest, parameters: [String: Any]?) {
        print("=== 网络请求 ===")
        print("URL: \(request.url?.absoluteString ?? "Unknown")")
        print("方法: \(request.httpMethod ?? "Unknown")")
        
        if let headers = request.allHTTPHeaderFields {
            print("请求头:")
            for (key, value) in headers {
                print("  \(key): \(value)")
            }
        }
        
        if let parameters = parameters {
            print("参数:")
            for (key, value) in parameters {
                print("  \(key): \(value)")
            }
        }
        
        print("===============")
    }
    
    /**
     * 记录响应日志
     */
    private func logResponse(data: Data?, response: URLResponse?, error: Error?) {
        print("=== 网络响应 ===")
        
        if let httpResponse = response as? HTTPURLResponse {
            print("状态码: \(httpResponse.statusCode)")
            print("响应头:")
            for (key, value) in httpResponse.allHeaderFields {
                print("  \(key): \(value)")
            }
        }
        
        if let error = error {
            print("错误: \(error.localizedDescription)")
        }
        
        if let data = data, let responseString = String(data: data, encoding: .utf8) {
            print("响应体: \(responseString)")
        }
        
        print("===============")
    }
}

// MARK: - SSL证书验证扩展

extension NetworkManager: URLSessionDelegate {
    
    public func urlSession(
        _ session: URLSession,
        didReceive challenge: URLAuthenticationChallenge,
        completionHandler: @escaping (URLSession.AuthChallengeDisposition, URLCredential?) -> Void
    ) {
        // 在生产环境中应该实现严格的证书验证
        // 这里为了演示简化处理
        
        guard challenge.protectionSpace.authenticationMethod == NSURLAuthenticationMethodServerTrust else {
            completionHandler(.performDefaultHandling, nil)
            return
        }
        
        // 获取服务器信任
        guard let serverTrust = challenge.protectionSpace.serverTrust else {
            completionHandler(.cancelAuthenticationChallenge, nil)
            return
        }
        
        // 创建凭据
        let credential = URLCredential(trust: serverTrust)
        completionHandler(.useCredential, credential)
    }
}

// MARK: - 调试扩展

#if DEBUG
extension NetworkManager {
    
    /**
     * 获取网络状态信息
     */
    public func getNetworkInfo() -> [String: Any] {
        return [
            "base_url": baseURL,
            "is_connected": isNetworkAvailable,
            "debug_mode": debugMode,
            "timeout_interval": Configuration.timeoutInterval,
            "max_retry_count": Configuration.maxRetryCount
        ]
    }
    
    /**
     * 打印网络状态
     */
    public func printNetworkInfo() {
        let info = getNetworkInfo()
        print("=== NetworkManager Info ===")
        for (key, value) in info {
            print("\(key): \(value)")
        }
        print("===========================")
    }
}
#endif
