/**
 * 设备指纹管理器
 * 
 * 功能说明：
 * 1. 生成唯一的设备指纹
 * 2. 检测设备安全状态
 * 3. 收集设备硬件信息
 * 4. 检测越狱/Root状态
 * 5. 提供设备信任度评估
 */

import Foundation
import UIKit
import Security
import SystemConfiguration
import AdSupport
import AppTrackingTransparency

/**
 * 设备指纹组件结构
 */
public struct DeviceFingerprintComponents {
    public let deviceModel: String
    public let systemVersion: String
    public let screenResolution: String
    public let timezone: String
    public let locale: String
    public let carrierInfo: String
    public let networkType: String
    public let batteryLevel: Float
    public let diskSpace: String
    public let memorySize: String
    public let cpuType: String
    public let isSimulator: Bool
    public let isJailbroken: Bool
    public let installedApps: [String]
    public let systemUptime: TimeInterval
}

/**
 * 设备指纹管理器类
 */
public class DeviceFingerprintManager {
    
    // MARK: - 私有属性
    private let keychain = KeychainManager()
    
    // MARK: - 常量
    private struct Constants {
        static let fingerprintKey = "DeviceFingerprint"
        static let componentsKey = "FingerprintComponents"
    }
    
    // MARK: - 公共方法
    
    /**
     * 生成设备指纹
     */
    public func generateFingerprint() -> String {
        let components = collectDeviceComponents()
        let fingerprintData = createFingerprintData(from: components)
        let fingerprint = hashFingerprint(data: fingerprintData)
        
        // 缓存指纹和组件信息
        cacheFingerprint(fingerprint, components: components)
        
        return fingerprint
    }
    
    /**
     * 获取缓存的设备指纹
     */
    public func getCachedFingerprint() -> String? {
        return keychain.get(key: Constants.fingerprintKey)
    }
    
    /**
     * 获取设备组件信息
     */
    public func getDeviceComponents() -> DeviceFingerprintComponents {
        return collectDeviceComponents()
    }
    
    /**
     * 检测设备是否越狱
     */
    public func isJailbroken() -> Bool {
        return checkJailbreakStatus()
    }
    
    /**
     * 检测是否为模拟器
     */
    public func isSimulator() -> Bool {
        #if targetEnvironment(simulator)
        return true
        #else
        return false
        #endif
    }
    
    /**
     * 获取设备信任度评分
     */
    public func getTrustScore() -> Double {
        let components = collectDeviceComponents()
        return calculateTrustScore(components: components)
    }
    
    /**
     * 检查指纹是否发生变化
     */
    public func hasFingerprintChanged() -> Bool {
        guard let cachedFingerprint = getCachedFingerprint() else {
            return true // 没有缓存的指纹，认为是变化
        }
        
        let currentFingerprint = generateFingerprint()
        return cachedFingerprint != currentFingerprint
    }
    
    /**
     * 获取设备唯一标识符
     */
    public func getDeviceIdentifier() -> String {
        // 优先使用IDFV
        if let idfv = UIDevice.current.identifierForVendor?.uuidString {
            return idfv
        }
        
        // 如果IDFV不可用，生成并存储一个UUID
        if let storedId = keychain.get(key: "DeviceUUID") {
            return storedId
        }
        
        let newId = UUID().uuidString
        keychain.set(key: "DeviceUUID", value: newId)
        return newId
    }
    
    // MARK: - 私有方法
    
    /**
     * 收集设备组件信息
     */
    private func collectDeviceComponents() -> DeviceFingerprintComponents {
        let device = UIDevice.current
        let screen = UIScreen.main
        let processInfo = ProcessInfo.processInfo
        
        return DeviceFingerprintComponents(
            deviceModel: getDeviceModel(),
            systemVersion: device.systemVersion,
            screenResolution: "\(Int(screen.bounds.width))x\(Int(screen.bounds.height))",
            timezone: TimeZone.current.identifier,
            locale: Locale.current.identifier,
            carrierInfo: getCarrierInfo(),
            networkType: getNetworkType(),
            batteryLevel: device.batteryLevel,
            diskSpace: getDiskSpace(),
            memorySize: getMemorySize(),
            cpuType: getCPUType(),
            isSimulator: isSimulator(),
            isJailbroken: isJailbroken(),
            installedApps: getInstalledApps(),
            systemUptime: processInfo.systemUptime
        )
    }
    
    /**
     * 获取设备型号
     */
    private func getDeviceModel() -> String {
        var systemInfo = utsname()
        uname(&systemInfo)
        let machineMirror = Mirror(reflecting: systemInfo.machine)
        let identifier = machineMirror.children.reduce("") { identifier, element in
            guard let value = element.value as? Int8, value != 0 else { return identifier }
            return identifier + String(UnicodeScalar(UInt8(value))!)
        }
        return identifier
    }
    
    /**
     * 获取运营商信息
     */
    private func getCarrierInfo() -> String {
        // 注意：在iOS 16+中，CTCarrier已被弃用
        return "Unknown"
    }
    
    /**
     * 获取网络类型
     */
    private func getNetworkType() -> String {
        var zeroAddress = sockaddr_in()
        zeroAddress.sin_len = UInt8(MemoryLayout<sockaddr_in>.size)
        zeroAddress.sin_family = sa_family_t(AF_INET)
        
        guard let defaultRouteReachability = withUnsafePointer(to: &zeroAddress, {
            $0.withMemoryRebound(to: sockaddr.self, capacity: 1) {
                SCNetworkReachabilityCreateWithAddress(nil, $0)
            }
        }) else {
            return "Unknown"
        }
        
        var flags: SCNetworkReachabilityFlags = []
        if !SCNetworkReachabilityGetFlags(defaultRouteReachability, &flags) {
            return "Unknown"
        }
        
        if flags.contains(.reachable) && !flags.contains(.connectionRequired) {
            if flags.contains(.isWWAN) {
                return "Cellular"
            } else {
                return "WiFi"
            }
        }
        
        return "None"
    }
    
    /**
     * 获取磁盘空间信息
     */
    private func getDiskSpace() -> String {
        do {
            let systemAttributes = try FileManager.default.attributesOfFileSystem(forPath: NSHomeDirectory())
            if let totalSpace = systemAttributes[.systemSize] as? NSNumber {
                let totalSpaceGB = totalSpace.int64Value / (1024 * 1024 * 1024)
                return "\(totalSpaceGB)GB"
            }
        } catch {
            // 忽略错误
        }
        return "Unknown"
    }
    
    /**
     * 获取内存大小
     */
    private func getMemorySize() -> String {
        let physicalMemory = ProcessInfo.processInfo.physicalMemory
        let memoryGB = physicalMemory / (1024 * 1024 * 1024)
        return "\(memoryGB)GB"
    }
    
    /**
     * 获取CPU类型
     */
    private func getCPUType() -> String {
        var systemInfo = utsname()
        uname(&systemInfo)
        let machineMirror = Mirror(reflecting: systemInfo.machine)
        let identifier = machineMirror.children.reduce("") { identifier, element in
            guard let value = element.value as? Int8, value != 0 else { return identifier }
            return identifier + String(UnicodeScalar(UInt8(value))!)
        }
        
        // 简化的CPU类型识别
        if identifier.contains("arm64") {
            return "ARM64"
        } else if identifier.contains("x86_64") {
            return "x86_64"
        } else {
            return identifier
        }
    }
    
    /**
     * 获取已安装应用列表（仅限公开可获取的信息）
     */
    private func getInstalledApps() -> [String] {
        // 由于iOS的沙盒限制，无法获取完整的已安装应用列表
        // 这里返回一些可以检测的系统应用URL Scheme
        let schemes = [
            "tel://", "sms://", "mailto://", "http://", "https://",
            "facetime://", "maps://", "music://", "videos://", "photos://"
        ]
        
        var availableSchemes: [String] = []
        for scheme in schemes {
            if let url = URL(string: scheme), UIApplication.shared.canOpenURL(url) {
                availableSchemes.append(scheme)
            }
        }
        
        return availableSchemes
    }
    
    /**
     * 检测越狱状态
     */
    private func checkJailbreakStatus() -> Bool {
        // 检查常见的越狱文件路径
        let jailbreakPaths = [
            "/Applications/Cydia.app",
            "/Library/MobileSubstrate/MobileSubstrate.dylib",
            "/bin/bash",
            "/usr/sbin/sshd",
            "/etc/apt",
            "/private/var/lib/apt/",
            "/private/var/lib/cydia",
            "/private/var/mobile/Library/SBSettings/Themes",
            "/Library/MobileSubstrate/DynamicLibraries/LiveClock.plist",
            "/System/Library/LaunchDaemons/com.ikey.bbot.plist",
            "/private/var/cache/apt/",
            "/private/var/lib/apt",
            "/private/var/Users/",
            "/var/cache/apt",
            "/var/lib/apt",
            "/var/lib/cydia",
            "/usr/bin/sshd",
            "/usr/libexec/sftp-server",
            "/usr/libexec/ssh-keysign",
            "/var/cache/apt",
            "/var/lib/apt",
            "/usr/sbin/frida-server",
            "/usr/bin/cycript",
            "/usr/local/bin/cycript",
            "/usr/lib/libcycript.dylib"
        ]
        
        for path in jailbreakPaths {
            if FileManager.default.fileExists(atPath: path) {
                return true
            }
        }
        
        // 检查是否可以写入系统目录
        let testPath = "/private/test_jailbreak.txt"
        do {
            try "test".write(toFile: testPath, atomically: true, encoding: .utf8)
            try FileManager.default.removeItem(atPath: testPath)
            return true // 如果能写入系统目录，说明可能越狱了
        } catch {
            // 无法写入，正常情况
        }
        
        // 检查环境变量
        if let dyldInsertLibraries = getenv("DYLD_INSERT_LIBRARIES") {
            let libraries = String(cString: dyldInsertLibraries)
            if !libraries.isEmpty {
                return true
            }
        }
        
        // 检查是否可以fork进程
        let pid = fork()
        if pid >= 0 {
            if pid > 0 {
                // 父进程，等待子进程结束
                waitpid(pid, nil, 0)
            }
            return true // 如果fork成功，可能是越狱设备
        }
        
        return false
    }
    
    /**
     * 创建指纹数据
     */
    private func createFingerprintData(from components: DeviceFingerprintComponents) -> String {
        let fingerprintString = [
            components.deviceModel,
            components.systemVersion,
            components.screenResolution,
            components.timezone,
            components.locale,
            components.diskSpace,
            components.memorySize,
            components.cpuType,
            String(components.isSimulator),
            String(components.isJailbroken),
            components.installedApps.joined(separator: ",")
        ].joined(separator: "|")
        
        return fingerprintString
    }
    
    /**
     * 对指纹数据进行哈希
     */
    private func hashFingerprint(data: String) -> String {
        guard let data = data.data(using: .utf8) else {
            return UUID().uuidString
        }
        
        let hash = SHA256.hash(data: data)
        return hash.compactMap { String(format: "%02x", $0) }.joined()
    }
    
    /**
     * 缓存指纹信息
     */
    private func cacheFingerprint(_ fingerprint: String, components: DeviceFingerprintComponents) {
        keychain.set(key: Constants.fingerprintKey, value: fingerprint)
        
        // 将组件信息序列化并缓存
        if let componentsData = try? JSONEncoder().encode(components) {
            keychain.set(key: Constants.componentsKey, value: String(data: componentsData, encoding: .utf8) ?? "")
        }
    }
    
    /**
     * 计算设备信任度评分
     */
    private func calculateTrustScore(components: DeviceFingerprintComponents) -> Double {
        var score = 1.0
        
        // 越狱设备降低信任度
        if components.isJailbroken {
            score -= 0.5
        }
        
        // 模拟器降低信任度
        if components.isSimulator {
            score -= 0.3
        }
        
        // 系统版本过旧降低信任度
        if let systemVersion = Float(components.systemVersion.components(separatedBy: ".").first ?? "0") {
            if systemVersion < 14.0 { // iOS 14以下
                score -= 0.2
            }
        }
        
        // 电池电量过低可能影响信任度
        if components.batteryLevel > 0 && components.batteryLevel < 0.1 {
            score -= 0.1
        }
        
        return max(0.0, min(1.0, score))
    }
}

// MARK: - 扩展

extension DeviceFingerprintComponents: Codable {
    // 使Codable协议可用于序列化
}

// MARK: - 调试扩展

#if DEBUG
extension DeviceFingerprintManager {
    
    /**
     * 获取详细的调试信息
     */
    public func getDebugInfo() -> [String: Any] {
        let components = getDeviceComponents()
        
        return [
            "device_model": components.deviceModel,
            "system_version": components.systemVersion,
            "screen_resolution": components.screenResolution,
            "timezone": components.timezone,
            "locale": components.locale,
            "carrier_info": components.carrierInfo,
            "network_type": components.networkType,
            "battery_level": components.batteryLevel,
            "disk_space": components.diskSpace,
            "memory_size": components.memorySize,
            "cpu_type": components.cpuType,
            "is_simulator": components.isSimulator,
            "is_jailbroken": components.isJailbroken,
            "installed_apps_count": components.installedApps.count,
            "system_uptime": components.systemUptime,
            "trust_score": getTrustScore(),
            "fingerprint": generateFingerprint()
        ]
    }
    
    /**
     * 打印调试信息
     */
    public func printDebugInfo() {
        let info = getDebugInfo()
        print("=== DeviceFingerprintManager Debug Info ===")
        for (key, value) in info.sorted(by: { $0.key < $1.key }) {
            print("\(key): \(value)")
        }
        print("==========================================")
    }
}
#endif
