/**
 * 生物识别管理器
 * 
 * 功能说明：
 * 1. 管理Face ID和Touch ID认证
 * 2. 检查生物识别可用性
 * 3. 处理生物识别错误
 * 4. 提供生物识别状态查询
 * 5. 支持自定义认证提示
 */

import Foundation
import LocalAuthentication
import Security

/**
 * 生物识别类型枚举
 */
public enum BiometricType {
    case none
    case touchID
    case faceID
    case opticID
    
    public var displayName: String {
        switch self {
        case .none:
            return "无"
        case .touchID:
            return "Touch ID"
        case .faceID:
            return "Face ID"
        case .opticID:
            return "Optic ID"
        }
    }
}

/**
 * 生物识别错误枚举
 */
public enum BiometricError: Error, LocalizedError {
    case notAvailable
    case notEnrolled
    case lockout
    case userCancel
    case userFallback
    case systemCancel
    case passcodeNotSet
    case biometryNotAvailable
    case biometryNotEnrolled
    case biometryLockout
    case invalidContext
    case notInteractive
    case unknown(Error)
    
    public var errorDescription: String? {
        switch self {
        case .notAvailable:
            return "生物识别不可用"
        case .notEnrolled:
            return "未设置生物识别"
        case .lockout:
            return "生物识别被锁定"
        case .userCancel:
            return "用户取消"
        case .userFallback:
            return "用户选择备用方式"
        case .systemCancel:
            return "系统取消"
        case .passcodeNotSet:
            return "未设置设备密码"
        case .biometryNotAvailable:
            return "生物识别硬件不可用"
        case .biometryNotEnrolled:
            return "未注册生物识别"
        case .biometryLockout:
            return "生物识别被锁定，请使用密码"
        case .invalidContext:
            return "无效的认证上下文"
        case .notInteractive:
            return "非交互式环境"
        case .unknown(let error):
            return "未知错误: \(error.localizedDescription)"
        }
    }
}

/**
 * 生物识别管理器类
 */
public class BiometricManager {
    
    // MARK: - 私有属性
    private let context = LAContext()
    
    // MARK: - 公共方法
    
    /**
     * 检查生物识别可用性
     */
    public func checkAvailability(completion: @escaping (Bool, BiometricError?) -> Void) {
        var error: NSError?
        let policy = LAPolicy.deviceOwnerAuthenticationWithBiometrics
        
        DispatchQueue.global(qos: .userInitiated).async { [weak self] in
            let isAvailable = self?.context.canEvaluatePolicy(policy, error: &error) ?? false
            
            DispatchQueue.main.async {
                if isAvailable {
                    completion(true, nil)
                } else {
                    let biometricError = self?.mapLAError(error) ?? .notAvailable
                    completion(false, biometricError)
                }
            }
        }
    }
    
    /**
     * 同步检查生物识别是否可用
     */
    public func isAvailable() -> Bool {
        var error: NSError?
        return context.canEvaluatePolicy(.deviceOwnerAuthenticationWithBiometrics, error: &error)
    }
    
    /**
     * 获取生物识别类型
     */
    public func getBiometricType() -> BiometricType {
        guard isAvailable() else { return .none }
        
        switch context.biometryType {
        case .none:
            return .none
        case .touchID:
            return .touchID
        case .faceID:
            return .faceID
        case .opticID:
            if #available(iOS 17.0, *) {
                return .opticID
            } else {
                return .none
            }
        @unknown default:
            return .none
        }
    }
    
    /**
     * 执行生物识别认证
     */
    public func authenticate(
        reason: String,
        fallbackTitle: String? = nil,
        cancelTitle: String? = nil,
        completion: @escaping (Bool, BiometricError?) -> Void
    ) {
        // 检查可用性
        checkAvailability { [weak self] available, error in
            guard available else {
                completion(false, error)
                return
            }
            
            self?.performAuthentication(
                reason: reason,
                fallbackTitle: fallbackTitle,
                cancelTitle: cancelTitle,
                completion: completion
            )
        }
    }
    
    /**
     * 使用密码回退的生物识别认证
     */
    public func authenticateWithPasscodeFallback(
        reason: String,
        completion: @escaping (Bool, BiometricError?) -> Void
    ) {
        let context = LAContext()
        let policy = LAPolicy.deviceOwnerAuthentication // 允许密码回退
        
        context.localizedFallbackTitle = "使用密码"
        context.localizedCancelTitle = "取消"
        
        context.evaluatePolicy(policy, localizedReason: reason) { [weak self] success, error in
            DispatchQueue.main.async {
                if success {
                    completion(true, nil)
                } else {
                    let biometricError = self?.mapLAError(error as NSError?) ?? .unknown(error!)
                    completion(false, biometricError)
                }
            }
        }
    }
    
    /**
     * 检查是否已注册生物识别
     */
    public func isEnrolled() -> Bool {
        var error: NSError?
        let canEvaluate = context.canEvaluatePolicy(.deviceOwnerAuthenticationWithBiometrics, error: &error)
        
        if let error = error {
            switch error.code {
            case LAError.biometryNotEnrolled.rawValue:
                return false
            default:
                return canEvaluate
            }
        }
        
        return canEvaluate
    }
    
    /**
     * 获取生物识别状态描述
     */
    public func getStatusDescription() -> String {
        var error: NSError?
        let canEvaluate = context.canEvaluatePolicy(.deviceOwnerAuthenticationWithBiometrics, error: &error)
        
        if canEvaluate {
            return "\(getBiometricType().displayName)可用"
        } else if let error = error {
            switch error.code {
            case LAError.biometryNotAvailable.rawValue:
                return "设备不支持生物识别"
            case LAError.biometryNotEnrolled.rawValue:
                return "未设置\(getBiometricType().displayName)"
            case LAError.biometryLockout.rawValue:
                return "\(getBiometricType().displayName)被锁定"
            case LAError.passcodeNotSet.rawValue:
                return "未设置设备密码"
            default:
                return "生物识别不可用"
            }
        } else {
            return "生物识别状态未知"
        }
    }
    
    /**
     * 重置认证上下文
     */
    public func resetContext() {
        // 创建新的上下文以重置状态
        let newContext = LAContext()
        // 这里可以复制一些配置到新上下文
    }
    
    /**
     * 检查生物识别是否被锁定
     */
    public func isLocked() -> Bool {
        var error: NSError?
        context.canEvaluatePolicy(.deviceOwnerAuthenticationWithBiometrics, error: &error)
        
        if let error = error {
            return error.code == LAError.biometryLockout.rawValue
        }
        
        return false
    }
    
    /**
     * 获取生物识别变更状态
     */
    public func getBiometricChanges() -> Data? {
        var error: NSError?
        context.canEvaluatePolicy(.deviceOwnerAuthenticationWithBiometrics, error: &error)
        
        if error == nil {
            return context.evaluatedPolicyDomainState
        }
        
        return nil
    }
    
    /**
     * 比较生物识别状态是否发生变化
     */
    public func hasBiometricChanged(previousState: Data?) -> Bool {
        guard let previousState = previousState else { return true }
        
        let currentState = getBiometricChanges()
        return currentState != previousState
    }
    
    // MARK: - 私有方法
    
    /**
     * 执行生物识别认证
     */
    private func performAuthentication(
        reason: String,
        fallbackTitle: String?,
        cancelTitle: String?,
        completion: @escaping (Bool, BiometricError?) -> Void
    ) {
        let context = LAContext()
        
        // 配置上下文
        if let fallbackTitle = fallbackTitle {
            context.localizedFallbackTitle = fallbackTitle
        }
        
        if let cancelTitle = cancelTitle {
            context.localizedCancelTitle = cancelTitle
        }
        
        // 设置触觉反馈
        context.touchIDAuthenticationAllowableReuseDuration = 10 // 10秒内可重用
        
        let policy = LAPolicy.deviceOwnerAuthenticationWithBiometrics
        
        context.evaluatePolicy(policy, localizedReason: reason) { [weak self] success, error in
            DispatchQueue.main.async {
                if success {
                    completion(true, nil)
                } else {
                    let biometricError = self?.mapLAError(error as NSError?) ?? .unknown(error!)
                    completion(false, biometricError)
                }
            }
        }
    }
    
    /**
     * 映射LocalAuthentication错误到自定义错误
     */
    private func mapLAError(_ error: NSError?) -> BiometricError {
        guard let error = error else { return .unknown(NSError()) }
        
        switch error.code {
        case LAError.authenticationFailed.rawValue:
            return .unknown(error)
        case LAError.userCancel.rawValue:
            return .userCancel
        case LAError.userFallback.rawValue:
            return .userFallback
        case LAError.systemCancel.rawValue:
            return .systemCancel
        case LAError.passcodeNotSet.rawValue:
            return .passcodeNotSet
        case LAError.biometryNotAvailable.rawValue:
            return .biometryNotAvailable
        case LAError.biometryNotEnrolled.rawValue:
            return .biometryNotEnrolled
        case LAError.biometryLockout.rawValue:
            return .biometryLockout
        case LAError.invalidContext.rawValue:
            return .invalidContext
        case LAError.notInteractive.rawValue:
            return .notInteractive
        default:
            return .unknown(error)
        }
    }
}

// MARK: - 扩展

extension BiometricManager {
    
    /**
     * 创建生物识别提示配置
     */
    public struct AuthenticationPrompt {
        public let reason: String
        public let fallbackTitle: String?
        public let cancelTitle: String?
        
        public init(reason: String, fallbackTitle: String? = nil, cancelTitle: String? = nil) {
            self.reason = reason
            self.fallbackTitle = fallbackTitle
            self.cancelTitle = cancelTitle
        }
        
        public static let `default` = AuthenticationPrompt(
            reason: "请验证您的身份",
            fallbackTitle: "使用密码",
            cancelTitle: "取消"
        )
        
        public static let login = AuthenticationPrompt(
            reason: "使用生物识别登录",
            fallbackTitle: "使用密码登录",
            cancelTitle: "取消"
        )
        
        public static let payment = AuthenticationPrompt(
            reason: "验证身份以完成支付",
            fallbackTitle: "使用密码",
            cancelTitle: "取消支付"
        )
    }
    
    /**
     * 使用预定义提示进行认证
     */
    public func authenticate(
        with prompt: AuthenticationPrompt,
        completion: @escaping (Bool, BiometricError?) -> Void
    ) {
        authenticate(
            reason: prompt.reason,
            fallbackTitle: prompt.fallbackTitle,
            cancelTitle: prompt.cancelTitle,
            completion: completion
        )
    }
}

// MARK: - 调试扩展

#if DEBUG
extension BiometricManager {
    
    /**
     * 获取详细的调试信息
     */
    public func getDebugInfo() -> [String: Any] {
        var info: [String: Any] = [:]
        
        info["biometric_type"] = getBiometricType().displayName
        info["is_available"] = isAvailable()
        info["is_enrolled"] = isEnrolled()
        info["is_locked"] = isLocked()
        info["status_description"] = getStatusDescription()
        
        var error: NSError?
        context.canEvaluatePolicy(.deviceOwnerAuthenticationWithBiometrics, error: &error)
        
        if let error = error {
            info["last_error_code"] = error.code
            info["last_error_description"] = error.localizedDescription
        }
        
        if let domainState = context.evaluatedPolicyDomainState {
            info["domain_state"] = domainState.base64EncodedString()
        }
        
        return info
    }
    
    /**
     * 打印调试信息
     */
    public func printDebugInfo() {
        let info = getDebugInfo()
        print("=== BiometricManager Debug Info ===")
        for (key, value) in info {
            print("\(key): \(value)")
        }
        print("===================================")
    }
}
#endif
