/**
 * ID Provider Android SDK
 * 
 * 功能说明：
 * 1. 提供Android平台的身份认证功能
 * 2. 集成生物识别认证（指纹/面部识别）
 * 3. 安全令牌管理和存储
 * 4. 设备指纹收集
 * 5. 零信任架构支持
 */

package com.idprovider.sdk

import android.content.Context
import android.content.SharedPreferences
import android.security.keystore.KeyGenParameterSpec
import android.security.keystore.KeyProperties
import androidx.biometric.BiometricManager
import androidx.biometric.BiometricPrompt
import androidx.core.content.ContextCompat
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.LifecycleOwner
import kotlinx.coroutines.*
import org.json.JSONObject
import java.security.KeyStore
import java.util.*
import javax.crypto.Cipher
import javax.crypto.KeyGenerator
import javax.crypto.SecretKey

/**
 * SDK配置数据类
 */
data class IDProviderConfig(
    val baseURL: String,
    val clientId: String,
    val clientSecret: String,
    val biometricEnabled: Boolean = true,
    val deviceFingerprintEnabled: Boolean = true,
    val debugMode: Boolean = false
)

/**
 * 认证结果密封类
 */
sealed class AuthResult {
    data class Success(val token: String, val refreshToken: String) : AuthResult()
    data class RequiresMFA(val sessionId: String, val methods: List<MFAMethod>) : AuthResult()
    data class RequiresBiometric(val sessionId: String) : AuthResult()
    data class Blocked(val reason: String) : AuthResult()
    data class Failed(val error: IDProviderError) : AuthResult()
}

/**
 * MFA方法枚举
 */
enum class MFAMethod(val value: String, val displayName: String) {
    SMS("sms", "短信验证"),
    TOTP("totp", "TOTP验证"),
    PUSH("push", "推送验证"),
    BIOMETRIC("biometric", "生物识别")
}

/**
 * SDK错误类型
 */
sealed class IDProviderError(message: String) : Exception(message) {
    object InvalidConfiguration : IDProviderError("SDK配置无效")
    data class NetworkError(val cause: Throwable) : IDProviderError("网络错误: ${cause.message}")
    data class AuthenticationFailed(val reason: String) : IDProviderError("认证失败: $reason")
    object BiometricNotAvailable : IDProviderError("生物识别不可用")
    object BiometricNotEnrolled : IDProviderError("未设置生物识别")
    object BiometricFailed : IDProviderError("生物识别验证失败")
    object TokenExpired : IDProviderError("令牌已过期")
    object InvalidToken : IDProviderError("无效令牌")
    object DeviceNotTrusted : IDProviderError("设备不受信任")
    object UserBlocked : IDProviderError("用户被阻止")
    data class UnknownError(val message: String) : IDProviderError("未知错误: $message")
}

/**
 * 用户信息数据类
 */
data class UserInfo(
    val id: String,
    val email: String,
    val name: String,
    val roles: List<String>,
    val permissions: List<String>,
    val lastLoginAt: Date?,
    val createdAt: Date
)

/**
 * 设备信息数据类
 */
data class DeviceInfo(
    val deviceId: String,
    val model: String,
    val systemVersion: String,
    val appVersion: String,
    val fingerprint: String,
    val trustLevel: Double,
    val isRooted: Boolean
)

/**
 * ID Provider SDK主类
 */
class IDProviderSDK private constructor() {
    
    companion object {
        @Volatile
        private var INSTANCE: IDProviderSDK? = null
        
        fun getInstance(): IDProviderSDK {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: IDProviderSDK().also { INSTANCE = it }
            }
        }
    }
    
    // 私有属性
    private var config: IDProviderConfig? = null
    private var context: Context? = null
    private lateinit var networkManager: NetworkManager
    private lateinit var biometricManager: BiometricManager
    private lateinit var deviceFingerprintManager: DeviceFingerprintManager
    private lateinit var secureStorage: SecureStorage
    
    // 常量
    private object Constants {
        const val ACCESS_TOKEN_KEY = "IDProvider_AccessToken"
        const val REFRESH_TOKEN_KEY = "IDProvider_RefreshToken"
        const val USER_INFO_KEY = "IDProvider_UserInfo"
        const val DEVICE_ID_KEY = "IDProvider_DeviceId"
        const val PREFS_NAME = "IDProviderSDK"
    }
    
    /**
     * 初始化SDK
     */
    fun initialize(context: Context, config: IDProviderConfig) {
        this.context = context.applicationContext
        this.config = config
        
        // 初始化组件
        networkManager = NetworkManager(config.baseURL, config.debugMode)
        biometricManager = BiometricManager.from(context)
        deviceFingerprintManager = DeviceFingerprintManager(context)
        secureStorage = SecureStorage(context)
        
        if (config.debugMode) {
            println("[IDProviderSDK] SDK已初始化，配置: $config")
        }
    }
    
    /**
     * 用户名密码登录
     */
    suspend fun login(username: String, password: String): AuthResult {
        return withContext(Dispatchers.IO) {
            try {
                val config = <EMAIL> ?: return@withContext AuthResult.Failed(IDProviderError.InvalidConfiguration)
                
                // 收集设备信息
                val deviceInfo = collectDeviceInfo()
                
                val parameters = mapOf(
                    "username" to username,
                    "password" to password,
                    "client_id" to config.clientId,
                    "client_secret" to config.clientSecret,
                    "device_info" to deviceInfo.toMap()
                )
                
                val response = networkManager.post("/auth/login", parameters)
                handleAuthResponse(response)
            } catch (e: Exception) {
                AuthResult.Failed(IDProviderError.NetworkError(e))
            }
        }
    }
    
    /**
     * 生物识别登录
     */
    suspend fun loginWithBiometric(activity: FragmentActivity): AuthResult {
        return withContext(Dispatchers.Main) {
            try {
                val config = <EMAIL> ?: return@withContext AuthResult.Failed(IDProviderError.InvalidConfiguration)
                
                if (!config.biometricEnabled) {
                    return@withContext AuthResult.Failed(IDProviderError.BiometricNotAvailable)
                }
                
                // 检查生物识别可用性
                when (biometricManager.canAuthenticate(BiometricManager.Authenticators.BIOMETRIC_WEAK)) {
                    BiometricManager.BIOMETRIC_SUCCESS -> {
                        // 执行生物识别验证
                        val result = authenticateWithBiometric(activity)
                        if (result) {
                            loginWithStoredCredentials()
                        } else {
                            AuthResult.Failed(IDProviderError.BiometricFailed)
                        }
                    }
                    BiometricManager.BIOMETRIC_ERROR_NO_HARDWARE -> {
                        AuthResult.Failed(IDProviderError.BiometricNotAvailable)
                    }
                    BiometricManager.BIOMETRIC_ERROR_HW_UNAVAILABLE -> {
                        AuthResult.Failed(IDProviderError.BiometricNotAvailable)
                    }
                    BiometricManager.BIOMETRIC_ERROR_NONE_ENROLLED -> {
                        AuthResult.Failed(IDProviderError.BiometricNotEnrolled)
                    }
                    else -> {
                        AuthResult.Failed(IDProviderError.BiometricNotAvailable)
                    }
                }
            } catch (e: Exception) {
                AuthResult.Failed(IDProviderError.UnknownError(e.message ?: "生物识别登录失败"))
            }
        }
    }
    
    /**
     * MFA验证
     */
    suspend fun verifyMFA(sessionId: String, method: MFAMethod, code: String): AuthResult {
        return withContext(Dispatchers.IO) {
            try {
                val parameters = mapOf(
                    "session_id" to sessionId,
                    "method" to method.value,
                    "code" to code
                )
                
                val response = networkManager.post("/auth/mfa/verify", parameters)
                handleAuthResponse(response)
            } catch (e: Exception) {
                AuthResult.Failed(IDProviderError.NetworkError(e))
            }
        }
    }
    
    /**
     * 刷新令牌
     */
    suspend fun refreshToken(): Result<String> {
        return withContext(Dispatchers.IO) {
            try {
                val refreshToken = secureStorage.getString(Constants.REFRESH_TOKEN_KEY)
                    ?: return@withContext Result.failure(IDProviderError.TokenExpired)
                
                val parameters = mapOf(
                    "refresh_token" to refreshToken,
                    "client_id" to (config?.clientId ?: "")
                )
                
                val response = networkManager.post("/auth/refresh", parameters)
                val accessToken = response.optString("access_token")
                
                if (accessToken.isNotEmpty()) {
                    secureStorage.putString(Constants.ACCESS_TOKEN_KEY, accessToken)
                    response.optString("refresh_token").takeIf { it.isNotEmpty() }?.let {
                        secureStorage.putString(Constants.REFRESH_TOKEN_KEY, it)
                    }
                    Result.success(accessToken)
                } else {
                    Result.failure(IDProviderError.InvalidToken)
                }
            } catch (e: Exception) {
                Result.failure(IDProviderError.NetworkError(e))
            }
        }
    }
    
    /**
     * 登出
     */
    suspend fun logout(): Result<Unit> {
        return withContext(Dispatchers.IO) {
            try {
                val accessToken = secureStorage.getString(Constants.ACCESS_TOKEN_KEY)
                
                if (accessToken != null) {
                    val parameters = mapOf("token" to accessToken)
                    try {
                        networkManager.post("/auth/logout", parameters)
                    } catch (e: Exception) {
                        // 忽略服务器错误，继续清理本地数据
                    }
                }
                
                // 清理本地数据
                clearLocalData()
                Result.success(Unit)
            } catch (e: Exception) {
                Result.failure(IDProviderError.NetworkError(e))
            }
        }
    }
    
    /**
     * 获取当前用户信息
     */
    suspend fun getCurrentUser(): Result<UserInfo> {
        return withContext(Dispatchers.IO) {
            try {
                val accessToken = secureStorage.getString(Constants.ACCESS_TOKEN_KEY)
                    ?: return@withContext Result.failure(IDProviderError.TokenExpired)
                
                val headers = mapOf("Authorization" to "Bearer $accessToken")
                val response = networkManager.get("/user/profile", headers = headers)
                
                val userInfo = UserInfo.fromJson(response)
                    ?: return@withContext Result.failure(IDProviderError.UnknownError("无法解析用户信息"))
                
                Result.success(userInfo)
            } catch (e: Exception) {
                Result.failure(IDProviderError.NetworkError(e))
            }
        }
    }
    
    /**
     * 检查认证状态
     */
    fun isAuthenticated(): Boolean {
        return secureStorage.getString(Constants.ACCESS_TOKEN_KEY) != null
    }
    
    /**
     * 获取访问令牌
     */
    fun getAccessToken(): String? {
        return secureStorage.getString(Constants.ACCESS_TOKEN_KEY)
    }
    
    /**
     * 启用生物识别
     */
    suspend fun enableBiometric(activity: FragmentActivity): Result<Unit> {
        return withContext(Dispatchers.Main) {
            try {
                val result = authenticateWithBiometric(activity, "启用生物识别登录")
                if (result) {
                    Result.success(Unit)
                } else {
                    Result.failure(IDProviderError.BiometricFailed)
                }
            } catch (e: Exception) {
                Result.failure(IDProviderError.UnknownError(e.message ?: "启用生物识别失败"))
            }
        }
    }
    
    /**
     * 获取设备信息
     */
    fun getDeviceInfo(): DeviceInfo {
        return collectDeviceInfo()
    }
    
    // 私有方法
    
    /**
     * 处理认证响应
     */
    private fun handleAuthResponse(response: JSONObject): AuthResult {
        return when {
            response.has("access_token") && response.has("refresh_token") -> {
                val accessToken = response.getString("access_token")
                val refreshToken = response.getString("refresh_token")
                
                // 保存令牌
                secureStorage.putString(Constants.ACCESS_TOKEN_KEY, accessToken)
                secureStorage.putString(Constants.REFRESH_TOKEN_KEY, refreshToken)
                
                AuthResult.Success(accessToken, refreshToken)
            }
            response.has("session_id") && response.has("required_methods") -> {
                val sessionId = response.getString("session_id")
                val methodsArray = response.getJSONArray("required_methods")
                val methods = mutableListOf<MFAMethod>()
                
                for (i in 0 until methodsArray.length()) {
                    val methodValue = methodsArray.getString(i)
                    MFAMethod.values().find { it.value == methodValue }?.let {
                        methods.add(it)
                    }
                }
                
                AuthResult.RequiresMFA(sessionId, methods)
            }
            response.has("session_id") && response.optBoolean("requires_biometric") -> {
                val sessionId = response.getString("session_id")
                AuthResult.RequiresBiometric(sessionId)
            }
            response.optBoolean("blocked") -> {
                val reason = response.optString("reason", "未知原因")
                AuthResult.Blocked(reason)
            }
            else -> {
                AuthResult.Failed(IDProviderError.UnknownError("未知的服务器响应"))
            }
        }
    }
    
    /**
     * 使用存储的凭据登录
     */
    private suspend fun loginWithStoredCredentials(): AuthResult {
        return withContext(Dispatchers.IO) {
            try {
                val accessToken = secureStorage.getString(Constants.ACCESS_TOKEN_KEY)
                    ?: return@withContext AuthResult.Failed(IDProviderError.TokenExpired)
                
                // 验证令牌有效性
                val headers = mapOf("Authorization" to "Bearer $accessToken")
                try {
                    networkManager.get("/auth/validate", headers = headers)
                    val refreshToken = secureStorage.getString(Constants.REFRESH_TOKEN_KEY)
                        ?: return@withContext AuthResult.Failed(IDProviderError.TokenExpired)
                    
                    AuthResult.Success(accessToken, refreshToken)
                } catch (e: Exception) {
                    // 令牌无效，尝试刷新
                    val refreshResult = refreshToken()
                    if (refreshResult.isSuccess) {
                        val newToken = refreshResult.getOrNull()!!
                        val refreshToken = secureStorage.getString(Constants.REFRESH_TOKEN_KEY)
                            ?: return@withContext AuthResult.Failed(IDProviderError.TokenExpired)
                        AuthResult.Success(newToken, refreshToken)
                    } else {
                        AuthResult.Failed(refreshResult.exceptionOrNull() as? IDProviderError ?: IDProviderError.TokenExpired)
                    }
                }
            } catch (e: Exception) {
                AuthResult.Failed(IDProviderError.NetworkError(e))
            }
        }
    }
    
    /**
     * 执行生物识别认证
     */
    private suspend fun authenticateWithBiometric(
        activity: FragmentActivity,
        title: String = "请验证身份"
    ): Boolean {
        return suspendCancellableCoroutine { continuation ->
            val executor = ContextCompat.getMainExecutor(activity)
            val biometricPrompt = BiometricPrompt(activity, executor, object : BiometricPrompt.AuthenticationCallback() {
                override fun onAuthenticationError(errorCode: Int, errString: CharSequence) {
                    super.onAuthenticationError(errorCode, errString)
                    if (continuation.isActive) {
                        continuation.resume(false) {}
                    }
                }
                
                override fun onAuthenticationSucceeded(result: BiometricPrompt.AuthenticationResult) {
                    super.onAuthenticationSucceeded(result)
                    if (continuation.isActive) {
                        continuation.resume(true) {}
                    }
                }
                
                override fun onAuthenticationFailed() {
                    super.onAuthenticationFailed()
                    if (continuation.isActive) {
                        continuation.resume(false) {}
                    }
                }
            })
            
            val promptInfo = BiometricPrompt.PromptInfo.Builder()
                .setTitle(title)
                .setSubtitle("使用生物识别验证身份")
                .setNegativeButtonText("取消")
                .build()
            
            biometricPrompt.authenticate(promptInfo)
        }
    }
    
    /**
     * 收集设备信息
     */
    private fun collectDeviceInfo(): DeviceInfo {
        val context = this.context ?: throw IllegalStateException("SDK未初始化")
        val deviceId = getOrCreateDeviceId()
        val fingerprint = if (config?.deviceFingerprintEnabled == true) {
            deviceFingerprintManager.generateFingerprint()
        } else {
            ""
        }
        
        return DeviceInfo(
            deviceId = deviceId,
            model = android.os.Build.MODEL,
            systemVersion = android.os.Build.VERSION.RELEASE,
            appVersion = getAppVersion(),
            fingerprint = fingerprint,
            trustLevel = calculateTrustLevel(),
            isRooted = deviceFingerprintManager.isRooted()
        )
    }
    
    /**
     * 获取或创建设备ID
     */
    private fun getOrCreateDeviceId(): String {
        val existingId = secureStorage.getString(Constants.DEVICE_ID_KEY)
        if (existingId != null) {
            return existingId
        }
        
        val newId = UUID.randomUUID().toString()
        secureStorage.putString(Constants.DEVICE_ID_KEY, newId)
        return newId
    }
    
    /**
     * 获取应用版本
     */
    private fun getAppVersion(): String {
        return try {
            val context = this.context ?: return "Unknown"
            val packageInfo = context.packageManager.getPackageInfo(context.packageName, 0)
            packageInfo.versionName ?: "Unknown"
        } catch (e: Exception) {
            "Unknown"
        }
    }
    
    /**
     * 计算设备信任级别
     */
    private fun calculateTrustLevel(): Double {
        var trustLevel = 1.0
        
        // 如果设备Root，降低信任级别
        if (deviceFingerprintManager.isRooted()) {
            trustLevel -= 0.5
        }
        
        // 如果生物识别不可用，降低信任级别
        if (biometricManager.canAuthenticate(BiometricManager.Authenticators.BIOMETRIC_WEAK) != BiometricManager.BIOMETRIC_SUCCESS) {
            trustLevel -= 0.2
        }
        
        return maxOf(0.0, trustLevel)
    }
    
    /**
     * 清理本地数据
     */
    private fun clearLocalData() {
        secureStorage.remove(Constants.ACCESS_TOKEN_KEY)
        secureStorage.remove(Constants.REFRESH_TOKEN_KEY)
        secureStorage.remove(Constants.USER_INFO_KEY)
    }
}

// 扩展方法

private fun DeviceInfo.toMap(): Map<String, Any> {
    return mapOf(
        "device_id" to deviceId,
        "model" to model,
        "system_version" to systemVersion,
        "app_version" to appVersion,
        "fingerprint" to fingerprint,
        "trust_level" to trustLevel,
        "is_rooted" to isRooted
    )
}

private fun UserInfo.Companion.fromJson(json: JSONObject): UserInfo? {
    return try {
        UserInfo(
            id = json.getString("id"),
            email = json.getString("email"),
            name = json.getString("name"),
            roles = json.getJSONArray("roles").let { array ->
                (0 until array.length()).map { array.getString(it) }
            },
            permissions = json.getJSONArray("permissions").let { array ->
                (0 until array.length()).map { array.getString(it) }
            },
            lastLoginAt = json.optString("last_login_at").takeIf { it.isNotEmpty() }?.let {
                // 这里应该解析ISO8601日期格式
                Date()
            },
            createdAt = Date() // 这里应该解析ISO8601日期格式
        )
    } catch (e: Exception) {
        null
    }
}

private fun UserInfo.Companion {
    // 伴生对象扩展
}
