/**
 * 示例插件：遗留系统适配器
 * 演示如何为遗留系统创建自定义认证插件
 */

const crypto = require('crypto');
const axios = require('axios');

/**
 * 遗留系统协议适配器
 */
class LegacySystemAdapter {
  constructor() {
    this.name = 'legacy-system';
    this.version = '1.0';
    this.supportedMethods = ['custom_token', 'legacy_auth'];
    this.config = null;
    this.initialized = false;
  }

  async initialize(config) {
    this.config = config;
    this.initialized = true;
    console.log('遗留系统适配器初始化完成', { config });
  }

  async handleAuthRequest(request) {
    if (!this.initialized) {
      throw new Error('适配器未初始化');
    }

    try {
      // 模拟遗留系统认证流程
      const { customParams } = request;
      const legacyToken = customParams?.legacy_token;

      if (!legacyToken) {
        return {
          success: false,
          error: {
            code: 'missing_legacy_token',
            message: '缺少遗留系统令牌'
          }
        };
      }

      // 验证遗留系统令牌
      const isValid = await this.validateLegacyToken(legacyToken);
      if (!isValid) {
        return {
          success: false,
          error: {
            code: 'invalid_legacy_token',
            message: '无效的遗留系统令牌'
          }
        };
      }

      // 获取用户信息
      const userInfo = await this.getLegacyUserInfo(legacyToken);

      // 生成现代化令牌
      const tokens = await this.generateModernTokens(userInfo);

      return {
        success: true,
        tokens,
        user: {
          id: userInfo.user_id,
          email: userInfo.email,
          name: userInfo.display_name,
          attributes: {
            legacy_user_id: userInfo.legacy_id,
            department: userInfo.department,
            role: userInfo.role
          }
        }
      };

    } catch (error) {
      console.error('遗留系统认证失败', error);
      return {
        success: false,
        error: {
          code: 'legacy_auth_error',
          message: error.message
        }
      };
    }
  }

  async handleTokenRequest(request) {
    // 处理令牌刷新等请求
    const { grant_type } = request.body;

    if (grant_type === 'legacy_refresh') {
      return await this.handleLegacyRefresh(request.body);
    }

    return {
      success: false,
      error: {
        code: 'unsupported_grant_type',
        message: '不支持的授权类型'
      }
    };
  }

  async handleUserInfoRequest(request) {
    // 从遗留系统获取用户信息
    try {
      const token = this.extractBearerToken(request);
      if (!token) {
        return {
          success: false,
          error: {
            code: 'missing_token',
            message: '缺少访问令牌'
          }
        };
      }

      // 这里应该验证令牌并获取用户信息
      const userInfo = await this.getUserInfoFromToken(token);

      return {
        success: true,
        user: userInfo
      };

    } catch (error) {
      return {
        success: false,
        error: {
          code: 'userinfo_error',
          message: error.message
        }
      };
    }
  }

  async handleLogoutRequest(request) {
    // 处理登出请求
    return { success: true };
  }

  async generateMetadata() {
    return {
      protocol: this.name,
      version: this.version,
      supported_grant_types: ['legacy_auth', 'legacy_refresh'],
      legacy_system_info: {
        system_name: this.config?.system_name || 'Unknown Legacy System',
        api_version: this.config?.api_version || '1.0'
      }
    };
  }

  async validateConfig(config) {
    return !!(config.legacy_api_url && config.legacy_api_key);
  }

  // 私有方法

  async validateLegacyToken(token) {
    try {
      // 调用遗留系统API验证令牌
      const response = await axios.post(`${this.config.legacy_api_url}/validate`, {
        token,
        api_key: this.config.legacy_api_key
      });

      return response.data.valid === true;
    } catch (error) {
      console.error('验证遗留令牌失败', error);
      return false;
    }
  }

  async getLegacyUserInfo(token) {
    try {
      const response = await axios.get(`${this.config.legacy_api_url}/user`, {
        headers: {
          'Authorization': `Legacy ${token}`,
          'X-API-Key': this.config.legacy_api_key
        }
      });

      return response.data;
    } catch (error) {
      throw new Error('获取遗留用户信息失败');
    }
  }

  async generateModernTokens(userInfo) {
    // 生成JWT令牌
    const payload = {
      sub: userInfo.user_id,
      email: userInfo.email,
      name: userInfo.display_name,
      legacy_id: userInfo.legacy_id,
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + 3600 // 1小时
    };

    // 这里应该使用实际的JWT库和密钥
    const accessToken = Buffer.from(JSON.stringify(payload)).toString('base64');

    return {
      access_token: accessToken,
      token_type: 'Bearer',
      expires_in: 3600
    };
  }

  extractBearerToken(request) {
    const authHeader = request.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return null;
    }
    return authHeader.substring(7);
  }

  async getUserInfoFromToken(token) {
    // 解析令牌获取用户信息
    try {
      const payload = JSON.parse(Buffer.from(token, 'base64').toString());
      return {
        id: payload.sub,
        email: payload.email,
        name: payload.name,
        attributes: {
          legacy_id: payload.legacy_id
        }
      };
    } catch (error) {
      throw new Error('无效的令牌格式');
    }
  }
}

/**
 * 遗留系统插件主类
 */
class LegacySystemPlugin {
  constructor() {
    this.name = 'legacy-system-plugin';
    this.version = '1.0.0';
    this.description = '遗留系统认证适配器插件';
    this.author = 'IdP Team';
  }

  async initialize(config) {
    this.config = config;
    console.log('遗留系统插件初始化', { config });
  }

  async destroy() {
    console.log('遗留系统插件销毁');
  }

  getProtocolAdapters() {
    return {
      'legacy-system': LegacySystemAdapter
    };
  }

  getCustomHandlers() {
    return {
      'legacy_user_transform': this.transformLegacyUser.bind(this),
      'legacy_token_validate': this.validateLegacyToken.bind(this),
      'legacy_data_sync': this.syncLegacyData.bind(this)
    };
  }

  async healthCheck() {
    try {
      // 检查遗留系统连接
      if (this.config?.legacy_api_url) {
        const response = await axios.get(`${this.config.legacy_api_url}/health`, {
          timeout: 5000
        });
        return response.status === 200;
      }
      return true;
    } catch (error) {
      return false;
    }
  }

  // 自定义处理器

  async transformLegacyUser(userData) {
    // 转换遗留系统用户数据格式
    return {
      id: userData.legacy_id,
      email: userData.email_address,
      firstName: userData.first_name,
      lastName: userData.last_name,
      nickname: userData.display_name,
      customAttributes: {
        employeeId: userData.employee_id,
        department: userData.dept_code,
        position: userData.job_title
      }
    };
  }

  async validateLegacyToken(token, context) {
    // 验证遗留系统令牌
    try {
      const adapter = new LegacySystemAdapter();
      await adapter.initialize(this.config);
      return await adapter.validateLegacyToken(token);
    } catch (error) {
      console.error('遗留令牌验证失败', error);
      return false;
    }
  }

  async syncLegacyData(userId, legacyData) {
    // 同步遗留系统数据
    console.log('同步遗留数据', { userId, legacyData });
    
    // 这里可以实现数据同步逻辑
    // 例如：更新用户属性、同步权限等
    
    return {
      success: true,
      syncedFields: Object.keys(legacyData)
    };
  }
}

// 导出插件类
module.exports = LegacySystemPlugin;
