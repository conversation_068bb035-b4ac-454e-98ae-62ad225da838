/**
 * Jest E2E测试配置
 * 专门用于端到端测试的Jest配置
 */

module.exports = {
  // 测试环境
  testEnvironment: 'node',
  
  // 测试文件匹配模式
  testMatch: [
    '<rootDir>/src/test/e2e/**/*.e2e.test.ts'
  ],
  
  // 忽略的文件模式
  testPathIgnorePatterns: [
    '/node_modules/',
    '/dist/',
    '/build/'
  ],
  
  // TypeScript支持
  preset: 'ts-jest',
  
  // 模块文件扩展名
  moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx', 'json'],
  
  // 模块名映射（支持路径别名）
  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/src/$1'
  },
  
  // 转换配置
  transform: {
    '^.+\\.tsx?$': 'ts-jest'
  },
  
  // 设置文件
  setupFilesAfterEnv: [
    '<rootDir>/src/test/e2e/jest.setup.ts'
  ],
  
  // 测试超时时间（E2E测试通常需要更长时间）
  testTimeout: 60000,
  
  // 全局设置
  globals: {
    'ts-jest': {
      tsconfig: {
        compilerOptions: {
          module: 'commonjs',
          target: 'es2020',
          lib: ['es2020'],
          allowJs: true,
          skipLibCheck: true,
          esModuleInterop: true,
          allowSyntheticDefaultImports: true,
          strict: true,
          forceConsistentCasingInFileNames: true,
          moduleResolution: 'node',
          resolveJsonModule: true,
          isolatedModules: true,
          noEmit: true,
          experimentalDecorators: true,
          emitDecoratorMetadata: true,
          baseUrl: '.',
          paths: {
            '@/*': ['src/*']
          }
        }
      }
    }
  },
  
  // 覆盖率配置（E2E测试通常不需要覆盖率）
  collectCoverage: false,
  
  // 并发运行配置
  maxWorkers: 1, // E2E测试通常需要串行运行
  
  // 详细输出
  verbose: true,
  
  // 测试结果报告
  reporters: [
    'default',
    [
      'jest-html-reporters',
      {
        publicPath: './test-results/e2e',
        filename: 'e2e-test-report.html',
        expand: true,
        hideIcon: false,
        pageTitle: 'E2E Test Report',
        logoImgPath: undefined,
        inlineSource: false
      }
    ],
    [
      'jest-junit',
      {
        outputDirectory: './test-results/e2e',
        outputName: 'e2e-test-results.xml',
        suiteName: 'E2E Tests',
        classNameTemplate: '{classname}',
        titleTemplate: '{title}',
        ancestorSeparator: ' › ',
        usePathForSuiteName: true
      }
    ]
  ],
  
  // 环境变量
  testEnvironmentOptions: {
    NODE_ENV: 'test'
  },
  
  // 清理模拟
  clearMocks: true,
  restoreMocks: true,
  
  // 错误处理
  errorOnDeprecated: true,
  
  // 监视模式配置
  watchPathIgnorePatterns: [
    '/node_modules/',
    '/dist/',
    '/build/',
    '/test-results/'
  ],
  
  // 缓存配置
  cache: false, // E2E测试禁用缓存以确保一致性
  
  // 全局变量
  globals: {
    ...module.exports.globals,
    E2E_TEST_MODE: true,
    TEST_TIMEOUT: 60000
  }
};
