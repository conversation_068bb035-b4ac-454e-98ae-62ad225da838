/**
 * ID Provider 移动端应用主入口
 * 支持生物识别认证、离线模式和多语言
 */

import React, { useEffect, useState } from 'react';
import { StatusBar } from 'expo-status-bar';
import { Provider as PaperProvider } from 'react-native-paper';
import { Provider as ReduxProvider } from 'react-redux';
import { PersistGate } from 'redux-persist/integration/react';
import { NavigationContainer } from '@react-navigation/native';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import * as SplashScreen from 'expo-splash-screen';
import * as Font from 'expo-font';
import * as Updates from 'expo-updates';
import NetInfo from '@react-native-community/netinfo';
import { Alert, AppState, AppStateStatus } from 'react-native';

import { store, persistor } from './src/store';
import { AppNavigator } from './src/navigation/AppNavigator';
import { AuthNavigator } from './src/navigation/AuthNavigator';
import { LoadingScreen } from './src/components/LoadingScreen';
import { ErrorBoundary } from './src/components/ErrorBoundary';
import { NotificationManager } from './src/services/NotificationManager';
import { BiometricService } from './src/services/BiometricService';
import { OfflineManager } from './src/services/OfflineManager';
import { SecurityService } from './src/services/SecurityService';
import { theme } from './src/theme';
import { useAppSelector, useAppDispatch } from './src/hooks/redux';
import { setNetworkStatus, setAppState } from './src/store/slices/appSlice';
import { logout } from './src/store/slices/authSlice';
import './src/i18n/config';

// 防止启动画面自动隐藏
SplashScreen.preventAutoHideAsync();

/**
 * 应用根组件
 */
function AppContent() {
  const dispatch = useAppDispatch();
  const { isAuthenticated, user } = useAppSelector(state => state.auth);
  const { isOnline, appState } = useAppSelector(state => state.app);
  
  const [isReady, setIsReady] = useState(false);
  const [initializing, setInitializing] = useState(true);

  /**
   * 初始化应用
   */
  useEffect(() => {
    async function initializeApp() {
      try {
        // 加载字体
        await Font.loadAsync({
          'Roboto': require('./assets/fonts/Roboto-Regular.ttf'),
          'Roboto-Bold': require('./assets/fonts/Roboto-Bold.ttf'),
        });

        // 初始化通知管理器
        await NotificationManager.initialize();

        // 初始化生物识别服务
        await BiometricService.initialize();

        // 初始化离线管理器
        await OfflineManager.initialize();

        // 初始化安全服务
        await SecurityService.initialize();

        // 检查应用更新
        await checkForUpdates();

        setIsReady(true);
      } catch (error) {
        console.error('应用初始化失败:', error);
        Alert.alert('初始化错误', '应用初始化失败，请重启应用');
      } finally {
        setInitializing(false);
        await SplashScreen.hideAsync();
      }
    }

    initializeApp();
  }, []);

  /**
   * 监听网络状态变化
   */
  useEffect(() => {
    const unsubscribe = NetInfo.addEventListener(state => {
      dispatch(setNetworkStatus({
        isOnline: state.isConnected ?? false,
        connectionType: state.type,
        isInternetReachable: state.isInternetReachable ?? false
      }));

      // 网络恢复时同步离线数据
      if (state.isConnected && !isOnline) {
        OfflineManager.syncOfflineData();
      }
    });

    return unsubscribe;
  }, [dispatch, isOnline]);

  /**
   * 监听应用状态变化
   */
  useEffect(() => {
    const handleAppStateChange = (nextAppState: AppStateStatus) => {
      dispatch(setAppState(nextAppState));

      // 应用进入后台时的安全处理
      if (appState === 'active' && nextAppState.match(/inactive|background/)) {
        SecurityService.handleAppBackground();
      }

      // 应用回到前台时的安全检查
      if (appState.match(/inactive|background/) && nextAppState === 'active') {
        SecurityService.handleAppForeground();
      }
    };

    const subscription = AppState.addEventListener('change', handleAppStateChange);
    return () => subscription?.remove();
  }, [dispatch, appState]);

  /**
   * 检查应用更新
   */
  const checkForUpdates = async () => {
    try {
      if (!__DEV__) {
        const update = await Updates.checkForUpdateAsync();
        if (update.isAvailable) {
          Alert.alert(
            '发现新版本',
            '有新版本可用，是否立即更新？',
            [
              { text: '稍后', style: 'cancel' },
              {
                text: '更新',
                onPress: async () => {
                  await Updates.fetchUpdateAsync();
                  await Updates.reloadAsync();
                }
              }
            ]
          );
        }
      }
    } catch (error) {
      console.error('检查更新失败:', error);
    }
  };

  /**
   * 处理深度链接
   */
  const handleDeepLink = (url: string) => {
    // 处理深度链接逻辑
    console.log('处理深度链接:', url);
  };

  if (initializing || !isReady) {
    return <LoadingScreen />;
  }

  return (
    <SafeAreaProvider>
      <NavigationContainer
        onStateChange={(state) => {
          // 导航状态变化处理
          console.log('导航状态变化:', state);
        }}
        linking={{
          prefixes: ['id-provider://', 'https://id-provider.example.com'],
          config: {
            screens: {
              Auth: {
                screens: {
                  Login: 'login',
                  Register: 'register',
                  ForgotPassword: 'forgot-password',
                  ResetPassword: 'reset-password',
                }
              },
              Main: {
                screens: {
                  Home: 'home',
                  Profile: 'profile',
                  Settings: 'settings',
                  Security: 'security',
                }
              }
            }
          }
        }}
      >
        <StatusBar style="auto" />
        {isAuthenticated ? <AppNavigator /> : <AuthNavigator />}
      </NavigationContainer>
    </SafeAreaProvider>
  );
}

/**
 * 应用主组件
 */
export default function App() {
  return (
    <ErrorBoundary>
      <ReduxProvider store={store}>
        <PersistGate loading={<LoadingScreen />} persistor={persistor}>
          <PaperProvider theme={theme}>
            <AppContent />
          </PaperProvider>
        </PersistGate>
      </ReduxProvider>
    </ErrorBoundary>
  );
}
