{"name": "id-provider-mobile", "version": "1.0.0", "description": "ID Provider 移动端应用", "main": "index.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "build:android": "eas build --platform android", "build:ios": "eas build --platform ios", "build:all": "eas build --platform all", "submit:android": "eas submit --platform android", "submit:ios": "eas submit --platform ios", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint . --ext .js,.jsx,.ts,.tsx --fix", "type-check": "tsc --noEmit", "clean": "expo r -c", "prebuild": "expo prebuild", "dev-client": "expo install --fix"}, "dependencies": {"@expo/vector-icons": "^13.0.0", "@react-native-async-storage/async-storage": "1.18.2", "@react-native-community/netinfo": "9.3.10", "@react-navigation/bottom-tabs": "^6.5.8", "@react-navigation/native": "^6.1.7", "@react-navigation/stack": "^6.3.17", "@reduxjs/toolkit": "^1.9.5", "expo": "~49.0.0", "expo-auth-session": "~5.0.2", "expo-barcode-scanner": "~12.5.3", "expo-camera": "~13.4.2", "expo-constants": "~14.4.2", "expo-crypto": "~12.4.1", "expo-device": "~5.4.0", "expo-font": "~11.4.0", "expo-linking": "~5.0.2", "expo-local-authentication": "~13.4.1", "expo-notifications": "~0.20.1", "expo-secure-store": "~12.3.1", "expo-splash-screen": "~0.20.5", "expo-status-bar": "~1.6.0", "expo-updates": "~0.18.12", "react": "18.2.0", "react-native": "0.72.3", "react-native-gesture-handler": "~2.12.0", "react-native-paper": "^5.9.1", "react-native-reanimated": "~3.3.0", "react-native-safe-area-context": "4.6.3", "react-native-screens": "~3.22.0", "react-native-svg": "13.9.0", "react-redux": "^8.1.2", "redux-persist": "^6.0.0", "react-hook-form": "^7.45.4", "react-native-keychain": "^8.1.3", "react-native-qrcode-scanner": "^1.5.5", "react-native-vector-icons": "^10.0.0", "axios": "^1.4.0", "date-fns": "^2.30.0", "i18next": "^23.4.4", "react-i18next": "^13.1.2", "react-native-localize": "^3.0.2"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "~18.2.14", "@types/react-native": "~0.72.2", "@typescript-eslint/eslint-plugin": "^6.2.1", "@typescript-eslint/parser": "^6.2.1", "eslint": "^8.45.0", "eslint-config-expo": "^7.0.0", "eslint-plugin-react": "^7.33.1", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-native": "^4.0.0", "jest": "^29.6.2", "jest-expo": "~49.0.0", "@testing-library/react-native": "^12.1.3", "@testing-library/jest-native": "^5.4.2", "typescript": "^5.1.3", "metro-config": "^0.76.7", "react-test-renderer": "18.2.0"}, "jest": {"preset": "jest-expo", "setupFilesAfterEnv": ["@testing-library/jest-native/extend-expect"], "transformIgnorePatterns": ["node_modules/(?!((jest-)?react-native|@react-native(-community)?)|expo(nent)?|@expo(nent)?/.*|@expo-google-fonts/.*|react-navigation|@react-navigation/.*|@unimodules/.*|unimodules|sentry-expo|native-base|react-native-svg)"], "collectCoverageFrom": ["src/**/*.{js,jsx,ts,tsx}", "!src/**/*.d.ts", "!src/**/*.test.{js,jsx,ts,tsx}", "!src/**/__tests__/**"]}, "private": true, "keywords": ["react-native", "expo", "mobile", "authentication", "identity-provider", "mfa", "biometric", "offline"], "author": "ID Provider Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/your-org/id-provider.git", "directory": "mobile"}, "bugs": {"url": "https://github.com/your-org/id-provider/issues"}, "homepage": "https://github.com/your-org/id-provider#readme"}