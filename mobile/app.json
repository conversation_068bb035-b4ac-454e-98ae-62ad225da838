{"expo": {"name": "ID Provider", "slug": "id-provider-mobile", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "automatic", "splash": {"image": "./assets/splash.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "bundleIdentifier": "com.idprovider.mobile", "buildNumber": "1", "infoPlist": {"NSCameraUsageDescription": "此应用需要访问相机以扫描二维码进行身份验证", "NSFaceIDUsageDescription": "此应用使用Face ID进行生物识别认证", "NSMicrophoneUsageDescription": "此应用需要访问麦克风以进行语音认证"}, "associatedDomains": ["applinks:id-provider.example.com"]}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#FFFFFF"}, "package": "com.idprovider.mobile", "versionCode": 1, "permissions": ["CAMERA", "USE_FINGERPRINT", "USE_BIOMETRIC", "INTERNET", "ACCESS_NETWORK_STATE", "VIBRATE", "RECEIVE_BOOT_COMPLETED"], "intentFilters": [{"action": "VIEW", "autoVerify": true, "data": [{"scheme": "https", "host": "id-provider.example.com"}], "category": ["BROWSABLE", "DEFAULT"]}]}, "web": {"favicon": "./assets/favicon.png", "bundler": "metro"}, "plugins": ["expo-localization", ["expo-local-authentication", {"faceIDPermission": "允许 $(PRODUCT_NAME) 使用Face ID进行身份验证"}], ["expo-camera", {"cameraPermission": "允许 $(PRODUCT_NAME) 访问相机以扫描二维码"}], ["expo-notifications", {"icon": "./assets/notification-icon.png", "color": "#ffffff", "sounds": ["./assets/notification.wav"]}], ["expo-secure-store"], ["expo-updates", {"username": "your-expo-username"}]], "scheme": "id-provider", "extra": {"eas": {"projectId": "your-project-id"}, "apiUrl": "https://api.id-provider.example.com", "webUrl": "https://id-provider.example.com", "sentryDsn": "your-sentry-dsn"}, "updates": {"fallbackToCacheTimeout": 0, "url": "https://u.expo.dev/your-project-id"}, "runtimeVersion": {"policy": "sdkVersion"}}}