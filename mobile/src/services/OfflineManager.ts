/**
 * 离线数据管理服务
 * 支持离线缓存、数据同步和冲突解决
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import NetInfo from '@react-native-community/netinfo';
import { Alert } from 'react-native';

export interface OfflineAction {
  id: string;
  type: string;
  payload: any;
  timestamp: number;
  retryCount: number;
  maxRetries: number;
}

export interface SyncResult {
  success: boolean;
  syncedActions: number;
  failedActions: number;
  conflicts: number;
  errors: string[];
}

export interface CacheItem<T = any> {
  data: T;
  timestamp: number;
  ttl: number;
  version: string;
}

/**
 * 离线管理器类
 */
export class OfflineManager {
  private static instance: OfflineManager;
  private isOnline = true;
  private syncInProgress = false;
  private pendingActions: OfflineAction[] = [];
  private cache = new Map<string, CacheItem>();
  private syncQueue: OfflineAction[] = [];

  // 存储键
  private static readonly PENDING_ACTIONS_KEY = 'offline_pending_actions';
  private static readonly CACHE_KEY_PREFIX = 'offline_cache_';
  private static readonly SYNC_QUEUE_KEY = 'offline_sync_queue';

  private constructor() {}

  /**
   * 获取服务实例
   */
  static getInstance(): OfflineManager {
    if (!OfflineManager.instance) {
      OfflineManager.instance = new OfflineManager();
    }
    return OfflineManager.instance;
  }

  /**
   * 初始化离线管理器
   */
  static async initialize(): Promise<void> {
    const instance = OfflineManager.getInstance();
    await instance.init();
  }

  /**
   * 初始化服务
   */
  private async init(): Promise<void> {
    try {
      // 监听网络状态变化
      NetInfo.addEventListener(state => {
        const wasOnline = this.isOnline;
        this.isOnline = state.isConnected ?? false;

        // 网络恢复时自动同步
        if (!wasOnline && this.isOnline) {
          this.syncOfflineData();
        }
      });

      // 加载待处理的操作
      await this.loadPendingActions();
      
      // 加载同步队列
      await this.loadSyncQueue();

      // 清理过期缓存
      await this.cleanExpiredCache();

      console.log('离线管理器初始化完成');
    } catch (error) {
      console.error('离线管理器初始化失败:', error);
      throw error;
    }
  }

  /**
   * 添加离线操作
   */
  async addOfflineAction(
    type: string,
    payload: any,
    options: {
      maxRetries?: number;
      priority?: 'low' | 'normal' | 'high';
    } = {}
  ): Promise<string> {
    const action: OfflineAction = {
      id: this.generateId(),
      type,
      payload,
      timestamp: Date.now(),
      retryCount: 0,
      maxRetries: options.maxRetries || 3
    };

    this.pendingActions.push(action);
    await this.savePendingActions();

    // 如果在线，立即尝试执行
    if (this.isOnline) {
      this.processPendingActions();
    }

    return action.id;
  }

  /**
   * 处理待处理的操作
   */
  private async processPendingActions(): Promise<void> {
    if (this.syncInProgress || !this.isOnline) {
      return;
    }

    this.syncInProgress = true;

    try {
      const actionsToProcess = [...this.pendingActions];
      const processedActions: string[] = [];

      for (const action of actionsToProcess) {
        try {
          const success = await this.executeAction(action);
          
          if (success) {
            processedActions.push(action.id);
          } else {
            action.retryCount++;
            if (action.retryCount >= action.maxRetries) {
              // 达到最大重试次数，移除操作
              processedActions.push(action.id);
              console.warn(`操作 ${action.id} 达到最大重试次数，已放弃`);
            }
          }
        } catch (error) {
          console.error(`执行操作 ${action.id} 失败:`, error);
          action.retryCount++;
        }
      }

      // 移除已处理的操作
      this.pendingActions = this.pendingActions.filter(
        action => !processedActions.includes(action.id)
      );

      await this.savePendingActions();
    } finally {
      this.syncInProgress = false;
    }
  }

  /**
   * 执行单个操作
   */
  private async executeAction(action: OfflineAction): Promise<boolean> {
    try {
      // 这里应该根据操作类型调用相应的API
      switch (action.type) {
        case 'UPDATE_PROFILE':
          return await this.updateProfile(action.payload);
        case 'CHANGE_PASSWORD':
          return await this.changePassword(action.payload);
        case 'ENABLE_MFA':
          return await this.enableMFA(action.payload);
        case 'SYNC_SETTINGS':
          return await this.syncSettings(action.payload);
        default:
          console.warn(`未知的操作类型: ${action.type}`);
          return false;
      }
    } catch (error) {
      console.error(`执行操作失败:`, error);
      return false;
    }
  }

  /**
   * 缓存数据
   */
  async cacheData<T>(
    key: string,
    data: T,
    ttl: number = 24 * 60 * 60 * 1000, // 默认24小时
    version: string = '1.0'
  ): Promise<void> {
    const cacheItem: CacheItem<T> = {
      data,
      timestamp: Date.now(),
      ttl,
      version
    };

    this.cache.set(key, cacheItem);
    
    try {
      await AsyncStorage.setItem(
        `${OfflineManager.CACHE_KEY_PREFIX}${key}`,
        JSON.stringify(cacheItem)
      );
    } catch (error) {
      console.error('缓存数据失败:', error);
    }
  }

  /**
   * 获取缓存数据
   */
  async getCachedData<T>(key: string): Promise<T | null> {
    try {
      // 先检查内存缓存
      let cacheItem = this.cache.get(key);

      // 如果内存中没有，从存储中加载
      if (!cacheItem) {
        const stored = await AsyncStorage.getItem(`${OfflineManager.CACHE_KEY_PREFIX}${key}`);
        if (stored) {
          cacheItem = JSON.parse(stored);
          this.cache.set(key, cacheItem!);
        }
      }

      if (!cacheItem) {
        return null;
      }

      // 检查是否过期
      const now = Date.now();
      if (now - cacheItem.timestamp > cacheItem.ttl) {
        await this.removeCachedData(key);
        return null;
      }

      return cacheItem.data;
    } catch (error) {
      console.error('获取缓存数据失败:', error);
      return null;
    }
  }

  /**
   * 移除缓存数据
   */
  async removeCachedData(key: string): Promise<void> {
    this.cache.delete(key);
    
    try {
      await AsyncStorage.removeItem(`${OfflineManager.CACHE_KEY_PREFIX}${key}`);
    } catch (error) {
      console.error('移除缓存数据失败:', error);
    }
  }

  /**
   * 清理过期缓存
   */
  private async cleanExpiredCache(): Promise<void> {
    try {
      const keys = await AsyncStorage.getAllKeys();
      const cacheKeys = keys.filter(key => key.startsWith(OfflineManager.CACHE_KEY_PREFIX));
      const now = Date.now();

      for (const key of cacheKeys) {
        try {
          const stored = await AsyncStorage.getItem(key);
          if (stored) {
            const cacheItem: CacheItem = JSON.parse(stored);
            if (now - cacheItem.timestamp > cacheItem.ttl) {
              await AsyncStorage.removeItem(key);
              const cacheKey = key.replace(OfflineManager.CACHE_KEY_PREFIX, '');
              this.cache.delete(cacheKey);
            }
          }
        } catch (error) {
          // 如果解析失败，删除该项
          await AsyncStorage.removeItem(key);
        }
      }
    } catch (error) {
      console.error('清理过期缓存失败:', error);
    }
  }

  /**
   * 同步离线数据
   */
  async syncOfflineData(): Promise<SyncResult> {
    if (!this.isOnline) {
      return {
        success: false,
        syncedActions: 0,
        failedActions: 0,
        conflicts: 0,
        errors: ['设备离线，无法同步数据']
      };
    }

    const result: SyncResult = {
      success: true,
      syncedActions: 0,
      failedActions: 0,
      conflicts: 0,
      errors: []
    };

    try {
      // 处理待处理的操作
      await this.processPendingActions();
      result.syncedActions = this.pendingActions.length;

      // 同步用户数据
      await this.syncUserData();

      // 同步设置数据
      await this.syncSettingsData();

      console.log('离线数据同步完成', result);
    } catch (error) {
      result.success = false;
      result.errors.push(error instanceof Error ? error.message : String(error));
      console.error('离线数据同步失败:', error);
    }

    return result;
  }

  /**
   * 获取离线状态
   */
  getOfflineStatus(): {
    isOnline: boolean;
    pendingActions: number;
    cacheSize: number;
    lastSync: Date | null;
  } {
    return {
      isOnline: this.isOnline,
      pendingActions: this.pendingActions.length,
      cacheSize: this.cache.size,
      lastSync: null // 可以从存储中获取
    };
  }

  /**
   * 清除所有离线数据
   */
  async clearOfflineData(): Promise<void> {
    try {
      this.pendingActions = [];
      this.syncQueue = [];
      this.cache.clear();

      await AsyncStorage.removeItem(OfflineManager.PENDING_ACTIONS_KEY);
      await AsyncStorage.removeItem(OfflineManager.SYNC_QUEUE_KEY);

      // 清除所有缓存项
      const keys = await AsyncStorage.getAllKeys();
      const cacheKeys = keys.filter(key => key.startsWith(OfflineManager.CACHE_KEY_PREFIX));
      await AsyncStorage.multiRemove(cacheKeys);

      console.log('离线数据已清除');
    } catch (error) {
      console.error('清除离线数据失败:', error);
    }
  }

  // 私有辅助方法

  private async loadPendingActions(): Promise<void> {
    try {
      const stored = await AsyncStorage.getItem(OfflineManager.PENDING_ACTIONS_KEY);
      if (stored) {
        this.pendingActions = JSON.parse(stored);
      }
    } catch (error) {
      console.error('加载待处理操作失败:', error);
    }
  }

  private async savePendingActions(): Promise<void> {
    try {
      await AsyncStorage.setItem(
        OfflineManager.PENDING_ACTIONS_KEY,
        JSON.stringify(this.pendingActions)
      );
    } catch (error) {
      console.error('保存待处理操作失败:', error);
    }
  }

  private async loadSyncQueue(): Promise<void> {
    try {
      const stored = await AsyncStorage.getItem(OfflineManager.SYNC_QUEUE_KEY);
      if (stored) {
        this.syncQueue = JSON.parse(stored);
      }
    } catch (error) {
      console.error('加载同步队列失败:', error);
    }
  }

  private generateId(): string {
    return `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // API调用方法（示例）
  private async updateProfile(payload: any): Promise<boolean> {
    // 实际的API调用
    console.log('更新用户资料:', payload);
    return true;
  }

  private async changePassword(payload: any): Promise<boolean> {
    // 实际的API调用
    console.log('修改密码:', payload);
    return true;
  }

  private async enableMFA(payload: any): Promise<boolean> {
    // 实际的API调用
    console.log('启用MFA:', payload);
    return true;
  }

  private async syncSettings(payload: any): Promise<boolean> {
    // 实际的API调用
    console.log('同步设置:', payload);
    return true;
  }

  private async syncUserData(): Promise<void> {
    // 同步用户数据的实现
    console.log('同步用户数据');
  }

  private async syncSettingsData(): Promise<void> {
    // 同步设置数据的实现
    console.log('同步设置数据');
  }
}

// 导出服务实例
export const offlineManager = OfflineManager.getInstance();
