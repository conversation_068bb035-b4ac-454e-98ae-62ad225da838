/**
 * 生物识别认证服务
 * 支持指纹识别、面部识别和其他生物识别方式
 */

import * as LocalAuthentication from 'expo-local-authentication';
import * as SecureStore from 'expo-secure-store';
import { Alert, Platform } from 'react-native';

export enum BiometricType {
  FINGERPRINT = 'fingerprint',
  FACIAL_RECOGNITION = 'facial_recognition',
  IRIS = 'iris',
  VOICE = 'voice'
}

export interface BiometricAuthResult {
  success: boolean;
  error?: string;
  biometricType?: BiometricType;
  authenticationLevel?: 'weak' | 'strong';
}

export interface BiometricCapabilities {
  isAvailable: boolean;
  isEnrolled: boolean;
  supportedTypes: BiometricType[];
  securityLevel: 'none' | 'weak' | 'strong';
}

/**
 * 生物识别服务类
 */
export class BiometricService {
  private static instance: BiometricService;
  private capabilities: BiometricCapabilities | null = null;
  private isInitialized = false;

  private constructor() {}

  /**
   * 获取服务实例
   */
  static getInstance(): BiometricService {
    if (!BiometricService.instance) {
      BiometricService.instance = new BiometricService();
    }
    return BiometricService.instance;
  }

  /**
   * 初始化生物识别服务
   */
  static async initialize(): Promise<void> {
    const instance = BiometricService.getInstance();
    await instance.init();
  }

  /**
   * 初始化服务
   */
  private async init(): Promise<void> {
    try {
      this.capabilities = await this.checkCapabilities();
      this.isInitialized = true;
      console.log('生物识别服务初始化完成', this.capabilities);
    } catch (error) {
      console.error('生物识别服务初始化失败:', error);
      throw error;
    }
  }

  /**
   * 检查生物识别能力
   */
  async checkCapabilities(): Promise<BiometricCapabilities> {
    try {
      const isAvailable = await LocalAuthentication.hasHardwareAsync();
      const isEnrolled = await LocalAuthentication.isEnrolledAsync();
      const supportedTypes = await this.getSupportedBiometricTypes();
      const securityLevel = await LocalAuthentication.getEnrolledLevelAsync();

      return {
        isAvailable,
        isEnrolled,
        supportedTypes,
        securityLevel: this.mapSecurityLevel(securityLevel)
      };
    } catch (error) {
      console.error('检查生物识别能力失败:', error);
      return {
        isAvailable: false,
        isEnrolled: false,
        supportedTypes: [],
        securityLevel: 'none'
      };
    }
  }

  /**
   * 获取支持的生物识别类型
   */
  private async getSupportedBiometricTypes(): Promise<BiometricType[]> {
    const types: BiometricType[] = [];
    
    try {
      const supportedTypes = await LocalAuthentication.supportedAuthenticationTypesAsync();
      
      supportedTypes.forEach(type => {
        switch (type) {
          case LocalAuthentication.AuthenticationType.FINGERPRINT:
            types.push(BiometricType.FINGERPRINT);
            break;
          case LocalAuthentication.AuthenticationType.FACIAL_RECOGNITION:
            types.push(BiometricType.FACIAL_RECOGNITION);
            break;
          case LocalAuthentication.AuthenticationType.IRIS:
            types.push(BiometricType.IRIS);
            break;
        }
      });
    } catch (error) {
      console.error('获取支持的生物识别类型失败:', error);
    }

    return types;
  }

  /**
   * 映射安全级别
   */
  private mapSecurityLevel(level: LocalAuthentication.SecurityLevel): 'none' | 'weak' | 'strong' {
    switch (level) {
      case LocalAuthentication.SecurityLevel.NONE:
        return 'none';
      case LocalAuthentication.SecurityLevel.SECRET:
        return 'weak';
      case LocalAuthentication.SecurityLevel.BIOMETRIC_WEAK:
        return 'weak';
      case LocalAuthentication.SecurityLevel.BIOMETRIC_STRONG:
        return 'strong';
      default:
        return 'none';
    }
  }

  /**
   * 执行生物识别认证
   */
  async authenticate(options?: {
    promptMessage?: string;
    cancelLabel?: string;
    fallbackLabel?: string;
    disableDeviceFallback?: boolean;
    requireConfirmation?: boolean;
  }): Promise<BiometricAuthResult> {
    try {
      if (!this.isInitialized) {
        await this.init();
      }

      if (!this.capabilities?.isAvailable) {
        return {
          success: false,
          error: '设备不支持生物识别认证'
        };
      }

      if (!this.capabilities?.isEnrolled) {
        return {
          success: false,
          error: '未设置生物识别认证，请先在系统设置中配置'
        };
      }

      const result = await LocalAuthentication.authenticateAsync({
        promptMessage: options?.promptMessage || '请验证您的身份',
        cancelLabel: options?.cancelLabel || '取消',
        fallbackLabel: options?.fallbackLabel || '使用密码',
        disableDeviceFallback: options?.disableDeviceFallback || false,
        requireConfirmation: options?.requireConfirmation || false
      });

      if (result.success) {
        // 记录成功的生物识别认证
        await this.recordSuccessfulAuth();
        
        return {
          success: true,
          biometricType: this.getPrimaryBiometricType(),
          authenticationLevel: this.capabilities.securityLevel
        };
      } else {
        return {
          success: false,
          error: this.getErrorMessage(result.error)
        };
      }
    } catch (error) {
      console.error('生物识别认证失败:', error);
      return {
        success: false,
        error: '生物识别认证过程中发生错误'
      };
    }
  }

  /**
   * 获取主要生物识别类型
   */
  private getPrimaryBiometricType(): BiometricType | undefined {
    if (!this.capabilities?.supportedTypes.length) {
      return undefined;
    }

    // 优先级：面部识别 > 指纹 > 虹膜 > 语音
    const priorityOrder = [
      BiometricType.FACIAL_RECOGNITION,
      BiometricType.FINGERPRINT,
      BiometricType.IRIS,
      BiometricType.VOICE
    ];

    for (const type of priorityOrder) {
      if (this.capabilities.supportedTypes.includes(type)) {
        return type;
      }
    }

    return this.capabilities.supportedTypes[0];
  }

  /**
   * 获取错误消息
   */
  private getErrorMessage(error?: string): string {
    switch (error) {
      case 'user_cancel':
        return '用户取消了认证';
      case 'user_fallback':
        return '用户选择了备用认证方式';
      case 'system_cancel':
        return '系统取消了认证';
      case 'authentication_failed':
        return '认证失败，请重试';
      case 'biometric_not_available':
        return '生物识别功能不可用';
      case 'biometric_not_enrolled':
        return '未设置生物识别认证';
      case 'too_many_attempts':
        return '尝试次数过多，请稍后再试';
      default:
        return '认证失败，请重试';
    }
  }

  /**
   * 记录成功的认证
   */
  private async recordSuccessfulAuth(): Promise<void> {
    try {
      const timestamp = new Date().toISOString();
      await SecureStore.setItemAsync('last_biometric_auth', timestamp);
    } catch (error) {
      console.error('记录生物识别认证失败:', error);
    }
  }

  /**
   * 获取上次认证时间
   */
  async getLastAuthTime(): Promise<Date | null> {
    try {
      const timestamp = await SecureStore.getItemAsync('last_biometric_auth');
      return timestamp ? new Date(timestamp) : null;
    } catch (error) {
      console.error('获取上次认证时间失败:', error);
      return null;
    }
  }

  /**
   * 检查是否需要重新认证
   */
  async shouldReauthenticate(maxAgeMinutes: number = 30): Promise<boolean> {
    try {
      const lastAuth = await this.getLastAuthTime();
      if (!lastAuth) {
        return true;
      }

      const now = new Date();
      const diffMinutes = (now.getTime() - lastAuth.getTime()) / (1000 * 60);
      return diffMinutes > maxAgeMinutes;
    } catch (error) {
      console.error('检查重新认证需求失败:', error);
      return true;
    }
  }

  /**
   * 清除认证记录
   */
  async clearAuthRecord(): Promise<void> {
    try {
      await SecureStore.deleteItemAsync('last_biometric_auth');
    } catch (error) {
      console.error('清除认证记录失败:', error);
    }
  }

  /**
   * 显示生物识别设置引导
   */
  async showBiometricSetupGuide(): Promise<void> {
    const { supportedTypes, isEnrolled } = this.capabilities || {};
    
    if (!supportedTypes?.length) {
      Alert.alert(
        '不支持生物识别',
        '您的设备不支持生物识别认证功能。',
        [{ text: '确定' }]
      );
      return;
    }

    if (isEnrolled) {
      Alert.alert(
        '生物识别已设置',
        '您已经设置了生物识别认证，可以在应用中使用。',
        [{ text: '确定' }]
      );
      return;
    }

    const biometricName = this.getBiometricDisplayName();
    Alert.alert(
      '设置生物识别认证',
      `请在系统设置中启用${biometricName}，以便在应用中使用生物识别认证。`,
      [
        { text: '稍后设置', style: 'cancel' },
        { 
          text: '前往设置', 
          onPress: () => {
            // 在实际应用中，这里可以打开系统设置
            console.log('打开系统设置');
          }
        }
      ]
    );
  }

  /**
   * 获取生物识别显示名称
   */
  private getBiometricDisplayName(): string {
    const primaryType = this.getPrimaryBiometricType();
    
    switch (primaryType) {
      case BiometricType.FINGERPRINT:
        return '指纹识别';
      case BiometricType.FACIAL_RECOGNITION:
        return Platform.OS === 'ios' ? 'Face ID' : '面部识别';
      case BiometricType.IRIS:
        return '虹膜识别';
      case BiometricType.VOICE:
        return '语音识别';
      default:
        return '生物识别';
    }
  }

  /**
   * 获取当前能力
   */
  getCapabilities(): BiometricCapabilities | null {
    return this.capabilities;
  }

  /**
   * 检查是否可用
   */
  isAvailable(): boolean {
    return this.capabilities?.isAvailable && this.capabilities?.isEnrolled || false;
  }
}

// 导出服务实例
export const biometricService = BiometricService.getInstance();
