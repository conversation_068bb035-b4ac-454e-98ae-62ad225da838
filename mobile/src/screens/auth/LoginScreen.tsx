/**
 * 登录界面
 * 支持用户名密码登录、生物识别登录和社交登录
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  Alert,
  KeyboardAvoidingView,
  Platform,
  ScrollView
} from 'react-native';
import {
  TextInput,
  Button,
  Text,
  Card,
  Divider,
  IconButton,
  Switch,
  HelperText
} from 'react-native-paper';
import { useForm, Controller } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { SafeAreaView } from 'react-native-safe-area-context';

import { useAppDispatch, useAppSelector } from '../../hooks/redux';
import { login, socialLogin, checkBiometricAvailability } from '../../store/slices/authSlice';
import { BiometricService } from '../../services/BiometricService';
import { LoadingOverlay } from '../../components/LoadingOverlay';
import { SocialLoginButtons } from '../../components/SocialLoginButtons';
import { BiometricLoginButton } from '../../components/BiometricLoginButton';
import { theme } from '../../theme';

interface LoginFormData {
  username: string;
  password: string;
  rememberMe: boolean;
}

interface LoginScreenProps {
  navigation: any;
}

export const LoginScreen: React.FC<LoginScreenProps> = ({ navigation }) => {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();
  const { loading, error, biometricAvailable } = useAppSelector(state => state.auth);
  
  const [showPassword, setShowPassword] = useState(false);
  const [biometricEnabled, setBiometricEnabled] = useState(false);

  const {
    control,
    handleSubmit,
    formState: { errors, isValid },
    watch,
    setValue
  } = useForm<LoginFormData>({
    defaultValues: {
      username: '',
      password: '',
      rememberMe: false
    },
    mode: 'onChange'
  });

  const watchedValues = watch();

  useEffect(() => {
    // 检查生物识别可用性
    checkBiometricSupport();
    
    // 如果启用了记住我功能，尝试从安全存储加载凭据
    loadSavedCredentials();
  }, []);

  /**
   * 检查生物识别支持
   */
  const checkBiometricSupport = async () => {
    try {
      const capabilities = await BiometricService.getInstance().checkCapabilities();
      dispatch(checkBiometricAvailability(capabilities));
      setBiometricEnabled(capabilities.isAvailable && capabilities.isEnrolled);
    } catch (error) {
      console.error('检查生物识别支持失败:', error);
    }
  };

  /**
   * 加载保存的凭据
   */
  const loadSavedCredentials = async () => {
    // 从安全存储加载保存的用户名
    // 实际实现中应该使用 SecureStore
    try {
      // const savedUsername = await SecureStore.getItemAsync('saved_username');
      // if (savedUsername) {
      //   setValue('username', savedUsername);
      //   setValue('rememberMe', true);
      // }
    } catch (error) {
      console.error('加载保存的凭据失败:', error);
    }
  };

  /**
   * 处理表单提交
   */
  const onSubmit = async (data: LoginFormData) => {
    try {
      const result = await dispatch(login({
        username: data.username,
        password: data.password,
        rememberMe: data.rememberMe
      })).unwrap();

      if (result.success) {
        // 如果启用了记住我，保存用户名
        if (data.rememberMe) {
          // await SecureStore.setItemAsync('saved_username', data.username);
        }

        // 登录成功，导航到主界面
        navigation.reset({
          index: 0,
          routes: [{ name: 'Main' }]
        });
      }
    } catch (error) {
      Alert.alert(
        t('auth.login_failed'),
        error instanceof Error ? error.message : t('errors.unknown_error')
      );
    }
  };

  /**
   * 处理生物识别登录
   */
  const handleBiometricLogin = async () => {
    try {
      const result = await BiometricService.getInstance().authenticate({
        promptMessage: t('auth.biometric_prompt'),
        cancelLabel: t('common.cancel'),
        fallbackLabel: t('auth.use_password')
      });

      if (result.success) {
        // 生物识别成功，使用保存的凭据登录
        const savedCredentials = await loadBiometricCredentials();
        if (savedCredentials) {
          await dispatch(login({
            username: savedCredentials.username,
            token: savedCredentials.token,
            biometric: true
          })).unwrap();

          navigation.reset({
            index: 0,
            routes: [{ name: 'Main' }]
          });
        } else {
          Alert.alert(
            t('auth.biometric_login_failed'),
            t('auth.no_saved_credentials')
          );
        }
      }
    } catch (error) {
      Alert.alert(
        t('auth.biometric_login_failed'),
        error instanceof Error ? error.message : t('errors.unknown_error')
      );
    }
  };

  /**
   * 加载生物识别凭据
   */
  const loadBiometricCredentials = async () => {
    // 从安全存储加载生物识别凭据
    // 实际实现中应该使用加密的安全存储
    return null;
  };

  /**
   * 处理社交登录
   */
  const handleSocialLogin = async (provider: string) => {
    try {
      const result = await dispatch(socialLogin({ provider })).unwrap();
      
      if (result.success) {
        navigation.reset({
          index: 0,
          routes: [{ name: 'Main' }]
        });
      }
    } catch (error) {
      Alert.alert(
        t('auth.social_login_failed'),
        error instanceof Error ? error.message : t('errors.unknown_error')
      );
    }
  };

  /**
   * 导航到注册页面
   */
  const navigateToRegister = () => {
    navigation.navigate('Register');
  };

  /**
   * 导航到忘记密码页面
   */
  const navigateToForgotPassword = () => {
    navigation.navigate('ForgotPassword');
  };

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardAvoidingView}
      >
        <ScrollView
          contentContainerStyle={styles.scrollContent}
          keyboardShouldPersistTaps="handled"
        >
          <View style={styles.header}>
            <Text variant="headlineLarge" style={styles.title}>
              {t('auth.welcome_back')}
            </Text>
            <Text variant="bodyLarge" style={styles.subtitle}>
              {t('auth.login_subtitle')}
            </Text>
          </View>

          <Card style={styles.card}>
            <Card.Content>
              {/* 用户名输入 */}
              <Controller
                control={control}
                name="username"
                rules={{
                  required: t('validation.required'),
                  minLength: {
                    value: 3,
                    message: t('validation.min_length', { min: 3 })
                  }
                }}
                render={({ field: { onChange, onBlur, value } }) => (
                  <TextInput
                    label={t('auth.username')}
                    value={value}
                    onChangeText={onChange}
                    onBlur={onBlur}
                    error={!!errors.username}
                    autoCapitalize="none"
                    autoCorrect={false}
                    autoComplete="username"
                    textContentType="username"
                    style={styles.input}
                  />
                )}
              />
              <HelperText type="error" visible={!!errors.username}>
                {errors.username?.message}
              </HelperText>

              {/* 密码输入 */}
              <Controller
                control={control}
                name="password"
                rules={{
                  required: t('validation.required'),
                  minLength: {
                    value: 6,
                    message: t('validation.min_length', { min: 6 })
                  }
                }}
                render={({ field: { onChange, onBlur, value } }) => (
                  <TextInput
                    label={t('auth.password')}
                    value={value}
                    onChangeText={onChange}
                    onBlur={onBlur}
                    error={!!errors.password}
                    secureTextEntry={!showPassword}
                    autoCapitalize="none"
                    autoCorrect={false}
                    autoComplete="password"
                    textContentType="password"
                    style={styles.input}
                    right={
                      <TextInput.Icon
                        icon={showPassword ? 'eye-off' : 'eye'}
                        onPress={() => setShowPassword(!showPassword)}
                      />
                    }
                  />
                )}
              />
              <HelperText type="error" visible={!!errors.password}>
                {errors.password?.message}
              </HelperText>

              {/* 记住我选项 */}
              <View style={styles.rememberMeContainer}>
                <Controller
                  control={control}
                  name="rememberMe"
                  render={({ field: { onChange, value } }) => (
                    <View style={styles.switchContainer}>
                      <Text variant="bodyMedium">{t('auth.remember_me')}</Text>
                      <Switch
                        value={value}
                        onValueChange={onChange}
                        color={theme.colors.primary}
                      />
                    </View>
                  )}
                />
              </View>

              {/* 错误信息 */}
              {error && (
                <HelperText type="error" visible={true} style={styles.errorText}>
                  {error}
                </HelperText>
              )}

              {/* 登录按钮 */}
              <Button
                mode="contained"
                onPress={handleSubmit(onSubmit)}
                loading={loading}
                disabled={!isValid || loading}
                style={styles.loginButton}
                contentStyle={styles.buttonContent}
              >
                {t('auth.login')}
              </Button>

              {/* 忘记密码链接 */}
              <Button
                mode="text"
                onPress={navigateToForgotPassword}
                style={styles.forgotPasswordButton}
              >
                {t('auth.forgot_password')}
              </Button>
            </Card.Content>
          </Card>

          {/* 生物识别登录 */}
          {biometricEnabled && (
            <View style={styles.biometricContainer}>
              <Divider style={styles.divider} />
              <Text variant="bodyMedium" style={styles.orText}>
                {t('auth.or_login_with')}
              </Text>
              <BiometricLoginButton
                onPress={handleBiometricLogin}
                disabled={loading}
              />
            </View>
          )}

          {/* 社交登录 */}
          <View style={styles.socialContainer}>
            <Divider style={styles.divider} />
            <Text variant="bodyMedium" style={styles.orText}>
              {t('auth.or_continue_with')}
            </Text>
            <SocialLoginButtons
              onSocialLogin={handleSocialLogin}
              disabled={loading}
            />
          </View>

          {/* 注册链接 */}
          <View style={styles.registerContainer}>
            <Text variant="bodyMedium">
              {t('auth.no_account')}
            </Text>
            <Button
              mode="text"
              onPress={navigateToRegister}
              style={styles.registerButton}
            >
              {t('auth.register')}
            </Button>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>

      {loading && <LoadingOverlay />}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    padding: 20,
    justifyContent: 'center',
  },
  header: {
    alignItems: 'center',
    marginBottom: 30,
  },
  title: {
    fontWeight: 'bold',
    textAlign: 'center',
    color: theme.colors.onBackground,
    marginBottom: 8,
  },
  subtitle: {
    textAlign: 'center',
    color: theme.colors.onSurfaceVariant,
  },
  card: {
    marginBottom: 20,
  },
  input: {
    marginBottom: 8,
  },
  rememberMeContainer: {
    marginVertical: 16,
  },
  switchContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  errorText: {
    textAlign: 'center',
    marginBottom: 16,
  },
  loginButton: {
    marginTop: 16,
    marginBottom: 8,
  },
  buttonContent: {
    paddingVertical: 8,
  },
  forgotPasswordButton: {
    alignSelf: 'center',
  },
  biometricContainer: {
    alignItems: 'center',
    marginVertical: 20,
  },
  socialContainer: {
    alignItems: 'center',
    marginVertical: 20,
  },
  divider: {
    width: '100%',
    marginBottom: 16,
  },
  orText: {
    color: theme.colors.onSurfaceVariant,
    marginBottom: 16,
  },
  registerContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 20,
  },
  registerButton: {
    marginLeft: 8,
  },
});

export default LoginScreen;
