{"auth": {"login": "<PERSON><PERSON>", "logout": "Logout", "register": "Register", "forgot_password": "Forgot Password", "reset_password": "Reset Password", "change_password": "Change Password", "confirm_password": "Confirm Password", "username": "Username", "email": "Email", "password": "Password", "remember_me": "Remember Me", "login_success": "Login successful", "login_failed": "<PERSON><PERSON> failed", "logout_success": "Logout successful", "register_success": "Registration successful", "password_reset_sent": "Password reset email sent", "password_changed": "Password changed successfully", "invalid_credentials": "Invalid username or password", "account_locked": "Account is locked", "account_disabled": "Account is disabled", "session_expired": "Session expired, please login again", "mfa_required": "Multi-factor authentication required", "mfa_code": "Verification Code", "mfa_verify": "Verify", "mfa_invalid": "Invalid verification code", "biometric_login": "Biometric Login", "social_login": "Social Login"}, "common": {"save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "create": "Create", "update": "Update", "search": "Search", "filter": "Filter", "sort": "Sort", "loading": "Loading...", "error": "Error", "success": "Success", "warning": "Warning", "info": "Information", "confirm": "Confirm", "yes": "Yes", "no": "No", "ok": "OK", "close": "Close", "back": "Back", "next": "Next", "previous": "Previous", "submit": "Submit", "reset": "Reset", "clear": "Clear", "refresh": "Refresh", "export": "Export", "import": "Import", "download": "Download", "upload": "Upload", "copy": "Copy", "paste": "Paste", "cut": "Cut", "select_all": "Select All", "deselect_all": "Deselect All", "expand": "Expand", "collapse": "Collapse", "show": "Show", "hide": "<PERSON>de", "enable": "Enable", "disable": "Disable", "activate": "Activate", "deactivate": "Deactivate", "online": "Online", "offline": "Offline", "available": "Available", "unavailable": "Unavailable", "active": "Active", "inactive": "Inactive", "pending": "Pending", "approved": "Approved", "rejected": "Rejected", "completed": "Completed", "failed": "Failed", "processing": "Processing", "draft": "Draft", "published": "Published", "archived": "Archived"}, "navigation": {"home": "Home", "dashboard": "Dashboard", "profile": "Profile", "settings": "Settings", "admin": "Admin", "users": "Users", "roles": "Roles", "permissions": "Permissions", "applications": "Applications", "security": "Security", "audit": "Audit", "logs": "Logs", "reports": "Reports", "help": "Help", "about": "About", "contact": "Contact Us", "privacy": "Privacy Policy", "terms": "Terms of Service"}, "user": {"profile": "Profile", "account": "Account", "personal_info": "Personal Information", "contact_info": "Contact Information", "security_settings": "Security Settings", "privacy_settings": "Privacy Settings", "notification_settings": "Notification Settings", "language_settings": "Language Settings", "theme_settings": "Theme Settings", "first_name": "First Name", "last_name": "Last Name", "full_name": "Full Name", "display_name": "Display Name", "phone": "Phone", "address": "Address", "city": "City", "country": "Country", "timezone": "Timezone", "avatar": "Avatar", "bio": "Bio", "website": "Website", "social_links": "Social Links", "last_login": "Last Login", "created_at": "Created At", "updated_at": "Updated At"}, "admin": {"dashboard": "Admin Dashboard", "user_management": "User Management", "role_management": "Role Management", "permission_management": "Permission Management", "application_management": "Application Management", "system_settings": "System Settings", "security_settings": "Security Settings", "audit_logs": "<PERSON><PERSON>", "system_logs": "System Logs", "performance_monitoring": "Performance Monitoring", "backup_restore": "Backup & Restore", "maintenance": "Maintenance", "statistics": "Statistics", "reports": "Reports", "alerts": "<PERSON><PERSON><PERSON>", "notifications": "Notifications"}, "security": {"two_factor_auth": "Two-Factor Authentication", "security_keys": "Security Keys", "trusted_devices": "Trusted Devices", "login_history": "Login History", "active_sessions": "Active Sessions", "password_policy": "Password Policy", "account_lockout": "Account Lockout", "ip_whitelist": "IP Whitelist", "ip_blacklist": "IP Blacklist", "risk_assessment": "Risk Assessment", "threat_detection": "Threat Detection", "security_alerts": "Security Alerts", "compliance": "Compliance", "encryption": "Encryption", "certificates": "Certificates", "ssl_tls": "SSL/TLS"}, "application": {"client_id": "Client ID", "client_secret": "Client Secret", "redirect_uri": "Redirect URI", "scopes": "<PERSON><PERSON><PERSON>", "grant_types": "Grant Types", "response_types": "Response Types", "token_endpoint_auth_method": "Token Endpoint Auth Method", "application_type": "Application Type", "web_application": "Web Application", "native_application": "Native Application", "single_page_application": "Single Page Application", "machine_to_machine": "Machine to Machine", "public_client": "Public Client", "confidential_client": "Confidential Client"}, "validation": {"required": "This field is required", "email_invalid": "Invalid email format", "password_too_short": "Password must be at least {{min}} characters", "password_too_weak": "Password is too weak", "passwords_not_match": "Passwords do not match", "username_taken": "Username is already taken", "email_taken": "Email is already registered", "invalid_format": "Invalid format", "min_length": "Minimum {{min}} characters", "max_length": "Maximum {{max}} characters", "min_value": "Minimum value is {{min}}", "max_value": "Maximum value is {{max}}", "invalid_url": "Invalid URL format", "invalid_phone": "Invalid phone number format", "invalid_date": "Invalid date format", "file_too_large": "File size exceeds limit", "file_type_not_allowed": "File type not allowed"}, "error": {"general": "An unknown error occurred", "network": "Network connection error", "server": "Server error", "not_found": "Requested resource not found", "unauthorized": "Unauthorized access", "forbidden": "Access forbidden", "bad_request": "Bad request parameters", "timeout": "Request timeout", "rate_limit": "Rate limit exceeded", "maintenance": "System under maintenance", "service_unavailable": "Service unavailable", "database_error": "Database error", "validation_failed": "Data validation failed", "permission_denied": "Permission denied", "session_expired": "Session expired", "token_invalid": "Invalid token", "token_expired": "Token expired"}, "success": {"saved": "Saved successfully", "created": "Created successfully", "updated": "Updated successfully", "deleted": "Deleted successfully", "uploaded": "Uploaded successfully", "downloaded": "Downloaded successfully", "sent": "<PERSON><PERSON> successfully", "imported": "Imported successfully", "exported": "Exported successfully", "activated": "Activated successfully", "deactivated": "Deactivated successfully", "enabled": "Enabled successfully", "disabled": "Disabled successfully", "verified": "Verified successfully", "approved": "Approved successfully", "rejected": "Rejected successfully", "completed": "Completed successfully", "synchronized": "Synchronized successfully"}, "time": {"now": "Now", "today": "Today", "yesterday": "Yesterday", "tomorrow": "Tomorrow", "this_week": "This Week", "last_week": "Last Week", "next_week": "Next Week", "this_month": "This Month", "last_month": "Last Month", "next_month": "Next Month", "this_year": "This Year", "last_year": "Last Year", "next_year": "Next Year", "seconds_ago": {"one": "{{count}} second ago", "other": "{{count}} seconds ago"}, "minutes_ago": {"one": "{{count}} minute ago", "other": "{{count}} minutes ago"}, "hours_ago": {"one": "{{count}} hour ago", "other": "{{count}} hours ago"}, "days_ago": {"one": "{{count}} day ago", "other": "{{count}} days ago"}, "weeks_ago": {"one": "{{count}} week ago", "other": "{{count}} weeks ago"}, "months_ago": {"one": "{{count}} month ago", "other": "{{count}} months ago"}, "years_ago": {"one": "{{count}} year ago", "other": "{{count}} years ago"}}, "i18n": {"language": "Language", "language_settings": "Language Settings", "select_language": "Select Language", "current_language": "Current Language", "change_language": "Change Language", "preference_updated": "Language preference updated", "auto_detect": "Auto Detect", "system_default": "System Default", "browser_default": "<PERSON><PERSON><PERSON> De<PERSON><PERSON>", "translation_missing": "Translation Missing", "localization": "Localization", "date_format": "Date Format", "time_format": "Time Format", "number_format": "Number Format", "currency_format": "Currency Format", "timezone": "Timezone", "first_day_of_week": "First Day of Week"}}