/**
 * 完整流程集成测试
 * 
 * 功能说明：
 * 1. 测试完整的用户认证流程
 * 2. 验证零信任架构功能
 * 3. 测试国际化支持
 * 4. 验证高级分析功能
 * 5. 测试移动端SDK集成
 */

import { describe, beforeAll, afterAll, beforeEach, afterEach, it, expect } from '@jest/globals';
import request from 'supertest';
import { PrismaClient } from '@prisma/client';
import Redis from 'ioredis';
import WebSocket from 'ws';
import app from '../../src/app';
import { RiskAssessmentEngine } from '../../src/services/security/RiskAssessmentEngine';
import { RealTimeAnalytics } from '../../src/services/analytics/RealTimeAnalytics';
import { DataCollector } from '../../src/services/analytics/DataCollector';
import { I18nService } from '../../src/services/i18n/I18nService';

describe('完整流程集成测试', () => {
  let prisma: PrismaClient;
  let redis: Redis;
  let riskEngine: RiskAssessmentEngine;
  let realTimeAnalytics: RealTimeAnalytics;
  let dataCollector: DataCollector;
  let i18nService: I18nService;
  let wsClient: WebSocket;
  
  // 测试数据
  const testUser = {
    email: '<EMAIL>',
    password: 'TestPassword123!',
    name: 'Integration Test User',
    phone: '+86-13800138000'
  };

  const testDevices = {
    desktop: {
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      ip: '*************',
      country: 'CN',
      city: 'Beijing'
    },
    mobile: {
      userAgent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15',
      ip: '*************',
      country: 'CN',
      city: 'Beijing'
    },
    suspicious: {
      userAgent: 'curl/7.68.0',
      ip: '***********',
      country: 'US',
      city: 'New York'
    }
  };

  beforeAll(async () => {
    // 初始化测试环境
    prisma = new PrismaClient({
      datasources: {
        db: {
          url: process.env.TEST_DATABASE_URL || 'postgresql://test:test@localhost:5432/test_db'
        }
      }
    });
    
    redis = new Redis({
      host: process.env.TEST_REDIS_HOST || 'localhost',
      port: parseInt(process.env.TEST_REDIS_PORT || '6379'),
      db: 2 // 使用测试数据库
    });

    // 初始化服务
    riskEngine = new RiskAssessmentEngine(prisma, redis, {} as any);
    dataCollector = new DataCollector(prisma, redis);
    realTimeAnalytics = new RealTimeAnalytics(prisma, redis, {} as any);
    i18nService = new I18nService();

    // 清理测试数据
    await cleanupTestData();
    
    // 创建测试用户
    await createTestUser();

    // 初始化WebSocket连接
    wsClient = new WebSocket('ws://localhost:8080');
    await new Promise((resolve) => {
      wsClient.on('open', resolve);
    });
  });

  afterAll(async () => {
    // 清理测试数据
    await cleanupTestData();
    
    // 关闭连接
    wsClient.close();
    await prisma.$disconnect();
    await redis.quit();
  });

  beforeEach(async () => {
    // 每个测试前清理Redis缓存
    await redis.flushdb();
  });

  describe('用户认证完整流程', () => {
    it('应该完成标准登录流程', async () => {
      // 1. 用户登录
      const loginResponse = await request(app)
        .post('/api/auth/login')
        .send({
          email: testUser.email,
          password: testUser.password
        })
        .set('User-Agent', testDevices.desktop.userAgent)
        .set('X-Forwarded-For', testDevices.desktop.ip)
        .set('Accept-Language', 'zh-CN')
        .expect(200);

      expect(loginResponse.body).toHaveProperty('access_token');
      expect(loginResponse.body).toHaveProperty('refresh_token');
      expect(loginResponse.body.user.email).toBe(testUser.email);

      const accessToken = loginResponse.body.access_token;

      // 2. 获取用户信息
      const profileResponse = await request(app)
        .get('/api/user/profile')
        .set('Authorization', `Bearer ${accessToken}`)
        .set('Accept-Language', 'zh-CN')
        .expect(200);

      expect(profileResponse.body.email).toBe(testUser.email);

      // 3. 检查分析数据收集
      await new Promise(resolve => setTimeout(resolve, 1000)); // 等待异步处理

      const analyticsEvents = await prisma.userBehaviorEvents.findMany({
        where: {
          eventType: 'user_login',
          ipAddress: testDevices.desktop.ip
        }
      });

      expect(analyticsEvents.length).toBeGreaterThan(0);

      // 4. 用户登出
      await request(app)
        .post('/api/auth/logout')
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200);

      // 5. 验证令牌已失效
      await request(app)
        .get('/api/user/profile')
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(401);
    });

    it('应该触发零信任风险评估', async () => {
      // 1. 从可疑设备登录
      const response = await request(app)
        .post('/api/auth/login')
        .send({
          email: testUser.email,
          password: testUser.password
        })
        .set('User-Agent', testDevices.suspicious.userAgent)
        .set('X-Forwarded-For', testDevices.suspicious.ip)
        .set('CF-IPCountry', testDevices.suspicious.country);

      // 应该要求额外认证
      expect(response.status).toBe(202);
      expect(response.body).toHaveProperty('session_id');
      expect(response.body).toHaveProperty('required_methods');

      // 2. 检查风险评估记录
      const riskAssessments = await prisma.riskAssessment.findMany({
        where: {
          ipAddress: testDevices.suspicious.ip
        }
      });

      expect(riskAssessments.length).toBeGreaterThan(0);
      expect(riskAssessments[0].riskLevel).toMatch(/medium|high|critical/);
    });

    it('应该支持MFA认证流程', async () => {
      // 1. 触发MFA要求
      const loginResponse = await request(app)
        .post('/api/auth/login')
        .send({
          email: testUser.email,
          password: testUser.password
        })
        .set('User-Agent', testDevices.suspicious.userAgent)
        .set('X-Forwarded-For', testDevices.suspicious.ip);

      expect(loginResponse.status).toBe(202);
      const sessionId = loginResponse.body.session_id;

      // 2. 发送SMS验证码
      await request(app)
        .post('/api/auth/mfa/sms/send')
        .send({ session_id: sessionId })
        .expect(200);

      // 3. 验证MFA
      const mfaResponse = await request(app)
        .post('/api/auth/mfa/verify')
        .send({
          session_id: sessionId,
          method: 'sms',
          code: '123456' // 测试环境固定验证码
        })
        .expect(200);

      expect(mfaResponse.body).toHaveProperty('access_token');
      expect(mfaResponse.body).toHaveProperty('refresh_token');
    });
  });

  describe('国际化功能测试', () => {
    it('应该支持多语言响应', async () => {
      // 测试中文响应
      const zhResponse = await request(app)
        .post('/api/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'wrongpassword'
        })
        .set('Accept-Language', 'zh-CN')
        .expect(401);

      expect(zhResponse.body.error).toContain('认证失败');

      // 测试英文响应
      const enResponse = await request(app)
        .post('/api/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'wrongpassword'
        })
        .set('Accept-Language', 'en-US')
        .expect(401);

      expect(enResponse.body.error).toContain('Authentication failed');
    });

    it('应该支持动态语言切换', async () => {
      // 登录获取令牌
      const loginResponse = await request(app)
        .post('/api/auth/login')
        .send({
          email: testUser.email,
          password: testUser.password
        })
        .set('User-Agent', testDevices.desktop.userAgent)
        .set('X-Forwarded-For', testDevices.desktop.ip);

      const accessToken = loginResponse.body.access_token;

      // 测试不同语言的API响应
      const languages = ['zh-CN', 'en-US', 'ja-JP', 'ko-KR'];
      
      for (const lang of languages) {
        const response = await request(app)
          .get('/api/user/profile')
          .set('Authorization', `Bearer ${accessToken}`)
          .set('Accept-Language', lang)
          .expect(200);

        // 验证响应包含正确的语言标识
        expect(response.headers['content-language']).toBe(lang);
      }
    });
  });

  describe('实时分析功能测试', () => {
    it('应该实时收集和分析用户行为', async () => {
      const events: any[] = [];
      
      // 监听WebSocket事件
      wsClient.on('message', (data) => {
        const event = JSON.parse(data.toString());
        events.push(event);
      });

      // 执行一系列用户操作
      const loginResponse = await request(app)
        .post('/api/auth/login')
        .send({
          email: testUser.email,
          password: testUser.password
        })
        .set('User-Agent', testDevices.desktop.userAgent)
        .set('X-Forwarded-For', testDevices.desktop.ip);

      const accessToken = loginResponse.body.access_token;

      // 多次API调用
      for (let i = 0; i < 5; i++) {
        await request(app)
          .get('/api/user/profile')
          .set('Authorization', `Bearer ${accessToken}`)
          .set('X-Forwarded-For', testDevices.desktop.ip);
      }

      // 等待事件处理
      await new Promise(resolve => setTimeout(resolve, 2000));

      // 验证实时事件
      expect(events.length).toBeGreaterThan(0);
      
      // 检查数据库中的分析数据
      const sessionAnalytics = await prisma.sessionAnalytics.findMany({
        where: {
          ipAddress: testDevices.desktop.ip
        }
      });

      expect(sessionAnalytics.length).toBeGreaterThan(0);
      expect(sessionAnalytics[0].apiCalls).toBeGreaterThan(1);
    });

    it('应该检测异常行为并告警', async () => {
      const anomalies: any[] = [];
      
      // 监听异常检测事件
      wsClient.on('message', (data) => {
        const event = JSON.parse(data.toString());
        if (event.type === 'anomaly') {
          anomalies.push(event.data);
        }
      });

      // 模拟异常行为：短时间内大量登录尝试
      const promises = [];
      for (let i = 0; i < 15; i++) {
        promises.push(
          request(app)
            .post('/api/auth/login')
            .send({
              email: testUser.email,
              password: 'wrongpassword'
            })
            .set('X-Forwarded-For', '*************')
        );
      }

      await Promise.all(promises);

      // 等待异常检测处理
      await new Promise(resolve => setTimeout(resolve, 3000));

      // 验证异常检测
      const anomalyRecords = await prisma.anomalyDetections.findMany({
        where: {
          metricName: 'login_frequency'
        }
      });

      expect(anomalyRecords.length).toBeGreaterThan(0);
    });
  });

  describe('高级分析仪表板测试', () => {
    it('应该提供实时仪表板数据', async () => {
      // 登录获取令牌
      const loginResponse = await request(app)
        .post('/api/auth/login')
        .send({
          email: testUser.email,
          password: testUser.password
        })
        .set('User-Agent', testDevices.desktop.userAgent)
        .set('X-Forwarded-For', testDevices.desktop.ip);

      const accessToken = loginResponse.body.access_token;

      // 获取仪表板数据
      const dashboardResponse = await request(app)
        .get('/api/analytics/dashboard')
        .query({
          start_date: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
          end_date: new Date().toISOString(),
          metrics: 'users,sessions,security'
        })
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200);

      expect(dashboardResponse.body).toHaveProperty('overview');
      expect(dashboardResponse.body).toHaveProperty('user_activity');
      expect(dashboardResponse.body).toHaveProperty('risk_analysis');
      expect(dashboardResponse.body.overview).toHaveProperty('total_users');
      expect(dashboardResponse.body.overview).toHaveProperty('active_users');
    });

    it('应该支持自定义时间范围查询', async () => {
      const loginResponse = await request(app)
        .post('/api/auth/login')
        .send({
          email: testUser.email,
          password: testUser.password
        })
        .set('User-Agent', testDevices.desktop.userAgent)
        .set('X-Forwarded-For', testDevices.desktop.ip);

      const accessToken = loginResponse.body.access_token;

      // 测试不同时间范围
      const timeRanges = [
        { start: '1h', end: 'now' },
        { start: '24h', end: 'now' },
        { start: '7d', end: 'now' },
        { start: '30d', end: 'now' }
      ];

      for (const range of timeRanges) {
        const response = await request(app)
          .get('/api/analytics/dashboard')
          .query({
            start_date: range.start,
            end_date: range.end
          })
          .set('Authorization', `Bearer ${accessToken}`)
          .expect(200);

        expect(response.body).toHaveProperty('overview');
      }
    });
  });

  describe('性能和可靠性测试', () => {
    it('应该处理高并发请求', async () => {
      const concurrentRequests = 50;
      const promises = [];

      for (let i = 0; i < concurrentRequests; i++) {
        promises.push(
          request(app)
            .post('/api/auth/login')
            .send({
              email: testUser.email,
              password: testUser.password
            })
            .set('User-Agent', testDevices.desktop.userAgent)
            .set('X-Forwarded-For', `192.168.1.${100 + (i % 50)}`)
        );
      }

      const responses = await Promise.all(promises);
      
      // 大部分请求应该成功
      const successfulResponses = responses.filter(r => r.status === 200);
      expect(successfulResponses.length).toBeGreaterThan(concurrentRequests * 0.8);
    });

    it('应该在数据库连接问题时优雅降级', async () => {
      // 这个测试需要特殊的环境设置来模拟数据库故障
      // 这里只测试健康检查端点
      const response = await request(app)
        .get('/api/health')
        .expect(200);

      expect(response.body).toHaveProperty('status');
      expect(response.body).toHaveProperty('timestamp');
      expect(response.body).toHaveProperty('services');
    });
  });

  describe('安全性测试', () => {
    it('应该防止SQL注入攻击', async () => {
      const maliciousInputs = [
        "'; DROP TABLE users; --",
        "' OR '1'='1",
        "admin'/*",
        "' UNION SELECT * FROM users --"
      ];

      for (const input of maliciousInputs) {
        const response = await request(app)
          .post('/api/auth/login')
          .send({
            email: input,
            password: 'password'
          });

        // 应该返回认证失败，而不是服务器错误
        expect(response.status).toBe(401);
      }
    });

    it('应该防止XSS攻击', async () => {
      const loginResponse = await request(app)
        .post('/api/auth/login')
        .send({
          email: testUser.email,
          password: testUser.password
        })
        .set('User-Agent', testDevices.desktop.userAgent)
        .set('X-Forwarded-For', testDevices.desktop.ip);

      const accessToken = loginResponse.body.access_token;

      const xssPayload = '<script>alert("xss")</script>';
      
      const response = await request(app)
        .put('/api/user/profile')
        .send({
          name: xssPayload
        })
        .set('Authorization', `Bearer ${accessToken}`);

      // 应该拒绝包含脚本的输入
      expect(response.status).toBe(400);
    });

    it('应该实施速率限制', async () => {
      const ip = '*************';
      const promises = [];

      // 快速发送大量请求
      for (let i = 0; i < 20; i++) {
        promises.push(
          request(app)
            .post('/api/auth/login')
            .send({
              email: '<EMAIL>',
              password: 'password'
            })
            .set('X-Forwarded-For', ip)
        );
      }

      const responses = await Promise.all(promises);
      
      // 应该有一些请求被速率限制拒绝
      const rateLimitedResponses = responses.filter(r => r.status === 429);
      expect(rateLimitedResponses.length).toBeGreaterThan(0);
    });
  });

  // 辅助函数

  async function createTestUser() {
    try {
      await prisma.user.create({
        data: {
          email: testUser.email,
          passwordHash: '$2b$10$example.hash.for.testing',
          name: testUser.name,
          phone: testUser.phone,
          isActive: true,
          emailVerified: true,
          role: 'user'
        }
      });
    } catch (error) {
      // 用户可能已存在，忽略错误
    }
  }

  async function cleanupTestData() {
    try {
      // 清理测试数据
      await prisma.userBehaviorEvents.deleteMany({
        where: {
          ipAddress: {
            startsWith: '192.168.1.'
          }
        }
      });

      await prisma.sessionAnalytics.deleteMany({
        where: {
          ipAddress: {
            startsWith: '192.168.1.'
          }
        }
      });

      await prisma.riskAssessment.deleteMany({
        where: {
          ipAddress: {
            startsWith: '192.168.1.'
          }
        }
      });

      await prisma.anomalyDetections.deleteMany({
        where: {
          contextData: {
            path: ['ip'],
            string_starts_with: '192.168.1.'
          }
        }
      });

      await prisma.user.deleteMany({
        where: {
          email: testUser.email
        }
      });

      // 清理Redis数据
      await redis.flushdb();
    } catch (error) {
      console.error('清理测试数据失败:', error);
    }
  }
});
