/**
 * 身份认证流程集成测试
 * 
 * 功能说明：
 * 1. 测试完整的认证流程
 * 2. 验证零信任架构功能
 * 3. 测试风险评估和自适应认证
 * 4. 验证MFA和生物识别集成
 * 5. 测试数据收集和分析功能
 */

import { describe, beforeAll, afterAll, beforeEach, afterEach, it, expect } from '@jest/globals';
import request from 'supertest';
import { PrismaClient } from '@prisma/client';
import Redis from 'ioredis';
import app from '../../src/app';
import { DataCollector } from '../../src/services/analytics/DataCollector';
import { RiskAssessmentEngine } from '../../src/services/security/RiskAssessmentEngine';
import { AdaptiveAuthService } from '../../src/services/security/AdaptiveAuthService';
import { ThreatDetectionService } from '../../src/services/security/ThreatDetectionService';
import { MFAService } from '../../src/services/auth/MFAService';

describe('身份认证流程集成测试', () => {
  let prisma: PrismaClient;
  let redis: Redis;
  let dataCollector: DataCollector;
  let riskEngine: RiskAssessmentEngine;
  let adaptiveAuth: AdaptiveAuthService;
  let threatDetection: ThreatDetectionService;
  let mfaService: MFAService;
  
  // 测试用户数据
  const testUser = {
    email: '<EMAIL>',
    password: 'TestPassword123!',
    name: 'Test User',
    phone: '+86-13800138000'
  };
  
  // 测试设备信息
  const testDevice = {
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    ip: '*************',
    country: 'CN',
    city: 'Beijing',
    deviceType: 'desktop'
  };

  beforeAll(async () => {
    // 初始化测试环境
    prisma = new PrismaClient({
      datasources: {
        db: {
          url: process.env.TEST_DATABASE_URL || 'postgresql://test:test@localhost:5432/test_db'
        }
      }
    });
    
    redis = new Redis({
      host: process.env.TEST_REDIS_HOST || 'localhost',
      port: parseInt(process.env.TEST_REDIS_PORT || '6379'),
      db: 1 // 使用测试数据库
    });
    
    // 初始化服务
    dataCollector = new DataCollector(prisma, redis);
    riskEngine = new RiskAssessmentEngine(prisma, redis, {} as any);
    mfaService = new MFAService(prisma, redis);
    adaptiveAuth = new AdaptiveAuthService(prisma, redis, riskEngine, mfaService, {} as any);
    threatDetection = new ThreatDetectionService(prisma, redis, dataCollector);
    
    // 清理测试数据
    await cleanupTestData();
    
    // 创建测试用户
    await createTestUser();
  });

  afterAll(async () => {
    // 清理测试数据
    await cleanupTestData();
    
    // 关闭连接
    await prisma.$disconnect();
    await redis.quit();
  });

  beforeEach(async () => {
    // 每个测试前清理Redis缓存
    await redis.flushdb();
  });

  describe('基础认证流程', () => {
    it('应该成功进行用户名密码登录', async () => {
      const response = await request(app)
        .post('/api/auth/login')
        .send({
          email: testUser.email,
          password: testUser.password
        })
        .set('User-Agent', testDevice.userAgent)
        .set('X-Forwarded-For', testDevice.ip)
        .expect(200);

      expect(response.body).toHaveProperty('access_token');
      expect(response.body).toHaveProperty('refresh_token');
      expect(response.body).toHaveProperty('user');
      expect(response.body.user.email).toBe(testUser.email);
    });

    it('应该拒绝错误的密码', async () => {
      const response = await request(app)
        .post('/api/auth/login')
        .send({
          email: testUser.email,
          password: 'WrongPassword'
        })
        .set('User-Agent', testDevice.userAgent)
        .set('X-Forwarded-For', testDevice.ip)
        .expect(401);

      expect(response.body).toHaveProperty('error');
      expect(response.body.error).toContain('认证失败');
    });

    it('应该成功刷新令牌', async () => {
      // 先登录获取令牌
      const loginResponse = await request(app)
        .post('/api/auth/login')
        .send({
          email: testUser.email,
          password: testUser.password
        })
        .set('User-Agent', testDevice.userAgent)
        .set('X-Forwarded-For', testDevice.ip);

      const refreshToken = loginResponse.body.refresh_token;

      // 刷新令牌
      const refreshResponse = await request(app)
        .post('/api/auth/refresh')
        .send({
          refresh_token: refreshToken
        })
        .expect(200);

      expect(refreshResponse.body).toHaveProperty('access_token');
      expect(refreshResponse.body).toHaveProperty('refresh_token');
    });

    it('应该成功登出', async () => {
      // 先登录
      const loginResponse = await request(app)
        .post('/api/auth/login')
        .send({
          email: testUser.email,
          password: testUser.password
        })
        .set('User-Agent', testDevice.userAgent)
        .set('X-Forwarded-For', testDevice.ip);

      const accessToken = loginResponse.body.access_token;

      // 登出
      await request(app)
        .post('/api/auth/logout')
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200);

      // 验证令牌已失效
      await request(app)
        .get('/api/user/profile')
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(401);
    });
  });

  describe('风险评估和自适应认证', () => {
    it('应该对低风险登录使用基础认证', async () => {
      const response = await request(app)
        .post('/api/auth/login')
        .send({
          email: testUser.email,
          password: testUser.password
        })
        .set('User-Agent', testDevice.userAgent)
        .set('X-Forwarded-For', testDevice.ip)
        .set('CF-IPCountry', testDevice.country);

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('access_token');
    });

    it('应该对高风险登录要求MFA', async () => {
      // 模拟来自新地理位置的登录
      const response = await request(app)
        .post('/api/auth/login')
        .send({
          email: testUser.email,
          password: testUser.password
        })
        .set('User-Agent', 'curl/7.68.0') // 可疑的User-Agent
        .set('X-Forwarded-For', '***********') // 新IP
        .set('CF-IPCountry', 'US'); // 新国家

      expect(response.status).toBe(202);
      expect(response.body).toHaveProperty('session_id');
      expect(response.body).toHaveProperty('required_methods');
      expect(response.body.required_methods).toContain('sms');
    });

    it('应该阻止来自黑名单IP的登录', async () => {
      // 先将IP加入黑名单
      await redis.sadd('blocked_ips', '*************');

      const response = await request(app)
        .post('/api/auth/login')
        .send({
          email: testUser.email,
          password: testUser.password
        })
        .set('User-Agent', testDevice.userAgent)
        .set('X-Forwarded-For', '*************');

      expect(response.status).toBe(403);
      expect(response.body).toHaveProperty('error');
      expect(response.body.error).toContain('访问被阻止');
    });
  });

  describe('MFA认证流程', () => {
    let sessionId: string;

    beforeEach(async () => {
      // 触发MFA要求
      const response = await request(app)
        .post('/api/auth/login')
        .send({
          email: testUser.email,
          password: testUser.password
        })
        .set('User-Agent', 'curl/7.68.0')
        .set('X-Forwarded-For', '***********')
        .set('CF-IPCountry', 'US');

      sessionId = response.body.session_id;
    });

    it('应该成功发送SMS验证码', async () => {
      const response = await request(app)
        .post('/api/auth/mfa/sms/send')
        .send({
          session_id: sessionId
        })
        .expect(200);

      expect(response.body).toHaveProperty('message');
      expect(response.body.message).toContain('验证码已发送');
    });

    it('应该成功验证SMS验证码', async () => {
      // 先发送验证码
      await request(app)
        .post('/api/auth/mfa/sms/send')
        .send({
          session_id: sessionId
        });

      // 获取测试验证码（在测试环境中使用固定验证码）
      const testCode = '123456';

      const response = await request(app)
        .post('/api/auth/mfa/verify')
        .send({
          session_id: sessionId,
          method: 'sms',
          code: testCode
        })
        .expect(200);

      expect(response.body).toHaveProperty('access_token');
      expect(response.body).toHaveProperty('refresh_token');
    });

    it('应该拒绝错误的验证码', async () => {
      const response = await request(app)
        .post('/api/auth/mfa/verify')
        .send({
          session_id: sessionId,
          method: 'sms',
          code: '000000'
        })
        .expect(400);

      expect(response.body).toHaveProperty('error');
      expect(response.body.error).toContain('验证码错误');
    });
  });

  describe('威胁检测', () => {
    it('应该检测暴力破解攻击', async () => {
      const ip = '*************';
      
      // 模拟多次登录失败
      for (let i = 0; i < 6; i++) {
        await request(app)
          .post('/api/auth/login')
          .send({
            email: testUser.email,
            password: 'WrongPassword'
          })
          .set('X-Forwarded-For', ip);
      }

      // 等待威胁检测处理
      await new Promise(resolve => setTimeout(resolve, 1000));

      // 检查是否创建了安全事件
      const securityEvents = await prisma.securityEvent.findMany({
        where: {
          ip: ip,
          type: 'brute_force'
        }
      });

      expect(securityEvents.length).toBeGreaterThan(0);
    });

    it('应该检测异常地理位置', async () => {
      // 先从正常位置登录
      await request(app)
        .post('/api/auth/login')
        .send({
          email: testUser.email,
          password: testUser.password
        })
        .set('X-Forwarded-For', '*************')
        .set('CF-IPCountry', 'CN');

      // 立即从远程位置登录
      const response = await request(app)
        .post('/api/auth/login')
        .send({
          email: testUser.email,
          password: testUser.password
        })
        .set('X-Forwarded-For', '***********')
        .set('CF-IPCountry', 'US');

      // 应该触发额外验证
      expect(response.status).toBe(202);
      expect(response.body).toHaveProperty('session_id');
    });
  });

  describe('数据收集和分析', () => {
    it('应该收集登录事件数据', async () => {
      await request(app)
        .post('/api/auth/login')
        .send({
          email: testUser.email,
          password: testUser.password
        })
        .set('User-Agent', testDevice.userAgent)
        .set('X-Forwarded-For', testDevice.ip)
        .set('CF-IPCountry', testDevice.country);

      // 检查是否记录了分析事件
      const events = await prisma.analyticsEvent.findMany({
        where: {
          type: 'user_login',
          ip: testDevice.ip
        }
      });

      expect(events.length).toBeGreaterThan(0);
      expect(events[0].userAgent).toBe(testDevice.userAgent);
      expect(events[0].country).toBe(testDevice.country);
    });

    it('应该更新会话分析数据', async () => {
      const agent = request.agent(app);
      
      // 登录
      await agent
        .post('/api/auth/login')
        .send({
          email: testUser.email,
          password: testUser.password
        })
        .set('User-Agent', testDevice.userAgent)
        .set('X-Forwarded-For', testDevice.ip);

      // 进行一些API调用
      await agent
        .get('/api/user/profile')
        .set('X-Forwarded-For', testDevice.ip);

      await agent
        .get('/api/user/settings')
        .set('X-Forwarded-For', testDevice.ip);

      // 检查会话分析数据
      const sessionAnalytics = await prisma.sessionAnalytics.findMany({
        where: {
          ip: testDevice.ip
        }
      });

      expect(sessionAnalytics.length).toBeGreaterThan(0);
      expect(sessionAnalytics[0].apiCalls).toBeGreaterThan(1);
    });
  });

  describe('性能和可靠性', () => {
    it('应该在高并发下正常工作', async () => {
      const concurrentRequests = 10;
      const promises = [];

      for (let i = 0; i < concurrentRequests; i++) {
        promises.push(
          request(app)
            .post('/api/auth/login')
            .send({
              email: testUser.email,
              password: testUser.password
            })
            .set('User-Agent', testDevice.userAgent)
            .set('X-Forwarded-For', `192.168.1.${100 + i}`)
        );
      }

      const responses = await Promise.all(promises);
      
      // 所有请求都应该成功
      responses.forEach(response => {
        expect(response.status).toBe(200);
        expect(response.body).toHaveProperty('access_token');
      });
    });

    it('应该正确处理数据库连接错误', async () => {
      // 模拟数据库错误（这需要特殊的测试设置）
      // 这里只是示例，实际实现需要mock数据库连接
      
      // 验证系统能够优雅地处理错误
      const response = await request(app)
        .get('/api/health')
        .expect(200);

      expect(response.body).toHaveProperty('status');
    });
  });

  // 辅助函数

  async function createTestUser() {
    try {
      await prisma.user.create({
        data: {
          email: testUser.email,
          passwordHash: '$2b$10$example.hash.for.testing', // 实际应该使用bcrypt
          name: testUser.name,
          phone: testUser.phone,
          isActive: true,
          emailVerified: true,
          role: 'user'
        }
      });
    } catch (error) {
      // 用户可能已存在，忽略错误
    }
  }

  async function cleanupTestData() {
    try {
      // 清理测试数据
      await prisma.analyticsEvent.deleteMany({
        where: {
          ip: {
            startsWith: '192.168.1.'
          }
        }
      });

      await prisma.securityEvent.deleteMany({
        where: {
          ip: {
            startsWith: '192.168.1.'
          }
        }
      });

      await prisma.sessionAnalytics.deleteMany({
        where: {
          ip: {
            startsWith: '192.168.1.'
          }
        }
      });

      await prisma.user.deleteMany({
        where: {
          email: testUser.email
        }
      });

      // 清理Redis数据
      await redis.flushdb();
    } catch (error) {
      console.error('清理测试数据失败:', error);
    }
  }
});
