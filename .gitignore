# 依赖
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 构建输出
dist/
build/

# 环境变量
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 日志
logs/
*.log

# 运行时数据
pids/
*.pid
*.seed
*.pid.lock

# 覆盖率报告
coverage/
.nyc_output/

# 依赖锁定文件
package-lock.json
yarn.lock

# IDE 配置
.vscode/
.idea/
*.swp
*.swo
*~

# 操作系统生成的文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 临时文件
tmp/
temp/

# 数据库
*.sqlite
*.db

# 证书和密钥
*.pem
*.key
*.crt
certs/

# 测试
test-results/
playwright-report/

# Prisma
prisma/migrations/
!prisma/migrations/.gitkeep
