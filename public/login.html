<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>身份提供商 - 管理员登录</title>
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'%3E%3Ctext y='.9em' font-size='90'%3E🔐%3C/text%3E%3C/svg%3E">
    
    <!-- 引入Tailwind CSS -->
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        .login-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .login-card {
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.95);
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        
        .input-focus:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            min-width: 300px;
            animation: slideInRight 0.3s ease-out;
        }
        
        @keyframes slideInRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }
        
        .loading-spinner {
            border: 2px solid #f3f3f3;
            border-top: 2px solid #667eea;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body class="login-bg min-h-screen flex items-center justify-center p-4">
    <!-- 通知容器 -->
    <div id="notification-container"></div>

    <!-- 登录表单 -->
    <div class="login-card w-full max-w-md p-8 rounded-2xl shadow-2xl">
        <!-- Logo和标题 -->
        <div class="text-center mb-8">
            <div class="w-16 h-16 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
                <i class="fas fa-shield-alt text-white text-2xl"></i>
            </div>
            <h1 class="text-2xl font-bold text-gray-900 mb-2">管理员登录</h1>
            <p class="text-gray-600">请使用管理员账户登录控制台</p>
        </div>

        <!-- 登录表单 -->
        <form id="login-form" class="space-y-6">
            <div>
                <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
                    <i class="fas fa-envelope mr-2"></i>邮箱地址
                </label>
                <input type="email" 
                       id="email" 
                       name="email" 
                       required 
                       class="input-focus w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none transition-all"
                       placeholder="请输入管理员邮箱">
            </div>

            <div>
                <label for="password" class="block text-sm font-medium text-gray-700 mb-2">
                    <i class="fas fa-lock mr-2"></i>密码
                </label>
                <div class="relative">
                    <input type="password" 
                           id="password" 
                           name="password" 
                           required 
                           class="input-focus w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none transition-all pr-12"
                           placeholder="请输入密码">
                    <button type="button" 
                            id="toggle-password" 
                            class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700">
                        <i class="fas fa-eye"></i>
                    </button>
                </div>
            </div>

            <div class="flex items-center justify-between">
                <label class="flex items-center">
                    <input type="checkbox" 
                           id="remember" 
                           name="remember" 
                           class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                    <span class="ml-2 text-sm text-gray-600">记住我</span>
                </label>
                <a href="#" class="text-sm text-indigo-600 hover:text-indigo-500">忘记密码？</a>
            </div>

            <button type="submit" 
                    id="login-btn"
                    class="btn-primary w-full py-3 px-4 text-white font-medium rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2">
                <span id="login-text">登录</span>
                <div id="login-spinner" class="loading-spinner mx-auto" style="display: none;"></div>
            </button>
        </form>

        <!-- 其他登录方式 -->
        <div class="mt-8">
            <div class="relative">
                <div class="absolute inset-0 flex items-center">
                    <div class="w-full border-t border-gray-300"></div>
                </div>
                <div class="relative flex justify-center text-sm">
                    <span class="px-2 bg-white text-gray-500">或者</span>
                </div>
            </div>

            <div class="mt-6 grid grid-cols-2 gap-3">
                <button class="w-full inline-flex justify-center py-2 px-4 border border-gray-300 rounded-lg shadow-sm bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                    <i class="fab fa-google text-red-500"></i>
                    <span class="ml-2">Google</span>
                </button>
                <button class="w-full inline-flex justify-center py-2 px-4 border border-gray-300 rounded-lg shadow-sm bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                    <i class="fab fa-microsoft text-blue-500"></i>
                    <span class="ml-2">Microsoft</span>
                </button>
            </div>
        </div>

        <!-- 底部链接 -->
        <div class="mt-8 text-center">
            <p class="text-sm text-gray-600">
                还没有账户？
                <a href="#" class="text-indigo-600 hover:text-indigo-500 font-medium">联系管理员</a>
            </p>
        </div>
    </div>

    <script>
        // 工具函数
        function showNotification(message, type = 'info', duration = 3000) {
            const container = document.getElementById('notification-container');
            const id = 'notification-' + Date.now();
            
            const typeClasses = {
                success: 'bg-green-500 text-white',
                error: 'bg-red-500 text-white',
                warning: 'bg-yellow-500 text-white',
                info: 'bg-blue-500 text-white'
            };

            const typeIcons = {
                success: 'fas fa-check-circle',
                error: 'fas fa-exclamation-circle',
                warning: 'fas fa-exclamation-triangle',
                info: 'fas fa-info-circle'
            };

            const notification = document.createElement('div');
            notification.id = id;
            notification.className = `notification ${typeClasses[type]} p-4 rounded-lg shadow-lg flex items-center space-x-3`;
            notification.innerHTML = `
                <i class="${typeIcons[type]}"></i>
                <span class="flex-1">${message}</span>
                <button onclick="closeNotification('${id}')" class="text-white hover:text-gray-200">
                    <i class="fas fa-times"></i>
                </button>
            `;

            container.appendChild(notification);

            if (duration > 0) {
                setTimeout(() => {
                    closeNotification(id);
                }, duration);
            }
        }

        function closeNotification(id) {
            const notification = document.getElementById(id);
            if (notification) {
                notification.style.animation = 'slideOutRight 0.3s ease-in';
                setTimeout(() => {
                    notification.remove();
                }, 300);
            }
        }

        // 密码显示/隐藏切换
        document.getElementById('toggle-password').onclick = function() {
            const passwordInput = document.getElementById('password');
            const icon = this.querySelector('i');
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                icon.className = 'fas fa-eye-slash';
            } else {
                passwordInput.type = 'password';
                icon.className = 'fas fa-eye';
            }
        };

        // 登录表单提交
        document.getElementById('login-form').onsubmit = async function(e) {
            e.preventDefault();
            
            const loginBtn = document.getElementById('login-btn');
            const loginText = document.getElementById('login-text');
            const loginSpinner = document.getElementById('login-spinner');
            
            // 显示加载状态
            loginText.style.display = 'none';
            loginSpinner.style.display = 'block';
            loginBtn.disabled = true;
            
            try {
                const formData = new FormData(e.target);
                const email = formData.get('email');
                const password = formData.get('password');
                
                // 发送登录请求
                const response = await fetch('/api/v1/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: email,
                        password: password
                    })
                });
                
                const data = await response.json();
                
                if (response.ok && data.accessToken) {
                    // 保存令牌
                    localStorage.setItem('admin_token', data.accessToken);
                    
                    showNotification('登录成功，正在跳转...', 'success');
                    
                    // 跳转到管理员控制台
                    setTimeout(() => {
                        const redirect = new URLSearchParams(window.location.search).get('redirect');
                        window.location.href = redirect || '/admin';
                    }, 1000);
                } else {
                    throw new Error(data.message || '登录失败');
                }
                
            } catch (error) {
                console.error('登录失败:', error);
                showNotification('登录失败: ' + error.message, 'error');
            } finally {
                // 恢复按钮状态
                loginText.style.display = 'block';
                loginSpinner.style.display = 'none';
                loginBtn.disabled = false;
            }
        };

        // 检查是否已经登录
        const token = localStorage.getItem('admin_token');
        if (token) {
            // 验证令牌有效性
            fetch('/api/v1/admin/stats', {
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            }).then(response => {
                if (response.ok) {
                    // 令牌有效，直接跳转
                    const redirect = new URLSearchParams(window.location.search).get('redirect');
                    window.location.href = redirect || '/admin';
                }
            }).catch(() => {
                // 令牌无效，清除
                localStorage.removeItem('admin_token');
            });
        }
    </script>
</body>
</html>
