/**
 * UI组件模块
 * 提供可复用的UI组件
 */

window.Components = {
    /**
     * 显示通知
     * @param {string} message - 消息内容
     * @param {string} type - 通知类型 (success, error, warning, info)
     * @param {number} duration - 显示时长（毫秒）
     */
    showNotification(message, type = 'info', duration = 3000) {
        const container = document.getElementById('notification-container');
        const id = Utils.generateUUID();
        
        const typeClasses = {
            success: 'bg-green-500 text-white',
            error: 'bg-red-500 text-white',
            warning: 'bg-yellow-500 text-white',
            info: 'bg-blue-500 text-white'
        };

        const typeIcons = {
            success: 'fas fa-check-circle',
            error: 'fas fa-exclamation-circle',
            warning: 'fas fa-exclamation-triangle',
            info: 'fas fa-info-circle'
        };

        const notification = document.createElement('div');
        notification.id = id;
        notification.className = `notification ${typeClasses[type]} p-4 rounded-lg shadow-lg flex items-center space-x-3`;
        notification.innerHTML = `
            <i class="${typeIcons[type]}"></i>
            <span class="flex-1">${message}</span>
            <button onclick="Components.closeNotification('${id}')" class="text-white hover:text-gray-200">
                <i class="fas fa-times"></i>
            </button>
        `;

        container.appendChild(notification);

        // 自动关闭
        if (duration > 0) {
            setTimeout(() => {
                this.closeNotification(id);
            }, duration);
        }
    },

    /**
     * 关闭通知
     * @param {string} id - 通知ID
     */
    closeNotification(id) {
        const notification = document.getElementById(id);
        if (notification) {
            notification.style.animation = 'slideOutRight 0.3s ease-in';
            setTimeout(() => {
                notification.remove();
            }, 300);
        }
    },

    /**
     * 显示确认对话框
     * @param {string} title - 标题
     * @param {string} message - 消息
     * @param {Function} onConfirm - 确认回调
     * @param {Function} onCancel - 取消回调
     */
    showConfirmDialog(title, message, onConfirm, onCancel) {
        const modal = this.createModal({
            title,
            content: `
                <div class="text-center">
                    <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4">
                        <i class="fas fa-exclamation-triangle text-red-600"></i>
                    </div>
                    <p class="text-gray-600 mb-6">${message}</p>
                    <div class="flex justify-center space-x-4">
                        <button id="confirm-btn" class="btn-primary px-6 py-2 bg-red-500 hover:bg-red-600 text-white rounded-lg">
                            确认
                        </button>
                        <button id="cancel-btn" class="px-6 py-2 bg-gray-300 hover:bg-gray-400 text-gray-700 rounded-lg">
                            取消
                        </button>
                    </div>
                </div>
            `,
            showCloseButton: false
        });

        document.getElementById('confirm-btn').onclick = () => {
            this.closeModal();
            if (onConfirm) onConfirm();
        };

        document.getElementById('cancel-btn').onclick = () => {
            this.closeModal();
            if (onCancel) onCancel();
        };
    },

    /**
     * 创建模态框
     * @param {Object} options - 模态框选项
     */
    createModal(options = {}) {
        const {
            title = '模态框',
            content = '',
            size = 'md',
            showCloseButton = true,
            onClose = null
        } = options;

        const sizeClasses = {
            sm: 'max-w-md',
            md: 'max-w-lg',
            lg: 'max-w-2xl',
            xl: 'max-w-4xl',
            full: 'max-w-full mx-4'
        };

        const modalHTML = `
            <div class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
                <div class="modal-enter bg-white rounded-lg shadow-xl ${sizeClasses[size]} w-full max-h-screen overflow-y-auto">
                    <div class="flex items-center justify-between p-6 border-b border-gray-200">
                        <h3 class="text-lg font-semibold text-gray-900">${title}</h3>
                        ${showCloseButton ? `
                            <button onclick="Components.closeModal()" class="text-gray-400 hover:text-gray-600">
                                <i class="fas fa-times"></i>
                            </button>
                        ` : ''}
                    </div>
                    <div class="p-6">
                        ${content}
                    </div>
                </div>
            </div>
        `;

        const container = document.getElementById('modal-container');
        container.innerHTML = modalHTML;

        // 点击背景关闭
        container.querySelector('.fixed').onclick = (e) => {
            if (e.target === e.currentTarget) {
                this.closeModal();
                if (onClose) onClose();
            }
        };

        return container;
    },

    /**
     * 关闭模态框
     */
    closeModal() {
        const container = document.getElementById('modal-container');
        container.innerHTML = '';
    },

    /**
     * 创建加载指示器
     * @param {string} message - 加载消息
     */
    showLoading(message = '加载中...') {
        const modal = this.createModal({
            title: '',
            content: `
                <div class="text-center py-8">
                    <div class="loading-spinner mx-auto mb-4"></div>
                    <p class="text-gray-600">${message}</p>
                </div>
            `,
            showCloseButton: false
        });
        return modal;
    },

    /**
     * 创建数据表格
     * @param {Object} options - 表格选项
     */
    createTable(options = {}) {
        const {
            columns = [],
            data = [],
            pagination = null,
            onRowClick = null,
            className = ''
        } = options;

        let tableHTML = `
            <div class="overflow-x-auto">
                <table class="min-w-full table-striped ${className}">
                    <thead class="bg-gray-50">
                        <tr>
        `;

        // 生成表头
        columns.forEach(column => {
            tableHTML += `
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    ${column.title}
                </th>
            `;
        });

        tableHTML += `
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
        `;

        // 生成表格数据
        data.forEach((row, index) => {
            const rowClass = onRowClick ? 'cursor-pointer hover:bg-gray-50' : '';
            const rowClickHandler = onRowClick ? `onclick="(${onRowClick.toString()})(${JSON.stringify(row).replace(/"/g, '&quot;')}, ${index})"` : '';
            
            tableHTML += `<tr class="${rowClass}" ${rowClickHandler}>`;
            
            columns.forEach(column => {
                let cellContent = '';
                
                if (column.render) {
                    cellContent = column.render(row[column.key], row, index);
                } else {
                    cellContent = row[column.key] || '-';
                }
                
                tableHTML += `
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        ${cellContent}
                    </td>
                `;
            });
            
            tableHTML += '</tr>';
        });

        tableHTML += `
                    </tbody>
                </table>
            </div>
        `;

        // 添加分页
        if (pagination) {
            tableHTML += this.createPagination(pagination);
        }

        return tableHTML;
    },

    /**
     * 创建分页组件
     * @param {Object} pagination - 分页信息
     */
    createPagination(pagination) {
        const { current, total, pageSize, onChange } = pagination;
        const totalPages = Math.ceil(total / pageSize);
        
        if (totalPages <= 1) return '';

        let paginationHTML = `
            <div class="flex items-center justify-between px-6 py-3 bg-white border-t border-gray-200">
                <div class="flex-1 flex justify-between sm:hidden">
        `;

        // 移动端分页
        if (current > 1) {
            paginationHTML += `
                <button onclick="(${onChange.toString()})(${current - 1})" 
                        class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    上一页
                </button>
            `;
        }

        if (current < totalPages) {
            paginationHTML += `
                <button onclick="(${onChange.toString()})(${current + 1})" 
                        class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    下一页
                </button>
            `;
        }

        paginationHTML += `
                </div>
                <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                    <div>
                        <p class="text-sm text-gray-700">
                            显示第 <span class="font-medium">${(current - 1) * pageSize + 1}</span> 到 
                            <span class="font-medium">${Math.min(current * pageSize, total)}</span> 条，
                            共 <span class="font-medium">${total}</span> 条记录
                        </p>
                    </div>
                    <div>
                        <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
        `;

        // 桌面端分页
        const startPage = Math.max(1, current - 2);
        const endPage = Math.min(totalPages, current + 2);

        // 上一页按钮
        if (current > 1) {
            paginationHTML += `
                <button onclick="(${onChange.toString()})(${current - 1})" 
                        class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                    <i class="fas fa-chevron-left"></i>
                </button>
            `;
        }

        // 页码按钮
        for (let i = startPage; i <= endPage; i++) {
            const isActive = i === current;
            const activeClass = isActive 
                ? 'bg-indigo-50 border-indigo-500 text-indigo-600' 
                : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50';
            
            paginationHTML += `
                <button onclick="(${onChange.toString()})(${i})" 
                        class="relative inline-flex items-center px-4 py-2 border text-sm font-medium ${activeClass}">
                    ${i}
                </button>
            `;
        }

        // 下一页按钮
        if (current < totalPages) {
            paginationHTML += `
                <button onclick="(${onChange.toString()})(${current + 1})" 
                        class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                    <i class="fas fa-chevron-right"></i>
                </button>
            `;
        }

        paginationHTML += `
                        </nav>
                    </div>
                </div>
            </div>
        `;

        return paginationHTML;
    },

    /**
     * 创建状态标签
     * @param {string} status - 状态值
     * @param {Object} statusMap - 状态映射
     */
    createStatusTag(status, statusMap = {}) {
        const defaultMap = {
            active: { text: '活跃', class: 'tag-success' },
            inactive: { text: '非活跃', class: 'tag-warning' },
            disabled: { text: '禁用', class: 'tag-danger' },
            pending: { text: '待处理', class: 'tag-info' }
        };

        const map = { ...defaultMap, ...statusMap };
        const config = map[status] || { text: status, class: 'tag-info' };

        return `<span class="tag ${config.class}">${config.text}</span>`;
    },

    /**
     * 创建操作按钮组
     * @param {Array} actions - 操作列表
     */
    createActionButtons(actions = []) {
        return actions.map(action => {
            const {
                text,
                icon,
                onClick,
                className = 'text-indigo-600 hover:text-indigo-900',
                tooltip
            } = action;

            const tooltipAttr = tooltip ? `data-tooltip="${tooltip}"` : '';
            const iconHTML = icon ? `<i class="${icon} mr-1"></i>` : '';

            return `
                <button onclick="${onClick}" 
                        class="tooltip ${className} text-sm font-medium" 
                        ${tooltipAttr}>
                    ${iconHTML}${text}
                </button>
            `;
        }).join('<span class="mx-2 text-gray-300">|</span>');
    },

    /**
     * 创建搜索框
     * @param {Object} options - 搜索框选项
     */
    createSearchBox(options = {}) {
        const {
            placeholder = '搜索...',
            onSearch,
            className = ''
        } = options;

        const searchId = Utils.generateUUID();

        return `
            <div class="search-box ${className}">
                <input type="text" 
                       id="${searchId}"
                       placeholder="${placeholder}"
                       class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                       onkeyup="if(event.key==='Enter') (${onSearch.toString()})(this.value)">
                <i class="search-icon fas fa-search"></i>
            </div>
        `;
    }
};
