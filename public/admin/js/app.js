/**
 * 主应用程序
 * 管理应用程序的初始化、路由和状态
 */

window.App = {
    // 当前页面
    currentPage: 'dashboard',
    
    // 用户信息
    user: null,

    // 侧边栏菜单配置
    menuItems: [
        {
            id: 'dashboard',
            title: '仪表板',
            icon: 'fas fa-tachometer-alt',
            page: 'dashboard'
        },
        {
            id: 'users',
            title: '用户管理',
            icon: 'fas fa-users',
            page: 'users'
        },
        {
            id: 'oauth-clients',
            title: 'OAuth客户端',
            icon: 'fas fa-key',
            page: 'oauth-clients'
        },
        {
            id: 'saml-providers',
            title: 'SAML提供商',
            icon: 'fas fa-shield-alt',
            page: 'saml-providers'
        },
        {
            id: 'audit-logs',
            title: '审计日志',
            icon: 'fas fa-clipboard-list',
            page: 'audit-logs'
        },
        {
            id: 'system-config',
            title: '系统配置',
            icon: 'fas fa-cog',
            page: 'system-config'
        }
    ],

    /**
     * 初始化应用程序
     */
    async init() {
        try {
            // 检查认证状态
            if (!API.token) {
                this.redirectToLogin();
                return;
            }

            // 验证令牌有效性
            try {
                await API.getSystemStats();
            } catch (error) {
                if (error.message.includes('认证失败')) {
                    this.redirectToLogin();
                    return;
                }
            }

            // 渲染界面
            this.renderSidebar();
            this.renderHeader();
            
            // 加载初始页面
            const initialPage = Utils.getQueryParam('page') || 'dashboard';
            await this.loadPage(initialPage);

            // 隐藏加载器，显示应用
            document.getElementById('page-loader').style.display = 'none';
            document.getElementById('app').style.display = 'flex';

            // 绑定事件
            this.bindEvents();

        } catch (error) {
            console.error('应用初始化失败:', error);
            this.showError('应用初始化失败，请刷新页面重试');
        }
    },

    /**
     * 重定向到登录页面
     */
    redirectToLogin() {
        window.location.href = '/login?redirect=' + encodeURIComponent(window.location.pathname);
    },

    /**
     * 渲染侧边栏
     */
    renderSidebar() {
        const sidebar = document.getElementById('sidebar');
        
        let menuHTML = `
            <div class="flex flex-col h-full">
                <!-- Logo -->
                <div class="flex items-center px-6 py-4 border-b border-gray-200">
                    <div class="flex items-center">
                        <div class="w-8 h-8 bg-indigo-600 rounded-lg flex items-center justify-center mr-3">
                            <i class="fas fa-shield-alt text-white"></i>
                        </div>
                        <div>
                            <h2 class="text-lg font-semibold text-gray-900">身份提供商</h2>
                            <p class="text-xs text-gray-500">管理控制台</p>
                        </div>
                    </div>
                </div>

                <!-- 导航菜单 -->
                <nav class="flex-1 px-4 py-6 space-y-2">
        `;

        this.menuItems.forEach(item => {
            const isActive = this.currentPage === item.page;
            const activeClass = isActive ? 'nav-active' : 'text-gray-700 hover:bg-gray-100';
            
            menuHTML += `
                <a href="#" onclick="App.loadPage('${item.page}')" 
                   class="flex items-center px-3 py-2 rounded-lg transition-colors ${activeClass}">
                    <i class="${item.icon} mr-3"></i>
                    <span>${item.title}</span>
                </a>
            `;
        });

        menuHTML += `
                </nav>

                <!-- 底部信息 -->
                <div class="px-4 py-4 border-t border-gray-200">
                    <div class="flex items-center text-sm text-gray-600">
                        <i class="fas fa-info-circle mr-2"></i>
                        <span>版本 1.0.0</span>
                    </div>
                </div>
            </div>
        `;

        sidebar.innerHTML = menuHTML;
    },

    /**
     * 渲染头部
     */
    renderHeader() {
        const header = document.getElementById('header');
        
        header.innerHTML = `
            <div class="flex items-center justify-between w-full">
                <!-- 移动端菜单按钮 -->
                <button id="mobile-menu-btn" class="md:hidden text-gray-600 hover:text-gray-900">
                    <i class="fas fa-bars text-xl"></i>
                </button>

                <!-- 面包屑导航 -->
                <div class="hidden md:flex items-center space-x-2 text-sm text-gray-600">
                    <i class="fas fa-home"></i>
                    <span class="mx-2">/</span>
                    <span class="text-gray-900 font-medium">${this.getCurrentPageTitle()}</span>
                </div>

                <!-- 右侧操作 -->
                <div class="flex items-center space-x-4">
                    <!-- 通知 -->
                    <button class="relative text-gray-600 hover:text-gray-900">
                        <i class="fas fa-bell text-xl"></i>
                        <span class="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full"></span>
                    </button>

                    <!-- 用户菜单 -->
                    <div class="relative">
                        <button id="user-menu-btn" class="flex items-center space-x-2 text-gray-700 hover:text-gray-900">
                            <div class="w-8 h-8 bg-indigo-100 rounded-full flex items-center justify-center">
                                <i class="fas fa-user text-indigo-600"></i>
                            </div>
                            <span class="hidden md:block">管理员</span>
                            <i class="fas fa-chevron-down text-sm"></i>
                        </button>
                        
                        <!-- 下拉菜单 -->
                        <div id="user-menu" class="hidden absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 z-50">
                            <div class="py-2">
                                <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                    <i class="fas fa-user mr-2"></i>个人资料
                                </a>
                                <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                    <i class="fas fa-cog mr-2"></i>设置
                                </a>
                                <hr class="my-2">
                                <a href="#" onclick="App.logout()" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                    <i class="fas fa-sign-out-alt mr-2"></i>退出登录
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    },

    /**
     * 获取当前页面标题
     */
    getCurrentPageTitle() {
        const menuItem = this.menuItems.find(item => item.page === this.currentPage);
        return menuItem ? menuItem.title : '未知页面';
    },

    /**
     * 加载页面
     */
    async loadPage(pageName) {
        try {
            this.currentPage = pageName;
            
            // 更新URL
            Utils.setQueryParam('page', pageName);
            
            // 更新侧边栏激活状态
            this.renderSidebar();
            
            // 更新头部
            this.renderHeader();
            
            // 显示加载状态
            const mainContent = document.getElementById('main-content');
            mainContent.innerHTML = `
                <div class="flex items-center justify-center py-12">
                    <div class="loading-spinner mr-3"></div>
                    <span class="text-gray-600">加载中...</span>
                </div>
            `;

            // 加载页面内容
            let content = '';
            
            switch (pageName) {
                case 'dashboard':
                    content = await Pages.dashboard.render();
                    break;
                case 'users':
                    content = await Pages.users.render();
                    break;
                case 'oauth-clients':
                    content = await Pages.oauthClients.render();
                    break;
                case 'saml-providers':
                    content = '<div class="text-center py-12">SAML提供商管理页面开发中...</div>';
                    break;
                case 'audit-logs':
                    content = '<div class="text-center py-12">审计日志页面开发中...</div>';
                    break;
                case 'system-config':
                    content = '<div class="text-center py-12">系统配置页面开发中...</div>';
                    break;
                default:
                    content = '<div class="text-center py-12">页面不存在</div>';
            }

            mainContent.innerHTML = content;

        } catch (error) {
            console.error('加载页面失败:', error);
            Components.showNotification('页面加载失败', 'error');
        }
    },

    /**
     * 绑定事件
     */
    bindEvents() {
        // 移动端菜单切换
        const mobileMenuBtn = document.getElementById('mobile-menu-btn');
        const sidebar = document.getElementById('sidebar');
        
        if (mobileMenuBtn) {
            mobileMenuBtn.onclick = () => {
                sidebar.classList.toggle('open');
            };
        }

        // 用户菜单切换
        const userMenuBtn = document.getElementById('user-menu-btn');
        const userMenu = document.getElementById('user-menu');
        
        if (userMenuBtn && userMenu) {
            userMenuBtn.onclick = (e) => {
                e.stopPropagation();
                userMenu.classList.toggle('hidden');
            };

            // 点击外部关闭菜单
            document.onclick = () => {
                userMenu.classList.add('hidden');
            };
        }

        // 键盘快捷键
        document.onkeydown = (e) => {
            // Ctrl/Cmd + K 打开搜索
            if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
                e.preventDefault();
                // TODO: 实现全局搜索
            }
        };

        // 窗口大小变化时关闭移动端菜单
        window.onresize = () => {
            if (window.innerWidth >= 768) {
                sidebar.classList.remove('open');
            }
        };
    },

    /**
     * 登出
     */
    async logout() {
        try {
            await API.logout();
            Components.showNotification('已成功退出登录', 'success');
            setTimeout(() => {
                this.redirectToLogin();
            }, 1000);
        } catch (error) {
            console.error('登出失败:', error);
            // 即使登出请求失败，也清除本地令牌
            API.clearToken();
            this.redirectToLogin();
        }
    },

    /**
     * 显示错误信息
     */
    showError(message) {
        document.getElementById('page-loader').innerHTML = `
            <div class="text-center">
                <i class="fas fa-exclamation-triangle text-red-500 text-4xl mb-4"></i>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">出现错误</h3>
                <p class="text-gray-600 mb-4">${message}</p>
                <button onclick="window.location.reload()" 
                        class="btn-primary px-4 py-2 bg-indigo-600 hover:bg-indigo-700 text-white rounded-lg">
                    刷新页面
                </button>
            </div>
        `;
    }
};

// 应用程序启动
document.addEventListener('DOMContentLoaded', () => {
    App.init();
});
