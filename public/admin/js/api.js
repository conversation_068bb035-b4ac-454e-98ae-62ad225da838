/**
 * API接口模块
 * 提供与后端API的交互功能
 */

window.API = {
    // API基础URL
    baseURL: '/api/v1',
    
    // 认证令牌
    token: Utils.storage.get('admin_token'),

    /**
     * 设置认证令牌
     * @param {string} token - JWT令牌
     */
    setToken(token) {
        this.token = token;
        Utils.storage.set('admin_token', token);
    },

    /**
     * 清除认证令牌
     */
    clearToken() {
        this.token = null;
        Utils.storage.remove('admin_token');
    },

    /**
     * 发送HTTP请求
     * @param {string} method - HTTP方法
     * @param {string} url - 请求URL
     * @param {Object} options - 请求选项
     * @returns {Promise} 请求结果
     */
    async request(method, url, options = {}) {
        const config = {
            method: method.toUpperCase(),
            headers: {
                'Content-Type': 'application/json',
                ...options.headers
            },
            ...options
        };

        // 添加认证头
        if (this.token) {
            config.headers.Authorization = `Bearer ${this.token}`;
        }

        // 处理请求体
        if (config.body && typeof config.body === 'object') {
            config.body = JSON.stringify(config.body);
        }

        try {
            const response = await fetch(`${this.baseURL}${url}`, config);
            
            // 处理认证失败
            if (response.status === 401) {
                this.clearToken();
                window.location.href = '/login';
                throw new Error('认证失败，请重新登录');
            }

            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.message || `HTTP ${response.status}`);
            }

            return data;
        } catch (error) {
            console.error('API请求失败:', error);
            throw error;
        }
    },

    /**
     * GET请求
     */
    get(url, options = {}) {
        return this.request('GET', url, options);
    },

    /**
     * POST请求
     */
    post(url, data, options = {}) {
        return this.request('POST', url, { ...options, body: data });
    },

    /**
     * PUT请求
     */
    put(url, data, options = {}) {
        return this.request('PUT', url, { ...options, body: data });
    },

    /**
     * DELETE请求
     */
    delete(url, options = {}) {
        return this.request('DELETE', url, options);
    },

    // ==================== 认证相关 ====================

    /**
     * 管理员登录
     * @param {string} email - 邮箱
     * @param {string} password - 密码
     * @returns {Promise} 登录结果
     */
    async login(email, password) {
        const response = await this.post('/auth/login', { 
            username: email, 
            password 
        });
        
        if (response.accessToken) {
            this.setToken(response.accessToken);
        }
        
        return response;
    },

    /**
     * 登出
     */
    async logout() {
        try {
            await this.post('/auth/logout');
        } catch (error) {
            console.error('登出请求失败:', error);
        } finally {
            this.clearToken();
        }
    },

    // ==================== 系统统计 ====================

    /**
     * 获取系统统计信息
     */
    getSystemStats() {
        return this.get('/admin/stats');
    },

    /**
     * 获取系统健康状态
     */
    getSystemHealth() {
        return this.get('/admin/health');
    },

    // ==================== 用户管理 ====================

    /**
     * 获取用户列表
     * @param {Object} params - 查询参数
     */
    getUsers(params = {}) {
        const query = new URLSearchParams(params).toString();
        return this.get(`/admin/users${query ? '?' + query : ''}`);
    },

    /**
     * 获取用户详情
     * @param {string} userId - 用户ID
     */
    getUser(userId) {
        return this.get(`/admin/users/${userId}`);
    },

    /**
     * 创建用户
     * @param {Object} userData - 用户数据
     */
    createUser(userData) {
        return this.post('/admin/users', userData);
    },

    /**
     * 更新用户
     * @param {string} userId - 用户ID
     * @param {Object} userData - 用户数据
     */
    updateUser(userId, userData) {
        return this.put(`/admin/users/${userId}`, userData);
    },

    /**
     * 删除用户
     * @param {string} userId - 用户ID
     */
    deleteUser(userId) {
        return this.delete(`/admin/users/${userId}`);
    },

    // ==================== OAuth客户端管理 ====================

    /**
     * 获取OAuth客户端列表
     * @param {Object} params - 查询参数
     */
    getOAuthClients(params = {}) {
        const query = new URLSearchParams(params).toString();
        return this.get(`/admin/oauth-clients${query ? '?' + query : ''}`);
    },

    /**
     * 创建OAuth客户端
     * @param {Object} clientData - 客户端数据
     */
    createOAuthClient(clientData) {
        return this.post('/admin/oauth-clients', clientData);
    },

    /**
     * 更新OAuth客户端
     * @param {string} clientId - 客户端ID
     * @param {Object} clientData - 客户端数据
     */
    updateOAuthClient(clientId, clientData) {
        return this.put(`/admin/oauth-clients/${clientId}`, clientData);
    },

    /**
     * 删除OAuth客户端
     * @param {string} clientId - 客户端ID
     */
    deleteOAuthClient(clientId) {
        return this.delete(`/admin/oauth-clients/${clientId}`);
    },

    /**
     * 重新生成客户端密钥
     * @param {string} clientId - 客户端ID
     */
    regenerateClientSecret(clientId) {
        return this.post(`/admin/oauth-clients/${clientId}/regenerate-secret`);
    },

    // ==================== SAML服务提供商管理 ====================

    /**
     * 获取SAML SP列表
     * @param {Object} params - 查询参数
     */
    getSAMLProviders(params = {}) {
        const query = new URLSearchParams(params).toString();
        return this.get(`/saml/sp${query ? '?' + query : ''}`);
    },

    /**
     * 创建SAML SP
     * @param {Object} spData - SP数据
     */
    createSAMLProvider(spData) {
        return this.post('/saml/sp', spData);
    },

    /**
     * 更新SAML SP
     * @param {string} spId - SP ID
     * @param {Object} spData - SP数据
     */
    updateSAMLProvider(spId, spData) {
        return this.put(`/saml/sp/${spId}`, spData);
    },

    /**
     * 删除SAML SP
     * @param {string} spId - SP ID
     */
    deleteSAMLProvider(spId) {
        return this.delete(`/saml/sp/${spId}`);
    },

    /**
     * 获取SAML元数据
     */
    getSAMLMetadata() {
        return fetch('/saml/metadata').then(res => res.text());
    },

    // ==================== 审计日志 ====================

    /**
     * 获取审计日志
     * @param {Object} params - 查询参数
     */
    getAuditLogs(params = {}) {
        const query = new URLSearchParams(params).toString();
        return this.get(`/admin/audit-logs${query ? '?' + query : ''}`);
    },

    // ==================== 系统维护 ====================

    /**
     * 清理缓存
     * @param {string} type - 缓存类型
     */
    clearCache(type = 'all') {
        return this.post('/admin/maintenance/clear-cache', { type });
    },

    /**
     * 清理过期令牌
     */
    cleanupExpiredTokens() {
        return this.post('/admin/maintenance/cleanup-tokens');
    },

    /**
     * 清理过期会话
     */
    cleanupExpiredSessions() {
        return this.post('/admin/maintenance/cleanup-sessions');
    },

    // ==================== 监控相关 ====================

    /**
     * 获取性能指标
     */
    getMetrics() {
        return this.get('/monitoring/metrics');
    },

    /**
     * 获取实时统计
     */
    getRealTimeStats() {
        return this.get('/monitoring/realtime');
    },

    // ==================== 配置管理 ====================

    /**
     * 获取系统配置
     */
    getSystemConfig() {
        return this.get('/admin/config');
    },

    /**
     * 更新系统配置
     * @param {Object} config - 配置数据
     */
    updateSystemConfig(config) {
        return this.put('/admin/config', config);
    },

    // ==================== 文件上传 ====================

    /**
     * 上传文件
     * @param {File} file - 文件对象
     * @param {Function} onProgress - 进度回调
     */
    async uploadFile(file, onProgress) {
        const formData = new FormData();
        formData.append('file', file);

        const config = {
            method: 'POST',
            headers: {
                Authorization: `Bearer ${this.token}`
            },
            body: formData
        };

        if (onProgress) {
            // 这里可以添加上传进度监听
            // 需要使用XMLHttpRequest来实现进度监听
        }

        const response = await fetch(`${this.baseURL}/admin/upload`, config);
        
        if (!response.ok) {
            const error = await response.json();
            throw new Error(error.message || '上传失败');
        }

        return response.json();
    }
};
