/**
 * 页面模块
 * 包含各个管理页面的实现
 */

window.Pages = {
    /**
     * 仪表板页面
     */
    dashboard: {
        async render() {
            try {
                const stats = await API.getSystemStats();
                
                return `
                    <div class="fade-in">
                        <!-- 页面标题 -->
                        <div class="mb-8">
                            <h1 class="text-3xl font-bold text-gray-900">仪表板</h1>
                            <p class="text-gray-600 mt-2">系统概览和关键指标</p>
                        </div>

                        <!-- 统计卡片 -->
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                            <div class="stat-card-1 text-white p-6 rounded-lg card-hover">
                                <div class="flex items-center">
                                    <div class="flex-1">
                                        <p class="text-white/80 text-sm">总用户数</p>
                                        <p class="text-2xl font-bold">${Utils.formatNumber(stats.totalUsers)}</p>
                                    </div>
                                    <i class="fas fa-users text-3xl text-white/60"></i>
                                </div>
                            </div>

                            <div class="stat-card-2 text-white p-6 rounded-lg card-hover">
                                <div class="flex items-center">
                                    <div class="flex-1">
                                        <p class="text-white/80 text-sm">活跃用户</p>
                                        <p class="text-2xl font-bold">${Utils.formatNumber(stats.activeUsers)}</p>
                                    </div>
                                    <i class="fas fa-user-check text-3xl text-white/60"></i>
                                </div>
                            </div>

                            <div class="stat-card-3 text-white p-6 rounded-lg card-hover">
                                <div class="flex items-center">
                                    <div class="flex-1">
                                        <p class="text-white/80 text-sm">活跃会话</p>
                                        <p class="text-2xl font-bold">${Utils.formatNumber(stats.totalSessions)}</p>
                                    </div>
                                    <i class="fas fa-clock text-3xl text-white/60"></i>
                                </div>
                            </div>

                            <div class="stat-card-4 text-white p-6 rounded-lg card-hover">
                                <div class="flex items-center">
                                    <div class="flex-1">
                                        <p class="text-white/80 text-sm">OAuth客户端</p>
                                        <p class="text-2xl font-bold">${Utils.formatNumber(stats.oauthClients)}</p>
                                    </div>
                                    <i class="fas fa-key text-3xl text-white/60"></i>
                                </div>
                            </div>
                        </div>

                        <!-- 图表和列表 -->
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <!-- 系统健康状态 -->
                            <div class="bg-white p-6 rounded-lg card-shadow">
                                <h3 class="text-lg font-semibold text-gray-900 mb-4">系统健康状态</h3>
                                <div class="space-y-4">
                                    <div class="flex items-center justify-between">
                                        <span class="text-gray-600">数据库连接</span>
                                        <span class="flex items-center">
                                            <div class="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                                            正常
                                        </span>
                                    </div>
                                    <div class="flex items-center justify-between">
                                        <span class="text-gray-600">Redis缓存</span>
                                        <span class="flex items-center">
                                            <div class="w-2 h-2 ${stats.isRedisHealthy ? 'bg-green-500' : 'bg-red-500'} rounded-full mr-2"></div>
                                            ${stats.isRedisHealthy ? '正常' : '异常'}
                                        </span>
                                    </div>
                                    <div class="flex items-center justify-between">
                                        <span class="text-gray-600">内存使用</span>
                                        <span class="text-gray-900">${Utils.formatFileSize(stats.memoryUsage?.heapUsed || 0)}</span>
                                    </div>
                                </div>
                            </div>

                            <!-- 最近活动 -->
                            <div class="bg-white p-6 rounded-lg card-shadow">
                                <h3 class="text-lg font-semibold text-gray-900 mb-4">最近活动</h3>
                                <div class="space-y-3">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                            <i class="fas fa-user-plus text-blue-600 text-sm"></i>
                                        </div>
                                        <div class="flex-1">
                                            <p class="text-sm text-gray-900">新用户注册</p>
                                            <p class="text-xs text-gray-500">2分钟前</p>
                                        </div>
                                    </div>
                                    <div class="flex items-center space-x-3">
                                        <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                                            <i class="fas fa-sign-in-alt text-green-600 text-sm"></i>
                                        </div>
                                        <div class="flex-1">
                                            <p class="text-sm text-gray-900">用户登录</p>
                                            <p class="text-xs text-gray-500">5分钟前</p>
                                        </div>
                                    </div>
                                    <div class="flex items-center space-x-3">
                                        <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                                            <i class="fas fa-key text-purple-600 text-sm"></i>
                                        </div>
                                        <div class="flex-1">
                                            <p class="text-sm text-gray-900">OAuth授权</p>
                                            <p class="text-xs text-gray-500">10分钟前</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            } catch (error) {
                console.error('加载仪表板失败:', error);
                return `
                    <div class="text-center py-12">
                        <i class="fas fa-exclamation-triangle text-red-500 text-4xl mb-4"></i>
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">加载失败</h3>
                        <p class="text-gray-600">无法加载仪表板数据，请刷新页面重试</p>
                    </div>
                `;
            }
        }
    },

    /**
     * 用户管理页面
     */
    users: {
        currentPage: 1,
        pageSize: 20,
        searchQuery: '',

        async render() {
            try {
                const params = {
                    page: this.currentPage,
                    limit: this.pageSize,
                    search: this.searchQuery
                };

                const response = await API.getUsers(params);
                const { users, pagination } = response;

                const tableHTML = Components.createTable({
                    columns: [
                        { key: 'email', title: '邮箱' },
                        { key: 'nickname', title: '昵称' },
                        { 
                            key: 'isActive', 
                            title: '状态',
                            render: (value) => Components.createStatusTag(value ? 'active' : 'inactive')
                        },
                        { 
                            key: 'createdAt', 
                            title: '创建时间',
                            render: (value) => Utils.formatDate(value, 'YYYY-MM-DD')
                        },
                        {
                            key: 'actions',
                            title: '操作',
                            render: (_, row) => Components.createActionButtons([
                                {
                                    text: '编辑',
                                    icon: 'fas fa-edit',
                                    onClick: `Pages.users.editUser('${row.id}')`
                                },
                                {
                                    text: '删除',
                                    icon: 'fas fa-trash',
                                    onClick: `Pages.users.deleteUser('${row.id}')`,
                                    className: 'text-red-600 hover:text-red-900'
                                }
                            ])
                        }
                    ],
                    data: users,
                    pagination: {
                        current: pagination.page,
                        total: pagination.total,
                        pageSize: pagination.limit,
                        onChange: (page) => {
                            this.currentPage = page;
                            App.loadPage('users');
                        }
                    }
                });

                return `
                    <div class="fade-in">
                        <!-- 页面标题和操作 -->
                        <div class="flex justify-between items-center mb-6">
                            <div>
                                <h1 class="text-3xl font-bold text-gray-900">用户管理</h1>
                                <p class="text-gray-600 mt-2">管理系统用户账户</p>
                            </div>
                            <button onclick="Pages.users.createUser()" 
                                    class="btn-primary px-4 py-2 bg-indigo-600 hover:bg-indigo-700 text-white rounded-lg">
                                <i class="fas fa-plus mr-2"></i>新建用户
                            </button>
                        </div>

                        <!-- 搜索和筛选 -->
                        <div class="bg-white p-4 rounded-lg card-shadow mb-6">
                            <div class="flex flex-col sm:flex-row gap-4">
                                ${Components.createSearchBox({
                                    placeholder: '搜索用户邮箱或昵称...',
                                    onSearch: (query) => {
                                        this.searchQuery = query;
                                        this.currentPage = 1;
                                        App.loadPage('users');
                                    },
                                    className: 'flex-1'
                                })}
                                <select class="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500">
                                    <option value="">所有状态</option>
                                    <option value="active">活跃</option>
                                    <option value="inactive">非活跃</option>
                                </select>
                            </div>
                        </div>

                        <!-- 用户表格 -->
                        <div class="bg-white rounded-lg card-shadow">
                            ${tableHTML}
                        </div>
                    </div>
                `;
            } catch (error) {
                console.error('加载用户列表失败:', error);
                Components.showNotification('加载用户列表失败', 'error');
                return '<div class="text-center py-12">加载失败</div>';
            }
        },

        createUser() {
            const content = `
                <form id="user-form" class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">邮箱地址</label>
                        <input type="email" name="email" required 
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">昵称</label>
                        <input type="text" name="nickname" required 
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">密码</label>
                        <input type="password" name="password" required 
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500">
                    </div>
                    <div class="flex items-center">
                        <input type="checkbox" name="isActive" checked 
                               class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                        <label class="ml-2 block text-sm text-gray-900">激活用户</label>
                    </div>
                    <div class="flex justify-end space-x-3 pt-4">
                        <button type="button" onclick="Components.closeModal()" 
                                class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50">
                            取消
                        </button>
                        <button type="submit" 
                                class="btn-primary px-4 py-2 bg-indigo-600 hover:bg-indigo-700 text-white rounded-lg">
                            创建用户
                        </button>
                    </div>
                </form>
            `;

            Components.createModal({
                title: '新建用户',
                content,
                size: 'md'
            });

            document.getElementById('user-form').onsubmit = async (e) => {
                e.preventDefault();
                const formData = new FormData(e.target);
                const userData = {
                    email: formData.get('email'),
                    nickname: formData.get('nickname'),
                    password: formData.get('password'),
                    isActive: formData.get('isActive') === 'on'
                };

                try {
                    await API.createUser(userData);
                    Components.closeModal();
                    Components.showNotification('用户创建成功', 'success');
                    App.loadPage('users');
                } catch (error) {
                    Components.showNotification('用户创建失败: ' + error.message, 'error');
                }
            };
        },

        editUser(userId) {
            // TODO: 实现编辑用户功能
            Components.showNotification('编辑功能开发中...', 'info');
        },

        deleteUser(userId) {
            Components.showConfirmDialog(
                '删除用户',
                '确定要删除这个用户吗？此操作不可撤销。',
                async () => {
                    try {
                        await API.deleteUser(userId);
                        Components.showNotification('用户删除成功', 'success');
                        App.loadPage('users');
                    } catch (error) {
                        Components.showNotification('用户删除失败: ' + error.message, 'error');
                    }
                }
            );
        }
    },

    /**
     * OAuth客户端管理页面
     */
    oauthClients: {
        currentPage: 1,
        pageSize: 20,

        async render() {
            try {
                const response = await API.getOAuthClients({
                    page: this.currentPage,
                    limit: this.pageSize
                });

                const { applications, pagination } = response;

                const tableHTML = Components.createTable({
                    columns: [
                        { key: 'name', title: '应用名称' },
                        { key: 'clientId', title: '客户端ID' },
                        { 
                            key: 'supportedProtocols', 
                            title: '支持协议',
                            render: (protocols) => protocols.map(p => 
                                `<span class="tag tag-info mr-1">${p.toUpperCase()}</span>`
                            ).join('')
                        },
                        { 
                            key: 'isActive', 
                            title: '状态',
                            render: (value) => Components.createStatusTag(value ? 'active' : 'inactive')
                        },
                        {
                            key: 'actions',
                            title: '操作',
                            render: (_, row) => Components.createActionButtons([
                                {
                                    text: '编辑',
                                    icon: 'fas fa-edit',
                                    onClick: `Pages.oauthClients.editClient('${row.id}')`
                                },
                                {
                                    text: '删除',
                                    icon: 'fas fa-trash',
                                    onClick: `Pages.oauthClients.deleteClient('${row.id}')`,
                                    className: 'text-red-600 hover:text-red-900'
                                }
                            ])
                        }
                    ],
                    data: applications,
                    pagination: {
                        current: pagination.page,
                        total: pagination.total,
                        pageSize: pagination.limit,
                        onChange: (page) => {
                            this.currentPage = page;
                            App.loadPage('oauth-clients');
                        }
                    }
                });

                return `
                    <div class="fade-in">
                        <div class="flex justify-between items-center mb-6">
                            <div>
                                <h1 class="text-3xl font-bold text-gray-900">OAuth客户端</h1>
                                <p class="text-gray-600 mt-2">管理OAuth 2.0和OIDC应用程序</p>
                            </div>
                            <button onclick="Pages.oauthClients.createClient()" 
                                    class="btn-primary px-4 py-2 bg-indigo-600 hover:bg-indigo-700 text-white rounded-lg">
                                <i class="fas fa-plus mr-2"></i>新建客户端
                            </button>
                        </div>

                        <div class="bg-white rounded-lg card-shadow">
                            ${tableHTML}
                        </div>
                    </div>
                `;
            } catch (error) {
                console.error('加载OAuth客户端失败:', error);
                return '<div class="text-center py-12">加载失败</div>';
            }
        },

        createClient() {
            // TODO: 实现创建OAuth客户端功能
            Components.showNotification('创建功能开发中...', 'info');
        },

        editClient(clientId) {
            // TODO: 实现编辑OAuth客户端功能
            Components.showNotification('编辑功能开发中...', 'info');
        },

        deleteClient(clientId) {
            // TODO: 实现删除OAuth客户端功能
            Components.showNotification('删除功能开发中...', 'info');
        }
    }
};
