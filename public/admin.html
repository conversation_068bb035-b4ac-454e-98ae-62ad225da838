<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="身份提供商管理控制台">
    <meta name="keywords" content="身份管理,权限管理,SSO,管理控制台">
    <meta name="author" content="身份提供商">
    
    <!-- 安全头 -->
    <meta http-equiv="X-Content-Type-Options" content="nosniff">
    <meta http-equiv="X-Frame-Options" content="DENY">
    <meta http-equiv="X-XSS-Protection" content="1; mode=block">
    <meta http-equiv="Referrer-Policy" content="strict-origin-when-cross-origin">
    
    <!-- CSP策略 -->
    <meta http-equiv="Content-Security-Policy" content="
        default-src 'self';
        script-src 'self' 'unsafe-inline' 'unsafe-eval';
        style-src 'self' 'unsafe-inline' https://fonts.googleapis.com;
        font-src 'self' https://fonts.gstatic.com;
        img-src 'self' data: https:;
        connect-src 'self' ws: wss:;
        frame-ancestors 'none';
        base-uri 'self';
        form-action 'self';
    ">
    
    <title>管理控制台 - 身份提供商</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png">
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">
    
    <!-- 预加载关键资源 -->
    <link rel="preload" href="/fonts/roboto-v30-latin-regular.woff2" as="font" type="font/woff2" crossorigin>
    <link rel="preload" href="/fonts/roboto-v30-latin-500.woff2" as="font" type="font/woff2" crossorigin>
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    
    <!-- 主题色 -->
    <meta name="theme-color" content="#1976d2">
    <meta name="msapplication-TileColor" content="#1976d2">
    
    <!-- PWA支持 -->
    <link rel="manifest" href="/manifest.json">
    
    <style>
        /* 初始加载样式 */
        body {
            margin: 0;
            padding: 0;
            font-family: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background-color: #f5f5f5;
            color: #333;
        }
        
        /* 加载屏幕 */
        .loading-screen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #1976d2 0%, #42a5f5 100%);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            transition: opacity 0.5s ease-out;
        }
        
        .loading-screen.hidden {
            opacity: 0;
            pointer-events: none;
        }
        
        .loading-logo {
            width: 80px;
            height: 80px;
            margin-bottom: 24px;
            background: white;
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
        }
        
        .loading-logo svg {
            width: 48px;
            height: 48px;
            fill: #1976d2;
        }
        
        .loading-title {
            color: white;
            font-size: 24px;
            font-weight: 500;
            margin-bottom: 8px;
            text-align: center;
        }
        
        .loading-subtitle {
            color: rgba(255, 255, 255, 0.8);
            font-size: 16px;
            font-weight: 300;
            margin-bottom: 32px;
            text-align: center;
        }
        
        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-top: 3px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        /* 错误屏幕 */
        .error-screen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: #f5f5f5;
            display: none;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 9998;
        }
        
        .error-icon {
            width: 64px;
            height: 64px;
            color: #f44336;
            margin-bottom: 16px;
        }
        
        .error-title {
            font-size: 24px;
            font-weight: 500;
            color: #333;
            margin-bottom: 8px;
            text-align: center;
        }
        
        .error-message {
            font-size: 16px;
            color: #666;
            margin-bottom: 24px;
            text-align: center;
            max-width: 400px;
        }
        
        .error-button {
            background: #1976d2;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        
        .error-button:hover {
            background: #1565c0;
        }
        
        /* 隐藏主应用直到加载完成 */
        #admin-root {
            opacity: 0;
            transition: opacity 0.5s ease-in;
        }
        
        #admin-root.loaded {
            opacity: 1;
        }
    </style>
</head>
<body>
    <!-- 加载屏幕 -->
    <div id="loading-screen" class="loading-screen">
        <div class="loading-logo">
            <svg viewBox="0 0 24 24">
                <path d="M12,1L3,5V11C3,16.55 6.84,21.74 12,23C17.16,21.74 21,16.55 21,11V5L12,1M12,7C13.4,7 14.8,8.6 14.8,10V11H16V18H8V11H9.2V10C9.2,8.6 10.6,7 12,7M12,8.2C11.2,8.2 10.4,8.7 10.4,10V11H13.6V10C13.6,8.7 12.8,8.2 12,8.2Z"/>
            </svg>
        </div>
        <h1 class="loading-title">身份提供商</h1>
        <p class="loading-subtitle">管理控制台正在加载...</p>
        <div class="loading-spinner"></div>
    </div>
    
    <!-- 错误屏幕 -->
    <div id="error-screen" class="error-screen">
        <svg class="error-icon" viewBox="0 0 24 24">
            <path d="M12,2C17.53,2 22,6.47 22,12C22,17.53 17.53,22 12,22C6.47,22 2,17.53 2,12C2,6.47 6.47,2 12,2M15.59,7L12,10.59L8.41,7L7,8.41L10.59,12L7,15.59L8.41,17L12,13.41L15.59,17L17,15.59L13.41,12L17,8.41L15.59,7Z"/>
        </svg>
        <h1 class="error-title">加载失败</h1>
        <p class="error-message">管理控制台无法正常加载，请检查网络连接或稍后重试。</p>
        <button class="error-button" onclick="location.reload()">重新加载</button>
    </div>
    
    <!-- 主应用容器 -->
    <div id="admin-root"></div>
    
    <!-- 环境配置 -->
    <script>
        // 全局配置
        window.APP_CONFIG = {
            API_BASE_URL: '/admin-api',
            CONFIG_API_URL: '/config',
            VERSION: '1.0.0',
            BUILD_TIME: new Date().toISOString(),
            FEATURES: {
                DARK_MODE: true,
                EXPORT: true,
                NOTIFICATIONS: true,
                REAL_TIME: false
            }
        };
        
        // 错误处理
        window.addEventListener('error', function(event) {
            console.error('应用错误:', event.error);
            showErrorScreen('应用运行时发生错误');
        });
        
        window.addEventListener('unhandledrejection', function(event) {
            console.error('未处理的Promise拒绝:', event.reason);
            showErrorScreen('网络请求失败或数据加载错误');
        });
        
        // 显示错误屏幕
        function showErrorScreen(message) {
            const loadingScreen = document.getElementById('loading-screen');
            const errorScreen = document.getElementById('error-screen');
            const errorMessage = errorScreen.querySelector('.error-message');
            
            if (errorMessage && message) {
                errorMessage.textContent = message;
            }
            
            loadingScreen.style.display = 'none';
            errorScreen.style.display = 'flex';
        }
        
        // 隐藏加载屏幕
        function hideLoadingScreen() {
            const loadingScreen = document.getElementById('loading-screen');
            const adminRoot = document.getElementById('admin-root');
            
            loadingScreen.classList.add('hidden');
            adminRoot.classList.add('loaded');
            
            setTimeout(() => {
                loadingScreen.style.display = 'none';
            }, 500);
        }
        
        // 应用加载完成回调
        window.onAppLoaded = hideLoadingScreen;
        
        // 超时处理
        setTimeout(() => {
            const loadingScreen = document.getElementById('loading-screen');
            if (loadingScreen && !loadingScreen.classList.contains('hidden')) {
                showErrorScreen('应用加载超时，请检查网络连接');
            }
        }, 30000); // 30秒超时
    </script>
    
    <!-- 主应用脚本 -->
    <script type="module" src="/admin/index.js"></script>
    
    <!-- 服务工作者注册 -->
    <script>
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', function() {
                navigator.serviceWorker.register('/sw.js')
                    .then(function(registration) {
                        console.log('ServiceWorker注册成功:', registration.scope);
                    })
                    .catch(function(error) {
                        console.log('ServiceWorker注册失败:', error);
                    });
            });
        }
    </script>
</body>
</html>
