# 开发指南

## 1. 环境搭建

### 1.1 系统要求

- Node.js 18.0+
- PostgreSQL 13+
- Redis 6+ (可选，用于缓存)
- Git

### 1.2 安装依赖

```bash
# 克隆项目
git clone <repository-url>
cd id-provider

# 安装依赖
npm install

# 复制环境变量文件
cp .env.example .env
```

### 1.3 配置环境变量

编辑 `.env` 文件，配置以下必需变量：

```bash
# 数据库配置
DATABASE_URL="postgresql://username:password@localhost:5432/id_provider"

# JWT密钥
JWT_SECRET="your-super-secret-jwt-key"
JWT_REFRESH_SECRET="your-super-secret-refresh-key"

# 邮件配置
SMTP_HOST="smtp.gmail.com"
SMTP_PORT=587
SMTP_USER="<EMAIL>"
SMTP_PASS="your-app-password"

# OAuth配置
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"
GITHUB_CLIENT_ID="your-github-client-id"
GITHUB_CLIENT_SECRET="your-github-client-secret"
```

### 1.4 数据库设置

```bash
# 生成Prisma客户端
npm run db:generate

# 运行数据库迁移
npm run db:migrate

# 初始化种子数据
npm run db:seed
```

## 2. 开发流程

### 2.1 启动开发服务器

```bash
# 开发模式启动
npm run dev

# 构建项目
npm run build

# 生产模式启动
npm start
```

### 2.2 代码规范

#### 2.2.1 TypeScript规范

- 使用严格的TypeScript配置
- 所有函数必须有明确的返回类型
- 使用接口定义数据结构
- 避免使用 `any` 类型

```typescript
// 好的示例
interface UserCreateRequest {
  email: string;
  password: string;
  nickname?: string;
}

async function createUser(data: UserCreateRequest): Promise<User> {
  // 实现
}

// 避免的示例
function createUser(data: any): any {
  // 实现
}
```

#### 2.2.2 命名规范

- 文件名使用kebab-case: `auth.service.ts`
- 类名使用PascalCase: `AuthService`
- 函数和变量使用camelCase: `getUserById`
- 常量使用UPPER_SNAKE_CASE: `JWT_SECRET`

#### 2.2.3 注释规范

- 所有公共方法必须有JSDoc注释
- 复杂逻辑需要行内注释
- 使用中文注释

```typescript
/**
 * 用户认证服务
 * 处理用户注册、登录、密码管理等功能
 */
export class AuthService {
  /**
   * 用户注册
   * @param registerData 注册数据
   * @param ipAddress IP地址
   * @returns 注册结果
   */
  async register(registerData: RegisterRequest, ipAddress?: string): Promise<RegisterResult> {
    // 实现逻辑
  }
}
```

### 2.3 错误处理

#### 2.3.1 统一错误格式

```typescript
// 错误响应格式
interface ErrorResponse {
  error: string;
  message: string;
  details?: any;
}

// 使用示例
res.status(400).json({
  error: 'validation_error',
  message: '请求数据验证失败',
  details: validationErrors
});
```

#### 2.3.2 错误日志记录

```typescript
try {
  // 业务逻辑
} catch (error) {
  logger.error('操作失败', { 
    error: (error as Error).message,
    userId,
    operation: 'user_registration'
  });
  throw error;
}
```

### 2.4 数据库操作

#### 2.4.1 使用Prisma

```typescript
// 查询示例
const user = await prisma.user.findUnique({
  where: { id: userId },
  include: {
    mfaDevices: true,
    sessions: {
      where: { isActive: true }
    }
  }
});

// 事务示例
await prisma.$transaction(async (tx) => {
  const user = await tx.user.create({ data: userData });
  await tx.userRole.create({ 
    data: { userId: user.id, roleId: defaultRoleId }
  });
});
```

#### 2.4.2 数据验证

```typescript
// 使用Joi进行数据验证
const schema = Joi.object({
  email: Joi.string().email().required(),
  password: Joi.string().min(8).required()
});

const { error, value } = schema.validate(requestData);
if (error) {
  throw new Error('数据验证失败');
}
```

## 3. 测试

### 3.1 单元测试

```bash
# 运行所有测试
npm test

# 运行特定测试文件
npm test auth.service.test.ts

# 运行测试并生成覆盖率报告
npm run test:coverage

# 监听模式运行测试
npm run test:watch
```

### 3.2 测试编写规范

```typescript
describe('AuthService', () => {
  let authService: AuthService;

  beforeEach(() => {
    authService = new AuthService();
    jest.clearAllMocks();
  });

  describe('register', () => {
    it('应该成功注册新用户', async () => {
      // Arrange
      const registerData = {
        email: '<EMAIL>',
        password: 'StrongPassword123!'
      };

      // Act
      const result = await authService.register(registerData);

      // Assert
      expect(result).toEqual({
        userId: expect.any(String),
        status: 'pending_verification'
      });
    });

    it('应该拒绝重复的邮箱', async () => {
      // 测试逻辑
    });
  });
});
```

### 3.3 集成测试

```typescript
import request from 'supertest';
import app from '../index';

describe('Auth API', () => {
  it('POST /api/v1/auth/register', async () => {
    const response = await request(app)
      .post('/api/v1/auth/register')
      .send({
        email: '<EMAIL>',
        password: 'StrongPassword123!'
      });

    expect(response.status).toBe(201);
    expect(response.body).toHaveProperty('userId');
  });
});
```

## 4. 安全最佳实践

### 4.1 输入验证

- 所有用户输入必须验证
- 使用白名单而非黑名单
- 防止SQL注入和XSS攻击

### 4.2 认证和授权

- 使用强密码策略
- 实施JWT令牌管理
- 支持多因素认证
- 实现基于角色的访问控制

### 4.3 数据保护

- 敏感数据加密存储
- 使用HTTPS通信
- 实施数据脱敏
- 定期备份数据

### 4.4 日志和监控

- 记录安全事件
- 监控异常活动
- 实施审计日志
- 设置告警机制

## 5. 性能优化

### 5.1 数据库优化

- 合理使用索引
- 优化查询语句
- 使用连接池
- 实施分页查询

### 5.2 缓存策略

- 使用Redis缓存热点数据
- 实施JWT令牌缓存
- 缓存用户会话信息
- 设置合理的过期时间

### 5.3 API优化

- 实施速率限制
- 使用压缩中间件
- 优化响应大小
- 实施CDN加速

## 6. 部署

### 6.1 Docker部署

```dockerfile
FROM node:18-alpine

WORKDIR /app

COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

EXPOSE 3000

CMD ["npm", "start"]
```

### 6.2 环境配置

```yaml
# docker-compose.yml
version: '3.8'
services:
  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - DATABASE_URL=******************************/idp
    depends_on:
      - db
      - redis

  db:
    image: postgres:13
    environment:
      - POSTGRES_DB=idp
      - POSTGRES_USER=user
      - POSTGRES_PASSWORD=pass
    volumes:
      - postgres_data:/var/lib/postgresql/data

  redis:
    image: redis:6-alpine
    volumes:
      - redis_data:/data

volumes:
  postgres_data:
  redis_data:
```

### 6.3 生产环境检查清单

- [ ] 环境变量配置正确
- [ ] 数据库迁移完成
- [ ] SSL证书配置
- [ ] 日志收集配置
- [ ] 监控告警配置
- [ ] 备份策略实施
- [ ] 安全扫描通过
- [ ] 性能测试通过

## 7. 故障排除

### 7.1 常见问题

**数据库连接失败**
- 检查数据库服务状态
- 验证连接字符串
- 检查网络连接

**JWT令牌验证失败**
- 检查密钥配置
- 验证令牌格式
- 检查过期时间

**邮件发送失败**
- 检查SMTP配置
- 验证邮箱凭据
- 检查网络连接

### 7.2 调试技巧

```typescript
// 使用调试日志
logger.debug('调试信息', { data });

// 使用断点调试
debugger;

// 使用性能监控
const start = Date.now();
// 业务逻辑
const duration = Date.now() - start;
logger.info('操作耗时', { operation, duration });
```

## 8. 贡献指南

### 8.1 提交规范

```bash
# 提交格式
<type>(<scope>): <description>

# 示例
feat(auth): 添加多因素认证功能
fix(user): 修复用户资料更新问题
docs(api): 更新API文档
```

### 8.2 代码审查

- 所有代码必须经过审查
- 确保测试覆盖率
- 检查安全漏洞
- 验证性能影响

### 8.3 发布流程

1. 创建功能分支
2. 开发和测试
3. 提交Pull Request
4. 代码审查
5. 合并到主分支
6. 部署到测试环境
7. 验证功能
8. 部署到生产环境
