# 非标准应用支持文档

## 概述

本身份提供商系统通过强大的协议适配器框架，为各种非标准应用和遗留系统提供了全面的身份认证支持。该框架采用插件化架构，支持动态加载和配置，能够适应各种复杂的企业环境需求。

## 核心特性

### ✅ 已实现的适配器类型

#### 1. **自定义OAuth适配器** (`custom-oauth`)
- **支持的流程**：授权码、隐式、密码、客户端凭据、设备码、自定义流程
- **PKCE支持**：完整的PKCE实现，支持S256和plain方法
- **自定义扩展**：支持自定义参数、头部、验证和转换逻辑
- **令牌格式**：支持JWT和不透明令牌
- **适用场景**：非标准OAuth实现、自定义授权流程

#### 2. **遗留系统适配器** (`legacy-system`)
- **认证方法**：
  - 基于表单的认证
  - HTTP基础认证
  - HTTP摘要认证
  - 自定义头部认证
  - 会话Cookie认证
  - API密钥认证
  - LDAP绑定认证
- **适用场景**：传统企业系统、LDAP集成、基于表单的应用

#### 3. **Webhook适配器** (`webhook`)
- **认证方式**：基于Webhook的认证验证
- **事件通知**：支持认证、登录、登出等事件通知
- **签名验证**：支持HMAC签名验证
- **批量处理**：支持事件批量发送
- **适用场景**：微服务架构、事件驱动系统

#### 4. **API网关适配器** (`api-gateway`)
- **支持的网关**：Kong、Zuul、Ambassador、Istio、Traefik、Nginx
- **认证策略**：JWT验证、API密钥、OAuth内省、自定义头部、mTLS
- **插件配置**：限流、CORS、日志、转换等
- **适用场景**：微服务网关、API管理平台

## 架构设计

### 适配器基础架构

```typescript
// 基础适配器抽象类
abstract class BaseProtocolAdapter {
  abstract name: string;
  abstract version: string;
  abstract supportedMethods: string[];
  
  // 核心方法
  abstract performAuthentication(request, application): Promise<AuthenticationResponse>;
  abstract generateMetadata(): Promise<Record<string, any>>;
  
  // 生命周期方法
  async initialize(): Promise<void>;
  async destroy(): Promise<void>;
  async updateConfig(config: ProtocolConfig): Promise<void>;
}
```

### 适配器注册表

```typescript
// 适配器注册表服务
class AdapterRegistryService {
  // 注册适配器
  registerAdapter(name: string, adapterClass: typeof BaseProtocolAdapter, metadata: AdapterMetadata): void;
  
  // 创建实例
  createAdapterInstance(name: string, config: ProtocolConfig): Promise<string>;
  
  // 管理实例
  getAdapterInstance(instanceId: string): AdapterInstance | undefined;
  stopAdapterInstance(instanceId: string): Promise<boolean>;
  restartAdapterInstance(instanceId: string): Promise<boolean>;
}
```

## API接口

### 适配器管理API

| 端点 | 方法 | 功能 | 说明 |
|------|------|------|------|
| `/api/v1/adapters` | GET | 获取已注册适配器 | 列出所有可用适配器 |
| `/api/v1/adapters/:name` | GET | 获取适配器元数据 | 获取特定适配器信息 |
| `/api/v1/adapters/instances` | GET | 获取适配器实例 | 列出所有实例 |
| `/api/v1/adapters/instances` | POST | 创建适配器实例 | 创建新的适配器实例 |
| `/api/v1/adapters/instances/:id` | GET | 获取特定实例 | 获取实例详情 |
| `/api/v1/adapters/instances/:id/config` | PUT | 更新实例配置 | 动态更新配置 |
| `/api/v1/adapters/instances/:id/stop` | POST | 停止实例 | 停止适配器实例 |
| `/api/v1/adapters/instances/:id/restart` | POST | 重启实例 | 重启适配器实例 |
| `/api/v1/adapters/stats` | GET | 获取使用统计 | 适配器使用情况 |
| `/api/v1/adapters/load` | POST | 加载外部适配器 | 动态加载适配器 |
| `/api/v1/adapters/cleanup` | POST | 清理非活跃实例 | 资源清理 |

### 创建适配器实例示例

```bash
curl -X POST http://localhost:3000/api/v1/adapters/instances \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "adapterName": "custom-oauth",
    "config": {
      "endpoints": {
        "authorization": "https://example.com/oauth/authorize",
        "token": "https://example.com/oauth/token"
      },
      "oauth": {
        "authorizationEndpoint": "https://example.com/oauth/authorize",
        "tokenEndpoint": "https://example.com/oauth/token"
      },
      "client": {
        "clientId": "example-client",
        "redirectUris": ["https://app.example.com/callback"],
        "allowedScopes": ["read", "write"]
      },
      "flows": {
        "enabled": ["authorization_code", "client_credentials"]
      }
    }
  }'
```

## 配置示例

### 1. 自定义OAuth配置

```json
{
  "oauth": {
    "authorizationEndpoint": "https://custom-oauth.example.com/authorize",
    "tokenEndpoint": "https://custom-oauth.example.com/token",
    "userinfoEndpoint": "https://custom-oauth.example.com/userinfo"
  },
  "client": {
    "clientId": "custom-client-id",
    "clientSecret": "custom-client-secret",
    "redirectUris": ["https://app.example.com/callback"],
    "allowedScopes": ["read", "write", "admin"]
  },
  "flows": {
    "enabled": ["authorization_code", "client_credentials"],
    "authorizationCodeConfig": {
      "pkceRequired": true,
      "stateRequired": true,
      "codeChallengeMethods": ["S256"]
    }
  },
  "tokenConfig": {
    "accessTokenLifetime": 3600,
    "refreshTokenLifetime": 86400,
    "tokenFormat": "jwt"
  }
}
```

### 2. 遗留系统配置

```json
{
  "authMethod": "ldap_bind",
  "ldapConfig": {
    "server": "ldap.example.com",
    "port": 389,
    "baseDN": "dc=example,dc=com",
    "userSearchFilter": "(uid={username})",
    "attributes": ["uid", "cn", "mail", "memberOf"],
    "tls": true
  },
  "endpoints": {
    "authentication": "/auth/ldap",
    "userinfo": "/auth/userinfo"
  }
}
```

### 3. Webhook配置

```json
{
  "webhooks": {
    "authUrl": "https://webhook.example.com/auth",
    "eventUrl": "https://webhook.example.com/events",
    "secretKey": "webhook-secret-key",
    "timeout": 10000,
    "retryAttempts": 3
  },
  "signatureConfig": {
    "algorithm": "sha256",
    "headerName": "X-Webhook-Signature",
    "prefix": "sha256="
  },
  "authConfig": {
    "method": "POST",
    "responseField": "authenticated",
    "userField": "user",
    "tokenField": "access_token"
  },
  "eventConfig": {
    "enabled": true,
    "events": ["auth.success", "auth.failure", "user.login", "user.logout"],
    "batchSize": 10,
    "batchDelay": 5000
  }
}
```

### 4. API网关配置

```json
{
  "gateway": {
    "type": "kong",
    "adminUrl": "http://kong-admin:8001",
    "adminApiKey": "kong-admin-key"
  },
  "authentication": {
    "strategy": "jwt_validation",
    "jwtConfig": {
      "algorithm": "RS256",
      "issuer": "https://idp.example.com",
      "audience": "api-gateway"
    }
  },
  "routing": {
    "upstreamUrl": "https://api.example.com",
    "pathPrefix": "/api",
    "stripPath": true,
    "timeout": 30000
  },
  "plugins": {
    "rateLimiting": {
      "enabled": true,
      "policy": "cluster",
      "minute": 100,
      "hour": 1000
    },
    "cors": {
      "enabled": true,
      "origins": ["https://app.example.com"],
      "methods": ["GET", "POST", "PUT", "DELETE"],
      "credentials": true
    }
  }
}
```

## 扩展开发

### 创建自定义适配器

```typescript
import { BaseProtocolAdapter } from '@/adapters/base-protocol.adapter';
import { AuthenticationRequest, AuthenticationResponse, ProtocolConfig } from '@/types/protocol-adapter';

export class MyCustomAdapter extends BaseProtocolAdapter {
  readonly name = 'my-custom-adapter';
  readonly version = '1.0.0';
  readonly supportedMethods = ['custom_auth'];

  protected async performAuthentication(
    request: AuthenticationRequest,
    application: any
  ): Promise<AuthenticationResponse> {
    // 实现自定义认证逻辑
    return {
      success: true,
      data: {
        access_token: 'custom-token',
        token_type: 'Bearer',
        expires_in: 3600
      }
    };
  }

  async generateMetadata(): Promise<Record<string, any>> {
    return {
      name: this.name,
      version: this.version,
      supportedMethods: this.supportedMethods,
      customFeatures: ['feature1', 'feature2']
    };
  }

  // 实现其他抽象方法...
}
```

### 注册自定义适配器

```typescript
import { adapterRegistry } from '@/services/adapter-registry.service';
import { MyCustomAdapter } from './my-custom-adapter';

// 注册适配器
adapterRegistry.registerAdapter('my-custom-adapter', MyCustomAdapter, {
  name: 'my-custom-adapter',
  version: '1.0.0',
  description: '我的自定义适配器',
  author: 'Your Name',
  supportedMethods: ['custom_auth'],
  category: 'custom',
  status: 'experimental'
});
```

## 监控和管理

### 使用统计

```bash
# 获取适配器使用统计
curl -X GET http://localhost:3000/api/v1/adapters/stats \
  -H "Authorization: Bearer YOUR_TOKEN"
```

响应示例：
```json
{
  "success": true,
  "data": {
    "stats": {
      "custom-oauth": {
        "instanceCount": 2,
        "totalUsage": 150,
        "activeInstances": 2
      },
      "webhook": {
        "instanceCount": 1,
        "totalUsage": 25,
        "activeInstances": 1
      }
    },
    "timestamp": "2024-01-01T12:00:00.000Z"
  }
}
```

### 实例管理

```bash
# 停止适配器实例
curl -X POST http://localhost:3000/api/v1/adapters/instances/INSTANCE_ID/stop \
  -H "Authorization: Bearer YOUR_TOKEN"

# 重启适配器实例
curl -X POST http://localhost:3000/api/v1/adapters/instances/INSTANCE_ID/restart \
  -H "Authorization: Bearer YOUR_TOKEN"

# 清理非活跃实例
curl -X POST http://localhost:3000/api/v1/adapters/cleanup \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## 最佳实践

### 1. 配置管理
- 使用环境变量管理敏感配置
- 实施配置版本控制
- 定期备份适配器配置

### 2. 安全考虑
- 验证所有输入参数
- 使用HTTPS进行通信
- 实施适当的访问控制

### 3. 性能优化
- 合理配置超时时间
- 实施连接池管理
- 监控适配器性能指标

### 4. 错误处理
- 实施重试机制
- 记录详细的错误日志
- 提供有意义的错误消息

## 故障排除

### 常见问题

1. **适配器实例创建失败**
   - 检查配置参数是否正确
   - 验证网络连接
   - 查看错误日志

2. **认证失败**
   - 验证凭据配置
   - 检查端点URL
   - 确认协议版本兼容性

3. **性能问题**
   - 检查网络延迟
   - 优化配置参数
   - 监控资源使用情况

### 日志分析

适配器相关的日志会记录在应用日志中，包括：
- 适配器注册和初始化
- 实例创建和销毁
- 认证请求处理
- 错误和异常信息

## 未来规划

- [ ] 图形化配置界面
- [ ] 适配器模板市场
- [ ] 更多内置适配器
- [ ] 性能监控仪表板
- [ ] 自动故障恢复
- [ ] 配置热重载

通过这个强大的协议适配器框架，身份提供商系统能够支持几乎任何类型的应用和系统，为企业提供统一的身份认证解决方案。
