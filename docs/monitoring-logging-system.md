# 监控和日志系统文档

## 概述

本身份提供商系统集成了完整的监控和日志解决方案，提供实时系统监控、智能日志聚合、告警管理和健康检查功能。该系统采用事件驱动架构，支持多种通知渠道和自定义告警规则。

## 核心特性

### ✅ 已实现的监控功能

#### 1. **高级指标收集器** (`MetricsCollectorService`)
- **✅ 多种指标类型**：
  - 计数器（Counter）- 累计计数指标
  - 仪表盘（Gauge）- 瞬时值指标
  - 直方图（Histogram）- 分布统计指标
  - 摘要（Summary）- 统计摘要指标
  - 计时器（Timer）- 时间测量指标
- **✅ 指标查询和聚合**：
  - 灵活的查询条件过滤
  - 多维度指标聚合
  - 百分位数计算（P50、P95、P99）
  - 时间范围查询支持
- **✅ 告警规则引擎**：
  - 自定义告警条件
  - 多种比较操作符
  - 告警冷却期管理
  - 自动告警触发和解决
- **✅ 数据管理**：
  - 自动数据清理
  - 内存使用优化
  - 历史数据保留策略

#### 2. **日志聚合器** (`LogAggregatorService`)
- **✅ 智能日志收集**：
  - 多级别日志支持（ERROR、WARN、INFO、DEBUG、VERBOSE）
  - 结构化日志数据
  - 自动元数据提取
  - 用户和会话关联
- **✅ 日志查询和分析**：
  - 多条件组合查询
  - 全文搜索支持
  - 时间范围过滤
  - 分页和排序功能
- **✅ 日志模式识别**：
  - 正则表达式模式匹配
  - 自动异常检测
  - 安全事件识别
  - 性能问题模式
- **✅ 统计分析**：
  - 日志级别分布统计
  - 错误率计算
  - 热点错误识别
  - 用户活动分析

#### 3. **告警管理器** (`AlertManagerService`)
- **✅ 统一告警管理**：
  - 多级别告警（LOW、MEDIUM、HIGH、CRITICAL）
  - 告警状态管理（ACTIVE、ACKNOWLEDGED、RESOLVED、SUPPRESSED）
  - 告警去重机制
  - 告警历史记录
- **✅ 多渠道通知**：
  - 邮件通知支持
  - Webhook集成
  - 控制台输出
  - 可扩展通知渠道
- **✅ 智能告警策略**：
  - 告警抑制规则
  - 冷却期管理
  - 条件化通知
  - 时间窗口控制
- **✅ 告警统计分析**：
  - 告警趋势分析
  - 来源分布统计
  - 严重程度分析
  - 处理效率统计

#### 4. **系统健康监控** (`SystemHealthService`)
- **✅ 全面健康检查**：
  - 数据库连接监控
  - Redis服务监控
  - 文件系统检查
  - 内存使用监控
  - CPU性能监控
- **✅ 系统指标收集**：
  - 实时资源使用统计
  - 性能指标计算
  - 负载平均值监控
  - 网络连接统计
- **✅ 健康状态评估**：
  - 综合健康评分
  - 组件状态聚合
  - 阈值告警触发
  - 趋势分析预测
- **✅ 自动化监控**：
  - 定时健康检查
  - 异常自动告警
  - 状态变化通知
  - 历史数据保存

#### 5. **日志收集中间件**
- **✅ HTTP请求日志**：
  - 自动请求响应记录
  - 性能指标收集
  - 错误状态跟踪
  - 用户行为分析
- **✅ 应用日志集成**：
  - 业务事件记录
  - 安全事件跟踪
  - 自定义日志级别
  - 上下文信息关联
- **✅ 错误处理集成**：
  - 异常自动捕获
  - 错误堆栈记录
  - 错误分类统计
  - 错误趋势分析

## 架构设计

### 事件驱动架构

```
┌─────────────────────────────────────────────────────────────────┐
│  API层 (监控管理接口)                                           │
├─────────────────────────────────────────────────────────────────┤
│  中间件层 (日志收集、错误处理)                                 │
├─────────────────────────────────────────────────────────────────┤
│  服务层 (指标收集器、日志聚合器、告警管理器、健康监控)         │
├─────────────────────────────────────────────────────────────────┤
│  事件层 (事件发布订阅、告警触发)                               │
├─────────────────────────────────────────────────────────────────┤
│  存储层 (内存存储、Redis缓存、数据库持久化)                    │
└─────────────────────────────────────────────────────────────────┘
```

### 数据流架构

```typescript
// 指标收集流程
HTTP请求 → 中间件 → 指标收集器 → 告警检查 → 通知发送

// 日志处理流程
应用日志 → 日志聚合器 → 模式匹配 → 告警触发 → 统计分析

// 健康监控流程
定时检查 → 组件检查 → 状态评估 → 指标记录 → 告警判断
```

## API接口

### 监控管理API

| 端点 | 方法 | 功能 | 权限 |
|------|------|------|------|
| `/api/v1/monitoring/health` | GET | 基础健康检查 | 公开 |
| `/api/v1/monitoring/health/advanced` | GET | 高级健康状态 | admin/operator |
| `/api/v1/monitoring/metrics` | GET | 性能指标报告 | admin/operator |
| `/api/v1/monitoring/metrics/query` | GET | 查询指标数据 | admin/operator |
| `/api/v1/monitoring/logs/query` | GET | 查询日志数据 | admin/operator |
| `/api/v1/monitoring/alerts/active` | GET | 获取活跃告警 | admin/operator |
| `/api/v1/monitoring/overview` | GET | 监控概览 | admin/operator |
| `/api/v1/monitoring/realtime` | GET | 实时监控流 | admin/operator |

### 使用示例

#### 获取系统健康状态
```bash
curl -X GET http://localhost:3000/api/v1/monitoring/health/advanced \
  -H "Authorization: Bearer YOUR_TOKEN"
```

响应：
```json
{
  "success": true,
  "data": {
    "overall": "healthy",
    "timestamp": "2024-01-01T12:00:00.000Z",
    "uptime": 3600000,
    "components": [
      {
        "name": "database",
        "status": "healthy",
        "responseTime": 15,
        "message": "数据库连接正常"
      }
    ],
    "metrics": {
      "cpu": { "usage": 25.5 },
      "memory": { "usage": 65.2 },
      "disk": { "usage": 45.8 }
    }
  }
}
```

#### 查询指标数据
```bash
curl -X GET "http://localhost:3000/api/v1/monitoring/metrics/query?name=http_requests_total&limit=100" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

响应：
```json
{
  "success": true,
  "data": {
    "metrics": [
      {
        "name": "http_requests_total",
        "type": "counter",
        "value": 1,
        "timestamp": "2024-01-01T12:00:00.000Z",
        "labels": {
          "method": "GET",
          "endpoint": "/api/v1/users"
        }
      }
    ],
    "count": 150
  }
}
```

#### 查询日志数据
```bash
curl -X GET "http://localhost:3000/api/v1/monitoring/logs/query?level=error&limit=50" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

响应：
```json
{
  "success": true,
  "data": {
    "logs": [
      {
        "id": "log_1234567890_abc123",
        "timestamp": "2024-01-01T12:00:00.000Z",
        "level": "error",
        "message": "数据库连接失败",
        "service": "auth-service",
        "metadata": {
          "error": "Connection timeout"
        }
      }
    ],
    "count": 25
  }
}
```

## 指标和日志

### 系统指标

#### HTTP请求指标
- `http_requests_total` - HTTP请求总数
- `http_request_duration_ms` - HTTP请求响应时间
- `http_responses_total` - HTTP响应总数
- `http_errors_total` - HTTP错误总数

#### 系统资源指标
- `system_health_score` - 系统健康评分
- `system_cpu_usage` - CPU使用率
- `system_memory_usage` - 内存使用率
- `system_disk_usage` - 磁盘使用率

#### 业务指标
- `business_events_total` - 业务事件总数
- `security_events_total` - 安全事件总数
- `alerts_created_total` - 告警创建总数
- `application_errors_total` - 应用错误总数

### 日志类型

#### HTTP请求日志
```json
{
  "level": "info",
  "message": "HTTP请求完成: GET /api/v1/users - 200 (125ms)",
  "service": "http-server",
  "module": "request-handler",
  "metadata": {
    "method": "GET",
    "url": "/api/v1/users",
    "statusCode": 200,
    "duration": 125
  },
  "tags": ["http", "request", "complete"]
}
```

#### 业务事件日志
```json
{
  "level": "info",
  "message": "业务事件: user_login",
  "service": "business-logic",
  "module": "event-tracker",
  "metadata": {
    "event": "user_login",
    "userId": "user-123",
    "loginMethod": "password"
  },
  "tags": ["business", "event", "medium"]
}
```

#### 安全事件日志
```json
{
  "level": "warn",
  "message": "安全事件: failed_login_attempt",
  "service": "security",
  "module": "security-monitor",
  "metadata": {
    "event": "failed_login_attempt",
    "attempts": 3,
    "ipAddress": "*************"
  },
  "tags": ["security", "event", "high"]
}
```

## 告警配置

### 邮件通知配置

```typescript
alertManager.configureNotifications({
  email: {
    host: 'smtp.gmail.com',
    port: 587,
    secure: false,
    auth: {
      user: '<EMAIL>',
      pass: 'password'
    },
    from: '<EMAIL>',
    to: ['<EMAIL>', '<EMAIL>']
  }
});
```

### 告警规则示例

```typescript
// CPU使用率告警
metricsCollector.addAlertRule({
  id: 'high_cpu_usage',
  name: 'CPU使用率过高',
  metricName: 'system_cpu_usage',
  condition: 'gt',
  threshold: 80,
  duration: 300000, // 5分钟
  severity: AlertSeverity.HIGH,
  enabled: true
});

// 错误率告警
metricsCollector.addAlertRule({
  id: 'high_error_rate',
  name: '错误率过高',
  metricName: 'http_errors_total',
  condition: 'gt',
  threshold: 10,
  duration: 60000, // 1分钟
  severity: AlertSeverity.CRITICAL,
  enabled: true
});
```

### 日志模式告警

```typescript
// 安全事件模式
logAggregator.addLogPattern({
  id: 'security_breach',
  name: '安全漏洞检测',
  pattern: /unauthorized|breach|attack|intrusion/i,
  description: '检测安全相关的异常事件',
  severity: 'critical',
  enabled: true
});

// 数据库错误模式
logAggregator.addLogPattern({
  id: 'database_error',
  name: '数据库错误检测',
  pattern: /database.*error|connection.*failed|query.*timeout/i,
  description: '检测数据库相关的错误',
  severity: 'high',
  enabled: true
});
```

## 使用指南

### 应用日志记录

```typescript
import { appLogger } from '@/middleware/log-collection.middleware';

// 记录业务事件
appLogger.logBusinessEvent('user_registration', {
  userId: 'user-123',
  email: '<EMAIL>',
  registrationMethod: 'email'
}, {
  userId: 'user-123',
  severity: 'medium'
});

// 记录安全事件
appLogger.logSecurityEvent('suspicious_login', {
  reason: 'multiple_failed_attempts',
  attempts: 5,
  timeWindow: '5 minutes'
}, {
  userId: 'user-456',
  ipAddress: '*************',
  severity: 'high'
});

// 记录错误
appLogger.error('支付处理失败', new Error('Payment gateway timeout'), {
  orderId: 'order-789',
  amount: 99.99,
  gateway: 'stripe'
}, {
  service: 'payment-service',
  module: 'payment-processor',
  userId: 'user-123'
});
```

### 自定义指标记录

```typescript
import { metricsCollector } from '@/services/metrics-collector.service';

// 记录业务指标
metricsCollector.incrementCounter('orders_created_total', 1, {
  payment_method: 'credit_card',
  currency: 'USD'
});

// 记录性能指标
metricsCollector.recordHistogram('payment_processing_duration_ms', 1250, {
  gateway: 'stripe',
  currency: 'USD'
});

// 记录状态指标
metricsCollector.setGauge('active_user_sessions', 150, {
  server: 'web-01'
});
```

## 最佳实践

### 1. **日志记录最佳实践**

#### 结构化日志
- 使用结构化的日志格式
- 包含必要的上下文信息
- 避免敏感信息泄露
- 使用合适的日志级别

#### 性能考虑
- 避免过度日志记录
- 使用异步日志写入
- 定期清理历史日志
- 监控日志存储使用

### 2. **指标收集最佳实践**

#### 指标设计
- 选择有意义的指标名称
- 使用适当的标签维度
- 避免高基数标签
- 定期清理无用指标

#### 告警设置
- 设置合理的告警阈值
- 避免告警风暴
- 使用告警分组
- 定期审查告警规则

### 3. **监控系统维护**

#### 定期维护
- 清理过期数据
- 优化查询性能
- 更新告警规则
- 检查系统资源使用

#### 容量规划
- 监控存储使用情况
- 预测数据增长趋势
- 规划系统扩容
- 优化数据保留策略

通过这个完整的监控和日志系统，身份提供商能够实现全面的系统可观测性，及时发现和解决问题，确保系统的稳定运行和最佳性能。
