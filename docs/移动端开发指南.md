# 移动端开发指南

## 📋 概述

ID Provider 移动端应用基于 React Native 和 Expo 开发，提供完整的身份认证功能，包括生物识别登录、离线模式支持、多语言界面和原生性能优化。

## 🏗️ 技术架构

### 核心技术栈
- **框架**: React Native 0.72+ + Expo SDK 49
- **开发语言**: TypeScript
- **状态管理**: Redux Toolkit + React Query
- **导航**: React Navigation v6
- **UI组件**: React Native Paper + 自定义组件
- **表单处理**: React Hook Form
- **国际化**: react-i18next + expo-localization

### 原生功能集成
- **生物识别**: expo-local-authentication
- **安全存储**: expo-secure-store + react-native-keychain
- **相机扫码**: expo-camera + expo-barcode-scanner
- **推送通知**: expo-notifications
- **网络检测**: @react-native-community/netinfo
- **离线存储**: @react-native-async-storage/async-storage

## 🚀 快速开始

### 环境要求
- **Node.js**: 18.0+ (推荐 LTS 版本)
- **Expo CLI**: 最新版本
- **iOS开发**: Xcode 14+ (仅 macOS)
- **Android开发**: Android Studio + Android SDK
- **设备**: iOS 13+ / Android 8.0+ (API 26+)

### 安装和配置

1. **安装依赖**
```bash
cd mobile
npm install
```

2. **配置环境变量**
```bash
# 创建环境配置文件
cp .env.example .env

# 编辑配置
API_URL=https://api.id-provider.example.com
WEB_URL=https://id-provider.example.com
SENTRY_DSN=your-sentry-dsn
```

3. **启动开发服务器**
```bash
# 启动 Expo 开发服务器
npm start

# 在 iOS 模拟器中运行
npm run ios

# 在 Android 模拟器中运行
npm run android

# 在 Web 浏览器中运行
npm run web
```

### 项目结构
```
mobile/
├── src/
│   ├── components/           # 可复用组件
│   │   ├── BiometricLoginButton.tsx
│   │   ├── LoadingOverlay.tsx
│   │   ├── SocialLoginButtons.tsx
│   │   └── ErrorBoundary.tsx
│   ├── screens/              # 页面组件
│   │   ├── auth/             # 认证相关页面
│   │   │   ├── LoginScreen.tsx
│   │   │   ├── RegisterScreen.tsx
│   │   │   └── ForgotPasswordScreen.tsx
│   │   ├── main/             # 主应用页面
│   │   │   ├── HomeScreen.tsx
│   │   │   ├── ProfileScreen.tsx
│   │   │   └── SettingsScreen.tsx
│   │   └── security/         # 安全相关页面
│   ├── navigation/           # 导航配置
│   │   ├── AppNavigator.tsx
│   │   ├── AuthNavigator.tsx
│   │   └── TabNavigator.tsx
│   ├── services/             # 业务服务
│   │   ├── ApiService.ts
│   │   ├── BiometricService.ts
│   │   ├── OfflineManager.ts
│   │   ├── NotificationManager.ts
│   │   └── SecurityService.ts
│   ├── store/                # 状态管理
│   │   ├── slices/
│   │   │   ├── authSlice.ts
│   │   │   ├── userSlice.ts
│   │   │   └── appSlice.ts
│   │   └── index.ts
│   ├── hooks/                # 自定义 Hooks
│   │   ├── useAuth.ts
│   │   ├── useBiometric.ts
│   │   ├── useOffline.ts
│   │   └── redux.ts
│   ├── utils/                # 工具函数
│   │   ├── validation.ts
│   │   ├── encryption.ts
│   │   ├── storage.ts
│   │   └── constants.ts
│   ├── i18n/                 # 国际化配置
│   │   ├── config.ts
│   │   ├── resources/
│   │   └── types.ts
│   ├── theme/                # 主题配置
│   │   ├── colors.ts
│   │   ├── typography.ts
│   │   └── index.ts
│   └── types/                # TypeScript 类型定义
├── assets/                   # 静态资源
│   ├── images/
│   ├── fonts/
│   ├── sounds/
│   └── icons/
├── __tests__/                # 测试文件
├── app.json                  # Expo 配置
├── package.json
└── tsconfig.json
```

## 🔐 核心功能实现

### 1. 生物识别认证

#### 功能特性
- **多种生物识别**: 指纹、Face ID、虹膜识别
- **安全存储**: 加密存储生物识别凭据
- **降级处理**: 生物识别失败时的备用方案
- **设备兼容**: 跨平台兼容性处理

#### 实现示例
```typescript
import { BiometricService } from '../services/BiometricService';

// 检查生物识别可用性
const capabilities = await BiometricService.getInstance().checkCapabilities();

// 执行生物识别认证
const result = await BiometricService.getInstance().authenticate({
  promptMessage: '请验证您的身份',
  cancelLabel: '取消',
  fallbackLabel: '使用密码'
});

if (result.success) {
  // 认证成功，执行登录逻辑
  await handleBiometricLogin();
}
```

### 2. 离线模式支持

#### 功能特性
- **离线缓存**: 关键数据本地缓存
- **操作队列**: 离线操作排队等待同步
- **冲突解决**: 数据同步冲突处理
- **智能同步**: 网络恢复时自动同步

#### 实现示例
```typescript
import { OfflineManager } from '../services/OfflineManager';

// 添加离线操作
await OfflineManager.getInstance().addOfflineAction(
  'UPDATE_PROFILE',
  { name: 'John Doe', email: '<EMAIL>' },
  { maxRetries: 3, priority: 'normal' }
);

// 缓存数据
await OfflineManager.getInstance().cacheData(
  'user_profile',
  userProfile,
  24 * 60 * 60 * 1000 // 24小时TTL
);

// 获取缓存数据
const cachedProfile = await OfflineManager.getInstance().getCachedData('user_profile');
```

### 3. 推送通知

#### 功能特性
- **本地通知**: 应用内通知提醒
- **远程推送**: 服务器推送消息
- **通知分类**: 不同类型通知处理
- **用户偏好**: 通知设置管理

#### 实现示例
```typescript
import { NotificationManager } from '../services/NotificationManager';

// 初始化通知服务
await NotificationManager.initialize();

// 发送本地通知
await NotificationManager.scheduleLocalNotification({
  title: '安全提醒',
  body: '检测到异常登录尝试',
  data: { type: 'security_alert' },
  trigger: { seconds: 5 }
});

// 处理通知点击
NotificationManager.addNotificationResponseReceivedListener(response => {
  const { type } = response.notification.request.content.data;
  // 根据通知类型执行相应操作
});
```

## 📱 平台特定功能

### iOS 特性
- **Face ID / Touch ID**: 原生生物识别集成
- **Keychain Services**: 安全凭据存储
- **App Transport Security**: 网络安全配置
- **Universal Links**: 深度链接支持
- **Background App Refresh**: 后台数据更新

### Android 特性
- **Biometric API**: Android 生物识别框架
- **Android Keystore**: 硬件安全模块
- **Network Security Config**: 网络安全策略
- **App Links**: Android 深度链接
- **Doze Mode**: 电池优化适配

## 🧪 测试策略

### 单元测试
```bash
# 运行单元测试
npm test

# 运行测试并生成覆盖率报告
npm run test:coverage

# 监听模式运行测试
npm run test:watch
```

### 集成测试
```bash
# 运行集成测试
npm run test:integration

# 测试特定功能模块
npm test -- --testPathPattern=auth
```

### E2E 测试
```bash
# 使用 Detox 进行 E2E 测试
npm run test:e2e:ios
npm run test:e2e:android
```

### 性能测试
```bash
# 性能分析
npm run analyze:bundle
npm run analyze:performance
```

## 📦 构建和发布

### 开发构建
```bash
# 创建开发构建
eas build --profile development --platform all

# 安装开发构建到设备
eas build:run --profile development
```

### 预览构建
```bash
# 创建预览构建
eas build --profile preview --platform all

# 分享预览构建
eas build:share
```

### 生产构建
```bash
# 创建生产构建
eas build --profile production --platform all

# 提交到应用商店
eas submit --platform ios
eas submit --platform android
```

### 构建配置 (eas.json)
```json
{
  "cli": {
    "version": ">= 3.0.0"
  },
  "build": {
    "development": {
      "developmentClient": true,
      "distribution": "internal",
      "ios": {
        "resourceClass": "m1-medium"
      }
    },
    "preview": {
      "distribution": "internal",
      "ios": {
        "simulator": true
      }
    },
    "production": {
      "ios": {
        "resourceClass": "m1-medium"
      }
    }
  },
  "submit": {
    "production": {
      "ios": {
        "appleId": "<EMAIL>",
        "ascAppId": "**********",
        "appleTeamId": "ABCDEFGHIJ"
      },
      "android": {
        "serviceAccountKeyPath": "./google-service-account.json",
        "track": "production"
      }
    }
  }
}
```

## 🔒 安全最佳实践

### 数据保护
- **敏感数据加密**: 使用 AES-256 加密存储
- **传输安全**: 强制 HTTPS 和证书固定
- **密钥管理**: 使用硬件安全模块
- **数据清理**: 应用卸载时清理敏感数据

### 代码安全
- **代码混淆**: 生产构建启用代码混淆
- **反调试**: 检测调试器和模拟器
- **完整性检查**: 应用签名验证
- **运行时保护**: 防止代码注入和篡改

### 网络安全
- **证书固定**: 防止中间人攻击
- **API安全**: JWT令牌和OAuth 2.0
- **请求签名**: API请求数字签名
- **流量加密**: 端到端加密通信

## 🌍 国际化支持

### 支持语言
- 中文 (简体) - zh-CN
- 英文 - en-US
- 日文 - ja-JP
- 韩文 - ko-KR

### 本地化配置
```typescript
// i18n/config.ts
import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import * as Localization from 'expo-localization';

import zhCN from './resources/zh-CN.json';
import enUS from './resources/en-US.json';
import jaJP from './resources/ja-JP.json';
import koKR from './resources/ko-KR.json';

i18n
  .use(initReactI18next)
  .init({
    compatibilityJSON: 'v3',
    lng: Localization.locale.split('-')[0],
    fallbackLng: 'en',
    resources: {
      zh: { translation: zhCN },
      en: { translation: enUS },
      ja: { translation: jaJP },
      ko: { translation: koKR }
    },
    interpolation: {
      escapeValue: false
    }
  });
```

## 📊 性能优化

### 启动优化
- **代码分割**: 按需加载组件
- **图片优化**: WebP格式和懒加载
- **字体优化**: 字体子集和预加载
- **Bundle分析**: 减少包体积

### 运行时优化
- **内存管理**: 避免内存泄漏
- **渲染优化**: 使用 React.memo 和 useMemo
- **列表优化**: FlatList 虚拟化
- **动画优化**: 使用原生动画驱动

### 网络优化
- **请求缓存**: HTTP缓存和离线缓存
- **请求合并**: 批量API调用
- **图片压缩**: 自动图片压缩
- **CDN加速**: 静态资源CDN分发

## 🚨 故障排除

### 常见问题

#### 1. 生物识别不工作
- 检查设备是否支持生物识别
- 确认用户已设置生物识别
- 验证权限配置是否正确

#### 2. 离线同步失败
- 检查网络连接状态
- 验证API端点可访问性
- 查看同步队列状态

#### 3. 推送通知不显示
- 确认通知权限已授予
- 检查推送令牌是否有效
- 验证通知配置是否正确

### 调试技巧
```bash
# 启用调试模式
npx expo start --dev-client

# 查看设备日志
npx expo logs --platform ios
npx expo logs --platform android

# 网络调试
npx expo install react-native-flipper
```

## 📚 相关文档

- [Expo 官方文档](https://docs.expo.dev/)
- [React Native 官方文档](https://reactnative.dev/)
- [React Navigation 文档](https://reactnavigation.org/)
- [Redux Toolkit 文档](https://redux-toolkit.js.org/)
- [React Native Paper 文档](https://reactnativepaper.com/)

---

*文档版本: 1.0*  
*最后更新: 2025-08-28*  
*维护团队: ID Provider 移动端开发团队*
