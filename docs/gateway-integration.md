# API网关集成指南

本文档详细说明如何将身份提供商(IdP)与各种主流API网关集成，实现统一的身份认证和授权。

## 概述

身份提供商支持与多种API网关集成，提供以下认证方式：

- **JWT令牌验证**: 网关验证JWT访问令牌
- **OAuth 2.0**: 标准OAuth 2.0授权流程
- **OpenID Connect**: OIDC协议支持
- **令牌内省**: RFC 7662标准令牌内省

## 支持的API网关

| 网关 | JWT | OAuth2 | OIDC | 内省 | JWKS |
|------|-----|--------|------|------|------|
| Kong | ✅ | ✅ | ✅ | ✅ | ✅ |
| Nginx Plus | ✅ | ❌ | ✅ | ✅ | ✅ |
| Traefik | ✅ | ❌ | ✅ | ❌ | ✅ |
| Envoy | ✅ | ✅ | ✅ | ✅ | ✅ |
| Spring Cloud Gateway | ✅ | ✅ | ✅ | ✅ | ✅ |

## 核心端点

### 1. 令牌验证端点
```
POST /api/v1/auth/validate-token
```

**请求体:**
```json
{
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

**响应:**
```json
{
  "valid": true,
  "user": {
    "id": "user-123",
    "email": "<EMAIL>",
    "nickname": "John",
    "roles": ["user"],
    "permissions": ["read:profile", "write:profile"]
  },
  "exp": 1640995200,
  "iat": 1640991600
}
```

### 2. 令牌内省端点 (RFC 7662)
```
POST /api/v1/auth/introspect
```

**请求体:**
```json
{
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type_hint": "access_token"
}
```

**响应:**
```json
{
  "active": true,
  "client_id": "api-gateway",
  "username": "<EMAIL>",
  "scope": "read:profile write:profile",
  "exp": 1640995200,
  "iat": 1640991600,
  "sub": "user-123",
  "aud": "api-gateway",
  "iss": "http://localhost:3000",
  "token_type": "Bearer",
  "roles": ["user"]
}
```

### 3. JWKS端点
```
GET /.well-known/jwks.json
```

**响应:**
```json
{
  "keys": [
    {
      "kty": "RSA",
      "use": "sig",
      "key_ops": ["verify"],
      "alg": "RS256",
      "kid": "key-1",
      "n": "...",
      "e": "AQAB"
    }
  ]
}
```

### 4. OpenID Connect发现端点
```
GET /.well-known/openid-configuration
```

## 集成配置

### 获取网关配置

使用以下API获取特定网关的集成配置：

```bash
# 获取支持的网关列表
curl http://localhost:3000/api/v1/gateway/supported

# 获取Kong网关配置
curl http://localhost:3000/api/v1/gateway/config/kong

# 获取配置示例
curl http://localhost:3000/api/v1/gateway/examples/kong
```

## 具体网关集成示例

### Kong API Gateway

1. **安装JWT插件:**
```bash
curl -X POST http://kong:8001/services/{service}/plugins \
  --data "name=jwt" \
  --data "config.uri_param_names=jwt" \
  --data "config.cookie_names=jwt" \
  --data "config.header_names=authorization" \
  --data "config.claims_to_verify=exp"
```

2. **配置消费者和凭据:**
```bash
# 创建消费者
curl -X POST http://kong:8001/consumers \
  --data "username=api-gateway"

# 添加JWT凭据
curl -X POST http://kong:8001/consumers/api-gateway/jwt \
  --data "key=id-provider" \
  --data "secret=your-jwt-secret"
```

### Nginx Plus

1. **配置JWT验证:**
```nginx
location /api/ {
    auth_jwt "API Gateway";
    auth_jwt_key_file /etc/nginx/jwt.key;
    auth_jwt_claim_set $user $jwt_claim_sub;
    
    proxy_pass http://backend;
    proxy_set_header X-User-ID $user;
}
```

2. **获取公钥:**
```bash
curl http://localhost:3000/.well-known/jwks.json > /etc/nginx/jwt.key
```

### Traefik

1. **配置ForwardAuth中间件:**
```yaml
http:
  middlewares:
    auth-idp:
      forwardAuth:
        address: "http://localhost:3000/api/v1/auth/validate-token"
        authResponseHeaders:
          - "X-User-Id"
          - "X-User-Email"
          - "X-User-Roles"
```

2. **应用到路由:**
```yaml
http:
  routers:
    api:
      rule: "PathPrefix(`/api/`)"
      middlewares:
        - "auth-idp"
      service: "api-service"
```

### Envoy Proxy

1. **配置JWT认证过滤器:**
```yaml
http_filters:
  - name: envoy.filters.http.jwt_authn
    typed_config:
      "@type": type.googleapis.com/envoy.extensions.filters.http.jwt_authn.v3.JwtAuthentication
      providers:
        id-provider:
          issuer: "http://localhost:3000"
          audiences:
            - "api-gateway"
          remote_jwks:
            http_uri:
              uri: "http://localhost:3000/.well-known/jwks.json"
              cluster: "id-provider"
              timeout: 5s
      rules:
        - match:
            prefix: "/api/"
          requires:
            provider_name: "id-provider"
```

### Spring Cloud Gateway

1. **配置OAuth2资源服务器:**
```yaml
spring:
  security:
    oauth2:
      resourceserver:
        jwt:
          issuer-uri: http://localhost:3000
          jwk-set-uri: http://localhost:3000/.well-known/jwks.json
```

2. **配置路由过滤器:**
```yaml
spring:
  cloud:
    gateway:
      routes:
        - id: api-route
          uri: http://backend-service
          predicates:
            - Path=/api/**
          filters:
            - TokenRelay
```

## 测试集成

### 1. 获取访问令牌
```bash
# 用户登录获取令牌
curl -X POST http://localhost:3000/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "<EMAIL>",
    "password": "password123"
  }'
```

### 2. 测试令牌验证
```bash
# 验证令牌
curl -X POST http://localhost:3000/api/v1/auth/validate-token \
  -H "Content-Type: application/json" \
  -d '{
    "token": "YOUR_ACCESS_TOKEN"
  }'
```

### 3. 通过网关访问API
```bash
# 通过网关访问受保护的API
curl -X GET http://gateway:8080/api/protected \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

## 安全考虑

1. **HTTPS通信**: 确保IdP和网关之间使用HTTPS通信
2. **令牌过期**: 合理设置JWT令牌过期时间
3. **密钥轮换**: 定期轮换JWT签名密钥
4. **速率限制**: 在网关层面实施速率限制
5. **日志监控**: 监控认证失败和异常访问

## 故障排除

### 常见问题

1. **令牌验证失败**
   - 检查JWT密钥配置
   - 验证令牌格式和签名
   - 确认时钟同步

2. **JWKS获取失败**
   - 检查网络连接
   - 验证JWKS端点可访问性
   - 检查缓存配置

3. **权限验证失败**
   - 确认用户角色和权限
   - 检查权限映射配置
   - 验证令牌中的权限声明

### 调试工具

```bash
# 检查网关健康状态
curl http://localhost:3000/api/v1/gateway/health/kong

# 测试网关集成
curl -X POST http://localhost:3000/api/v1/gateway/test-integration \
  -H "Authorization: Bearer ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "gatewayType": "kong",
    "testToken": "TEST_TOKEN",
    "gatewayUrl": "http://kong:8001"
  }'
```

## 最佳实践

1. **使用专用的网关应用**: 为每个网关创建专门的应用配置
2. **实施监控**: 监控认证成功率和响应时间
3. **缓存策略**: 合理配置JWKS和用户信息缓存
4. **错误处理**: 实施优雅的错误处理和降级策略
5. **文档维护**: 保持集成文档的更新

## 支持

如需更多帮助，请：

1. 查看API文档: `http://localhost:3000/api/v1/gateway/documentation`
2. 获取配置示例: `http://localhost:3000/api/v1/gateway/examples/{type}`
3. 联系技术支持团队
