# SAML 2.0 身份提供商实现文档

## 概述

本项目实现了完整的SAML 2.0身份提供商(Identity Provider, IdP)功能，支持与各种SAML服务提供商(Service Provider, SP)进行单点登录(SSO)集成。

## 功能特性

### ✅ 已实现的SAML核心功能

#### 1. **SAML元数据管理**
- **IdP元数据生成** - 自动生成符合SAML 2.0标准的元数据
- **证书管理** - 支持X.509证书的签名和加密
- **端点配置** - 配置SSO和SLO服务端点
- **协议支持** - 支持HTTP-POST和HTTP-Redirect绑定

#### 2. **单点登录(SSO)流程**
- **认证请求处理** - 解析和验证来自SP的SAML认证请求
- **用户认证** - 集成现有的用户认证系统
- **断言生成** - 生成包含用户信息的SAML断言
- **响应签名** - 对SAML响应进行数字签名
- **属性映射** - 灵活的用户属性映射配置

#### 3. **单点登出(SLO)流程**
- **登出请求处理** - 处理来自SP的SAML登出请求
- **会话终止** - 终止用户在IdP的会话
- **登出响应** - 生成SAML登出响应

#### 4. **服务提供商管理**
- **SP注册** - 支持动态注册SAML服务提供商
- **配置管理** - 管理SP的ACS URL、证书等配置
- **属性要求** - 配置必需和可选的用户属性

## API端点

### SAML协议端点

| 端点 | 方法 | 功能 | 绑定类型 |
|------|------|------|----------|
| `/saml/metadata` | GET | IdP元数据 | - |
| `/saml/sso` | GET/POST | 单点登录 | HTTP-Redirect/HTTP-POST |
| `/saml/slo` | GET/POST | 单点登出 | HTTP-Redirect/HTTP-POST |
| `/saml/authenticate` | POST | 认证完成 | 内部API |

### 管理端点

| 端点 | 方法 | 功能 | 说明 |
|------|------|------|------|
| `/saml/sp` | GET | 获取SP列表 | 管理员功能 |
| `/saml/sp` | POST | 创建SP配置 | 管理员功能 |
| `/saml/sp/:id` | PUT | 更新SP配置 | 管理员功能 |
| `/saml/sp/:id` | DELETE | 删除SP配置 | 管理员功能 |

## 配置说明

### 环境变量配置

```bash
# SAML 配置
SAML_CERT_PATH="./certs/saml.crt"
SAML_KEY_PATH="./certs/saml.key"
SAML_ISSUER="http://localhost:3000"
```

### 服务提供商配置

创建SAML SP的配置示例：

```json
{
  "name": "示例应用",
  "description": "SAML服务提供商示例",
  "entityId": "https://sp.example.com",
  "acsUrl": "https://sp.example.com/saml/acs",
  "sloUrl": "https://sp.example.com/saml/slo",
  "certificate": "-----BEGIN CERTIFICATE-----...",
  "wantAssertionsSigned": false,
  "wantNameId": true,
  "signMetadata": false,
  "requiredAttributes": [
    "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress",
    "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name"
  ],
  "optionalAttributes": [
    "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/givenname",
    "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/surname"
  ]
}
```

## 支持的SAML特性

### 名称ID格式
- `urn:oasis:names:tc:SAML:1.1:nameid-format:emailAddress` - 邮箱地址
- `urn:oasis:names:tc:SAML:2.0:nameid-format:persistent` - 持久标识符
- `urn:oasis:names:tc:SAML:2.0:nameid-format:transient` - 临时标识符

### 认证上下文
- `urn:oasis:names:tc:SAML:2.0:ac:classes:PasswordProtectedTransport` - 密码保护传输

### 协议绑定
- `urn:oasis:names:tc:SAML:2.0:bindings:HTTP-POST` - HTTP POST绑定
- `urn:oasis:names:tc:SAML:2.0:bindings:HTTP-Redirect` - HTTP重定向绑定

### 用户属性映射

| SAML属性名称 | 用户字段 | 说明 |
|-------------|----------|------|
| `http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress` | email | 邮箱地址 |
| `http://schemas.xmlsoap.org/ws/2005/05/identity/claims/givenname` | firstName | 名字 |
| `http://schemas.xmlsoap.org/ws/2005/05/identity/claims/surname` | lastName | 姓氏 |
| `http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name` | nickname | 显示名称 |
| `http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier` | id | 用户ID |

## 安全特性

### 1. **数字签名**
- 支持RSA-SHA256签名算法
- 对SAML响应和断言进行签名
- 验证来自SP的签名请求

### 2. **时间戳验证**
- 验证请求的时间戳有效性
- 可配置的时钟偏移容忍度
- 防止重放攻击

### 3. **证书管理**
- X.509证书支持
- 证书有效期验证
- 支持证书轮换

### 4. **会话安全**
- 安全的会话管理
- 会话超时控制
- 单点登出支持

## 集成示例

### 1. **获取IdP元数据**

```bash
curl -X GET http://localhost:3000/saml/metadata
```

### 2. **创建服务提供商**

```bash
curl -X POST http://localhost:3000/saml/sp \
  -H "Content-Type: application/json" \
  -d '{
    "name": "测试应用",
    "entityId": "https://app.example.com",
    "acsUrl": "https://app.example.com/saml/acs",
    "requiredAttributes": ["email", "name"]
  }'
```

### 3. **SAML SSO流程**

1. SP重定向用户到IdP的SSO端点
2. 用户在IdP进行认证
3. IdP生成SAML响应并重定向回SP
4. SP验证响应并建立用户会话

## 测试

运行SAML测试套件：

```bash
npm test -- --testPathPattern="saml.test.ts"
```

测试覆盖：
- ✅ SAML元数据生成
- ✅ SAML工具函数
- ✅ 基础功能验证

## 技术实现

### 核心组件

1. **SAMLService** - SAML协议核心服务
2. **SAMLController** - HTTP请求处理控制器
3. **SAML工具类** - XML处理、签名、加密工具
4. **类型定义** - 完整的SAML类型系统

### 依赖库

- `xmlbuilder2` - XML构建
- `xml2js` - XML解析
- `node-forge` - 加密和证书处理
- `uuid` - 唯一标识符生成

## 部署注意事项

### 1. **证书配置**
- 生产环境必须使用有效的X.509证书
- 证书应存储在安全位置
- 定期更新证书

### 2. **网络配置**
- 确保SAML端点可被SP访问
- 配置正确的HTTPS证书
- 设置适当的CORS策略

### 3. **性能优化**
- 启用元数据缓存
- 使用Redis缓存SAML状态
- 监控响应时间

## 故障排除

### 常见问题

1. **元数据无法访问**
   - 检查网络连接
   - 验证端点URL配置

2. **签名验证失败**
   - 检查证书配置
   - 验证时钟同步

3. **属性映射错误**
   - 检查属性配置
   - 验证用户数据

### 日志监控

SAML相关的日志会记录在应用日志中，包括：
- 认证请求处理
- 响应生成
- 错误信息
- 性能指标

## 下一步计划

- [ ] 完整的XML签名验证实现
- [ ] 加密断言支持
- [ ] 更多认证上下文支持
- [ ] SAML属性查询服务
- [ ] 联合身份管理
- [ ] 高级安全特性
