# 身份提供商 (IdP) - 完整 API 文档

| 版本 | 日期       | 作者 | 变更说明 |
| :--- | :--------- | :--- | :------- |
| 1.0  | 2025-07-26 | Gemini | 初始草案 |
| 2.0  | 2025-08-16 | AI Assistant | 完整API文档更新，增加所有端点详细说明 |

## 1.0 概述

本文档定义了身份提供商 (IdP) 的完整 RESTful API。这些 API 旨在供前端界面、移动应用、API网关以及需要以编程方式与 IdP 交互的后端服务使用。所有 API 端点都遵循标准的 HTTP 状态码，并返回 JSON 格式的响应。

### API 基础信息
- **根路径**: `/api/v1`
- **认证方式**: JWT Bearer Token
- **内容类型**: `application/json`
- **字符编码**: UTF-8
- **速率限制**: 各端点有不同的速率限制策略

### 通用响应格式
```json
// 成功响应
{
  "data": { ... },
  "message": "操作成功"
}

// 错误响应
{
  "error": "error_code",
  "message": "错误描述",
  "details": { ... }
}
```

## 2.0 认证 API (Authentication)

这些端点处理用户注册、登录、注销和密码管理。

### 2.1 用户注册
- **端点**: `POST /api/v1/auth/register`
- **描述**: 使用邮箱和密码注册新用户
- **访问权限**: 公开
- **速率限制**: 1小时内最多3次注册
- **请求体**:
  ```json
  {
    "email": "<EMAIL>",
    "password": "strongpassword123",
    "nickname": "用户昵称",
    "firstName": "名",
    "lastName": "姓"
  }
  ```
- **成功响应 (201)**:
  ```json
  {
    "userId": "uuid-1234",
    "status": "pending_verification",
    "message": "注册成功，请检查邮箱验证"
  }
  ```
- **错误响应**:
  - `400`: 请求参数无效
  - `409`: 邮箱已存在
  - `429`: 注册请求过于频繁

### 2.2 用户登录
- **端点**: `POST /api/v1/auth/login`
- **描述**: 使用邮箱/用户名和密码登录
- **访问权限**: 公开
- **速率限制**: 15分钟内最多5次尝试
- **请求体**:
  ```json
  {
    "username": "<EMAIL>",
    "password": "strongpassword123",
    "rememberMe": true
  }
  ```
- **成功响应 (200)**:
  ```json
  {
    "user": {
      "id": "uuid-1234",
      "email": "<EMAIL>",
      "nickname": "用户昵称",
      "isEmailVerified": true
    },
    "tokens": {
      "accessToken": "jwt_access_token",
      "refreshToken": "jwt_refresh_token",
      "expiresIn": 900
    },
    "sessionId": "session-uuid",
    "requiresMfa": false
  }
  ```
- **错误响应**:
  - `401`: 凭据无效
  - `423`: 账户被锁定
  - `429`: 登录尝试过于频繁

### 2.3 用户登出
- **端点**: `POST /api/v1/auth/logout`
- **描述**: 注销当前会话
- **访问权限**: 需要认证
- **请求头**: `Authorization: Bearer <access_token>`
- **请求体**:
  ```json
  {
    "refreshToken": "jwt_refresh_token"
  }
  ```
- **成功响应 (204)**: No Content

### 2.4 刷新访问令牌
- **端点**: `POST /api/v1/auth/refresh-token`
- **描述**: 使用刷新令牌获取新的访问令牌
- **访问权限**: 公开
- **请求体**:
  ```json
  {
    "refreshToken": "jwt_refresh_token"
  }
  ```
- **成功响应 (200)**:
  ```json
  {
    "accessToken": "new_jwt_access_token",
    "expiresIn": 900
  }
  ```

### 2.5 忘记密码
- **端点**: `POST /api/v1/auth/forgot-password`
- **描述**: 启动密码重置流程
- **访问权限**: 公开
- **速率限制**: 1小时内最多3次请求
- **请求体**:
  ```json
  {
    "email": "<EMAIL>"
  }
  ```
- **成功响应 (202)**:
  ```json
  {
    "message": "密码重置邮件已发送"
  }
  ```

### 2.6 重置密码
- **端点**: `POST /api/v1/auth/reset-password`
- **描述**: 使用重置令牌设置新密码
- **访问权限**: 公开
- **请求体**:
  ```json
  {
    "token": "reset_token",
    "newPassword": "newstrongpassword456"
  }
  ```
- **成功响应 (204)**: No Content

### 2.7 修改密码
- **端点**: `POST /api/v1/auth/change-password`
- **描述**: 修改当前用户密码
- **访问权限**: 需要认证
- **速率限制**: 每用户15分钟内最多5次
- **请求体**:
  ```json
  {
    "currentPassword": "oldpassword123",
    "newPassword": "newpassword456"
  }
  ```
- **成功响应 (204)**: No Content

### 2.8 邮箱验证
- **端点**: `GET /api/v1/auth/verify-email`
- **描述**: 验证用户邮箱
- **访问权限**: 公开
- **查询参数**: `?token=verification_token`
- **成功响应 (200)**:
  ```json
  {
    "message": "邮箱验证成功"
  }
  ```

### 2.9 令牌验证 (供API网关使用)
- **端点**: `POST /api/v1/auth/validate-token`
- **描述**: 验证访问令牌并返回用户信息
- **访问权限**: 公开
- **请求体**:
  ```json
  {
    "token": "jwt_access_token"
  }
  ```
- **成功响应 (200)**:
  ```json
  {
    "valid": true,
    "user": {
      "id": "uuid-1234",
      "email": "<EMAIL>",
      "roles": ["user"],
      "permissions": ["read:profile"]
    },
    "exp": **********
  }
  ```

### 2.10 令牌内省 (RFC 7662)
- **端点**: `POST /api/v1/auth/introspect`
- **描述**: RFC 7662标准的令牌内省端点
- **访问权限**: 公开
- **请求体**:
  ```json
  {
    "token": "jwt_access_token",
    "token_type_hint": "access_token"
  }
  ```
- **成功响应 (200)**:
  ```json
  {
    "active": true,
    "scope": "read write",
    "client_id": "client-id",
    "username": "<EMAIL>",
    "exp": **********,
    "iat": **********,
    "sub": "uuid-1234"
  }
  ```

## 3.0 OAuth 第三方登录 API

这些端点处理第三方OAuth提供商的集成登录。

### 3.1 获取OAuth提供商列表
- **端点**: `GET /api/v1/auth/providers`
- **描述**: 获取系统支持的所有OAuth提供商列表
- **访问权限**: 公开
- **成功响应 (200)**:
  ```json
  {
    "providers": [
      {
        "name": "google",
        "displayName": "Google",
        "enabled": true,
        "authUrl": "/api/v1/auth/google"
      },
      {
        "name": "github",
        "displayName": "GitHub",
        "enabled": true,
        "authUrl": "/api/v1/auth/github"
      },
      {
        "name": "wechat",
        "displayName": "微信",
        "enabled": true,
        "authUrl": "/api/v1/auth/wechat"
      },
      {
        "name": "weibo",
        "displayName": "微博",
        "enabled": true,
        "authUrl": "/api/v1/auth/weibo"
      }
    ]
  }
  ```

### 3.2 Google OAuth登录
- **端点**: `GET /api/v1/auth/google`
- **描述**: 发起Google OAuth登录流程
- **访问权限**: 公开
- **响应**: 重定向到Google授权页面

### 3.3 Google OAuth回调
- **端点**: `GET /api/v1/auth/google/callback`
- **描述**: 处理Google OAuth回调
- **访问权限**: 公开
- **查询参数**: `?code=auth_code&state=state_value`
- **成功**: 重定向到前端页面并携带认证信息
- **失败**: 重定向到错误页面

### 3.4 GitHub OAuth登录
- **端点**: `GET /api/v1/auth/github`
- **描述**: 发起GitHub OAuth登录流程
- **访问权限**: 公开
- **响应**: 重定向到GitHub授权页面

### 3.5 GitHub OAuth回调
- **端点**: `GET /api/v1/auth/github/callback`
- **描述**: 处理GitHub OAuth回调
- **访问权限**: 公开
- **查询参数**: `?code=auth_code&state=state_value`
- **成功**: 重定向到前端页面并携带认证信息
- **失败**: 重定向到错误页面

### 3.6 微信OAuth登录
- **端点**: `GET /api/v1/auth/wechat`
- **描述**: 发起微信OAuth登录流程
- **访问权限**: 公开
- **响应**: 重定向到微信授权页面

### 3.7 微信OAuth回调
- **端点**: `GET /api/v1/auth/wechat/callback`
- **描述**: 处理微信OAuth回调
- **访问权限**: 公开
- **查询参数**: `?code=auth_code&state=state_value`
- **成功**: 重定向到前端页面并携带认证信息
- **失败**: 重定向到错误页面

### 3.8 微博OAuth登录
- **端点**: `GET /api/v1/auth/weibo`
- **描述**: 发起微博OAuth登录流程
- **访问权限**: 公开
- **响应**: 重定向到微博授权页面

### 3.9 微博OAuth回调
- **端点**: `GET /api/v1/auth/weibo/callback`
- **描述**: 处理微博OAuth回调
- **访问权限**: 公开
- **查询参数**: `?code=auth_code&state=state_value`
- **成功**: 重定向到前端页面并携带认证信息
- **失败**: 重定向到错误页面

### 3.10 解除OAuth提供商关联
- **端点**: `DELETE /api/v1/auth/disconnect/:provider`
- **描述**: 解除指定OAuth提供商的账户关联
- **访问权限**: 需要认证
- **路径参数**: `provider` - OAuth提供商名称 (google, github, wechat, weibo)
- **成功响应 (204)**: No Content
- **错误响应**:
  - `404`: 未找到关联账户
  - `400`: 无法解除唯一登录方式

## 4.0 用户管理 API

这些端点处理用户资料管理、会话管理和多因素认证。所有端点都需要有效的访问令牌。

### 4.1 获取当前用户信息
- **端点**: `GET /api/v1/me`
- **描述**: 获取当前认证用户的详细信息
- **访问权限**: 需要认证
- **请求头**: `Authorization: Bearer <access_token>`
- **成功响应 (200)**:
  ```json
  {
    "id": "uuid-1234",
    "email": "<EMAIL>",
    "nickname": "用户昵称",
    "firstName": "名",
    "lastName": "姓",
    "avatar": "https://example.com/avatar.jpg",
    "isEmailVerified": true,
    "mfaEnabled": false,
    "roles": ["user"],
    "createdAt": "2023-08-16T10:30:00Z",
    "lastLoginAt": "2023-08-16T15:45:00Z"
  }
  ```

### 4.2 更新用户资料
- **端点**: `PUT /api/v1/me`
- **描述**: 更新当前用户的资料信息
- **访问权限**: 需要认证
- **速率限制**: 每用户15分钟内最多10次
- **请求体**:
  ```json
  {
    "nickname": "新昵称",
    "firstName": "新名",
    "lastName": "新姓",
    "avatar": "https://example.com/new-avatar.jpg"
  }
  ```
- **成功响应 (200)**:
  ```json
  {
    "message": "资料更新成功",
    "user": {
      "id": "uuid-1234",
      "email": "<EMAIL>",
      "nickname": "新昵称",
      "firstName": "新名",
      "lastName": "新姓",
      "avatar": "https://example.com/new-avatar.jpg"
    }
  }
  ```

### 4.3 获取用户会话列表
- **端点**: `GET /api/v1/me/sessions`
- **描述**: 获取当前用户的所有活跃会话
- **访问权限**: 需要认证
- **成功响应 (200)**:
  ```json
  {
    "sessions": [
      {
        "id": "session-uuid-1",
        "deviceInfo": {
          "userAgent": "Mozilla/5.0...",
          "platform": "Windows",
          "browser": "Chrome"
        },
        "ipAddress": "*************",
        "lastAccessedAt": "2023-08-16T15:45:00Z",
        "expiresAt": "2023-08-17T15:45:00Z",
        "isCurrent": true
      }
    ],
    "total": 1
  }
  ```

### 4.4 终止指定会话
- **端点**: `DELETE /api/v1/me/sessions/:sessionId`
- **描述**: 终止指定的用户会话
- **访问权限**: 需要认证
- **速率限制**: 每用户15分钟内最多20次
- **路径参数**: `sessionId` - 会话ID
- **成功响应 (204)**: No Content

### 4.5 终止所有其他会话
- **端点**: `DELETE /api/v1/me/sessions`
- **描述**: 终止除当前会话外的所有其他会话
- **访问权限**: 需要认证
- **速率限制**: 每用户15分钟内最多5次
- **成功响应 (200)**:
  ```json
  {
    "message": "已终止所有其他会话",
    "terminatedSessions": 3
  }
  ```

### 4.6 获取联合身份列表
- **端点**: `GET /api/v1/me/connections`
- **描述**: 获取用户关联的第三方账户列表
- **访问权限**: 需要认证
- **成功响应 (200)**:
  ```json
  {
    "connections": [
      {
        "id": "connection-uuid-1",
        "provider": "google",
        "providerId": "google-user-id",
        "email": "<EMAIL>",
        "name": "用户名",
        "avatar": "https://lh3.googleusercontent.com/...",
        "connectedAt": "2023-08-16T10:30:00Z",
        "lastUsedAt": "2023-08-16T15:45:00Z"
      }
    ],
    "total": 1
  }
  ```

### 4.7 解除联合身份关联
- **端点**: `DELETE /api/v1/me/connections/:connectionId`
- **描述**: 解除指定的第三方账户关联
- **访问权限**: 需要认证
- **速率限制**: 每用户15分钟内最多10次
- **路径参数**: `connectionId` - 连接ID
- **成功响应 (204)**: No Content
- **错误响应**:
  - `400`: 无法解除唯一登录方式
  - `404`: 未找到指定连接

## 5.0 多因素认证 (MFA) API

### 5.1 获取MFA状态
- **端点**: `GET /api/v1/me/mfa`
- **描述**: 获取当前用户的MFA配置状态
- **访问权限**: 需要认证
- **成功响应 (200)**:
  ```json
  {
    "enabled": true,
    "methods": [
      {
        "type": "totp",
        "enabled": true,
        "deviceName": "Google Authenticator"
      },
      {
        "type": "email",
        "enabled": true,
        "email": "<EMAIL>"
      }
    ],
    "backupCodes": {
      "remaining": 8
    }
  }
  ```

### 5.2 启用MFA
- **端点**: `POST /api/v1/me/mfa/enable`
- **描述**: 启用多因素认证
- **访问权限**: 需要认证
- **速率限制**: 每用户15分钟内最多5次
- **请求体**:
  ```json
  {
    "method": "totp",
    "deviceName": "My Phone"
  }
  ```
- **成功响应 (200)**:
  ```json
  {
    "qrCode": "data:image/png;base64,...",
    "secret": "JBSWY3DPEHPK3PXP",
    "backupCodes": [
      "12345678",
      "87654321"
    ]
  }
  ```

### 5.3 验证MFA
- **端点**: `POST /api/v1/me/mfa/verify`
- **描述**: 验证MFA代码
- **访问权限**: 需要认证
- **速率限制**: 每用户15分钟内最多10次
- **请求体**:
  ```json
  {
    "code": "123456",
    "method": "totp"
  }
  ```
- **成功响应 (200)**:
  ```json
  {
    "valid": true,
    "message": "MFA验证成功"
  }
  ```

### 5.4 禁用MFA设备
- **端点**: `DELETE /api/v1/me/mfa/devices/:deviceId`
- **描述**: 禁用指定的MFA设备
- **访问权限**: 需要认证
- **速率限制**: 每用户15分钟内最多10次
- **路径参数**: `deviceId` - 设备ID
- **成功响应 (204)**: No Content

### 5.5 发送邮件验证码
- **端点**: `POST /api/v1/me/mfa/send-email-code`
- **描述**: 发送邮件MFA验证码
- **访问权限**: 需要认证
- **速率限制**: 每用户15分钟内最多5次
- **成功响应 (200)**:
  ```json
  {
    "message": "验证码已发送到邮箱"
  }
  ```

### 5.6 发送短信验证码
- **端点**: `POST /api/v1/me/mfa/send-sms-code`
- **描述**: 发送短信MFA验证码
- **访问权限**: 需要认证
- **速率限制**: 每用户15分钟内最多5次
- **请求体**:
  ```json
  {
    "phoneNumber": "+86138****1234"
  }
  ```
- **成功响应 (200)**:
  ```json
  {
    "message": "验证码已发送到手机"
  }
  ```

## 6.0 API网关集成 API

### 6.1 获取支持的网关类型
- **端点**: `GET /api/v1/gateway/supported`
- **描述**: 获取系统支持的API网关类型列表
- **访问权限**: 公开
- **成功响应 (200)**:
  ```json
  {
    "gateways": [
      {
        "type": "kong",
        "name": "Kong Gateway",
        "features": ["jwt-auth", "oauth2", "rate-limiting"]
      },
      {
        "type": "nginx",
        "name": "Nginx Plus",
        "features": ["jwt-validation", "oauth2-introspection"]
      }
    ],
    "total": 2
  }
  ```

### 6.2 获取网关集成配置
- **端点**: `GET /api/v1/gateway/config/:type`
- **描述**: 获取指定网关类型的集成配置
- **访问权限**: 公开
- **路径参数**: `type` - 网关类型 (kong, nginx, traefik等)
- **查询参数**: `?baseUrl=https://your-idp.com`
- **成功响应 (200)**:
  ```json
  {
    "gateway": "Kong Gateway",
    "type": "kong",
    "configuration": {
      "plugins": [
        {
          "name": "jwt",
          "config": {
            "uri_param_names": ["jwt"],
            "key_claim_name": "iss"
          }
        }
      ]
    },
    "endpoints": {
      "jwks": "https://your-idp.com/.well-known/jwks.json",
      "introspection": "https://your-idp.com/api/v1/auth/introspect"
    }
  }
  ```

### 6.3 获取集成配置示例
- **端点**: `GET /api/v1/gateway/examples/:type`
- **描述**: 获取网关集成的详细配置示例
- **访问权限**: 公开
- **路径参数**: `type` - 网关类型
- **成功响应 (200)**:
  ```json
  {
    "gateway": "Kong Gateway",
    "type": "kong",
    "examples": {
      "curl": "curl -X POST http://kong:8001/services/my-service/plugins ...",
      "yaml": "plugins:\n  - name: jwt\n    config: ..."
    },
    "documentation": {
      "description": "Kong Gateway 集成配置示例",
      "endpoints": {...},
      "authentication": {...}
    }
  }
  ```

### 6.4 测试网关集成
- **端点**: `POST /api/v1/gateway/test`
- **描述**: 测试网关集成配置是否正常工作
- **访问权限**: 公开
- **请求体**:
  ```json
  {
    "gatewayType": "kong",
    "testToken": "jwt_token_to_test",
    "gatewayUrl": "https://your-gateway.com"
  }
  ```
- **成功响应 (200)**:
  ```json
  {
    "success": true,
    "message": "集成测试成功",
    "details": {
      "tokenValid": true,
      "userInfo": {...},
      "responseTime": 150
    }
  }
  ```

### 6.5 获取网关集成文档
- **端点**: `GET /api/v1/gateway/documentation`
- **描述**: 获取完整的网关集成文档
- **访问权限**: 公开
- **成功响应 (200)**:
  ```json
  {
    "title": "身份提供商 - API网关集成指南",
    "version": "1.0.0",
    "description": "本文档描述如何将身份提供商与各种API网关集成",
    "overview": {...},
    "endpoints": {...},
    "integrationSteps": [...],
    "examples": {...}
  }
  ```

## 7.0 OpenID Connect & JWKS 端点

### 7.1 OpenID Connect 发现端点
- **端点**: `GET /.well-known/openid-configuration`
- **描述**: OpenID Connect 发现端点，返回IdP的配置信息
- **访问权限**: 公开
- **成功响应 (200)**:
  ```json
  {
    "issuer": "https://your-idp.com",
    "authorization_endpoint": "https://your-idp.com/oauth2/authorize",
    "token_endpoint": "https://your-idp.com/oauth2/token",
    "userinfo_endpoint": "https://your-idp.com/oauth2/userinfo",
    "jwks_uri": "https://your-idp.com/.well-known/jwks.json",
    "response_types_supported": ["code", "token", "id_token"],
    "subject_types_supported": ["public"],
    "id_token_signing_alg_values_supported": ["RS256", "HS256"]
  }
  ```

### 7.2 JSON Web Key Set (JWKS)
- **端点**: `GET /.well-known/jwks.json`
- **描述**: 获取用于验证JWT令牌的公钥集合
- **访问权限**: 公开
- **成功响应 (200)**:
  ```json
  {
    "keys": [
      {
        "kty": "RSA",
        "use": "sig",
        "kid": "key-id-1",
        "n": "public-key-modulus",
        "e": "AQAB"
      }
    ]
  }
  ```

## 8.0 非标准应用集成 API

### 8.1 获取协议适配器列表
- **端点**: `GET /nsa/adapters`
- **描述**: 获取可用的协议适配器列表
- **访问权限**: 公开
- **成功响应 (200)**:
  ```json
  {
    "adapters": [
      {
        "id": "ldap-adapter",
        "name": "LDAP协议适配器",
        "version": "1.0.0",
        "protocols": ["ldap", "ldaps"],
        "status": "active"
      }
    ]
  }
  ```

### 8.2 应用认证 (非标准)
- **端点**: `POST /nsa/auth`
- **描述**: 为非标准应用提供认证服务
- **访问权限**: 需要应用密钥
- **请求体**:
  ```json
  {
    "appId": "legacy-app-1",
    "protocol": "ldap",
    "credentials": {
      "username": "<EMAIL>",
      "password": "password123"
    }
  }
  ```
- **成功响应 (200)**:
  ```json
  {
    "success": true,
    "user": {
      "dn": "cn=user,ou=users,dc=example,dc=com",
      "attributes": {
        "cn": "用户名",
        "mail": "<EMAIL>"
      }
    },
    "token": "legacy-token"
  }
  ```

### 8.3 获取插件列表 (管理员)
- **端点**: `GET /nsa/admin/plugins`
- **描述**: 获取已加载的插件和适配器列表
- **访问权限**: 需要管理员权限
- **成功响应 (200)**:
  ```json
  {
    "loaded_plugins": [...],
    "available_adapters": [...],
    "available_handlers": [...]
  }
  ```

## 9.0 系统健康检查 API

### 9.1 基础健康检查
- **端点**: `GET /health`
- **描述**: 基础的系统健康状态检查
- **访问权限**: 公开
- **成功响应 (200)**:
  ```json
  {
    "status": "ok",
    "timestamp": "2023-08-16T15:45:00Z",
    "uptime": 3600,
    "service": "id-provider"
  }
  ```

### 9.2 详细健康检查
- **端点**: `GET /api/v1/health`
- **描述**: 详细的系统健康状态检查，包含数据库连接状态
- **访问权限**: 公开
- **成功响应 (200)**:
  ```json
  {
    "status": "ok",
    "timestamp": "2023-08-16T15:45:00Z",
    "uptime": 3600,
    "database": "connected",
    "memory": {
      "rss": 52428800,
      "heapTotal": 29360128,
      "heapUsed": 20537304
    },
    "version": "v18.17.0",
    "service": "id-provider",
    "apiVersion": "v1"
  }
  ```

## 10.0 通用错误代码

所有API端点都可能返回以下通用错误代码：

### HTTP状态码说明
- **200 OK**: 请求成功
- **201 Created**: 资源创建成功
- **204 No Content**: 请求成功，无返回内容
- **400 Bad Request**: 请求参数错误
- **401 Unauthorized**: 未认证或认证失败
- **403 Forbidden**: 权限不足
- **404 Not Found**: 资源不存在
- **409 Conflict**: 资源冲突（如邮箱已存在）
- **422 Unprocessable Entity**: 请求格式正确但语义错误
- **423 Locked**: 账户被锁定
- **429 Too Many Requests**: 请求过于频繁
- **500 Internal Server Error**: 服务器内部错误
- **503 Service Unavailable**: 服务不可用

### 常见错误代码
```json
{
  "error": "error_code",
  "message": "错误描述",
  "details": {
    "field": "具体错误字段",
    "code": "详细错误代码"
  }
}
```

#### 认证相关错误
- `invalid_credentials`: 用户名或密码错误
- `account_locked`: 账户被锁定
- `email_not_verified`: 邮箱未验证
- `mfa_required`: 需要多因素认证
- `invalid_token`: 令牌无效或已过期
- `token_expired`: 令牌已过期

#### 速率限制错误
- `rate_limit_exceeded`: 请求过于频繁
- `login_rate_limit_exceeded`: 登录尝试过于频繁
- `register_rate_limit_exceeded`: 注册请求过于频繁
- `too_many_attempts`: 尝试次数过多
- `too_many_password_resets`: 密码重置请求过于频繁

#### 验证错误
- `validation_failed`: 数据验证失败
- `password_too_weak`: 密码强度不足
- `email_invalid`: 邮箱格式无效
- `required_field_missing`: 必填字段缺失

#### OAuth相关错误
- `oauth_error`: OAuth认证失败
- `oauth_denied`: OAuth授权被拒绝
- `oauth_callback_error`: OAuth回调处理失败
- `invalid_state`: OAuth状态参数无效
- `provider_not_supported`: 不支持的OAuth提供商

#### 资源错误
- `user_not_found`: 用户不存在
- `session_not_found`: 会话不存在
- `connection_not_found`: 连接不存在
- `resource_not_found`: 资源不存在

#### 系统错误
- `server_error`: 服务器内部错误
- `database_error`: 数据库连接错误
- `service_unavailable`: 服务不可用
- `maintenance_mode`: 系统维护中

## 11.0 API使用示例

### 11.1 完整的用户注册和登录流程

```javascript
// 1. 用户注册
const registerResponse = await fetch('/api/v1/auth/register', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    email: '<EMAIL>',
    password: 'StrongPassword123!',
    nickname: '用户昵称'
  })
});

// 2. 用户登录
const loginResponse = await fetch('/api/v1/auth/login', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    username: '<EMAIL>',
    password: 'StrongPassword123!'
  })
});

const loginData = await loginResponse.json();
const { accessToken, refreshToken } = loginData.tokens;

// 3. 使用访问令牌获取用户信息
const userResponse = await fetch('/api/v1/me', {
  headers: {
    'Authorization': `Bearer ${accessToken}`
  }
});

// 4. 刷新访问令牌
const refreshResponse = await fetch('/api/v1/auth/refresh-token', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    refreshToken: refreshToken
  })
});
```

### 11.2 OAuth第三方登录流程

```javascript
// 1. 获取OAuth提供商列表
const providersResponse = await fetch('/api/v1/auth/providers');
const providers = await providersResponse.json();

// 2. 重定向到Google OAuth
window.location.href = '/api/v1/auth/google';

// 3. OAuth回调会自动处理，成功后重定向到前端页面
// 前端页面可以从URL参数或localStorage中获取认证信息
```

### 11.3 API网关集成示例

```bash
# 获取Kong网关集成配置
curl -X GET "https://your-idp.com/api/v1/gateway/config/kong?baseUrl=https://your-idp.com"

# 验证JWT令牌
curl -X POST "https://your-idp.com/api/v1/auth/validate-token" \
  -H "Content-Type: application/json" \
  -d '{"token": "your-jwt-token"}'

# 令牌内省
curl -X POST "https://your-idp.com/api/v1/auth/introspect" \
  -H "Content-Type: application/json" \
  -d '{"token": "your-jwt-token"}'
```

## 12.0 安全最佳实践

### 12.1 令牌管理
- 访问令牌有效期较短（15分钟），减少泄露风险
- 刷新令牌有效期较长（7天），用于获取新的访问令牌
- 令牌应存储在安全的地方（如HttpOnly Cookie）
- 定期轮换刷新令牌

### 12.2 速率限制
- 所有敏感操作都有速率限制
- 登录失败会触发账户锁定机制
- 使用IP和用户双重限制策略

### 12.3 数据验证
- 所有输入数据都经过严格验证
- 密码强度要求：至少8位，包含大小写字母、数字和特殊字符
- 邮箱格式验证和域名检查

### 12.4 安全头
- 所有响应都包含安全头
- CORS策略严格控制允许的源
- CSP策略防止XSS攻击

---

**文档版本**: 2.0
**最后更新**: 2023-08-16
**联系方式**: 如有问题请联系技术支持
