# 多因素认证(MFA)功能文档

## 1. 概述

多因素认证(Multi-Factor Authentication, MFA)是身份提供商的核心安全功能之一，为用户账户提供额外的安全保护层。本系统支持多种MFA方法，包括TOTP、邮件验证码和短信验证码。

## 2. 支持的MFA方法

### 2.1 TOTP (Time-based One-Time Password)

**特点:**
- 基于时间的一次性密码
- 兼容Google Authenticator、Microsoft Authenticator等应用
- 离线工作，无需网络连接
- 30秒时间窗口，6位数字验证码

**实现细节:**
- 使用speakeasy库生成TOTP密钥
- 支持QR码扫描设置
- 提供备用恢复码
- 允许±2个时间窗口的时钟偏差

### 2.2 邮件验证码

**特点:**
- 通过邮件发送6位数字验证码
- 5分钟有效期
- 支持自定义邮件模板

**实现细节:**
- 使用nodemailer发送邮件
- 验证码存储在数据库中
- 支持重发机制
- 防止频繁发送

### 2.3 短信验证码

**特点:**
- 通过短信发送6位数字验证码
- 5分钟有效期
- 支持国际手机号

**实现细节:**
- 使用Twilio发送短信
- 验证码存储在数据库中
- 支持重发机制
- 防止频繁发送

## 3. 数据模型

### 3.1 MFA设备表 (mfa_devices)

```sql
CREATE TABLE mfa_devices (
  id UUID PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES users(id),
  type VARCHAR(10) NOT NULL, -- 'totp', 'email', 'sms'
  name VARCHAR(100) NOT NULL, -- 用户自定义设备名称
  secret TEXT, -- TOTP密钥（加密存储）
  backup_codes TEXT[], -- 备用恢复码（加密存储）
  phone_number VARCHAR(20), -- 短信MFA的手机号
  email_address VARCHAR(255), -- 邮件MFA的邮箱
  is_active BOOLEAN DEFAULT true,
  is_verified BOOLEAN DEFAULT false,
  last_used_at TIMESTAMP,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

### 3.2 验证码表 (verification_codes)

```sql
-- TODO: 需要创建此表
CREATE TABLE verification_codes (
  id UUID PRIMARY KEY,
  device_id UUID NOT NULL REFERENCES mfa_devices(id),
  code VARCHAR(10) NOT NULL,
  type VARCHAR(10) NOT NULL, -- 'email', 'sms'
  expires_at TIMESTAMP NOT NULL,
  used_at TIMESTAMP,
  created_at TIMESTAMP DEFAULT NOW()
);
```

## 4. API接口

### 4.1 获取MFA状态

```http
GET /api/v1/me/mfa
Authorization: Bearer <access_token>
```

**响应:**
```json
{
  "isEnabled": true,
  "methods": ["totp", "email"],
  "devices": [
    {
      "id": "device-uuid",
      "type": "totp",
      "name": "我的认证器",
      "isVerified": true,
      "lastUsedAt": "2025-07-26T10:00:00Z",
      "createdAt": "2025-07-26T09:00:00Z"
    }
  ]
}
```

### 4.2 启用TOTP MFA

```http
POST /api/v1/me/mfa/enable
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "method": "totp",
  "name": "我的认证器"
}
```

**响应:**
```json
{
  "setupKey": "JBSWY3DPEHPK3PXP",
  "qrCodeUri": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...",
  "backupCodes": [
    "A1B2C3D4",
    "E5F6G7H8",
    "..."
  ],
  "message": "请使用认证器应用扫描二维码，然后验证以完成设置"
}
```

### 4.3 启用邮件MFA

```http
POST /api/v1/me/mfa/enable
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "method": "email",
  "name": "工作邮箱",
  "emailAddress": "<EMAIL>"
}
```

### 4.4 启用短信MFA

```http
POST /api/v1/me/mfa/enable
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "method": "sms",
  "name": "我的手机",
  "phoneNumber": "+8613800138000"
}
```

### 4.5 验证MFA

```http
POST /api/v1/me/mfa/verify
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "deviceId": "device-uuid",
  "code": "123456"
}
```

**响应:**
```json
{
  "success": true,
  "message": "MFA验证成功"
}
```

### 4.6 发送邮件验证码

```http
POST /api/v1/me/mfa/send-email-code
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "deviceId": "device-uuid"
}
```

### 4.7 发送短信验证码

```http
POST /api/v1/me/mfa/send-sms-code
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "deviceId": "device-uuid"
}
```

### 4.8 禁用MFA设备

```http
DELETE /api/v1/me/mfa/devices/{deviceId}
Authorization: Bearer <access_token>
```

## 5. 安全考虑

### 5.1 数据保护

- **密钥加密**: TOTP密钥使用AES加密存储
- **备用码加密**: 备用恢复码加密存储
- **验证码过期**: 邮件和短信验证码有时间限制
- **使用记录**: 记录MFA设备的使用时间

### 5.2 防护措施

- **速率限制**: 限制验证码发送频率
- **尝试限制**: 限制验证失败次数
- **时间窗口**: TOTP允许合理的时钟偏差
- **审计日志**: 记录所有MFA相关操作

### 5.3 备用恢复

- **恢复码**: 提供一次性备用恢复码
- **管理员重置**: 管理员可以重置用户MFA
- **邮件恢复**: 通过邮件验证重置MFA

## 6. 用户体验

### 6.1 设置流程

1. 用户选择MFA方法
2. 系统生成设置信息（TOTP密钥/QR码）
3. 用户配置认证器应用或提供联系方式
4. 用户输入验证码完成设置
5. 系统提供备用恢复码（TOTP）

### 6.2 登录流程

1. 用户输入用户名和密码
2. 系统检查是否启用MFA
3. 如果启用，要求输入MFA验证码
4. 验证成功后完成登录

### 6.3 管理界面

- 查看已启用的MFA设备
- 添加新的MFA设备
- 禁用不需要的设备
- 查看使用历史
- 下载备用恢复码

## 7. 集成指南

### 7.1 前端集成

```javascript
// 启用TOTP MFA
const enableTotp = async (name) => {
  const response = await fetch('/api/v1/me/mfa/enable', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${accessToken}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      method: 'totp',
      name
    })
  });
  
  const data = await response.json();
  // 显示QR码和备用恢复码
  showQRCode(data.qrCodeUri);
  showBackupCodes(data.backupCodes);
};

// 验证MFA
const verifyMfa = async (deviceId, code) => {
  const response = await fetch('/api/v1/me/mfa/verify', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${accessToken}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      deviceId,
      code
    })
  });
  
  return response.json();
};
```

### 7.2 移动应用集成

- 支持相机扫描QR码
- 集成认证器应用
- 支持生物识别验证
- 离线验证支持

## 8. 故障排除

### 8.1 常见问题

**Q: TOTP验证码总是失败**
A: 检查设备时间是否同步，TOTP依赖准确的时间

**Q: 收不到邮件验证码**
A: 检查垃圾邮件文件夹，确认邮箱地址正确

**Q: 收不到短信验证码**
A: 确认手机号格式正确，检查网络状况

**Q: 备用恢复码在哪里**
A: 在首次设置TOTP时提供，建议安全保存

### 8.2 错误代码

- `mfa_device_not_found`: MFA设备不存在
- `mfa_verification_failed`: MFA验证失败
- `mfa_code_expired`: 验证码已过期
- `mfa_rate_limit_exceeded`: 请求过于频繁

## 9. 最佳实践

### 9.1 用户建议

- 至少启用一种MFA方法
- 安全保存备用恢复码
- 定期检查MFA设备状态
- 及时更新联系方式

### 9.2 管理员建议

- 为敏感应用强制启用MFA
- 定期审查MFA使用情况
- 监控异常MFA活动
- 提供用户培训和支持
