# CDN集成指南

## 📋 概述

本项目集成了完整的CDN（内容分发网络）解决方案，支持静态资源的全球加速分发。CDN集成提供了自动资源重写、缓存控制、版本管理和性能优化功能。

## 🏗️ 系统架构

### 核心组件
- **CDNService**: CDN服务管理核心
- **CDN中间件**: 资源重写和缓存控制
- **资源版本管理**: 自动版本号生成和缓存控制
- **多CDN提供商支持**: Cloudflare、AWS CloudFront、阿里云CDN

### 支持的资源类型
- **JavaScript文件** (.js, .mjs): 长期缓存，版本化
- **CSS文件** (.css): 长期缓存，版本化
- **图片文件** (.png, .jpg, .jpeg, .gif, .svg, .webp, .ico): 中期缓存
- **字体文件** (.woff, .woff2, .ttf, .eot): 长期缓存
- **其他静态资源** (.json, .xml, .txt): 短期缓存

## 🚀 快速开始

### 环境变量配置

在 `.env` 文件中配置CDN相关参数：

```bash
# CDN基础配置
CDN_ENABLED=true
CDN_PROVIDER=cloudflare
CDN_BASE_URL=https://cdn.example.com

# Cloudflare配置
CDN_API_KEY=your_cloudflare_api_key
CDN_ZONE_ID=your_zone_id

# AWS CloudFront配置
CDN_BUCKET_NAME=your_s3_bucket
CDN_REGION=us-east-1
CDN_SECRET_KEY=your_aws_secret

# 阿里云CDN配置
CDN_API_KEY=your_aliyun_access_key
CDN_SECRET_KEY=your_aliyun_secret

# 缓存配置
CDN_CACHE_CONTROL="public, max-age=31536000"
CDN_MAX_AGE=31536000
CDN_CUSTOM_HEADERS='{"X-Custom-Header": "value"}'
```

### 启用CDN

CDN服务会在应用启动时自动初始化：

```typescript
// CDN服务自动加载配置并初始化
import { cdnService } from '@/services/cdn.service';

// 检查CDN状态
const stats = await cdnService.getStatistics();
console.log('CDN状态:', stats);
```

## 🔧 配置选项

### CDN提供商配置

#### Cloudflare
```bash
CDN_PROVIDER=cloudflare
CDN_BASE_URL=https://your-domain.com
CDN_API_KEY=your_api_token
CDN_ZONE_ID=your_zone_id
```

#### AWS CloudFront
```bash
CDN_PROVIDER=aws
CDN_BASE_URL=https://d123456789.cloudfront.net
CDN_BUCKET_NAME=your-s3-bucket
CDN_REGION=us-east-1
CDN_API_KEY=your_access_key
CDN_SECRET_KEY=your_secret_key
```

#### 阿里云CDN
```bash
CDN_PROVIDER=aliyun
CDN_BASE_URL=https://your-domain.com
CDN_API_KEY=your_access_key
CDN_SECRET_KEY=your_secret_key
```

#### 自定义CDN
```bash
CDN_PROVIDER=custom
CDN_BASE_URL=https://your-custom-cdn.com
```

### 资源类型配置

系统预配置了不同类型资源的缓存策略：

```typescript
// JavaScript和CSS文件 - 长期缓存
{
  cacheControl: 'public, max-age=31536000, immutable',
  maxAge: 31536000, // 1年
  versioning: true
}

// 图片文件 - 中期缓存
{
  cacheControl: 'public, max-age=2592000', // 30天
  maxAge: 2592000,
  versioning: false
}

// 字体文件 - 长期缓存
{
  cacheControl: 'public, max-age=31536000, immutable',
  maxAge: 31536000,
  versioning: false
}
```

## 📊 API接口

### 获取CDN状态
```http
GET /api/v1/cdn/status
Authorization: Bearer <token>
```

### 获取资源CDN URL
```http
GET /api/v1/cdn/asset-url?path=/assets/app.js
Authorization: Bearer <token>
```

### 清除CDN缓存
```http
POST /api/v1/cdn/purge
Content-Type: application/json
Authorization: Bearer <token>

{
  "paths": ["/assets/app.js", "/assets/style.css"]
}
```

### 预热CDN缓存
```http
POST /api/v1/cdn/warmup
Content-Type: application/json
Authorization: Bearer <token>

{
  "paths": ["/assets/app.js", "/assets/style.css"]
}
```

### 获取CDN统计
```http
GET /api/v1/cdn/statistics?period=24h
Authorization: Bearer <token>
```

## 🛠️ 前端集成

### 自动资源重写

CDN中间件会自动重写HTML中的资源URL：

```html
<!-- 原始HTML -->
<link rel="stylesheet" href="/assets/style.css">
<script src="/assets/app.js"></script>
<img src="/images/logo.png" alt="Logo">

<!-- CDN重写后 -->
<link rel="stylesheet" href="https://cdn.example.com/assets/style.abc123.css">
<script src="https://cdn.example.com/assets/app.def456.js"></script>
<img src="https://cdn.example.com/images/logo.png" alt="Logo">
```

### 资源预加载

系统支持自动添加资源预加载提示：

```html
<head>
  <!-- 自动添加的预加载提示 -->
  <link rel="preload" href="https://cdn.example.com/assets/app.js" as="script">
  <link rel="preload" href="https://cdn.example.com/assets/style.css" as="style">
  <link rel="preload" href="https://cdn.example.com/fonts/font.woff2" as="font" crossorigin>
</head>
```

### 构建配置

Vite构建配置已优化支持CDN：

```typescript
// vite.config.ts
export default defineConfig({
  build: {
    rollupOptions: {
      output: {
        // 文件名包含hash
        entryFileNames: 'assets/[name].[hash].js',
        chunkFileNames: 'assets/[name].[hash].js',
        assetFileNames: 'assets/[name].[hash].[ext]'
      }
    },
    // 生成资源清单
    manifest: true
  }
});
```

## 📈 性能优化

### 缓存策略

1. **长期缓存** (1年)
   - JavaScript和CSS文件
   - 字体文件
   - 使用文件hash确保更新时缓存失效

2. **中期缓存** (30天)
   - 图片文件
   - 图标文件

3. **短期缓存** (1天)
   - JSON配置文件
   - 其他动态内容

### 版本管理

系统自动为资源生成版本号：

```typescript
// 基于文件内容的MD5 hash
const version = generateAssetVersion('/assets/app.js');
// 输出: "abc12345"

// 版本化URL
const versionedUrl = `/assets/app.abc12345.js`;
```

### 压缩优化

支持的压缩类型：
- **Gzip**: 文本文件压缩
- **Brotli**: 现代浏览器支持
- **图片优化**: WebP格式支持

## 🔍 监控和统计

### 性能指标

系统自动收集以下指标：

- **请求统计**: 总请求数、CDN命中数、本地命中数
- **带宽统计**: 总带宽、节省带宽
- **性能指标**: 平均响应时间、缓存命中率、错误率
- **热门资源**: 访问量最高的资源
- **错误分析**: 按类型分类的错误统计

### 健康检查

```bash
# 检查CDN健康状态
curl /api/v1/cdn/health

# 响应示例
{
  "success": true,
  "data": {
    "healthy": true,
    "enabled": true,
    "provider": "cloudflare",
    "baseUrl": "https://cdn.example.com"
  }
}
```

## 🚨 故障排除

### 常见问题

#### 1. CDN未生效
- 检查 `CDN_ENABLED` 环境变量
- 验证 `CDN_BASE_URL` 配置
- 确认DNS解析正确

#### 2. 资源404错误
- 检查资源是否已上传到CDN
- 验证资源路径映射
- 确认缓存清除是否完成

#### 3. 缓存未更新
- 手动清除CDN缓存
- 检查资源版本号是否变化
- 验证缓存控制头设置

### 调试技巧

1. **启用调试模式**
```bash
NODE_ENV=development npm start
```

2. **查看CDN统计**
```bash
curl /api/v1/cdn/statistics
```

3. **测试资源URL**
```bash
curl "/api/v1/cdn/asset-url?path=/assets/app.js"
```

## 📚 最佳实践

### 1. 资源组织
- 将静态资源放在 `/assets/` 目录
- 使用语义化的文件名
- 分离第三方库和业务代码

### 2. 缓存策略
- 为不同类型资源设置合适的缓存时间
- 使用版本号确保更新时缓存失效
- 定期清理过期缓存

### 3. 性能监控
- 定期检查CDN健康状态
- 监控缓存命中率
- 分析热门资源访问模式

### 4. 安全考虑
- 使用HTTPS传输
- 设置适当的CORS策略
- 定期更新CDN访问密钥

## 🔧 扩展开发

### 添加新的CDN提供商

```typescript
// 扩展CDN服务
class CustomCDNProvider {
  async purgeCache(paths: string[]): Promise<void> {
    // 实现自定义CDN的缓存清除逻辑
  }
  
  async uploadAsset(path: string, content: Buffer): Promise<void> {
    // 实现资源上传逻辑
  }
}
```

### 自定义缓存策略

```typescript
// 添加新的资源类型配置
assetTypes.set('videos', {
  extensions: ['.mp4', '.webm', '.ogg'],
  cacheControl: 'public, max-age=604800', // 7天
  maxAge: 604800,
  compress: false,
  versioning: false
});
```

---

*文档版本: 1.0*  
*最后更新: 2025-08-28*
