# OpenID Connect Provider 实现指南

## 概述

本身份提供商系统实现了完整的 OpenID Connect Provider 功能，支持标准的 OAuth 2.0 和 OpenID Connect 协议。

## 支持的功能

### OAuth 2.0 授权类型
- ✅ **Authorization Code Flow** - 授权码流程
- ✅ **Refresh Token** - 刷新令牌
- ✅ **Client Credentials** - 客户端凭据流程
- ✅ **Token Introspection** - 令牌内省 (RFC 7662)
- ✅ **Token Revocation** - 令牌撤销 (RFC 7009)

### OpenID Connect 功能
- ✅ **ID Token** - 身份令牌生成和验证
- ✅ **UserInfo Endpoint** - 用户信息端点
- ✅ **Discovery** - 自动发现端点
- ✅ **JWKS** - JSON Web Key Set
- ✅ **Multiple Response Types** - 多种响应类型支持

### 安全特性
- ✅ **PKCE** - Proof Key for Code Exchange (RFC 7636)
- ✅ **State Parameter** - 状态参数验证
- ✅ **Nonce** - 随机数支持
- ✅ **JWT Signature** - RS256 签名算法
- ✅ **Token Blacklisting** - 令牌黑名单
- ✅ **Rate Limiting** - 速率限制

## API 端点

### 核心 OIDC 端点

#### 1. 授权端点
```
GET /oauth2/authorize
```

**参数:**
- `response_type` (必需): 响应类型 (`code`, `token`, `id_token` 等)
- `client_id` (必需): 客户端ID
- `redirect_uri` (必需): 重定向URI
- `scope` (必需): 权限范围 (必须包含 `openid`)
- `state` (推荐): 状态参数
- `nonce` (可选): 随机数 (用于ID令牌)
- `code_challenge` (可选): PKCE代码挑战
- `code_challenge_method` (可选): PKCE挑战方法 (`S256` 或 `plain`)
- `prompt` (可选): 提示参数 (`none`, `login`, `consent`)
- `max_age` (可选): 最大认证年龄

**示例:**
```
GET /oauth2/authorize?response_type=code&client_id=client_abc123&redirect_uri=https://app.example.com/callback&scope=openid%20profile%20email&state=xyz789
```

#### 2. 令牌端点
```
POST /oauth2/token
```

**参数:**
- `grant_type` (必需): 授权类型
- `code` (授权码流程): 授权码
- `redirect_uri` (授权码流程): 重定向URI
- `client_id` (必需): 客户端ID
- `client_secret` (必需): 客户端密钥
- `code_verifier` (PKCE): 代码验证器
- `refresh_token` (刷新令牌流程): 刷新令牌

**响应:**
```json
{
  "access_token": "eyJhbGciOiJSUzI1NiIs...",
  "token_type": "Bearer",
  "expires_in": 3600,
  "refresh_token": "def50200...",
  "id_token": "eyJhbGciOiJSUzI1NiIs...",
  "scope": "openid profile email"
}
```

#### 3. 用户信息端点
```
GET /oauth2/userinfo
Authorization: Bearer {access_token}
```

**响应:**
```json
{
  "sub": "user-123",
  "email": "<EMAIL>",
  "email_verified": true,
  "name": "John Doe",
  "nickname": "john",
  "picture": "https://example.com/avatar.jpg",
  "given_name": "John",
  "family_name": "Doe",
  "updated_at": 1640995200
}
```

#### 4. 令牌撤销端点
```
POST /oauth2/revoke
```

**参数:**
- `token` (必需): 要撤销的令牌
- `token_type_hint` (可选): 令牌类型提示
- `client_id` (必需): 客户端ID
- `client_secret` (必需): 客户端密钥

#### 5. 令牌内省端点
```
POST /oauth2/introspect
```

**参数:**
- `token` (必需): 要内省的令牌
- `token_type_hint` (可选): 令牌类型提示
- `client_id` (可选): 客户端ID
- `client_secret` (可选): 客户端密钥

**响应:**
```json
{
  "active": true,
  "scope": "openid profile email",
  "client_id": "client_abc123",
  "username": "<EMAIL>",
  "token_type": "Bearer",
  "exp": 1640998800,
  "iat": 1640995200,
  "sub": "user-123",
  "aud": "client_abc123",
  "iss": "https://idp.example.com"
}
```

### 发现端点

#### OpenID Connect 发现
```
GET /.well-known/openid-configuration
```

**响应:**
```json
{
  "issuer": "https://idp.example.com",
  "authorization_endpoint": "https://idp.example.com/oauth2/authorize",
  "token_endpoint": "https://idp.example.com/oauth2/token",
  "userinfo_endpoint": "https://idp.example.com/oauth2/userinfo",
  "jwks_uri": "https://idp.example.com/.well-known/jwks.json",
  "introspection_endpoint": "https://idp.example.com/oauth2/introspect",
  "revocation_endpoint": "https://idp.example.com/oauth2/revoke",
  "response_types_supported": ["code", "token", "id_token", "code token", "code id_token"],
  "grant_types_supported": ["authorization_code", "refresh_token", "client_credentials"],
  "subject_types_supported": ["public"],
  "id_token_signing_alg_values_supported": ["RS256"],
  "scopes_supported": ["openid", "profile", "email", "offline_access"],
  "token_endpoint_auth_methods_supported": ["client_secret_basic", "client_secret_post"],
  "claims_supported": ["sub", "iss", "aud", "exp", "iat", "email", "email_verified", "name", "nickname", "picture"],
  "code_challenge_methods_supported": ["S256"]
}
```

#### JWKS 端点
```
GET /.well-known/jwks.json
```

**响应:**
```json
{
  "keys": [
    {
      "kty": "RSA",
      "use": "sig",
      "kid": "key-id-123",
      "n": "0vx7agoebGcQSuuPiLJXZptN9nndrQmbXEps2aiAFbWhM78LhWx4cbbfAAtVT86zwu1RK7aPFFxuhDR1L6tSoc_BJECPebWKRXjBZCiFV4n3oknjhMstn64tZ_2W-5JsGY4Hc5n9yBXArwl93lqt7_RN5w6Cf0h4QyQ5v-65YGjQR0_FDW2QvzqY368QQMicAtaSqzs8KJZgnYb9c7d0zgdAZHzu6qMQvRL5hajrn1n91CbOpbISD08qNLyrdkt-bFTWhAI4vMQFh6WeZu0fM4lFd2NcRwr3XPksINHaQ-G_xBniIqbw0Ls1jF44-csFCur-kEgU8awapJzKnqDKgw",
      "e": "AQAB",
      "alg": "RS256"
    }
  ]
}
```

### 客户端管理端点

#### 创建客户端
```
POST /oauth2/clients
Authorization: Bearer {admin_token}
```

**请求体:**
```json
{
  "name": "My Application",
  "description": "示例应用程序",
  "redirectUris": [
    "https://app.example.com/callback"
  ],
  "grantTypes": ["authorization_code", "refresh_token"],
  "responseTypes": ["code"],
  "scopes": ["openid", "profile", "email"],
  "requirePkce": false,
  "requireConsent": true
}
```

#### 列出客户端
```
GET /oauth2/clients
Authorization: Bearer {admin_token}
```

#### 获取客户端信息
```
GET /oauth2/clients/{clientId}
Authorization: Bearer {admin_token}
```

#### 更新客户端
```
PUT /oauth2/clients/{clientId}
Authorization: Bearer {admin_token}
```

#### 删除客户端
```
DELETE /oauth2/clients/{clientId}
Authorization: Bearer {admin_token}
```

#### 重新生成客户端密钥
```
POST /oauth2/clients/{clientId}/regenerate-secret
Authorization: Bearer {admin_token}
```

## 权限范围 (Scopes)

| 范围 | 描述 | 包含的声明 |
|------|------|------------|
| `openid` | OpenID Connect 身份验证 | `sub`, `iss`, `aud`, `exp`, `iat` |
| `profile` | 基本用户资料信息 | `name`, `nickname`, `given_name`, `family_name`, `picture`, `updated_at` |
| `email` | 邮箱地址 | `email`, `email_verified` |
| `offline_access` | 离线访问 (刷新令牌) | - |

## 客户端认证方法

- `client_secret_basic`: HTTP Basic 认证 (推荐)
- `client_secret_post`: POST 参数认证
- `private_key_jwt`: JWT 私钥认证 (计划支持)

## 使用示例

### 1. 授权码流程

```javascript
// 1. 重定向到授权端点
const authUrl = new URL('https://idp.example.com/oauth2/authorize');
authUrl.searchParams.set('response_type', 'code');
authUrl.searchParams.set('client_id', 'your-client-id');
authUrl.searchParams.set('redirect_uri', 'https://your-app.com/callback');
authUrl.searchParams.set('scope', 'openid profile email');
authUrl.searchParams.set('state', 'random-state-value');

window.location.href = authUrl.toString();

// 2. 处理回调并交换令牌
const urlParams = new URLSearchParams(window.location.search);
const code = urlParams.get('code');
const state = urlParams.get('state');

if (code && state === 'random-state-value') {
  const tokenResponse = await fetch('https://idp.example.com/oauth2/token', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
      'Authorization': 'Basic ' + btoa('client-id:client-secret')
    },
    body: new URLSearchParams({
      grant_type: 'authorization_code',
      code: code,
      redirect_uri: 'https://your-app.com/callback'
    })
  });
  
  const tokens = await tokenResponse.json();
  console.log('Access Token:', tokens.access_token);
  console.log('ID Token:', tokens.id_token);
}
```

### 2. 获取用户信息

```javascript
const userInfoResponse = await fetch('https://idp.example.com/oauth2/userinfo', {
  headers: {
    'Authorization': `Bearer ${accessToken}`
  }
});

const userInfo = await userInfoResponse.json();
console.log('User Info:', userInfo);
```

### 3. PKCE 流程

```javascript
// 生成 PKCE 参数
function generateCodeVerifier() {
  const array = new Uint8Array(32);
  crypto.getRandomValues(array);
  return btoa(String.fromCharCode.apply(null, array))
    .replace(/\+/g, '-')
    .replace(/\//g, '_')
    .replace(/=/g, '');
}

async function generateCodeChallenge(verifier) {
  const encoder = new TextEncoder();
  const data = encoder.encode(verifier);
  const digest = await crypto.subtle.digest('SHA-256', data);
  return btoa(String.fromCharCode.apply(null, new Uint8Array(digest)))
    .replace(/\+/g, '-')
    .replace(/\//g, '_')
    .replace(/=/g, '');
}

const codeVerifier = generateCodeVerifier();
const codeChallenge = await generateCodeChallenge(codeVerifier);

// 在授权URL中添加PKCE参数
authUrl.searchParams.set('code_challenge', codeChallenge);
authUrl.searchParams.set('code_challenge_method', 'S256');

// 在令牌交换时包含code_verifier
const tokenResponse = await fetch('https://idp.example.com/oauth2/token', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/x-www-form-urlencoded'
  },
  body: new URLSearchParams({
    grant_type: 'authorization_code',
    code: code,
    redirect_uri: 'https://your-app.com/callback',
    client_id: 'your-client-id',
    code_verifier: codeVerifier
  })
});
```

## 错误处理

### 授权端点错误
- `invalid_request`: 请求参数无效
- `unauthorized_client`: 客户端未授权
- `access_denied`: 用户拒绝授权
- `unsupported_response_type`: 不支持的响应类型
- `invalid_scope`: 无效的权限范围
- `server_error`: 服务器内部错误

### 令牌端点错误
- `invalid_request`: 请求参数无效
- `invalid_client`: 客户端认证失败
- `invalid_grant`: 无效的授权码或刷新令牌
- `unauthorized_client`: 客户端未授权使用此授权类型
- `unsupported_grant_type`: 不支持的授权类型
- `invalid_scope`: 无效的权限范围

## 最佳实践

1. **使用 HTTPS**: 所有端点都应该使用 HTTPS
2. **验证重定向URI**: 严格验证重定向URI
3. **使用 PKCE**: 对于公共客户端，始终使用 PKCE
4. **短期访问令牌**: 使用较短的访问令牌生命周期
5. **安全存储**: 安全存储客户端密钥和令牌
6. **状态参数**: 始终使用状态参数防止CSRF攻击
7. **权限最小化**: 只请求必要的权限范围

## 故障排除

### 常见问题

1. **授权码已使用**: 授权码只能使用一次
2. **重定向URI不匹配**: 确保重定向URI完全匹配
3. **权限范围无效**: 检查请求的权限范围是否被客户端支持
4. **令牌过期**: 使用刷新令牌获取新的访问令牌
5. **客户端认证失败**: 检查客户端ID和密钥

### 调试工具

- **OIDC Debugger**: https://oidcdebugger.com/
- **JWT.io**: https://jwt.io/ (用于解码JWT令牌)
- **Postman**: 支持OAuth 2.0流程测试

## 配置示例

### 环境变量
```bash
# JWT配置
JWT_SECRET=your-jwt-secret-key
JWT_REFRESH_SECRET=your-refresh-secret-key
JWT_EXPIRES_IN=15m
JWT_REFRESH_EXPIRES_IN=7d

# 服务器配置
SERVER_BASE_URL=https://idp.example.com
```

### 客户端配置示例
```json
{
  "name": "Web Application",
  "redirectUris": [
    "https://app.example.com/callback",
    "https://app.example.com/silent-renew"
  ],
  "grantTypes": ["authorization_code", "refresh_token"],
  "responseTypes": ["code"],
  "scopes": ["openid", "profile", "email", "offline_access"],
  "tokenEndpointAuthMethod": "client_secret_basic",
  "requirePkce": false,
  "requireConsent": true,
  "accessTokenLifetime": 3600,
  "refreshTokenLifetime": 2592000,
  "idTokenLifetime": 3600
}
```
