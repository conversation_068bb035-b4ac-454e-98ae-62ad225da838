# 非标准应用支持指南

## 概述

本指南介绍如何使用身份提供商(IdP)系统支持各种非标准应用，包括遗留系统、自定义协议应用、API专用应用等。

## 支持的非标准应用类型

### 1. 遗留系统 (Legacy Systems)
- **特点**: 使用旧的认证协议或自定义认证方式
- **适用场景**: 老旧的企业应用、无法升级的系统
- **解决方案**: 通过协议适配器转换认证流程

### 2. 自定义协议应用 (Custom Protocol Apps)
- **特点**: 使用非标准的认证协议
- **适用场景**: 特殊业务需求的应用
- **解决方案**: 开发自定义协议适配器

### 3. API专用应用 (API-Only Apps)
- **特点**: 仅通过API进行认证，无用户界面
- **适用场景**: 微服务、后台服务
- **解决方案**: 使用API密钥或客户端凭据认证

### 4. Webhook应用 (Webhook-Based Apps)
- **特点**: 通过Webhook接收认证事件
- **适用场景**: 事件驱动的应用
- **解决方案**: 配置Webhook回调

### 5. 移动应用 (Mobile Apps)
- **特点**: 需要特殊的移动端认证流程
- **适用场景**: iOS/Android应用
- **解决方案**: 支持PKCE、深度链接等

### 6. IoT设备 (IoT Devices)
- **特点**: 资源受限，需要轻量级认证
- **适用场景**: 物联网设备
- **解决方案**: 设备码认证、证书认证

## 快速开始

### 1. 创建非标准应用

```bash
# 使用API创建非标准应用
curl -X POST http://localhost:3000/nsa/admin/apps \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "遗留系统应用",
    "description": "集成旧的HR系统",
    "appType": "legacy_system",
    "protocolName": "legacy-system",
    "protocolConfig": {
      "name": "legacy-system",
      "version": "1.0",
      "endpoints": {
        "authorization": "/nsa/auth/{applicationId}/legacy-system",
        "token": "/nsa/token/{applicationId}/legacy-system",
        "userinfo": "/nsa/userinfo/{applicationId}/legacy-system"
      },
      "customSettings": {
        "legacy_api_url": "https://legacy-hr.company.com/api",
        "legacy_api_key": "your-legacy-api-key"
      }
    },
    "webhookUrls": {
      "onSuccess": "https://your-app.com/auth/success",
      "onError": "https://your-app.com/auth/error"
    }
  }'
```

### 2. 配置协议适配器

```javascript
// 示例：自定义OAuth适配器配置
{
  "name": "custom-oauth",
  "version": "2.0",
  "endpoints": {
    "authorization": "/nsa/auth/{applicationId}/custom-oauth",
    "token": "/nsa/token/{applicationId}/custom-oauth",
    "userinfo": "/nsa/userinfo/{applicationId}/custom-oauth"
  },
  "supportedGrantTypes": [
    "authorization_code",
    "client_credentials",
    "custom:api_key"
  ],
  "customSettings": {
    "token_lifetime": 3600,
    "refresh_token_lifetime": 86400,
    "custom_claims": ["department", "role"]
  }
}
```

### 3. 实现认证流程

```javascript
// 前端发起认证
window.location.href = '/nsa/auth/your-app-id/custom-oauth?' +
  'client_id=your-client-id&' +
  'redirect_uri=https://your-app.com/callback&' +
  'scope=profile email&' +
  'state=random-state-value';

// 处理回调
const urlParams = new URLSearchParams(window.location.search);
const code = urlParams.get('code');
const state = urlParams.get('state');

if (code) {
  // 交换访问令牌
  const tokenResponse = await fetch('/nsa/token/your-app-id/custom-oauth', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    body: new URLSearchParams({
      grant_type: 'authorization_code',
      code: code,
      client_id: 'your-client-id',
      client_secret: 'your-client-secret',
      redirect_uri: 'https://your-app.com/callback'
    })
  });

  const tokens = await tokenResponse.json();
  localStorage.setItem('access_token', tokens.access_token);
}
```

## 插件开发

### 1. 创建插件

```javascript
// plugins/my-custom-plugin.js
class MyCustomPlugin {
  constructor() {
    this.name = 'my-custom-plugin';
    this.version = '1.0.0';
    this.description = '我的自定义认证插件';
  }

  async initialize(config) {
    this.config = config;
    console.log('插件初始化', config);
  }

  async destroy() {
    console.log('插件销毁');
  }

  getProtocolAdapters() {
    return {
      'my-protocol': MyProtocolAdapter
    };
  }

  getCustomHandlers() {
    return {
      'my_custom_handler': this.myCustomHandler.bind(this)
    };
  }

  async myCustomHandler(data) {
    // 自定义处理逻辑
    return { processed: true, data };
  }
}

module.exports = MyCustomPlugin;
```

### 2. 安装插件

```bash
# 将插件文件放到plugins目录
cp my-custom-plugin.js plugins/

# 通过API安装插件
curl -X POST http://localhost:3000/nsa/admin/plugins/install \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "my-custom-plugin",
    "version": "1.0.0",
    "filePath": "my-custom-plugin.js",
    "entryPoint": "MyCustomPlugin",
    "config": {
      "setting1": "value1",
      "setting2": "value2"
    }
  }'
```

### 3. 启用插件

```bash
curl -X POST http://localhost:3000/nsa/admin/plugins/my-custom-plugin/enable \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN"
```

## 常见集成场景

### 场景1: 遗留HR系统集成

```javascript
// 1. 创建遗留系统应用配置
const appConfig = {
  name: "HR遗留系统",
  appType: "legacy_system",
  protocolName: "legacy-system",
  protocolConfig: {
    legacy_api_url: "https://hr.company.com/api",
    legacy_api_key: "hr-api-key-123",
    user_info_endpoint: "/users/info",
    token_validation_endpoint: "/auth/validate"
  },
  customHandlers: {
    userTransform: "legacy_hr_user_transform"
  }
};

// 2. 实现用户数据转换处理器
async function legacy_hr_user_transform(legacyUserData) {
  return {
    id: legacyUserData.employee_id,
    email: legacyUserData.work_email,
    firstName: legacyUserData.first_name,
    lastName: legacyUserData.last_name,
    department: legacyUserData.dept_code,
    position: legacyUserData.job_title
  };
}
```

### 场景2: API专用微服务

```javascript
// 1. 创建API专用应用
const apiAppConfig = {
  name: "订单处理微服务",
  appType: "api_only",
  protocolName: "custom-oauth",
  apiKeyEnabled: true,
  protocolConfig: {
    supportedGrantTypes: ["client_credentials", "custom:api_key"],
    scopes: ["orders:read", "orders:write", "inventory:read"]
  }
};

// 2. 使用客户端凭据认证
const tokenResponse = await fetch('/nsa/token/api-app-id/custom-oauth', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/x-www-form-urlencoded'
  },
  body: new URLSearchParams({
    grant_type: 'client_credentials',
    client_id: 'api-client-id',
    client_secret: 'api-client-secret',
    scope: 'orders:read orders:write'
  })
});
```

### 场景3: IoT设备认证

```javascript
// 1. 创建IoT设备应用
const iotAppConfig = {
  name: "智能传感器",
  appType: "iot_device",
  protocolName: "custom-oauth",
  protocolConfig: {
    supportedGrantTypes: ["custom:device_code"],
    device_code_lifetime: 600,
    polling_interval: 5
  }
};

// 2. 设备码认证流程
// 设备端请求设备码
const deviceCodeResponse = await fetch('/nsa/device/code', {
  method: 'POST',
  headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
  body: new URLSearchParams({
    client_id: 'iot-client-id',
    scope: 'device:telemetry'
  })
});

const { device_code, user_code, verification_uri } = await deviceCodeResponse.json();

// 用户在verification_uri输入user_code进行授权
// 设备轮询获取令牌
const pollForToken = async () => {
  const tokenResponse = await fetch('/nsa/token/iot-app-id/custom-oauth', {
    method: 'POST',
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    body: new URLSearchParams({
      grant_type: 'custom:device_code',
      device_code: device_code,
      client_id: 'iot-client-id'
    })
  });

  if (tokenResponse.status === 200) {
    const tokens = await tokenResponse.json();
    return tokens;
  } else if (tokenResponse.status === 400) {
    const error = await tokenResponse.json();
    if (error.error === 'authorization_pending') {
      // 继续轮询
      setTimeout(pollForToken, 5000);
    }
  }
};
```

## 最佳实践

### 1. 安全考虑
- 使用HTTPS进行所有通信
- 实施适当的速率限制
- 验证所有输入参数
- 使用强密码策略
- 定期轮换密钥和证书

### 2. 性能优化
- 缓存协议适配器实例
- 使用连接池
- 实施超时机制
- 监控性能指标

### 3. 错误处理
- 提供详细的错误信息
- 实施重试机制
- 记录详细的审计日志
- 设置告警机制

### 4. 监控和维护
- 定期进行健康检查
- 监控认证成功率
- 跟踪性能指标
- 定期更新插件和配置

## 故障排除

### 常见问题

1. **插件加载失败**
   - 检查插件文件路径
   - 验证插件接口实现
   - 查看错误日志

2. **认证失败**
   - 验证客户端凭据
   - 检查协议配置
   - 确认端点URL正确

3. **令牌无效**
   - 检查令牌过期时间
   - 验证令牌签名
   - 确认作用域权限

### 调试工具

```bash
# 查看插件状态
curl -H "Authorization: Bearer YOUR_TOKEN" \
  http://localhost:3000/nsa/admin/plugins

# 检查协议元数据
curl http://localhost:3000/nsa/protocols/custom-oauth/metadata

# 测试应用连接
curl -X POST -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"protocolName": "custom-oauth"}' \
  http://localhost:3000/nsa/admin/apps/your-app-id/test
```

## 总结

通过本指南，您可以：
1. 了解支持的非标准应用类型
2. 学会创建和配置非标准应用
3. 开发自定义插件和协议适配器
4. 实现各种集成场景
5. 遵循最佳实践确保安全性和性能

如需更多帮助，请参考API文档或联系技术支持团队。
