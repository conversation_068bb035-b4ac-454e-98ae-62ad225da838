# 威胁情报集成指南

## 📋 概述

威胁情报集成系统为 ID Provider 提供实时威胁检测和分析能力，通过集成多个外部威胁情报源，能够识别恶意IP、可疑域名、钓鱼URL等安全威胁，并提供相应的防护建议和自动化响应。

## 🎯 功能特性

### 核心功能
- **多源威胁情报**: 集成 VirusTotal、AbuseIPDB、Shodan、ThreatCrowd 等威胁情报源
- **实时威胁检测**: 支持IP、域名、URL的实时威胁检查
- **批量威胁分析**: 支持批量指标检查，提升检测效率
- **风险评分**: 基于多维度数据计算综合风险分数
- **智能缓存**: 减少API调用，提升响应速度
- **自动化响应**: 根据威胁级别自动执行防护措施

### 威胁类型支持
- **恶意IP地址**: 僵尸网络、攻击源、恶意服务器
- **可疑域名**: 恶意域名、钓鱼网站、C&C服务器
- **钓鱼URL**: 钓鱼链接、恶意下载链接
- **已知恶意软件**: 恶意文件哈希、样本信息
- **Tor出口节点**: 匿名网络出口识别
- **VPN/代理**: 代理服务器和VPN出口

## 🏗️ 系统架构

### 架构组件
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   API网关       │    │   威胁情报服务   │    │   外部情报源    │
│   (Express)     │────│   (Service)     │────│   (Multiple)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │              ┌─────────────────┐              │
         │              │   缓存层        │              │
         │              │   (Redis)       │              │
         │              └─────────────────┘              │
         │                       │                       │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   审计日志      │    │   指标收集      │    │   告警系统      │
│   (Audit)       │    │   (Metrics)     │    │   (Alerts)      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 数据流程
1. **威胁检查请求**: 客户端发起威胁检查请求
2. **缓存查询**: 首先检查Redis缓存中是否有结果
3. **多源查询**: 并行查询多个威胁情报源
4. **结果聚合**: 聚合和分析来自不同源的威胁数据
5. **风险评估**: 计算综合风险分数和威胁级别
6. **响应生成**: 生成检测结果和安全建议
7. **缓存存储**: 将结果缓存以提升后续查询性能
8. **审计记录**: 记录威胁检查活动和结果

## 🔧 配置说明

### 环境变量配置
```bash
# VirusTotal 配置
VIRUSTOTAL_API_KEY=your_virustotal_api_key

# AbuseIPDB 配置
ABUSEIPDB_API_KEY=your_abuseipdb_api_key

# Shodan 配置
SHODAN_API_KEY=your_shodan_api_key

# 威胁情报缓存配置
THREAT_INTEL_CACHE_TTL=3600  # 缓存时间（秒）
THREAT_INTEL_CACHE_PREFIX=threat_intel:

# 速率限制配置
THREAT_INTEL_RATE_LIMIT_ENABLED=true
THREAT_INTEL_RATE_LIMIT_WINDOW=60000  # 时间窗口（毫秒）
THREAT_INTEL_RATE_LIMIT_MAX=30        # 最大请求数

# 批量查询配置
THREAT_INTEL_BATCH_SIZE=10            # 批量大小
THREAT_INTEL_BATCH_MAX=100            # 最大批量数量
```

### 威胁情报源配置
```typescript
// 威胁情报源优先级配置
const sourceConfig = {
  virustotal: {
    priority: 1,
    weight: 0.4,
    timeout: 10000
  },
  abuseipdb: {
    priority: 2,
    weight: 0.3,
    timeout: 8000
  },
  shodan: {
    priority: 3,
    weight: 0.2,
    timeout: 8000
  },
  threatcrowd: {
    priority: 4,
    weight: 0.1,
    timeout: 5000
  }
};
```

## 📊 API接口

### IP威胁检查
```http
GET /api/v1/threat-intelligence/ip/{ip}?includeDetails=true

# 响应示例
{
  "success": true,
  "data": {
    "isThreat": true,
    "riskScore": 85,
    "threats": [
      {
        "id": "threat_001",
        "type": "malicious_ip",
        "level": "high",
        "indicator": "*************",
        "source": "virustotal",
        "description": "已知恶意IP地址",
        "confidence": 95,
        "firstSeen": "2025-01-01T00:00:00Z",
        "lastSeen": "2025-01-15T12:00:00Z",
        "tags": ["botnet", "malware"],
        "metadata": {
          "country": "CN",
          "asn": "AS4134"
        }
      }
    ],
    "recommendations": [
      "立即阻断来自此IP的所有连接",
      "检查相关日志以确定潜在的入侵范围"
    ],
    "blockedActions": [
      "block_all_traffic",
      "quarantine_source"
    ]
  },
  "meta": {
    "queryTime": "2025-01-15T12:00:00Z",
    "duration": "250ms"
  }
}
```

### 域名威胁检查
```http
GET /api/v1/threat-intelligence/domain/{domain}

# 响应示例
{
  "success": true,
  "data": {
    "isThreat": false,
    "riskScore": 15,
    "threatCount": 0,
    "recommendations": []
  }
}
```

### URL威胁检查
```http
POST /api/v1/threat-intelligence/url
Content-Type: application/json

{
  "url": "https://example.com/suspicious-link"
}

# 响应示例
{
  "success": true,
  "data": {
    "isThreat": true,
    "riskScore": 75,
    "threatCount": 2,
    "recommendations": [
      "阻止访问此URL",
      "通知用户此链接可能是钓鱼攻击"
    ]
  }
}
```

### 批量威胁检查
```http
POST /api/v1/threat-intelligence/batch
Content-Type: application/json

{
  "indicators": [
    {
      "type": "ip",
      "value": "*************"
    },
    {
      "type": "domain",
      "value": "suspicious-domain.com"
    },
    {
      "type": "url",
      "value": "https://phishing-site.com/login"
    }
  ]
}

# 响应示例
{
  "success": true,
  "data": {
    "results": {
      "*************": {
        "isThreat": true,
        "riskScore": 85
      },
      "suspicious-domain.com": {
        "isThreat": false,
        "riskScore": 10
      },
      "https://phishing-site.com/login": {
        "isThreat": true,
        "riskScore": 90
      }
    },
    "summary": {
      "total": 3,
      "threats": 2,
      "clean": 1
    }
  }
}
```

### 威胁情报统计
```http
GET /api/v1/threat-intelligence/stats

# 响应示例
{
  "success": true,
  "data": {
    "sources": [
      {
        "name": "virustotal",
        "enabled": true,
        "priority": 1,
        "supportedTypes": ["malicious_ip", "suspicious_domain"]
      }
    ],
    "cacheStats": {
      "hitRate": 0.75,
      "totalQueries": 1000,
      "cacheHits": 750
    },
    "recentThreats": [
      {
        "indicator": "*************",
        "type": "malicious_ip",
        "level": "high",
        "detectedAt": "2025-01-15T12:00:00Z"
      }
    ]
  }
}
```

## 🔒 权限管理

### 所需权限
- `security:threat_intel:read` - 基础威胁情报查询权限
- `security:threat_intel:batch` - 批量威胁检查权限
- `security:threat_intel:stats` - 威胁情报统计查询权限
- `security:threat_intel:admin` - 威胁情报管理权限

### 角色配置示例
```json
{
  "roles": {
    "security_analyst": {
      "permissions": [
        "security:threat_intel:read",
        "security:threat_intel:batch",
        "security:threat_intel:stats"
      ]
    },
    "security_admin": {
      "permissions": [
        "security:threat_intel:read",
        "security:threat_intel:batch",
        "security:threat_intel:stats",
        "security:threat_intel:admin"
      ]
    }
  }
}
```

## 📈 性能优化

### 缓存策略
- **查询结果缓存**: 威胁检查结果缓存1小时
- **负面缓存**: 干净指标缓存时间更长
- **分层缓存**: 内存缓存 + Redis缓存
- **缓存预热**: 常见威胁指标预加载

### 速率限制
- **API速率限制**: 防止API配额耗尽
- **用户速率限制**: 防止滥用和DDoS
- **智能限流**: 基于用户角色的差异化限制
- **熔断机制**: API故障时的降级处理

### 并发优化
```typescript
// 并行查询多个威胁情报源
const promises = sources.map(source => 
  this.queryThreatSource(source, indicator)
);

const results = await Promise.allSettled(promises);

// 超时控制
const timeoutPromise = new Promise((_, reject) => 
  setTimeout(() => reject(new Error('查询超时')), 10000)
);

const result = await Promise.race([
  threatQuery,
  timeoutPromise
]);
```

## 🚨 监控和告警

### 关键指标
- **查询响应时间**: 威胁检查API响应时间
- **缓存命中率**: 缓存效率指标
- **威胁检出率**: 威胁发现比例
- **API可用性**: 外部威胁情报源可用性
- **错误率**: 查询失败率

### 告警规则
```yaml
# 威胁情报告警配置
alerts:
  - name: threat_intel_high_response_time
    condition: avg(threat_intel_response_time) > 5000
    duration: 5m
    severity: warning
    message: "威胁情报查询响应时间过长"

  - name: threat_intel_api_error_rate
    condition: rate(threat_intel_errors) > 0.1
    duration: 2m
    severity: critical
    message: "威胁情报API错误率过高"

  - name: threat_intel_cache_miss_rate
    condition: rate(threat_intel_cache_misses) > 0.5
    duration: 10m
    severity: warning
    message: "威胁情报缓存命中率过低"
```

### 监控面板
```typescript
// Grafana 监控面板配置
const dashboardConfig = {
  panels: [
    {
      title: "威胁检查请求量",
      type: "graph",
      targets: [
        "sum(rate(threat_intel_requests_total[5m]))"
      ]
    },
    {
      title: "威胁检出率",
      type: "stat",
      targets: [
        "rate(threat_intel_threats_detected[1h]) / rate(threat_intel_requests_total[1h])"
      ]
    },
    {
      title: "API响应时间",
      type: "heatmap",
      targets: [
        "histogram_quantile(0.95, threat_intel_response_time_bucket)"
      ]
    }
  ]
};
```

## 🔧 故障排除

### 常见问题

#### 1. API配额耗尽
**症状**: 威胁情报源返回配额限制错误
**解决方案**:
- 检查API密钥配额使用情况
- 调整查询频率和缓存策略
- 考虑升级API套餐或添加备用源

#### 2. 查询响应缓慢
**症状**: 威胁检查响应时间过长
**解决方案**:
- 检查网络连接和DNS解析
- 优化并发查询策略
- 增加缓存命中率
- 设置合理的超时时间

#### 3. 缓存失效频繁
**症状**: 缓存命中率低，重复查询多
**解决方案**:
- 检查Redis连接状态
- 调整缓存TTL设置
- 优化缓存键设计
- 监控缓存内存使用

### 调试技巧
```bash
# 检查威胁情报服务状态
curl -H "Authorization: Bearer $TOKEN" \
  http://localhost:3000/api/v1/threat-intelligence/stats

# 测试IP威胁检查
curl -H "Authorization: Bearer $TOKEN" \
  http://localhost:3000/api/v1/threat-intelligence/ip/*******

# 查看威胁情报日志
docker logs id-provider | grep "threat-intelligence"

# 检查Redis缓存
redis-cli keys "threat_intel:*"
```

## 📚 最佳实践

### 安全考虑
- **API密钥保护**: 使用环境变量存储API密钥
- **数据脱敏**: 日志中避免记录敏感指标
- **访问控制**: 严格的权限管理和审计
- **传输加密**: 所有API通信使用HTTPS

### 性能优化
- **智能缓存**: 根据威胁类型设置不同缓存时间
- **批量处理**: 合并相似查询减少API调用
- **异步处理**: 非关键路径使用异步查询
- **降级策略**: API故障时的备用方案

### 运维管理
- **监控覆盖**: 全面的性能和业务指标监控
- **告警及时**: 关键问题的实时告警
- **定期维护**: 定期清理缓存和更新配置
- **文档更新**: 保持文档和配置同步

---

*文档版本: 1.0*  
*最后更新: 2025-08-28*  
*维护团队: ID Provider 安全团队*
