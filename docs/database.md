# 数据库设计文档

## 1. 概述

身份提供商(IdP)系统使用PostgreSQL作为主数据库，通过Prisma ORM进行数据访问。数据库设计遵循以下原则：

- **安全性**: 敏感数据加密存储，密码使用bcrypt哈希
- **可扩展性**: 支持水平扩展，合理的索引设计
- **完整性**: 外键约束，数据一致性保证
- **审计性**: 完整的操作日志记录

## 2. 核心数据模型

### 2.1 用户表 (users)

存储用户的基本信息和认证凭据。

**主要字段:**
- `id`: UUID主键
- `email`: 邮箱地址（唯一）
- `phone`: 手机号（唯一，可选）
- `username`: 用户名（唯一，可选）
- `passwordHash`: 密码哈希（可选，支持仅第三方登录）
- `emailVerified`: 邮箱验证状态
- `isActive`: 账户激活状态
- `isLocked`: 账户锁定状态

**安全特性:**
- 密码使用bcrypt加密存储
- 支持账户锁定机制
- 记录最后登录时间和IP

### 2.2 应用表 (applications)

存储注册的服务提供商(SP)信息。

**主要字段:**
- `id`: UUID主键
- `name`: 应用名称
- `clientId`: 客户端ID（唯一）
- `clientSecret`: 客户端密钥
- `redirectUris`: 回调URL列表
- `supportedProtocols`: 支持的SSO协议

**协议支持:**
- OpenID Connect (OIDC)
- OAuth 2.0
- SAML 2.0

### 2.3 会话表 (sessions)

管理用户登录会话和状态。

**主要字段:**
- `id`: UUID主键
- `userId`: 用户ID
- `applicationId`: 应用ID（可选）
- `sessionToken`: 会话令牌
- `deviceInfo`: 设备信息
- `ipAddress`: IP地址
- `riskScore`: 风险评分

**安全特性:**
- 会话超时管理
- 设备指纹识别
- 风险评分机制

### 2.4 MFA设备表 (mfa_devices)

管理多因素认证设备。

**支持的MFA类型:**
- TOTP (Time-based One-Time Password)
- 邮件验证码
- 短信验证码

**安全特性:**
- TOTP密钥加密存储
- 备用恢复码
- 设备验证状态

### 2.5 联合身份表 (federated_identities)

管理第三方账户关联。

**支持的提供商:**
- Google OAuth 2.0
- GitHub OAuth 2.0
- 可扩展支持更多提供商

**主要字段:**
- `provider`: 提供商标识
- `providerId`: 第三方用户ID
- `accessToken`: 访问令牌（加密存储）
- `refreshToken`: 刷新令牌（加密存储）

### 2.6 审计日志表 (audit_logs)

记录所有安全相关操作。

**记录内容:**
- 用户登录/登出
- 密码修改
- MFA操作
- 管理员操作
- 系统配置变更

**字段设计:**
- `action`: 操作类型
- `resource`: 操作资源
- `details`: 详细信息(JSON)
- `success`: 操作结果
- `ipAddress`: 操作IP

### 2.7 风险评估表 (risk_assessments)

零信任模式的风险评估记录。

**风险因子:**
- IP风险: 基于IP地理位置和信誉
- 设备风险: 基于设备指纹和历史
- 行为风险: 基于用户行为模式
- 位置风险: 基于地理位置变化
- 时间风险: 基于登录时间模式

**评估结果:**
- `totalRisk`: 总风险评分 (0-100)
- `riskLevel`: 风险等级 (low/medium/high/critical)
- `action`: 建议操作 (allow/mfa_required/deny)

### 2.8 角色权限表 (roles, user_roles)

基于角色的访问控制(RBAC)。

**默认角色:**
- `admin`: 系统管理员
- `user`: 普通用户

**权限设计:**
- 细粒度权限控制
- 支持权限继承
- 动态权限检查

### 2.9 刷新令牌表 (refresh_tokens)

JWT刷新令牌管理。

**特性:**
- 令牌轮换机制
- 设备绑定
- 自动清理过期令牌

### 2.10 系统配置表 (system_configs)

全局系统配置。

**配置类型:**
- 密码策略
- MFA设置
- 零信任配置
- 会话管理
- 邮件模板

## 3. 数据关系

### 3.1 一对多关系
- User → Sessions
- User → MFADevices
- User → FederatedIdentities
- User → AuditLogs
- Application → Sessions

### 3.2 多对多关系
- User ↔ Role (通过UserRole)

### 3.3 外键约束
- 级联删除: 用户删除时清理相关数据
- 设置NULL: 应用删除时保留审计日志

## 4. 索引设计

### 4.1 唯一索引
- users.email
- users.phone
- users.username
- applications.clientId
- sessions.sessionToken

### 4.2 复合索引
- (userId, applicationId) on sessions
- (provider, providerId) on federated_identities
- (userId, roleId) on user_roles

### 4.3 性能索引
- users.lastLoginAt
- sessions.expiresAt
- audit_logs.createdAt
- risk_assessments.createdAt

## 5. 数据安全

### 5.1 加密存储
- 用户密码: bcrypt哈希
- MFA密钥: AES加密
- OAuth令牌: AES加密
- 备用恢复码: AES加密

### 5.2 数据脱敏
- 日志中的敏感信息脱敏
- 导出数据时的隐私保护

### 5.3 数据保留
- 审计日志保留策略
- 过期会话清理
- 无效令牌清理

## 6. 备份和恢复

### 6.1 备份策略
- 每日全量备份
- 实时增量备份
- 异地备份存储

### 6.2 恢复测试
- 定期恢复测试
- 灾难恢复演练
- RTO/RPO目标

## 7. 监控和维护

### 7.1 性能监控
- 查询性能监控
- 连接池监控
- 慢查询分析

### 7.2 容量规划
- 存储容量监控
- 增长趋势分析
- 扩容计划

### 7.3 维护任务
- 定期VACUUM
- 统计信息更新
- 索引重建
