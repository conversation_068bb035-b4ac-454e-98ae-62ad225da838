# Redis缓存集成指南

## 📋 概述

本文档描述了身份提供商(IdP)项目中Redis缓存系统的集成和使用方法。Redis缓存系统提供了高性能的数据缓存、会话管理、速率限制和临时数据存储功能。

## 🏗️ 架构设计

### 缓存层次结构
```
应用层 (Controllers/Services)
    ↓
缓存服务层 (CacheService)
    ↓
Redis服务层 (RedisService)
    ↓
Redis数据库
```

### 核心组件
- **RedisService**: 底层Redis连接和基础操作
- **CacheService**: 高级缓存功能和业务逻辑
- **Redis配置**: 环境配置和连接管理

## 🚀 快速开始

### 1. 环境配置

在 `.env` 文件中配置Redis连接：

```bash
# Redis 缓存配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
REDIS_KEY_PREFIX=idp:
```

### 2. 启动Redis服务

```bash
# 使用Docker启动Redis
docker run -d --name redis -p 6379:6379 redis:7-alpine

# 或使用Docker Compose
docker-compose up -d redis
```

### 3. 应用集成

Redis服务会在应用启动时自动初始化：

```typescript
import { redisService } from '@/services/redis.service';
import { cacheService } from '@/services/cache.service';

// 应用启动时自动初始化
await redisService.initialize();
```

## 💡 使用示例

### 基础缓存操作

```typescript
import { cacheService } from '@/services/cache.service';

// 设置缓存
await cacheService.cache('user:123', userData, 3600); // 1小时过期

// 获取缓存
const userData = await cacheService.getCached('user:123');

// 删除缓存
await cacheService.invalidate('user:123');
```

### 会话管理

```typescript
// 设置用户会话
const sessionData = {
  userId: 'user-123',
  email: '<EMAIL>',
  roles: ['user'],
  permissions: ['read:profile'],
  lastAccessedAt: new Date(),
  expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000)
};

await cacheService.setSession('session-id', sessionData);

// 获取会话
const session = await cacheService.getSession('session-id');

// 获取用户所有会话
const userSessions = await cacheService.getUserSessions('user-123');

// 删除会话
await cacheService.deleteSession('session-id');

// 删除用户所有会话
await cacheService.deleteAllUserSessions('user-123');
```

### JWT令牌管理

```typescript
// 将JWT加入黑名单
await cacheService.blacklistJWT('jwt-id', expirationTime);

// 检查JWT是否在黑名单
const isBlacklisted = await cacheService.isJWTBlacklisted('jwt-id');

// 缓存刷新令牌
await cacheService.setRefreshToken('token-id', tokenData);

// 获取刷新令牌
const tokenData = await cacheService.getRefreshToken('token-id');
```

### 速率限制

```typescript
// 检查速率限制
const result = await cacheService.checkRateLimit(
  'api:user:123',  // 限制键
  100,             // 最大请求数
  60000           // 时间窗口(毫秒)
);

if (!result.allowed) {
  // 请求被限制
  console.log(`请求被限制，剩余: ${result.remaining}, 重置时间: ${result.resetTime}`);
}
```

### OAuth状态管理

```typescript
// 缓存OAuth状态
const oauthData = {
  provider: 'google',
  redirectUrl: 'http://localhost:3001/auth/callback',
  timestamp: Date.now()
};

await cacheService.setOAuthState('state-123', oauthData);

// 获取OAuth状态
const state = await cacheService.getOAuthState('state-123');

// 删除OAuth状态
await cacheService.deleteOAuthState('state-123');
```

### MFA验证码管理

```typescript
// 设置MFA验证码
await cacheService.setMFACode('user-123', 'email', '123456');

// 获取MFA验证码
const code = await cacheService.getMFACode('user-123', 'email');

// 删除MFA验证码
await cacheService.deleteMFACode('user-123', 'email');
```

## 🔧 配置选项

### Redis连接配置

```typescript
export interface RedisConfig {
  host: string;                    // Redis主机地址
  port: number;                    // Redis端口
  password?: string;               // Redis密码
  db: number;                      // 数据库编号
  keyPrefix: string;               // 键前缀
  retryDelayOnFailover: number;    // 故障转移重试延迟
  maxRetriesPerRequest: number;    // 最大重试次数
  lazyConnect: boolean;            // 懒加载连接
  keepAlive: number;               // 连接保活时间
  connectTimeout: number;          // 连接超时
  commandTimeout: number;          // 命令超时
}
```

### 缓存过期时间配置

```typescript
export const RedisTTL = {
  SESSION: 24 * 60 * 60,           // 会话: 24小时
  JWT_BLACKLIST: 15 * 60,          // JWT黑名单: 15分钟
  REFRESH_TOKEN: 7 * 24 * 60 * 60, // 刷新令牌: 7天
  RATE_LIMIT_LOGIN: 15 * 60,       // 登录限制: 15分钟
  OAUTH_STATE: 10 * 60,            // OAuth状态: 10分钟
  MFA_CODE: 5 * 60,                // MFA验证码: 5分钟
  USER_PROFILE: 15 * 60,           // 用户资料: 15分钟
  USER_PERMISSIONS: 30 * 60        // 用户权限: 30分钟
};
```

## 📊 性能指标

### 基准测试结果

基于本地Redis实例的性能测试结果：

| 操作类型 | 性能指标 | 说明 |
|---------|---------|------|
| 基础SET操作 | 76,336 ops/s | 小数据对象缓存 |
| 基础GET操作 | 79,365 ops/s | 小数据对象读取 |
| 会话缓存操作 | 65,217 ops/s | 复杂会话数据处理 |
| 速率限制检查 | 111,111 ops/s | 高频限制检查 |
| 大数据SET | 16,667 ops/s | 10KB数据写入 |
| 大数据GET | 18,868 ops/s | 10KB数据读取 |

### 内存使用

- **基础内存占用**: ~15MB
- **内存碎片率**: 1.71
- **推荐生产环境内存**: 256MB - 1GB

## 🔍 监控和调试

### 健康检查

```typescript
// 检查Redis连接状态
const isReady = redisService.isReady();

// 获取Redis信息
const info = await redisService.getInfo();
```

### 日志记录

Redis服务提供详细的日志记录：

```typescript
// 连接事件
logger.info('Redis连接已建立', { host, port, db });

// 操作日志
logger.debug('缓存设置成功', { key, ttl });

// 错误日志
logger.error('缓存操作失败', { key, error });
```

### 性能测试

运行性能测试脚本：

```bash
node scripts/redis-performance-test.js
```

## 🛡️ 安全考虑

### 1. 连接安全
- 使用密码保护Redis实例
- 配置防火墙限制访问
- 使用TLS加密连接（生产环境）

### 2. 数据安全
- 敏感数据加密存储
- 设置适当的过期时间
- 定期清理过期数据

### 3. 访问控制
- 使用Redis ACL控制访问权限
- 限制危险命令的使用
- 监控异常访问模式

## 🚨 故障处理

### 常见问题

1. **连接失败**
   ```
   解决方案: 检查Redis服务状态、网络连接、配置参数
   ```

2. **内存不足**
   ```
   解决方案: 增加内存限制、优化过期策略、清理无用数据
   ```

3. **性能下降**
   ```
   解决方案: 检查慢查询、优化数据结构、增加连接池大小
   ```

### 优雅降级

当Redis不可用时，应用会自动降级：

```typescript
// 缓存操作失败时不影响主要功能
try {
  await cacheService.setSession(sessionId, sessionData);
} catch (error) {
  logger.warn('缓存设置失败，继续正常流程', { error });
  // 继续执行主要业务逻辑
}
```

## 📈 最佳实践

### 1. 键命名规范
- 使用有意义的前缀: `idp:session:`, `idp:user:`
- 包含版本信息: `idp:v1:user:profile:`
- 避免过长的键名

### 2. 过期时间设置
- 根据数据重要性设置TTL
- 会话数据: 24小时
- 临时验证码: 5-10分钟
- 用户资料: 15-30分钟

### 3. 内存优化
- 使用合适的数据结构
- 定期清理过期数据
- 监控内存使用情况

### 4. 错误处理
- 实现优雅降级
- 记录详细错误日志
- 设置重试机制

## 🔄 维护和运维

### 备份策略
```bash
# 创建RDB快照
redis-cli BGSAVE

# 启用AOF持久化
redis-cli CONFIG SET appendonly yes
```

### 监控指标
- 连接数
- 内存使用率
- 命令执行时间
- 键空间命中率

### 扩展方案
- Redis Cluster集群部署
- 读写分离配置
- 哨兵模式高可用

---

## 📚 相关文档

- [Redis官方文档](https://redis.io/documentation)
- [ioredis客户端文档](https://github.com/luin/ioredis)
- [项目API文档](./api.md)
- [部署指南](./deployment.md)

---

*最后更新: 2023-08-16*  
*文档版本: 1.0*
