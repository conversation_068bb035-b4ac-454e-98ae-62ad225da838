# 身份认证界面使用指南

## 概述

身份提供商 (IdP) 现在包含了完整的前端认证界面，提供用户友好的登录、注册、多因素认证等功能。本文档将指导您如何使用和自定义这些界面。

## 功能特性

### 🔐 核心认证功能
- **用户登录** - 支持用户名/邮箱登录，记住我功能
- **用户注册** - 完整的注册流程，包含密码强度检查
- **密码重置** - 忘记密码和重置密码功能
- **多因素认证 (MFA)** - 支持TOTP、邮件、短信验证
- **OAuth登录** - 集成Google、GitHub等第三方登录

### 🎨 界面特性
- **响应式设计** - 完美适配桌面和移动设备
- **中文本地化** - 全中文界面和错误提示
- **主题自定义** - 支持颜色、圆角等样式自定义
- **品牌定制** - 可自定义Logo、标题等品牌元素

### 🛡️ 安全特性
- **JWT令牌管理** - 自动刷新和安全存储
- **会话管理** - 安全的用户会话处理
- **错误处理** - 友好的错误提示和处理
- **输入验证** - 前端和后端双重验证

## 快速开始

### 1. 构建前端界面

```bash
# 构建前端资源
npm run build:frontend

# 或者在开发模式下运行
npm run dev:frontend
```

### 2. 启动服务

```bash
# 启动后端服务
npm run dev

# 或者构建并启动生产版本
npm run build:all
npm start
```

### 3. 访问界面

打开浏览器访问：
- 主页：http://localhost:3000
- 登录页面：http://localhost:3000/login
- 注册页面：http://localhost:3000/register
- 仪表板：http://localhost:3000/dashboard

## 界面说明

### 登录页面 (`/login`)

**功能特性：**
- 用户名或邮箱登录
- 密码输入和显示切换
- "记住我"选项
- 忘记密码链接
- OAuth第三方登录按钮
- 注册页面跳转链接

**表单验证：**
- 用户名/邮箱格式验证
- 密码长度验证
- 实时错误提示

### 注册页面 (`/register`)

**功能特性：**
- 邮箱注册（必填）
- 昵称设置（可选）
- 姓名设置（可选）
- 密码强度实时检查
- 密码确认验证

**密码强度检查：**
- 长度要求（至少8个字符）
- 包含大小写字母
- 包含数字和特殊字符
- 实时强度指示器

### MFA认证页面 (`/mfa`)

**支持的MFA方法：**

1. **TOTP认证器**
   - 支持Google Authenticator、Authy等
   - 二维码扫描设置
   - 备用恢复码生成

2. **邮件验证**
   - 发送6位验证码到注册邮箱
   - 自动重发功能

3. **短信验证**
   - 发送验证码到手机号
   - 支持中国大陆手机号

### 仪表板页面 (`/dashboard`)

**功能模块：**
- 用户信息展示
- 安全设置管理
- MFA设备管理
- 应用授权管理
- 会话管理

## 自定义配置

### 环境变量配置

在 `.env` 文件中配置以下变量：

```bash
# UI自定义
UI_TITLE="我的身份提供商"
UI_LOGO="/path/to/logo.png"
UI_PRIMARY_COLOR="#1890ff"
UI_BORDER_RADIUS=6

# 功能开关
ENABLE_REGISTRATION=true
ENABLE_SOCIAL_LOGIN=true
ENABLE_MFA=true

# OAuth配置
GOOGLE_CLIENT_ID="your-google-client-id"
GITHUB_CLIENT_ID="your-github-client-id"
```

### 主题自定义

#### 1. 颜色主题

通过环境变量或API配置主色调：

```bash
UI_PRIMARY_COLOR="#ff6b35"  # 橙色主题
UI_PRIMARY_COLOR="#52c41a"  # 绿色主题
UI_PRIMARY_COLOR="#722ed1"  # 紫色主题
```

#### 2. 圆角设置

```bash
UI_BORDER_RADIUS=0   # 直角风格
UI_BORDER_RADIUS=4   # 小圆角
UI_BORDER_RADIUS=8   # 大圆角
```

#### 3. 自定义CSS

在 `frontend/src/index.css` 中添加自定义样式：

```css
/* 自定义认证容器样式 */
.auth-container {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* 自定义卡片样式 */
.auth-card {
  box-shadow: 0 10px 25px rgba(0,0,0,0.1);
  border-radius: var(--border-radius);
}
```

### Logo和品牌定制

#### 1. 替换Logo

将您的Logo文件放置在 `frontend/public/` 目录下，然后更新配置：

```bash
UI_LOGO="/logo.png"
```

#### 2. 自定义标题

```bash
UI_TITLE="您的公司名称"
```

#### 3. 自定义Favicon

替换 `frontend/public/favicon.ico` 文件。

## OAuth集成配置

### Google OAuth

1. 在 [Google Cloud Console](https://console.cloud.google.com/) 创建OAuth应用
2. 配置重定向URI：`http://localhost:3000/api/v1/auth/google/callback`
3. 设置环境变量：

```bash
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"
```

### GitHub OAuth

1. 在 [GitHub Developer Settings](https://github.com/settings/developers) 创建OAuth应用
2. 配置回调URL：`http://localhost:3000/api/v1/auth/github/callback`
3. 设置环境变量：

```bash
GITHUB_CLIENT_ID="your-github-client-id"
GITHUB_CLIENT_SECRET="your-github-client-secret"
```

## 开发和调试

### 开发模式

```bash
# 同时启动前端和后端开发服务器
npm run dev &
npm run dev:frontend
```

前端开发服务器运行在 `http://localhost:3001`，会自动代理API请求到后端。

### 构建生产版本

```bash
# 构建所有组件
npm run build:all

# 启动生产服务器
npm start
```

### 调试技巧

1. **查看网络请求**
   - 打开浏览器开发者工具
   - 查看Network标签页中的API请求

2. **查看控制台日志**
   - 前端错误会显示在浏览器控制台
   - 后端日志会显示在终端

3. **检查认证状态**
   - 在浏览器中查看localStorage中的认证信息
   - 使用 `/health` 端点检查服务状态

## 故障排除

### 常见问题

1. **前端界面无法访问**
   - 确保已运行 `npm run build:frontend`
   - 检查 `dist/frontend` 目录是否存在

2. **OAuth登录失败**
   - 检查OAuth应用配置
   - 确认回调URL设置正确
   - 验证客户端ID和密钥

3. **MFA设置失败**
   - 确保邮件服务配置正确
   - 检查短信服务提供商设置
   - 验证TOTP密钥生成

4. **样式显示异常**
   - 清除浏览器缓存
   - 检查CSS文件是否正确加载
   - 验证主题配置

### 日志查看

```bash
# 查看应用日志
tail -f logs/app.log

# 查看错误日志
tail -f logs/error.log
```

## 安全建议

1. **生产环境配置**
   - 使用HTTPS协议
   - 设置安全的JWT密钥
   - 配置适当的CORS策略

2. **密码策略**
   - 启用密码强度检查
   - 设置密码过期策略
   - 启用账户锁定机制

3. **MFA推荐**
   - 为管理员账户强制启用MFA
   - 推荐使用TOTP认证器
   - 定期更新备用恢复码

## 更多资源

- [API文档](./api-documentation.md)
- [开发指南](./development.md)
- [部署指南](./deployment.md)
- [安全配置](./security.md)

如有问题，请查看项目的GitHub Issues或联系技术支持。
