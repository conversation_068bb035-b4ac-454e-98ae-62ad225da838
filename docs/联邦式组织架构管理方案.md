# 联邦式组织架构管理方案

## 📋 方案概述

为了解决多应用组织架构数据冲突问题，我们设计了联邦式组织架构管理方案，让IDP作为组织架构的协调中心和权限验证枢纽，而不是唯一的数据源。

---

## 🏗️ 架构设计

### 核心理念

```
┌─────────────────────────────────────────────────────────────┐
│                    IDP 联邦协调中心                          │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   标准组织模型   │  │   映射关系管理   │  │   权限验证引擎   │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
           │                    │                    │
           ▼                    ▼                    ▼
┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐
│    应用A组织     │  │    应用B组织     │  │    应用C组织     │
│   ┌───────────┐ │  │   ┌───────────┐ │  │   ┌───────────┐ │
│   │技术部     │ │  │   │研发中心   │ │  │   │工程部     │ │
│   │└─后端组   │ │  │   │└─服务端   │ │  │   │└─开发组   │ │
│   └───────────┘ │  │   └───────────┘ │  │   └───────────┘ │
└─────────────────┘  └─────────────────┘  └─────────────────┘
```

### 设计原则

1. **数据主权**: 每个应用保持自己的组织架构数据主权
2. **标准映射**: IDP提供标准组织模型和映射机制
3. **联邦验证**: 权限验证时动态解析和映射
4. **最终一致性**: 通过事件同步保证数据最终一致

---

## 🔧 技术实现

### 1. 扩展组织架构数据模型

```typescript
// 联邦组织架构模型
interface FederatedOrganization {
  id: string;
  name: string;
  displayName: string;
  path: string;
  type: OrganizationType;
  
  // 联邦相关字段
  sourceApplication?: string;  // 数据来源应用
  isFederated: boolean;       // 是否为联邦组织
  canonicalPath?: string;     // 标准路径映射
  
  // 映射关系
  mappings: OrganizationMapping[];
}

interface OrganizationMapping {
  id: string;
  sourceOrgId: string;        // 源组织ID
  sourceApplication: string;  // 源应用标识
  targetOrgId: string;        // 目标组织ID (IDP中的标准组织)
  mappingType: 'exact' | 'parent' | 'child' | 'equivalent';
  confidence: number;         // 映射置信度 0-1
  createdBy: string;
  createdAt: Date;
  validFrom: Date;
  validUntil?: Date;
}

// 应用组织架构注册
interface ApplicationOrgRegistry {
  id: string;
  applicationId: string;
  orgSchemaVersion: string;
  syncEndpoint: string;       // 组织数据同步端点
  webhookEndpoint?: string;   // 变更通知端点
  lastSyncAt: Date;
  syncStatus: 'active' | 'failed' | 'disabled';
}
```

### 2. 组织架构同步服务

```typescript
export class FederatedOrganizationService {
  /**
   * 注册应用的组织架构
   */
  async registerApplicationOrganization(
    applicationId: string,
    orgData: ApplicationOrganizationData
  ): Promise<void> {
    // 1. 验证应用权限
    await this.validateApplicationPermission(applicationId);
    
    // 2. 解析应用组织架构
    const parsedOrgs = await this.parseApplicationOrganizations(orgData);
    
    // 3. 智能映射到标准组织架构
    const mappings = await this.generateOrganizationMappings(
      applicationId, 
      parsedOrgs
    );
    
    // 4. 存储映射关系
    await this.storeMappings(applicationId, mappings);
    
    // 5. 发布同步事件
    await this.publishSyncEvent(applicationId, 'organization_registered');
  }

  /**
   * 智能组织映射算法
   */
  private async generateOrganizationMappings(
    applicationId: string,
    appOrgs: ApplicationOrganization[]
  ): Promise<OrganizationMapping[]> {
    const mappings: OrganizationMapping[] = [];
    
    for (const appOrg of appOrgs) {
      // 1. 精确匹配 (名称+路径)
      let targetOrg = await this.findExactMatch(appOrg);
      
      if (!targetOrg) {
        // 2. 模糊匹配 (相似度算法)
        targetOrg = await this.findSimilarMatch(appOrg);
      }
      
      if (!targetOrg) {
        // 3. 创建新的标准组织
        targetOrg = await this.createStandardOrganization(appOrg);
      }
      
      mappings.push({
        sourceOrgId: appOrg.id,
        sourceApplication: applicationId,
        targetOrgId: targetOrg.id,
        mappingType: this.determineMappingType(appOrg, targetOrg),
        confidence: this.calculateConfidence(appOrg, targetOrg)
      });
    }
    
    return mappings;
  }

  /**
   * 联邦权限解析
   */
  async resolveFederatedPermissions(
    userId: string,
    applicationId: string,
    organizationContext?: string
  ): Promise<FederatedPermissionResult> {
    // 1. 获取用户在应用中的组织关系
    const appOrgMemberships = await this.getUserAppOrganizations(
      userId, 
      applicationId
    );
    
    // 2. 映射到标准组织架构
    const standardOrgMemberships = await this.mapToStandardOrganizations(
      appOrgMemberships
    );
    
    // 3. 解析有效权限
    const effectivePermissions = await this.resolveEffectivePermissions(
      userId,
      standardOrgMemberships,
      organizationContext
    );
    
    return {
      userId,
      applicationId,
      permissions: effectivePermissions.permissions,
      organizationMemberships: standardOrgMemberships,
      mappingConfidence: this.calculateOverallConfidence(standardOrgMemberships),
      resolvedAt: new Date()
    };
  }
}
```

### 3. 应用集成API

```typescript
// 应用组织架构上报API
export class ApplicationOrgIntegrationController {
  /**
   * 上报应用组织架构
   */
  @Post('/applications/:appId/organizations/sync')
  async syncOrganizations(
    @Param('appId') applicationId: string,
    @Body() orgData: ApplicationOrganizationSyncRequest
  ) {
    try {
      // 验证应用身份
      await this.validateApplicationAuth(applicationId);
      
      // 处理组织架构同步
      const result = await this.federatedOrgService.syncApplicationOrganizations(
        applicationId,
        orgData
      );
      
      return {
        success: true,
        message: '组织架构同步成功',
        data: {
          syncedOrganizations: result.syncedCount,
          newMappings: result.newMappings,
          updatedMappings: result.updatedMappings,
          conflicts: result.conflicts
        }
      };
    } catch (error) {
      return {
        success: false,
        error: 'SYNC_FAILED',
        message: error.message
      };
    }
  }

  /**
   * 查询组织映射关系
   */
  @Get('/applications/:appId/organizations/:orgId/mappings')
  async getOrganizationMappings(
    @Param('appId') applicationId: string,
    @Param('orgId') organizationId: string
  ) {
    const mappings = await this.federatedOrgService.getOrganizationMappings(
      applicationId,
      organizationId
    );
    
    return {
      success: true,
      data: {
        sourceOrganization: mappings.source,
        targetOrganizations: mappings.targets,
        mappingHistory: mappings.history
      }
    };
  }

  /**
   * 联邦权限验证
   */
  @Post('/permissions/federated-check')
  async checkFederatedPermission(
    @Body() request: FederatedPermissionCheckRequest
  ) {
    const result = await this.federatedOrgService.resolveFederatedPermissions(
      request.userId,
      request.applicationId,
      request.organizationContext
    );
    
    const hasPermission = result.permissions.includes(request.permission);
    
    return {
      success: true,
      data: {
        granted: hasPermission,
        permissions: result.permissions,
        organizationMemberships: result.organizationMemberships,
        mappingConfidence: result.mappingConfidence,
        resolvedAt: result.resolvedAt
      }
    };
  }
}
```

---

## 🔄 同步机制

### 1. 主动同步

```typescript
// 应用主动上报组织架构变更
const syncRequest = {
  applicationId: 'my-app',
  organizations: [
    {
      id: 'tech-dept',
      name: 'technology',
      displayName: '技术部',
      parentId: 'company',
      type: 'department',
      members: [
        {
          userId: 'user-123',
          role: 'manager',
          permissions: ['read:team_data', 'manage:team']
        }
      ]
    }
  ],
  syncType: 'incremental', // full | incremental
  timestamp: new Date().toISOString()
};

await fetch('/api/v1/applications/my-app/organizations/sync', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${appToken}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify(syncRequest)
});
```

### 2. 事件驱动同步

```typescript
// Webhook事件处理
export class OrganizationSyncEventHandler {
  @EventHandler('organization.created')
  async handleOrganizationCreated(event: OrganizationCreatedEvent) {
    // 通知相关应用组织架构变更
    await this.notifyApplications(event.applicationId, {
      type: 'organization_created',
      organization: event.organization,
      timestamp: event.timestamp
    });
  }

  @EventHandler('organization.member.added')
  async handleMemberAdded(event: MemberAddedEvent) {
    // 更新用户的联邦组织关系
    await this.updateFederatedMembership(
      event.userId,
      event.organizationId,
      event.applicationId
    );
  }
}
```

---

## 🛡️ 冲突解决策略

### 1. 映射冲突处理

```typescript
interface MappingConflict {
  type: 'duplicate_mapping' | 'circular_reference' | 'inconsistent_hierarchy';
  sourceOrg: string;
  conflictingMappings: OrganizationMapping[];
  suggestedResolution: ConflictResolution;
}

interface ConflictResolution {
  strategy: 'manual_review' | 'auto_merge' | 'create_separate' | 'use_latest';
  confidence: number;
  reasoning: string;
}

export class ConflictResolutionService {
  async resolveConflicts(conflicts: MappingConflict[]): Promise<void> {
    for (const conflict of conflicts) {
      switch (conflict.suggestedResolution.strategy) {
        case 'auto_merge':
          await this.autoMergeOrganizations(conflict);
          break;
        case 'create_separate':
          await this.createSeparateMapping(conflict);
          break;
        case 'manual_review':
          await this.flagForManualReview(conflict);
          break;
      }
    }
  }
}
```

### 2. 数据一致性保证

```typescript
export class ConsistencyManager {
  /**
   * 定期一致性检查
   */
  @Cron('0 2 * * *') // 每天凌晨2点
  async performConsistencyCheck() {
    const applications = await this.getRegisteredApplications();
    
    for (const app of applications) {
      try {
        // 1. 拉取应用最新组织架构
        const latestOrgData = await this.fetchApplicationOrganizations(app.id);
        
        // 2. 比较与IDP中的映射关系
        const inconsistencies = await this.detectInconsistencies(
          app.id, 
          latestOrgData
        );
        
        // 3. 自动修复或标记人工处理
        await this.handleInconsistencies(inconsistencies);
        
      } catch (error) {
        logger.error(`一致性检查失败: ${app.id}`, error);
      }
    }
  }
}
```

---

## 📊 监控和管理

### 1. 联邦状态监控

```typescript
// 联邦组织架构健康度监控
export interface FederationHealthMetrics {
  totalApplications: number;
  activeSyncApplications: number;
  mappingAccuracy: number;        // 映射准确度
  syncLatency: number;           // 同步延迟
  conflictRate: number;          // 冲突率
  lastSyncTimes: Record<string, Date>;
}

export class FederationMonitoringService {
  async getHealthMetrics(): Promise<FederationHealthMetrics> {
    return {
      totalApplications: await this.countRegisteredApps(),
      activeSyncApplications: await this.countActiveSyncApps(),
      mappingAccuracy: await this.calculateMappingAccuracy(),
      syncLatency: await this.calculateAverageSyncLatency(),
      conflictRate: await this.calculateConflictRate(),
      lastSyncTimes: await this.getLastSyncTimes()
    };
  }
}
```

### 2. 管理界面功能

- **应用注册管理**: 注册和管理接入的应用
- **映射关系可视化**: 图形化显示组织架构映射
- **冲突解决工具**: 人工处理映射冲突
- **同步状态监控**: 实时监控同步状态
- **一致性报告**: 定期生成一致性报告

---

## 🚀 实施建议

### 阶段性实施

1. **第一阶段**: 实现基础联邦框架
2. **第二阶段**: 开发智能映射算法
3. **第三阶段**: 完善冲突解决机制
4. **第四阶段**: 优化性能和监控

### 最佳实践

1. **渐进式迁移**: 逐步将现有应用接入联邦架构
2. **映射验证**: 建立映射关系的验证和审核机制
3. **备份策略**: 保留原始组织架构数据作为备份
4. **权限降级**: 在映射失败时提供权限降级策略

---

*此方案既保持了各应用的数据主权，又实现了统一的权限管理，是解决多应用组织架构冲突的最佳方案。*
