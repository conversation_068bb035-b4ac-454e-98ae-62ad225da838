# 身份提供商功能验证指南

## 概述

本指南介绍如何运行测试来验证身份提供商的核心功能是否正常工作。我们已经创建了全面的功能验证测试，涵盖了系统的关键业务逻辑。

## 快速开始

### 方法一：使用自动化脚本（推荐）

```bash
# 运行完整的功能验证测试
./scripts/run-functional-tests.sh
```

这个脚本会自动：
- 检查运行环境
- 安装必要的依赖
- 生成数据库客户端
- 运行数据库迁移
- 执行所有功能验证测试
- 显示测试结果摘要

### 方法二：手动运行测试

```bash
# 1. 确保依赖已安装
npm install

# 2. 生成数据库客户端
npm run db:generate

# 3. 运行数据库迁移
npm run db:migrate

# 4. 运行功能验证测试
npm test -- --testPathPattern=functional.test.ts
```

## 已验证的功能模块

### ✅ 密码安全功能
- **密码加密**: 使用 bcrypt 进行安全的密码哈希
- **密码验证**: 验证用户输入的密码是否正确
- **密码强度检查**: 确保密码符合安全要求
  - 至少8位长度
  - 包含大写字母
  - 包含小写字母
  - 包含数字
  - 包含特殊字符

### ✅ JWT令牌管理
- **令牌生成**: 创建包含用户信息的JWT令牌
- **令牌验证**: 验证令牌的有效性和完整性
- **错误处理**: 正确处理无效或过期的令牌
- **安全性**: 防止使用错误密钥签名的令牌

### ✅ 数据验证功能
- **邮箱格式验证**: 严格的邮箱地址格式检查
  - 支持标准邮箱格式
  - 拒绝无效格式
  - 符合RFC标准
- **输入数据清理**: 防止恶意输入和数据注入

### ✅ 工具函数
- **UUID生成**: 生成符合标准的唯一标识符
- **时间处理**: 正确计算过期时间和时间差
- **数据类型验证**: 确保数据类型的正确性

## 测试结果解读

### 成功的测试输出示例

```
✅ 功能验证测试完成！
==================================================
📊 已验证的功能模块：
  ✓ 密码加密和验证 (bcrypt)
  ✓ JWT令牌生成和验证
  ✓ 邮箱格式验证
  ✓ 密码强度检查
  ✓ UUID生成
  ✓ 时间处理

🎉 身份提供商核心功能验证通过！

Test Suites: 1 passed, 1 total
Tests:       15 passed, 15 total
```

### 测试失败时的处理

如果测试失败，请检查：

1. **环境配置**
   ```bash
   # 检查Node.js版本
   node --version  # 应该 >= 18.0.0
   
   # 检查npm版本
   npm --version
   ```

2. **依赖安装**
   ```bash
   # 重新安装依赖
   rm -rf node_modules package-lock.json
   npm install
   ```

3. **数据库设置**
   ```bash
   # 重新生成数据库客户端
   npm run db:generate
   
   # 重新运行迁移
   npm run db:migrate
   ```

## 测试覆盖范围

### 当前已覆盖的功能
- ✅ 密码安全处理
- ✅ JWT令牌管理
- ✅ 数据验证
- ✅ 工具函数
- ✅ 基础数据库操作

### 计划中的测试扩展
- 🔄 完整的用户注册流程
- 🔄 用户登录流程
- 🔄 OAuth第三方登录
- 🔄 多因素认证(MFA)
- 🔄 会话管理
- 🔄 API端点集成测试

## 持续集成

建议在以下情况下运行功能验证测试：

1. **代码提交前**: 确保新代码不会破坏现有功能
2. **部署前**: 验证生产环境的功能完整性
3. **定期检查**: 定期运行测试确保系统稳定性

## 故障排除

### 常见问题

**问题1**: 数据库连接失败
```bash
# 解决方案：重新生成数据库
npm run db:reset
npm run db:migrate
```

**问题2**: 模块导入错误
```bash
# 解决方案：清理并重新安装依赖
npm run clean
npm install
```

**问题3**: TypeScript编译错误
```bash
# 解决方案：重新构建项目
npm run build
```

## 联系支持

如果遇到测试相关的问题，请：

1. 检查本指南的故障排除部分
2. 查看 `docs/testing-guide.md` 获取更详细的测试信息
3. 检查项目的 GitHub Issues
4. 联系开发团队获取支持

---

**最后更新**: 2025-07-31  
**测试版本**: v1.0.0  
**支持的Node.js版本**: >= 18.0.0
