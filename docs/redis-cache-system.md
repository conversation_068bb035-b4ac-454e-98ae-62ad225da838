# Redis缓存系统文档

## 概述

本身份提供商系统集成了完整的Redis缓存解决方案，提供高性能的数据缓存、会话管理、JWT黑名单和健康监控功能。该系统采用分层架构设计，支持多种缓存策略和高级功能。

## 核心特性

### ✅ 已实现的缓存功能

#### 1. **高级缓存管理器** (`CacheManager`)
- **✅ 多种缓存策略**：
  - 写穿透（Write-Through）
  - 写回（Write-Behind）
  - 写绕过（Write-Around）
  - 读穿透（Read-Through）
  - 缓存旁路（Cache-Aside）
- **✅ 分布式锁** - 基于Redis的分布式锁机制
- **✅ 缓存预热** - 批量预加载热点数据
- **✅ 标签管理** - 按标签批量失效缓存
- **✅ 统计监控** - 命中率、响应时间等指标
- **✅ 事件系统** - 缓存操作事件通知

#### 2. **会话管理器** (`SessionManager`)
- **✅ 完整的会话生命周期管理**：
  - 会话创建和验证
  - 自动过期清理
  - 设备信息跟踪
  - IP地址验证
- **✅ 会话限制** - 每用户最大会话数控制
- **✅ 设备信任** - 受信任设备缓存
- **✅ 会话统计** - 详细的使用统计信息
- **✅ 批量操作** - 用户会话批量管理

#### 3. **JWT黑名单服务** (`JWTBlacklistService`)
- **✅ JWT令牌撤销管理**：
  - 单个令牌撤销
  - 用户所有令牌撤销
  - 按原因分类管理
- **✅ 自动清理** - 过期黑名单条目清理
- **✅ 统计分析** - 撤销原因和趋势分析
- **✅ 索引优化** - 用户和原因索引加速查询

#### 4. **健康监控服务** (`RedisHealthService`)
- **✅ 实时健康检查**：
  - 连接状态监控
  - 性能指标收集
  - 内存使用分析
  - 响应时间监控
- **✅ 告警系统** - 异常状态自动告警
- **✅ 趋势分析** - 历史数据趋势分析
- **✅ 自动恢复** - 健康状态自动检测

#### 5. **速率限制服务** (`RateLimiterService`)
- **✅ 多种限流算法**：
  - 令牌桶算法
  - 漏桶算法
  - 固定窗口算法
  - 滑动窗口算法
- **✅ 灵活配置** - 支持自定义限流策略
- **✅ 批量检查** - 批量速率限制检查
- **✅ 自动清理** - 过期限流数据清理

#### 6. **HTTP缓存中间件**
- **✅ 响应缓存** - 自动缓存HTTP响应
- **✅ 条件缓存** - 基于条件的智能缓存
- **✅ 缓存失效** - 自动缓存失效机制
- **✅ 用户特定缓存** - 用户级别的缓存隔离

## 架构设计

### 分层架构

```
┌─────────────────────────────────────────────────────────────────┐
│  API层 (缓存管理接口)                                           │
├─────────────────────────────────────────────────────────────────┤
│  中间件层 (HTTP缓存、速率限制)                                  │
├─────────────────────────────────────────────────────────────────┤
│  服务层 (缓存管理器、会话管理器、黑名单服务、健康监控)          │
├─────────────────────────────────────────────────────────────────┤
│  Redis客户端层 (连接管理、命令执行)                             │
├─────────────────────────────────────────────────────────────────┤
│  Redis服务器 (数据存储、持久化)                                 │
└─────────────────────────────────────────────────────────────────┘
```

### 数据结构设计

#### Redis键命名规范
```typescript
// 用户会话
session:{sessionId}
user:sessions:{userId}

// JWT黑名单
jwt:blacklist:{jti}
blacklist:user:{userId}
blacklist:reason:{reason}

// 缓存数据
cache:{namespace}:{key}
lock:{resource}

// 速率限制
ratelimit:bucket:{key}
ratelimit:window:{key}:{window}
ratelimit:sliding:{key}

// 统计数据
stats:cache:{namespace}
stats:blacklist:daily:{date}
```

## API接口

### 缓存管理API

| 端点 | 方法 | 功能 | 权限 |
|------|------|------|------|
| `/api/v1/cache/stats` | GET | 获取缓存统计 | admin/operator |
| `/api/v1/cache/health` | GET | 获取Redis健康状态 | admin/operator |
| `/api/v1/cache/sessions/stats` | GET | 获取会话统计 | admin/operator |
| `/api/v1/cache/blacklist/stats` | GET | 获取黑名单统计 | admin/operator |
| `/api/v1/cache/get/:key` | GET | 获取缓存值 | admin/operator |
| `/api/v1/cache/set` | POST | 设置缓存值 | admin |
| `/api/v1/cache/delete/:key` | DELETE | 删除缓存值 | admin |
| `/api/v1/cache/mget` | POST | 批量获取缓存 | admin/operator |
| `/api/v1/cache/mset` | POST | 批量设置缓存 | admin |
| `/api/v1/cache/cleanup` | POST | 清理过期缓存 | admin |
| `/api/v1/cache/stats/reset` | POST | 重置统计信息 | admin |
| `/api/v1/cache/command` | POST | 执行Redis命令 | admin |

### 使用示例

#### 获取缓存统计
```bash
curl -X GET http://localhost:3000/api/v1/cache/stats \
  -H "Authorization: Bearer YOUR_TOKEN"
```

响应：
```json
{
  "success": true,
  "data": {
    "stats": {
      "default": {
        "hits": 150,
        "misses": 25,
        "hitRate": 0.857,
        "totalRequests": 175,
        "avgResponseTime": 15.5
      }
    },
    "timestamp": "2024-01-01T12:00:00.000Z"
  }
}
```

#### 获取Redis健康状态
```bash
curl -X GET http://localhost:3000/api/v1/cache/health \
  -H "Authorization: Bearer YOUR_TOKEN"
```

响应：
```json
{
  "success": true,
  "data": {
    "current": {
      "status": "healthy",
      "metrics": {
        "connectedClients": 5,
        "usedMemory": 1024000,
        "responseTime": 15,
        "hitRate": 85.7
      },
      "issues": [],
      "recommendations": []
    },
    "trend": {
      "avgResponseTime": 12.3,
      "avgMemoryUsage": 45.2
    },
    "isConnected": true
  }
}
```

#### 设置缓存值
```bash
curl -X POST http://localhost:3000/api/v1/cache/set \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "key": "user:123:profile",
    "value": {"id": 123, "name": "John Doe"},
    "ttl": 3600,
    "namespace": "user"
  }'
```

## 中间件使用

### HTTP缓存中间件

```typescript
import { httpCache, apiCache, userCache } from '@/middleware/cache.middleware';

// 通用HTTP缓存
app.use('/api/public', httpCache({ ttl: 300 }));

// API响应缓存
app.use('/api/v1/users', apiCache(600));

// 用户特定缓存
app.use('/api/v1/profile', userCache({ ttl: 1800 }));
```

### 速率限制中间件

```typescript
import { rateLimiterService, RateLimitAlgorithm } from '@/services/rate-limiter.service';

// 创建速率限制中间件
const createRateLimit = (algorithm: RateLimitAlgorithm, limit: number, windowMs: number) => {
  return async (req: Request, res: Response, next: NextFunction) => {
    const key = req.ip;
    const result = await rateLimiterService.checkRateLimit(key, {
      algorithm,
      limit,
      windowMs
    });

    if (!result.allowed) {
      return res.status(429).json({
        error: 'Too Many Requests',
        retryAfter: result.retryAfter
      });
    }

    next();
  };
};

// 应用速率限制
app.use('/api/auth/login', createRateLimit(
  RateLimitAlgorithm.SLIDING_WINDOW, 
  5, 
  15 * 60 * 1000 // 15分钟5次
));
```

## 配置管理

### Redis配置

```typescript
// src/config/redis.ts
export const redisConfig = {
  host: process.env.REDIS_HOST || 'localhost',
  port: parseInt(process.env.REDIS_PORT || '6379'),
  password: process.env.REDIS_PASSWORD,
  db: parseInt(process.env.REDIS_DB || '0'),
  retryDelayOnFailover: 100,
  maxRetriesPerRequest: 3,
  lazyConnect: true,
  keepAlive: 30000,
  family: 4,
  keyPrefix: process.env.REDIS_KEY_PREFIX || 'idp:'
};
```

### 缓存策略配置

```typescript
// 缓存配置示例
const cacheConfig = {
  // 用户配置文件缓存
  userProfile: {
    strategy: CacheStrategy.CACHE_ASIDE,
    ttl: 3600, // 1小时
    namespace: 'user'
  },
  
  // API响应缓存
  apiResponse: {
    strategy: CacheStrategy.WRITE_THROUGH,
    ttl: 300, // 5分钟
    namespace: 'api'
  },
  
  // 会话缓存
  session: {
    strategy: CacheStrategy.WRITE_THROUGH,
    ttl: 86400, // 24小时
    namespace: 'session'
  }
};
```

## 监控和告警

### 健康检查配置

```typescript
const healthConfig = {
  checkInterval: 30000,           // 30秒检查一次
  responseTimeThreshold: 100,     // 响应时间阈值100ms
  memoryUsageThreshold: 80,       // 内存使用阈值80%
  errorRateThreshold: 5,          // 错误率阈值5%
  hitRateThreshold: 70,           // 命中率阈值70%
  enableAlerts: true,
  alertCooldown: 300000           // 告警冷却5分钟
};
```

### 告警事件监听

```typescript
redisHealthService.on('alert', (result) => {
  logger.warn('Redis健康告警', {
    status: result.status,
    issues: result.issues,
    recommendations: result.recommendations
  });
  
  // 发送告警通知
  notificationService.sendAlert({
    type: 'redis_health',
    severity: result.status === 'unhealthy' ? 'critical' : 'warning',
    message: `Redis状态: ${result.status}`,
    details: result.issues
  });
});
```

## 性能优化

### 1. **连接池优化**
- 合理配置连接池大小
- 启用连接保活机制
- 设置适当的超时时间

### 2. **内存优化**
- 使用合适的数据结构
- 设置合理的TTL
- 定期清理过期数据

### 3. **网络优化**
- 使用管道（Pipeline）批量操作
- 启用压缩减少网络传输
- 就近部署Redis实例

### 4. **缓存策略优化**
- 根据业务场景选择合适的缓存策略
- 实施缓存预热机制
- 避免缓存雪崩和穿透

## 故障排除

### 常见问题

1. **连接超时**
   - 检查网络连接
   - 验证Redis服务状态
   - 调整超时配置

2. **内存不足**
   - 检查内存使用情况
   - 清理过期数据
   - 优化数据结构

3. **性能下降**
   - 分析慢查询日志
   - 检查命中率
   - 优化缓存策略

4. **数据不一致**
   - 检查缓存失效机制
   - 验证数据同步逻辑
   - 实施强一致性策略

### 监控指标

- **连接指标**：连接数、阻塞客户端数
- **性能指标**：QPS、响应时间、命中率
- **内存指标**：内存使用量、碎片率
- **错误指标**：错误率、超时率

## 最佳实践

### 1. **键命名规范**
- 使用有意义的前缀
- 保持命名一致性
- 避免过长的键名

### 2. **TTL设置**
- 根据数据特性设置合理TTL
- 避免大量数据同时过期
- 使用随机TTL防止缓存雪崩

### 3. **数据序列化**
- 选择高效的序列化格式
- 考虑数据压缩
- 处理序列化异常

### 4. **错误处理**
- 实施优雅降级
- 记录详细错误日志
- 提供备用方案

通过这个完整的Redis缓存系统，身份提供商能够提供高性能、高可用的缓存服务，显著提升系统响应速度和用户体验。
