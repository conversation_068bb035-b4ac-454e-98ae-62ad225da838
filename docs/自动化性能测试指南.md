# 自动化性能测试指南

## 📋 概述

本项目集成了完整的自动化性能测试系统，支持定时执行性能测试、基准测试和回归检测。系统能够自动监控应用性能变化，及时发现性能回归问题，为系统优化提供数据支持。

## 🏗️ 系统架构

### 核心组件
- **AutomatedPerformanceTestingService**: 自动化性能测试服务
- **PerformanceTestService**: 性能测试执行引擎
- **BenchmarkService**: 基准测试服务
- **回归检测引擎**: 性能回归分析和报告

### 测试类型
- **负载测试 (Load Test)**: 模拟正常业务负载
- **压力测试 (Stress Test)**: 测试系统极限承载能力
- **峰值测试 (Spike Test)**: 测试突发流量处理能力
- **基准测试 (Baseline Test)**: 建立性能基准线
- **耐久性测试 (Endurance Test)**: 测试长时间运行稳定性

## 🚀 快速开始

### 默认测试计划

系统预配置了以下测试计划：

#### 1. 每日负载测试
- **执行时间**: 每天凌晨3点
- **测试类型**: 负载测试
- **配置**: 50并发用户，5分钟测试，目标100 RPS
- **场景**: 登录(30%) + API访问(50%) + OAuth流程(20%)

#### 2. 每周压力测试
- **执行时间**: 每周日凌晨2点
- **测试类型**: 压力测试
- **配置**: 200并发用户，10分钟测试，目标500 RPS

#### 3. 每小时基准测试
- **执行时间**: 每小时执行
- **测试类型**: 基准测试
- **配置**: 10并发用户，1分钟测试

### 启动服务

自动化性能测试服务会在应用启动时自动初始化：

```typescript
// 服务会自动启动所有启用的测试计划
await automatedPerformanceTestingService.start();
```

## 🔧 配置选项

### 测试计划配置

```typescript
interface PerformanceTestSchedule {
  id: string;                    // 计划ID
  name: string;                  // 计划名称
  cronExpression: string;        // Cron表达式
  enabled: boolean;              // 是否启用
  testType: TestType;           // 测试类型
  config: TestConfig;           // 测试配置
  scenarios: TestScenario[];    // 测试场景
  thresholds: PerformanceThresholds; // 性能阈值
}
```

### 性能阈值配置

```typescript
interface PerformanceThresholds {
  responseTime: {
    avg: number;    // 平均响应时间阈值(ms)
    p95: number;    // 95%响应时间阈值(ms)
    p99: number;    // 99%响应时间阈值(ms)
  };
  throughput: {
    min: number;    // 最小吞吐量阈值(RPS)
  };
  errorRate: {
    max: number;    // 最大错误率阈值(%)
  };
  resourceUsage: {
    cpu: number;    // CPU使用率阈值(%)
    memory: number; // 内存使用率阈值(%)
  };
}
```

## 📊 API接口

### 获取测试计划列表
```http
GET /api/v1/performance/automated/schedules
Authorization: Bearer <token>
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "schedules": [
      {
        "id": "daily-load-test",
        "name": "每日负载测试",
        "cronExpression": "0 3 * * *",
        "enabled": true,
        "testType": "load",
        "lastRun": "2024-01-01T03:00:00.000Z",
        "nextRun": "2024-01-02T03:00:00.000Z"
      }
    ],
    "totalCount": 3,
    "activeCount": 2
  }
}
```

### 获取测试历史
```http
GET /api/v1/performance/automated/history/:scheduleId?limit=50
Authorization: Bearer <token>
```

### 手动触发测试
```http
POST /api/v1/performance/automated/trigger/:scheduleId
Authorization: Bearer <token>
```

### 获取回归报告
```http
GET /api/v1/performance/automated/regression/:scheduleId?days=7
Authorization: Bearer <token>
```

## 🔍 回归检测

### 检测指标

系统自动检测以下性能指标的回归：

1. **响应时间回归**
   - 平均响应时间退化 > 20%
   - P95响应时间退化 > 25%
   - P99响应时间退化 > 30%

2. **吞吐量回归**
   - 吞吐量下降 > 15%

3. **错误率回归**
   - 错误率增加 > 1%

### 回归严重级别

- **Critical**: 严重回归，需要立即处理
  - 响应时间退化 > 50%
  - 吞吐量下降 > 30%
  - 错误率增加 > 5%

- **Warning**: 警告级别，需要关注
  - 响应时间退化 20-50%
  - 吞吐量下降 15-30%
  - 错误率增加 1-5%

### 回归报告示例

```json
{
  "hasRegression": true,
  "regressions": [
    {
      "metric": "avg_response_time",
      "severity": "warning",
      "currentValue": 250,
      "baselineValue": 200,
      "degradationPercent": 25.0,
      "description": "平均响应时间退化 25.0%"
    }
  ],
  "improvements": [
    {
      "metric": "throughput",
      "currentValue": 105,
      "baselineValue": 95,
      "improvementPercent": 10.5,
      "description": "吞吐量改进 10.5%"
    }
  ]
}
```

## 📈 监控和告警

### 性能指标监控

系统自动收集以下指标：

- `performance_test_executions_total`: 测试执行总数
- `performance_test_duration_seconds`: 测试执行时间
- `performance_regression_detected`: 检测到的回归数量
- `performance_test_failures`: 测试失败次数

### 告警机制

当检测到性能回归时，系统会：

1. **记录日志**: 详细的回归信息
2. **发送通知**: 邮件/Slack通知（需配置）
3. **更新指标**: Prometheus指标更新
4. **生成报告**: 详细的回归分析报告

## 🛠️ 测试场景

### 内置测试场景

#### 1. 登录场景 (login_scenario)
- 权重: 30%
- 操作: 用户登录流程
- 验证: 登录成功响应

#### 2. API访问场景 (api_access_scenario)
- 权重: 50%
- 操作: 常用API端点访问
- 验证: API响应正确性

#### 3. OAuth流程场景 (oauth_flow_scenario)
- 权重: 20%
- 操作: OAuth授权流程
- 验证: 授权码获取成功

### 自定义测试场景

```typescript
const customScenario = {
  name: 'custom_scenario',
  weight: 25,
  execute: async () => {
    // 自定义测试逻辑
    const response = await fetch('/api/custom-endpoint');
    return { 
      success: response.ok,
      duration: response.headers.get('x-response-time')
    };
  }
};
```

## 📋 最佳实践

### 1. 测试计划设计
- **频率适中**: 避免过于频繁影响生产环境
- **时间错开**: 不同类型测试错开执行时间
- **资源考虑**: 考虑测试对系统资源的影响

### 2. 阈值设置
- **基于历史数据**: 根据历史性能数据设置合理阈值
- **分级告警**: 设置不同严重级别的阈值
- **定期调整**: 根据系统优化情况调整阈值

### 3. 回归分析
- **趋势分析**: 关注性能趋势而非单次异常
- **环境因素**: 考虑环境变化对性能的影响
- **业务关联**: 将性能变化与业务变更关联

### 4. 结果处理
- **及时响应**: 对严重回归及时处理
- **根因分析**: 深入分析性能回归的根本原因
- **持续改进**: 基于测试结果持续优化系统

## 🔧 故障排除

### 常见问题

#### 1. 测试执行失败
- 检查测试服务器资源是否充足
- 验证测试场景配置是否正确
- 查看详细错误日志

#### 2. 回归检测误报
- 检查基准数据是否过时
- 验证测试环境是否一致
- 调整回归检测阈值

#### 3. 性能数据异常
- 检查网络连接状况
- 验证数据库性能
- 查看系统资源使用情况

### 调试技巧

1. **启用详细日志**
```bash
LOG_LEVEL=debug npm start
```

2. **手动执行测试**
```bash
curl -X POST /api/v1/performance/automated/trigger/daily-load-test \
  -H "Authorization: Bearer <token>"
```

3. **查看测试历史**
```bash
curl /api/v1/performance/automated/history/daily-load-test \
  -H "Authorization: Bearer <token>"
```

## 📚 扩展开发

### 添加新的测试计划

```typescript
const newSchedule: PerformanceTestSchedule = {
  id: 'custom-test',
  name: '自定义测试',
  cronExpression: '0 */2 * * *', // 每2小时
  enabled: true,
  testType: TestType.LOAD,
  config: {
    concurrency: 20,
    duration: 120000,
    targetRPS: 50
  },
  scenarios: [customScenario],
  thresholds: {
    responseTime: { avg: 300, p95: 600, p99: 1200 },
    throughput: { min: 45 },
    errorRate: { max: 2 },
    resourceUsage: { cpu: 70, memory: 75 }
  }
};
```

### 自定义回归检测逻辑

```typescript
class CustomRegressionDetector {
  detectRegression(current: any, baseline: any): RegressionResult {
    // 自定义回归检测逻辑
    return {
      hasRegression: false,
      regressions: [],
      improvements: []
    };
  }
}
```

---

*文档版本: 1.0*  
*最后更新: 2025-08-28*
