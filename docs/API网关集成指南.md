# API网关集成指南

## 📋 概述

本指南详细说明如何在API网关层面集成联邦权限验证，实现统一的权限管理，避免每个后端应用重复实现权限验证逻辑。

**支持的网关**: Kong、Nginx、Envoy、Traefik、AWS API Gateway、Azure API Management

---

## 🏗️ 架构设计

### 网关层权限验证架构

```
┌─────────────────────────────────────────────────────────────┐
│                    API 网关层                                │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   权限验证插件   │  │   数据过滤中间件 │  │   缓存管理器     │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
           │                    │                    │
           ▼                    ▼                    ▼
┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐
│   后端应用A      │  │   后端应用B      │  │   后端应用C      │
│  (无权限验证)    │  │  (无权限验证)    │  │  (无权限验证)    │
└─────────────────┘  └─────────────────┘  └─────────────────┘
```

### 核心优势

- **统一权限验证**: 所有API请求在网关层统一验证权限
- **性能优化**: 网关层缓存减少后端压力
- **简化开发**: 后端应用无需实现权限验证逻辑
- **集中管理**: 权限策略在网关层集中配置和管理
- **安全增强**: 在网络边界进行权限控制

---

## 🔧 Kong 网关集成

### 1. 安装联邦权限插件

```bash
# 创建Kong插件目录
mkdir -p /usr/local/share/lua/5.1/kong/plugins/federated-auth

# 下载联邦权限插件
curl -o /usr/local/share/lua/5.1/kong/plugins/federated-auth/handler.lua \
  https://github.com/your-org/kong-federated-auth/releases/latest/download/handler.lua

curl -o /usr/local/share/lua/5.1/kong/plugins/federated-auth/schema.lua \
  https://github.com/your-org/kong-federated-auth/releases/latest/download/schema.lua
```

### 2. Kong插件配置

```lua
-- /usr/local/share/lua/5.1/kong/plugins/federated-auth/handler.lua
local BasePlugin = require "kong.plugins.base_plugin"
local http = require "resty.http"
local cjson = require "cjson"

local FederatedAuthHandler = BasePlugin:extend()

FederatedAuthHandler.PRIORITY = 1000
FederatedAuthHandler.VERSION = "1.0.0"

function FederatedAuthHandler:new()
  FederatedAuthHandler.super.new(self, "federated-auth")
end

function FederatedAuthHandler:access(conf)
  FederatedAuthHandler.super.access(self)
  
  -- 获取请求信息
  local headers = kong.request.get_headers()
  local path = kong.request.get_path()
  local method = kong.request.get_method()
  
  -- 提取认证信息
  local auth_header = headers["authorization"]
  if not auth_header then
    return kong.response.exit(401, {
      success = false,
      error = "MISSING_AUTHORIZATION",
      message = "缺少认证头"
    })
  end
  
  local token = auth_header:match("Bearer%s+(.+)")
  if not token then
    return kong.response.exit(401, {
      success = false,
      error = "INVALID_TOKEN_FORMAT",
      message = "无效的token格式"
    })
  end
  
  -- 解析JWT获取用户信息
  local jwt_payload = self:decode_jwt(token)
  if not jwt_payload then
    return kong.response.exit(401, {
      success = false,
      error = "INVALID_TOKEN",
      message = "无效的token"
    })
  end
  
  -- 构建权限验证请求
  local permission_request = {
    userId = jwt_payload.sub,
    applicationId = conf.application_id,
    permission = self:extract_permission(method, path),
    organizationContext = headers["x-organization-context"]
  }
  
  -- 调用联邦权限验证API
  local httpc = http.new()
  httpc:set_timeout(conf.timeout or 5000)
  
  local res, err = httpc:request_uri(conf.idp_endpoint .. "/api/v1/federation/permissions/federated-check", {
    method = "POST",
    headers = {
      ["Content-Type"] = "application/json",
      ["Authorization"] = "Bearer " .. conf.service_token
    },
    body = cjson.encode(permission_request)
  })
  
  if not res then
    kong.log.err("权限验证请求失败: ", err)
    return kong.response.exit(500, {
      success = false,
      error = "PERMISSION_CHECK_FAILED",
      message = "权限验证服务不可用"
    })
  end
  
  local result = cjson.decode(res.body)
  
  if not result.success or not result.data.granted then
    return kong.response.exit(403, {
      success = false,
      error = "INSUFFICIENT_PERMISSION",
      message = "权限不足",
      required = permission_request.permission,
      mappingConfidence = result.data and result.data.mappingConfidence or 0
    })
  end
  
  -- 设置上下文信息供后端使用
  kong.service.request.set_header("X-User-ID", jwt_payload.sub)
  kong.service.request.set_header("X-User-Permissions", cjson.encode(result.data.allPermissions))
  kong.service.request.set_header("X-Organization-Memberships", cjson.encode(result.data.organizationMemberships))
  kong.service.request.set_header("X-Mapping-Confidence", tostring(result.data.mappingConfidence))
end

function FederatedAuthHandler:extract_permission(method, path)
  -- 根据HTTP方法和路径提取所需权限
  local permission_map = {
    ["GET"] = "read",
    ["POST"] = "write",
    ["PUT"] = "write",
    ["PATCH"] = "write",
    ["DELETE"] = "delete"
  }
  
  local action = permission_map[method] or "read"
  local resource = path:match("^/api/v%d+/([^/]+)") or "unknown"
  
  return action .. ":" .. resource
end

function FederatedAuthHandler:decode_jwt(token)
  -- JWT解码逻辑 (简化版本)
  local jwt = require "resty.jwt"
  local jwt_obj = jwt:verify(kong.configuration.jwt_secret, token)
  
  if jwt_obj and jwt_obj.valid then
    return jwt_obj.payload
  end
  
  return nil
end

return FederatedAuthHandler
```

### 3. Kong服务配置

```bash
# 创建服务
curl -X POST http://kong-admin:8001/services \
  -d "name=my-backend-service" \
  -d "url=http://backend-app:3000"

# 创建路由
curl -X POST http://kong-admin:8001/services/my-backend-service/routes \
  -d "paths[]=/api/v1" \
  -d "methods[]=GET" \
  -d "methods[]=POST" \
  -d "methods[]=PUT" \
  -d "methods[]=DELETE"

# 启用联邦权限插件
curl -X POST http://kong-admin:8001/services/my-backend-service/plugins \
  -d "name=federated-auth" \
  -d "config.application_id=my-app-id" \
  -d "config.idp_endpoint=https://id-provider.example.com" \
  -d "config.service_token=YOUR_SERVICE_TOKEN" \
  -d "config.timeout=5000"
```

---

## 🌐 Nginx 网关集成

### 1. Nginx配置

```nginx
# /etc/nginx/conf.d/federated-auth.conf
upstream idp_backend {
    server id-provider.example.com:443;
    keepalive 32;
}

upstream app_backend {
    server backend-app:3000;
    keepalive 32;
}

# Lua脚本路径
lua_package_path "/etc/nginx/lua/?.lua;;";

# 共享内存用于缓存
lua_shared_dict permission_cache 10m;
lua_shared_dict user_cache 5m;

server {
    listen 80;
    server_name api-gateway.example.com;
    
    # 联邦权限验证location
    location = /auth {
        internal;
        proxy_pass http://idp_backend/api/v1/federation/permissions/federated-check;
        proxy_pass_request_body off;
        proxy_set_header Content-Length "";
        proxy_set_header X-Original-URI $request_uri;
        proxy_set_header X-Original-Method $request_method;
        proxy_set_header Authorization "Bearer $service_token";
    }
    
    # API路由
    location /api/ {
        # 权限验证
        access_by_lua_block {
            local federated_auth = require "federated_auth"
            federated_auth.check_permission()
        }
        
        # 代理到后端
        proxy_pass http://app_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        
        # 传递权限上下文
        proxy_set_header X-User-ID $user_id;
        proxy_set_header X-User-Permissions $user_permissions;
        proxy_set_header X-Organization-Memberships $org_memberships;
        proxy_set_header X-Mapping-Confidence $mapping_confidence;
    }
}
```

### 2. Lua权限验证脚本

```lua
-- /etc/nginx/lua/federated_auth.lua
local http = require "resty.http"
local cjson = require "cjson"
local jwt = require "resty.jwt"

local _M = {}

function _M.check_permission()
    -- 获取请求信息
    local headers = ngx.req.get_headers()
    local method = ngx.req.get_method()
    local uri = ngx.var.request_uri
    
    -- 检查认证头
    local auth_header = headers["authorization"]
    if not auth_header then
        ngx.status = 401
        ngx.say(cjson.encode({
            success = false,
            error = "MISSING_AUTHORIZATION",
            message = "缺少认证头"
        }))
        ngx.exit(401)
    end
    
    -- 提取token
    local token = auth_header:match("Bearer%s+(.+)")
    if not token then
        ngx.status = 401
        ngx.say(cjson.encode({
            success = false,
            error = "INVALID_TOKEN_FORMAT",
            message = "无效的token格式"
        }))
        ngx.exit(401)
    end
    
    -- 解析JWT
    local jwt_obj = jwt:verify(ngx.var.jwt_secret, token)
    if not jwt_obj or not jwt_obj.valid then
        ngx.status = 401
        ngx.say(cjson.encode({
            success = false,
            error = "INVALID_TOKEN",
            message = "无效的token"
        }))
        ngx.exit(401)
    end
    
    local user_id = jwt_obj.payload.sub
    
    -- 检查缓存
    local cache_key = user_id .. ":" .. method .. ":" .. uri
    local permission_cache = ngx.shared.permission_cache
    local cached_result = permission_cache:get(cache_key)
    
    if cached_result then
        local result = cjson.decode(cached_result)
        if result.granted then
            _M.set_context_headers(result)
            return
        else
            ngx.status = 403
            ngx.say(cjson.encode({
                success = false,
                error = "INSUFFICIENT_PERMISSION",
                message = "权限不足 (缓存)"
            }))
            ngx.exit(403)
        end
    end
    
    -- 构建权限验证请求
    local permission_request = {
        userId = user_id,
        applicationId = ngx.var.application_id,
        permission = _M.extract_permission(method, uri),
        organizationContext = headers["x-organization-context"]
    }
    
    -- 调用权限验证API
    local httpc = http.new()
    httpc:set_timeout(5000)
    
    local res, err = httpc:request_uri(ngx.var.idp_endpoint .. "/api/v1/federation/permissions/federated-check", {
        method = "POST",
        headers = {
            ["Content-Type"] = "application/json",
            ["Authorization"] = "Bearer " .. ngx.var.service_token
        },
        body = cjson.encode(permission_request)
    })
    
    if not res then
        ngx.log(ngx.ERR, "权限验证请求失败: ", err)
        ngx.status = 500
        ngx.say(cjson.encode({
            success = false,
            error = "PERMISSION_CHECK_FAILED",
            message = "权限验证服务不可用"
        }))
        ngx.exit(500)
    end
    
    local result = cjson.decode(res.body)
    
    -- 缓存结果 (5分钟)
    permission_cache:set(cache_key, cjson.encode(result.data), 300)
    
    if not result.success or not result.data.granted then
        ngx.status = 403
        ngx.say(cjson.encode({
            success = false,
            error = "INSUFFICIENT_PERMISSION",
            message = "权限不足",
            required = permission_request.permission,
            mappingConfidence = result.data and result.data.mappingConfidence or 0
        }))
        ngx.exit(403)
    end
    
    -- 设置上下文头
    _M.set_context_headers(result.data)
end

function _M.extract_permission(method, uri)
    local permission_map = {
        ["GET"] = "read",
        ["POST"] = "write",
        ["PUT"] = "write",
        ["PATCH"] = "write",
        ["DELETE"] = "delete"
    }
    
    local action = permission_map[method] or "read"
    local resource = uri:match("^/api/v%d+/([^/]+)") or "unknown"
    
    return action .. ":" .. resource
end

function _M.set_context_headers(data)
    ngx.var.user_id = data.userId
    ngx.var.user_permissions = cjson.encode(data.allPermissions or {})
    ngx.var.org_memberships = cjson.encode(data.organizationMemberships or {})
    ngx.var.mapping_confidence = tostring(data.mappingConfidence or 0)
end

return _M
```

### 3. Nginx环境变量配置

```bash
# /etc/nginx/conf.d/env.conf
env JWT_SECRET;
env APPLICATION_ID;
env IDP_ENDPOINT;
env SERVICE_TOKEN;

# 在nginx.conf中设置变量
set $jwt_secret $JWT_SECRET;
set $application_id $APPLICATION_ID;
set $idp_endpoint $IDP_ENDPOINT;
set $service_token $SERVICE_TOKEN;
```

---

## ☁️ Envoy 网关集成

### 1. Envoy配置

```yaml
# envoy.yaml
static_resources:
  listeners:
  - name: listener_0
    address:
      socket_address:
        protocol: TCP
        address: 0.0.0.0
        port_value: 8080
    filter_chains:
    - filters:
      - name: envoy.filters.network.http_connection_manager
        typed_config:
          "@type": type.googleapis.com/envoy.extensions.filters.network.http_connection_manager.v3.HttpConnectionManager
          stat_prefix: ingress_http
          access_log:
          - name: envoy.access_loggers.stdout
            typed_config:
              "@type": type.googleapis.com/envoy.extensions.access_loggers.stream.v3.StdoutAccessLog
          http_filters:
          # 联邦权限验证过滤器
          - name: envoy.filters.http.ext_authz
            typed_config:
              "@type": type.googleapis.com/envoy.extensions.filters.http.ext_authz.v3.ExtAuthz
              transport_api_version: V3
              http_service:
                server_uri:
                  uri: http://federated-auth-service:8080
                  cluster: federated_auth_cluster
                  timeout: 5s
                authorization_request:
                  allowed_headers:
                    patterns:
                    - exact: authorization
                    - exact: x-organization-context
                authorization_response:
                  allowed_upstream_headers:
                    patterns:
                    - exact: x-user-id
                    - exact: x-user-permissions
                    - exact: x-organization-memberships
                    - exact: x-mapping-confidence
          - name: envoy.filters.http.router
            typed_config:
              "@type": type.googleapis.com/envoy.extensions.filters.http.router.v3.Router
          route_config:
            name: local_route
            virtual_hosts:
            - name: local_service
              domains: ["*"]
              routes:
              - match:
                  prefix: "/api/"
                route:
                  cluster: backend_cluster

  clusters:
  - name: backend_cluster
    connect_timeout: 30s
    type: LOGICAL_DNS
    dns_lookup_family: V4_ONLY
    lb_policy: ROUND_ROBIN
    load_assignment:
      cluster_name: backend_cluster
      endpoints:
      - lb_endpoints:
        - endpoint:
            address:
              socket_address:
                address: backend-app
                port_value: 3000

  - name: federated_auth_cluster
    connect_timeout: 5s
    type: LOGICAL_DNS
    dns_lookup_family: V4_ONLY
    lb_policy: ROUND_ROBIN
    load_assignment:
      cluster_name: federated_auth_cluster
      endpoints:
      - lb_endpoints:
        - endpoint:
            address:
              socket_address:
                address: federated-auth-service
                port_value: 8080
```

### 2. 联邦权限验证服务

```go
// federated-auth-service/main.go
package main

import (
    "bytes"
    "encoding/json"
    "fmt"
    "io/ioutil"
    "net/http"
    "os"
    "strings"
    "time"
    
    "github.com/golang-jwt/jwt/v4"
    "github.com/gorilla/mux"
)

type AuthRequest struct {
    UserID              string `json:"userId"`
    ApplicationID       string `json:"applicationId"`
    Permission          string `json:"permission"`
    OrganizationContext string `json:"organizationContext,omitempty"`
}

type AuthResponse struct {
    Success bool `json:"success"`
    Data    struct {
        Granted                 bool                   `json:"granted"`
        AllPermissions         []string               `json:"allPermissions"`
        OrganizationMemberships []interface{}          `json:"organizationMemberships"`
        MappingConfidence      float64                `json:"mappingConfidence"`
    } `json:"data"`
}

func main() {
    r := mux.NewRouter()
    r.HandleFunc("/auth", handleAuth).Methods("GET", "POST")
    
    fmt.Println("联邦权限验证服务启动在端口 8080")
    http.ListenAndServe(":8080", r)
}

func handleAuth(w http.ResponseWriter, r *http.Request) {
    // 获取请求头
    authHeader := r.Header.Get("Authorization")
    if authHeader == "" {
        http.Error(w, "缺少认证头", http.StatusUnauthorized)
        return
    }
    
    // 提取token
    token := strings.TrimPrefix(authHeader, "Bearer ")
    if token == authHeader {
        http.Error(w, "无效的token格式", http.StatusUnauthorized)
        return
    }
    
    // 解析JWT
    claims, err := parseJWT(token)
    if err != nil {
        http.Error(w, "无效的token", http.StatusUnauthorized)
        return
    }
    
    // 提取权限信息
    method := r.Header.Get("X-Original-Method")
    if method == "" {
        method = r.Method
    }
    
    uri := r.Header.Get("X-Original-URI")
    if uri == "" {
        uri = r.URL.Path
    }
    
    permission := extractPermission(method, uri)
    orgContext := r.Header.Get("X-Organization-Context")
    
    // 构建权限验证请求
    authReq := AuthRequest{
        UserID:              claims["sub"].(string),
        ApplicationID:       os.Getenv("APPLICATION_ID"),
        Permission:          permission,
        OrganizationContext: orgContext,
    }
    
    // 调用IDP权限验证API
    granted, authResp, err := checkPermission(authReq)
    if err != nil {
        http.Error(w, "权限验证失败", http.StatusInternalServerError)
        return
    }
    
    if !granted {
        w.Header().Set("Content-Type", "application/json")
        w.WriteHeader(http.StatusForbidden)
        json.NewEncoder(w).Encode(map[string]interface{}{
            "success": false,
            "error":   "INSUFFICIENT_PERMISSION",
            "message": "权限不足",
        })
        return
    }
    
    // 设置响应头供后端使用
    w.Header().Set("X-User-ID", authReq.UserID)
    w.Header().Set("X-User-Permissions", strings.Join(authResp.Data.AllPermissions, ","))
    w.Header().Set("X-Mapping-Confidence", fmt.Sprintf("%.2f", authResp.Data.MappingConfidence))
    
    w.WriteHeader(http.StatusOK)
}

func parseJWT(tokenString string) (jwt.MapClaims, error) {
    token, err := jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
        return []byte(os.Getenv("JWT_SECRET")), nil
    })
    
    if err != nil {
        return nil, err
    }
    
    if claims, ok := token.Claims.(jwt.MapClaims); ok && token.Valid {
        return claims, nil
    }
    
    return nil, fmt.Errorf("无效的token")
}

func extractPermission(method, uri string) string {
    permissionMap := map[string]string{
        "GET":    "read",
        "POST":   "write",
        "PUT":    "write",
        "PATCH":  "write",
        "DELETE": "delete",
    }
    
    action := permissionMap[method]
    if action == "" {
        action = "read"
    }
    
    // 简单的资源提取逻辑
    parts := strings.Split(uri, "/")
    resource := "unknown"
    if len(parts) >= 4 {
        resource = parts[3]
    }
    
    return action + ":" + resource
}

func checkPermission(authReq AuthRequest) (bool, *AuthResponse, error) {
    // 构建请求
    reqBody, _ := json.Marshal(authReq)
    
    client := &http.Client{Timeout: 5 * time.Second}
    req, err := http.NewRequest("POST", 
        os.Getenv("IDP_ENDPOINT")+"/api/v1/federation/permissions/federated-check",
        bytes.NewBuffer(reqBody))
    if err != nil {
        return false, nil, err
    }
    
    req.Header.Set("Content-Type", "application/json")
    req.Header.Set("Authorization", "Bearer "+os.Getenv("SERVICE_TOKEN"))
    
    resp, err := client.Do(req)
    if err != nil {
        return false, nil, err
    }
    defer resp.Body.Close()
    
    body, err := ioutil.ReadAll(resp.Body)
    if err != nil {
        return false, nil, err
    }
    
    var authResp AuthResponse
    if err := json.Unmarshal(body, &authResp); err != nil {
        return false, nil, err
    }
    
    return authResp.Success && authResp.Data.Granted, &authResp, nil
}
```

---

## 🚀 部署和配置

### 1. Docker Compose部署

```yaml
# docker-compose.yml
version: '3.8'

services:
  # Kong网关
  kong:
    image: kong:latest
    environment:
      KONG_DATABASE: "off"
      KONG_DECLARATIVE_CONFIG: /kong/declarative/kong.yml
      KONG_PROXY_ACCESS_LOG: /dev/stdout
      KONG_ADMIN_ACCESS_LOG: /dev/stdout
      KONG_PROXY_ERROR_LOG: /dev/stderr
      KONG_ADMIN_ERROR_LOG: /dev/stderr
      KONG_ADMIN_LISTEN: "0.0.0.0:8001"
      KONG_PLUGINS: "bundled,federated-auth"
    volumes:
      - ./kong/plugins:/usr/local/share/lua/5.1/kong/plugins
      - ./kong/kong.yml:/kong/declarative/kong.yml
    ports:
      - "8000:8000"
      - "8001:8001"
    depends_on:
      - federated-auth-service

  # 联邦权限验证服务
  federated-auth-service:
    build: ./federated-auth-service
    environment:
      APPLICATION_ID: "my-app-id"
      IDP_ENDPOINT: "https://id-provider.example.com"
      SERVICE_TOKEN: "your-service-token"
      JWT_SECRET: "your-jwt-secret"
    ports:
      - "8080:8080"

  # 后端应用
  backend-app:
    image: your-backend-app:latest
    environment:
      PORT: 3000
    ports:
      - "3000:3000"
```

### 2. Kubernetes部署

```yaml
# k8s-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: federated-auth-service
spec:
  replicas: 3
  selector:
    matchLabels:
      app: federated-auth-service
  template:
    metadata:
      labels:
        app: federated-auth-service
    spec:
      containers:
      - name: federated-auth-service
        image: federated-auth-service:latest
        ports:
        - containerPort: 8080
        env:
        - name: APPLICATION_ID
          valueFrom:
            configMapKeyRef:
              name: federated-config
              key: application-id
        - name: IDP_ENDPOINT
          valueFrom:
            configMapKeyRef:
              name: federated-config
              key: idp-endpoint
        - name: SERVICE_TOKEN
          valueFrom:
            secretKeyRef:
              name: federated-secrets
              key: service-token
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: federated-secrets
              key: jwt-secret
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "200m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5

---
apiVersion: v1
kind: Service
metadata:
  name: federated-auth-service
spec:
  selector:
    app: federated-auth-service
  ports:
  - protocol: TCP
    port: 8080
    targetPort: 8080

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: federated-config
data:
  application-id: "my-app-id"
  idp-endpoint: "https://id-provider.example.com"

---
apiVersion: v1
kind: Secret
metadata:
  name: federated-secrets
type: Opaque
data:
  service-token: <base64-encoded-service-token>
  jwt-secret: <base64-encoded-jwt-secret>
```

---

## 📊 性能优化

### 1. 缓存策略

```javascript
// Redis缓存配置
const cacheConfig = {
  // 权限验证结果缓存
  permissionCache: {
    ttl: 300, // 5分钟
    keyPattern: "perm:{userId}:{appId}:{permission}:{orgContext}",
    maxSize: "100MB"
  },
  
  // 用户组织关系缓存
  userOrgCache: {
    ttl: 600, // 10分钟
    keyPattern: "user_org:{userId}:{appId}",
    maxSize: "50MB"
  },
  
  // 组织映射关系缓存
  mappingCache: {
    ttl: 3600, // 1小时
    keyPattern: "mapping:{appId}:{orgId}",
    maxSize: "200MB"
  }
};
```

### 2. 连接池优化

```go
// HTTP客户端连接池配置
var httpClient = &http.Client{
    Timeout: 5 * time.Second,
    Transport: &http.Transport{
        MaxIdleConns:        100,
        MaxIdleConnsPerHost: 10,
        IdleConnTimeout:     90 * time.Second,
        DisableCompression:  false,
    },
}
```

### 3. 批量权限检查

```javascript
// 批量权限验证API
const batchCheckPermissions = async (requests) => {
  const response = await fetch('/api/v1/federation/permissions/batch-check', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${serviceToken}`
    },
    body: JSON.stringify({
      requests: requests.map(req => ({
        userId: req.userId,
        applicationId: req.applicationId,
        permission: req.permission,
        organizationContext: req.organizationContext
      }))
    })
  });
  
  return await response.json();
};
```

---

## 🔍 监控和告警

### 1. 性能监控指标

```yaml
# Prometheus监控配置
- job_name: 'federated-auth-service'
  static_configs:
  - targets: ['federated-auth-service:8080']
  metrics_path: /metrics
  scrape_interval: 15s
  
  # 关键指标
  metric_relabel_configs:
  - source_labels: [__name__]
    regex: 'federated_auth_(request_duration|cache_hit_rate|error_rate)'
    target_label: __tmp_keep
    replacement: 'true'
```

### 2. 告警规则

```yaml
# alerting-rules.yml
groups:
- name: federated-auth
  rules:
  - alert: FederatedAuthHighLatency
    expr: histogram_quantile(0.95, federated_auth_request_duration_seconds) > 0.5
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "联邦权限验证延迟过高"
      description: "95%的权限验证请求延迟超过500ms"

  - alert: FederatedAuthLowCacheHitRate
    expr: federated_auth_cache_hit_rate < 0.8
    for: 10m
    labels:
      severity: warning
    annotations:
      summary: "权限缓存命中率过低"
      description: "权限缓存命中率低于80%"

  - alert: FederatedAuthHighErrorRate
    expr: rate(federated_auth_errors_total[5m]) > 0.1
    for: 2m
    labels:
      severity: critical
    annotations:
      summary: "联邦权限验证错误率过高"
      description: "权限验证错误率超过10%"
```

---

## 🛡️ 安全考虑

### 1. 网络安全

```yaml
# 网络策略配置
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: federated-auth-network-policy
spec:
  podSelector:
    matchLabels:
      app: federated-auth-service
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - podSelector:
        matchLabels:
          app: api-gateway
    ports:
    - protocol: TCP
      port: 8080
  egress:
  - to:
    - namespaceSelector:
        matchLabels:
          name: id-provider
    ports:
    - protocol: TCP
      port: 443
```

### 2. 服务认证

```go
// 服务间认证
func validateServiceToken(token string) bool {
    // 验证服务token的有效性
    claims, err := jwt.Parse(token, func(token *jwt.Token) (interface{}, error) {
        return []byte(os.Getenv("SERVICE_JWT_SECRET")), nil
    })
    
    if err != nil {
        return false
    }
    
    // 检查服务权限
    if serviceClaims, ok := claims.Claims.(jwt.MapClaims); ok {
        return serviceClaims["service"] == "api-gateway" && 
               serviceClaims["scope"] == "federated-auth"
    }
    
    return false
}
```

---

## 🎯 最佳实践

### 1. 错误处理

```javascript
// 优雅的错误处理和降级
const checkPermissionWithFallback = async (request) => {
  try {
    // 尝试联邦权限验证
    const result = await checkFederatedPermission(request);
    return result;
  } catch (error) {
    console.error('联邦权限验证失败:', error);
    
    // 降级到基础权限检查
    if (error.code === 'SERVICE_UNAVAILABLE') {
      return await checkBasicPermission(request);
    }
    
    // 记录错误并拒绝访问
    logSecurityEvent('permission_check_failed', {
      userId: request.userId,
      permission: request.permission,
      error: error.message
    });
    
    return { granted: false, reason: 'permission_check_failed' };
  }
};
```

### 2. 性能优化

```javascript
// 智能缓存策略
const smartCache = {
  // 根据权限类型设置不同的缓存时间
  getTTL: (permission) => {
    if (permission.startsWith('read:')) return 300; // 5分钟
    if (permission.startsWith('write:')) return 60; // 1分钟
    if (permission.startsWith('admin:')) return 30; // 30秒
    return 180; // 默认3分钟
  },
  
  // 预热热点数据
  preloadHotData: async () => {
    const hotUsers = await getActiveUsers();
    const hotPermissions = await getFrequentPermissions();
    
    for (const user of hotUsers) {
      for (const permission of hotPermissions) {
        await checkPermissionWithCache(user.id, permission);
      }
    }
  }
};
```

通过以上API网关集成方案，您可以在网关层实现统一的联邦权限验证，大大简化后端应用的开发复杂度，同时提供高性能、高可用的权限管理能力。
