# 管理员控制台使用指南

## 📋 概述

身份提供商管理员控制台是基于React Admin框架构建的现代化管理界面，为系统管理员提供了完整的用户管理、OAuth客户端管理、系统配置和审计功能。

## 🚀 功能特性

### 核心功能
- **用户管理** - 完整的用户CRUD操作，包括角色分配和状态管理
- **OAuth客户端管理** - OAuth2/OIDC客户端的注册、配置和管理
- **审计日志** - 详细的系统操作日志查看和分析
- **系统配置** - 动态系统配置管理
- **仪表板** - 系统概览和关键指标监控

### 技术特性
- **响应式设计** - 支持桌面和移动设备
- **实时数据** - 自动刷新和实时更新
- **权限控制** - 基于角色的访问控制
- **国际化** - 中文界面支持
- **主题定制** - 符合Ant Design设计规范

## 🔐 访问权限

### 权限要求
- 用户必须具有 `admin` 或 `super_admin` 角色
- 需要完成身份认证和MFA验证（如果启用）

### 访问地址
```
https://your-domain.com/admin
```

## 📊 仪表板

### 系统统计
- **用户统计** - 总用户数、活跃用户数、增长趋势
- **OAuth统计** - 客户端数量、API请求量
- **系统健康** - 数据库、Redis、内存、CPU状态

### 快速操作
- 直接跳转到各个管理模块
- 常用操作快捷入口

## 👥 用户管理

### 用户列表
- **搜索功能** - 支持邮箱、昵称、姓名搜索
- **过滤器** - 按状态、角色筛选用户
- **批量操作** - 批量启用/禁用用户
- **导出功能** - 导出用户数据

### 用户操作
- **查看详情** - 完整的用户信息展示
- **编辑用户** - 修改用户基本信息和权限
- **创建用户** - 添加新用户账户
- **删除用户** - 删除用户账户（谨慎操作）

### 用户字段
```typescript
interface User {
  id: string
  email: string           // 邮箱地址
  nickname?: string       // 昵称
  firstName?: string      // 名
  lastName?: string       // 姓
  isActive: boolean       // 启用状态
  isEmailVerified: boolean // 邮箱验证状态
  mfaEnabled: boolean     // MFA启用状态
  roles: string[]         // 用户角色
  lastLoginAt?: Date      // 最后登录时间
  createdAt: Date         // 创建时间
  updatedAt: Date         // 更新时间
}
```

## 🔑 OAuth客户端管理

### 客户端列表
- **搜索功能** - 按客户端名称、ID搜索
- **状态过滤** - 筛选启用/禁用的客户端
- **使用统计** - 查看客户端使用情况

### 客户端配置
- **基本信息** - 名称、描述、状态
- **重定向URI** - 配置授权回调地址
- **授权类型** - 支持的OAuth2流程
- **权限范围** - 可访问的API范围
- **安全设置** - PKCE、用户同意等

### 支持的授权类型
- `authorization_code` - 授权码流程
- `refresh_token` - 刷新令牌
- `client_credentials` - 客户端凭据

### 权限范围
- `openid` - OpenID Connect
- `profile` - 用户资料
- `email` - 邮箱地址
- `offline_access` - 离线访问

## 📋 审计日志

### 日志类型
- **认证操作** - 登录、登出、注册
- **用户操作** - 用户创建、更新、删除
- **客户端操作** - OAuth客户端管理
- **系统操作** - 配置变更、权限修改

### 日志字段
- **操作类型** - 具体的操作动作
- **用户信息** - 操作者身份
- **资源信息** - 被操作的资源
- **客户端信息** - IP地址、用户代理
- **操作详情** - 详细的操作参数

### 查询功能
- **时间范围** - 按日期筛选日志
- **操作类型** - 按操作分类筛选
- **用户筛选** - 查看特定用户的操作
- **全文搜索** - 在日志内容中搜索

## ⚙️ 系统配置

### 配置分类
- **安全设置** - 密码策略、会话配置
- **邮件配置** - SMTP服务器设置
- **OAuth配置** - OAuth2/OIDC参数
- **系统设置** - 基础系统参数
- **性能配置** - 缓存、限流等设置

### 配置管理
- **动态更新** - 无需重启即可生效
- **类型安全** - 支持多种数据类型
- **权限控制** - 区分公开和私有配置
- **变更记录** - 配置修改历史

## 🛠️ 使用指南

### 首次登录
1. 使用管理员账户登录系统
2. 访问 `/admin` 路径
3. 系统会自动验证管理员权限

### 常用操作

#### 创建新用户
1. 进入"用户管理"模块
2. 点击"创建"按钮
3. 填写用户基本信息
4. 分配适当的角色
5. 保存用户信息

#### 注册OAuth客户端
1. 进入"OAuth客户端"模块
2. 点击"创建客户端"
3. 配置客户端基本信息
4. 设置重定向URI
5. 选择授权类型和权限范围
6. 保存配置并记录客户端密钥

#### 查看审计日志
1. 进入"审计日志"模块
2. 使用过滤器筛选所需日志
3. 点击日志条目查看详情
4. 导出日志用于分析

### 最佳实践

#### 用户管理
- 定期审查用户权限
- 及时禁用离职员工账户
- 使用强密码策略
- 启用MFA增强安全性

#### OAuth客户端
- 使用HTTPS重定向URI
- 启用PKCE增强安全性
- 定期轮换客户端密钥
- 最小权限原则分配范围

#### 安全监控
- 定期检查审计日志
- 监控异常登录行为
- 关注权限变更操作
- 设置安全告警

## 🔧 故障排除

### 常见问题

#### 无法访问管理控制台
- 检查用户是否具有管理员权限
- 确认已完成身份认证
- 验证MFA设置是否正确

#### 数据加载失败
- 检查网络连接
- 验证API服务状态
- 查看浏览器控制台错误

#### 权限错误
- 确认用户角色配置
- 检查令牌是否过期
- 验证API权限设置

### 技术支持
如遇到技术问题，请：
1. 查看系统日志
2. 检查网络连接
3. 联系系统管理员
4. 提交问题报告

## 📈 性能优化

### 数据加载
- 使用分页减少数据量
- 启用缓存提升响应速度
- 优化查询条件

### 用户体验
- 使用过滤器快速定位数据
- 利用搜索功能精确查找
- 定期清理无用数据

---

*最后更新: 2025-08-27*  
*文档版本: 1.0*  
*适用版本: IdP v2.0+*
