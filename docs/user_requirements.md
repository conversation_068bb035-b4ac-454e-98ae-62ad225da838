# 身份提供商 (IdP) - 用户需求文档

| 版本 | 日期       | 作者 | 变更说明 |
| :--- | :--------- | :--- | :------- |
| 1.0  | 2025-07-26 | Gemini | 初始草案 |

## 1.0 项目概述

本文档旨在定义一个新的身份提供商（IdP）平台的核心用户需求。该平台旨在为各类应用（服务提供商, SP）提供安全、可靠、可扩展的统一身份认证和授权服务。

核心目标是简化用户的登录体验，增强账户安全性，并为应用开发者提供灵活的集成选项。平台需支持传统的用户名密码认证、多因素认证（MFA），并聚合多种第三方身份源（如 Google, GitHub 等）实现联合登录。此外，平台必须提供一个可配置的“零信任”安全模式，以适应高安全级别的应用场景。

## 2.0 用户角色

| 角色 | 描述 |
| :--- | :--- |
| **最终用户** | 希望通过本 IdP 登录到各种关联应用的个人用户。 |
| **应用管理员** | 在本 IdP 中注册和管理其应用程序的开发者或管理员。 |
| **系统管理员** | 负责配置、管理和监控整个 IdP 平台自身健康与安全的人员。 |

## 3.0 功能需求

### 3.1 核心认证功能

- **FR3.1.1 用户注册:**
    - 最终用户应能通过邮箱或手机号进行自服务注册。
    - 注册过程中需要进行邮箱或手机号的有效性验证。
    - 系统管理员应能手动创建用户账户。

- **FR3.1.2 用户登录:**
    - 支持使用“用户名/邮箱/手机号 + 密码”的方式进行登录。
    - 提供“记住我”功能，以在特定时间内保持登录状态。

- **FR3.1.3 密码管理:**
    - 用户可以安全地重置其忘记的密码（例如，通过邮件链接）。
    - 系统管理员应能配置密码强度策略（长度、复杂度、历史记录等）。
    - 用户应能随时修改自己的密码。

- **FR3.1.4 多因素认证 (MFA):**
    - 用户可为其账户启用或禁用 MFA 以增强安全性。
    - 至少支持以下 MFA 方式：
        - 基于时间的一次性密码（TOTP），兼容 Google Authenticator, Microsoft Authenticator 等应用。
        - 通过电子邮件发送验证码。
        - 通过短信发送验证码。

### 3.2 联合身份认证 (Federated Identity)

- **FR3.2.1 支持多种身份源:**
    - 用户应能选择使用第三方账号登录或关联到其 IdP 账户。
    - 必须支持以下主流身份源：
        - **Google** (OAuth 2.0 / OIDC)
        - **GitHub** (OAuth 2.0)
    - 平台应具备可扩展性，以便未来支持更多身份源，例如：
        - Microsoft Account
        - Facebook
        - 其他遵循 OAuth 2.0 / OpenID Connect (OIDC) 标准的国内外社区账号。

- **FR3.2.2 账户关联:**
    - 用户首次使用第三方账号登录时，系统可引导其创建一个新的 IdP 账户或关联到一个已存在的 IdP 账户。
    - 用户应能在其个人资料中管理（添加/解除）已关联的第三方账号。

### 3.3 单点登录 (Single Sign-On, SSO)

- **FR3.3.1 标准协议支持:**
    - IdP 必须支持行业标准的 SSO 协议，以确保与各类应用的兼容性。
    - 必须支持 `OpenID Connect (OIDC) 1.0`。
    - 必须支持 `OAuth 2.0` 授权框架。
    - 必须支持 `SAML 2.0`。

- **FR3.3.2 无缝登录体验:**
    - 最终用户在 IdP 成功登录一次后，访问任何已集成的应用时都无需再次输入凭据。

### 3.4 零信任模式 (Zero Trust Mode)

- **FR3.4.1 模式可配置性:**
    - 系统管理员应能全局性地开启或关闭“零信任模式”。
    - 应用管理员应能为其单个应用强制要求“零信任”级别的认证策略，即使全局模式关闭。

- **FR3.4.2 持续自适应认证:**
    - 当零信任模式开启时，IdP 必须基于风险信号进行持续和自适应的访问评估。
    - 风险信号至少应包括：
        - **用户和设备上下文:** IP 地址（地理位置、是否来自已知代理）、设备类型、操作系统版本、浏览器信息。
        - **行为分析:** 异常的登录时间、频率或地理位置变化。
    - 基于风险评估，系统可触发相应的操作，例如：
        - **允许访问**。
        - **要求 MFA 验证**（即使之前已验证过）。
        - **拒绝访问**。

- **FR3.4.3 设备信任:**
    - 系统应能识别并标记受信任的设备。
    - 来自未知或不受信任设备的访问请求应被视为更高风险。

### 3.5 用户与管理

- **FR3.5.1 最终用户管理:**
    - 支持用户查看和编辑其个人基本资料（如昵称、联系方式）。
    - 支持用户管理其安全设置（修改密码、管理 MFA 设备、管理已关联的第三方账号）。
    - 支持用户查看自己的近期登录历史和活动记录。

- **FR3.5.2 应用管理:**
    - 支持应用管理员可以注册新的应用程序到 IdP。
    - 支持为每个应用配置所需的 SSO 协议（OIDC/SAML）和相关参数（如回调 URL、客户端密钥等）。
    - 支持应用管理员查看其名下应用的登录统计和审计日志。

- **FR3.5.3 系统管理:**
    - 提供全面的用户管理功能（增、删、改、查、锁定/解锁用户）。
    - 提供全面的应用管理功能。
    - 支持配置和管理身份源。
    - 支持配置系统级安全策略（密码策略、MFA 选项、零信任模式）。
    - 支持查看和导出全平台的审计日志。

## 4.0 非功能需求

- **NFR4.1 安全性:**
    - 所有网络通信必须使用 TLS 加密。
    - 敏感数据（如密码哈希、密钥）在存储时必须加密。
    - 平台应能抵御常见的 Web 攻击（如 OWASP Top 10）。

- **NFR4.2 性能:**
    - 95% 的登录请求应在 2 秒内完成。
    - 用户仪表盘页面的加载时间不应超过 3 秒。

- **NFR4.3 可用性:**
    - 核心认证服务的可用性目标为 99.95%。
    - 系统应具备冗余和故障转移能力。

- **NFR4.4 可扩展性:**
    - 系统架构应支持水平扩展，以应对用户量和请求量的增长。
    - 添加新的身份源或 MFA 方法不应需要重构核心系统。

- **NFR4.5 易用性:**
    - 用户界面应简洁、直观，并提供清晰的指引。
    - 为应用管理员提供清晰的集成文档和向导。
