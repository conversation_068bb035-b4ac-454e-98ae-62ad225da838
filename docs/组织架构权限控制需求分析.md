# 组织架构权限控制需求分析与技术方案

## 📋 文档概览

**创建日期**: 2025-08-28
**分析范围**: 组织架构权限控制系统设计与实现
**技术参考**: Cerbos层次权限控制最佳实践
**目标**: 实现企业级多层级组织架构权限管理

---

## 🎯 需求概述

基于对当前系统的分析，我们需要实现一个完整的组织架构权限控制系统，支持：
- 多层级组织架构管理
- 基于组织层次的权限继承
- 跨组织数据访问控制
- 动态权限传递和隔离

---

## 🏗️ 组织架构概念设计

### 核心概念定义

#### 1. 组织层次结构
```
企业 (Company)
├── 事业部 (Division)
│   ├── 部门 (Department)
│   │   ├── 团队 (Team)
│   │   │   └── 小组 (Group)
│   │   └── 项目组 (Project)
│   └── 分部 (Branch)
└── 子公司 (Subsidiary)
```

#### 2. 组织路径表示
采用类似Cerbos的层次路径格式：
- `company.division.department.team`
- `acme.engineering.backend.auth-team`
- `acme.sales.north.enterprise`

#### 3. 权限作用域
- **全局权限**: 跨所有组织生效
- **组织权限**: 在特定组织及其子组织生效
- **局部权限**: 仅在当前组织生效

---

## 📊 数据模型设计

### 核心数据表结构

#### 1. 组织表 (Organization)
```sql
CREATE TABLE organizations (
    id VARCHAR(255) PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    display_name VARCHAR(255) NOT NULL,
    description TEXT,
    
    -- 层次结构字段
    parent_id VARCHAR(255),
    path VARCHAR(1000) NOT NULL, -- 如: "acme.engineering.backend"
    level INTEGER NOT NULL DEFAULT 0,
    
    -- 组织类型和属性
    type VARCHAR(100) NOT NULL, -- company, division, department, team, etc.
    status VARCHAR(50) DEFAULT 'active',
    metadata JSON,
    
    -- 权限配置
    permission_inheritance BOOLEAN DEFAULT true,
    data_isolation_level VARCHAR(50) DEFAULT 'inherit', -- strict, inherit, none
    
    -- 审计字段
    created_by VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_by VARCHAR(255) NOT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- 索引和约束
    FOREIGN KEY (parent_id) REFERENCES organizations(id),
    INDEX idx_org_path (path),
    INDEX idx_org_parent (parent_id),
    INDEX idx_org_level (level)
);
```

#### 2. 组织成员表 (OrganizationMember)
```sql
CREATE TABLE organization_members (
    id VARCHAR(255) PRIMARY KEY,
    user_id VARCHAR(255) NOT NULL,
    organization_id VARCHAR(255) NOT NULL,
    
    -- 角色和权限
    role VARCHAR(100) NOT NULL, -- admin, manager, member, viewer
    permissions JSON, -- 特定权限覆盖
    
    -- 成员状态
    status VARCHAR(50) DEFAULT 'active',
    joined_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NULL,
    
    -- 审计字段
    created_by VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- 约束
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_org (user_id, organization_id),
    INDEX idx_member_user (user_id),
    INDEX idx_member_org (organization_id)
);
```

#### 3. 组织权限表 (OrganizationPermission)
```sql
CREATE TABLE organization_permissions (
    id VARCHAR(255) PRIMARY KEY,
    organization_id VARCHAR(255) NOT NULL,
    permission_id VARCHAR(255) NOT NULL,
    
    -- 权限配置
    scope VARCHAR(50) NOT NULL, -- self, children, descendants
    inheritance_rule VARCHAR(50) DEFAULT 'inherit', -- inherit, override, block
    conditions JSON, -- 权限条件
    
    -- 生效时间
    effective_from TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    effective_until TIMESTAMP NULL,
    
    -- 审计字段
    granted_by VARCHAR(255) NOT NULL,
    granted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- 约束
    FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE CASCADE,
    FOREIGN KEY (permission_id) REFERENCES permissions(id) ON DELETE CASCADE,
    UNIQUE KEY unique_org_permission (organization_id, permission_id),
    INDEX idx_org_perm_org (organization_id),
    INDEX idx_org_perm_permission (permission_id)
);
```

---

## 🔐 权限控制机制设计

### 1. 权限继承规则

#### 继承类型
- **向下继承**: 父组织权限自动传递给子组织
- **权限累积**: 子组织权限 = 父组织权限 + 自身权限
- **权限覆盖**: 子组织可以覆盖父组织的特定权限
- **权限阻断**: 子组织可以阻断某些父组织权限

#### 继承算法
```typescript
interface OrganizationHierarchy {
  path: string;
  level: number;
  parentPath?: string;
}

class OrganizationPermissionResolver {
  /**
   * 解析用户在组织中的有效权限
   */
  async resolveUserPermissions(
    userId: string, 
    organizationPath: string
  ): Promise<EffectivePermissions> {
    // 1. 获取组织层次路径
    const hierarchy = await this.getOrganizationHierarchy(organizationPath);
    
    // 2. 从根组织开始收集权限
    const permissions = new Set<string>();
    
    for (const org of hierarchy) {
      // 获取组织级权限
      const orgPermissions = await this.getOrganizationPermissions(org.id);
      
      // 获取用户在该组织的角色权限
      const memberPermissions = await this.getMemberPermissions(userId, org.id);
      
      // 应用继承规则
      this.applyInheritanceRules(permissions, orgPermissions, memberPermissions);
    }
    
    return {
      permissions: Array.from(permissions),
      organizationPath,
      resolvedAt: new Date()
    };
  }
}
```

### 2. 数据访问控制

#### 数据隔离级别
- **严格隔离**: 只能访问本组织数据
- **继承访问**: 可访问本组织及子组织数据
- **授权访问**: 通过明确授权访问其他组织数据

#### 访问控制实现
```typescript
class OrganizationDataFilter {
  /**
   * 基于组织架构过滤数据查询
   */
  async applyOrganizationFilter(
    query: DatabaseQuery,
    userContext: UserContext
  ): Promise<DatabaseQuery> {
    const userOrgs = await this.getUserOrganizations(userContext.userId);
    
    // 构建组织访问范围
    const accessibleOrgPaths = new Set<string>();
    
    for (const org of userOrgs) {
      // 添加当前组织
      accessibleOrgPaths.add(org.path);
      
      // 根据权限添加子组织
      if (await this.hasPermission(userContext.userId, 'read:descendants', org.id)) {
        const descendants = await this.getDescendantOrganizations(org.path);
        descendants.forEach(desc => accessibleOrgPaths.add(desc.path));
      }
    }
    
    // 应用组织过滤条件
    return query.whereIn('organization_path', Array.from(accessibleOrgPaths));
  }
}
```

---

## 🔄 跨组织权限管理

### 1. 权限申请流程

#### 申请类型
- **临时访问**: 有时间限制的跨组织访问
- **项目协作**: 基于项目的跨组织权限
- **数据共享**: 特定数据的跨组织访问
- **管理委托**: 管理权限的跨组织委托

#### 申请工作流
```mermaid
graph TD
    A[用户发起申请] --> B[系统验证申请]
    B --> C{申请类型}
    C -->|临时访问| D[直属上级审批]
    C -->|项目协作| E[项目负责人审批]
    C -->|数据共享| F[数据所有者审批]
    C -->|管理委托| G[高级管理员审批]
    D --> H[目标组织确认]
    E --> H
    F --> H
    G --> H
    H --> I[权限自动授予]
    I --> J[通知相关方]
```

### 2. 权限委托机制

#### 委托类型
- **角色委托**: 将角色权限委托给其他用户
- **权限委托**: 将特定权限委托给其他用户
- **组织委托**: 将组织管理权委托给其他用户

#### 委托实现
```typescript
interface PermissionDelegation {
  id: string;
  delegatorId: string;
  delegateeId: string;
  organizationId: string;
  permissions: string[];
  conditions?: DelegationConditions;
  expiresAt?: Date;
}

class PermissionDelegationService {
  async createDelegation(
    delegatorId: string,
    delegateeId: string,
    permissions: string[],
    organizationId: string,
    options: DelegationOptions
  ): Promise<PermissionDelegation> {
    // 验证委托者权限
    await this.validateDelegatorPermissions(delegatorId, permissions, organizationId);
    
    // 创建委托记录
    const delegation = await this.prisma.permissionDelegation.create({
      data: {
        delegatorId,
        delegateeId,
        organizationId,
        permissions,
        conditions: options.conditions,
        expiresAt: options.expiresAt
      }
    });
    
    // 更新权限缓存
    await this.invalidatePermissionCache(delegateeId);
    
    return delegation;
  }
}
```

---

## 🛠️ 技术实现方案

### 1. 层次查询优化

#### 路径索引策略
```sql
-- 使用路径前缀索引优化层次查询
CREATE INDEX idx_org_path_prefix ON organizations(path(100));

-- 查询子组织
SELECT * FROM organizations 
WHERE path LIKE 'acme.engineering.%' 
AND path != 'acme.engineering';

-- 查询父组织
SELECT * FROM organizations 
WHERE 'acme.engineering.backend.auth' LIKE CONCAT(path, '.%');
```

#### 缓存策略
```typescript
class OrganizationHierarchyCache {
  private readonly CACHE_TTL = 300; // 5分钟
  
  async getOrganizationHierarchy(path: string): Promise<Organization[]> {
    const cacheKey = `org:hierarchy:${path}`;
    
    let hierarchy = await this.cache.get(cacheKey);
    if (!hierarchy) {
      hierarchy = await this.buildHierarchy(path);
      await this.cache.setex(cacheKey, this.CACHE_TTL, hierarchy);
    }
    
    return hierarchy;
  }
  
  private async buildHierarchy(path: string): Promise<Organization[]> {
    const pathParts = path.split('.');
    const hierarchy: Organization[] = [];
    
    for (let i = 1; i <= pathParts.length; i++) {
      const currentPath = pathParts.slice(0, i).join('.');
      const org = await this.getOrganizationByPath(currentPath);
      if (org) hierarchy.push(org);
    }
    
    return hierarchy;
  }
}
```

### 2. 权限验证中间件

```typescript
export function requireOrganizationPermission(
  permission: string,
  scope: 'self' | 'children' | 'descendants' = 'self'
) {
  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      const userId = req.user?.id;
      const organizationId = req.params.organizationId || req.body.organizationId;
      
      if (!userId || !organizationId) {
        return res.status(400).json({
          success: false,
          error: 'MISSING_PARAMETERS',
          message: '缺少用户ID或组织ID'
        });
      }
      
      // 验证组织权限
      const hasPermission = await organizationPermissionService.checkPermission(
        userId,
        organizationId,
        permission,
        scope
      );
      
      if (!hasPermission) {
        return res.status(403).json({
          success: false,
          error: 'INSUFFICIENT_ORGANIZATION_PERMISSION',
          message: '组织权限不足'
        });
      }
      
      next();
    } catch (error) {
      logger.error('组织权限验证失败', { error, userId: req.user?.id });
      res.status(500).json({
        success: false,
        error: 'PERMISSION_CHECK_FAILED',
        message: '权限验证失败'
      });
    }
  };
}
```

---

## 📋 实施优先级

### 🔴 第一优先级 (立即实施)
1. **组织架构数据模型** - 基础数据结构
2. **基础权限继承机制** - 核心权限逻辑
3. **组织成员管理** - 用户组织关系

### 🟡 第二优先级 (2周内)
1. **权限验证中间件** - API集成
2. **数据访问控制** - 数据隔离
3. **基础管理界面** - 组织管理UI

### 🟢 第三优先级 (1个月内)
1. **跨组织权限申请** - 工作流系统
2. **权限委托机制** - 高级功能
3. **监控和审计** - 运维支持

---

*本文档将根据实施进展和需求变化持续更新。*
