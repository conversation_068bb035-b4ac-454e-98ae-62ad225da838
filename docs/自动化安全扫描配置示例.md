# 自动化安全扫描配置示例

## 📋 概述

本文档提供了自动化安全扫描系统的详细配置说明和示例。自动化安全扫描系统支持定时执行各种类型的安全扫描，并通过多种方式发送通知。

## 🔧 环境变量配置

### 基础配置

```bash
# 启用自动化安全扫描
AUTOMATED_SECURITY_SCANNER_ENABLED=true

# 时区设置（默认：Asia/Shanghai）
AUTOMATED_SECURITY_SCANNER_TIMEZONE=Asia/Shanghai
```

### 扫描计划配置

#### 全面安全扫描
```bash
# 启用全面安全扫描（默认：true）
AUTOMATED_FULL_SCAN_ENABLED=true

# Cron表达式：每天凌晨2点执行
AUTOMATED_FULL_SCAN_CRON="0 2 * * *"

# 通知阈值：medium（critical/high/medium/low）
AUTOMATED_FULL_SCAN_THRESHOLD=medium
```

#### 依赖项安全扫描
```bash
# 启用依赖项安全扫描（默认：true）
AUTOMATED_DEPENDENCY_SCAN_ENABLED=true

# Cron表达式：每6小时执行一次
AUTOMATED_DEPENDENCY_SCAN_CRON="0 */6 * * *"

# 通知阈值：high
AUTOMATED_DEPENDENCY_SCAN_THRESHOLD=high
```

#### 代码安全扫描
```bash
# 启用代码安全扫描（默认：true）
AUTOMATED_CODE_SCAN_ENABLED=true

# Cron表达式：每天凌晨4点执行
AUTOMATED_CODE_SCAN_CRON="0 4 * * *"

# 通知阈值：medium
AUTOMATED_CODE_SCAN_THRESHOLD=medium
```

#### 基础设施安全扫描
```bash
# 启用基础设施安全扫描（默认：true）
AUTOMATED_INFRASTRUCTURE_SCAN_ENABLED=true

# Cron表达式：每周日凌晨3点执行
AUTOMATED_INFRASTRUCTURE_SCAN_CRON="0 3 * * 0"

# 通知阈值：medium
AUTOMATED_INFRASTRUCTURE_SCAN_THRESHOLD=medium
```

### 邮件通知配置

```bash
# 启用邮件通知
SECURITY_SCAN_EMAIL_ENABLED=true

# 收件人列表（逗号分隔）
SECURITY_SCAN_EMAIL_RECIPIENTS=<EMAIL>,<EMAIL>

# SMTP服务器配置
SECURITY_SCAN_SMTP_HOST=smtp.company.com
SECURITY_SCAN_SMTP_PORT=587
SECURITY_SCAN_SMTP_SECURE=true
SECURITY_SCAN_SMTP_USER=<EMAIL>
SECURITY_SCAN_SMTP_PASSWORD=your_smtp_password

# 发件人地址
SECURITY_SCAN_EMAIL_FROM=<EMAIL>
```

### Webhook通知配置

```bash
# 启用Webhook通知
SECURITY_SCAN_WEBHOOK_ENABLED=true

# Webhook URL
SECURITY_SCAN_WEBHOOK_URL=https://your-webhook-endpoint.com/security-alerts

# 认证令牌
SECURITY_SCAN_WEBHOOK_TOKEN=your_webhook_token

# 超时时间（毫秒，默认：30000）
SECURITY_SCAN_WEBHOOK_TIMEOUT=30000

# 重试次数（默认：3）
SECURITY_SCAN_WEBHOOK_RETRY=3
```

### Slack通知配置

```bash
# 启用Slack通知
SECURITY_SCAN_SLACK_ENABLED=true

# Slack Webhook URL
SECURITY_SCAN_SLACK_WEBHOOK_URL=https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK

# Slack频道（默认：#security）
SECURITY_SCAN_SLACK_CHANNEL=#security

# 机器人用户名（默认：Security Scanner）
SECURITY_SCAN_SLACK_USERNAME=Security Scanner

# 机器人图标（默认：:shield:）
SECURITY_SCAN_SLACK_ICON=:shield:
```

### 存储配置

```bash
# 扫描结果保留天数（默认：30）
SECURITY_SCAN_RETENTION_DAYS=30

# 每次扫描最大结果数（默认：1000）
SECURITY_SCAN_MAX_RESULTS=1000

# 启用压缩存储（默认：true）
SECURITY_SCAN_COMPRESSION_ENABLED=true
```

### 性能配置

```bash
# 最大并发扫描数（默认：2）
SECURITY_SCAN_MAX_CONCURRENT=2

# 扫描超时时间（毫秒，默认：1800000 = 30分钟）
SECURITY_SCAN_TIMEOUT=1800000

# 最大内存使用量（MB，默认：512）
SECURITY_SCAN_MAX_MEMORY=512

# 最大CPU使用率（百分比，默认：80）
SECURITY_SCAN_MAX_CPU=80
```

## 📅 Cron表达式说明

Cron表达式格式：`分钟 小时 日 月 星期`

### 常用示例

| 表达式 | 说明 |
|--------|------|
| `0 2 * * *` | 每天凌晨2点 |
| `0 */6 * * *` | 每6小时 |
| `0 0 * * 0` | 每周日午夜 |
| `0 3 * * 1` | 每周一凌晨3点 |
| `0 1 1 * *` | 每月1日凌晨1点 |
| `0 0 1 1 *` | 每年1月1日午夜 |

### 字段说明

- **分钟**: 0-59
- **小时**: 0-23
- **日**: 1-31
- **月**: 1-12
- **星期**: 0-6 (0=周日)

## 🔔 通知阈值说明

| 阈值 | 说明 | 包含的漏洞级别 |
|------|------|----------------|
| `critical` | 仅严重漏洞 | Critical |
| `high` | 高危及以上 | Critical, High |
| `medium` | 中危及以上 | Critical, High, Medium |
| `low` | 所有漏洞 | Critical, High, Medium, Low |

## 📊 API接口

### 获取扫描计划列表
```http
GET /api/v1/security/automated-scans/schedules
Authorization: Bearer <token>
```

### 启用/禁用扫描计划
```http
PUT /api/v1/security/automated-scans/schedules/{scheduleId}/toggle
Authorization: Bearer <token>
Content-Type: application/json

{
  "enabled": true
}
```

### 手动触发扫描
```http
POST /api/v1/security/automated-scans/trigger
Authorization: Bearer <token>
Content-Type: application/json

{
  "scanType": "full"
}
```

## 🛠️ 部署建议

### 生产环境配置

1. **启用所有扫描类型**：确保全面的安全覆盖
2. **配置多种通知方式**：邮件 + Slack + Webhook
3. **合理设置扫描频率**：避免过于频繁影响性能
4. **监控资源使用**：设置合适的内存和CPU限制

### 开发环境配置

1. **减少扫描频率**：避免影响开发效率
2. **降低通知阈值**：只关注高危漏洞
3. **使用测试通知渠道**：避免干扰生产通知

### 测试环境配置

1. **启用所有扫描**：验证扫描功能完整性
2. **使用测试通知**：验证通知功能
3. **较短的保留期**：节省存储空间

## 🔍 故障排除

### 常见问题

1. **扫描未执行**
   - 检查 `AUTOMATED_SECURITY_SCANNER_ENABLED` 是否为 `true`
   - 验证 Cron 表达式格式是否正确
   - 查看应用日志中的错误信息

2. **通知未发送**
   - 检查对应的通知配置是否启用
   - 验证 SMTP/Webhook/Slack 配置是否正确
   - 检查网络连接和防火墙设置

3. **扫描超时**
   - 增加 `SECURITY_SCAN_TIMEOUT` 值
   - 检查系统资源是否充足
   - 考虑减少并发扫描数

4. **内存不足**
   - 增加 `SECURITY_SCAN_MAX_MEMORY` 值
   - 减少 `SECURITY_SCAN_MAX_RESULTS` 值
   - 启用压缩存储

### 日志查看

```bash
# 查看自动化扫描相关日志
docker logs <container_id> | grep "自动化安全扫描"

# 查看扫描执行日志
docker logs <container_id> | grep "执行自动化安全扫描"

# 查看通知发送日志
docker logs <container_id> | grep "发送.*通知"
```

## 📈 监控指标

系统提供以下监控指标：

- `security_scans_total`: 总扫描次数
- `security_scans_duration_seconds`: 扫描耗时
- `security_vulnerabilities_found_total`: 发现的漏洞总数
- `security_notifications_sent_total`: 发送的通知总数
- `security_scan_errors_total`: 扫描错误次数

## 🔐 安全建议

1. **保护敏感配置**：使用环境变量或密钥管理系统
2. **限制API访问**：确保只有授权用户可以管理扫描计划
3. **审计扫描活动**：记录所有扫描和配置变更
4. **定期更新**：保持扫描引擎和规则库最新
5. **备份配置**：定期备份扫描配置和历史结果

---

*配置文档版本: 1.0*  
*最后更新: 2025-08-28*
