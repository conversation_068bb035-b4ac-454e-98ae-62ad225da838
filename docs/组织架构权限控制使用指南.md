# 组织架构权限控制使用指南

## 📋 文档概览

**版本**: 1.0
**更新日期**: 2025-08-28
**适用范围**: 多层级组织架构权限管理
**目标读者**: 系统管理员、组织管理员、最终用户

---

## 🎯 系统概述

组织架构权限控制系统提供企业级的多层级权限管理能力，支持：
- 灵活的组织架构定义
- 基于层次的权限继承
- 细粒度的权限控制
- 跨组织权限管理
- 数据访问隔离

---

## 🏗️ 组织架构管理

### 组织层次结构

系统支持多层级的组织架构：

```
企业 (Company)
├── 事业部 (Division)
│   ├── 部门 (Department)
│   │   ├── 团队 (Team)
│   │   │   └── 小组 (Group)
│   │   └── 项目组 (Project)
│   └── 分部 (Branch)
└── 子公司 (Subsidiary)
```

### 组织类型说明

| 类型 | 描述 | 典型用途 |
|------|------|----------|
| `company` | 公司/企业 | 根组织，代表整个企业 |
| `division` | 事业部 | 大型业务单元 |
| `department` | 部门 | 职能部门 |
| `team` | 团队 | 工作团队 |
| `group` | 小组 | 小型工作组 |
| `project` | 项目组 | 临时项目团队 |

### 创建组织架构

#### 1. 创建根组织

```bash
curl -X POST "https://id-provider.example.com/api/v1/organizations" \
  -H "Authorization: Bearer ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "acme",
    "displayName": "ACME公司",
    "description": "ACME科技有限公司",
    "type": "company",
    "permissionInheritance": true,
    "dataIsolationLevel": "inherit"
  }'
```

#### 2. 创建子组织

```bash
curl -X POST "https://id-provider.example.com/api/v1/organizations" \
  -H "Authorization: Bearer ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "engineering",
    "displayName": "工程部",
    "description": "技术研发部门",
    "parentId": "acme-org-id",
    "type": "division",
    "permissionInheritance": true,
    "dataIsolationLevel": "inherit"
  }'
```

#### 3. 创建团队

```bash
curl -X POST "https://id-provider.example.com/api/v1/organizations" \
  -H "Authorization: Bearer ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "backend",
    "displayName": "后端团队",
    "description": "后端服务开发团队",
    "parentId": "engineering-org-id",
    "type": "team",
    "permissionInheritance": true,
    "dataIsolationLevel": "strict"
  }'
```

### 组织路径规则

- 路径格式：`parent.child.grandchild`
- 示例：`acme.engineering.backend.auth`
- 路径唯一性：每个路径在系统中必须唯一
- 自动生成：系统根据组织层次自动生成路径

---

## 👥 组织成员管理

### 添加组织成员

#### 基本成员添加

```bash
curl -X POST "https://id-provider.example.com/api/v1/organizations/{orgId}/members" \
  -H "Authorization: Bearer ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "userId": "user-123",
    "role": "member",
    "permissions": ["read:documents", "write:reports"]
  }'
```

#### 添加管理员

```bash
curl -X POST "https://id-provider.example.com/api/v1/organizations/{orgId}/members" \
  -H "Authorization: Bearer ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "userId": "user-456",
    "role": "admin",
    "permissions": ["*"],
    "isOwner": false,
    "isPrimary": true
  }'
```

### 组织角色说明

| 角色 | 权限范围 | 典型职责 |
|------|----------|----------|
| `owner` | 完全控制 | 组织所有者，拥有所有权限 |
| `admin` | 管理权限 | 组织管理员，可管理成员和权限 |
| `manager` | 部分管理 | 团队经理，可管理下属和资源 |
| `member` | 基础权限 | 普通成员，拥有基本访问权限 |
| `viewer` | 只读权限 | 访客用户，只能查看信息 |

### 成员权限配置

```json
{
  "userId": "user-789",
  "role": "manager",
  "permissions": [
    "read:team_data",
    "write:team_reports",
    "manage:team_members",
    "approve:leave_requests"
  ],
  "expiresAt": "2024-12-31T23:59:59Z"
}
```

---

## 🔐 权限继承机制

### 继承规则

#### 1. 向下继承
父组织的权限自动传递给子组织：

```
acme (read:global_data)
└── engineering (继承: read:global_data)
    └── backend (继承: read:global_data)
```

#### 2. 权限累积
子组织权限 = 父组织权限 + 自身权限：

```
acme: [read:global_data]
├── engineering: [read:global_data, read:tech_docs]
    └── backend: [read:global_data, read:tech_docs, write:code]
```

#### 3. 权限覆盖
子组织可以覆盖父组织的特定权限：

```json
{
  "organizationId": "backend-team",
  "permissionId": "read:sensitive_data",
  "inheritanceRule": "override",
  "scope": "self"
}
```

#### 4. 权限阻断
子组织可以阻断某些父组织权限：

```json
{
  "organizationId": "external-team",
  "permissionId": "admin:system",
  "inheritanceRule": "block",
  "scope": "descendants"
}
```

### 继承配置示例

```bash
curl -X POST "https://id-provider.example.com/api/v1/organizations/{orgId}/permissions" \
  -H "Authorization: Bearer ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "permissionId": "read:financial_data",
    "scope": "descendants",
    "inheritanceRule": "inherit",
    "conditions": {
      "timeRestriction": {
        "start": "09:00",
        "end": "17:00"
      }
    }
  }'
```

---

## 🛡️ 数据访问控制

### 隔离级别

#### 1. 严格隔离 (`strict`)
- 只能访问本组织的数据
- 不能访问父组织或子组织数据
- 适用于敏感部门

#### 2. 继承访问 (`inherit`)
- 可以访问本组织及子组织数据
- 继承父组织的访问权限
- 默认推荐设置

#### 3. 无隔离 (`none`)
- 可以访问所有有权限的数据
- 不受组织边界限制
- 适用于特殊角色

### 数据过滤实现

```javascript
// 应用中的数据过滤示例
const getFilteredData = async (userId, query) => {
  // 获取用户的组织权限上下文
  const orgContext = await getOrganizationContext(userId);
  
  // 构建组织过滤条件
  const orgFilter = {
    organizationPath: {
      $in: orgContext.accessiblePaths
    }
  };
  
  // 应用过滤条件
  return await database.find({
    ...query,
    ...orgFilter
  });
};
```

---

## 🔄 跨组织权限管理

### 跨组织访问场景

#### 1. 项目协作
不同部门的员工需要协作完成项目：

```bash
# 申请跨部门项目权限
curl -X POST "https://id-provider.example.com/api/v1/permission-requests" \
  -H "Authorization: Bearer USER_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "targetOrganizationId": "marketing-team",
    "requestedPermissions": ["read:campaign_data", "write:project_docs"],
    "requestType": "project",
    "reason": "参与Q4产品发布项目",
    "requestedDuration": 720
  }'
```

#### 2. 数据共享
需要访问其他部门的数据进行分析：

```bash
# 申请数据访问权限
curl -X POST "https://id-provider.example.com/api/v1/permission-requests" \
  -H "Authorization: Bearer USER_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "targetOrganizationId": "sales-team",
    "requestedPermissions": ["read:sales_reports"],
    "requestType": "data_sharing",
    "reason": "需要销售数据进行业务分析",
    "requestedDuration": 168
  }'
```

#### 3. 临时支援
临时支援其他团队的工作：

```bash
# 申请临时访问权限
curl -X POST "https://id-provider.example.com/api/v1/permission-requests" \
  -H "Authorization: Bearer USER_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "targetOrganizationId": "support-team",
    "requestedPermissions": ["read:tickets", "write:responses"],
    "requestType": "temporary",
    "reason": "临时支援客服团队处理紧急问题",
    "priority": "high",
    "requestedDuration": 24
  }'
```

### 权限委托

#### 委托权限给其他用户

```bash
curl -X POST "https://id-provider.example.com/api/v1/permission-delegations" \
  -H "Authorization: Bearer MANAGER_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "delegateeId": "user-456",
    "organizationId": "my-team",
    "delegationType": "permission",
    "delegatedPermissions": ["approve:expenses", "manage:schedules"],
    "effectiveUntil": "2024-03-31T23:59:59Z",
    "conditions": {
      "maxAmount": 5000,
      "requiresNotification": true
    }
  }'
```

---

## 📊 权限监控和审计

### 权限使用监控

#### 查看权限使用情况

```bash
curl -X GET "https://id-provider.example.com/api/v1/organizations/{orgId}/access-logs?startDate=2024-01-01&endDate=2024-01-31" \
  -H "Authorization: Bearer ADMIN_TOKEN"
```

#### 异常访问检测

系统自动检测以下异常模式：
- 非工作时间的敏感数据访问
- 大量数据下载
- 跨地域的异常访问
- 权限提升尝试

### 权限审计报告

#### 生成组织权限报告

```bash
curl -X POST "https://id-provider.example.com/api/v1/audit/reports" \
  -H "Authorization: Bearer ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "reportType": "organization_permissions",
    "organizationId": "engineering-team",
    "dateRange": {
      "start": "2024-01-01",
      "end": "2024-01-31"
    },
    "includeSubOrganizations": true
  }'
```

---

## 🔧 配置最佳实践

### 组织架构设计

#### 1. 层次规划
- 不超过6层深度
- 每层不超过20个子组织
- 使用有意义的组织名称

#### 2. 权限设计
- 遵循最小权限原则
- 合理使用权限继承
- 定期审查权限配置

#### 3. 数据隔离
- 敏感部门使用严格隔离
- 协作部门使用继承访问
- 特殊角色谨慎使用无隔离

### 成员管理

#### 1. 角色分配
- 明确角色职责边界
- 避免过度授权
- 定期审查角色分配

#### 2. 权限有效期
- 临时权限设置明确过期时间
- 长期权限定期审查
- 离职员工及时撤销权限

### 安全配置

#### 1. 访问控制
- 启用IP地址限制
- 设置时间窗口限制
- 配置设备限制

#### 2. 监控告警
- 配置异常访问告警
- 设置权限变更通知
- 启用审计日志记录

---

## 🚨 故障排除

### 常见问题

#### Q: 用户无法访问子组织数据
**可能原因**:
- 数据隔离级别设置为`strict`
- 缺少`read:descendants`权限
- 组织架构配置错误

**解决方案**:
1. 检查组织的数据隔离级别
2. 为用户添加相应权限
3. 验证组织层次关系

#### Q: 权限继承不生效
**可能原因**:
- 子组织禁用了权限继承
- 存在权限阻断配置
- 权限作用域设置错误

**解决方案**:
1. 检查`permissionInheritance`设置
2. 查看权限的`inheritanceRule`
3. 验证权限的`scope`配置

#### Q: 跨组织访问被拒绝
**可能原因**:
- 缺少跨组织访问权限
- 目标组织不允许外部访问
- 权限申请未批准

**解决方案**:
1. 提交权限申请
2. 联系目标组织管理员
3. 检查申请状态

### 诊断工具

#### 权限诊断

```bash
curl -X POST "https://id-provider.example.com/api/v1/permissions/diagnose" \
  -H "Authorization: Bearer ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "userId": "user-123",
    "organizationId": "target-org",
    "permission": "read:sensitive_data"
  }'
```

#### 组织架构验证

```bash
curl -X GET "https://id-provider.example.com/api/v1/organizations/{orgId}/validate" \
  -H "Authorization: Bearer ADMIN_TOKEN"
```

---

## 📈 性能优化

### 缓存策略

- 组织架构信息缓存5分钟
- 用户权限信息缓存3分钟
- 权限验证结果缓存1分钟

### 查询优化

- 使用组织路径前缀索引
- 批量权限检查
- 异步权限预计算

---

*本指南将根据系统更新和用户反馈持续完善。如需技术支持，请联系系统管理员。*
