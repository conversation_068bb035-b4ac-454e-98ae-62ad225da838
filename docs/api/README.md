# ID Provider API 文档

## 概述

ID Provider 是一个基于零信任架构的现代身份认证和授权平台，提供安全、可扩展的身份管理解决方案。

### 核心特性

- 🔐 **零信任架构** - 基于风险评估的自适应认证
- 🛡️ **多因素认证** - 支持SMS、TOTP、推送通知、生物识别
- 📊 **实时威胁检测** - 智能识别和响应安全威胁
- 📈 **数据分析** - 全面的用户行为和安全分析
- 📱 **移动端SDK** - iOS和Android原生支持
- 🌐 **RESTful API** - 标准化的API接口

### 基础信息

- **Base URL**: `https://api.idprovider.com/v1`
- **认证方式**: Bearer <PERSON>ken
- **数据格式**: JSON
- **字符编码**: UTF-8

## 快速开始

### 1. 获取API密钥

首先需要在管理控制台创建应用并获取API密钥：

```bash
curl -X POST https://api.idprovider.com/v1/applications \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "My Application",
    "description": "应用描述",
    "redirect_uris": ["https://myapp.com/callback"]
  }'
```

### 2. 用户登录

```bash
curl -X POST https://api.idprovider.com/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123",
    "client_id": "your_client_id"
  }'
```

### 3. 使用访问令牌

```bash
curl -X GET https://api.idprovider.com/v1/user/profile \
  -H "Authorization: Bearer ACCESS_TOKEN"
```

## 认证流程

### 标准登录流程

```mermaid
sequenceDiagram
    participant C as 客户端
    participant API as ID Provider API
    participant RA as 风险评估引擎
    participant MFA as MFA服务

    C->>API: POST /auth/login
    API->>RA: 评估登录风险
    RA-->>API: 风险评估结果
    
    alt 低风险
        API-->>C: 返回访问令牌
    else 中高风险
        API-->>C: 要求MFA验证
        C->>MFA: 发送验证码
        C->>API: POST /auth/mfa/verify
        API-->>C: 返回访问令牌
    else 极高风险
        API-->>C: 拒绝访问
    end
```

## API 端点

### 认证相关

#### POST /auth/login
用户登录

**请求参数:**
```json
{
  "email": "<EMAIL>",
  "password": "password123",
  "client_id": "your_client_id",
  "device_info": {
    "device_id": "device_uuid",
    "user_agent": "Mozilla/5.0...",
    "fingerprint": "device_fingerprint"
  }
}
```

**响应:**
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIs...",
  "refresh_token": "eyJhbGciOiJIUzI1NiIs...",
  "token_type": "Bearer",
  "expires_in": 3600,
  "user": {
    "id": "user_id",
    "email": "<EMAIL>",
    "name": "用户名",
    "roles": ["user"]
  }
}
```

**需要MFA时的响应:**
```json
{
  "session_id": "session_uuid",
  "required_methods": ["sms", "totp"],
  "message": "需要多因素认证"
}
```

#### POST /auth/mfa/sms/send
发送SMS验证码

**请求参数:**
```json
{
  "session_id": "session_uuid"
}
```

#### POST /auth/mfa/verify
验证MFA

**请求参数:**
```json
{
  "session_id": "session_uuid",
  "method": "sms",
  "code": "123456"
}
```

#### POST /auth/refresh
刷新访问令牌

**请求参数:**
```json
{
  "refresh_token": "refresh_token_here"
}
```

#### POST /auth/logout
用户登出

**请求头:**
```
Authorization: Bearer ACCESS_TOKEN
```

### 用户管理

#### GET /user/profile
获取用户信息

**响应:**
```json
{
  "id": "user_id",
  "email": "<EMAIL>",
  "name": "用户名",
  "phone": "+86-13800138000",
  "roles": ["user"],
  "permissions": ["read:profile"],
  "last_login_at": "2024-01-15T10:30:00Z",
  "created_at": "2024-01-01T00:00:00Z"
}
```

#### PUT /user/profile
更新用户信息

**请求参数:**
```json
{
  "name": "新用户名",
  "phone": "+86-13800138001"
}
```

#### POST /user/change-password
修改密码

**请求参数:**
```json
{
  "current_password": "old_password",
  "new_password": "new_password"
}
```

### MFA管理

#### GET /user/mfa/methods
获取用户的MFA方法

**响应:**
```json
{
  "methods": [
    {
      "type": "sms",
      "enabled": true,
      "phone": "+86-138****8000"
    },
    {
      "type": "totp",
      "enabled": false
    }
  ]
}
```

#### POST /user/mfa/totp/setup
设置TOTP

**响应:**
```json
{
  "secret": "JBSWY3DPEHPK3PXP",
  "qr_code": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...",
  "backup_codes": ["12345678", "87654321"]
}
```

#### POST /user/mfa/totp/verify
验证TOTP设置

**请求参数:**
```json
{
  "code": "123456"
}
```

### 安全管理

#### GET /user/sessions
获取用户会话

**响应:**
```json
{
  "sessions": [
    {
      "id": "session_id",
      "device": "Chrome on Windows",
      "ip": "*************",
      "location": "北京, 中国",
      "last_activity": "2024-01-15T10:30:00Z",
      "is_current": true
    }
  ]
}
```

#### DELETE /user/sessions/{session_id}
终止指定会话

#### GET /user/security-events
获取安全事件

**响应:**
```json
{
  "events": [
    {
      "id": "event_id",
      "type": "suspicious_login",
      "severity": "medium",
      "description": "检测到异常地理位置登录",
      "timestamp": "2024-01-15T10:30:00Z",
      "ip": "***********",
      "location": "东京, 日本"
    }
  ]
}
```

### 分析数据

#### GET /analytics/dashboard
获取仪表板数据

**查询参数:**
- `start_date`: 开始日期 (ISO 8601)
- `end_date`: 结束日期 (ISO 8601)
- `metrics`: 指标类型 (逗号分隔)

**响应:**
```json
{
  "overview": {
    "total_users": 12543,
    "active_users": 3421,
    "total_sessions": 8765,
    "security_events": 23
  },
  "user_activity": [
    {
      "time": "2024-01-15T00:00:00Z",
      "logins": 156,
      "registrations": 34,
      "failures": 8
    }
  ]
}
```

## 错误处理

### 错误响应格式

```json
{
  "error": {
    "code": "INVALID_CREDENTIALS",
    "message": "用户名或密码错误",
    "details": {
      "field": "password",
      "reason": "密码不匹配"
    }
  },
  "request_id": "req_123456789"
}
```

### 常见错误码

| 错误码 | HTTP状态码 | 描述 |
|--------|------------|------|
| `INVALID_CREDENTIALS` | 401 | 认证凭据无效 |
| `TOKEN_EXPIRED` | 401 | 访问令牌已过期 |
| `INSUFFICIENT_PERMISSIONS` | 403 | 权限不足 |
| `RESOURCE_NOT_FOUND` | 404 | 资源未找到 |
| `RATE_LIMIT_EXCEEDED` | 429 | 请求频率超限 |
| `VALIDATION_ERROR` | 400 | 请求参数验证失败 |
| `MFA_REQUIRED` | 202 | 需要多因素认证 |
| `ACCOUNT_BLOCKED` | 403 | 账户被阻止 |

## 速率限制

API实施了速率限制以防止滥用：

- **认证端点**: 每分钟5次请求
- **一般API**: 每分钟100次请求
- **分析API**: 每分钟20次请求

超出限制时会返回HTTP 429状态码，响应头包含限制信息：

```
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 0
X-RateLimit-Reset: **********
```

## Webhook

### 配置Webhook

```bash
curl -X POST https://api.idprovider.com/v1/webhooks \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "url": "https://yourapp.com/webhook",
    "events": ["user.login", "security.threat_detected"],
    "secret": "webhook_secret"
  }'
```

### 事件类型

- `user.login` - 用户登录
- `user.logout` - 用户登出
- `user.register` - 用户注册
- `security.threat_detected` - 威胁检测
- `security.mfa_required` - 需要MFA
- `security.account_blocked` - 账户被阻止

### Webhook载荷示例

```json
{
  "event": "user.login",
  "timestamp": "2024-01-15T10:30:00Z",
  "data": {
    "user_id": "user_123",
    "email": "<EMAIL>",
    "ip": "*************",
    "user_agent": "Mozilla/5.0...",
    "risk_score": 0.2
  }
}
```

## SDK集成

### JavaScript/TypeScript

```bash
npm install @idprovider/sdk
```

```typescript
import { IDProviderSDK } from '@idprovider/sdk';

const sdk = new IDProviderSDK({
  baseURL: 'https://api.idprovider.com/v1',
  clientId: 'your_client_id'
});

// 登录
const result = await sdk.login('<EMAIL>', 'password');
if (result.success) {
  console.log('登录成功:', result.user);
}
```

### iOS Swift

```swift
import IDProviderSDK

let config = IDProviderConfig(
    baseURL: "https://api.idprovider.com/v1",
    clientId: "your_client_id",
    clientSecret: "your_client_secret"
)

IDProviderSDK.shared.initialize(config: config)

// 登录
IDProviderSDK.shared.login(username: "<EMAIL>", password: "password") { result in
    switch result {
    case .success(let token, let refreshToken):
        print("登录成功")
    case .failed(let error):
        print("登录失败: \(error)")
    }
}
```

### Android Kotlin

```kotlin
import com.idprovider.sdk.IDProviderSDK

val config = IDProviderConfig(
    baseURL = "https://api.idprovider.com/v1",
    clientId = "your_client_id",
    clientSecret = "your_client_secret"
)

IDProviderSDK.getInstance().initialize(context, config)

// 登录
val result = IDProviderSDK.getInstance().login("<EMAIL>", "password")
when (result) {
    is AuthResult.Success -> println("登录成功")
    is AuthResult.Failed -> println("登录失败: ${result.error}")
}
```

## 最佳实践

### 安全建议

1. **使用HTTPS** - 所有API调用必须使用HTTPS
2. **安全存储令牌** - 使用安全存储机制保存访问令牌
3. **实施令牌刷新** - 在令牌过期前主动刷新
4. **验证Webhook** - 验证Webhook签名确保来源可信
5. **监控异常** - 监控登录异常和安全事件

### 性能优化

1. **缓存用户信息** - 合理缓存用户数据减少API调用
2. **批量操作** - 使用批量API减少网络请求
3. **连接池** - 使用HTTP连接池提高性能
4. **压缩传输** - 启用gzip压缩减少传输量

### 错误处理

1. **重试机制** - 对临时错误实施指数退避重试
2. **优雅降级** - 在服务不可用时提供备用方案
3. **日志记录** - 记录详细的错误日志便于调试
4. **用户友好** - 向用户显示友好的错误信息

## 支持

- **文档**: https://docs.idprovider.com
- **API参考**: https://api.idprovider.com/docs
- **技术支持**: <EMAIL>
- **状态页面**: https://status.idprovider.com

## 更新日志

### v1.2.0 (2024-01-15)
- 新增生物识别认证支持
- 改进威胁检测算法
- 优化移动端SDK性能

### v1.1.0 (2024-01-01)
- 新增Webhook支持
- 增强分析功能
- 修复已知问题

### v1.0.0 (2023-12-01)
- 首次发布
- 基础认证功能
- MFA支持
