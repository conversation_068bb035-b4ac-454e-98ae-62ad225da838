# 国际化支持文档

## 概述

本身份提供商系统实现了完整的国际化（i18n）支持，提供多语言界面、本地化适配、动态语言切换等功能，支持全球化部署和多地区用户使用。

## 核心特性

### ✅ 已实现的国际化功能

#### 1. **多语言支持** (`I18nService`)
- **✅ 支持12种语言**：
  - **中文** - 简体中文 (zh-CN)、繁体中文 (zh-TW)
  - **英语** - 美式英语 (en-US)、英式英语 (en-GB)
  - **亚洲语言** - 日语 (ja-JP)、韩语 (ko-KR)
  - **欧洲语言** - 法语 (fr-FR)、德语 (de-DE)、西班牙语 (es-ES)、葡萄牙语 (pt-BR)、俄语 (ru-RU)
  - **阿拉伯语** - 阿拉伯语 (ar-SA)

- **✅ 智能语言检测**：
  - **HTTP头部检测** - 基于Accept-Language头部自动检测
  - **用户代理检测** - 从User-Agent提取语言信息
  - **文本内容检测** - 基于文本内容智能识别语言
  - **多源检测** - 支持查询参数、Cookie、会话、用户偏好等多种检测源
  - **优先级排序** - 可配置的检测源优先级

- **✅ 翻译资源管理**：
  - **命名空间支持** - 按功能模块组织翻译资源
  - **嵌套键支持** - 支持多层级的翻译键结构
  - **变量插值** - 支持{{variable}}格式的变量替换
  - **复数形式** - 支持不同语言的复数规则
  - **性别形式** - 支持基于性别的翻译变体
  - **回退机制** - 翻译缺失时的多级回退策略

#### 2. **本地化适配** (`LocalizationConfig`)
- **✅ 日期时间格式化**：
  - **日期格式** - 各地区标准日期格式 (YYYY-MM-DD, MM/DD/YYYY等)
  - **时间格式** - 12/24小时制支持 (HH:mm:ss, h:mm:ss A)
  - **时区支持** - 自动时区检测和转换
  - **相对时间** - 智能相对时间显示 ("2小时前", "昨天"等)
  - **国际化API** - 基于Intl API的标准化格式

- **✅ 数字货币格式化**：
  - **数字格式** - 千分位分隔符、小数点符号
  - **货币格式** - 各地区货币符号和格式
  - **百分比格式** - 本地化百分比显示
  - **科学计数法** - 大数字的科学计数法表示

- **✅ 文化适配**：
  - **文本方向** - LTR/RTL文本方向支持
  - **一周首日** - 各地区一周开始日期设置
  - **颜色文化** - 考虑文化差异的颜色使用
  - **图标适配** - 文化敏感的图标选择

#### 3. **动态语言切换** (`I18nMiddleware`)
- **✅ 实时语言切换**：
  - **无刷新切换** - 动态更新界面语言
  - **状态保持** - 切换后保持当前页面状态
  - **Cookie持久化** - 语言偏好自动保存
  - **会话同步** - 跨会话的语言偏好同步

- **✅ 用户偏好管理**：
  - **个人设置** - 用户个人语言偏好设置
  - **自动检测** - 首次访问时的智能语言检测
  - **缓存优化** - 用户偏好缓存提升性能
  - **数据库持久化** - 长期保存用户语言偏好

#### 4. **RTL语言支持** (`RTLSupportMiddleware`)
- **✅ 阿拉伯语支持**：
  - **文本方向** - 自动RTL文本方向设置
  - **布局镜像** - 界面布局自动镜像翻转
  - **图标调整** - RTL环境下的图标方向调整
  - **CSS适配** - 自动应用RTL样式

#### 5. **翻译管理系统** (`I18nController`)
- **✅ 翻译资源API**：
  - **批量翻译** - 支持单个和批量翻译请求
  - **实时翻译** - 动态翻译文本内容
  - **缓存优化** - 翻译结果智能缓存
  - **性能监控** - 翻译性能指标收集

- **✅ 翻译完整性检查**：
  - **缺失检测** - 自动检测缺失的翻译键
  - **完整性统计** - 各语言翻译完成度统计
  - **质量评估** - 翻译质量评估和报告
  - **更新提醒** - 翻译更新提醒和通知

## 架构设计

### 国际化架构层次

```
┌─────────────────────────────────────────────────────────────────┐
│  表现层 (Presentation Layer)                                   │
│  - 多语言界面  - RTL支持  - 动态切换                           │
├─────────────────────────────────────────────────────────────────┤
│  服务层 (Service Layer)                                       │
│  - 翻译服务  - 本地化服务  - 语言检测                         │
├─────────────────────────────────────────────────────────────────┤
│  中间件层 (Middleware Layer)                                  │
│  - 语言检测中间件  - RTL支持中间件  - 语言切换中间件          │
├─────────────────────────────────────────────────────────────────┤
│  资源层 (Resource Layer)                                      │
│  - 翻译文件  - 本地化配置  - 语言元数据                       │
└─────────────────────────────────────────────────────────────────┘
```

### 语言检测流程

```typescript
// 多源语言检测流程
查询参数 → Cookie → 会话 → 用户偏好 → HTTP头部 → 默认语言

// 语言检测优先级
1. URL查询参数 (?lang=zh-CN)
2. Cookie存储 (i18n_lang)
3. 会话存储 (session.language)
4. 用户偏好 (database/cache)
5. Accept-Language头部
6. 系统默认语言
```

### 翻译资源结构

```
locales/
├── zh-CN/                     # 简体中文
│   ├── common.json                # 通用翻译
│   ├── auth.json                  # 认证相关
│   ├── admin.json                 # 管理界面
│   └── errors.json                # 错误信息
├── en-US/                     # 美式英语
│   ├── common.json
│   ├── auth.json
│   ├── admin.json
│   └── errors.json
├── ja-JP/                     # 日语
│   └── ...
└── ar-SA/                     # 阿拉伯语
    └── ...
```

## API接口

### 国际化管理API

| 端点 | 方法 | 功能 | 权限 | 缓存 |
|------|------|------|------|------|
| `/api/v1/i18n/languages` | GET | 获取支持语言列表 | 无 | 1小时 |
| `/api/v1/i18n/translations` | GET | 获取翻译资源 | 无 | 30分钟 |
| `/api/v1/i18n/preference` | POST | 设置语言偏好 | 认证用户 | 无 |
| `/api/v1/i18n/localization` | GET | 获取本地化配置 | 无 | 1小时 |
| `/api/v1/i18n/translate` | POST | 翻译文本 | 无 | 无 |
| `/api/v1/i18n/statistics` | GET | 获取统计信息 | admin/translator | 30分钟 |
| `/api/v1/i18n/detect` | POST | 检测语言 | 无 | 无 |
| `/api/v1/i18n/switch` | POST/GET | 语言切换 | 无 | 无 |

### 使用示例

#### 获取支持的语言列表
```bash
curl -X GET http://localhost:3000/api/v1/i18n/languages
```

响应：
```json
{
  "success": true,
  "data": {
    "languages": [
      {
        "code": "zh-CN",
        "name": "Chinese (Simplified)",
        "nativeName": "简体中文",
        "direction": "ltr",
        "region": "China",
        "flag": "🇨🇳",
        "enabled": true,
        "completeness": 100,
        "isCurrent": true,
        "isDefault": false
      },
      {
        "code": "en-US",
        "name": "English (US)",
        "nativeName": "English",
        "direction": "ltr",
        "region": "United States",
        "flag": "🇺🇸",
        "enabled": true,
        "completeness": 100,
        "isCurrent": false,
        "isDefault": true
      }
    ],
    "currentLanguage": "zh-CN",
    "totalLanguages": 12,
    "enabledLanguages": 12
  }
}
```

#### 设置语言偏好
```bash
curl -X POST http://localhost:3000/api/v1/i18n/preference \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"language": "zh-CN"}'
```

#### 获取翻译资源
```bash
curl -X GET "http://localhost:3000/api/v1/i18n/translations?language=zh-CN&namespace=common"
```

#### 翻译文本
```bash
curl -X POST http://localhost:3000/api/v1/i18n/translate \
  -H "Content-Type: application/json" \
  -d '{
    "key": "auth.login",
    "language": "zh-CN",
    "variables": {"username": "张三"}
  }'
```

## 翻译资源格式

### 基础翻译格式
```json
{
  "auth": {
    "login": "登录",
    "logout": "登出",
    "welcome": "欢迎，{{username}}！"
  },
  "common": {
    "save": "保存",
    "cancel": "取消"
  }
}
```

### 复数形式支持
```json
{
  "items": {
    "one": "{{count}} 个项目",
    "other": "{{count}} 个项目"
  }
}
```

### 性别形式支持
```json
{
  "welcome": {
    "male": "欢迎您，先生！",
    "female": "欢迎您，女士！",
    "neutral": "欢迎您！"
  }
}
```

## 中间件配置

### 国际化中间件配置
```typescript
// 基础配置
app.use(i18nMiddleware({
  sources: ['query', 'cookie', 'session', 'user', 'header'],
  cookieName: 'i18n_lang',
  queryParam: 'lang',
  sessionKey: 'language',
  cacheUserPreference: true,
  fallbackLanguage: SupportedLanguage.EN_US
}));

// RTL支持
app.use(rtlSupportMiddleware());

// 语言切换
app.use('/api/v1/i18n/switch', languageSwitchMiddleware());
```

### 在代码中使用翻译
```typescript
// 在路由处理器中
app.get('/api/example', (req, res) => {
  const message = req.i18n.t('common.success');
  const formattedDate = req.i18n.formatDate(new Date());
  const formattedNumber = req.i18n.formatNumber(1234.56);
  
  res.json({
    message,
    date: formattedDate,
    number: formattedNumber
  });
});

// 直接使用服务
import { i18nService } from '@/services/i18n.service';

const translation = i18nService.translate('auth.login', {
  language: SupportedLanguage.ZH_CN,
  variables: { username: '张三' }
});
```

## 本地化配置

### 各语言本地化设置
```typescript
// 中文配置
{
  dateFormat: 'YYYY-MM-DD',
  timeFormat: 'HH:mm:ss',
  numberFormat: { decimal: '.', thousands: ',', currency: '¥' },
  timezone: 'Asia/Shanghai',
  firstDayOfWeek: 1  // 周一
}

// 英文配置
{
  dateFormat: 'MM/DD/YYYY',
  timeFormat: 'h:mm:ss A',
  numberFormat: { decimal: '.', thousands: ',', currency: '$' },
  timezone: 'America/New_York',
  firstDayOfWeek: 0  // 周日
}

// 阿拉伯语配置
{
  dateFormat: 'DD/MM/YYYY',
  timeFormat: 'HH:mm:ss',
  numberFormat: { decimal: '.', thousands: ',', currency: 'ر.س' },
  timezone: 'Asia/Riyadh',
  firstDayOfWeek: 6  // 周六
}
```

## 性能优化

### 1. **缓存策略**
- **翻译缓存** - 翻译结果缓存30分钟
- **语言列表缓存** - 支持语言列表缓存1小时
- **用户偏好缓存** - 用户语言偏好缓存30天
- **本地化配置缓存** - 本地化配置缓存1小时

### 2. **懒加载**
- **按需加载** - 只加载当前语言的翻译资源
- **命名空间分离** - 按功能模块分离翻译文件
- **动态导入** - 运行时动态加载翻译资源

### 3. **压缩优化**
- **JSON压缩** - 翻译文件压缩存储
- **Gzip压缩** - HTTP传输压缩
- **CDN分发** - 翻译资源CDN分发

## 翻译管理

### 1. **翻译完整性监控**
```typescript
// 翻译完整性检查
const statistics = {
  totalLanguages: 12,
  enabledLanguages: 12,
  completenessStats: {
    fullyTranslated: 2,      // 100%完成
    partiallyTranslated: 10, // 部分完成
    notTranslated: 0,        // 未翻译
    averageCompleteness: 75.8 // 平均完成度
  }
};
```

### 2. **翻译质量保证**
- **键值一致性检查** - 确保所有语言包含相同的翻译键
- **变量占位符检查** - 验证变量占位符的一致性
- **格式验证** - JSON格式和语法验证
- **长度限制检查** - 翻译文本长度合理性检查

### 3. **翻译工作流**
```
1. 开发阶段 → 添加翻译键到基础语言(en-US)
2. 提取阶段 → 自动提取新增的翻译键
3. 翻译阶段 → 翻译人员翻译各语言版本
4. 审核阶段 → 语言专家审核翻译质量
5. 集成阶段 → 集成翻译到系统中
6. 测试阶段 → 多语言功能测试
7. 发布阶段 → 发布多语言版本
```

## 最佳实践

### 1. **翻译键命名规范**
```typescript
// 推荐的命名规范
'module.component.action'     // 模块.组件.操作
'auth.login.button'          // 认证.登录.按钮
'user.profile.save_success'  // 用户.资料.保存成功
'error.validation.required'  // 错误.验证.必填
```

### 2. **变量使用规范**
```typescript
// 使用描述性变量名
'welcome_message': '欢迎，{{username}}！'
'item_count': '共有 {{count}} 个项目'
'date_range': '从 {{startDate}} 到 {{endDate}}'
```

### 3. **复数处理**
```typescript
// 英语复数规则
{
  "items": {
    "one": "{{count}} item",
    "other": "{{count}} items"
  }
}

// 中文无复数变化
{
  "items": {
    "other": "{{count}} 个项目"
  }
}
```

### 4. **文化敏感性**
- **颜色使用** - 避免文化敏感的颜色组合
- **图标选择** - 使用国际通用的图标
- **日期格式** - 遵循当地日期格式习惯
- **数字格式** - 使用当地数字格式规范

## 部署配置

### 环境变量配置
```bash
# 国际化配置
I18N_DEFAULT_LANGUAGE=en-US
I18N_FALLBACK_LANGUAGE=en-US
I18N_CACHE_TTL=1800
I18N_AUTO_DETECT=true

# 本地化配置
LOCALIZATION_TIMEZONE=UTC
LOCALIZATION_DATE_FORMAT=YYYY-MM-DD
LOCALIZATION_TIME_FORMAT=HH:mm:ss
```

### Docker配置
```dockerfile
# 复制翻译资源
COPY locales/ /app/locales/

# 设置语言环境
ENV LANG=C.UTF-8
ENV LC_ALL=C.UTF-8
```

## 监控和分析

### 1. **使用统计**
- **语言使用分布** - 各语言的使用频率统计
- **地区分布** - 用户地理分布分析
- **切换频率** - 语言切换行为分析
- **性能指标** - 翻译服务性能监控

### 2. **错误监控**
- **翻译缺失** - 缺失翻译的监控和告警
- **格式错误** - 翻译格式错误检测
- **加载失败** - 翻译资源加载失败监控
- **性能异常** - 翻译服务性能异常告警

通过这个全面的国际化支持实现，身份提供商系统能够为全球用户提供本地化的使用体验，支持多语言、多地区的部署需求，确保在不同文化背景下都能提供优质的用户体验。

**🌍 国际化支持实现完成！**
