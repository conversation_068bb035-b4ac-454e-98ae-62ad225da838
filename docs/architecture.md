# 身份提供商 (IdP) 系统架构文档

## 1. 技术栈选择

### 后端技术栈
- **运行时**: Node.js 18+
- **语言**: TypeScript
- **Web框架**: Express.js
- **数据库**: PostgreSQL
- **ORM**: Prisma
- **认证**: Passport.js + JWT
- **加密**: bcrypt, crypto
- **验证**: Joi
- **日志**: Winston
- **测试**: Jest + Supertest

### 前端技术栈
- **框架**: React + TypeScript
- **状态管理**: Zustand
- **UI库**: Ant Design
- **HTTP客户端**: Axios
- **路由**: React Router

## 2. 系统架构

### 2.1 整体架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端应用      │    │   服务提供商    │    │   第三方IdP     │
│   (React)       │    │   (SP)          │    │   (Google/GitHub)│
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │ HTTP/HTTPS            │ OIDC/SAML            │ OAuth2
         │                       │                       │
┌─────────────────────────────────────────────────────────────────┐
│                    身份提供商 (IdP)                              │
├─────────────────────────────────────────────────────────────────┤
│  API Gateway (Express.js + Middleware)                         │
├─────────────────────────────────────────────────────────────────┤
│  认证服务  │  用户管理  │  MFA服务  │  零信任  │  SSO协议        │
├─────────────────────────────────────────────────────────────────┤
│  数据访问层 (Prisma ORM)                                        │
├─────────────────────────────────────────────────────────────────┤
│  数据库 (PostgreSQL)                                            │
└─────────────────────────────────────────────────────────────────┘
```

### 2.2 目录结构
```
src/
├── config/          # 配置文件
├── controllers/     # 控制器层
├── middleware/      # 中间件
├── models/          # 数据模型
├── routes/          # 路由定义
├── services/        # 业务逻辑层
├── utils/           # 工具函数
├── types/           # TypeScript类型定义
└── test/            # 测试文件
```

## 3. 核心模块设计

### 3.1 认证模块 (Authentication)
- 用户注册/登录
- 密码管理
- JWT令牌管理
- 会话管理

### 3.2 多因素认证模块 (MFA)
- TOTP (Time-based OTP)
- 邮件验证码
- 短信验证码
- 备用恢复码

### 3.3 联合认证模块 (Federation)
- OAuth2 客户端
- 第三方账户关联
- 身份映射

### 3.4 SSO协议模块
- OpenID Connect Provider
- OAuth2 Authorization Server
- SAML 2.0 Identity Provider

### 3.5 零信任模块 (Zero Trust)
- 风险评估引擎
- 设备指纹识别
- 行为分析
- 自适应认证

### 3.6 用户管理模块
- 用户CRUD操作
- 个人资料管理
- 权限管理

### 3.7 应用管理模块
- 应用注册
- 客户端凭据管理
- 回调URL管理

## 4. 数据模型设计

### 4.1 核心实体
- User (用户)
- Application (应用)
- Session (会话)
- MFADevice (MFA设备)
- FederatedIdentity (联合身份)
- AuditLog (审计日志)
- RiskAssessment (风险评估)

### 4.2 关系设计
- 用户 1:N MFA设备
- 用户 1:N 联合身份
- 用户 1:N 会话
- 应用 1:N 用户会话
- 用户 1:N 审计日志

## 5. 安全设计

### 5.1 数据保护
- 密码使用bcrypt加密存储
- 敏感数据加密存储
- TLS/HTTPS通信加密

### 5.2 访问控制
- JWT令牌认证
- 基于角色的访问控制(RBAC)
- API速率限制

### 5.3 安全中间件
- CORS配置
- Helmet安全头
- 输入验证和清理
- SQL注入防护

## 6. 性能优化

### 6.1 缓存策略
- JWT令牌缓存
- 用户会话缓存
- 配置信息缓存

### 6.2 数据库优化
- 索引优化
- 查询优化
- 连接池管理

## 7. 监控和日志

### 7.1 日志记录
- 结构化日志
- 安全事件日志
- 性能指标日志

### 7.2 监控指标
- 响应时间
- 错误率
- 并发用户数
- 认证成功率
