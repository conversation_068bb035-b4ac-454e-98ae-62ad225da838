# OAuth 2.0 安全配置指南

## 概述

本文档详细介绍了OAuth 2.0认证系统的安全配置和最佳实践，确保系统在生产环境中的安全性和可靠性。

## 安全架构

### 核心安全组件

1. **状态参数验证** - 防止CSRF攻击
2. **PKCE (Proof Key for Code Exchange)** - 增强授权码安全性
3. **令牌安全存储** - 加密存储敏感令牌
4. **回调URL验证** - 防止重定向攻击
5. **速率限制** - 防止暴力攻击
6. **安全日志** - 记录和监控安全事件

### 安全流程图

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant IdP as 身份提供商
    participant OAuth as OAuth服务商
    participant Redis as Redis缓存

    Client->>IdP: 1. 发起OAuth登录
    IdP->>Redis: 2. 生成并存储状态参数
    IdP->>Client: 3. 重定向到OAuth授权页面
    Client->>OAuth: 4. 用户授权
    OAuth->>IdP: 5. 回调携带授权码和状态
    IdP->>Redis: 6. 验证并消费状态参数
    IdP->>OAuth: 7. 交换访问令牌
    OAuth->>IdP: 8. 返回访问令牌
    IdP->>OAuth: 9. 获取用户信息
    OAuth->>IdP: 10. 返回用户信息
    IdP->>Client: 11. 登录成功，返回JWT令牌
```

## 安全配置

### 1. 状态参数管理

#### 配置说明
```typescript
// OAuth状态配置
export const OAUTH_STATE_CONFIG = {
  // 状态参数长度（字节）
  STATE_LENGTH: 32,
  // 状态过期时间（毫秒）
  STATE_EXPIRY_TIME: 10 * 60 * 1000, // 10分钟
  // Redis键前缀
  STATE_PREFIX: 'oauth:state:',
  // 清理间隔（毫秒）
  CLEANUP_INTERVAL: 5 * 60 * 1000 // 5分钟
};
```

#### 安全特性
- 使用加密安全的随机数生成器
- 状态参数一次性使用，防止重放攻击
- 自动过期清理，防止内存泄漏
- 详细的安全事件日志记录

### 2. PKCE配置

#### 代码验证器生成
```typescript
// PKCE配置
export const PKCE_CONFIG = {
  // 代码验证器长度（字节）
  CODE_VERIFIER_LENGTH: 128,
  // 代码挑战方法
  CODE_CHALLENGE_METHOD: 'S256', // SHA256
  // 存储过期时间（秒）
  STORAGE_TTL: 600 // 10分钟
};
```

#### 实现细节
- 使用SHA256哈希算法生成代码挑战
- Base64URL编码确保URL安全
- 服务端验证代码验证器和挑战的匹配性

### 3. 令牌安全存储

#### 加密配置
```typescript
// 令牌加密配置
export const TOKEN_ENCRYPTION_CONFIG = {
  // 加密算法
  ALGORITHM: 'aes-256-gcm',
  // 密钥长度
  KEY_LENGTH: 32,
  // IV长度
  IV_LENGTH: 16,
  // 认证标签长度
  AUTH_TAG_LENGTH: 16
};
```

#### 存储策略
- 访问令牌加密存储在Redis中
- 刷新令牌使用独立的加密密钥
- 定期轮换加密密钥
- 令牌过期自动清理

### 4. 回调URL验证

#### 域名白名单配置
```typescript
// 允许的回调域名配置
export const CALLBACK_DOMAINS = {
  // 生产环境域名
  production: [
    'yourdomain.com',
    '*.yourdomain.com',
    'api.yourdomain.com'
  ],
  // 开发环境域名
  development: [
    'localhost',
    '127.0.0.1',
    '*.local'
  ],
  // 测试环境域名
  test: [
    'test.yourdomain.com',
    '*.test.yourdomain.com'
  ]
};
```

#### 验证规则
- 强制HTTPS（生产环境）
- 域名白名单验证
- 支持通配符子域名
- 防止开放重定向攻击

### 5. 速率限制配置

#### 限制策略
```typescript
// OAuth速率限制配置
export const OAUTH_RATE_LIMITS = {
  // 认证请求限制
  auth_requests: {
    windowMs: 15 * 60 * 1000, // 15分钟
    max: 20, // 每IP最多20次
    message: 'OAuth请求过于频繁'
  },
  // 令牌交换限制
  token_exchange: {
    windowMs: 5 * 60 * 1000, // 5分钟
    max: 10, // 每IP最多10次
    message: '令牌交换请求过于频繁'
  },
  // 用户信息获取限制
  user_info: {
    windowMs: 10 * 60 * 1000, // 10分钟
    max: 50, // 每用户最多50次
    message: '用户信息请求过于频繁'
  }
};
```

## 安全监控

### 1. 安全事件类型

#### 高风险事件
- `state_validation_failed` - 状态验证失败
- `invalid_callback_url` - 无效回调URL
- `token_exchange_failed` - 令牌交换失败
- `excessive_oauth_attempts` - 过多OAuth尝试

#### 中风险事件
- `oauth_error_received` - OAuth错误响应
- `user_info_failed` - 用户信息获取失败
- `rate_limit_exceeded` - 速率限制超出

#### 低风险事件
- `oauth_success` - OAuth认证成功
- `state_cleanup` - 状态清理
- `token_refresh` - 令牌刷新

### 2. 监控指标

#### 关键性能指标 (KPI)
```typescript
// 监控指标配置
export const SECURITY_METRICS = {
  // 认证成功率
  auth_success_rate: {
    threshold: 0.95, // 95%
    alert_level: 'warning'
  },
  // 状态验证失败率
  state_validation_failure_rate: {
    threshold: 0.05, // 5%
    alert_level: 'critical'
  },
  // 平均响应时间
  avg_response_time: {
    threshold: 2000, // 2秒
    alert_level: 'warning'
  },
  // 错误率
  error_rate: {
    threshold: 0.01, // 1%
    alert_level: 'critical'
  }
};
```

### 3. 日志配置

#### 日志级别
```typescript
// 安全日志配置
export const SECURITY_LOGGING = {
  // 日志级别
  levels: {
    security_event: 'warn',
    oauth_success: 'info',
    oauth_error: 'error',
    rate_limit: 'warn'
  },
  // 日志格式
  format: {
    timestamp: true,
    level: true,
    message: true,
    metadata: true,
    stack_trace: true // 仅错误级别
  },
  // 日志输出
  transports: [
    'console',
    'file',
    'elasticsearch' // 生产环境
  ]
};
```

## 部署安全配置

### 1. 环境变量安全

#### 敏感信息管理
```bash
# 使用密钥管理服务
export OAUTH_ENCRYPTION_KEY=$(aws ssm get-parameter --name "/idp/oauth/encryption-key" --with-decryption --query "Parameter.Value" --output text)

# 或使用Docker secrets
docker service create \
  --secret oauth-encryption-key \
  --env OAUTH_ENCRYPTION_KEY_FILE=/run/secrets/oauth-encryption-key \
  your-app
```

### 2. 网络安全

#### HTTPS配置
```nginx
# Nginx SSL配置
server {
    listen 443 ssl http2;
    server_name your-domain.com;
    
    # SSL证书
    ssl_certificate /path/to/cert.pem;
    ssl_certificate_key /path/to/key.pem;
    
    # SSL安全配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    ssl_prefer_server_ciphers off;
    
    # 安全头
    add_header Strict-Transport-Security "max-age=63072000" always;
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header Referrer-Policy "strict-origin-when-cross-origin";
    
    # OAuth端点
    location /api/v1/auth/ {
        proxy_pass http://localhost:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 速率限制
        limit_req zone=oauth burst=10 nodelay;
    }
}
```

### 3. 容器安全

#### Docker安全配置
```dockerfile
# 使用非root用户
FROM node:18-alpine
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nextjs -u 1001

# 复制应用文件
COPY --chown=nextjs:nodejs . .
USER nextjs

# 安全启动
EXPOSE 3000
CMD ["node", "dist/index.js"]
```

## 安全测试

### 1. 安全测试清单

#### OAuth流程测试
- [ ] 状态参数验证测试
- [ ] PKCE流程测试
- [ ] 回调URL验证测试
- [ ] 令牌交换安全测试
- [ ] 用户信息获取测试

#### 安全攻击测试
- [ ] CSRF攻击防护测试
- [ ] 重放攻击防护测试
- [ ] 重定向攻击防护测试
- [ ] 暴力攻击防护测试
- [ ] SQL注入防护测试

### 2. 自动化安全测试

#### 测试脚本示例
```bash
#!/bin/bash
# OAuth安全测试脚本

echo "开始OAuth安全测试..."

# 测试状态参数验证
echo "测试状态参数验证..."
curl -X GET "http://localhost:3000/api/v1/auth/google/callback?code=test&state=invalid" \
  -H "Accept: application/json" \
  --fail-with-body || echo "✓ 状态验证测试通过"

# 测试速率限制
echo "测试速率限制..."
for i in {1..25}; do
  curl -X GET "http://localhost:3000/api/v1/auth/google" \
    -H "X-Forwarded-For: *************" \
    --silent --output /dev/null
done
curl -X GET "http://localhost:3000/api/v1/auth/google" \
  -H "X-Forwarded-For: *************" \
  --fail-with-body || echo "✓ 速率限制测试通过"

echo "OAuth安全测试完成"
```

## 应急响应

### 1. 安全事件响应流程

#### 高风险事件处理
1. **立即响应** (< 5分钟)
   - 自动告警通知
   - 临时封禁可疑IP
   - 记录详细日志

2. **调查分析** (< 30分钟)
   - 分析攻击模式
   - 确定影响范围
   - 制定应对策略

3. **修复措施** (< 2小时)
   - 修复安全漏洞
   - 更新安全配置
   - 部署安全补丁

### 2. 安全配置备份

#### 配置备份策略
```bash
# 每日备份安全配置
#!/bin/bash
DATE=$(date +%Y%m%d)
BACKUP_DIR="/backup/security-config/$DATE"

mkdir -p $BACKUP_DIR

# 备份OAuth配置
cp /app/config/oauth.json $BACKUP_DIR/
cp /app/config/security.json $BACKUP_DIR/

# 备份Redis状态数据
redis-cli --rdb $BACKUP_DIR/oauth-states.rdb

# 上传到安全存储
aws s3 cp $BACKUP_DIR s3://security-backups/oauth-config/$DATE/ --recursive
```

## 合规性要求

### 1. 数据保护法规

#### GDPR合规
- 用户数据最小化收集
- 明确的用户同意机制
- 数据删除权实现
- 数据泄露通知机制

#### 国内法规合规
- 《网络安全法》合规
- 《数据安全法》合规
- 《个人信息保护法》合规

### 2. 安全标准

#### ISO 27001
- 信息安全管理体系
- 风险评估和管理
- 安全控制措施
- 持续改进机制

#### OAuth 2.0 Security Best Practices
- RFC 6749 OAuth 2.0规范
- RFC 7636 PKCE规范
- OAuth 2.0 Security Best Current Practice
