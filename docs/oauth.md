# OAuth 第三方登录集成指南

本文档介绍如何在身份提供商系统中集成和使用第三方OAuth登录功能。

## 支持的OAuth提供商

### 已实现的提供商

1. **Google OAuth 2.0**
   - 提供商标识：`google`
   - 支持获取用户基本信息和邮箱
   - 授权范围：`profile`, `email`

2. **GitHub OAuth 2.0**
   - 提供商标识：`github`
   - 支持获取用户基本信息和邮箱
   - 授权范围：`user:email`

3. **微信开放平台**
   - 提供商标识：`wechat`
   - 支持获取用户基本信息
   - 授权范围：`snsapi_login`

4. **微博开放平台**
   - 提供商标识：`weibo`
   - 支持获取用户基本信息和邮箱
   - 授权范围：`email`

## 配置说明

### 环境变量配置

在 `.env` 文件中配置各个OAuth提供商的应用信息：

```bash
# Google OAuth 配置
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"
GOOGLE_CALLBACK_URL="http://localhost:3000/api/v1/auth/google/callback"

# GitHub OAuth 配置
GITHUB_CLIENT_ID="your-github-client-id"
GITHUB_CLIENT_SECRET="your-github-client-secret"
GITHUB_CALLBACK_URL="http://localhost:3000/api/v1/auth/github/callback"

# 微信开放平台配置
WECHAT_APP_ID="your-wechat-app-id"
WECHAT_APP_SECRET="your-wechat-app-secret"
WECHAT_CALLBACK_URL="http://localhost:3000/api/v1/auth/wechat/callback"

# 微博开放平台配置
WEIBO_CLIENT_ID="your-weibo-client-id"
WEIBO_CLIENT_SECRET="your-weibo-client-secret"
WEIBO_CALLBACK_URL="http://localhost:3000/api/v1/auth/weibo/callback"
```

### 获取OAuth应用凭据

#### Google OAuth 2.0
1. 访问 [Google Cloud Console](https://console.cloud.google.com/)
2. 创建新项目或选择现有项目
3. 启用 Google+ API
4. 创建OAuth 2.0客户端ID
5. 配置授权重定向URI

#### GitHub OAuth
1. 访问 [GitHub Developer Settings](https://github.com/settings/developers)
2. 创建新的OAuth App
3. 配置Authorization callback URL

#### 微信开放平台
1. 访问 [微信开放平台](https://open.weixin.qq.com/)
2. 注册开发者账号
3. 创建网站应用
4. 获取AppID和AppSecret

#### 微博开放平台
1. 访问 [微博开放平台](https://open.weibo.com/)
2. 注册开发者账号
3. 创建网站应用
4. 获取App Key和App Secret

## API接口

### 获取支持的OAuth提供商

```http
GET /api/v1/auth/providers
```

响应示例：
```json
{
  "providers": [
    {
      "name": "google",
      "displayName": "Google",
      "authUrl": "/api/v1/auth/google",
      "enabled": true
    },
    {
      "name": "github",
      "displayName": "GitHub",
      "authUrl": "/api/v1/auth/github",
      "enabled": true
    }
  ],
  "total": 2
}
```

### 发起OAuth登录

```http
GET /api/v1/auth/{provider}
```

支持的provider值：`google`, `github`, `wechat`, `weibo`

### OAuth回调处理

```http
GET /api/v1/auth/{provider}/callback
```

成功登录后会重定向到前端，携带以下参数：
- `access_token`: 访问令牌
- `refresh_token`: 刷新令牌
- `expires_in`: 令牌有效期
- `user_id`: 用户ID
- `is_new_user`: 是否为新用户
- `provider`: OAuth提供商

### 解除OAuth关联

```http
DELETE /api/v1/auth/disconnect/{provider}
Authorization: Bearer {access_token}
```

## 前端集成示例

### React示例

```jsx
import React from 'react';

const OAuthLogin = () => {
  const handleOAuthLogin = (provider) => {
    window.location.href = `/api/v1/auth/${provider}`;
  };

  return (
    <div>
      <button onClick={() => handleOAuthLogin('google')}>
        使用Google登录
      </button>
      <button onClick={() => handleOAuthLogin('github')}>
        使用GitHub登录
      </button>
      <button onClick={() => handleOAuthLogin('wechat')}>
        使用微信登录
      </button>
      <button onClick={() => handleOAuthLogin('weibo')}>
        使用微博登录
      </button>
    </div>
  );
};

export default OAuthLogin;
```

### 处理OAuth回调

```jsx
import { useEffect } from 'react';
import { useLocation } from 'react-router-dom';

const OAuthCallback = () => {
  const location = useLocation();

  useEffect(() => {
    const params = new URLSearchParams(location.search);
    const accessToken = params.get('access_token');
    const error = params.get('error');

    if (error) {
      console.error('OAuth登录失败:', params.get('message'));
      return;
    }

    if (accessToken) {
      // 保存令牌并跳转到主页
      localStorage.setItem('access_token', accessToken);
      localStorage.setItem('refresh_token', params.get('refresh_token'));
      window.location.href = '/dashboard';
    }
  }, [location]);

  return <div>正在处理登录...</div>;
};
```

## 安全注意事项

1. **HTTPS要求**：生产环境必须使用HTTPS
2. **回调URL验证**：确保回调URL配置正确
3. **令牌安全**：访问令牌和刷新令牌需要安全存储
4. **CSRF保护**：使用state参数防止CSRF攻击
5. **作用域最小化**：只请求必要的权限范围

## 故障排除

### 常见错误

1. **redirect_uri_mismatch**
   - 检查OAuth应用配置中的回调URL
   - 确保环境变量中的回调URL正确

2. **invalid_client**
   - 检查Client ID和Client Secret是否正确
   - 确认OAuth应用状态是否正常

3. **access_denied**
   - 用户拒绝授权
   - 检查请求的权限范围是否合理

### 调试技巧

1. 查看应用日志中的OAuth相关错误
2. 使用浏览器开发者工具检查网络请求
3. 验证环境变量配置是否正确
4. 测试OAuth应用的回调URL是否可访问
