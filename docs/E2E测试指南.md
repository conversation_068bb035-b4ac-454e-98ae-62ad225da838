# 端到端 (E2E) 测试指南

## 📋 概述

本项目使用 Playwright 和 Jest 构建了完整的端到端测试套件，覆盖了身份提供商的关键用户流程和功能。E2E测试确保整个应用程序在真实浏览器环境中的正确性。

## 🏗️ 测试架构

### 技术栈
- **测试框架**: Jest
- **浏览器自动化**: Playwright
- **页面对象模式**: 封装页面元素和操作
- **测试环境管理**: 自动化测试服务器启动和清理
- **报告生成**: HTML和JUnit格式的测试报告

### 目录结构
```
src/test/e2e/
├── setup.ts              # E2E测试环境设置
├── jest.setup.ts         # Jest配置和自定义匹配器
├── pages/                # 页面对象模型
│   ├── login.page.ts     # 登录页面
│   ├── dashboard.page.ts # 仪表板页面
│   └── ...
└── specs/                # 测试用例
    ├── auth.e2e.test.ts  # 认证流程测试
    ├── oauth.e2e.test.ts # OAuth流程测试
    └── ...
```

## 🚀 快速开始

### 安装依赖
```bash
npm install
```

### 安装浏览器
```bash
npx playwright install
```

### 运行E2E测试
```bash
# 运行所有E2E测试（无头模式）
npm run test:e2e

# 运行E2E测试（有头模式，可以看到浏览器）
npm run test:e2e:headed

# 监视模式运行E2E测试
npm run test:e2e:watch

# CI环境运行E2E测试
npm run test:e2e:ci
```

## 🔧 配置选项

### 环境变量

| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| `E2E_BASE_URL` | `http://localhost:3001` | 测试服务器地址 |
| `E2E_HEADLESS` | `true` | 是否无头模式运行 |
| `E2E_TIMEOUT` | `30000` | 测试超时时间（毫秒） |
| `E2E_BROWSER` | `chromium` | 使用的浏览器 |
| `E2E_VIEWPORT_WIDTH` | `1280` | 浏览器视口宽度 |
| `E2E_VIEWPORT_HEIGHT` | `720` | 浏览器视口高度 |
| `E2E_SLOW_MO` | `0` | 操作间延迟（毫秒） |
| `E2E_SCREENSHOTS` | `true` | 是否启用截图 |
| `E2E_VIDEOS` | `false` | 是否录制视频 |
| `E2E_RETRIES` | `2` | 失败重试次数 |

### 示例配置
```bash
# 开发环境 - 有头模式，慢速执行
E2E_HEADLESS=false E2E_SLOW_MO=500 npm run test:e2e

# CI环境 - 无头模式，启用重试
E2E_HEADLESS=true E2E_RETRIES=3 npm run test:e2e:ci

# 调试模式 - 有头模式，启用视频录制
E2E_HEADLESS=false E2E_VIDEOS=true npm run test:e2e
```

## 📝 编写测试

### 页面对象模式

使用页面对象模式封装页面元素和操作：

```typescript
// src/test/e2e/pages/example.page.ts
import { Page } from 'playwright';
import { BasePage } from '../setup';

export class ExamplePage extends BasePage {
  private readonly selectors = {
    submitButton: '[data-testid="submit-button"]',
    errorMessage: '[data-testid="error-message"]'
  };

  constructor(page: Page, baseUrl: string) {
    super(page, baseUrl);
  }

  async clickSubmit(): Promise<void> {
    await this.click(this.selectors.submitButton);
  }

  async getErrorMessage(): Promise<string> {
    return await this.getText(this.selectors.errorMessage);
  }
}
```

### 测试用例结构

```typescript
// src/test/e2e/specs/example.e2e.test.ts
import { describe, beforeAll, afterAll, test, expect } from '@jest/globals';
import { testEnvironment, TestDataFactory } from '../setup';
import { ExamplePage } from '../pages/example.page';

describe('示例功能 E2E 测试', () => {
  let examplePage: ExamplePage;

  beforeAll(async () => {
    await testEnvironment.setup();
    const page = testEnvironment.getPage();
    examplePage = new ExamplePage(page, 'http://localhost:3001');
  });

  afterAll(async () => {
    await testEnvironment.teardown();
  });

  test('应该能够执行基本操作', async () => {
    await examplePage.goto('/example');
    await examplePage.clickSubmit();
    
    expect(await examplePage.getErrorMessage()).toContain('成功');
  });
});
```

### 测试数据管理

使用 TestDataFactory 创建测试数据：

```typescript
// 创建测试用户
const testUser = await TestDataFactory.createTestUser({
  email: '<EMAIL>',
  password: 'Test123!@#'
});

// 创建OAuth客户端
const oauthClient = await TestDataFactory.createOAuthClient({
  clientId: 'test-client',
  redirectUris: ['http://localhost:3001/callback']
});
```

## 🧪 测试覆盖范围

### 认证流程测试
- ✅ 用户登录/登出
- ✅ 密码验证
- ✅ 多因素认证 (MFA)
- ✅ 会话管理
- ✅ 安全防护（暴力破解保护）

### OAuth流程测试
- ✅ 授权码流程
- ✅ 令牌交换
- ✅ 刷新令牌
- ✅ PKCE支持
- ✅ 作用域管理

### 用户界面测试
- ✅ 响应式设计
- ✅ 表单验证
- ✅ 错误处理
- ✅ 国际化支持

### 性能测试
- ✅ 页面加载时间
- ✅ 操作响应时间
- ✅ 网络错误处理

## 📊 测试报告

### HTML报告
测试完成后，会在 `test-results/e2e/` 目录生成HTML格式的测试报告：
- `e2e-test-report.html` - 详细的测试结果报告

### JUnit报告
同时生成JUnit格式的报告，用于CI/CD集成：
- `e2e-test-results.xml` - JUnit格式的测试结果

### 截图和视频
- 失败的测试会自动截图保存到 `test-results/screenshots/`
- 启用视频录制时，会保存到 `test-results/videos/`

## 🔍 调试测试

### 本地调试
```bash
# 有头模式运行，可以看到浏览器操作
E2E_HEADLESS=false npm run test:e2e

# 慢速执行，便于观察
E2E_HEADLESS=false E2E_SLOW_MO=1000 npm run test:e2e

# 运行特定测试
npm run test:e2e -- --testNamePattern="登录功能"
```

### 调试技巧
1. **使用断点**: 在测试代码中添加 `await page.pause()` 暂停执行
2. **截图调试**: 在关键步骤添加 `await page.screenshot({ path: 'debug.png' })`
3. **控制台输出**: 监听页面控制台消息来调试JavaScript错误
4. **网络监控**: 使用 `page.route()` 拦截和检查网络请求

### 常见问题排查

#### 测试超时
```bash
# 增加超时时间
E2E_TIMEOUT=60000 npm run test:e2e
```

#### 元素找不到
- 检查选择器是否正确
- 确认元素是否已加载
- 使用 `waitForSelector` 等待元素出现

#### 服务器启动失败
- 检查端口是否被占用
- 确认数据库连接正常
- 查看服务器启动日志

## 🚀 CI/CD 集成

### GitHub Actions 示例
```yaml
name: E2E Tests

on: [push, pull_request]

jobs:
  e2e:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          
      - name: Install dependencies
        run: npm ci
        
      - name: Install Playwright browsers
        run: npx playwright install --with-deps
        
      - name: Run E2E tests
        run: npm run test:e2e:ci
        
      - name: Upload test results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: e2e-test-results
          path: test-results/
```

## 📈 最佳实践

### 测试设计原则
1. **独立性**: 每个测试应该独立运行，不依赖其他测试
2. **可重复性**: 测试结果应该一致和可预测
3. **清晰性**: 测试名称和步骤应该清晰易懂
4. **维护性**: 使用页面对象模式，便于维护

### 性能优化
1. **并行执行**: 在可能的情况下并行运行测试
2. **资源清理**: 及时清理测试数据和资源
3. **选择性运行**: 只运行相关的测试用例
4. **缓存优化**: 合理使用浏览器缓存

### 数据管理
1. **测试隔离**: 每个测试使用独立的测试数据
2. **数据清理**: 测试前后清理数据库状态
3. **工厂模式**: 使用工厂方法创建测试数据
4. **环境分离**: 使用专门的测试数据库

## 🔧 扩展测试

### 添加新的页面对象
1. 在 `src/test/e2e/pages/` 创建新的页面类
2. 继承 `BasePage` 基类
3. 定义页面特定的选择器和方法

### 添加新的测试用例
1. 在 `src/test/e2e/specs/` 创建新的测试文件
2. 使用描述性的测试名称
3. 遵循 AAA 模式（Arrange, Act, Assert）

### 自定义匹配器
在 `jest.setup.ts` 中添加自定义的Jest匹配器：

```typescript
expect.extend({
  toBeVisibleWithin(received, timeout) {
    // 自定义匹配器实现
  }
});
```

---

*文档版本: 1.0*  
*最后更新: 2025-08-28*
