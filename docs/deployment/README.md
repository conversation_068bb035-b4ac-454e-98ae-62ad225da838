# ID Provider 部署指南

## 概述

本指南详细介绍了如何在生产环境中部署 ID Provider 身份认证平台。

## 系统要求

### 硬件要求

**最小配置:**
- CPU: 2核心
- 内存: 4GB RAM
- 存储: 20GB SSD
- 网络: 100Mbps

**推荐配置:**
- CPU: 4核心以上
- 内存: 8GB RAM以上
- 存储: 100GB SSD以上
- 网络: 1Gbps

**生产环境:**
- CPU: 8核心以上
- 内存: 16GB RAM以上
- 存储: 500GB SSD以上
- 网络: 10Gbps

### 软件要求

- **操作系统**: Ubuntu 20.04+ / CentOS 8+ / RHEL 8+
- **Node.js**: 18.x LTS
- **PostgreSQL**: 14+
- **Redis**: 6.2+
- **Docker**: 20.10+ (可选)
- **Kubernetes**: 1.24+ (可选)

## 部署架构

### 单机部署

```
┌─────────────────────────────────────┐
│              Load Balancer          │
│            (Nginx/HAProxy)          │
└─────────────────┬───────────────────┘
                  │
┌─────────────────▼───────────────────┐
│           ID Provider API           │
│         (Node.js Application)      │
└─────────────────┬───────────────────┘
                  │
┌─────────────────▼───────────────────┐
│            PostgreSQL               │
│              Redis                  │
└─────────────────────────────────────┘
```

### 高可用部署

```
┌─────────────────────────────────────┐
│              Load Balancer          │
│            (Nginx/HAProxy)          │
└─────────────┬───────────┬───────────┘
              │           │
┌─────────────▼─┐   ┌─────▼─────────────┐
│ ID Provider  │   │  ID Provider      │
│   API (1)    │   │    API (2)        │
└─────────────┬─┘   └─┬─────────────────┘
              │       │
              └───┬───┘
                  │
┌─────────────────▼───────────────────┐
│         PostgreSQL Cluster         │
│           (Primary/Replica)         │
└─────────────────┬───────────────────┘
                  │
┌─────────────────▼───────────────────┐
│           Redis Cluster             │
│         (Master/Sentinel)           │
└─────────────────────────────────────┘
```

## 环境准备

### 1. 系统更新

```bash
# Ubuntu/Debian
sudo apt update && sudo apt upgrade -y

# CentOS/RHEL
sudo yum update -y
```

### 2. 安装依赖

```bash
# 安装 Node.js
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# 安装 PostgreSQL
sudo apt-get install -y postgresql postgresql-contrib

# 安装 Redis
sudo apt-get install -y redis-server

# 安装 Nginx
sudo apt-get install -y nginx

# 安装 PM2 (进程管理器)
sudo npm install -g pm2
```

### 3. 配置防火墙

```bash
# 开放必要端口
sudo ufw allow 22    # SSH
sudo ufw allow 80    # HTTP
sudo ufw allow 443   # HTTPS
sudo ufw allow 3000  # API (内部)
sudo ufw enable
```

## 数据库配置

### PostgreSQL 配置

```bash
# 切换到 postgres 用户
sudo -u postgres psql

# 创建数据库和用户
CREATE DATABASE idprovider;
CREATE USER idprovider_user WITH PASSWORD 'secure_password';
GRANT ALL PRIVILEGES ON DATABASE idprovider TO idprovider_user;
\q
```

### 数据库优化配置

编辑 `/etc/postgresql/14/main/postgresql.conf`:

```ini
# 内存配置
shared_buffers = 256MB
effective_cache_size = 1GB
work_mem = 4MB
maintenance_work_mem = 64MB

# 连接配置
max_connections = 200
listen_addresses = '*'

# 日志配置
log_statement = 'all'
log_duration = on
log_line_prefix = '%t [%p]: [%l-1] user=%u,db=%d,app=%a,client=%h '

# 性能配置
checkpoint_completion_target = 0.9
wal_buffers = 16MB
default_statistics_target = 100
```

### Redis 配置

编辑 `/etc/redis/redis.conf`:

```ini
# 网络配置
bind 127.0.0.1
port 6379
timeout 300

# 内存配置
maxmemory 512mb
maxmemory-policy allkeys-lru

# 持久化配置
save 900 1
save 300 10
save 60 10000

# 安全配置
requirepass your_redis_password

# 日志配置
loglevel notice
logfile /var/log/redis/redis-server.log
```

## 应用部署

### 1. 获取源代码

```bash
# 克隆仓库
git clone https://github.com/your-org/id-provider.git
cd id-provider

# 安装依赖
npm install

# 构建应用
npm run build
```

### 2. 环境配置

创建 `.env.production` 文件:

```env
# 应用配置
NODE_ENV=production
PORT=3000
APP_NAME=ID Provider
APP_VERSION=1.0.0

# 数据库配置
DATABASE_URL=postgresql://idprovider_user:secure_password@localhost:5432/idprovider

# Redis 配置
REDIS_URL=redis://:your_redis_password@localhost:6379

# JWT 配置
JWT_SECRET=your_super_secret_jwt_key_here
JWT_EXPIRES_IN=1h
REFRESH_TOKEN_EXPIRES_IN=7d

# 加密配置
ENCRYPTION_KEY=your_32_character_encryption_key

# 邮件配置
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password

# SMS 配置
SMS_PROVIDER=twilio
TWILIO_ACCOUNT_SID=your_twilio_sid
TWILIO_AUTH_TOKEN=your_twilio_token
TWILIO_PHONE_NUMBER=+**********

# 安全配置
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
CORS_ORIGIN=https://yourdomain.com

# 监控配置
LOG_LEVEL=info
METRICS_ENABLED=true
HEALTH_CHECK_ENABLED=true

# 外部服务
WEBHOOK_SECRET=your_webhook_secret
```

### 3. 数据库迁移

```bash
# 运行数据库迁移
npm run db:migrate

# 生成初始数据
npm run db:seed
```

### 4. 启动应用

使用 PM2 管理应用进程:

```bash
# 创建 PM2 配置文件
cat > ecosystem.config.js << EOF
module.exports = {
  apps: [{
    name: 'id-provider',
    script: 'dist/app.js',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    error_file: './logs/err.log',
    out_file: './logs/out.log',
    log_file: './logs/combined.log',
    time: true,
    max_memory_restart: '1G',
    node_args: '--max-old-space-size=1024'
  }]
};
EOF

# 启动应用
pm2 start ecosystem.config.js

# 设置开机自启
pm2 startup
pm2 save
```

## 负载均衡配置

### Nginx 配置

创建 `/etc/nginx/sites-available/idprovider`:

```nginx
upstream idprovider_backend {
    server 127.0.0.1:3000;
    # 如果有多个实例，添加更多服务器
    # server 127.0.0.1:3001;
    # server 127.0.0.1:3002;
}

server {
    listen 80;
    server_name api.yourdomain.com;
    
    # 重定向到 HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name api.yourdomain.com;
    
    # SSL 证书配置
    ssl_certificate /etc/ssl/certs/yourdomain.crt;
    ssl_certificate_key /etc/ssl/private/yourdomain.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    
    # 安全头部
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    
    # 日志配置
    access_log /var/log/nginx/idprovider_access.log;
    error_log /var/log/nginx/idprovider_error.log;
    
    # 代理配置
    location / {
        proxy_pass http://idprovider_backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # 超时配置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
    
    # 健康检查
    location /health {
        proxy_pass http://idprovider_backend/health;
        access_log off;
    }
    
    # 静态文件缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

启用站点:

```bash
sudo ln -s /etc/nginx/sites-available/idprovider /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

## SSL 证书配置

### 使用 Let's Encrypt

```bash
# 安装 Certbot
sudo apt install certbot python3-certbot-nginx

# 获取证书
sudo certbot --nginx -d api.yourdomain.com

# 设置自动续期
sudo crontab -e
# 添加以下行
0 12 * * * /usr/bin/certbot renew --quiet
```

## 监控和日志

### 1. 日志配置

创建日志目录:

```bash
sudo mkdir -p /var/log/idprovider
sudo chown -R $USER:$USER /var/log/idprovider
```

### 2. 系统监控

安装监控工具:

```bash
# 安装 htop
sudo apt install htop

# 安装 iotop
sudo apt install iotop

# 安装 netstat
sudo apt install net-tools
```

### 3. 应用监控

使用 PM2 监控:

```bash
# 查看应用状态
pm2 status

# 查看日志
pm2 logs

# 查看监控面板
pm2 monit

# 重启应用
pm2 restart id-provider

# 重新加载应用（零停机）
pm2 reload id-provider
```

## 备份策略

### 数据库备份

创建备份脚本 `/opt/scripts/backup_db.sh`:

```bash
#!/bin/bash

BACKUP_DIR="/opt/backups/postgresql"
DATE=$(date +%Y%m%d_%H%M%S)
DB_NAME="idprovider"
DB_USER="idprovider_user"

# 创建备份目录
mkdir -p $BACKUP_DIR

# 执行备份
pg_dump -U $DB_USER -h localhost $DB_NAME | gzip > $BACKUP_DIR/idprovider_$DATE.sql.gz

# 删除7天前的备份
find $BACKUP_DIR -name "idprovider_*.sql.gz" -mtime +7 -delete

echo "数据库备份完成: idprovider_$DATE.sql.gz"
```

设置定时备份:

```bash
sudo chmod +x /opt/scripts/backup_db.sh
sudo crontab -e
# 添加每日凌晨2点备份
0 2 * * * /opt/scripts/backup_db.sh
```

### Redis 备份

```bash
# 手动备份
redis-cli --rdb /opt/backups/redis/dump_$(date +%Y%m%d_%H%M%S).rdb

# 自动备份脚本
cat > /opt/scripts/backup_redis.sh << 'EOF'
#!/bin/bash
BACKUP_DIR="/opt/backups/redis"
DATE=$(date +%Y%m%d_%H%M%S)

mkdir -p $BACKUP_DIR
redis-cli --rdb $BACKUP_DIR/dump_$DATE.rdb
find $BACKUP_DIR -name "dump_*.rdb" -mtime +7 -delete
EOF

chmod +x /opt/scripts/backup_redis.sh
```

## 安全加固

### 1. 系统安全

```bash
# 禁用 root 登录
sudo sed -i 's/PermitRootLogin yes/PermitRootLogin no/' /etc/ssh/sshd_config

# 更改 SSH 端口
sudo sed -i 's/#Port 22/Port 2222/' /etc/ssh/sshd_config

# 重启 SSH 服务
sudo systemctl restart ssh

# 安装 fail2ban
sudo apt install fail2ban

# 配置 fail2ban
sudo cp /etc/fail2ban/jail.conf /etc/fail2ban/jail.local
```

### 2. 应用安全

```bash
# 设置文件权限
chmod 600 .env.production
chown $USER:$USER .env.production

# 限制日志文件权限
chmod 640 /var/log/idprovider/*
```

## 性能优化

### 1. 数据库优化

```sql
-- 创建索引
CREATE INDEX CONCURRENTLY idx_users_email ON users(email);
CREATE INDEX CONCURRENTLY idx_analytics_events_timestamp ON analytics_events(timestamp);
CREATE INDEX CONCURRENTLY idx_analytics_events_user_id ON analytics_events(user_id);

-- 分析表统计信息
ANALYZE users;
ANALYZE analytics_events;
ANALYZE security_events;
```

### 2. Redis 优化

```bash
# 设置内存过载策略
redis-cli CONFIG SET maxmemory-policy allkeys-lru

# 启用压缩
redis-cli CONFIG SET rdbcompression yes
```

### 3. 应用优化

```javascript
// 在 ecosystem.config.js 中优化 PM2 配置
module.exports = {
  apps: [{
    name: 'id-provider',
    script: 'dist/app.js',
    instances: 'max',
    exec_mode: 'cluster',
    max_memory_restart: '1G',
    node_args: '--max-old-space-size=1024 --optimize-for-size',
    env: {
      NODE_ENV: 'production',
      UV_THREADPOOL_SIZE: 128
    }
  }]
};
```

## 故障排除

### 常见问题

1. **应用无法启动**
   ```bash
   # 检查日志
   pm2 logs id-provider
   
   # 检查端口占用
   netstat -tulpn | grep :3000
   ```

2. **数据库连接失败**
   ```bash
   # 检查 PostgreSQL 状态
   sudo systemctl status postgresql
   
   # 测试连接
   psql -U idprovider_user -h localhost -d idprovider
   ```

3. **Redis 连接失败**
   ```bash
   # 检查 Redis 状态
   sudo systemctl status redis
   
   # 测试连接
   redis-cli ping
   ```

### 性能问题诊断

```bash
# 查看系统资源使用
htop
iotop
df -h

# 查看应用性能
pm2 monit

# 查看数据库性能
sudo -u postgres psql -c "SELECT * FROM pg_stat_activity;"
```

## 更新和维护

### 应用更新

```bash
# 备份当前版本
cp -r /path/to/id-provider /path/to/id-provider.backup

# 拉取最新代码
git pull origin main

# 安装依赖
npm install

# 构建应用
npm run build

# 运行数据库迁移
npm run db:migrate

# 重新加载应用（零停机更新）
pm2 reload id-provider
```

### 系统维护

```bash
# 定期清理日志
sudo logrotate -f /etc/logrotate.conf

# 更新系统包
sudo apt update && sudo apt upgrade

# 清理包缓存
sudo apt autoremove && sudo apt autoclean
```

这个部署指南涵盖了从环境准备到生产部署的完整流程，包括安全配置、监控、备份和故障排除等关键方面。
