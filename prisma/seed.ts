import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcrypt';

const prisma = new PrismaClient();

/**
 * 数据库种子数据初始化
 * 创建默认的系统管理员、角色和系统配置
 */
async function main(): Promise<void> {
  console.log('开始初始化数据库种子数据...');

  // 创建默认角色
  const adminRole = await prisma.role.upsert({
    where: { name: 'admin' },
    update: {},
    create: {
      name: 'admin',
      description: '系统管理员',
      permissions: [
        'user:read',
        'user:write',
        'user:delete',
        'app:read',
        'app:write',
        'app:delete',
        'config:read',
        'config:write',
        'audit:read'
      ]
    }
  });

  const userRole = await prisma.role.upsert({
    where: { name: 'user' },
    update: {},
    create: {
      name: 'user',
      description: '普通用户',
      permissions: [
        'profile:read',
        'profile:write',
        'mfa:manage',
        'session:manage'
      ]
    }
  });

  console.log('✅ 默认角色创建完成');

  // 创建默认系统管理员
  const adminPasswordHash = await bcrypt.hash('admin123456', 12);
  
  const adminUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      username: 'admin',
      passwordHash: adminPasswordHash,
      nickname: '系统管理员',
      emailVerified: true,
      isActive: true
    }
  });

  // 分配管理员角色
  await prisma.userRole.upsert({
    where: {
      userId_roleId: {
        userId: adminUser.id,
        roleId: adminRole.id
      }
    },
    update: {},
    create: {
      userId: adminUser.id,
      roleId: adminRole.id
    }
  });

  console.log('✅ 默认管理员账户创建完成');

  // 创建默认测试应用
  const testApp = await prisma.application.upsert({
    where: { clientId: 'test-app-client-id' },
    update: {},
    create: {
      name: '测试应用',
      description: '用于开发和测试的示例应用',
      clientId: 'test-app-client-id',
      clientSecret: 'test-app-client-secret',
      redirectUris: [
        'http://localhost:3001/callback',
        'http://localhost:3001/auth/callback'
      ],
      allowedOrigins: [
        'http://localhost:3001',
        'http://localhost:3000'
      ],
      supportedProtocols: ['oidc', 'oauth2'],
      requireMfa: false,
      zeroTrustEnabled: false,
      isActive: true
    }
  });

  console.log('✅ 默认测试应用创建完成');

  // 创建系统配置
  const systemConfigs = [
    {
      key: 'password_policy',
      value: {
        minLength: 8,
        requireUppercase: true,
        requireLowercase: true,
        requireNumbers: true,
        requireSpecialChars: true,
        maxAge: 90, // 密码最大有效期（天）
        historyCount: 5 // 记住最近几次密码
      },
      description: '密码策略配置'
    },
    {
      key: 'mfa_settings',
      value: {
        totpIssuer: 'IdP',
        totpDigits: 6,
        totpPeriod: 30,
        backupCodesCount: 10,
        emailCodeExpiry: 300, // 5分钟
        smsCodeExpiry: 300 // 5分钟
      },
      description: 'MFA设置配置'
    },
    {
      key: 'zero_trust_config',
      value: {
        enabled: false,
        riskThresholds: {
          low: 30,
          medium: 60,
          high: 80
        },
        factors: {
          ipRisk: { weight: 0.2, enabled: true },
          deviceRisk: { weight: 0.3, enabled: true },
          behaviorRisk: { weight: 0.3, enabled: true },
          locationRisk: { weight: 0.1, enabled: true },
          timeRisk: { weight: 0.1, enabled: true }
        }
      },
      description: '零信任模式配置'
    },
    {
      key: 'session_config',
      value: {
        defaultTimeout: 30, // 默认会话超时（分钟）
        rememberMeDays: 30, // 记住我功能天数
        maxConcurrentSessions: 5, // 最大并发会话数
        extendOnActivity: true // 活动时延长会话
      },
      description: '会话管理配置'
    },
    {
      key: 'email_templates',
      value: {
        verification: {
          subject: '验证您的邮箱地址',
          template: 'verification_email'
        },
        passwordReset: {
          subject: '重置您的密码',
          template: 'password_reset_email'
        },
        mfaCode: {
          subject: '您的验证码',
          template: 'mfa_code_email'
        }
      },
      description: '邮件模板配置'
    }
  ];

  for (const config of systemConfigs) {
    await prisma.systemConfig.upsert({
      where: { key: config.key },
      update: { value: config.value },
      create: config
    });
  }

  console.log('✅ 系统配置创建完成');

  console.log('🎉 数据库种子数据初始化完成！');
  console.log('默认管理员账户: <EMAIL> / admin123456');
}

main()
  .catch((e) => {
    console.error('❌ 种子数据初始化失败:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
