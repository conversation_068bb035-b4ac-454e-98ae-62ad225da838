// Prisma schema file for IdP (Identity Provider)
// 身份提供商数据库模式定义

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

// 用户表 - 核心用户信息
model User {
  id                String    @id @default(uuid())
  email             String    @unique
  phone             String?   @unique
  username          String?   @unique
  passwordHash      String?   // 可为空，支持仅第三方登录的用户
  nickname          String?
  firstName         String?
  lastName          String?
  avatar            String?
  emailVerified     Boolean   @default(false)
  phoneVerified     Boolean   @default(false)
  isActive          Boolean   @default(true)
  isLocked          Boolean   @default(false)
  lockReason        String?
  lastLoginAt       DateTime?
  lastLoginIp       String?
  passwordChangedAt DateTime?
  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt

  // 关联关系
  sessions          Session[]
  mfaDevices        MFADevice[]
  federatedIdentities FederatedIdentity[]
  auditLogs         AuditLog[]
  riskAssessments   RiskAssessment[]
  userRoles         UserRole[]
  refreshTokens     RefreshToken[]
  authorizationCodes AuthorizationCode[]
  passwordResetTokens PasswordResetToken[]
  emailVerificationTokens EmailVerificationToken[]
  permissionUsageLogs PermissionUsageLog[]
  delegatedPermissions PermissionDelegation[] @relation("DelegatorRelation")
  receivedDelegations PermissionDelegation[] @relation("DelegateeRelation")
  configChanges     ConfigChangeHistory[]

  // 组织架构相关关联
  organizationMemberships OrganizationMember[]
  permissionRequests PermissionRequest[] @relation("PermissionRequester")
  targetedRequests  PermissionRequest[] @relation("PermissionTarget")
  approvedRequests  PermissionRequest[] @relation("PermissionApprover")
  organizationAccessLogs OrganizationAccessLog[]

  // 联邦架构相关关联
  federatedPermissionCache FederatedPermissionCache[]

  @@map("users")
}

// 应用表 - 注册的服务提供商
model Application {
  id                String    @id @default(uuid())
  name              String
  description       String?
  clientId          String    @unique
  clientSecret      String
  redirectUris      Json      // JSON数组存储多个回调URL
  allowedOrigins    Json      // 允许的CORS源
  logoUrl           String?
  homepageUrl       String?
  privacyPolicyUrl  String?
  termsOfServiceUrl String?

  // SSO协议配置
  supportedProtocols Json     // ["oidc", "oauth2", "saml", "custom-oauth", "api-key", "webhook"]
  oidcConfig        Json?     // OIDC特定配置
  samlConfig        Json?     // SAML特定配置
  customProtocolConfig Json?  // 自定义协议配置

  // 非标准应用支持
  appType           String    @default("standard") // "standard", "legacy", "custom", "api_only", "webhook", "mobile", "iot"
  customAuthFlow    Json?     // 自定义认证流程配置

  // 钩子函数配置
  preAuthHook       String?   // 预认证钩子函数
  postAuthHook      String?   // 后认证钩子函数
  tokenTransformHook String?  // 令牌转换钩子函数
  userTransformHook String?   // 用户信息转换钩子函数

  // Webhook配置
  webhookUrls       Json?     // Webhook URL配置 {"onSuccess": "url", "onError": "url", "onLogout": "url"}
  webhookSecret     String?   // Webhook签名密钥
  webhookTimeout    Int?      @default(30) // Webhook超时时间（秒）

  // API配置
  apiKeyEnabled     Boolean   @default(false)
  apiKeyConfig      Json?     // API密钥配置
  rateLimitConfig   Json?     // 速率限制配置

  // 安全配置
  requireMfa        Boolean   @default(false)
  zeroTrustEnabled  Boolean   @default(false)
  allowedIpRanges   Json?     // 允许的IP范围
  tokenLifetime     Int?      @default(3600) // 令牌生命周期（秒）

  // 扩展配置
  customSettings    Json?     // 自定义设置
  pluginConfig      Json?     // 插件配置

  isActive          Boolean   @default(true)
  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt

  // 关联关系
  sessions          Session[]
  auditLogs         AuditLog[]
  protocolConfigs   ApplicationProtocolConfig[]
  apiKeys           ApiKey[]
  oauthClients      OAuthClient[]
  applicationPermissions ApplicationPermission[]
  permissionUsageLogs PermissionUsageLog[]
  permissionDelegations PermissionDelegation[]

  // 联邦架构相关关联
  organizationMappings OrganizationMapping[]
  orgRegistry       ApplicationOrgRegistry?
  mappingConflicts  MappingConflict[]
  federationSyncLogs FederationSyncLog[]
  federatedPermissionCache FederatedPermissionCache[]

  @@map("applications")
}

// 会话表 - 用户登录会话
model Session {
  id              String    @id @default(uuid())
  userId          String
  applicationId   String?   // 可为空，表示IdP自身的会话
  sessionToken    String    @unique
  deviceId        String?   // 设备标识
  deviceInfo      Json?     // 设备信息
  ipAddress       String
  userAgent       String?
  location        Json?     // 地理位置信息
  
  // 会话状态
  isActive        Boolean   @default(true)
  expiresAt       DateTime
  lastAccessedAt  DateTime  @default(now())
  
  // 认证信息
  authMethod      String    // "password", "mfa", "federated"
  mfaVerified     Boolean   @default(false)
  riskScore       Int?      // 风险评分 0-100
  
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt
  
  // 关联关系
  user            User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  application     Application? @relation(fields: [applicationId], references: [id], onDelete: Cascade)
  
  @@map("sessions")
}

// MFA设备表 - 多因素认证设备
model MFADevice {
  id            String    @id @default(uuid())
  userId        String
  type          String    // "totp", "email", "sms"
  name          String    // 用户自定义设备名称
  secret        String?   // TOTP密钥（加密存储）
  backupCodes   Json?     // 备用恢复码（加密存储）
  phoneNumber   String?   // 短信MFA的手机号
  emailAddress  String?   // 邮件MFA的邮箱
  
  isActive      Boolean   @default(true)
  isVerified    Boolean   @default(false)
  lastUsedAt    DateTime?
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
  
  // 关联关系
  user          User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  @@map("mfa_devices")
}

// 联合身份表 - 第三方账户关联
model FederatedIdentity {
  id            String    @id @default(uuid())
  userId        String
  provider      String    // "google", "github", "microsoft"
  providerId    String    // 第三方平台的用户ID
  email         String?   // 第三方账户邮箱
  name          String?   // 第三方账户名称
  avatar        String?   // 第三方账户头像
  accessToken   String?   // 访问令牌（加密存储）
  refreshToken  String?   // 刷新令牌（加密存储）
  expiresAt     DateTime?
  
  isActive      Boolean   @default(true)
  lastUsedAt    DateTime?
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
  
  // 关联关系
  user          User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  @@unique([provider, providerId])
  @@map("federated_identities")
}

// 审计日志表 - 安全和操作日志
model AuditLog {
  id            String    @id @default(uuid())
  userId        String?   // 可为空，系统操作
  applicationId String?   // 可为空，IdP自身操作
  action        String    // 操作类型
  resource      String    // 操作资源
  details       Json?     // 详细信息
  ipAddress     String?
  userAgent     String?
  success       Boolean
  errorMessage  String?
  
  createdAt     DateTime  @default(now())
  
  // 关联关系
  user          User?     @relation(fields: [userId], references: [id], onDelete: SetNull)
  application   Application? @relation(fields: [applicationId], references: [id], onDelete: SetNull)
  
  @@map("audit_logs")
}

// 风险评估表 - 零信任模式风险评估
model RiskAssessment {
  id            String    @id @default(uuid())
  userId        String
  sessionId     String?
  
  // 风险因子
  ipRisk        Int       @default(0)    // IP风险评分
  deviceRisk    Int       @default(0)    // 设备风险评分
  behaviorRisk  Int       @default(0)    // 行为风险评分
  locationRisk  Int       @default(0)    // 位置风险评分
  timeRisk      Int       @default(0)    // 时间风险评分
  
  totalRisk     Int       @default(0)    // 总风险评分
  riskLevel     String    // "low", "medium", "high", "critical"
  
  // 评估结果
  action        String    // "allow", "mfa_required", "deny"
  reason        String?   // 风险原因
  
  createdAt     DateTime  @default(now())
  
  // 关联关系
  user          User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  @@map("risk_assessments")
}

// 权限定义表 - 标准化权限元数据
model Permission {
  id                  String    @id @default(uuid())
  name                String
  description         String?
  category            String
  resourceType        String    @map("resource_type")
  operations          Json      // 支持的操作数组
  level               Int       @default(1) // 权限级别 1-10
  dependencies        Json?     // 权限依赖关系
  constraints         Json?     // 权限约束条件
  tags                Json?     // 权限标签
  isSensitive         Boolean   @default(false) @map("is_sensitive")
  isDelegatable       Boolean   @default(true) @map("is_delegatable")
  validity            Json?     // 权限有效期配置

  // 元数据字段
  createdBy           String    @map("created_by")
  createdAt           DateTime  @default(now()) @map("created_at")
  updatedBy           String    @map("updated_by")
  updatedAt           DateTime  @updatedAt @map("updated_at")
  version             String    @default("1.0.0")
  sourceApplication   String?   @map("source_application")
  documentationUrl    String?   @map("documentation_url")
  owner               String?
  approvalStatus      String    @default("pending") @map("approval_status") // pending, approved, rejected, deprecated

  // 关联关系
  applicationPermissions ApplicationPermission[]
  usageLogs           PermissionUsageLog[]
  changeRecords       PermissionChangeRecord[]
  organizationPermissions OrganizationPermission[]

  @@map("permissions")
}

// 应用权限关联表
model ApplicationPermission {
  id                  String    @id @default(uuid())
  applicationId       String    @map("application_id")
  permissionId        String    @map("permission_id")
  isRequired          Boolean   @default(false) @map("is_required")
  grantedAt           DateTime? @map("granted_at")
  expiresAt           DateTime? @map("expires_at")
  grantedBy           String?   @map("granted_by")
  revokedAt           DateTime? @map("revoked_at")
  revokedBy           String?   @map("revoked_by")
  revokeReason        String?   @map("revoke_reason")

  // 权限配置
  customConstraints   Json?     @map("custom_constraints")
  delegationAllowed   Boolean   @default(true) @map("delegation_allowed")
  maxDelegationDepth  Int       @default(3) @map("max_delegation_depth")

  // 元数据
  createdAt           DateTime  @default(now()) @map("created_at")
  updatedAt           DateTime  @updatedAt @map("updated_at")

  // 关联关系
  application         Application @relation(fields: [applicationId], references: [id], onDelete: Cascade)
  permission          Permission  @relation(fields: [permissionId], references: [id], onDelete: Cascade)

  @@unique([applicationId, permissionId], name: "unique_app_permission")
  @@map("application_permissions")
}

// 权限使用日志表
model PermissionUsageLog {
  id                  String    @id @default(uuid())
  userId              String    @map("user_id")
  applicationId       String?   @map("application_id")
  permissionId        String    @map("permission_id")
  resourceId          String?   @map("resource_id")
  resourceType        String?   @map("resource_type")
  action              String
  result              String    // granted, denied, error

  // 请求信息
  ipAddress           String?   @map("ip_address")
  userAgent           String?   @map("user_agent")
  deviceType          String?   @map("device_type")
  location            Json?
  sessionId           String?   @map("session_id")

  // 验证详情
  validationTimeMs    Int?      @map("validation_time_ms")
  failedConstraints   Json?     @map("failed_constraints")
  missingDependencies Json?     @map("missing_dependencies")

  // 额外信息
  requestDetails      Json?     @map("request_details")
  responseDetails     Json?     @map("response_details")

  // 时间戳
  createdAt           DateTime  @default(now()) @map("created_at")

  // 关联关系
  user                User        @relation(fields: [userId], references: [id], onDelete: Cascade)
  application         Application? @relation(fields: [applicationId], references: [id], onDelete: SetNull)
  permission          Permission  @relation(fields: [permissionId], references: [id], onDelete: Cascade)

  @@map("permission_usage_logs")
}

// 权限变更记录表
model PermissionChangeRecord {
  id                  String    @id @default(uuid())
  permissionId        String    @map("permission_id")
  changeType          String    @map("change_type") // created, updated, deleted, activated, deactivated
  previousValue       Json?     @map("previous_value")
  newValue            Json?     @map("new_value")
  reason              String

  // 变更者信息
  changedBy           String    @map("changed_by")
  changedAt           DateTime  @default(now()) @map("changed_at")

  // 审批信息
  approvalRequired    Boolean   @default(false) @map("approval_required")
  approvalStatus      String    @default("approved") @map("approval_status") // pending, approved, rejected
  approvedBy          String?   @map("approved_by")
  approvedAt          DateTime? @map("approved_at")
  approvalComments    String?   @map("approval_comments")

  // 关联关系
  permission          Permission @relation(fields: [permissionId], references: [id], onDelete: Cascade)

  @@map("permission_change_records")
}

// 权限模板表
model PermissionTemplate {
  id                  String    @id @default(uuid())
  name                String
  description         String?
  category            String
  permissions         Json      // 权限ID数组
  variables           Json?     // 模板变量
  isSystemTemplate    Boolean   @default(false) @map("is_system_template")

  // 元数据
  createdBy           String    @map("created_by")
  createdAt           DateTime  @default(now()) @map("created_at")
  updatedAt           DateTime  @updatedAt @map("updated_at")

  @@map("permission_templates")
}

// 注意：权限委托表已移至组织架构部分，支持更完整的组织级权限委托

// 角色表 - 用户角色管理
model Role {
  id            String    @id @default(uuid())
  name          String    @unique
  description   String?
  permissions   Json?     // 权限列表
  
  isActive      Boolean   @default(true)
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
  
  // 关联关系
  userRoles     UserRole[]
  
  @@map("roles")
}

// 用户角色关联表
model UserRole {
  id        String    @id @default(uuid())
  userId    String
  roleId    String
  
  createdAt DateTime  @default(now())
  
  // 关联关系
  user      User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  role      Role      @relation(fields: [roleId], references: [id], onDelete: Cascade)
  
  @@unique([userId, roleId])
  @@map("user_roles")
}

// 刷新令牌表 - JWT刷新令牌管理
model RefreshToken {
  id            String    @id @default(uuid())
  userId        String
  token         String    @unique
  deviceId      String?
  
  isActive      Boolean   @default(true)
  expiresAt     DateTime
  createdAt     DateTime  @default(now())
  
  // 关联关系
  user          User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  @@map("refresh_tokens")
}

// 应用协议配置表 - 应用特定的协议配置
model ApplicationProtocolConfig {
  id              String    @id @default(uuid())
  applicationId   String
  protocolName    String    // "oidc", "saml", "custom-oauth", etc.
  protocolVersion String    @default("1.0")
  config          Json      // 协议特定配置
  isActive        Boolean   @default(true)

  // 自定义处理器
  customHandlers  Json?     // {"preAuth": "handler_name", "postAuth": "handler_name"}

  // Webhook配置
  webhooks        Json?     // {"onSuccess": "url", "onError": "url"}

  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt

  // 关联关系
  application     Application @relation(fields: [applicationId], references: [id], onDelete: Cascade)

  @@unique([applicationId, protocolName])
  @@map("application_protocol_configs")
}

// 插件表 - 管理认证插件
model Plugin {
  id            String    @id @default(uuid())
  name          String    @unique
  version       String
  description   String?
  author        String?

  // 插件文件信息
  filePath      String    // 插件文件路径
  entryPoint    String    // 入口点函数
  checksum      String    // 文件校验和

  // 插件配置
  config        Json?     // 插件配置
  dependencies  Json?     // 依赖的其他插件

  // 插件状态
  isEnabled     Boolean   @default(false)
  isLoaded      Boolean   @default(false)
  loadError     String?   // 加载错误信息

  // 插件提供的功能
  protocolAdapters Json?    // 提供的协议适配器
  customHandlers   Json?    // 提供的自定义处理器

  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  @@map("plugins")
}

// 自定义认证流程表
model CustomAuthFlow {
  id            String    @id @default(uuid())
  name          String    @unique
  description   String?

  // 流程定义
  steps         Json      // 认证流程步骤定义
  config        Json?     // 流程配置

  // 使用统计
  usageCount    Int       @default(0)
  lastUsedAt    DateTime?

  isActive      Boolean   @default(true)
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  @@map("custom_auth_flows")
}

// API密钥表 - 管理应用API密钥
model ApiKey {
  id            String    @id @default(uuid())
  applicationId String
  name          String    // 密钥名称
  keyHash       String    @unique // 密钥哈希值
  keyPrefix     String    // 密钥前缀（用于识别）

  // 权限配置
  scopes        Json?     // 权限范围
  allowedIps    Json?     // 允许的IP地址

  // 使用统计
  usageCount    Int       @default(0)
  lastUsedAt    DateTime?

  // 状态
  isActive      Boolean   @default(true)
  expiresAt     DateTime?

  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  // 关联关系
  application   Application @relation(fields: [applicationId], references: [id], onDelete: Cascade)

  @@map("api_keys")
}

// 系统配置表 - 全局配置
model SystemConfig {
  id            String    @id @default(uuid())
  key           String    @unique
  value         Json
  description   String?

  updatedAt     DateTime  @updatedAt

  @@map("system_configs")
}

// OAuth客户端表 - OIDC客户端注册
model OAuthClient {
  id                    String    @id @default(uuid())
  applicationId         String?   // 关联的应用ID（可为空，支持独立OAuth客户端）
  clientId              String    @unique
  clientSecret          String
  name                  String
  description           String?

  // 重定向URI和权限配置
  redirectUris          Json      // 允许的重定向URI数组
  grantTypes            Json      // 支持的授权类型 ["authorization_code", "refresh_token", "client_credentials"]
  responseTypes         Json      // 支持的响应类型 ["code", "token", "id_token"]
  scopes                Json      // 允许的权限范围 ["openid", "profile", "email", "offline_access"]

  // 客户端配置
  tokenEndpointAuthMethod String  @default("client_secret_basic") // "client_secret_basic", "client_secret_post", "private_key_jwt"
  requirePkce           Boolean   @default(false)
  requireConsent        Boolean   @default(true)

  // 令牌配置
  accessTokenLifetime   Int       @default(3600)    // 访问令牌生命周期（秒）
  refreshTokenLifetime  Int       @default(2592000) // 刷新令牌生命周期（秒，默认30天）
  idTokenLifetime       Int       @default(3600)    // ID令牌生命周期（秒）

  // 客户端元数据
  logoUri               String?
  clientUri             String?
  policyUri             String?
  tosUri                String?

  isActive              Boolean   @default(true)
  createdAt             DateTime  @default(now())
  updatedAt             DateTime  @updatedAt

  // 关联关系
  application           Application? @relation(fields: [applicationId], references: [id], onDelete: Cascade)
  authorizationCodes    AuthorizationCode[]

  @@map("oauth_clients")
}

// 授权码表 - OAuth授权码存储
model AuthorizationCode {
  id                    String    @id @default(uuid())
  code                  String    @unique
  clientId              String
  userId                String
  redirectUri           String
  scopes                String    // 空格分隔的权限范围

  // PKCE支持
  codeChallenge         String?
  codeChallengeMethod   String?   // "plain" or "S256"

  // OpenID Connect参数
  nonce                 String?   // 用于ID令牌

  // 状态和过期
  used                  Boolean   @default(false)
  expiresAt             DateTime
  createdAt             DateTime  @default(now())

  // 关联关系
  client                OAuthClient @relation(fields: [clientId], references: [clientId], onDelete: Cascade)
  user                  User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("authorization_codes")
}

// 密码重置令牌表
model PasswordResetToken {
  id            String    @id @default(uuid())
  token         String    @unique
  userId        String
  used          Boolean   @default(false)
  expiresAt     DateTime
  createdAt     DateTime  @default(now())

  // 关联关系
  user          User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("password_reset_tokens")
}

// 邮箱验证令牌表
model EmailVerificationToken {
  id            String    @id @default(uuid())
  token         String    @unique
  userId        String
  email         String    // 要验证的邮箱
  used          Boolean   @default(false)
  expiresAt     DateTime
  createdAt     DateTime  @default(now())

  // 关联关系
  user          User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("email_verification_tokens")
}

// 协议适配器表 - 非标准应用支持
model ProtocolAdapter {
  id            String    @id @default(uuid())
  name          String    // 适配器名称
  version       String    // 适配器版本
  config        Json      // 适配器配置
  isActive      Boolean   @default(true)
  usageCount    Int       @default(0)

  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
  lastUsed      DateTime?

  @@map("protocol_adapters")
}

// 系统配置表 - 动态配置管理
model SystemConfig {
  id              String   @id @default(uuid())
  key             String   @unique
  value           String
  type            String   @default("string") // string, number, boolean, json, array
  category        String
  description     String
  isPublic        Boolean  @default(false)
  isRequired      Boolean  @default(false)
  defaultValue    String?
  validationRules Json     @default("{}")
  metadata        Json     @default("{}")
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  @@index([category])
  @@index([isPublic])
  @@map("system_configs")
}

// 配置变更历史表
model ConfigChangeHistory {
  id           String   @id @default(uuid())
  configKey    String   @map("config_key")
  oldValue     String?  @map("old_value")
  newValue     String?  @map("new_value")
  changedBy    String   @map("changed_by")
  changeReason String?  @map("change_reason")
  createdAt    DateTime @default(now()) @map("created_at")

  user User @relation(fields: [changedBy], references: [id])

  @@index([configKey])
  @@index([changedBy])
  @@index([createdAt])
  @@map("config_change_history")
}

// 分析事件表 - 用户行为和系统事件数据收集
model AnalyticsEvent {
  id          String   @id @default(uuid())
  type        String   // 事件类型：user_login, auth_failure, api_request等
  timestamp   DateTime @default(now())
  userId      String?  @map("user_id")
  sessionId   String?  @map("session_id")

  // 请求信息
  ip          String
  userAgent   String   @map("user_agent")
  referer     String?

  // 地理位置信息
  country     String?
  region      String?
  city        String?

  // 设备信息
  deviceType  String?  @map("device_type") // desktop, mobile, tablet
  browser     String?
  os          String?

  // 事件数据（JSON格式存储具体事件信息）
  data        Json     @default("{}")

  // 元数据
  metadata    Json     @default("{}")

  // 索引优化
  @@index([type])
  @@index([userId])
  @@index([timestamp])
  @@index([ip])
  @@index([type, timestamp])
  @@index([userId, timestamp])
  @@map("analytics_events")
}

// 用户行为模式表 - 存储分析后的用户行为模式
model UserBehaviorPattern {
  id              String   @id @default(uuid())
  userId          String   @map("user_id")
  patternType     String   @map("pattern_type") // login_pattern, usage_pattern, risk_pattern

  // 模式数据
  pattern         Json     // 存储具体的行为模式数据
  confidence      Float    // 模式置信度 0-1
  frequency       Int      @default(0) // 模式出现频率

  // 时间范围
  startDate       DateTime @map("start_date")
  endDate         DateTime @map("end_date")

  // 状态
  isActive        Boolean  @default(true) @map("is_active")
  lastUpdated     DateTime @default(now()) @map("last_updated")

  createdAt       DateTime @default(now()) @map("created_at")
  updatedAt       DateTime @updatedAt @map("updated_at")

  @@index([userId])
  @@index([patternType])
  @@index([userId, patternType])
  @@index([lastUpdated])
  @@map("user_behavior_patterns")
}

// 安全事件表 - 专门存储安全相关事件
model SecurityEvent {
  id            String   @id @default(uuid())
  type          String   // suspicious_login, brute_force, unauthorized_access等
  severity      String   // low, medium, high, critical
  status        String   @default("open") // open, investigating, resolved, false_positive

  // 关联信息
  userId        String?  @map("user_id")
  ip            String
  userAgent     String?  @map("user_agent")

  // 事件详情
  description   String
  details       Json     @default("{}")

  // 响应信息
  assignedTo    String?  @map("assigned_to")
  resolvedBy    String?  @map("resolved_by")
  resolvedAt    DateTime? @map("resolved_at")
  resolution    String?

  createdAt     DateTime @default(now()) @map("created_at")
  updatedAt     DateTime @updatedAt @map("updated_at")

  @@index([type])
  @@index([severity])
  @@index([status])
  @@index([userId])
  @@index([ip])
  @@index([createdAt])
  @@map("security_events")
}

// 系统性能指标表 - 存储系统性能数据
model PerformanceMetric {
  id            String   @id @default(uuid())
  metricType    String   @map("metric_type") // response_time, cpu_usage, memory_usage, db_query_time
  value         Float    // 指标值
  unit          String   // 单位：ms, %, MB, etc.

  // 上下文信息
  endpoint      String?  // API端点
  method        String?  // HTTP方法
  statusCode    Int?     @map("status_code")

  // 时间信息
  timestamp     DateTime @default(now())
  duration      Int?     // 持续时间（毫秒）

  // 额外数据
  metadata      Json     @default("{}")

  @@index([metricType])
  @@index([timestamp])
  @@index([endpoint])
  @@index([metricType, timestamp])
  @@map("performance_metrics")
}

// 用户会话分析表 - 存储会话级别的分析数据
model SessionAnalytics {
  id              String   @id @default(uuid())
  sessionId       String   @unique @map("session_id")
  userId          String?  @map("user_id")

  // 会话基本信息
  startTime       DateTime @map("start_time")
  endTime         DateTime? @map("end_time")
  duration        Int?     // 会话持续时间（秒）

  // 活动统计
  pageViews       Int      @default(0) @map("page_views")
  apiCalls        Int      @default(0) @map("api_calls")
  errorCount      Int      @default(0) @map("error_count")

  // 设备和位置信息
  ip              String
  userAgent       String   @map("user_agent")
  country         String?
  city            String?
  deviceType      String?  @map("device_type")

  // 行为分析
  bounceRate      Float?   @map("bounce_rate") // 跳出率
  engagementScore Float?   @map("engagement_score") // 参与度评分
  riskScore       Float?   @map("risk_score") // 风险评分

  // 转换事件
  conversions     Json     @default("[]") // 转换事件列表

  createdAt       DateTime @default(now()) @map("created_at")
  updatedAt       DateTime @updatedAt @map("updated_at")

  @@index([userId])
  @@index([startTime])
  @@index([ip])
  @@index([riskScore])
  @@map("session_analytics")
}

// 报告模板表 - 存储分析报告模板
model ReportTemplate {
  id            String   @id @default(uuid())
  name          String   // 报告模板名称
  description   String?  // 描述
  type          String   // daily, weekly, monthly, custom

  // 报告配置
  config        Json     // 报告配置：数据源、图表类型、过滤条件等
  schedule      Json?    // 定时配置：cron表达式等

  // 权限和状态
  isActive      Boolean  @default(true) @map("is_active")
  isPublic      Boolean  @default(false) @map("is_public")
  createdBy     String   @map("created_by")

  // 使用统计
  usageCount    Int      @default(0) @map("usage_count")
  lastUsed      DateTime? @map("last_used")

  createdAt     DateTime @default(now()) @map("created_at")
  updatedAt     DateTime @updatedAt @map("updated_at")

  @@index([type])
  @@index([createdBy])
  @@index([isActive])
  @@map("report_templates")
}

// 生成的报告表 - 存储已生成的报告
model GeneratedReport {
  id            String   @id @default(uuid())
  templateId    String?  @map("template_id")
  name          String   // 报告名称
  type          String   // 报告类型

  // 报告内容
  data          Json     // 报告数据
  format        String   @default("json") // json, pdf, excel, csv
  filePath      String?  @map("file_path") // 文件存储路径
  fileSize      Int?     @map("file_size") // 文件大小（字节）

  // 时间范围
  startDate     DateTime @map("start_date")
  endDate       DateTime @map("end_date")

  // 生成信息
  generatedBy   String   @map("generated_by")
  generatedAt   DateTime @default(now()) @map("generated_at")

  // 状态
  status        String   @default("completed") // generating, completed, failed
  error         String?  // 错误信息

  // 访问控制
  isPublic      Boolean  @default(false) @map("is_public")
  expiresAt     DateTime? @map("expires_at")

  @@index([templateId])
  @@index([type])
  @@index([generatedBy])
  @@index([generatedAt])
  @@index([startDate, endDate])
  @@map("generated_reports")
}

// ================================
// 组织架构权限控制相关数据模型
// ================================

// 组织表 - 支持多层级组织架构
model Organization {
  id                  String    @id @default(uuid())
  name                String    // 组织名称，如 "engineering"
  displayName         String    @map("display_name") // 显示名称，如 "工程部"
  description         String?

  // 层次结构字段
  parentId            String?   @map("parent_id")
  path                String    @unique // 层次路径，如 "acme.engineering.backend"
  level               Int       @default(0) // 层级深度，根组织为0

  // 组织类型和属性
  type                String    // company, division, department, team, group, project
  status              String    @default("active") // active, inactive, archived
  metadata            Json      @default("{}")

  // 权限配置
  permissionInheritance Boolean @default(true) @map("permission_inheritance")
  dataIsolationLevel  String    @default("inherit") @map("data_isolation_level") // strict, inherit, none

  // 组织设置
  settings            Json      @default("{}")

  // 审计字段
  createdBy           String    @map("created_by")
  createdAt           DateTime  @default(now()) @map("created_at")
  updatedBy           String    @map("updated_by")
  updatedAt           DateTime  @updatedAt @map("updated_at")

  // 关联关系
  parent              Organization? @relation("OrganizationHierarchy", fields: [parentId], references: [id])
  children            Organization[] @relation("OrganizationHierarchy")
  members             OrganizationMember[]
  permissions         OrganizationPermission[]
  delegations         PermissionDelegation[] @relation("OrganizationDelegations")

  // 联邦架构关联
  sourceMappings      OrganizationMapping[] @relation("SourceOrgMapping")
  targetMappings      OrganizationMapping[] @relation("TargetOrgMapping")

  @@index([parentId])
  @@index([path])
  @@index([level])
  @@index([type])
  @@index([status])
  @@index([createdAt])
  @@map("organizations")
}

// 组织成员表 - 用户在组织中的角色和权限
model OrganizationMember {
  id              String    @id @default(uuid())
  userId          String    @map("user_id")
  organizationId  String    @map("organization_id")

  // 角色和权限
  role            String    // admin, manager, member, viewer, custom
  permissions     Json      @default("[]") // 特定权限覆盖

  // 成员状态
  status          String    @default("active") // active, inactive, pending, suspended
  joinedAt        DateTime  @default(now()) @map("joined_at")
  expiresAt       DateTime? @map("expires_at")

  // 成员属性
  isOwner         Boolean   @default(false) @map("is_owner")
  isPrimary       Boolean   @default(false) @map("is_primary") // 是否为主要组织

  // 审计字段
  invitedBy       String?   @map("invited_by")
  createdBy       String    @map("created_by")
  createdAt       DateTime  @default(now()) @map("created_at")
  updatedAt       DateTime  @updatedAt @map("updated_at")

  // 关联关系
  user            User         @relation(fields: [userId], references: [id], onDelete: Cascade)
  organization    Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  @@unique([userId, organizationId])
  @@index([userId])
  @@index([organizationId])
  @@index([role])
  @@index([status])
  @@index([joinedAt])
  @@map("organization_members")
}

// 组织权限表 - 组织级别的权限配置
model OrganizationPermission {
  id                String    @id @default(uuid())
  organizationId    String    @map("organization_id")
  permissionId      String    @map("permission_id")

  // 权限配置
  scope             String    @default("self") // self, children, descendants
  inheritanceRule   String    @default("inherit") @map("inheritance_rule") // inherit, override, block
  conditions        Json      @default("{}") // 权限条件

  // 生效时间
  effectiveFrom     DateTime  @default(now()) @map("effective_from")
  effectiveUntil    DateTime? @map("effective_until")

  // 权限状态
  isActive          Boolean   @default(true) @map("is_active")

  // 审计字段
  grantedBy         String    @map("granted_by")
  grantedAt         DateTime  @default(now()) @map("granted_at")
  revokedBy         String?   @map("revoked_by")
  revokedAt         DateTime? @map("revoked_at")

  // 关联关系
  organization      Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  permission        Permission   @relation(fields: [permissionId], references: [id], onDelete: Cascade)

  @@unique([organizationId, permissionId])
  @@index([organizationId])
  @@index([permissionId])
  @@index([scope])
  @@index([effectiveFrom, effectiveUntil])
  @@index([isActive])
  @@map("organization_permissions")
}

// 跨组织权限申请表 - 权限申请工作流
model PermissionRequest {
  id                String    @id @default(uuid())
  requesterId       String    @map("requester_id")
  targetUserId      String?   @map("target_user_id") // 为其他用户申请权限时使用

  // 申请目标
  targetOrganizationId String @map("target_organization_id")
  requestedPermissions Json   @map("requested_permissions") // 申请的权限列表

  // 申请类型和原因
  requestType       String    @map("request_type") // temporary, project, data_sharing, delegation
  reason            String    // 申请原因
  businessJustification String? @map("business_justification")

  // 申请状态
  status            String    @default("pending") // pending, approved, rejected, expired, revoked
  priority          String    @default("normal") // low, normal, high, urgent

  // 时间配置
  requestedDuration Int?      @map("requested_duration") // 申请时长（小时）
  expiresAt         DateTime? @map("expires_at")

  // 审批信息
  approvedBy        String?   @map("approved_by")
  approvedAt        DateTime? @map("approved_at")
  rejectedBy        String?   @map("rejected_by")
  rejectedAt        DateTime? @map("rejected_at")
  rejectionReason   String?   @map("rejection_reason")

  // 审计字段
  createdAt         DateTime  @default(now()) @map("created_at")
  updatedAt         DateTime  @updatedAt @map("updated_at")

  // 关联关系
  requester         User         @relation("PermissionRequester", fields: [requesterId], references: [id])
  targetUser        User?        @relation("PermissionTarget", fields: [targetUserId], references: [id])
  targetOrganization Organization @relation(fields: [targetOrganizationId], references: [id])
  approver          User?        @relation("PermissionApprover", fields: [approvedBy], references: [id])

  @@index([requesterId])
  @@index([targetUserId])
  @@index([targetOrganizationId])
  @@index([status])
  @@index([requestType])
  @@index([priority])
  @@index([createdAt])
  @@index([expiresAt])
  @@map("permission_requests")
}

// 权限委托表 - 权限委托机制
model PermissionDelegation {
  id              String    @id @default(uuid())
  delegatorId     String    @map("delegator_id")
  delegateeId     String    @map("delegatee_id")
  organizationId  String    @map("organization_id")

  // 委托配置
  delegationType  String    @map("delegation_type") // role, permission, organization
  delegatedRoles  Json      @default("[]") @map("delegated_roles")
  delegatedPermissions Json @default("[]") @map("delegated_permissions")

  // 委托条件
  conditions      Json      @default("{}")
  restrictions    Json      @default("{}")

  // 生效时间
  effectiveFrom   DateTime  @default(now()) @map("effective_from")
  effectiveUntil  DateTime? @map("effective_until")

  // 委托状态
  status          String    @default("active") // active, suspended, revoked, expired
  isRevocable     Boolean   @default(true) @map("is_revocable")

  // 审计字段
  createdAt       DateTime  @default(now()) @map("created_at")
  updatedAt       DateTime  @updatedAt @map("updated_at")
  revokedAt       DateTime? @map("revoked_at")
  revokedBy       String?   @map("revoked_by")

  // 关联关系
  delegator       User         @relation("DelegatorRelation", fields: [delegatorId], references: [id])
  delegatee       User         @relation("DelegateeRelation", fields: [delegateeId], references: [id])
  organization    Organization @relation("OrganizationDelegations", fields: [organizationId], references: [id])

  @@unique([delegatorId, delegateeId, organizationId])
  @@index([delegatorId])
  @@index([delegateeId])
  @@index([organizationId])
  @@index([status])
  @@index([effectiveFrom, effectiveUntil])
  @@map("permission_delegations")
}

// 组织数据访问日志表 - 跨组织数据访问审计
model OrganizationAccessLog {
  id                String    @id @default(uuid())
  userId            String    @map("user_id")
  sourceOrganizationId String @map("source_organization_id") // 用户所属组织
  targetOrganizationId String @map("target_organization_id") // 访问的目标组织

  // 访问信息
  accessType        String    @map("access_type") // read, write, delete, admin
  resourceType      String    @map("resource_type") // 访问的资源类型
  resourceId        String?   @map("resource_id") // 具体资源ID

  // 访问结果
  accessGranted     Boolean   @map("access_granted")
  denialReason      String?   @map("denial_reason")

  // 上下文信息
  ipAddress         String    @map("ip_address")
  userAgent         String?   @map("user_agent")
  requestPath       String?   @map("request_path")

  // 时间信息
  accessedAt        DateTime  @default(now()) @map("accessed_at")

  // 关联关系
  user              User         @relation(fields: [userId], references: [id])
  sourceOrganization Organization @relation("SourceOrgAccess", fields: [sourceOrganizationId], references: [id])
  targetOrganization Organization @relation("TargetOrgAccess", fields: [targetOrganizationId], references: [id])

  @@index([userId])
  @@index([sourceOrganizationId])
  @@index([targetOrganizationId])
  @@index([accessType])
  @@index([accessGranted])
  @@index([accessedAt])
  @@index([userId, accessedAt])
  @@map("organization_access_logs")
}

// ================================
// 联邦式组织架构管理相关数据模型
// ================================

// 组织映射表 - 应用组织到标准组织的映射关系
model OrganizationMapping {
  id                String    @id @default(uuid())
  sourceOrgId       String    @map("source_org_id")
  sourceApplication String    @map("source_application")
  targetOrgId       String    @map("target_org_id")

  // 映射配置
  mappingType       String    @map("mapping_type") // exact, parent, child, equivalent
  confidence        Float     @default(1.0) // 映射置信度 0-1

  // 映射条件和元数据
  conditions        Json      @default("{}")
  metadata          Json      @default("{}")

  // 生效时间
  validFrom         DateTime  @default(now()) @map("valid_from")
  validUntil        DateTime? @map("valid_until")

  // 映射状态
  isActive          Boolean   @default(true) @map("is_active")

  // 审计字段
  createdBy         String    @map("created_by")
  createdAt         DateTime  @default(now()) @map("created_at")
  updatedAt         DateTime  @updatedAt @map("updated_at")

  // 关联关系
  sourceOrganization Organization? @relation("SourceOrgMapping", fields: [sourceOrgId], references: [id], onDelete: Cascade)
  targetOrganization Organization  @relation("TargetOrgMapping", fields: [targetOrgId], references: [id], onDelete: Cascade)
  application       Application   @relation(fields: [sourceApplication], references: [id], onDelete: Cascade)

  @@unique([sourceOrgId, sourceApplication])
  @@index([sourceApplication])
  @@index([targetOrgId])
  @@index([mappingType])
  @@index([confidence])
  @@index([validFrom, validUntil])
  @@index([isActive])
  @@map("organization_mappings")
}

// 应用组织架构注册表 - 管理接入的应用组织架构信息
model ApplicationOrgRegistry {
  id                String    @id @default(uuid())
  applicationId     String    @unique @map("application_id")

  // 组织架构配置
  orgSchemaVersion  String    @map("org_schema_version")
  syncEndpoint      String    @map("sync_endpoint") // 组织数据同步端点
  webhookEndpoint   String?   @map("webhook_endpoint") // 变更通知端点

  // 同步配置
  syncInterval      Int       @default(3600) @map("sync_interval") // 同步间隔(秒)
  autoSync          Boolean   @default(true) @map("auto_sync")
  syncBatchSize     Int       @default(100) @map("sync_batch_size")

  // 同步状态
  lastSyncAt        DateTime  @map("last_sync_at")
  nextSyncAt        DateTime? @map("next_sync_at")
  syncStatus        String    @default("active") @map("sync_status") // active, failed, disabled, pending
  lastSyncError     String?   @map("last_sync_error")

  // 统计信息
  totalOrganizations Int      @default(0) @map("total_organizations")
  mappedOrganizations Int     @default(0) @map("mapped_organizations")
  conflictCount     Int       @default(0) @map("conflict_count")

  // 配置选项
  settings          Json      @default("{}")

  // 审计字段
  registeredBy      String    @map("registered_by")
  registeredAt      DateTime  @default(now()) @map("registered_at")
  updatedAt         DateTime  @updatedAt @map("updated_at")

  // 关联关系
  application       Application @relation(fields: [applicationId], references: [id], onDelete: Cascade)

  @@index([applicationId])
  @@index([syncStatus])
  @@index([lastSyncAt])
  @@index([nextSyncAt])
  @@map("application_org_registries")
}

// 映射冲突记录表 - 记录和管理组织映射冲突
model MappingConflict {
  id                String    @id @default(uuid())
  applicationId     String    @map("application_id")

  // 冲突信息
  conflictType      String    @map("conflict_type") // duplicate_mapping, circular_reference, inconsistent_hierarchy
  sourceOrgId       String    @map("source_org_id")
  conflictingMappings Json    @map("conflicting_mappings") // 冲突的映射关系

  // 解决方案
  suggestedResolution Json    @map("suggested_resolution")
  resolutionStrategy  String? @map("resolution_strategy") // manual_review, auto_merge, create_separate, use_latest
  resolutionConfidence Float? @map("resolution_confidence")

  // 冲突状态
  status            String    @default("pending") @map("status") // pending, resolved, ignored
  resolvedBy        String?   @map("resolved_by")
  resolvedAt        DateTime? @map("resolved_at")
  resolutionNotes   String?   @map("resolution_notes")

  // 审计字段
  detectedAt        DateTime  @default(now()) @map("detected_at")
  updatedAt         DateTime  @updatedAt @map("updated_at")

  // 关联关系
  application       Application @relation(fields: [applicationId], references: [id], onDelete: Cascade)

  @@index([applicationId])
  @@index([conflictType])
  @@index([status])
  @@index([detectedAt])
  @@map("mapping_conflicts")
}

// 联邦同步日志表 - 记录组织架构同步历史
model FederationSyncLog {
  id                String    @id @default(uuid())
  applicationId     String    @map("application_id")

  // 同步信息
  syncType          String    @map("sync_type") // full, incremental, manual
  syncTrigger       String    @map("sync_trigger") // scheduled, webhook, manual, api

  // 同步结果
  status            String    @map("status") // success, failed, partial
  startedAt         DateTime  @default(now()) @map("started_at")
  completedAt       DateTime? @map("completed_at")
  duration          Int?      @map("duration") // 同步耗时(毫秒)

  // 同步统计
  totalRecords      Int       @default(0) @map("total_records")
  processedRecords  Int       @default(0) @map("processed_records")
  successfulRecords Int       @default(0) @map("successful_records")
  failedRecords     Int       @default(0) @map("failed_records")

  // 变更统计
  newMappings       Int       @default(0) @map("new_mappings")
  updatedMappings   Int       @default(0) @map("updated_mappings")
  deletedMappings   Int       @default(0) @map("deleted_mappings")
  conflictsDetected Int       @default(0) @map("conflicts_detected")

  // 错误信息
  errorMessage      String?   @map("error_message")
  errorDetails      Json?     @map("error_details")

  // 同步数据快照
  syncData          Json?     @map("sync_data")

  // 关联关系
  application       Application @relation(fields: [applicationId], references: [id], onDelete: Cascade)

  @@index([applicationId])
  @@index([status])
  @@index([syncType])
  @@index([startedAt])
  @@map("federation_sync_logs")
}

// 联邦权限缓存表 - 缓存联邦权限解析结果
model FederatedPermissionCache {
  id                String    @id @default(uuid())
  userId            String    @map("user_id")
  applicationId     String    @map("application_id")
  organizationContext String? @map("organization_context")

  // 缓存内容
  permissions       Json      @map("permissions") // 权限列表
  organizationMemberships Json @map("organization_memberships") // 组织成员关系
  mappingConfidence Float     @map("mapping_confidence") // 映射置信度

  // 缓存元数据
  resolvedAt        DateTime  @map("resolved_at")
  expiresAt         DateTime  @map("expires_at")
  hitCount          Int       @default(0) @map("hit_count")
  lastAccessedAt    DateTime  @default(now()) @map("last_accessed_at")

  // 关联关系
  user              User        @relation(fields: [userId], references: [id], onDelete: Cascade)
  application       Application @relation(fields: [applicationId], references: [id], onDelete: Cascade)

  @@unique([userId, applicationId, organizationContext])
  @@index([userId])
  @@index([applicationId])
  @@index([expiresAt])
  @@index([lastAccessedAt])
  @@map("federated_permission_cache")
}
