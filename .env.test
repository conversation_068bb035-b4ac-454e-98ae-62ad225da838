# 测试环境配置
NODE_ENV=test

# 数据库配置 (使用内存SQLite数据库)
DATABASE_URL="file:./test.db"

# Redis 缓存配置 (测试数据库)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=2
REDIS_KEY_PREFIX=idp:test:

# JWT 配置
JWT_SECRET=test-jwt-secret-key-for-testing-only
JWT_REFRESH_SECRET=test-refresh-secret-key-for-testing-only
JWT_EXPIRES_IN=15m
JWT_REFRESH_EXPIRES_IN=7d

# 服务器配置
PORT=3001

# 邮件配置 (测试模式)
SMTP_HOST=localhost
SMTP_PORT=1025
SMTP_SECURE=false
SMTP_USER=test
SMTP_PASS=test
FROM_EMAIL=<EMAIL>

# 短信配置 (测试模式)
TWILIO_ACCOUNT_SID=test_account_sid
TWILIO_AUTH_TOKEN=test_auth_token
TWILIO_PHONE_NUMBER=+**********

# OAuth 配置 (测试模式)
GOOGLE_CLIENT_ID=test_google_client_id
GOOGLE_CLIENT_SECRET=test_google_client_secret
GOOGLE_CALLBACK_URL=http://localhost:3001/api/v1/auth/google/callback

GITHUB_CLIENT_ID=test_github_client_id
GITHUB_CLIENT_SECRET=test_github_client_secret
GITHUB_CALLBACK_URL=http://localhost:3001/api/v1/auth/github/callback

WECHAT_APP_ID=test_wechat_app_id
WECHAT_APP_SECRET=test_wechat_app_secret
WECHAT_CALLBACK_URL=http://localhost:3001/api/v1/auth/wechat/callback

WEIBO_CLIENT_ID=test_weibo_client_id
WEIBO_CLIENT_SECRET=test_weibo_client_secret
WEIBO_CALLBACK_URL=http://localhost:3001/api/v1/auth/weibo/callback

# 前端URL配置
FRONTEND_URL=http://localhost:3001

# 安全配置
BCRYPT_ROUNDS=4
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX_REQUESTS=1000

# 零信任模式配置
ZERO_TRUST_ENABLED=false
RISK_THRESHOLD_LOW=30
RISK_THRESHOLD_MEDIUM=60
RISK_THRESHOLD_HIGH=80

# 会话配置
SESSION_TIMEOUT_MINUTES=30
REMEMBER_ME_DAYS=30

# 日志配置
LOG_LEVEL=error
LOG_FILE_PATH=./logs/test.log
